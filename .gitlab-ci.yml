variables:
  GIT_DEPTH: 0
  MAVEN_OPTS: >-
    -Dserver
    -Dhttps.protocols=TLSv1.2
    -Dorg.slf4j.simpleLogger.showDateTime=true
    -Djava.awt.headless=true
    -Xmx8192m

  MAVEN_CLI_OPTS: >-
    --batch-mode
    --errors
    --fail-at-end
    --show-version
    --no-transfer-progress

default:
  interruptible: true
  tags:
    - jdk8
    - sonar
    - k8s
  artifacts:
    expire_in: 3 week
    exclude:
      - /**/*.class
      - /**/*.war
      - /**/lib/*.jar

stages:
#  - i18nScan
  - verify
  - deploy

#i18nScan:
#  stage: i18nScan
#  timeout: 30m
#  script:
#    - 'python3 /scripts/i18n_scan.py $CI_PROJECT_DIR'
#  only:
#    - merge_requests
#    - main
#    - master
#    - release
#    - develop
#    - dev
#    - hotfix
#    - bugfix
#    - /develop\/[-\d.]*/
#    - /dev\/[-\d.]*/
#    - /hotfix\/[-\d.]*/
#    - /bugfix\/[-\d.]*/
#    - /hotfix\/.*/
    
verify:
  stage: verify
  timeout: 3h
  variables:
    RUNNER_SCRIPT_TIMEOUT: 180m
    RUNNER_AFTER_SCRIPT_TIMEOUT: 180m
  only:
    - merge_requests
    - main
    - master
    - release
    - develop
    - dev
    - hotfix
    - bugfix
    - /develop\/[-\d.]*/
    - /dev\/[-\d.]*/
    - /hotfix\/[-\d.]*/
    - /bugfix\/[-\d.]*/
    - /hotfix\/.*/
  before_script:
    - export SONAR_OPTS=$(grep '^sonar.' sonar-project.properties | sed -e 's|^|-D|g' | grep "sonar." | tr '\r\n' ' ')
  script:
    - pwd
    - FORMATTED_VERSION=`date +%Y%m%d`
    - 'mvn $MAVEN_CLI_OPTS clean DskipTests=true verify sonar:sonar -Dmaven.test.failure.ignore=true $SONAR_OPTS -Dsonar.branch.name=${CI_COMMIT_REF_NAME} -Dsonar.projectVersion=${FORMATTED_VERSION}#${CI_BUILD_ID}'
    - echo "start upload jacoco"
    - cp /scripts/jacoco-report-upload.py .
    - echo "$CI_COMMIT_SHA" > commitID.txt
    - echo "$CI_COMMIT_REF_NAME" > gitRef.txt
    - 'python3 jacoco-report-upload.py $CI_REPOSITORY_URL $CI_COMMIT_SHA $CI_COMMIT_REF_NAME'

deploy:
  stage: deploy
  timeout: 10m
  script:
    - pwd
    - 'mvn $MAVEN_CLI_OPTS clean deploy -Dmaven.test.skip=true'
  except:
    variables:
      - $NightlyBuild == "1"
  only:
    variables:
      - $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $SHARECRM_RELEASE == "1"
