package com.facishare.crm.sfa.activity.predefine.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.activity.predefine.ActivityMQUtils;
import com.facishare.crm.sfa.activity.predefine.ActivityPredefineObject;
import com.facishare.crm.sfa.activity.predefine.action.ability.AppendCCRangeAbility;
import com.facishare.crm.sfa.activity.predefine.action.ability.CreationLayoutFinder;
import com.facishare.crm.sfa.activity.predefine.action.ability.WhatListValidator;
import com.facishare.crm.sfa.activity.predefine.enums.FileTypeEnum;
import com.facishare.crm.sfa.activity.predefine.service.ActivityGeneralService;
import com.facishare.crm.sfa.activity.predefine.util.ActivityProcessesContentUtils;
import com.facishare.crm.sfa.activity.service.activerecord.ActiveRecordCCRangeSettingService;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.activity.producer.ActivityRocketProducer;
import com.facishare.crm.sfa.lto.activity.service.ActivityResourceUsageService;
import com.facishare.crm.sfa.lto.utils.LicenseCheckUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.enums.FeedTypes;
import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.Serializer;
import com.facishare.organization.adapter.api.config.model.GetConfigDto;
import com.facishare.organization.adapter.api.config.service.EnterpriseConfigService;
import com.facishare.organization.api.model.department.arg.GetUpperDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.GetUpperDepartmentDtoResult;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.paas.appframework.core.model.ControllerLocateService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.social.bizmanage.AbstractCCRangeSettingService;
import com.facishare.social.exception.ErrorCode;
import com.facishare.social.exception.SocialException;
import com.facishare.social.model.*;
import com.facishare.social.predefine.action.SocialAddAction;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Idempotent(
        serializer = Serializer.Type.json
)
public class ActiveRecordAddAction extends SocialAddAction<SocialAddActionVo.Argument>
        implements WhatListValidator, AppendCCRangeAbility, CreationLayoutFinder {
    protected static EmployeeProviderService employeeProviderService = SpringUtil.getContext()
            .getBean(EmployeeProviderService.class);
    protected static DepartmentProviderService departmentProviderService = SpringUtil.getContext()
            .getBean(DepartmentProviderService.class);
    private static final EnterpriseConfigService enterpriseConfigService = SpringUtil.getContext()
            .getBean(EnterpriseConfigService.class);
    private static final ActiveRecordCCRangeSettingService activeRecordCCRangeSettingService = SpringUtil.getContext()
            .getBean(ActiveRecordCCRangeSettingService.class);
    private final ControllerLocateService controllerLocateService = SpringUtil.getContext()
            .getBean(ControllerLocateService.class);
    private final ActivityRocketProducer activityRocketProducer = SpringUtil.getContext()
            .getBean(ActivityRocketProducer.class);
    private final ActivityGeneralService activityGeneralService = SpringUtil.getContext()
            .getBean(ActivityGeneralService.class);

    /**
     * 业务自定义处理
     * 1、销售记录 关联设置
     * 2、销售记录 抄送范围
     */
    @Override
    protected void socialCustomBefore(SocialAddActionVo.Argument arg) {
        String _source = (String) arg.getObjectData().get("_source");
        if (Objects.equals(_source, "server")) {
            actionContext.setAttribute("not_validate", true);
        }
        final User user = actionContext.getUser();
        if (!Objects.isNull(user.getUpstreamOwnerIdOrUserId()) && !Objects.equals(user.getUpstreamOwnerIdOrUserId(), User.SUPPER_ADMIN_USER_ID)) {
            //员工详情
            GetEmployeeDtoResult getEmployeeDtoResult = getEmployeeDetail(Integer.valueOf(user.getUpstreamOwnerIdOrUserId()), user.getTenantIdInt());
            stopWatch.lap("activeRecordSet-getEmployeeDetail");
            Integer mainDepartmentId = getEmployeeDtoResult.getEmployeeDto()
                    .getMainDepartmentId();
            if (mainDepartmentId == null) {
                mainDepartmentId = 999999;
                getEmployeeDtoResult.getEmployeeDto()
                        .setMainDepartmentIds(Collections.singletonList(mainDepartmentId));
            }
            final GetUpperDepartmentDtoResult allDepartment = getAllDepartment(user, mainDepartmentId);

            handleActiveRecordRelateSetting(arg, user, getEmployeeDtoResult, allDepartment);

            appendCCRange(arg, user, socialPublishCollector, getEmployeeDtoResult, allDepartment);
            stopWatch.lap("activeRecordSet");
        }
    }

    /**
     * 销售记录设置 - 销售记录关联设置
     */
    @SuppressWarnings("deprecation")
    protected void handleActiveRecordRelateSetting(SocialAddActionVo.Argument arg, User user, GetEmployeeDtoResult getEmployeeDtoResult, GetUpperDepartmentDtoResult allDepartment) {
        stopWatch.lap("activeRecordSet-getAllDepartment");
        arg.getPaasObjects()
                .addAll(applyRelatedPAASObjectSetting(arg, user, getEmployeeDtoResult, allDepartment));

        stopWatch.lap("activeRecordSet-applyRelatedPAASObjectSetting");

        //过滤掉作废的对象 暂时不提交
        final Map<String, List<PaasObject>> paasObjects = arg.getPaasObjects()
                .stream()
                .collect(Collectors.groupingBy(PaasObject::getApiName));

        paasObjects.forEach((key, value) -> {
            socialPublishCollector.appendCrmObjectAndCrmObjectOwner(key, value.stream()
                    .map(PaasObject::getObjectId)
                    .collect(Collectors.toList()));
            stopWatch.lap("activeRecordSet-appendCrmObjectAndCrmObjectOwner-" + key);
        });

    }


    protected GetUpperDepartmentDtoResult getAllDepartment(final User user, final Integer mainDepartmentId) {
        GetUpperDepartmentDtoArg getAllParentDepartmentArg = new GetUpperDepartmentDtoArg();
        getAllParentDepartmentArg.setDepartmentId(mainDepartmentId);
        getAllParentDepartmentArg.setSelf(true);
        getAllParentDepartmentArg.setEnterpriseId(user.getTenantIdInt());
        return departmentProviderService.getUpperDepartmentDto(getAllParentDepartmentArg);
    }

    public GetEmployeeDtoResult getEmployeeDetail(Integer userId, Integer tenantId) {
        GetEmployeeDtoArg arg = new GetEmployeeDtoArg();
        arg.setEmployeeId(userId);
        arg.setEnterpriseId(tenantId);
        return employeeProviderService.getEmployeeDto(arg);
    }

    @SuppressWarnings("deprecation")
    protected List<PaasObject> applyRelatedPAASObjectSetting(
            final SocialAddActionVo.Argument arg,
            final User user,
            final GetEmployeeDtoResult getEmployeeDtoResult,
            final GetUpperDepartmentDtoResult getAllParentDepartmentResult) {
        //销售记录多关联
        //1.获取后台的多关联设置
        Map<String, List<RelatedObjectData>> relatedObjectConfigMap = convertToRelatedObjectConfigMap(user, getEmployeeDtoResult, getAllParentDepartmentResult);
        //2.遍历用户传入的关联业务数据，找到它们的关联数据
        return getRelatedObjectDataList(arg.getPaasObjects(), relatedObjectConfigMap);
    }

    protected List<PaasObject> getRelatedObjectDataList(
            List<PaasObject> paasObjects,
            Map<String, List<RelatedObjectData>> relatedObjectConfigMap
    ) {
        List<PaasObject> relatedPaasObjectList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(relatedObjectConfigMap)) {
            return relatedPaasObjectList;
        }
        final Map<String, List<PaasObject>> paasObjectMap =
                paasObjects.stream().collect(Collectors.groupingBy(
                        PaasObject::getApiName
                ));
        for (Map.Entry<String, List<PaasObject>> paasObjectEntry : paasObjectMap.entrySet()) {
            String apiName = paasObjectEntry.getKey();
            List<PaasObject> paasObjectList = paasObjectEntry.getValue();
            if (!CollectionUtils.isEmpty(paasObjectList)) {
                List<RelatedObjectData> relatedObjectDataList = relatedObjectConfigMap.get(apiName);
                if (!CollectionUtils.isEmpty(relatedObjectDataList)) {
                    final List<IObjectData> objectDataList = paasObjectList.stream()
                            .map(it -> socialPublishCollector.getObjectDataByApiNameAndDataId(apiName, it.getObjectId()))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    for (IObjectData objectData : objectDataList) {
                        for (RelatedObjectData relatedObjectData : relatedObjectDataList) {
                            String relatedApiName = relatedObjectData.getApiName();
                            String fieldName = relatedObjectData.getFieldName();
                            String relatedDataId = objectData.get(fieldName, String.class);
                            if (!StringUtils.isEmpty(relatedDataId)) {
                                PaasObject relatedPaasObject = new PaasObject();
                                relatedPaasObject.setApiName(relatedApiName);
                                relatedPaasObject.setObjectId(relatedDataId);
                                relatedPaasObjectList.add(relatedPaasObject);
                            }
                        }
                    }
                }
            }
        }
        return relatedPaasObjectList;
    }

    protected Map<String, List<RelatedObjectData>> convertToRelatedObjectConfigMap(
            final User user,
            final GetEmployeeDtoResult getEmployeeDtoResult,
            final GetUpperDepartmentDtoResult getAllParentDepartmentResult
    ) {
        try {
            Map<String, List<RelatedObjectData>> map = Maps.newHashMap();

            String relatedObjectConfig = getEnterpriseConfigByConfigKey(user);
            if (StringUtils.isEmpty(relatedObjectConfig)) {
                return map;
            }
            final List<MultiRelatedObjectsRule> multiRelatedObjectsRules = JSONArray.parseArray(relatedObjectConfig, MultiRelatedObjectsRule.class);
            for (MultiRelatedObjectsRule multiRelatedObjectsRule : multiRelatedObjectsRules) {
                if (multiRelatedObjectsRule.accept(getEmployeeDtoResult, getAllParentDepartmentResult)) {
                    final List<MultiRelatedObjectsConfig> relations = multiRelatedObjectsRule.getRelations();
                    computeRelations(map, relations);
                }
            }
            return map;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new SocialException(ErrorCode.MULTI_RELATED_OBJECT_CONFIG_PARSE_ERROR);
        }
    }

    protected void computeRelations(final Map<String, List<RelatedObjectData>> map, final List<MultiRelatedObjectsConfig> relations) {
        if (relations == null) {
            return;
        }
        for (final MultiRelatedObjectsConfig relation : relations) {
            if (relation.getRelatedObject() != null) {
                List<RelatedObjectData> relatedObjectDataList =
                        relation.getRelatedObject().stream().map(it ->
                                JSONObject.parseObject(it, RelatedObjectData.class)
                        ).collect(Collectors.toList());
                map.compute(relation.getOriginObjectApiName(), (apiName, value) -> {
                    if (value == null) {
                        return relatedObjectDataList;
                    } else {
                        value.addAll(relatedObjectDataList);
                        return value;
                    }
                });
            }
        }
    }

    protected String getEnterpriseConfigByConfigKey(User user) {
        GetConfigDto.Argument argument = new GetConfigDto.Argument();
        argument.setKey("ActiveRecordObjectRelationSettingsV2");
        final Integer userIdInt = Integer.valueOf(user.getUpstreamOwnerIdOrUserId());
        argument.setEmployeeId(userIdInt);
        argument.setCurrentEmployeeId(userIdInt);
        argument.setEnterpriseId(user.getTenantIdInt());
        GetConfigDto.Result result = enterpriseConfigService.getConfig(argument);
        return result.getValue();
    }

    @Override
    protected void socialCustomModifyObjectDataBeforeCreate(IObjectData objectData, IObjectDescribe describe) {
        checkRelatedObjectNotEmpty(objectData);
        fillPresetField(objectData);
        addRenderTypesField(objectData);
        //addProcessesToContent(objectData);
    }

    static void fillPresetField(IObjectData objectData) {
        @SuppressWarnings("unchecked")
        Map<String, List<String>> relatedObject = (Map<String, List<String>>) objectData.get("related_object");
        fillPresetField(objectData, relatedObject, Utils.NEW_OPPORTUNITY_API_NAME, "new_opportunity_id");
        fillPresetField(objectData, relatedObject, Utils.ACCOUNT_API_NAME, "account_id");
        fillPresetField(objectData, relatedObject, Utils.LEADS_API_NAME, "leads_id");
    }

    private static void fillPresetField(IObjectData validData, Map<String, List<String>> relatedObject, String apiName, String fieldName) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(validData.get(fieldName, String.class))) {
            Optional.ofNullable(relatedObject.get(apiName)).filter(l -> !l.isEmpty()).map(l -> l.get(0)).ifPresent(
                    id -> validData.set(fieldName, id)
            );
        }
    }

    private void addRenderTypesField(IObjectData objectData) {
        Object interactionRecordsObj = objectData.get("interaction_records");
        if (interactionRecordsObj == null) {
            return;
        }

        Object interactiveRenderTypesObj = objectData.get("interactive_render_types");
        if (interactiveRenderTypesObj != null) {
            return;
        }

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> interactionRecords = (List<Map<String, Object>>) interactionRecordsObj;
        Set<String> renderTypes = interactionRecords.stream()
                .map(record -> FileTypeEnum.getComponentTypeByExtension((String) record.get("ext")))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        objectData.set("interactive_render_types", Lists.newArrayList(renderTypes));

        if (ActivityMQUtils.hasNoRecordingUsage(objectData, serviceFacade.getBean(ActivityResourceUsageService.class), serviceFacade.getBean(LicenseClient.class))) {
            objectData.set("interactive_processes", "501");
        }
    }

    private static void addProcessesToContent(IObjectData objectData) {
        String process = objectData.get("interactive_processes", String.class);
        if (!"1".equals(process)) {
            return;
        }
        ActivityProcessesContentUtils.setMeetingProcessingContent(objectData);
    }

    @Override
    protected FeedTypes requiredFeedType() {
        return FeedTypes.EVENT;
    }

    /**
     * 销售记录发布屏蔽审批流
     */
    @Override
    protected boolean needTriggerApprovalFlow() {
        if (arg.skipApprovalFlow()) {
            return false;
        }
        if (ActivityMQUtils.isAudioRecording(objectData)) {
            return false;
        }
        return GrayUtil.allowActiveRecordObjectification(actionContext.getTenantId());
    }

    @Override
    public AbstractCCRangeSettingService requiredRangeSettingService() {
        return activeRecordCCRangeSettingService;
    }

    @Override
    protected Result after(final SocialAddActionVo.Argument arg, final Result result) {
        stopWatch.lap("before findLayoutByCreator");
        findLayoutByCreator(
                objectData,
                controllerLocateService,
                socialPublishCollector::setAddLayout,
                socialPublishCollector::setOnlyRichTextAbstract,
                socialPublishCollector::getLayoutContainedFieldNames,
                ActivityPredefineObject.ActiveRecord.getApiName()
        );
        stopWatch.lap("after findLayoutByCreator");
        sendMQ();
        activityGeneralService.initAllState(actionContext.getTenantId(), objectData.getId());
        return super.after(arg, result);
    }

    private void sendMQ() {
        String stage = actionContext.getActionCode();
        if (objectData == null){
            return;
        }
        // 没有互动语料 并且 达到了不发消息的条件
        if (!ActivityMQUtils.isReachLimit(objectData)) {
            log.warn("content is too short, not send message");
            return;
        }

        if (!ActivityMQUtils.haveInteractiveRecords(objectData)) {
            stage = "AddNoAttachment";
        }

        // 非 call 类型 且 AI 存在 才发送消息
        // call 类型 直接发送消息
        if (!"call".equals(objectData.get("interactive_types", String.class))
            && !LicenseCheckUtil.checkAIExist(actionContext.getTenantId())){
            return;
        }

        if ("501".equals(objectData.get("interactive_processes", String.class))) {
            log.warn("no recording usage, not send message");
            return;
        }

        ActivityMessage activityMessage = ActivityMessage.builder()
                .tenantId(actionContext.getUser().getTenantId())
                .objectId(objectData.getId())
                .sourceId(objectData.get("source_id", String.class))
                .sourceApiName(objectData.get("source_api_name", String.class))
                .objectApiName(objectData.getDescribeApiName())
                .actionCode(actionContext.getActionCode())
                .stage(stage)
                .opId(actionContext.getUser().getUpstreamOwnerIdOrUserId())
                .interactiveTypes(objectData.get("interactive_types", String.class))
                .language(actionContext.getLang() != null ? actionContext.getLang().getValue() : "zh_CN")
                .build();
        activityRocketProducer.sendActivityToTextMessage(activityMessage);
    }
}
