package com.facishare.crm.sfa.activity.predefine.action;

import com.facishare.feeds.adapter.api.services.FeedAdapterService;
import com.facishare.organization.adapter.api.config.service.EnterpriseConfigService;
import com.facishare.paas.appframework.core.model.ControllerLocateService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.social.ability.FeedUpdater;
import com.facishare.crm.sfa.activity.predefine.action.ability.CreationLayoutFinder;
import com.facishare.crm.sfa.activity.predefine.action.ability.WhatListRemoveAbility;
import com.facishare.social.model.NotifyRangeVo;
import com.facishare.social.model.SocialUpdateCollector;
import com.facishare.social.predefine.action.SocialIncrementUpdater;
import com.facishare.social.predefine.processor.FeedFieldProcessorProvider;
import com.github.autoconf.ConfigFactory;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@EqualsAndHashCode(callSuper = true)
public class ActiveRecordIncrementUpdateAction extends StandardIncrementUpdateAction
        implements FeedUpdater, SocialIncrementUpdater, WhatListRemoveAbility, CreationLayoutFinder {
    protected static List<String> editAllowPeerNames = new ArrayList<>();

    private final ControllerLocateService controllerLocateService = SpringUtil.getContext()
            .getBean(ControllerLocateService.class);

    protected FeedFieldProcessorProvider feedFieldProcessorProvider = SpringUtil.getContext()
            .getBean(FeedFieldProcessorProvider.class);

    protected FeedAdapterService feedAdapterService = SpringUtil.getContext()
            .getBean(FeedAdapterService.class);
    private static final EnterpriseConfigService enterpriseConfigService = SpringUtil.getContext()
            .getBean(EnterpriseConfigService.class);

    protected SocialUpdateCollector collector;

    private NotifyRangeVo ccRange = null;

    static {
        ConfigFactory.getConfig("variables_plat_global", config -> {
            final String journalEditAllowPeerNames = config.get("ActiveRecordEditAllowPeerNames");
            if (!StringUtils.isBlank(journalEditAllowPeerNames)) {
                final String[] split = journalEditAllowPeerNames.split(",");
                editAllowPeerNames = Arrays.asList(split);
            }
        });
    }

    @Override
    protected void doDataPrivilegeCheck() {
        Runnable checker = super::doDataPrivilegeCheck;
        doDataPrivilegeCheck4Social(checker);
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        socialBeforeIncrement(objectData, dbObjectData, objectDescribe);
        IObjectData updateData = ObjectDataExt.of(this.objectData).copy();
        Map<String, Object> updatedFieldMap = ObjectDataExt.of(updateData)
                .toMap();

        final String related_object = "related_object";
        final String related_object_data = "related_object_data";

        if (updatedFieldMap.containsKey(related_object) || updatedFieldMap.containsKey(related_object_data)) {
            final Map<String, List<String>> newRelatedObject = getRelatedObject(objectData, related_object, related_object_data);
            checkWhatlistDeletion(actionContext, newRelatedObject, () -> getRelatedObject(dbObjectData, related_object, related_object_data));
        }
    }

    @Override
    protected Result after(final Arg arg, final Result result) {
        final Result after = super.after(arg, result);
        socialAfterIncrement(objectData, dbObjectData, objectDescribe);
        stopWatch.lap("before findLayoutByCreator");
        findLayoutByCreator(
                objectData,
                controllerLocateService,
                collector::setAddLayout,
                collector::setOnlyRichTextAbstract,
                collector::getLayoutContainedFieldNames,
                "ActiveRecordObj"
        );
        stopWatch.lap("after findLayoutByCreator");

        return after;
    }

    @Override
    public ServiceFacade getServiceFacade() {
        return serviceFacade;
    }

    @Override
    public List<String> getAllowPeerNames() {
        return editAllowPeerNames;
    }

    @Override
    public void setSocialUpdateCollector(final SocialUpdateCollector socialUpdateCollector) {
        this.collector = socialUpdateCollector;
    }

    @Override
    public void setCcRange(final NotifyRangeVo parseObject) {
        ccRange = parseObject;
    }

    @Override
    public FeedAdapterService getFeedAdapterService() {
        return feedAdapterService;
    }

    @Override
    public NotifyRangeVo getSavedCcRange() {
        return ccRange;
    }

    @Override
    public SocialUpdateCollector getSocialUpdateCollector() {
        return collector;
    }

    @Override
    public FeedFieldProcessorProvider getFieldProcessorProvider() {
        return feedFieldProcessorProvider;
    }

    @Override
    public EnterpriseConfigService getEnterpriseConfigService() {
        return enterpriseConfigService;
    }
}