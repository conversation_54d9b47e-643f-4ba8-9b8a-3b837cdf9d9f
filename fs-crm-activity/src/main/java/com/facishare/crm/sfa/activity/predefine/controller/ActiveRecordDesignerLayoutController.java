package com.facishare.crm.sfa.activity.predefine.controller;

import com.facishare.crm.sfa.lto.utils.LicenseCheckUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardDesignerLayoutController;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.social.ability.layout.XTLayoutFormComponentMaintainer;
import com.google.common.collect.Sets;

public class ActiveRecordDesignerLayoutController extends StandardDesignerLayoutController implements XTLayoutFormComponentMaintainer {
    @Override
    protected void processLayout(ILayout layout) {
        boolean allowActiveRecordObjectification = GrayUtil.allowActiveRecordObjectification(controllerContext.getTenantId());
        if (!allowActiveRecordObjectification && !LicenseCheckUtil.checkAIExist(controllerContext.getTenantId())) {
            removeAllComponentsExcept(layout, Sets.newHashSet(ComponentExt.FORM_COMPONENT, ComponentExt.HEAD_INFO_COMPONENT_NAME));
        }
    }
}