package com.facishare.crm.sfa.activity.predefine.controller;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.sfa.activity.predefine.ActivityMQUtils;
import com.facishare.crm.sfa.activity.predefine.ActivityPredefineObject;
import com.facishare.crm.sfa.activity.predefine.enums.FileTypeEnum;
import com.facishare.feeds.adapter.api.services.FeedAdapterService;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.impl.ui.layout.TabSection;
import com.facishare.paas.metadata.impl.ui.layout.component.TabsComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.social.ability.FeedRangeCompletable;
import com.facishare.social.feeds.converter.facade.FeedFieldConverterFacade;
import com.facishare.social.predefine.controller.abilities.FeedPermissionChecker;
import com.facishare.social.service.FeedResource;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class ActiveRecordWebDetailController extends StandardWebDetailController implements FeedRangeCompletable, FeedPermissionChecker {
    private static final Logger logger = LoggerFactory.getLogger(ActiveRecordWebDetailController.class);
    protected FeedAdapterService feedAdapterService = SpringUtil.getContext()
            .getBean(FeedAdapterService.class);
    protected FeedResource feedResource = SpringUtil.getContext()
            .getBean(FeedResource.class);
    protected EIEAConverter eieaConverter = SpringUtil.getContext()
            .getBean(EIEAConverter.class);
    Boolean hasFeedPermission = null;

    @Override
    protected void doFunPrivilegeCheck() {
        if (hasFeedPermission()) {
            return;
        }

        super.doFunPrivilegeCheck();
    }

    @Override
    protected boolean skipDataPrivilegeCheck() {
        if (hasFeedPermission()) {
            return true;
        } else {
            return super.skipDataPrivilegeCheck();
        }
    }

    private boolean hasFeedPermission() {
        if (hasFeedPermission != null) {
            return hasFeedPermission;
        }
        hasFeedPermission = hasFeedPermission(
                controllerContext.getUser(),
                arg.getObjectDescribeApiName(),
                arg.getObjectDataId()
        );

        return hasFeedPermission;
    }

    @Override
    protected Result after(final Arg arg, final Result result) {
        super.after(arg, result);
        ObjectDataDocument activityData = result.getData();
        List<String> componentTypes = Lists.newArrayList();
        Object interactionRecordsObj = activityData.get("interaction_records");
        Object interactiveContentObj = activityData.get("interactive_content");
        if (interactionRecordsObj != null) {
            try {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> interactionRecords = (List<Map<String, Object>>) interactionRecordsObj;
                for (Map<String, Object> interactionRecord : interactionRecords) {
                    String extension = (String) interactionRecord.get("ext");
                    String componentType = FileTypeEnum.getComponentTypeByExtension(extension);
                    if (extension != null) {
                        componentTypes.add(componentType);
                    } else if (interactiveContentObj != null) {
                        componentTypes.add("corpus_audio");
                        logger.debug("Extension is null add default component type corpus_audio {}", data.getId());
                    }
                }
            } catch (ClassCastException e) {
                logger.error("Failed to process interaction records: {}", e.getMessage(), e);
            }
        } else if (interactiveContentObj != null) {
            componentTypes.add("corpus_audio");
            logger.debug("No interaction records found in activity data");
        }
        activityData.put("attachment_ext", componentTypes);
        logger.debug("Added attachment extensions: {}", componentTypes);
        // 如何需要一个附件类型
        SpringUtil.getContext().getBean(FeedFieldConverterFacade.class)
                .handleEditGet(describe, result.getData(), getControllerContext());
        completeFeedRange(controllerContext.getUser(), ActivityPredefineObject.ActiveRecord.getApiName() + "|" + data.getId(), data);
        if (ActivityMQUtils.isAudioRecording(activityData)) {
            ArrayList<String> hideList = Lists.newArrayList("form_component", "activity_meeting_summary", "sfa_activity_emotion_analysis", "sfa_activity_new_business_info", "sfa_activity_attendees_insight", "tab_RequirementObj_active_record_id_related_list");
            removeComponents(result, hideList);
        }
        if (AppIdMapping.isPRM(controllerContext.getAppId())) { // 代理通下游企业隐藏待办组件
            removeComponents(result, Collections.singletonList("sfa_activity_todo"));
        }
        removeComponents(result, Collections.singletonList("sfa_activity_emotion_analysis")); // 960情感分析下线
        if (data.get("account_id") == null && data.get("new_opportunity_id") == null) { // 建议话题不在非关联对象下发
            removeComponents(result, Collections.singletonList("sfa_activity_suggest_issues"));
        }
        return result;
    }

    private static void removeComponents(Result result, List<String> list) {
        LayoutExt layoutExt = LayoutExt.of(result.getLayout());
        layoutExt.removeComponents(list);
        List<IComponent> tabsComponents = layoutExt.getComponentByTypes(Collections.singletonList(IComponent.TYPE_TABS));
        for (IComponent component : tabsComponents) {
            removeComponentsFromTab((TabsComponent) component, list);
        }
        List<String> hiddenComponents = layoutExt.getHiddenComponents();
        if (hiddenComponents == null) {
            hiddenComponents = list;
        } else {
            hiddenComponents.addAll(list);
        }
        layoutExt.setHiddenComponents(hiddenComponents);
    }

    private static void removeComponentsFromTab(TabsComponent tab, Collection<String> param) {
        Optional.ofNullable(tab.getTabs()).ifPresent(sections -> {
            Iterator<TabSection> iterator = sections.iterator();
            while (iterator.hasNext()) {
                TabSection section = iterator.next();
                for (String p : param) {
                    if (section.getApiName().startsWith(p)) {
                        iterator.remove();
                        break;
                    }
                }
            }
            tab.setTabs(sections);
        });
        Optional.ofNullable(tab.getComponents()).ifPresent(components ->
                components.removeIf(l -> l != null && !l.isEmpty() && param.contains(l.get(0))));
    }

    @Override
    public EIEAConverter getEieaConverter() {
        return eieaConverter;
    }

    @Override
    public FeedResource getFeedResource() {
        return feedResource;
    }

    @Override
    public FeedAdapterService getFeedAdapterService() {
        return feedAdapterService;
    }
}
