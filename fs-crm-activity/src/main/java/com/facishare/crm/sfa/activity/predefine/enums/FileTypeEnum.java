package com.facishare.crm.sfa.activity.predefine.enums;

import lombok.Getter;

@Getter
public enum FileTypeEnum {
    // 文档类型
    DOC("doc", "corpus_text"),
    DOCX("docx", "corpus_text"),
    PPT("ppt", "corpus_text"),
    PPTX("pptx", "corpus_text"),
    PDF("pdf", "corpus_text"),
    
    // 音频类型
    WAV("wav", "corpus_audio"),
    MP3("mp3", "corpus_audio"),
    M4A("m4a", "corpus_audio"),
    WMA("wma", "corpus_audio"),
    AMR("amr", "corpus_audio"),
    AAC("aac", "corpus_audio"),
    OGG_OPUS("ogg-opus", "corpus_audio"),
    FLAC("flac", "corpus_audio"),
    FLV("flv", "corpus_audio"),
    MP4("mp4", "corpus_audio"),
    GP3("3gp", "corpus_audio"),

    // 视频类型
    MEETING("meeting", "corpus_meeting");

    private final String extension;
    private final String componentType;

    FileTypeEnum(String extension, String componentType) {
        this.extension = extension;
        this.componentType = componentType;
    }

    public static String getComponentTypeByExtension(String extension) {
        if (extension == null) {
            return null;
        }
        for (FileTypeEnum fileType : FileTypeEnum.values()) {
            if (fileType.getExtension().equalsIgnoreCase(extension)) {
                return fileType.getComponentType();
            }
        }
        return null;
    }
} 