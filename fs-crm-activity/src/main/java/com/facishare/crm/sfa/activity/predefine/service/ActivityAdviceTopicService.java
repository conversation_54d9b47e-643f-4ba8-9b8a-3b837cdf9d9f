package com.facishare.crm.sfa.activity.predefine.service;


import com.alibaba.fastjson2.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.activity.predefine.service.model.ActivityAdviceTopicModel;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.util.ActivityQuestionUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.SelectOneExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.common.data.Where;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@ServiceModule("advice_topic")
@AllArgsConstructor
public class ActivityAdviceTopicService {
    private static final String ACTIVITY_QUESTION_OBJ = "ActivityQuestionObj";
    private static final String ADVICE_STATUS = "advice_status";
    private static final String LIBRARY_ID = "library_id";
    private ServiceFacade serviceFacade;

    @ServiceMethod("statistic")
    public ActivityAdviceTopicModel.Result statistic(ServiceContext context, ActivityAdviceTopicModel.Arg arg) {
        User user = context.getUser();
        String tenantId = context.getTenantId();
        Optional<SearchTemplateQuery> queryOp = getQuery(user, arg);
        List<IObjectData> dataList;
        if (queryOp.isPresent()) {
            SearchTemplateQuery query = queryOp.get();
            ActivityQuestionUtil.addFilterByAplFunction(user, serviceFacade, arg.getActiveRecordId(), query);
            dataList = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, ACTIVITY_QUESTION_OBJ, query, Lists.newArrayList(ADVICE_STATUS, DBRecord.ID, LIBRARY_ID, "account_id", "new_opportunity_id")).getData();
        } else {
            dataList = Collections.emptyList();
        }
        Map<String, Long> countMap = dataList.stream()
                .collect(Collectors.groupingBy(
                        map -> (String) map.get(ADVICE_STATUS),
                        Collectors.counting()
                ));
        IObjectDescribe describe = serviceFacade.findObject(tenantId, ACTIVITY_QUESTION_OBJ);
        SelectOneExt selectOneExt = SelectOneExt.of((SelectOne) describe.getFieldDescribe(ADVICE_STATUS));
        String[] statusArray = new String[]{"clearly_defined", "unclearly_defined", "not_asked"};
        Map<String, JSONObject> statistic = new HashMap<>();
        long totalCount = 0;
        for (String status : statusArray) {
            String label = selectOneExt.getLabelByValue(status);
            long count = 0L;
            if (countMap.containsKey(status)) {
                count = countMap.get(status);
            }
            JSONObject obj = new JSONObject();
            totalCount += count;
            obj.put("count", count);
            obj.put("label", label);
            statistic.put(status, obj);
        }
        JSONObject total = new JSONObject();
        total.put("count", totalCount);
        total.put("label", I18N.text("sfa.all"));
        statistic.put("total", total);
        ActivityAdviceTopicModel.Result result = new ActivityAdviceTopicModel.Result();
        List<String> libraryIds = dataList.stream().map(d -> d.get(LIBRARY_ID)).filter(Objects::nonNull).map(String.class::cast).collect(Collectors.toList());
        List<IObjectData> librarydataList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), libraryIds, "SalesTopicLibraryObj");
        List<String> tags = librarydataList.stream().filter(x -> ObjectUtils.isNotEmpty(x.get("tags")))
                .map(x -> x.get("tags__r", String.class, x.get("tags", String.class, ""))).collect(Collectors.toList());
        result.setTagsList(tags);
        result.setStatistic(statistic);
        return result;
    }

    @ServiceMethod("objectsList")
    public ActivityAdviceTopicModel.Result objectsList(ServiceContext context, ActivityAdviceTopicModel.Arg arg) {
        String tenantId = context.getTenantId();
        User user = context.getUser();
        ActivityAdviceTopicModel.Result result = new ActivityAdviceTopicModel.Result();
        List<Map<String, String>> objectsList = new ArrayList<>();
        List<String> objectApiNames = new ArrayList<>();
        if (Utils.ACTIVE_RECORD_API_NAME.equals(arg.getSourceApiName())) {
            if (arg.getAccountId() != null) {
                List<IFilter> filters = baseFilter(user);
                SearchUtil.fillFilterEq(filters, "account_id", arg.getAccountId());
                SearchTemplateQuery query = new SearchTemplateQuery();
                query.setLimit(1);
                query.setFilters(filters);
                if (!serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, ACTIVITY_QUESTION_OBJ, query, Lists.newArrayList(DBRecord.ID)).getData().isEmpty()) {
                    objectApiNames.add(SFAPreDefine.Account.getApiName());
                }
            }
            if (arg.getOpportunityId() != null) {
                List<IFilter> filters = baseFilter(user);
                SearchUtil.fillFilterEq(filters, "new_opportunity_id", arg.getOpportunityId());
                SearchTemplateQuery query = new SearchTemplateQuery();
                query.setLimit(1);
                query.setFilters(filters);
                if (!serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, ACTIVITY_QUESTION_OBJ, query, Lists.newArrayList(DBRecord.ID)).getData().isEmpty()) {
                    objectApiNames.add(SFAPreDefine.NewOpportunity.getApiName());
                }
            }
        } else {
            objectApiNames.add(arg.getSourceApiName());
        }
        for (String objectApiName : objectApiNames) {
            Map<String, String> map = new HashMap<>();
            map.put("api_name", objectApiName);
            map.put("display_name__r", serviceFacade.findObject(tenantId, objectApiName).getDisplayName());
            objectsList.add(map);
        }
        result.setObjectsList(objectsList);
        return result;
    }

    @ServiceMethod("update")
    public ActivityAdviceTopicModel.Result update(ServiceContext context, ActivityAdviceTopicModel.Arg arg) {
        ActivityAdviceTopicModel.Result result = new ActivityAdviceTopicModel.Result();
        String questionId = arg.getQuestionId();
        if (questionId == null || arg.getData() == null || arg.getData().getAnswer() == null) {
            return result;
        }
        List<IObjectData> list = serviceFacade.findObjectDataByIds(context.getTenantId(), Collections.singletonList(questionId), ACTIVITY_QUESTION_OBJ);
        if (list.isEmpty()) {
            return result;
        }
        IObjectData data = list.get(0);
        data.set("answer_summary", arg.getData().getAnswer());
        serviceFacade.updateObjectData(context.getUser(), data);
        return result;
    }

    private static Optional<SearchTemplateQuery> getQuery(User user, ActivityAdviceTopicModel.Arg arg) {
        List<IFilter> filters = baseFilter(user);
        String sourceApiName = arg.getSourceApiName();
        SearchTemplateQuery query = new SearchTemplateQuery();
        if (sourceApiName == null) {
            SearchUtil.fillFilterEq(filters, "account_id", arg.getAccountId() == null ? arg.getId() : arg.getAccountId());
        } else if (SFAPreDefine.Account.getApiName().equals(sourceApiName)) {
            SearchUtil.fillFilterEq(filters, "account_id", arg.getAccountId());
        } else if (SFAPreDefine.NewOpportunity.getApiName().equals(sourceApiName)) {
            SearchUtil.fillFilterEq(filters, "new_opportunity_id", arg.getOpportunityId());
        } else {
            List<Wheres> wheresList = new ArrayList<>();
            if (arg.getAccountId() != null) {
                List<IFilter> orFilters1 = new ArrayList<>();
                SearchUtil.fillFilterEq(orFilters1, "account_id", arg.getAccountId());
                Wheres orWheres1 = new Wheres();
                orWheres1.setConnector(Where.CONN.AND.toString());
                orWheres1.setFilters(Lists.newArrayList(orFilters1));
                wheresList.add(orWheres1);
            }
            if (arg.getOpportunityId() != null) {
                List<IFilter> orFilter2 = new ArrayList<>();
                SearchUtil.fillFilterEq(orFilter2, "new_opportunity_id", arg.getOpportunityId());
                Wheres orWheres2 = new Wheres();
                orWheres2.setConnector(Where.CONN.AND.toString());
                orWheres2.setFilters(orFilter2);
                wheresList.add(orWheres2);
            }
            if (!wheresList.isEmpty()) {
                query.setWheres(wheresList);
            } else {
                return Optional.empty();
            }
        }
        query.setFilters(filters);
        query.setLimit(2000);
        query.setOffset(0);
        query.setFindExplicitTotalNum(false);
        query.setNeedReturnCountNum(false);
        query.setPermissionType(0);
        return Optional.of(query);
    }

    private static List<IFilter> baseFilter(User user) {
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, user.getTenantId());
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, DELETE_STATUS.NORMAL.getValue());
        SearchUtil.fillFilterEq(filters, "question_type", "2");
        SearchUtil.fillFilterEq(filters, ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectLifeStatus.NORMAL.getCode());
        SearchUtil.fillFilterIsNull(filters, "history_flag");
        return filters;
    }
}
