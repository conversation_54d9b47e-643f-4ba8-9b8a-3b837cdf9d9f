package com.facishare.crm.sfa.activity.predefine.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.api.client.TokenCountingResourceClient;
import com.facishare.ai.api.dto.BaseArgument;
import com.facishare.ai.api.dto.TokenCountingResourceQuery;
import com.facishare.crm.sfa.activity.predefine.ActivityPredefineObject;
import com.facishare.crm.sfa.activity.predefine.service.model.*;
import com.facishare.crm.sfa.lto.activity.enums.TaskStatusEnum;
import com.facishare.crm.sfa.lto.activity.model.SFANewBusinessModel;
import com.facishare.crm.sfa.lto.activity.service.ActivityTaskStateService;
import com.facishare.crm.sfa.lto.utils.LicenseCheckUtil;
import com.facishare.crm.sfa.lto.utils.StringUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.facishare.crm.sfa.lto.activity.service.ActivityTaskStateService.TASK_STATUS;

@ServiceModule("activity_general")
@Component
@AllArgsConstructor
@Slf4j
public class ActivityGeneralService {

    public static final String INTERACTIVE_CONTENT = "interactive_content"; // 互动语料
    public static final String INTERACTIVE_SUMMARY = "interactive_summary"; // 会议摘要
    public static final String INTERACTIVE_ISSUES = "interactive_issues"; // 互动话题
    public static final String SUGGEST_ISSUES = "suggest_issues"; // 建议话题
    public static final String SENTIMENT_ANALYSIS = "sentiment_analysis"; // 情感分析
    public static final String NEW_BUSINESS_INFO = "new_business_info"; // 新业务信息
    public static final String ACTIVITY_TODO = "activity_todo"; // 待办
    public static final String ATTENDEES_INSIGHT = "sfa_activity_attendees_insight"; // 参会人洞察
    public static final List<String> API_NAME_LIST = Lists.newArrayList(
            INTERACTIVE_SUMMARY, INTERACTIVE_ISSUES, SUGGEST_ISSUES,
            SENTIMENT_ANALYSIS, NEW_BUSINESS_INFO, INTERACTIVE_CONTENT,
            ACTIVITY_TODO, ATTENDEES_INSIGHT
    );
    private static Integer MAX_OVERTIME = 300; // 任务最长生成时间，单位秒

    private final ServiceFacade serviceFacade;
    private final TokenCountingResourceClient tokenCountingResourceClient;
    private final ActivityTaskStateService activityTaskStateService;
    private final ActivityMeetingSummaryService activityMeetingSummaryService;
    private final InteractQuestionService interactQuestionService;
    private final ActivityAdviceTopicService activityAdviceTopicService;
    private final EmotionAnalysisService emotionAnalysisService;
    private final ActivityBusiness activityBusiness;
    private final ActivityTodoService activityTodoService;
    private final ActivityAttendeesInsightService activityAttendeesInsightService;

    static {
        ConfigFactory.getInstance().getConfig("fs-sfa-ai", (config) -> {
            MAX_OVERTIME = config.getInt("activity_state_overtime", 300);
        });
    }

    @ServiceMethod("get_state")
    public ActivityGeneralModel.StateResult getState(ServiceContext context, ActivityGeneralModel.StateArg arg) {
        if (ObjectUtils.isEmpty(arg.getApiName()) || ObjectUtils.isEmpty(arg.getDataId()) || !API_NAME_LIST.contains(arg.getComponentName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_EMPTY));
        }
        // 查算粒余额
        boolean aiResource = true;
        BaseArgument baseArgument = new BaseArgument();
        baseArgument.setTenantId(context.getTenantId());
        baseArgument.setUserId(User.SUPPER_ADMIN_USER_ID);
        try {
            TokenCountingResourceQuery.Result aiTokenResult = tokenCountingResourceClient.queryTokenCounting(baseArgument);
            log.info("get aiTokenResult success:{}", aiTokenResult);
            if (Boolean.TRUE.equals(aiTokenResult.getSuccess()) && Boolean.FALSE.equals(aiTokenResult.getAllowUsing())) {
                aiResource = false;
            }
        } catch (Exception e) {
            log.error("get aiTokenResult error", e);
        }

        ActivityGeneralModel.StateResult result = ActivityGeneralModel.StateResult.builder().aiResource(aiResource).build();
        if (!ActivityPredefineObject.ActiveRecord.getApiName().equals(arg.getApiName())) {
            result.setStateCode(TaskStatusEnum.COMPLETED.getNum());
            return result;
        }
        result.setDetailData(ObjectDataDocument.of(serviceFacade.findObjectData(context.getUser(), arg.getDataId(), arg.getApiName())));
        // 待办特殊处理
        if (ACTIVITY_TODO.equals(arg.getComponentName())) {
            TaskStatusEnum todoEnum = activityTodoService.getState(context.getTenantId(), arg.getDataId(), arg.getInteractiveProcesses());
            if (!TaskStatusEnum.ONGOING.equals(todoEnum)){
                todoEnum = TaskStatusEnum.COMPLETED;
            }
            result.setStateCode(todoEnum.getNum());
            return result;
        }
        // 查询到当前组件的状态
        TaskStatusEnum state = activityTaskStateService.getState(context.getTenantId(), arg.getComponentName(), arg.getDataId(), null);
        if (state == null) {
            log.warn("state is null");
            result.setStateCode(TaskStatusEnum.COMPLETED.getNum());
            return result;
        }
        // 忽略当前组件，更新其他组件的状态
        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        task.submit(() -> API_NAME_LIST.forEach(apiName -> {
            if (apiName.equals(arg.getComponentName())) {
                return;
            }
            updateState(context.getRequestContext(), arg.getDataId(), apiName, result.getAiResource(), arg.getInteractiveProcesses());
        }));
        // 任务完成直接返回完成
        if (TaskStatusEnum.COMPLETED.equals(state)) {
            result.setStateCode(TaskStatusEnum.COMPLETED.getNum());
            return result;
        }
        // 更新结果
        TaskStatusEnum newState = updateState(context.getRequestContext(), arg.getDataId(), arg.getComponentName(), aiResource, arg.getInteractiveProcesses());
        result.setStateCode(newState.getNum());
        return result;
    }

    // 初始化所有的组件状态
    public void initAllState(String tenantId, String dataId) {
        // 判断是否有AiLicense这个产品
        if (!LicenseCheckUtil.checkAIExist(tenantId)) {
           return;
        }
        API_NAME_LIST.forEach(apiName -> {
            if (ACTIVITY_TODO.equals(apiName)){
                return;
            }
            activityTaskStateService.insOrUpdate(tenantId, apiName, dataId, TaskStatusEnum.ONGOING, null);
        });
    }

    private TaskStatusEnum updateState(RequestContext context, String dataId, String apiName, boolean aiResource, boolean interactiveProcesses) {
        IObjectData objectData = activityTaskStateService.getObjectData(context.getTenantId(), apiName, dataId, null);
        if (objectData == null) {
            return TaskStatusEnum.COMPLETED;
        }
        // 已完成跳过更新
        TaskStatusEnum taskStatusEnum = TaskStatusEnum.fromValue(objectData.get(TASK_STATUS, String.class));
        if (TaskStatusEnum.COMPLETED.equals(taskStatusEnum)) {
            return TaskStatusEnum.COMPLETED;
        }
        // 事后判断是否超时
        if (!interactiveProcesses && ((new Date().getTime() - objectData.getCreateTime()) > MAX_OVERTIME * 1000L)) {
            activityTaskStateService.insOrUpdate(context.getTenantId(), apiName, dataId, TaskStatusEnum.COMPLETED, null);
            return TaskStatusEnum.COMPLETED;
        }
        // 判断资源
        if (!aiResource) {
            activityTaskStateService.insOrUpdate(context.getTenantId(), apiName, dataId, TaskStatusEnum.COMPLETED, null);
            return TaskStatusEnum.COMPLETED;
        }
        // 根据不同组件查不同的结果
        TaskStatusEnum state = null;
        IObjectData activeRecordData = null;
        try {
            ServiceContext serviceContext;
            switch (apiName) {
                case INTERACTIVE_CONTENT: // 互动语料
                    if (Boolean.TRUE.equals(interactiveProcesses)) {
                        state = TaskStatusEnum.COMPLETED;
                        break;
                    }
                    activeRecordData = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), dataId, ActivityPredefineObject.ActiveRecord.getApiName());
                    if (!ObjectUtils.isEmpty(activeRecordData.get("interactive_content"))) {
                        state = TaskStatusEnum.COMPLETED;
                    }
                    break;
                case INTERACTIVE_SUMMARY: // 会议摘要
                    serviceContext = new ServiceContext(context, "activity_meeting_summary", "query");
                    ActivityMeetingSummaryModel.Arg interactiveSummaryArg = new ActivityMeetingSummaryModel.Arg();
                    interactiveSummaryArg.setActiveRecordId(dataId);
                    String names = "all_content_summary,active_record_content,speaker_summary,chapter_overview";
                    interactiveSummaryArg.setQuery(names);
                    Map<String,Object> interactiveSummaryResult = activityMeetingSummaryService.query(serviceContext, interactiveSummaryArg);
                    for (String name : names.split(",")) {
                        if (!ObjectUtils.isEmpty(interactiveSummaryResult.get(name))) {
                            state = TaskStatusEnum.COMPLETED;
                            break;
                        }
                    }
                    break;
                case INTERACTIVE_ISSUES: // 互动话题
                    if (TaskStatusEnum.EXPAND.equals(taskStatusEnum)) {
                        break;
                    }
                    serviceContext = new ServiceContext(context, "interact_question", "get_interact_count");
                    InteractQuestionModel.Arg interactiveIssuesArg = InteractQuestionModel.Arg.builder()
                            .objectApiName(ActivityPredefineObject.ActiveRecord.getApiName())
                            .objectDataId(dataId)
                            .build();
                    InteractQuestionModel.InteractCountResult interactiveIssuesResult = interactQuestionService.getInteractCount(serviceContext, interactiveIssuesArg);
                    for (ObjectDataDocument o : interactiveIssuesResult.getDataList()) {

                        if (o != null && o.get("num") != null && StringUtil.convertInt(o.get("num").toString(), 0) != 0) {
                            state = TaskStatusEnum.EXPAND;
                            break;
                        }
                    }
                    break;
                case SUGGEST_ISSUES: // 建议话题
                    // 查出销售记录关联的客户
                    activeRecordData = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), dataId, ActivityPredefineObject.ActiveRecord.getApiName());
                    if (ObjectUtils.isEmpty(activeRecordData) || ObjectUtils.isEmpty(activeRecordData.get("account_id"))) {
                        state = TaskStatusEnum.COMPLETED;
                        break;
                    }
                    serviceContext = new ServiceContext(context, "advice_topic", "statistic");
                    ActivityAdviceTopicModel.Arg suggestIssuesArg = new ActivityAdviceTopicModel.Arg();
                    suggestIssuesArg.setId(activeRecordData.get("account_id", String.class));
                    ActivityAdviceTopicModel.Result suggestIssuesResult = activityAdviceTopicService.statistic(serviceContext, suggestIssuesArg);
                    for (String key : suggestIssuesResult.getStatistic().keySet()) {
                        com.alibaba.fastjson2.JSONObject statisticObject = suggestIssuesResult.getStatistic().get(key);
                        if (statisticObject.getInteger("count") != 0) {
                            state = TaskStatusEnum.COMPLETED;
                            break;
                        }
                    }
                    break;
                case SENTIMENT_ANALYSIS: // 情感分析
                    serviceContext = new ServiceContext(context, "activity_emotion_analysis", "get_summary");
                    EmotionAnalysisModel.GetEmotionSummaryArg emotionAnalysisArg = new EmotionAnalysisModel.GetEmotionSummaryArg();
                    emotionAnalysisArg.setActiveRecordId(dataId);
                    ObjectDataDocument emotionAnalysisData = emotionAnalysisService.getSummary(serviceContext, emotionAnalysisArg).getData();
                    if (emotionAnalysisData != null && !ObjectUtils.isEmpty(emotionAnalysisData.get("detail_count"))) {
                        if (Integer.parseInt(emotionAnalysisData.get("detail_count").toString()) > 0) {
                            state = TaskStatusEnum.COMPLETED;
                        }
                    }
                    break;
                case NEW_BUSINESS_INFO: // 新业务信息
                    serviceContext = new ServiceContext(context, "activity_business", "getInfo");
                    ActivityBusinessModel.Arg businessArg = new ActivityBusinessModel.Arg();
                    businessArg.setDataId(dataId);
                    businessArg.setObjectApiName(ActivityPredefineObject.ActiveRecord.getApiName());
                    ObjectDataDocument businessInfoData = activityBusiness.getInfo(serviceContext, businessArg);
                    ArrayList<String> businessFields = Lists.newArrayList(
                            SFANewBusinessModel.Field.ACCOUNT_ABSTRACT,
                            SFANewBusinessModel.Field.NEW_OPPORTUNITY_ABSTRACT,
                            SFANewBusinessModel.Field.CONTACT_ABSTRACT,
                            SFANewBusinessModel.Field.ACCOUNT_DEPARTMENT_ABSTRACT);
                    for (String businessField : businessFields) {
                        if (!ObjectUtils.isEmpty(businessInfoData.get(businessField))) {
                            state = TaskStatusEnum.COMPLETED;
                            break;
                        }
                    }
                    break;
                case ATTENDEES_INSIGHT: // 参会人洞察
                    serviceContext = new ServiceContext(context, "activity_attendees_insight", "get_attendees");
                    AttendeesInsightModel.GetAttendeesArg attendeesInsightArg = new AttendeesInsightModel.GetAttendeesArg();
                    attendeesInsightArg.setActiveRecordId(dataId);
                    AttendeesInsightModel.GetAttendeesResult attendees = activityAttendeesInsightService.getAttendees(serviceContext, attendeesInsightArg);
                    if (!ObjectUtils.isEmpty(attendees.getDataList())) {
                        state = TaskStatusEnum.COMPLETED;
                    }
                    break;
            }
        } catch (ObjectDataNotFoundException e) {
            state = TaskStatusEnum.ONGOING;
        } catch (Exception e) {
            log.error("select state error,apiName:{}", apiName, e);
            state = TaskStatusEnum.COMPLETED;
        }
        if (state == null) {
            state = TaskStatusEnum.ONGOING;
        } else if (!state.equals(taskStatusEnum)) {
            activityTaskStateService.insOrUpdate(context.getTenantId(), apiName, dataId, state, null);
        }
        return state;
    }

}
