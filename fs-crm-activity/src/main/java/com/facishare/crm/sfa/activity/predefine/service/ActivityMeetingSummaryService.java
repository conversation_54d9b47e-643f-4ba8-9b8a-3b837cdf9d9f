package com.facishare.crm.sfa.activity.predefine.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.facishare.crm.constants.CommonConstants;
import com.facishare.crm.sfa.activity.predefine.service.model.ActivityMeetingSummaryModel;
import com.facishare.crm.sfa.activity.predefine.service.model.InteractQuestionModel;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.crm.sfa.activity.predefine.service.EmotionAnalysisService.EMOTION_ANALYSIS_API_NAME;
import static com.facishare.paas.appframework.metadata.ActionContextExt.SEARCH_RICH_TEXT_EXTRA;

@Component
@ServiceModule("activity_meeting_summary")
@Slf4j
public class ActivityMeetingSummaryService {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private InteractQuestionService interactQuestionService;

    @ServiceMethod("query")
    public Map<String,Object> query(ServiceContext context, ActivityMeetingSummaryModel.Arg arg) {
        if (ObjectUtils.isEmpty(arg)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACTIVITY_MEETING_SUMMARY_PARAM_IS_EMPTY));
        }

        if (Strings.isNullOrEmpty(arg.getActiveRecordId())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACTIVITY_MEETING_SUMMARY_PARAM_IS_EMPTY));
        }

        if (Strings.isNullOrEmpty(arg.getQuery())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACTIVITY_MEETING_SUMMARY_PARAM_IS_EMPTY));
        }

        Map<String, Object> result = getFormattedResult(context, arg.getActiveRecordId(), arg.getQuery());

        IObjectData objectData = null;
        try {
            IActionContext actionContext = ActionContextExt.of(User.systemUser(context.getTenantId())).getContext();
            actionContext.put(SEARCH_RICH_TEXT_EXTRA, true);
            objectData = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), arg.getActiveRecordId(), "ActivityMeetingSummaryObj");
        }catch (Exception e){
            log.warn("query activeRecordObj failed for arg={}", arg, e);
        }

        if (Objects.isNull(objectData)) {
            if (ObjectUtils.isEmpty(result)){
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACTIVITY_MEETING_SUMMARY_RESULT_IS_EMPTY));
            }
            return result;
        }

        List<String> queryKeys = Splitter.on(',').omitEmptyStrings().trimResults().splitToList(arg.getQuery());
        for (String queryKey : queryKeys) {
            Object moduleData = objectData.get(queryKey);
            if (ObjectUtils.isEmpty(moduleData)){
                continue;
            }
            if (!ObjectUtils.isEmpty(result.get(queryKey))) {
                continue;
            }
            // 全文摘要获取拼接其他组件结果
            if ("all_content_summary".equals(queryKey)){
                String appendText = getInteractQuestionAndEmotionAnalysis(context, arg.getActiveRecordId());
                result.put(queryKey, fillFullTextSummaryText(moduleData.toString(), appendText));
                continue;
            }
            // 其他类型的摘要
            try {
                result.put(queryKey, JSON.parseObject(moduleData.toString()));
            } catch (Exception e) {
                log.warn("The value was converted json is incorrect, queryKey={} objectData={}", queryKey, objectData, e);
                result.put(queryKey, null);
            }
        }
        return result;
    }

    private JSONObject fillFullTextSummaryText(String jsonString, String insText) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(jsonString);
            if (ObjectUtils.isEmpty(insText)) {
                return jsonObject;
            }
            JSONArray paragraphContent = jsonObject.getJSONObject("__xt")
                    .getJSONObject("__json")
                    .getJSONArray("content")
                    .getJSONObject(1)
                    .getJSONArray("content");
            for (JSONObject textElement : paragraphContent.toJavaList(JSONObject.class)) {
                if (!"text".equals(textElement.getString("type"))) {
                    continue;
                }
                String text = textElement.getString("text");
                textElement.put("text", text + "\n\n\n" + insText);
                break;
            }
            return jsonObject;
        } catch (Exception e) {
            log.error("fillFullTextSummaryText failed for jsonString={}", jsonString, e);
            return null;
        }
    }

    private String getInteractQuestionAndEmotionAnalysis(ServiceContext context,String activeRecordId){
        // 获取互动话题统计结果
        Map<String,String> interactCountMap = new HashMap<>();
        InteractQuestionModel.Arg arg = InteractQuestionModel.Arg.builder()
                .objectApiName("ActiveRecordObj")
                .objectDataId(activeRecordId)
                .build();
        InteractQuestionModel.InteractCountResult interactCountResult = interactQuestionService.getInteractCount(context, arg);
        interactCountResult.getDataList().forEach(o -> interactCountMap.put(o.get("type").toString(), o.get("num").toString()));
        // 获取情感洞察结果
        Map<String,String> emotionAnalysisCountMap = new HashMap<>();
        SearchTemplateQuery emotionAnalysisQuery = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(emotionAnalysisQuery.getFilters(), "active_record_id", activeRecordId);
        emotionAnalysisQuery.setLimit(1);
        QueryResult<IObjectData> emotionAnalysisResult = serviceFacade.findBySearchQuery(context.getUser(), EMOTION_ANALYSIS_API_NAME, emotionAnalysisQuery);
        if (ObjectUtils.isEmpty(emotionAnalysisResult) || ObjectUtils.isEmpty(emotionAnalysisResult.getData())) {
            emotionAnalysisCountMap.put("detail_count", "0");
        }else {
            IObjectData emotionAnalysisObjectData = emotionAnalysisResult.getData().get(0);
            emotionAnalysisCountMap.put("detail_count", emotionAnalysisObjectData.get("detail_count", String.class));
            emotionAnalysisCountMap.put("satisfaction_count", emotionAnalysisObjectData.get("satisfaction_count", String.class));
            emotionAnalysisCountMap.put("neutrality_count", emotionAnalysisObjectData.get("neutrality_count", String.class));
            emotionAnalysisCountMap.put("dissatisfied_count", emotionAnalysisObjectData.get("dissatisfied_count", String.class));
        }

        // 如果其他组件都没有结果就不再拼接结果
        if ("0".equals(interactCountMap.get("all")) && "0".equals(emotionAnalysisCountMap.get("detail_count"))){
            return "";
        }
        // 拼接结果格式: 本次互动中，对于我们的互动话题，客户满意10/12，客户不满意2/12；干系人满意2/5，干系人中立1/5，干系人不满2/5。
        StringBuilder resString = new StringBuilder();
        resString.append(I18N.text("sfa.activity.summary.full.text.append.head")); // 本次互动中，
        // 拼接互动话题结果
        if (!"0".equals(interactCountMap.get("all"))) {
            String allCount = interactCountMap.get("all");
            resString.append(I18N.text("sfa.activity.summary.full.text.topic.head")); // 对于我们的互动话题
            appendCount(resString, interactCountMap, "satisfied", "topic", allCount);
            appendCount(resString, interactCountMap, "general", "topic", allCount);
            appendCount(resString, interactCountMap, "dissatisfied", "topic", allCount);
            appendCount(resString, interactCountMap, "not_responded", "topic", allCount);
            resString.append(";");
        }
        // 拼接情感洞察结果
        if (!"0".equals(emotionAnalysisCountMap.get("detail_count"))) {
            String allCount = emotionAnalysisCountMap.get("detail_count");
            resString.append(I18N.text("sfa.activity.summary.full.text.emotion.head")); // 干系人
            appendCount(resString, emotionAnalysisCountMap, "satisfaction_count", "emotion", allCount);
            appendCount(resString, emotionAnalysisCountMap, "neutrality_count", "emotion", allCount);
            appendCount(resString, emotionAnalysisCountMap, "dissatisfied_count", "emotion", allCount);
            resString.append(";");
        }
        return resString.toString();
    }

    // 辅助方法：拼接统计结果
    // sfa.activity.summary.full.text.topic.satisfied ，客户满意{0}{1}
    // sfa.activity.summary.full.text.topic.general ，客户一般{0}{1}
    // sfa.activity.summary.full.text.topic.dissatisfied ，客户不满意{0}{1}
    // sfa.activity.summary.full.text.topic.not_responded ，未应答{0}{1}
    // sfa.activity.summary.full.text.emotion.satisfaction_count ，满意{0}/{1}
    // sfa.activity.summary.full.text.emotion.neutrality_count ，中立{0}/{1}
    // sfa.activity.summary.full.text.emotion.dissatisfied_count ，不满{0}/{1}
    private void appendCount(StringBuilder resString, Map<String, String> countMap, String key, String i18n, String allCount) {
        if (!"0".equals(countMap.get(key))) {
            String i18nKey = String.format("sfa.activity.summary.full.text.%s.%s", i18n, key);
            resString.append(I18N.text(i18nKey, countMap.get(key), allCount));
        }
    }

    private Map<String, Object> getFormattedResult(ServiceContext context, String activeRecordId, String keys) {
        Map<String, Object> result = Maps.of();
        User user = User.systemUser(context.getTenantId());
        for (String key : Splitter.on(',').omitEmptyStrings().trimResults().splitToList(keys)) {
            if (!Lists.newArrayList("chapter_overview", "speaker_summary", "all_content_summary").contains(key)) {
                continue;
            }
            List<IObjectData> objectDataList = queryResult(user, activeRecordId, key);
            if (ObjectUtils.isEmpty(objectDataList)){
                continue;
            }
            StringBuilder text = new StringBuilder();
            String title = "";
            try {
                switch (key){
                    case "chapter_overview": // 章节概览
                        // 1: <话题名称>；
                        //   - 时间点：<态度内容>；
                        //   - 话题：<话题>；
                        //   - 结论内容: <结论内容>：
                        title = I18N.text("sfa.activity.meeting.summary.chapter_preview");
                        for (IObjectData objectData : objectDataList) {
                            JSONArray jsonArray = JSONArray.parseArray(objectData.get("content", String.class));
                            text.append(objectData.get("display_order", String.class));
                            text.append(": ");
                            text.append(objectData.getName());
                            text.append("\n  -");
                            text.append(I18N.text("sfa.activity.meeting.summary.time.point"));
                            text.append(":");
                            text.append(objectData.get("start_time"));
                            text.append("\n  -");
                            text.append(I18N.text("sfa.activity.meeting.summary.topic"));
                            text.append(":");
                            text.append(jsonArray.get(0));
                            text.append("\n  -");
                            text.append(I18N.text("sfa.activity.meeting.summary.conclusion"));
                            text.append(":");
                            text.append(jsonArray.get(1));
                            text.append("\n\n");
                        }
                        break;
                    case "speaker_summary": // 发言人总结
                        // 发言人名称：
                        //  1.xxx
                        //  2.xxx
                        title = I18N.text("sfa.activity.meeting.summary.speaker_summary");
                        for (IObjectData objectData : objectDataList) {
                            text.append(objectData.getName());
                            text.append(":\n");
                            JSONArray jsonArray = JSONArray.parseArray(objectData.get("content", String.class));
                            for (int i = 0; i < jsonArray.size(); i++) {
                                text.append("  ");
                                text.append(i+1);
                                text.append(".");
                                text.append(jsonArray.get(i));
                                text.append("\n");
                            }
                            text.append("\n");
                        }
                        break;
                    case "all_content_summary": // 全文摘要
                        // 1.xxx
                        //  - xxx
                        //  - xxx
                        title = I18N.text("sfa.activity.meeting.summary.full_text_summary");
                        for (IObjectData objectData : objectDataList) {
                            text.append(objectData.get("display_order"));
                            text.append(".");
                            text.append(objectData.getName());
                            text.append(":\n");
                            JSONArray jsonArray = JSONArray.parseArray(objectData.get("content", String.class));
                            for (Object o : jsonArray) {
                                text.append("  -");
                                text.append(o);
                                text.append("\n");
                            }
                            text.append("\n");
                        }
                        if (text.length() != 0) {
                            text.append(getInteractQuestionAndEmotionAnalysis(context, activeRecordId));
                        }
                        break;
                }
            }catch (Exception e){
                log.warn("getFormattedResult error, key:{}", key, e);
            }

            if (text.length() == 0 || ObjectUtils.isEmpty(title)) {
                continue;
            }
            result.put(key, stringToFulltext(title, text.toString()));
        }
        return result;
    }

    private List<IObjectData> queryResult(User user, String activeRecordId,String key){
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, user.getTenantId());
        SearchUtil.fillFilterEq(filters, CommonConstants.IS_DELETED, 0);
        SearchUtil.fillFilterEq(filters, "active_record_id", activeRecordId);
        SearchUtil.fillFilterEq(filters, "components_name", key);
        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName("display_order");
        orderBy.setIsAsc(true);
        query.setFilters(filters);
        query.setOrders(Lists.newArrayList(orderBy));
        try {
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, "ActivityMeetingSummaryFormatObj", query);
            if (ObjectUtils.isEmpty(queryResult) || ObjectUtils.isEmpty(queryResult.getData())) {
                return Lists.newArrayList();
            }
            return queryResult.getData();
        } catch (Exception e) {
            log.warn("get format data error", e);
        }
        return Lists.newArrayList();
    }

    public static Map<String, Object> stringToFulltext(String title, String body) {
        List<Object> contentList = Lists.newArrayList();
        contentList.add(buildContent(title, Lists.newArrayList(Maps.of("type", "bold"))));
        contentList.add(buildContent(body, null));
        Map<String, Object> doc = Maps.of("type", "doc", "content", contentList);
        Map<String, Object> json = Maps.of("__json", doc);
        return Maps.of("cmpt", "XT_TEXT", "__xt", json);
    }

    private static Map<String, Object> buildContent(String text, List<Object> marks) {
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("type", "text");
        contentMap.put("text", removeMarkdownSymbols(text));
        if (!CollectionUtils.isEmpty(marks)) {
            contentMap.put("marks", marks);
        }
        List<Object> contentList = Lists.newArrayList();
        contentList.add(contentMap);
        Map<String, Object> attrs = new HashMap<>();
        attrs.put("textAlign", "left");
        attrs.put("isSFAActivityCreated", "true");
        Map<String, Object> paragraph = new HashMap<>();
        paragraph.put("type", "paragraph");
        paragraph.put("attrs", attrs);
        paragraph.put("content", contentList);
        return paragraph;
    }
    private static String removeMarkdownSymbols(String text) {
        if (Strings.isNullOrEmpty(text)) {
            return "";
        }
        // 移除所有的 #、* 符号和多余的空行
        return text.replaceAll("#+|\\*+", "") // 移除一个或多个#号和*号
                .trim(); // 移除首尾空白
    }

}

/*

http://localhost:8080/API/v1/inner/object/activity_meeting_summary/service/query

{
    "activeRecordId":"674707b70f30990001033c3d","query":"all_content_summary,chapter_overview"
}

*/
