package com.facishare.crm.sfa.activity.predefine.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.activity.predefine.service.model.ActivityText;
import com.facishare.crm.sfa.activity.predefine.service.speakerchange.BindSpeakerOperation;
import com.facishare.crm.sfa.activity.predefine.service.speakerchange.EditSpeakerOperation;
import com.facishare.crm.sfa.activity.predefine.service.speakerchange.MergeSpeakerOperation;
import com.facishare.crm.sfa.activity.predefine.service.speakerchange.SpeakerChangeOperation;
import com.facishare.crm.sfa.lto.activity.model.ActivityUserModel;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.util.Safes;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.apache.commons.lang3.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> gongchunru
 * @date : 2025/4/18 10:31
 * @description:
 */
@Component
@Slf4j
public class ActivityUserService {

    private static final String ACTIVITY_USER_API_NAME = "ActivityUserObj";

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private IObjectDataService objectDataService;

    @Resource
    private ActivityMongoDao activityMongoDao;

    // 策略映射
    private final Map<ActivityText.SpeakerOpType, SpeakerChangeOperation> speakerChangeOperations = new HashMap<>();

    // 注入策略Bean
    @Resource
    private BindSpeakerOperation bindSpeakerOperation;

    @Resource
    private EditSpeakerOperation editSpeakerOperation;

    @Resource
    private MergeSpeakerOperation mergeSpeakerOperation;

    // PostConstruct用于在依赖注入完成后初始化策略Map
    @PostConstruct
    public void initStrategies() {
        // 初始化策略 - 使用Spring注入的Bean而不是直接new实例
        speakerChangeOperations.put(ActivityText.SpeakerOpType.BIND, bindSpeakerOperation);
        speakerChangeOperations.put(ActivityText.SpeakerOpType.EDIT, editSpeakerOperation);
        speakerChangeOperations.put(ActivityText.SpeakerOpType.MERGE, mergeSpeakerOperation);
        log.info("Speaker change strategies initialized with {} operations", speakerChangeOperations.size());
    }


    public static final List<String> AVATAR_BG_COLORS = Lists.newArrayList("#FFA142","#368DFF","#55D48C","#FF7752","#C1C5CE","#BC97F7","#5BCFC1","#A3D962");

    private Integer getDefaultAvatarBgColor(Integer nameAvaId) {
        if (nameAvaId == null || nameAvaId <= 0) { // 修改判断条件，0 也应该使用第一个颜色
            return 0;
        }
        return nameAvaId;
    }

    public void upsertActivityUser(ServiceContext context, ActivityText.SaveMeetingDocResult meetingDocResult, List<InteractiveDocument> interactiveDocuments) {
        User user = context.getUser();
        String objectId = meetingDocResult.getObjectId();
        // 获取里面的用户信息，然后拍平
        List<ActivityUserModel.ActivityUser> activityUsers = meetingDocResult.getContent().stream()
            .map(content -> {
                // 使用userId作为唯一标识
                String userId = content.getUi();
                ActivityUserModel.ActivityUser activityUser = new ActivityUserModel.ActivityUser();
                activityUser.setActiveRecordId(meetingDocResult.getObjectId());
                activityUser.setTenantId(user.getTenantId());
                activityUser.setUserId(userId);
                activityUser.setUserName(StringUtils.isEmpty(content.getUserName()) ? "user_"+content.getUi() : content.getUserName());
                activityUser.setName(StringUtils.isEmpty(content.getUserName()) ? "user_"+content.getUi() : content.getUserName());
                if (StringUtils.isNotEmpty(content.getUserApiName())) {
                    activityUser.setUserApiName(content.getUserApiName());
                }
                activityUser.setOriginalUserId(userId);
                activityUser.setOriginalUserName(StringUtils.isEmpty(content.getUserName()) ? "user_"+content.getUi() : content.getUserName());
                activityUser.setNameAvaId(Long.valueOf(getDefaultAvatarBgColor(content.getNameAvaId())));
                activityUser.setAvatarBgColor(calAvatarBgColor(content.getNameAvaId()));
                activityUser.setObjectDescribeApiName(meetingDocResult.getObjectApiName());
                activityUser.setCreateTime(System.currentTimeMillis());
                activityUser.setCreatedBy(user.getUserId());
                activityUser.setIsDefaultSpearker((content.getIsDefaultSpeaker() == null || content.getIsDefaultSpeaker()) ? 1 : 0);
                return activityUser;
            })
            // 使用 userId 和 userName 共同判断是否为同一个用户进行去重
            .collect(Collectors.toMap(
                au -> au.getUserId() + "_" + au.getUserName(), 
                user1 -> user1, 
                (existing, replacement) -> existing  // 如果键冲突，保留现有值
            ))
            .values()
            .stream()
            .collect(Collectors.toList());
            
        log.info("upsertActivityUser {}", activityUsers);
        
        List<IObjectData> existActivityUsers = getActivityUsers(user.getTenantId(), interactiveDocuments.get(0).getObjectId());

        List<ActivityUserModel.ActivityUser> newActivityUsers = new ArrayList<>();
        List<ActivityUserModel.ActivityUser> updateActivityUsers = new ArrayList<>();
        // 构建userId到activityUserId的映射, 包含所有需要处理的用户
        Map<String, String> userIdToActivityUserId = new HashMap<>();

        // 处理每个用户
        for (ActivityUserModel.ActivityUser activityUser : activityUsers) {
            boolean exists = activityUserExist(existActivityUsers, activityUser); // activityUserExist会设置ID如果用户存在
            log.info("exists: {}, objectId: {}, activityUser: {}", exists, objectId, activityUser);
            if (exists) {
                userIdToActivityUserId.put(activityUser.getOriginalUserId(), activityUser.getId()); // 记录映射关系
                if (activityUser.getIsDefaultSpearker() == 0) {
                    // 存在的非默认用户，需要更新
                    log.info("更新现有非默认用户:{}, {}", objectId, activityUser.getUserName());
                    updateActivityUsers.add(activityUser);
                } else {
                    // 存在的默认用户，不需要更新，仅记录映射关系即可
                    log.info("跳过更新现有默认用户:{}, {}", objectId, activityUser.getUserName());
                }
            } else {
                // 用户不存在，需要新建
                newActivityUsers.add(activityUser);
            }
        }
        
        // 插入新用户
        List<IObjectData> newActivityUserResult = insertActivityUser(user, newActivityUsers);
        // 将新用户的映射关系添加到map
        newActivityUserResult.forEach(item ->
            userIdToActivityUserId.put(
                (String) item.get("original_user_id"),
                item.getId()
            )
        );

        // 更新需要更新的用户
        updateActivityUser(context, updateActivityUsers);

        // 一次性更新所有document的activityUserId
        interactiveDocuments.forEach(document -> {
             String activityUserId = userIdToActivityUserId.get(document.getUserId());
             if (StringUtils.isNotEmpty(activityUserId)) { // 增加空指针检查
                document.setActivityUserId(activityUserId);
             } else {
                 log.warn("cant find biz_activity_user '{}'  activityUserId, activeRecordId: {}, docId: {}",
                          document.getUserId(), objectId, document.getId());
             }
         });
    }

    /**
     * 检查活动用户是否已存在
     * @param dbActivityUsers 数据库中已存在的活动用户列表
     * @param newActivityUser 待检查的新活动用户
     * @return true:用户已存在; false:用户不存在
     */
    public boolean activityUserExist(List<IObjectData> dbActivityUsers, ActivityUserModel.ActivityUser newActivityUser) {
        if (dbActivityUsers == null || dbActivityUsers.isEmpty() || newActivityUser == null || newActivityUser.getOriginalUserId() == null) {
            return false;
        }

        String newUserId = newActivityUser.getOriginalUserId();
        for (IObjectData user : dbActivityUsers) {
            if (user == null || user.get("original_user_id") == null) {
                continue;
            }
            String userId = user.get("original_user_id").toString();
            if (userId.equals(newUserId)) {
                newActivityUser.setId(user.getId());
                return true;
            }
        }
        return false;
    }

    public List<IObjectData> insertActivityUser(User user, List<ActivityUserModel.ActivityUser> activityUsers) {
        List<IObjectData> objectDatas = activityUsers.stream().map(activityUser -> {
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("tenant_id", user.getTenantId());
            dataMap.put("active_record_id", activityUser.getActiveRecordId());
            dataMap.put("personnel_id", activityUser.getPersonnelId());
            dataMap.put("user_id", activityUser.getUserId());
            dataMap.put("user_name", activityUser.getUserName());
            dataMap.put("name", activityUser.getName());
            dataMap.put("original_user_id", activityUser.getOriginalUserId());
            dataMap.put("original_user_name", activityUser.getOriginalUserName());
            dataMap.put("user_api_name", activityUser.getUserApiName());
            dataMap.put("name_ava_id", activityUser.getNameAvaId());
            dataMap.put("avatar_bg_color", activityUser.getAvatarBgColor());
            dataMap.put("is_default_speaker", activityUser.getIsDefaultSpearker());
            dataMap.put("object_describe_api_name", ACTIVITY_USER_API_NAME);
            dataMap.put("create_time", activityUser.getCreateTime());
            dataMap.put("created_by", Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
            dataMap.put("is_deleted", false);
            dataMap.put("sys_modified_time", System.currentTimeMillis());
            return ObjectDataExt.of(dataMap).getObjectData();
        }).collect(Collectors.toList());
        List<IObjectData> result = serviceFacade.bulkSaveObjectData(objectDatas, user);
        log.info("insertActivityUser result: {}", result);
        return result;
    }


    public void updateActivityUser(ServiceContext context, List<ActivityUserModel.ActivityUser> activityUsers) {
        List<IObjectData> updateDatas = new ArrayList<>();
        activityUsers.forEach(activityUser -> {
            IObjectData data = new ObjectData();
            data.setTenantId(context.getTenantId());
            data.setId(activityUser.getId());
            data.setDescribeApiName("ActivityUserObj");
            data.set("user_name", activityUser.getUserName());
            data.set("name", activityUser.getName());
            data.set("user_api_name", activityUser.getUserApiName());
            data.set("user_id", activityUser.getUserId());
            data.set("name_ava_id", activityUser.getNameAvaId());
            data.set("avatar_bg_color", calAvatarBgColor(activityUser.getNameAvaId().intValue()));
            data.set("is_default_speaker", 0);
            updateDatas.add(data);
        });
        List<String> updateFields = Lists.newArrayList("user_name", "name", "user_api_name", "user_id", "name_ava_id", "avatar_bg_color", "is_default_speaker");
        updateActivityUser(context, updateDatas, updateFields);
    }

    public void updateActivityUser(ServiceContext context, List<IObjectData> objectDatas, List<String> updateFields) {
        if (CollectionUtils.isEmpty(objectDatas)) {
            return;
        }
        IActionContext actionContext = ActionContextExt.of(context.getUser()).skipRelevantTeam().getContext();
        try {
            objectDataService.batchUpdateIgnoreOther(objectDatas, updateFields, actionContext);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public List<IObjectData> getActivityUsers(String tenantId, String activeRecordId) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, "0");
        SearchUtil.fillFilterEq(filters, IObjectData.TENANT_ID, tenantId);
        SearchUtil.fillFilterEq(filters, "active_record_id", activeRecordId);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setNeedReturnQuote(false);
        searchTemplateQuery.setLimit(100);
        searchTemplateQuery.setOffset(0);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ACTIVITY_USER_API_NAME, searchTemplateQuery);
        if (ObjectUtils.isEmpty(queryResult) || ObjectUtils.isEmpty(queryResult.getData())) {
            return null;
        }
        return queryResult.getData();
    }

    public List<IObjectData> getActivityUsersWithProfileImage(String tenantId, String activeRecordId) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, "0");
        SearchUtil.fillFilterEq(filters, IObjectData.TENANT_ID, tenantId);
        SearchUtil.fillFilterEq(filters, "active_record_id", activeRecordId);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setNeedReturnQuote(false);
        searchTemplateQuery.setLimit(100);
        searchTemplateQuery.setOffset(0);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ACTIVITY_USER_API_NAME, searchTemplateQuery);
        if (ObjectUtils.isEmpty(queryResult) || ObjectUtils.isEmpty(queryResult.getData())) {
            return null;
        }
        List<IObjectData> activityUserList = queryResult.getData();
        
        // 获取头像
        List<String> personnelIds = activityUserList.stream()
                .filter(x -> ObjectUtils.isNotEmpty(x.get("personnel_id")))
                .map(x -> x.get("personnel_id").toString())
                .collect(Collectors.toList());
                
        if (CollectionUtils.isNotEmpty(personnelIds)) {
            List<IObjectData> personnelList = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, personnelIds, "PersonnelObj");
            Map<String, Object> profileImageMap = personnelList.stream()
                    .filter(x -> ObjectUtils.isNotEmpty(x.get("profile_image")))
                    .collect(Collectors.toMap(IObjectData::getId, x -> x.get("profile_image")));
                    
            activityUserList.forEach(x -> {
                if (ObjectUtils.isNotEmpty(x.get("personnel_id"))) {
                    String personnelId = x.get("personnel_id").toString();
                    if (profileImageMap.containsKey(personnelId)) {
                        x.set("profile_image", profileImageMap.get(personnelId));
                    }
                }
            });
        }
        
        return activityUserList;
    }


    public void batchChangeSpeaker(ServiceContext context, ActivityText.BatchChangeSpeaker batchChangeSpeaker) {
        List<IObjectData> activityUsers = getActivityUsers(context.getTenantId(), batchChangeSpeaker.getObjectId());
        if (CollectionUtils.isEmpty(activityUsers)) {
            log.info("No activity users found for objectId: {}. Regenerating...", batchChangeSpeaker.getObjectId());
            reGenerateActivityUser(context, batchChangeSpeaker.getObjectId());
            activityUsers = getActivityUsers(context.getTenantId(), batchChangeSpeaker.getObjectId());
            if (CollectionUtils.isEmpty(activityUsers)) {
                log.warn("Still no activity users found after regeneration for objectId: {}. Skipping batch change.", batchChangeSpeaker.getObjectId());
                return; // 如果重建后仍然没有用户，则直接返回
            }
        }

        // Fix: Use valueOf() or iterate to get enum from String value
        ActivityText.SpeakerOpType opType = null;
        try {
            // Try standard valueOf first
            opType = ActivityText.SpeakerOpType.valueOf(batchChangeSpeaker.getOpType().toUpperCase());
        } catch (IllegalArgumentException e) {
            log.warn("Could not find SpeakerOpType enum for value: '{}'. Trying iteration.", batchChangeSpeaker.getOpType());
            // Fallback: Iterate through enum values to find a match (case-insensitive)
            for (ActivityText.SpeakerOpType type : ActivityText.SpeakerOpType.values()) {
                // Assuming SpeakerOpType has a getValue() method returning the string representation
                // Adjust this comparison based on how SpeakerOpType stores its value
                if (type.getValue().equalsIgnoreCase(batchChangeSpeaker.getOpType())) {
                    opType = type;
                    break;
                }
            }
        }

        if (opType == null) {
             log.error("Unsupported or invalid speaker operation type provided: {}", batchChangeSpeaker.getOpType());
             return;
        }

        SpeakerChangeOperation operation = speakerChangeOperations.get(opType);

        if (operation != null) {
            log.info("Executing speaker change operation: {} for objectId: {}", opType, batchChangeSpeaker.getObjectId());
            operation.execute(context, batchChangeSpeaker, activityUsers); // 不再需要传递this参数
        } else {
            log.warn("Unsupported speaker operation type: {}", batchChangeSpeaker.getOpType());
         }
    }



    /**
     * 根据目标用户的 API 名称填充相应的 ID 字段。
     *
     * @param newData           要更新的 ObjectData
     * @param targetUserApiName 目标用户的 API 名称
     * @param targetUserId      目标用户的 ID
     */
    public void populateTargetUserFields(String tenantId, IObjectData newData, String targetUserApiName, String targetUserId) {
        // 清除可能存在的旧关联字段，以防万一
        newData.set("personnel_id", null);
        newData.set("contact_id", null);
        newData.set("public_employee_id", null);

        if (StringUtils.isEmpty(targetUserApiName) || StringUtils.isEmpty(targetUserId)) {
            log.warn("Cannot populate target user fields: targetUserApiName or targetUserId is empty.");
            return;
        }

        if ("PersonnelObj".equals(targetUserApiName)) {
            SearchTemplateQuery query = new SearchTemplateQuery();
            SearchUtil.fillFilterEq(query.getFilters(), "user_id", targetUserId);
            List<IObjectData> userList = serviceFacade.findBySearchQuery(User.systemUser(tenantId), Utils.PERSONNEL_OBJ_API_NAME, query).getData();
            targetUserId = Optional.ofNullable(Safes.first(userList)).map(IObjectData::getId).orElse(targetUserId);
            newData.set("personnel_id", targetUserId);
            newData.set("participant_types", "our_side");
        } else if ("ContactObj".equals(targetUserApiName)) {
            newData.set("contact_id", targetUserId);
            newData.set("participant_types", "their_side");
        } else if ("PublicEmployeeObj".equals(targetUserApiName)) {
            newData.set("public_employee_id", targetUserId);
            newData.set("participant_types", "our_side");
        } else {
            log.warn("Unknown targetUserApiName: {} for targetUserId: {}. No specific field populated.", targetUserApiName, targetUserId);
        }
    }

    public void reGenerateActivityUser(ServiceContext context, String activeRecordId) {
        // 重建发言人信息,
        //1. 查询文档，根据文档的人，去重新构建发言人信息。
        // 2. 生成发言人信息，然后插入到数据库中。
        // 3. 更新MongoDB的document 的 activityUserId
        List<InteractiveDocument> interactiveDocuments = activityMongoDao.queryListByActiveRecordId(context.getTenantId(), activeRecordId,0,2000);
        List<ActivityUserModel.ActivityUser> activityUsers = new ArrayList<>();
        
        // 使用userId进行去重，避免重复添加同一个用户
        Map<String, ActivityUserModel.ActivityUser> userMap = new HashMap<>();
        // 判断是不是所有的 userID 都为空
        boolean allUserIdEmpty = interactiveDocuments.stream().allMatch(doc -> StringUtils.isEmpty(doc.getUserId()));

        
        for (InteractiveDocument interactiveDocument : interactiveDocuments) {
            String userId = interactiveDocument.getUserId();
            if (StringUtils.isEmpty(userId) || userMap.containsKey(userId)) {
                if (allUserIdEmpty) {
                    userId = "1";
                    interactiveDocument.setUserId(userId);
                }else{
                    continue;
                }
            }
            
            ActivityUserModel.ActivityUser activityUser = new ActivityUserModel.ActivityUser();
            activityUser.setActiveRecordId(activeRecordId);
            activityUser.setTenantId(context.getTenantId());
            activityUser.setUserId(userId);
            activityUser.setUserName(StringUtils.isEmpty(interactiveDocument.getUserName()) ? "user_" + userId : interactiveDocument.getUserName());
            activityUser.setName(StringUtils.isEmpty(interactiveDocument.getUserName()) ? "user_" + userId : interactiveDocument.getUserName());
            activityUser.setOriginalUserId(userId);
            activityUser.setOriginalUserName(StringUtils.isEmpty(interactiveDocument.getUserName()) ? "user_" + userId : interactiveDocument.getUserName());
            
            // 设置头像相关信息
            int nameAvaId = interactiveDocument.getNameAvaId() != null ? interactiveDocument.getNameAvaId() : 0;
            activityUser.setNameAvaId((long) nameAvaId);
            activityUser.setAvatarBgColor(calAvatarBgColor(nameAvaId));
            
            activityUser.setObjectDescribeApiName(ACTIVITY_USER_API_NAME);
            activityUser.setCreateTime(System.currentTimeMillis());
            activityUser.setCreatedBy(context.getUser().getUserId());
            activityUser.setIsDefaultSpearker(0); // 非默认发言人
            userMap.put(userId, activityUser);
        }
        
        // 转换Map为List
        activityUsers = new ArrayList<>(userMap.values());
        
        log.info("reGenerateActivityUser 用户列表: {}", activityUsers);
        
        if (activityUsers.isEmpty()) {
            log.warn("没有找到需要重建的活动用户信息，activeRecordId: {}", activeRecordId);
            return;
        }
        
        // 获取现有的ActivityUser记录
        List<IObjectData> existActivityUsers = getActivityUsers(context.getTenantId(), activeRecordId);
        
        List<ActivityUserModel.ActivityUser> newActivityUsers = new ArrayList<>();
        List<ActivityUserModel.ActivityUser> updateActivityUsers = new ArrayList<>();
        
        // 处理每个用户，区分新增和更新
        for (ActivityUserModel.ActivityUser activityUser : activityUsers) {
            boolean exists = activityUserExist(existActivityUsers, activityUser);
            if (exists) {
                log.info("更新现有用户: {}", activityUser.getUserName());
                updateActivityUsers.add(activityUser);
            } else {
                newActivityUsers.add(activityUser);
            }
        }
        
        // 插入新用户
        List<IObjectData> newActivityUserResult = new ArrayList<>();
        if (!newActivityUsers.isEmpty()) {
            newActivityUserResult = insertActivityUser(context.getUser(), newActivityUsers);
        }
        
        // 更新现有用户
        if (!updateActivityUsers.isEmpty()) {
            updateActivityUser(context, updateActivityUsers);
        }
        
        // 构建userId到activityUserId的映射
        Map<String, String> userIdToActivityUserId = new HashMap<>();
        
        // 添加新创建的用户映射
        if (!newActivityUserResult.isEmpty()) {
            newActivityUserResult.forEach(item -> 
                userIdToActivityUserId.put(
                    (String) item.get("original_user_id"), 
                    item.getId()
                )
            );
        }
        
        // 添加更新的用户映射
        if (!updateActivityUsers.isEmpty()) {
            updateActivityUsers.forEach(item -> 
                userIdToActivityUserId.put(
                    item.getOriginalUserId(), 
                    item.getId()
                )
            );
        }
        
        // 更新MongoDB中document的activityUserId
        for (InteractiveDocument document : interactiveDocuments) {
            String activityUserId = userIdToActivityUserId.get(document.getUserId());
            if (StringUtils.isNotEmpty(activityUserId)) {
                document.setActivityUserId(activityUserId);

            }
        }
        if (allUserIdEmpty) {
            activityMongoDao.updateActivityUserIdAndUserId(context.getTenantId(), activeRecordId, interactiveDocuments);
        }else{
            activityMongoDao.updateActivityUserId(context.getTenantId(), activeRecordId, interactiveDocuments);
        }
        log.info("重建活动用户信息完成，activeRecordId: {}, 新建: {}, 更新: {}", 
            activeRecordId, newActivityUsers.size(), updateActivityUsers.size());
    }


    public String calAvatarBgColor(Integer nameAvaId) {
        if (nameAvaId == null || nameAvaId <= 0) { // 修改判断条件，0 也应该使用第一个颜色
            return AVATAR_BG_COLORS.get(0);
        }
        // 确保索引在范围内
//        int index = (nameAvaId - 1) % AVATAR_BG_COLORS.size(); // 如果 nameAvaId 从 1 开始计数
        // 或者
        int index = nameAvaId % AVATAR_BG_COLORS.size(); // 如果 nameAvaId 从 0 开始计数，根据实际情况调整
        return AVATAR_BG_COLORS.get(index);
    }

    // 添加getter以便策略类可以访问
    public List<String> getAvatarBgColors() {
        return AVATAR_BG_COLORS;
    }

    public void deleteActivityUser(ServiceContext context, List<IObjectData> activityUsers) {
        serviceFacade.bulkDeleteDirect(activityUsers, context.getUser());
    }

}
