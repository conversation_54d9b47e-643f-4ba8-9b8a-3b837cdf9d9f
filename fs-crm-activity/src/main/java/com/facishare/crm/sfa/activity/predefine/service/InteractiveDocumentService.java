package com.facishare.crm.sfa.activity.predefine.service;

import com.facishare.crm.sfa.activity.predefine.service.model.ActivityText;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> gongchunru
 * @date : 2025/4/20 18:58
 * @description:
 */
@Component
@Slf4j
public class InteractiveDocumentService {

    @Resource
    private ActivityMongoDao activityMongoDao;

    @Resource
    private ActivityUserService activityUserService;

    public List<Map<String, Object>> getFullInteractiveDocumentList(ServiceContext context, ActivityText.ActivityTextQueryArg arg) {
        List<InteractiveDocument> interactiveDocuments = activityMongoDao.queryListByActiveRecordId(context.getTenantId(), arg.getObjectId(), arg.getOffset(), arg.getLimit(),true);
        return getInteractiveDocumentList(context,arg.getObjectId(), interactiveDocuments);
    }

    public List<Map<String, Object>> getFullInteractiveDocumentForSave(ServiceContext context, ActivityText.SaveMeetingDocResult meetingDocResult) {
        if (CollectionUtils.isEmpty(meetingDocResult.getContent())) {
            return Lists.newArrayList();
        }
        List<Long> seqList = meetingDocResult.getContent().stream()  
            .map(ActivityText.Content::getId)
            .collect(Collectors.toList());
        List<InteractiveDocument> interactiveDocuments = activityMongoDao.queryListByActiveRecordIdAndSeq(context.getTenantId(), meetingDocResult.getObjectId(), seqList);
        return getInteractiveDocumentList(context,meetingDocResult.getObjectId(), interactiveDocuments);
    }
    
    /**
     * 根据参数获取过滤后的互动文档内容
     * @param context 服务上下文
     * @param arg 包含过滤参数的请求参数
     * @return 过滤后的互动文档列表
     */
    public List<Map<String, Object>> getInteractiveContent(ServiceContext context, ActivityText.GetInteractiveContentArg arg) {
        // 首先获取所有互动文档
        List<InteractiveDocument> allInteractiveDocuments = activityMongoDao.queryListByActiveRecordId(
            context.getTenantId(), arg.getObjectId(), 0, 2000, true);
        
        if (CollectionUtils.isEmpty(allInteractiveDocuments)) {
            return Lists.newArrayList();
        }
        
        // 获取过滤后的活动用户信息
        List<IObjectData> filteredActivityUsers = getFilteredActivityUsers(context, arg);
        
        // 根据过滤后的用户来过滤文档
        List<InteractiveDocument> filteredDocuments = filterDocumentsByActivityUsers(allInteractiveDocuments, filteredActivityUsers);

        List<Map<String, Object>> dataList =  buildNewInteractiveDocument(filteredDocuments, filteredActivityUsers);
        
        return dataList;
    }

    private List<Map<String, Object>> buildNewInteractiveDocument(List<InteractiveDocument> filteredDocuments, List<IObjectData> filteredActivityUsers) {
        Map<String, IObjectData> activityUserMap = filteredActivityUsers.stream()
            .collect(Collectors.toMap(user -> user.getId(), obj -> obj));

        List<Map<String, Object>> dataList = Lists.newArrayList();
        filteredDocuments.forEach(doc -> {
            Map<String, Object> map = new HashMap<>();
            map.put("id", doc.getId().toString());
            map.put("objectId", doc.getObjectId());
            map.put("seq", doc.getSeq());
            map.put("startTime", doc.getStartTime());
            map.put("endTime", doc.getEndTime());
            map.put("content", doc.getContent());
            map.put("translateContent", doc.getTranslateContent());
            if (activityUserMap.get(doc.getActivityUserId()) != null) {
                map.put("userId",activityUserMap.get(doc.getActivityUserId()).get("user_id"));  
                String userName = activityUserMap.get(doc.getActivityUserId()).get("user_name", String.class);
                if (userName!= null && userName.startsWith("user_")) {
                    userName = "发言人"+userName.substring(5,userName.length()); // ignoreI18n
                }
                map.put("userName",userName);
            }
            dataList.add(map);
        });
        return dataList;
    }
    
    /**
     * 根据参数获取过滤后的活动用户
     * @param context 服务上下文
     * @param arg 过滤参数
     * @return 过滤后的活动用户列表
     */
    private List<IObjectData> getFilteredActivityUsers(ServiceContext context, ActivityText.GetInteractiveContentArg arg) {
        // 获取所有活动用户
        List<IObjectData> allActivityUsers = activityUserService.getActivityUsers(context.getTenantId(), arg.getObjectId());
        
        if (CollectionUtils.isEmpty(allActivityUsers)) {
            return Lists.newArrayList();
        }
        
        // 如果没有提供任何过滤参数，返回所有用户
        if (CollectionUtils.isEmpty(arg.getPersonnelIds()) && CollectionUtils.isEmpty(arg.getContactIds()) && CollectionUtils.isEmpty(arg.getPublicEmployeeIds())) {
            return allActivityUsers;
        }
        
        // 根据参数过滤用户
        return allActivityUsers.stream()
            .filter(user -> matchesUserFilterCriteria(user, arg))
            .collect(Collectors.toList());
    }
    
    /**
     * 检查用户是否匹配过滤条件
     * @param user 活动用户
     * @param arg 过滤参数
     * @return 是否匹配
     */
    private boolean matchesUserFilterCriteria(IObjectData user, ActivityText.GetInteractiveContentArg arg) {
        // 检查人员ID匹配
        if (CollectionUtils.isNotEmpty(arg.getPersonnelIds())) {
            String personnelId = user.get("personnel_id", String.class);
            if (arg.getPersonnelIds().contains(personnelId)) {
                return true;
            }
        }
        
        // 检查联系人ID匹配
        if (CollectionUtils.isNotEmpty(arg.getContactIds())) {
            String contactId = user.get("contact_id", String.class);
            if (arg.getContactIds().contains(contactId)) {
                return true;
            }
        }
        
        // 检查公共员工ID匹配
        if (CollectionUtils.isNotEmpty(arg.getPublicEmployeeIds())) {
            String publicEmployeeId = user.get("public_employee_id", String.class);
            if (arg.getPublicEmployeeIds().contains(publicEmployeeId)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 根据过滤后的活动用户来过滤文档
     * @param documents 所有文档列表
     * @param filteredActivityUsers 过滤后的活动用户列表
     * @return 过滤后的文档列表
     */
    private List<InteractiveDocument> filterDocumentsByActivityUsers(List<InteractiveDocument> documents, 
                                                                     List<IObjectData> filteredActivityUsers) {
        if (CollectionUtils.isEmpty(documents) || CollectionUtils.isEmpty(filteredActivityUsers)) {
            return Lists.newArrayList();
        }
        
        // 构建活动用户ID集合，用于快速查找
        Set<String> activityUserIds = filteredActivityUsers.stream()
            .map(IObjectData::getId)
            .collect(Collectors.toSet());
        
        // 过滤文档：只保留activityUserId在过滤后用户列表中的文档
        return documents.stream()
            .filter(doc -> StringUtils.isNotBlank(doc.getActivityUserId()) && 
                          activityUserIds.contains(doc.getActivityUserId()))
            .collect(Collectors.toList());
    }

    public List<Map<String, Object>> getInteractiveDocumentList(ServiceContext context,String objectId, List<InteractiveDocument> interactiveDocuments) {
        String tenantId = context.getTenantId();
        List<IObjectData> activityUsers = activityUserService.getActivityUsersWithProfileImage(tenantId, objectId);
        if (CollectionUtils.isNotEmpty(interactiveDocuments) && CollectionUtils.isEmpty(activityUsers)) {
            activityUserService.reGenerateActivityUser(context, objectId);
            activityUsers = activityUserService.getActivityUsersWithProfileImage(tenantId, objectId);
        }
        // 转换为Map列表
        List<Map<String, Object>> dataList = interactiveDocuments.stream()
            .map(this::interactiveDocumentTransfMap)
            .collect(Collectors.toList());
            
        List<String> activityUserIds = interactiveDocuments.stream()
            .map(InteractiveDocument::getActivityUserId)
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(activityUserIds)) {
            if (CollectionUtils.isEmpty(activityUsers)) {
                return dataList;
            }
            Map<String, IObjectData> activityUserMap = activityUsers.stream()
                .collect(Collectors.toMap(IObjectData::getId, obj -> obj));

            dataList.forEach(map -> {
                String activityUserId = (String) map.get("activityUserId");
                IObjectData activityUser = activityUserMap.get(activityUserId);
                if (activityUser != null) {
                    map.put("userName",activityUser.get("user_name"));
                    map.put("userApiName",activityUser.get("user_api_name"));
                    map.put("originalUserId",activityUser.get("original_user_id"));
                    map.put("originalUserName",activityUser.get("original_user_name"));
                    map.put("nameAvaId",activityUser.get("name_ava_id"));
                    map.put("userId",activityUser.get("user_id"));
                    if (activityUser.get("is_default_speaker") != null){
                        map.put("isDefaultSpeaker", Integer.parseInt(activityUser.get("is_default_speaker").toString()) != 0);
                    }else {
                        map.put("isDefaultSpeaker", false);
                    }
                    map.put("avatarBgColor",activityUser.get("avatar_bg_color"));
                    if(activityUser.get("profile_image") != null){
                        map.put("profileImage",activityUser.get("profile_image"));
                    }
                }
            });
        }

        return dataList;
    }


    public Map<String,Object> interactiveDocumentTransfMap(InteractiveDocument  doc){
        Map<String, Object> map = new HashMap<>();
        map.put("id", doc.getId().toString());
        map.put("objectId", doc.getObjectId());
        map.put("objectApiName", doc.getObjectApiName());
        map.put("originalUserName", doc.getOriginalUserName());
        map.put("userName", doc.getUserName());
        map.put("userApiName", doc.getUserApiName());
        map.put("nameAvaId", doc.getNameAvaId());
        map.put("userId", doc.getUserId());
        map.put("seq", doc.getSeq());
        map.put("tenantId", doc.getTenantId());
        map.put("createTime", doc.getCreateTime());
        map.put("startTime", doc.getStartTime());
        map.put("endTime", doc.getEndTime());
        map.put("content", doc.getContent());
        map.put("translateContent", doc.getTranslateContent());
        map.put("speakTime", doc.getSpeakTime());
        map.put("activityUserId", doc.getActivityUserId());
        map.put("oldContent", doc.getOldContent());
        map.put("oldTranslateContent", doc.getOldTranslateContent());
        return map;
    }
}
