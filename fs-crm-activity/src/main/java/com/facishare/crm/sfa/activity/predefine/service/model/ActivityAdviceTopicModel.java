package com.facishare.crm.sfa.activity.predefine.service.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson2.JSONObject;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface ActivityAdviceTopicModel {
    @Data
    class Arg {
        String id;
        @JSONField(name = "active_record_id")
        @JsonProperty("active_record_id")
        String activeRecordId;
        @J<PERSON>NField(name = "account_id")
        @JsonProperty("account_id")
        String accountId;
        @JSONField(name = "new_opportunity_id")
        @JsonProperty("new_opportunity_id")
        String opportunityId;
        @JSONField(name = "leads_id")
        @JsonProperty("leads_id")
        String leadsId;
        @<PERSON><PERSON>NField(name = "contact_id")
        @JsonProperty("contact_id")
        String contactId;
        @JSONField(name = "source_api_name")
        @JsonProperty("source_api_name")
        String sourceApiName;
        @JSONField(name = "question_id")
        @JsonProperty("question_id")
        String questionId;
        UpdateData data;
    }

    @Data
    class UpdateData {
        String answer;
    }

    @Data
    class Result {
        Map<String, JSONObject> statistic;
        List<ObjectDataDocument> data;
        List<String> tagsList;
        List<Map<String, String>> objectsList;
    }
}
