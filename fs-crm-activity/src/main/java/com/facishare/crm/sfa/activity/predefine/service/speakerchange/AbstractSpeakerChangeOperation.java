package com.facishare.crm.sfa.activity.predefine.service.speakerchange;

import com.facishare.crm.sfa.activity.predefine.service.ActivityUserService;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 抽象基类，用于发言人变更操作策略
 */
@Slf4j
@Component 
public abstract class AbstractSpeakerChangeOperation implements SpeakerChangeOperation {

    @Autowired
    protected ActivityUserService userService;

    @Autowired
    protected ActivityMongoDao activityMongoDao;

    @Autowired
    protected ServiceFacade serviceFacade;

    @Autowired
    protected IObjectDataService objectDataService;


    public IObjectData findTargetActivityUser(List<IObjectData> activityUsers, String targetActivityUserId, String targetUserId, String targetUserName) {
        return findActivityUser(activityUsers, targetActivityUserId, targetUserId, targetUserName);
    }

    /**
     * 查找活动用户。
     * 优先使用 activityUserId 查找，其次使用 userId 查找，如果提供了 userName，则同时匹配 userId 和 userName。
     *
     * @param activityUsers        用户列表
     * @param activityUserId       活动用户ID (可选)
     * @param userId               用户ID (可选, 如果 activityUserId 未提供则需要)
     * @param userName             用户名 (可选, 如果提供则用于更精确匹配)
     * @return 找到的 IObjectData，未找到则返回 null
     */
    public IObjectData findActivityUser(List<IObjectData> activityUsers, String activityUserId, String userId, String userName) {
        if (CollectionUtils.isEmpty(activityUsers)) {
            return null;
        }

        // 优先通过 activityUserId 查找
        if (StringUtils.isNotEmpty(activityUserId)) {
            String finalActivityUserId = activityUserId;
            return activityUsers.stream()
                    .filter(user -> user != null && finalActivityUserId.equals(user.getId()))
                    .findFirst()
                    .orElse(null);
        }

        // 如果 activityUserId 未提供或未找到，尝试通过 userId (和 userName) 查找
        if (StringUtils.isNotEmpty(userId)) {
            String finalUserId = userId;
            // 如果提供了 userName，精确匹配
            if (StringUtils.isNotEmpty(userName)) {
                String finalUserName = userName;
                return activityUsers.stream()
                        .filter(user -> user != null && finalUserId.equals(user.get("user_id")) && finalUserName.equals(user.get("user_name")))
                        .findFirst()
                        .orElseGet(() -> // 如果精确匹配失败，只用 userId 再次尝试
                                activityUsers.stream()
                                        .filter(user -> user != null && finalUserId.equals(user.get("user_id")))
                                        .findFirst()
                                        .orElse(null)
                        );
            } else {
                // 只提供了 userId
                return activityUsers.stream()
                        .filter(user -> user != null && finalUserId.equals(user.get("user_id")))
                        .findFirst()
                        .orElse(null);
            }
        }

        // 如果两者都未提供，无法查找
        log.warn("Cannot find activity user without activityUserId or userId.");
        return null;
    }

     /**
     * Utility method to calculate avatar background color. Delegates to userService.
     * Subclasses can use this directly.
     */
    protected String calAvatarBgColor(Integer nameAvaId) {
         // 修改为使用计算逻辑而不是直接调用userService的方法，避免访问权限问题
         if (nameAvaId == null || nameAvaId <= 0) {
             return userService.getAvatarBgColors().get(0);
         }
         int index = nameAvaId % userService.getAvatarBgColors().size();
         return userService.getAvatarBgColors().get(index);
    }

     /**
     * Utility method to populate target user fields in an IObjectData. Delegates to userService.
     * Subclasses can use this directly.
     */
    protected void populateTargetUserFields(String tenantId, IObjectData newData, String targetUserApiName, String targetUserId) {
        userService.populateTargetUserFields(tenantId, newData, targetUserApiName, targetUserId);
    }

    // The main execute method remains abstract, forcing subclasses to implement specific logic.
    // public abstract void execute(ServiceContext context, ActivityText.BatchChangeSpeaker batchChangeSpeaker, List<IObjectData> activityUsers, ActivityUserService userService);
    // Since the interface already defines execute, we don't need to declare it abstract here unless we want to add common pre/post logic.
    // Let's rely on the interface contract.

} 