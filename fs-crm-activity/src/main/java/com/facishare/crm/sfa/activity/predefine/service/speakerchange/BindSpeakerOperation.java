package com.facishare.crm.sfa.activity.predefine.service.speakerchange;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.activity.predefine.service.ActivityAttendeesInsightService;
import com.facishare.crm.sfa.activity.predefine.service.model.ActivityText;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.util.Safes;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 处理绑定(BIND)发言人操作的策略。
 * BIND 操作通常是将一个已存在的发言人（目标发言人）绑定到指定的文档上。
 */
@Slf4j
@Component
public class BindSpeakerOperation extends AbstractSpeakerChangeOperation {

    @Autowired
    private ActivityAttendeesInsightService activityAttendeesInsightService;

    @Override
    public void execute(ServiceContext context, ActivityText.BatchChangeSpeaker batchChangeSpeaker, List<IObjectData> activityUsers) {

        if (batchChangeSpeaker.getIsGlobal()) {
            handleGlobalBind(context, batchChangeSpeaker, activityUsers);
            log.warn("BIND operation requested with global flag, but BIND is typically per-document. Processing per document info.");
        }else{
            for (ActivityText.SpeakerReplaceInfo replaceInfo : batchChangeSpeaker.getReplaceInfos()) {
                handleSingleBind(context, replaceInfo, activityUsers);
            }
        }
        try {
            String activeRecordId = Safes.first(activityUsers).get("active_record_id", String.class);
            activityAttendeesInsightService.tryTriggerInsight(context.getTenantId(), activeRecordId);
        } catch (Exception e) {
            log.error("Error triggering insight after BIND operation", e);
        }
    }


    private void handleSingleBind(ServiceContext context, ActivityText.SpeakerReplaceInfo replaceInfo, List<IObjectData> activityUsers) {
        String docId = replaceInfo.getDocId();
        String targetActivityUserId = replaceInfo.getTargetActivityUserId();
        String targetUserId = replaceInfo.getTargetUserId();
        String activityUserId = replaceInfo.getActivityUserId();
        String userId = replaceInfo.getUserId();
        String userName = replaceInfo.getUserName();
        String targetUserApiName = replaceInfo.getTargetUserApiName();
        String targetUserName = replaceInfo.getTargetUserName();
        Integer targetNameAvaId = replaceInfo.getTargetNameAvaId();

        if (StringUtils.isEmpty(docId)) {
            log.warn("[BIND] Skipping BIND operation because docId is missing in replaceInfo: {}", replaceInfo);
            return;
        }

        // For BIND, the target speaker MUST exist. We find them using targetActivityUserId or targetUserId.
        IObjectData targetActivityUser = findTargetActivityUser(activityUsers, targetActivityUserId, targetUserId, null);
        IObjectData activityUser = findActivityUser(activityUsers, activityUserId, userId, userName);

        
        if (targetActivityUser == null) {
            if (activityUser == null) {
                 log.warn("Cannot find original activity user to copy from. activityUserId={}, userId={}, userName={}", activityUserId, userId, userName);
                 return;
            }
            List<IObjectData> data = ObjectDataExt.copyList(Lists.newArrayList(activityUser));
            IObjectData newData = data.get(0);
            newData.setId(serviceFacade.generateId());
            newData.set("user_api_name", targetUserApiName);
            newData.set("user_id", targetUserId);
            newData.set("user_name", targetUserName);
            newData.set("name", targetUserName);
            newData.set("last_modified_by", Lists.newArrayList(context.getUser().getUserId()));
            newData.set("last_modified_time", System.currentTimeMillis());
            newData.set("name_ava_id", targetNameAvaId);
            newData.set("avatar_bg_color", calAvatarBgColor(targetNameAvaId));
            newData.set("is_default_speaker", 0);
            populateTargetUserFields(context.getTenantId(), newData, targetUserApiName, targetUserId);
            IObjectData result = serviceFacade.saveObjectData(context.getUser(), newData);
            targetActivityUserId = result.getId();
            log.info("[EDIT-Single] Created new target activity user: {} ({}) for single change.", targetUserName, targetActivityUserId);
        }else {
            targetActivityUserId = targetActivityUser.getId();
        }
        log.info("[BIND] Binding docId '{}' to targetActivityUserId: {}", docId, targetActivityUserId);
        activityMongoDao.replaceSingleById(context.getTenantId(), docId, targetActivityUserId);
    }


    private void handleGlobalBind(ServiceContext context, ActivityText.BatchChangeSpeaker batchChangeSpeaker, List<IObjectData> activityUsers) {
        List<IObjectData> updateDatas = new ArrayList<>();
        List<IObjectData> usersToDelete = new ArrayList<>();

        List<String> updateFields = Lists.newArrayList("user_api_name", "user_id", "user_name", "name",
                "personnel_id", "contact_id", "public_employee_id",
                "last_modified_by", "last_modified_time", "is_default_speaker", "name_ava_id", "avatar_bg_color");

        for (ActivityText.SpeakerReplaceInfo replaceInfo : batchChangeSpeaker.getReplaceInfos()) {
            String activityUserId = replaceInfo.getActivityUserId();
            String targetUserApiName = replaceInfo.getTargetUserApiName();
            String targetUserId = replaceInfo.getTargetUserId();
            String targetUserName = replaceInfo.getTargetUserName();
            String targetActivityUserId = replaceInfo.getTargetActivityUserId();
            String userId = replaceInfo.getUserId();
            String userName = replaceInfo.getUserName();
            Integer targetNameAvaId = replaceInfo.getTargetNameAvaId();

            IObjectData targetActivityUser = findTargetActivityUser(activityUsers, targetActivityUserId, targetUserId, targetUserName);
            IObjectData activityUser = findActivityUser(activityUsers, activityUserId, userId, userName);

            if (activityUser == null) {
                log.warn("Cannot find activity user for global change. activityUserId={}, userId={}, userName={}", activityUserId, userId, userName);
                continue;
            }
            String originalActivityUserId = activityUser.getId();

            if (targetActivityUser == null) {
                log.info("[EDIT-Global] Updating existing activity user {} globally.", originalActivityUserId);
                IObjectData dataToUpdate = ObjectDataExt.copyList(Lists.newArrayList(activityUser)).get(0);
                dataToUpdate.setId(originalActivityUserId);
                dataToUpdate.set("user_api_name", targetUserApiName);
                dataToUpdate.set("user_id", targetUserId);
                dataToUpdate.set("user_name", targetUserName);
                dataToUpdate.set("name", targetUserName);
                populateTargetUserFields(context.getTenantId(), dataToUpdate, targetUserApiName, targetUserId);
                dataToUpdate.set("last_modified_by", Lists.newArrayList(context.getUser().getUserId()));
                dataToUpdate.set("last_modified_time", System.currentTimeMillis());
                dataToUpdate.set("is_default_speaker", 0);
                dataToUpdate.set("name_ava_id", targetNameAvaId);
                dataToUpdate.set("avatar_bg_color", calAvatarBgColor(targetNameAvaId));
                updateDatas.add(dataToUpdate);
            } else {
                targetActivityUserId = targetActivityUser.getId();
                log.info("[EDIT-Global] Replacing speaker {} with existing target {}. Marking original for deletion.", originalActivityUserId, targetActivityUserId);
                activityMongoDao.batchReplaceSpeaker(context.getTenantId(), batchChangeSpeaker.getObjectId(), originalActivityUserId, targetActivityUserId);
                if (!originalActivityUserId.equals(targetActivityUserId)) {
                     usersToDelete.add(activityUser);
                }
            }
        }

        if (!updateDatas.isEmpty()) {
            log.info("[EDIT-Global] Batch updating {} activity users globally.", updateDatas.size());
            userService.updateActivityUser(context, updateDatas, updateFields);
        }

         if (!usersToDelete.isEmpty()){
            log.warn("Need to implement deletion for users: {}", usersToDelete);
            userService.deleteActivityUser(context, usersToDelete);
         }
    }
} 