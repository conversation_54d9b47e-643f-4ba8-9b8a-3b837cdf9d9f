package com.facishare.crm.sfa.activity.predefine.service.speakerchange;

import com.facishare.crm.sfa.activity.predefine.service.model.ActivityText;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 处理编辑(EDIT)发言人操作的策略
 */
@Slf4j
@Component
public class EditSpeakerOperation extends AbstractSpeakerChangeOperation {

    @Override
    public void execute(ServiceContext context, ActivityText.BatchChangeSpeaker batchChangeSpeaker, List<IObjectData> activityUsers) {
        if (batchChangeSpeaker.getIsGlobal()) {
            handleGlobalChange(context, batchChangeSpeaker, activityUsers);
        } else {
            handleSingleChange(context, batchChangeSpeaker, activityUsers);
        }
    }

    // 编辑操作，只判断 user_name ,如果相同则认为是同一个人。
    @Override
    public IObjectData findTargetActivityUser(List<IObjectData> activityUsers, String targetActivityUserId, String targetUserId, String targetUserName) {
        String finalUserName = targetUserName;
        IObjectData objectData = null;
        if (StringUtils.isNotEmpty(finalUserName)) {
            objectData =  activityUsers.stream()
                        .filter(user -> user != null  && finalUserName.equals(user.get("user_name")))
                        .findFirst()
                        .orElse(null);
        }
        return objectData;
    }

    @Override
    public IObjectData findActivityUser(List<IObjectData> activityUsers, String activityUserId, String userId, String userName) {
        return findTargetActivityUser(activityUsers, "", userId, userName);
    }

    private void handleSingleChange(ServiceContext context, ActivityText.BatchChangeSpeaker batchChangeSpeaker, List<IObjectData> activityUsers) {
        for (ActivityText.SpeakerReplaceInfo replaceInfo : batchChangeSpeaker.getReplaceInfos()) {
            String activityUserId = replaceInfo.getActivityUserId();
            String targetUserApiName = replaceInfo.getTargetUserApiName();
            String targetUserId = replaceInfo.getTargetUserId();
            String targetUserName = replaceInfo.getTargetUserName();
            String targetActivityUserId = replaceInfo.getTargetActivityUserId();
            String userId = replaceInfo.getUserId();
            String userName = replaceInfo.getUserName();
            Integer targetNameAvaId = replaceInfo.getTargetNameAvaId();

            IObjectData targetActivityUser = findTargetActivityUser(activityUsers, targetActivityUserId, targetUserId, null);
            IObjectData activityUser = findActivityUser(activityUsers, activityUserId, userId, userName);

            if (targetActivityUser == null) {
                if (activityUser == null) {
                     log.warn("Cannot find original activity user to copy from. activityUserId={}, userId={}, userName={}", activityUserId, userId, userName);
                     continue;
                }
                List<IObjectData> data = ObjectDataExt.copyList(Lists.newArrayList(activityUser));
                IObjectData newData = data.get(0);
                newData.setId(serviceFacade.generateId());
                newData.set("user_api_name", targetUserApiName);
                newData.set("user_id", targetUserId);
                newData.set("user_name", targetUserName);
                newData.set("name", targetUserName);
                newData.set("last_modified_by", Lists.newArrayList(context.getUser().getUserId()));
                newData.set("last_modified_time", System.currentTimeMillis());
                newData.set("name_ava_id", targetNameAvaId);
                newData.set("avatar_bg_color", calAvatarBgColor(targetNameAvaId));
                newData.set("is_default_speaker", 0);
                populateTargetUserFields(context.getTenantId(), newData, targetUserApiName, targetUserId);
                IObjectData result = serviceFacade.saveObjectData(context.getUser(), newData);
                targetActivityUserId = result.getId();
                log.info("[EDIT-Single] Created new target activity user: {} ({}) for single change.", targetUserName, targetActivityUserId);
            } else {
                targetActivityUserId = targetActivityUser.getId();
            }
            log.info("[EDIT-Single] Replacing doc '{}' speaker with targetActivityUserId: {}", replaceInfo.getDocId(), targetActivityUserId);
            activityMongoDao.replaceSingleById(context.getTenantId(), replaceInfo.getDocId(), targetActivityUserId);
        }
    }

    private void handleGlobalChange(ServiceContext context, ActivityText.BatchChangeSpeaker batchChangeSpeaker, List<IObjectData> activityUsers) {
        List<IObjectData> updateDatas = new ArrayList<>();
        List<IObjectData> usersToDelete = new ArrayList<>();

        List<String> updateFields = Lists.newArrayList("user_api_name", "user_id", "user_name", "name",
                "personnel_id", "contact_id", "public_employee_id",
                "last_modified_by", "last_modified_time", "is_default_speaker", "name_ava_id", "avatar_bg_color");

        for (ActivityText.SpeakerReplaceInfo replaceInfo : batchChangeSpeaker.getReplaceInfos()) {
            String activityUserId = replaceInfo.getActivityUserId();
            String targetUserApiName = replaceInfo.getTargetUserApiName();
            String targetUserId = replaceInfo.getTargetUserId();
            String targetUserName = replaceInfo.getTargetUserName();
            String targetActivityUserId = replaceInfo.getTargetActivityUserId();
            String userId = replaceInfo.getUserId();
            String userName = replaceInfo.getUserName();
            Integer targetNameAvaId = replaceInfo.getTargetNameAvaId();

            IObjectData targetActivityUser = findTargetActivityUser(activityUsers, targetActivityUserId, targetUserId, targetUserName);
            IObjectData activityUser = findActivityUser(activityUsers, activityUserId, userId, userName);

            if (activityUser == null) {
                log.warn("Cannot find activity user for global change. activityUserId={}, userId={}, userName={}", activityUserId, userId, userName);
                continue;
            }
            String originalActivityUserId = activityUser.getId();

            if (targetActivityUser == null) {
                log.info("[EDIT-Global] Updating existing activity user {} globally.", originalActivityUserId);
                IObjectData dataToUpdate = ObjectDataExt.copyList(Lists.newArrayList(activityUser)).get(0);
                dataToUpdate.setId(originalActivityUserId);
                dataToUpdate.set("user_api_name", targetUserApiName);
                dataToUpdate.set("user_id", targetUserId);
                dataToUpdate.set("user_name", targetUserName);
                dataToUpdate.set("name", targetUserName);
                populateTargetUserFields(context.getTenantId(), dataToUpdate, targetUserApiName, targetUserId);
                dataToUpdate.set("last_modified_by", Lists.newArrayList(context.getUser().getUserId()));
                dataToUpdate.set("last_modified_time", System.currentTimeMillis());
                dataToUpdate.set("is_default_speaker", 0);
                dataToUpdate.set("name_ava_id", targetNameAvaId);
                dataToUpdate.set("avatar_bg_color", calAvatarBgColor(targetNameAvaId));
                updateDatas.add(dataToUpdate);
            } else {
                targetActivityUserId = targetActivityUser.getId();
                log.info("[EDIT-Global] Replacing speaker {} with existing target {}. Marking original for deletion.", originalActivityUserId, targetActivityUserId);
                activityMongoDao.batchReplaceSpeaker(context.getTenantId(), batchChangeSpeaker.getObjectId(), originalActivityUserId, targetActivityUserId);
                if (!originalActivityUserId.equals(targetActivityUserId)) {
                     usersToDelete.add(activityUser);
                }
            }
        }

        if (!updateDatas.isEmpty()) {
            log.info("[EDIT-Global] Batch updating {} activity users globally.", updateDatas.size());
            userService.updateActivityUser(context, updateDatas, updateFields);
        }

         if (!usersToDelete.isEmpty()){
            log.warn("Need to implement deletion for users: {}", usersToDelete);
            userService.deleteActivityUser(context, usersToDelete);
         }
    }


} 