package com.facishare.crm.sfa.activity.proxy;

import com.facishare.crm.sfa.activity.predefine.service.model.ActivityMeetingModel;
import com.facishare.crm.util.RestUtils;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.HashMap;
import java.util.Map;

@RestResource(value = "FS-CRM-MEETING", desc = "Meeting", contentType = "application/json")
public interface MeetingProxy {

    @POST(value = "/activity_meeting/create_meeting", desc = "创建会议")
    ActivityMeetingModel.Result<String> createMeeting(@Body ActivityMeetingModel.CreateMeetingArg arg, @HeaderMap Map<String, String> headers);
    @POST(value = "/activity_meeting/create_by_schedule", desc = "创建会议")
    ActivityMeetingModel.Result<String> createBySchedule(@Body ActivityMeetingModel.CreateMeetingArg arg, @HeaderMap Map<String, String> headers);
    @POST(value = "/activity_meeting/get_meeting_url", desc = "获取入会url")
    ActivityMeetingModel.Result<String> getMeetingUrl(@Body ActivityMeetingModel.GetUrlArg arg, @HeaderMap Map<String, String> headers);
    @POST(value = "/activity_meeting/get_active_record", desc = "通过会议id获取销售记录")
    ActivityMeetingModel.Result<String> getActiveRecord(@Body ActivityMeetingModel.GetUrlArg arg, @HeaderMap Map<String, String> headers);
    @POST(value = "/activity_meeting/is_open_transfer", desc = "是否开启会议转写")
    ActivityMeetingModel.Result<Boolean> isOpenTransfer(@Body ActivityMeetingModel.GetUrlArg arg, @HeaderMap Map<String, String> headers);
    @POST(value = "/activity_meeting/get_api_sign", desc = "获取jsApi签名信息")
    ActivityMeetingModel.Result<Map> getApiSign(@Body Map<String, String> arg, @HeaderMap Map<String, String> headers);

    static Map<String, String> getHeaders(ServiceContext context) {
        User user = context.getUser();
        Map<String, String> headers = new HashMap<>();
        headers.put("x-fs-ei", user.getTenantId());
        headers.put("x-fs-userInfo", user.getUserIdOrOutUserIdIfOutUser());
        headers.put("Expect", "100-continue");
        headers.put("X-fs-Trace-Color", "1");
        return headers;
    }

}
