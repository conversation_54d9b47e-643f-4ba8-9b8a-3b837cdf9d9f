package com.facishare.crm.constants;

/**
 * <AUTHOR>
 * @time 2023-12-05 10:15
 * @Description
 */
public interface PrmI18NConstants {
    /**
     * 通知类型转换错误
     */
    String PRM_NOTIFICATION_TYPE_CONVERT_ERROR = "prm.notification.type.convert.error";
    /**
     * 通知方式转换错误
     */
    String PRM_NOTIFICATION_TYPES_CONVERT_ERROR = "prm.notification.types.convert.error";
    /**
     * 短信类型转换错误
     */
    String PRM_SMS_TYPE_CONVERT_ERROR = "prm.sms.type.convert.error";
    /**
     * 存在多个同一配置类型的配置数据
     */
    String PRM_EXISTS_MORE_CONFIG_DATA_ERROR = "prm.exists.more.config.data.error";
    /**
     * 通知范围正在计算中，请稍后再更改通知范围。
     */
    String PRM_NOTICE_RANGE_IN_CALCULATION = "prm.notice.range.in.calculation";

    /**
     * 参数：{0} 协议错误
     */
    String PRM_PARAM_PROTOCOL_ERROR = "prm.param.protocol.error";
    /**
     * 条件不可为空
     */
    String PRM_CONDITION_NOT_EMPTY = "prm.condition.not.empty";

    /**
     * 配置数量已超出最大值，最大数量：{0} 条
     */
    String PRM_CONFIG_LIMIT = "prm.config.limit";


    /**
     * 存在 {0} 的提醒配置数据，但是参数 approvalNoticeId 为空。
     */
    String PRM_APPROVAL_NOTICE_ID_EMPTY_ERROR = "prm.approval.notice.id.empty.error";

    /**
     * 数据已被删除
     */
    String PRM_DATA_HAS_BEEN_DELETED = "prm.data.has.been.deleted";

    /**
     * 该业务场景不允许此操作
     */
    String PRM_CHANNEL_UNAUTHORIZED_OPERATION = "prm.channel.unauthorized.operation";
    /**
     * 根据更新数据的 id 查找不到对应的数据
     */
    String PRM_CHANNEL_CAN_NOT_FIND_DATA_BY_ID = "prm.channel.can.not.find.by.id";

    /**
     * 同一规定方案配置下，政策详情不可重复
     */
    String PRM_CHANNEL_PROVISION_UN_ALLOW_REPEAT = "prm.channel.provision.un.allow.repeat";
    /**
     * 根据:{0} 未查到具体数据，数据已作废或删除。
     */
    String PRM_CHANNEL_DATA_NOT_EXISTS = "prm.channel.data.not.exists";

    /**
     * 已被推送的签约方案配置无法被编辑
     */
    String PRM_CHANNEL_SIGN_SCHEME_PUSHED_CAN_NOT_EDIT = "prm.channel.sign.scheme.pushed.can.not.edit";
    /**
     * 时间必须大于等于 0。
     */
    String PRM_CHANNEL_TIME_MUST_GREATER_THAN_ZERO = "prm.channel.time.must.greater.than.zero";

    /**
     * 时间必须大于 0。
     */
    String PRM_CHANNEL_TIME_MUST_GREATER_THAN_ZERO_ERROR = "prm.channel.time.must.greater.than.zero.error";
    /**
     * 提醒时机的时间必须小于签约周期
     */
    String PRM_CHANNEL_NOTICE_TIME_MUST_LESS_THAN_SIGN_PERIOD = "prm.channel.notice.time.must.less.than.sign.period";
    /**
     * 已被推送的签约方案无法重复被推送。
     */
    String PRM_CHANNEL_SIGN_SCHEME_PUSHED_CAN_NOT_PUSH_AGAIN = "prm.channel.sign.scheme.pushed.can.not.push.again";
    /**
     * 根据合作伙伴:{0} 无法匹配到具体的签约方案配置。
     */
    String PRM_CHANNEL_SIGN_SCHEME_NOT_MATCH = "prm.channel.sign.scheme.not.match";
    /**
     * 该合作伙伴无下游企业，无法续签。
     */
    String PRM_CHANNEL_SIGN_SCHEME_NO_DOWNSTREAM_COMPANY = "prm.channel.sign.scheme.no.downstream.company";
    /**
     * 外部企业为空，不可发起签约。
     */
    String PRM_CHANNEL_SIGN_SCHEME_OUTSIDE_COMPANY_EMPTY = "prm.channel.sign.scheme.outside.company.empty";
    /**
     * 该对象不支持导入
     */
    String PRM_CHANNEL_SIGN_SCHEME_NOT_SUPPORT_IMPORT = "prm.channel.sign.scheme.not.support.import";
    /**
     * 首次保存签约方案配置时，提醒数据不正确。
     */
    String PRM_CHANNEL_SIGN_SCHEME_FIRST_SAVE_NOTICE_ERROR = "prm.channel.sign.scheme.first.save.notice.error";
    /**
     * 月份与日期必须同时填写。
     */
    String PRM_CHANNEL_SIGN_SCHEME_MONTH_AND_DAY_MUST_FILL = "prm.channel.sign.scheme.month.and.day.must.fill";
    /**
     * 日期必须大于等于 1 并且小于等于 31
     */
    String PRM_CHANNEL_SIGN_SCHEME_DAY_MUST_GREATER_THAN_ZERO_AND_LESS_THAN_31 = "prm.channel.sign.scheme.day.must.greater.than.zero.and.less.than.31";
    /**
     * 月份必须大于等于 1 并且小于等于 12
     */
    String PRM_CHANNEL_SIGN_SCHEME_MONTH_MUST_GREATER_THAN_ZERO_AND_LESS_THAN_12 = "prm.channel.sign.scheme.month.must.greater.than.zero.and.less.than.12";
    /**
     * 2 月不可设置大于29日的日期
     */
    String PRM_CHANNEL_SIGN_SCHEME_FEB_MUST_LESS_THAN_29 = "prm.channel.sign.scheme.feb.must.less.than.29";
    /**
     * {0} 月不可设置大于 30 日的日期
     */
    String PRM_CHANNEL_SIGN_SCHEME_MONTH_MUST_LESS_THAN_30 = "prm.channel.sign.scheme.month.must.less.than.30";

    /**
     * ${协议标题}${几天}后到期
     */
    String PRM_CHANNEL_VAR_MESSAGE = "prm.channel.variable.message";

    /**
     * 应用内弹窗提醒文案缺少不可删除的变量，请调整文案内容后重新保存。
     */
    String PRM_CHANNEL_PRM_ALERT_MISSING_VAR_SYMBOL = "prm.channel.prm.alert.missing_var_symbol";
    /**
     * 协议明细正在变更协议状态中，剩余数据量:{0}, 请等待变更完成后再进行启用停用操作！
     */
    String PRM_CHANNEL_PROTOCOL_DETAIL_IN_CHANGE_STATUS = "prm.channel.protocol.detail.in.change.status";
    /**
     * 发起签约
     */
    String PRM_CHANNEL_INITIATE_RENEWAL_ACTION = "prm.channel.initiate.renewal.action";
    /**
     * 协议状态为非激活状态，不可发起签约。
     */
    String PRM_CHANNEL_AGREEMENT_STATUS_NOT_EQ_ACTIVATE = "prm.channel.agreement.status.not.eq.activate";

}
