package com.facishare.crm.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.Getter;

import static com.facishare.crm.constants.PrmI18NConstants.PRM_PARAM_PROTOCOL_ERROR;

/**
 * <AUTHOR>
 * @time 2024-06-26 10:24
 * @Description
 */
@Getter
public enum ConditionTypeEnums {
    /**
     * 全部
     */
    ALL("ALL"),
    /**
     * 按条件
     */
    CONDITION("CONDITION"),
    /**
     * APL 函数
     */
    APL("APL");

    private final String type;

    ConditionTypeEnums(String type) {
        this.type = type;
    }

    public static ConditionTypeEnums fromString(String type) {
        for (ConditionTypeEnums e : values()) {
            if (e.type.equals(type)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "conditionType"));
    }
}
