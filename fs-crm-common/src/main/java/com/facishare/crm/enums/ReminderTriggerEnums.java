package com.facishare.crm.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.Getter;

import static com.facishare.crm.constants.PrmI18NConstants.PRM_PARAM_PROTOCOL_ERROR;

/**
 * Created by Sundy on 2024/10/9 17:31
 */
@Getter
public enum ReminderTriggerEnums {
    MANUAL("manual"),
    AUTO("auto");
    private final String trigger;

    ReminderTriggerEnums(String trigger) {
        this.trigger = trigger;
    }

    public static ReminderTriggerEnums of(String trigger) {
        for (ReminderTriggerEnums e : values()) {
            if (e.trigger.equals(trigger)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "reminder_trigger"));
    }

    public static ReminderTriggerEnums of(String trigger, ReminderTriggerEnums defaultValue) {
        for (ReminderTriggerEnums e : values()) {
            if (e.trigger.equals(trigger)) {
                return e;
            }
        }
        return defaultValue;
    }
}