package com.facishare.crm.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.Getter;

import static com.facishare.crm.constants.PrmI18NConstants.PRM_PARAM_PROTOCOL_ERROR;

@Getter
public enum ScheduleTypeEnums {
    /**
     * 固定周期签署
     */
    CYCLE("cycle"),
    /**
     * 固定日签署
     */
    FIXED_DATE("fixed_date"),
    /**
     * 一次性签署
     */
    ONE_TIME("one_time");

    private final String type;

    ScheduleTypeEnums(String type) {
        this.type = type;
    }

    public static ScheduleTypeEnums of(String type) {
        for (ScheduleTypeEnums e : values()) {
            if (e.type.equals(type)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "scheduleType"));
    }

    public static ScheduleTypeEnums of(String type, ScheduleTypeEnums defaultValue) {
        for (ScheduleTypeEnums e : values()) {
            if (e.type.equals(type)) {
                return e;
            }
        }
        return defaultValue;
    }
}
