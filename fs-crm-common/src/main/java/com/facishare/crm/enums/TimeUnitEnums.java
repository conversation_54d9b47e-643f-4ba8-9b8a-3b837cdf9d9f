package com.facishare.crm.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.Getter;

import static com.facishare.crm.constants.PrmI18NConstants.PRM_PARAM_PROTOCOL_ERROR;

/**
 * Created by Sundy on 2024/10/9 17:26
 */
@Getter
public enum TimeUnitEnums {
    YEAR("year", "time.unit.year"),
    QUARTER("quarter", "time.unit.quarter"),
    MONTH("month", "time.unit.month"),
    WEEK("week", "time.unit.week"),
    DAY("day", "time.unit.day");

    private final String unit;
    private final String i18n;

    TimeUnitEnums(String unit, String i18n) {
        this.unit = unit;
        this.i18n = i18n;
    }

    public static TimeUnitEnums of(String unit) {
        for (TimeUnitEnums e : values()) {
            if (e.unit.equals(unit)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "unit"));
    }
}
