package com.facishare.crm.model;

import com.facishare.crm.enums.ReminderTriggerEnums;
import com.facishare.crm.platform.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by Sundy on 2024/10/9 18:50
 */
public interface ChannelManagementDTO {
    @Convertible
    @Setter
    @Getter
    @Diff
    class SignScheme {
        @Mappable(fieldApiName = "_id")
        @ExcludeDiff
        private String signSchemeId;
        private String schemeName;
        private String agreementId;
        private String condition;
        private String signMode;
        private String aplApiName;
        private String agreementStartMode;
        private String agreementEndMode;
        private String customStartDate;
        private String customEndDate;
        private List<String> activateRoles;
        @ExcludeDiff
        private List<String> noticeIds;
        private Integer priority;
        private String signTimeUnit;
        private Integer signTime;
        @DefaultValue(value = "cycle")
        private String scheduleType;
        private Integer fixedMonth;
        private Integer fixedDay;
        private List<String> ableEditFields;
        private String reminderTrigger;
        @ExcludeDiff
        private Boolean pushed;
    }

    @Convertible
    @Setter
    @Getter
    @Diff
    class ReminderType {
        @Mappable(fieldApiName = "_id")
        @ExcludeDiff
        private String reminderTypeId;
        @ExcludeDiff
        private String reminderTrigger;
        @ExcludeDiff
        private String signSchemeId;
        @ExcludeDiff
        private String reminderMethod;
        private String timeUnit;
        private Integer reminderTime;
        @DefaultValue(value = "")
        private String templateId;
        @DefaultValue(value = "")
        private String message;
        private Boolean activated;

    }

    @Convertible
    @Setter
    @Getter
    class ReminderPerson {
        @Mappable(fieldApiName = "_id")
        private String reminderPersonId;
        private String identity;
        private String memberType;
        private String signSchemeId;
        private String dataId;
    }

    @Convertible
    @Setter
    @Getter
    class ReminderCache {
        private String outTenantId;
        private String outUserId;
    }

    @Convertible
    @Setter
    @Getter
    class MatchSignSchemeWithPrmReminder {
        private String signSchemeId;
        private ReminderTriggerEnums reminderTrigger;
        private ReminderType prmAlertWindowReminder;
        private List<ReminderPerson> externalPerson;
    }
}


