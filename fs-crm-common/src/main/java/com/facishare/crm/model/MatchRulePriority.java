package com.facishare.crm.model;

import com.facishare.crm.enums.ConditionTypeEnums;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024-07-02 16:37
 * @Description
 */
public interface MatchRulePriority {
    Integer getPriority();

    String getAplApiName();

    ConditionTypeEnums getConditionTypeEnum();

    String getMatch();

    List<String> getMatchList();

    String getMatchSchemeId();
}
