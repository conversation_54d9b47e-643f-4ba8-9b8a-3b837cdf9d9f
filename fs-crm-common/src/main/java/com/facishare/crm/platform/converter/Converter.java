package com.facishare.crm.platform.converter;

import com.facishare.paas.metadata.api.IObjectData;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Created by Sundy on 2024/10/15 11:46
 */
public interface Converter {
    /**
     * 转换任意对象为 DTO
     * 输出对象O（DTO） 必须有 @Convertible 注解
     *
     * @param inputData   任意对象
     * @param outputClazz DTO class
     * @param <I>         输入对象
     * @param <O>         输出对象
     * @return DTO 对象实例
     */
    <I, O> O convertDTO(I inputData, @NotNull Class<O> outputClazz);

    /**
     * 转换任意对象为 DTO
     * 输出对象O（DTO） 必须有 @Convertible 注解
     *
     * @param inputObjectDataList   任意对象
     * @param outputClazz DTO class
     * @param <I>         输入对象
     * @param <O>         输出对象
     * @return DTO 对象实例 列表
     */
    <I, O> List<O> convertDTOList(List<I> inputObjectDataList, @NotNull Class<O> outputClazz);

    /**
     * 转换任意对象为 IObjectData
     * inputObj 必须有 @Convertible 注解
     *
     * @param inputObj 任意对象
     * @param <T>      对象类型
     * @return IObjectData
     */
    <T> IObjectData convertObjectData(@NotNull T inputObj);

    /**
     * IObjectData 对象转换为任意 DTO
     *
     * @param objectData  对象实例
     * @param outputClazz 输出对象（DTO）class
     * @param <T>         对象类型
     * @return DTO
     */
    <T> T convertDTO(@NotNull IObjectData objectData, @NotNull Class<T> outputClazz);

    /**
     * IObjectData 对象 List 转换为任意 DTO List
     *
     * @param objectDataList 对象实例数组
     * @param outputClazz    输出对象（DTO）class
     * @param <T>            对象类型
     * @return DTO List
     */
    <T> List<T> convertInPutDataList(List<IObjectData> objectDataList, @NotNull Class<T> outputClazz);
}
