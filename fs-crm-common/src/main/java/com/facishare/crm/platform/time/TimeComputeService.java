package com.facishare.crm.platform.time;

import com.facishare.crm.enums.TimeUnitEnums;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.google.common.collect.ImmutableSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

import static com.facishare.crm.constants.PrmI18NConstants.PRM_CHANNEL_TIME_MUST_GREATER_THAN_ZERO;

@Service
@Slf4j
public class TimeComputeService {
    private static final Set<Integer> LONG_MONTHS = ImmutableSet.of(1, 3, 5, 7, 8, 10, 12);

    public @NotNull Date timestamp2Date(@NotNull Long timestamp) {
        return new Date(timestamp);
    }

    public @NotNull Long date2timestamp(@NotNull Date date) {
        return date.toInstant().toEpochMilli();
    }

    public boolean isExpired(Long expiredTimestamp) {
        if (expiredTimestamp == null) {
            return false;
        }
        LocalDate currentDate = LocalDate.now();
        LocalDate expiredDate = Instant.ofEpochMilli(expiredTimestamp)
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        return currentDate.isEqual(expiredDate) || currentDate.isAfter(expiredDate);
    }

    public Date getNDaysBefore(Date anchorDate, int timeHours, int nDays) {
        // 将 anchorDate 转换为 LocalDateTime
        LocalDateTime localDateTime = anchorDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        // 减去 N 天，并将时间设置为 timeHours
        localDateTime = localDateTime.minusDays(nDays)
                .withHour(timeHours)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);
        // 将 LocalDateTime 转换回 Date
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public Date getNextNDay(int plusDays, int hours, int minutes) {
        LocalDateTime now = LocalDateTime.now();
        // 获取第二天的日期时间
        LocalDateTime tomorrow = now.plusDays(plusDays).with(LocalTime.of(hours, minutes));
        return Date.from(tomorrow.atZone(ZoneId.systemDefault()).toInstant());
    }

    public int calculateDifference(@NotNull Long endDate, @NotNull TimeUnitEnums timeUnit) {
        LocalDate endLocalDate = Instant.ofEpochMilli(endDate)
                .atZone(ZoneId.systemDefault()) // 根据系统默认时区
                .toLocalDate();
        return (int) calculateDifference(endLocalDate, timeUnit);
    }

    public long calculateDifference(@NotNull LocalDate endDate, @NotNull TimeUnitEnums timeUnit) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 根据不同的单位计算差异
        switch (timeUnit) {
            case YEAR:  // 计算差异的年数
                return ChronoUnit.YEARS.between(currentDate, endDate);
            case MONTH:  // 计算差异的年数
                return ChronoUnit.MONTHS.between(currentDate, endDate);
            case QUARTER:  // 计算差异的周数
                return calculateQuartersBetween(currentDate, endDate);
            case WEEK:  // 计算差异的周
                return ChronoUnit.WEEKS.between(currentDate, endDate);
            case DAY:  // 计算差异的周
                return ChronoUnit.DAYS.between(currentDate, endDate);
            default:
                throw new IllegalArgumentException("Invalid unit. Use 'day', 'week', 'month', 'year', or 'quarter'.");
        }
    }

    /**
     * 计算月份的差异
     *
     * @param currentDate 当前时间
     * @param endDate     结束时间
     * @return 相差季度
     */
    private long calculateQuartersBetween(LocalDate currentDate, @NotNull LocalDate endDate) {
        long monthsBetween = ChronoUnit.MONTHS.between(currentDate, endDate);
        // 一个季度是3个月，因此将月份的差异除以3
        return monthsBetween / 3;
    }

    /**
     * 计算换算为多少天
     *
     * @param time     时间
     * @param timeUnit 时间单位
     * @return
     */
    public int calculateDays(Integer time, TimeUnitEnums timeUnit) {
        if (time == null || time < 0) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_TIME_MUST_GREATER_THAN_ZERO));
        }
        AtomicInteger daysAtomic = new AtomicInteger(0);
        switch (timeUnit) {
            case YEAR:
                daysAtomic.set(time * 365);
                break;
            case MONTH:
                daysAtomic.set(time * 30);
                break;
            case QUARTER:
                daysAtomic.set(time * 90);
                break;
            case WEEK:
                daysAtomic.set(time * 7);
                break;
            case DAY:
                daysAtomic.set(time);
                break;
            default:
                throw new IllegalArgumentException("Invalid unit. Use 'week', 'month', 'year', or 'quarter'.");
        }
        return daysAtomic.get();
    }

    public boolean isMonthWith31(int fixedMonth) {
        return LONG_MONTHS.contains(fixedMonth);
    }
    /**
     * 获取当前 periodKey
     *
     * @return periodKey 字符串
     */
    public String getCurrentPeriodKey(TimeUnitEnums timeUnit) {
        String periodKey;
        switch (timeUnit) {
            case MONTH:
                periodKey = computeCurrentMonthPeriodKey();
                break;
            default:
                throw new IllegalArgumentException("Unsupported time unit: {}" + timeUnit);
        }
        return periodKey;
    }

    /**
     * 获取上个时间 periodKey
     *
     * @return periodKey 字符串
     */
    public String getLastPeriodKey(TimeUnitEnums timeUnit) {
        String lastPeriodKey;
        switch (timeUnit) {
            case MONTH:
                lastPeriodKey = computeLastMonthPeriodKey();
                break;
            default:
                throw new IllegalArgumentException("Unsupported time unit: {}" + timeUnit);
        }
        return lastPeriodKey;
    }

    private String computeCurrentMonthPeriodKey() {
        // 获取当前日期
        LocalDate now = LocalDate.now();
        // 格式化为 "yyyyMM" 格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        return now.format(formatter);
    }

    private String computeLastMonthPeriodKey() {
        // 获取当前日期并减去一个月
        LocalDate lastMonth = LocalDate.now().minusMonths(1);
        // 格式化为 "yyMM" 格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        return lastMonth.format(formatter);
    }
}
