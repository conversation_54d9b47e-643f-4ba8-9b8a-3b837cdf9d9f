package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.TransformData4ViewService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.util.ConcatenateSqlUtils;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService.MODULE_MULTIPLE_UNIT;

@Service
@Slf4j
public class ProductCoreService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private TransformData4ViewService transformData4ViewService;
    @Autowired
    private MultiUnitService multiUnitService;
    @Autowired
    private BizCommonConfigService bizCommonConfigService;
    @Autowired
    private UnitCoreService unitCoreService;
    @Autowired
    private LicenseService licenseService;

    /**
     * 特殊：给订单选择价目表产品时，填充产品数据
     * 给数据填充特殊产品数据
     */
    public void fillDataWithProduct(User user, List<IObjectData> data, boolean isRealLookUp, String masterObjectApiName) {
        List<String> productIdList = data.stream().map(k -> k.get("product_id", String.class)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(data) && CollectionUtils.isNotEmpty(productIdList)) {
            IObjectDescribe productDescribe = serviceFacade.findObject(user.getTenantId(), Utils.PRODUCT_API_NAME);

            List<IObjectData> productList = findProductList(user, productIdList, masterObjectApiName);
            Map<String, String> nonAttributeValues = productList.stream().filter(x->Objects.nonNull(x.get(ProductConstants.NON_ATTRIBUTE_VALUES))).collect(Collectors.toMap(x->x.getId(), x->x.get(ProductConstants.NON_ATTRIBUTE_VALUES, String.class), (v1, v2)->v1));
            transformData4ViewService.batchTransformDataForView(user, productDescribe, productList);

            Map<String, ObjectDataDocument> productMap = productList.stream().collect(Collectors.toMap(DBRecord::getId, ObjectDataDocument::of));
            if (!isRealLookUp) {
                fillSpecAndValue4Skus(user.getTenantId(), productMap);
            }

            data.forEach(k -> {
                ObjectDataDocument product = productMap.get(k.get("product_id", String.class));
                if(nonAttributeValues !=null && nonAttributeValues.size() > 0) {
                    //暂时保留产品上的非标属性值，在PriceBookProductRelatedListController中用完之后会移除
                    product.put(ProductConstants.NON_ATTRIBUTE_VALUES, nonAttributeValues.get(k.get("product_id", String.class)));
                }
                k.set("product_id__ro", product);
            });
        }
    }

    public void fillSpecAndValue4Skus(String tenantId, Map<String, ObjectDataDocument> haveSpecObjectIdDataMapping) {
        if (MapUtils.isNotEmpty(haveSpecObjectIdDataMapping)) {
            String specSpecValueInfoSql = ConcatenateSqlUtils.getSpecSpecValueInfoSql(tenantId, Lists.newArrayList(haveSpecObjectIdDataMapping.keySet()));
            try {
                List<Map> specSpecValueInfo = objectDataService.findBySql(tenantId, specSpecValueInfoSql);

                specSpecValueInfo.sort(Comparator.comparing(o -> {
                    o.putIfAbsent("order_field", "");
                    return Optional.ofNullable(o.get("order_field")).orElse("").toString();
                }));

                specSpecValueInfo.forEach(o -> {
                    ObjectDataDocument skuData = haveSpecObjectIdDataMapping.get(o.get("sku_id").toString());
                    if (skuData != null) {
                        List<Object> specAndSpecValue = (List) skuData.get("spec_and_value");
                        if (specAndSpecValue == null) {
                            skuData.put("spec_and_value", Lists.newArrayList(o));
                        } else {
                            specAndSpecValue.add(o);
                        }
                    }
                });
            } catch (MetadataServiceException e) {
                log.warn("paasFindBySql error. sql {}", specSpecValueInfoSql, e);
                throw new MetaDataBusinessException(e);
            }
        }
    }

    private List<IObjectData> findProductList(User user, List<String> productIdList, String masterObjectApiName) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(productIdList.size());
        SearchUtil.fillFilterIn(searchTemplateQuery.getFilters(), DBRecord.ID, productIdList);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        QueryResult<IObjectData> productResult = serviceFacade.findBySearchQuery(ActionContextExt.of(user).skipRelevantTeam().getContext(), Utils.PRODUCT_API_NAME, searchTemplateQuery);
        if (bizCommonConfigService.isOpen(MODULE_MULTIPLE_UNIT, user)) {
            multiUnitService.handleUnitAndPrice(productResult.getData(), user.getTenantId());
            unitCoreService.handCommonUnitInfo(user.getTenantId()
                    , productResult.getData()
                    , Utils.PRODUCT_API_NAME, masterObjectApiName);
        }
        return productResult.getData();
    }

    public void handleSpuField(User user, List<IObjectData> dataList) {
        //快消企业并且开了商品
        if (!SFAConfigUtil.isSpuOpen(user.getTenantId())) {
            return;
        }
        Set<String> module = licenseService.getModule(user.getTenantId());
        if (!module.contains("kx_peculiarity")) {
            return;
        }
        List<String> productIds = dataList.stream().filter(x->StringUtils.isBlank(x.get(ProductConstants.PRODUCT_SPU_ID, String.class)))
                .map(x -> x.get(ProductConstants.PRODUCT_ID, String.class)).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productIds)) {
            return;
        }
        List<IObjectData> productObjs = serviceFacade.findObjectDataByIdsIncludeDeletedIgnoreAll(user, productIds, Utils.PRODUCT_API_NAME);
        if (CollectionUtils.isEmpty(productObjs)) {
            return;
        }
        Map<String, String> map = productObjs.stream().filter(x-> StringUtils.isNotBlank(x.get(ProductConstants.PRODUCT_SPU_ID,String.class)))
                .collect(Collectors.toMap(DBRecord::getId, x -> x.get(ProductConstants.PRODUCT_SPU_ID, String.class), (v1, v2) -> v1));

        dataList.stream().forEach(x->{
            String productId = x.get(ProductConstants.PRODUCT_ID, String.class);
            if(StringUtils.isNotBlank(productId)&&StringUtils.isNotBlank(map.get(productId))){
                x.set(ProductConstants.PRODUCT_SPU_ID,map.get(productId));
            }
        });
    }

}
