package com.facishare.crm.sfa.predefine.service.attribute;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.attribute.model.Attribute;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeRangeMode;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeValue;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.util.AttributeUtils;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 属性范围服务类
 */
@Service
@Slf4j
public class AttributeRangeServiceImpl implements AttributeRangeService {
    private static final String CATEGORY_API_NAME = "ProductCategoryObj";
    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;


    @Override
    public void batchSaveAttrRange(User user, AttributeRangeMode.Arg arg) {
        if (StringUtils.isAnyBlank(arg.getDataId(), arg.getApiName())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MISS_REQUIRED_PARAMETERS));
        }
        List<IObjectData> attrRangeList = queryAttrRangeList(user, arg.getApiName(), Sets.newHashSet(arg.getDataId()));
        if (CollectionUtils.notEmpty(attrRangeList)) {
            serviceFacade.bulkDeleteWithInternalDescribe(attrRangeList, user);
        }
        if (CollectionUtils.empty(arg.getDataList())) {
            return;
        }
        Set<IObjectData> objectDataList = Sets.newHashSet();
        CollectionUtils.nullToEmpty(arg.getDataList()).forEach(x->{
            CollectionUtils.nullToEmpty(x.getAttrList()).forEach(attr->setData(arg, objectDataList, x, attr, Utils.ATTRIBUTE_OBJ_API_NAME, user));
            CollectionUtils.nullToEmpty(x.getNonAttrList()).forEach(nonAttr->setData(arg, objectDataList, x, nonAttr, Utils.NONSTANDARD_ATTRIBUTE_OBJ_API_NAME, user));
        });
        Lists.partition(Lists.newArrayList(objectDataList), 500).forEach(x -> serviceFacade.bulkSaveObjectData(x, user));
    }

    private void setData(AttributeRangeMode.Arg arg, Set<IObjectData> objectDataList, AttributeRangeMode.RangeEntity x, AttributeRangeMode.AttrEntity attr, String attributeObjApiName, User user) {
        IObjectData objectData = new ObjectData();
        objectData.setId(serviceFacade.generateId());
        objectData.setTenantId(user.getTenantId());
        objectData.setDescribeApiName(AttributeRangeMode.API_NAME);
        objectData.setCreateTime(System.currentTimeMillis());
        objectData.set(AttributeRangeMode.DATA_ID, arg.getDataId());
        objectData.set(AttributeRangeMode.DATA_OBJECT_DESCRIBE_API_NAME, arg.getApiName());
        objectData.set(AttributeRangeMode.ATTR_GROUP_ID, x.getGroupId());
        objectData.set(AttributeRangeMode.ATTR_GROUP_NO, x.getGroupNo());
        objectData.set(AttributeRangeMode.REF_DATA_ID, attr.getId());
        objectData.set(AttributeRangeMode.REF_DATA_NO, attr.getAttrNo());
        objectData.set(AttributeRangeMode.REF_OBJECT_DESCRIBE_API_NAME, attributeObjApiName);
        objectDataList.add(objectData);
    }

    @Override
    public void queryAttrRange(User user, List<IObjectData> dataList, String describeApiName) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        if (!bizConfigThreadLocalCacheService.isOpenNonstandardAttribute(user.getTenantId()) && !bizConfigThreadLocalCacheService.isOpenAttribute(user.getTenantId())) {
            return;
        }
        try {
            Map<String, List<IObjectData>> dataMap = Maps.newHashMap();
            String key ;
            if (StringUtils.equalsAny(describeApiName, Utils.PRODUCT_API_NAME, CATEGORY_API_NAME)) {
                key = IObjectData.ID;
            } else if (StringUtils.equals(describeApiName, Utils.BOM_CORE_API_NAME)) {
                key = BomConstants.FIELD_CORE_ID;
            } else {
                describeApiName = Utils.PRODUCT_API_NAME;
                key = BomConstants.FIELD_PRODUCT_ID;
            }
            for (IObjectData iObjectData : dataList) {
                dataMap.computeIfAbsent(iObjectData.get(key,String.class), k -> new ArrayList<>()).add(iObjectData);
            }
            List<IObjectData> attrRangeList = queryAttrRangeList(user, describeApiName, dataMap.keySet());
            if (CollectionUtils.empty(attrRangeList)) {
                return;
            }
            Set<String> groupIds = Sets.newHashSet();
            Set<String> attrIds = Sets.newHashSet();
            Set<String> nonAttrIds = Sets.newHashSet();
            Map<String, List<IObjectData>> attrRangeMap = Maps.newHashMap();
            attrRangeList.forEach(x -> {
                groupIds.add(x.get(AttributeRangeMode.ATTR_GROUP_ID, String.class));
                if (Objects.equals(x.get(AttributeRangeMode.REF_OBJECT_DESCRIBE_API_NAME, String.class), Utils.ATTRIBUTE_OBJ_API_NAME)) {
                    attrIds.add(x.get(AttributeRangeMode.REF_DATA_ID, String.class));
                } else if (Objects.equals(x.get(AttributeRangeMode.REF_OBJECT_DESCRIBE_API_NAME, String.class), Utils.NONSTANDARD_ATTRIBUTE_OBJ_API_NAME)) {
                    nonAttrIds.add(x.get(AttributeRangeMode.REF_DATA_ID, String.class));
                }
                attrRangeMap.computeIfAbsent(x.get(AttributeRangeMode.DATA_ID, String.class), k -> new ArrayList<>()).add(x);
            });
            Map<String, String> attrMap = Maps.newHashMap();
            Map<String, List<IObjectData>> attrValueMap = Maps.newHashMap();
            Map<String, String> groupMap = Maps.newHashMap();
            Map<String, String> nonAttrMap = Maps.newHashMap();
            if (CollectionUtils.notEmpty(attrIds)) {
                List<IObjectData> attributeValueList = queryAttrValueList(user, attrIds);
                attrValueMap = attributeValueList.stream().collect(Collectors.groupingBy(x -> x.get(AttributeConstants.ValueField.ATTRIBUTE_ID, String.class)));
                List<IObjectData> tmpList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(attrIds), Utils.ATTRIBUTE_OBJ_API_NAME);
                CollectionUtils.nullToEmpty(tmpList).forEach(x->{
                    attrMap.put(x.getId(), AttributeUtils.getI18nName(x));
                }); 
            }
            if (CollectionUtils.notEmpty(groupIds)) {
                groupMap = CollectionUtils.nullToEmpty(serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(groupIds), Utils.ATTRIBUTE_GROUP_OBJ_API_NAME)).stream().collect(Collectors.toMap(IObjectData::getId, AttributeUtils::getI18nName));
            }   
            if (CollectionUtils.notEmpty(nonAttrIds)) {
                nonAttrMap = CollectionUtils.nullToEmpty(serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(nonAttrIds), Utils.NONSTANDARD_ATTRIBUTE_OBJ_API_NAME)).stream().collect(Collectors.toMap(IObjectData::getId, AttributeUtils::getI18nName));
            }
            setAttrRange(dataMap, attrRangeMap, attrMap, attrValueMap, groupMap, nonAttrMap);
        } catch (Exception e) {
            log.warn(e.getMessage(),e);
        }
    }

    private void setAttrRange(Map<String, List<IObjectData>> dataMap, Map<String, List<IObjectData>> attrRangeMap, Map<String, String> attrMap, Map<String, List<IObjectData>> attrValueMap, Map<String, String> groupMap, Map<String, String> nonAttrMap) {
        for (Map.Entry<String, List<IObjectData>> entry : attrRangeMap.entrySet()) {
            List<IObjectData> tmpList = entry.getValue();
            List<AttributeRangeMode.AttributeRange> attributeRanges = Lists.newArrayList();
            List<String> rangeIds = Lists.newArrayList();
            for (IObjectData x : tmpList) {
                attributeRanges.add(AttributeRangeMode.AttributeRange.builder()
                        .id(x.get(AttributeRangeMode.REF_DATA_ID, String.class))
                        .name(getName(x, nonAttrMap, attrMap))
                        .apiName(x.get(AttributeRangeMode.REF_OBJECT_DESCRIBE_API_NAME, String.class))
                        .attrNo(x.get(AttributeRangeMode.REF_DATA_NO, Integer.class))
                        .groupId(x.get(AttributeRangeMode.ATTR_GROUP_ID, String.class))
                        .groupNo(x.get(AttributeRangeMode.ATTR_GROUP_NO, Integer.class))
                        .groupName(groupMap.get(x.get(AttributeRangeMode.ATTR_GROUP_ID, String.class)))
                        .attributeValues(getAttributeValues(attrValueMap.getOrDefault(x.get(AttributeRangeMode.REF_DATA_ID, String.class), Lists.newArrayList())))
                        .build());
                rangeIds.add(x.get(AttributeRangeMode.REF_DATA_ID, String.class));
            }
            List<IObjectData> objectDataList = dataMap.getOrDefault(entry.getKey(), Lists.newArrayList());
            objectDataList.forEach(objectData -> {
                objectData.set(AttributeRangeMode.ATTR_RANGE, attributeRanges);
                objectData.set("rangeIds",rangeIds);
            });
        }
    }

    private List<IObjectData> queryAttrValueList(User user, Set<String> attrIds) {
        SearchTemplateQuery query = getSearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, AttributeConstants.ValueField.ATTRIBUTE_ID, Lists.newArrayList(attrIds));
        query.setFilters(filters);
        return findBySearchQueryIgnoreAll(user, Utils.ATTRIBUTE_VALUE_OBJ_API_NAME, query);
    }

    private List<IObjectData> queryAttrRangeList(User user, String describeApiName, Set<String> dataIds) {
        SearchTemplateQuery query = getSearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, AttributeRangeMode.DATA_ID, dataIds);
        SearchUtil.fillFilterEq(filters, AttributeRangeMode.DATA_OBJECT_DESCRIBE_API_NAME, describeApiName);
        query.setFilters(filters);
        return findBySearchQueryIgnoreAll(user, AttributeRangeMode.API_NAME, query);
    }

    private SearchTemplateQuery getSearchTemplateQuery() {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setNeedReturnCountNum(false);
        query.setPermissionType(0);
        query.setOffset(0);
        query.setLimit(2000);
        return query;
    }

    private String getName(IObjectData objectData, Map<String, String> nonAttrMap, Map<String, String> attrMap) {
        return Objects.equals(objectData.get(AttributeRangeMode.REF_OBJECT_DESCRIBE_API_NAME), Utils.ATTRIBUTE_OBJ_API_NAME) ?
                attrMap.get(objectData.get(AttributeRangeMode.REF_DATA_ID, String.class)) : nonAttrMap.get(objectData.get(AttributeRangeMode.REF_DATA_ID, String.class));
    }


    private List<AttributeValue> getAttributeValues(List<IObjectData> objectDataList) {
        return objectDataList.stream().map(objectData -> AttributeValue.builder()
                .id(objectData.getId())
                .name(AttributeUtils.getI18nName(objectData))
                .code(AttributeUtils.getI18nCode(objectData))
                .attributeId(objectData.get(AttributeConstants.ValueField.ATTRIBUTE_ID, String.class))
                .order_field(objectData.get(AttributeConstants.ValueField.ORDER_FIELD, Integer.class))
                .is_default(objectData.get(AttributeConstants.ValueField.IS_DEFAULT, String.class))
                .build())
                .collect(Collectors.toList());
    }

    private List<IObjectData> findBySearchQueryIgnoreAll(User user, String objectApiName, SearchTemplateQuery query) {
        List<IObjectData> result = Lists.newArrayList();
        // 每次查询个数
        int limitSize = 2000;
        query.setLimit(limitSize);
        // 当前循环次数
        int loop = 0;
        int queryDataSize;
        // 最多循环10次
        do {
            query.setOffset(loop++ * limitSize);
            List<IObjectData> dataList = serviceFacade.findBySearchQueryIgnoreAll(user, objectApiName, query).getData();
            queryDataSize = dataList.size();
            result.addAll(dataList);
        } while (queryDataSize >= limitSize && loop < 10);
        return result;
    }

    @Override
    public void deleteAttrRangeRelation(User user,List<String> productIds, List<String> attrIds, String apiName) {
        if (CollectionUtils.empty(productIds)||CollectionUtils.empty(attrIds)) {
            return;
        }
        SearchTemplateQuery query = getSearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, AttributeRangeMode.DATA_ID, productIds);
        SearchUtil.fillFilterEq(filters, AttributeRangeMode.DATA_OBJECT_DESCRIBE_API_NAME, Utils.PRODUCT_API_NAME);
        SearchUtil.fillFilterIn(filters, AttributeRangeMode.REF_DATA_ID, attrIds);
        SearchUtil.fillFilterEq(filters, AttributeRangeMode.REF_OBJECT_DESCRIBE_API_NAME, apiName);
        SearchUtil.fillFilterEq(filters, "is_deleted", "0");
        query.setFilters(filters);
        List<IObjectData> attrRangeList = findBySearchQueryIgnoreAll(user, AttributeRangeMode.API_NAME, query);
        if (CollectionUtils.notEmpty(attrRangeList)) {
            serviceFacade.bulkDeleteWithInternalDescribe(attrRangeList, user);
        }
        if(SFAConfigUtil.enableBom(user.getTenantId())){
            query = getSearchTemplateQuery();
            filters = Lists.newArrayList();
            SearchUtil.fillFilterIn(filters, BomConstants.FIELD_PRODUCT_ID, productIds);
            SearchUtil.fillFilterIsNotNull(filters, BomConstants.FIELD_CORE_ID);
            query.setFilters(filters);
            List<IObjectData> bomList = findBySearchQueryIgnoreAll(user, Utils.BOM_API_NAME, query);
            if (CollectionUtils.notEmpty(bomList)) {
                query = getSearchTemplateQuery();
                filters = Lists.newArrayList();
                SearchUtil.fillFilterIn(filters, AttributeRangeMode.DATA_ID, bomList.stream().map(x->x.get(BomConstants.FIELD_CORE_ID,String.class)).collect(Collectors.toList()));
                SearchUtil.fillFilterEq(filters, AttributeRangeMode.DATA_OBJECT_DESCRIBE_API_NAME, Utils.BOM_CORE_API_NAME);
                SearchUtil.fillFilterIn(filters, AttributeRangeMode.REF_DATA_ID, attrIds);
                SearchUtil.fillFilterEq(filters, AttributeRangeMode.REF_OBJECT_DESCRIBE_API_NAME, apiName);
                SearchUtil.fillFilterEq(filters, "is_deleted", "0");
                query.setFilters(filters);
                attrRangeList = findBySearchQueryIgnoreAll(user, AttributeRangeMode.API_NAME, query);
                if (CollectionUtils.notEmpty(attrRangeList)) {
                    serviceFacade.bulkDeleteWithInternalDescribe(attrRangeList, user);
                }
            }
        }
    }

    @Override
    public void batchDeleteProductCategoryAttrRange(User user, List<String> categoryIds, List<String> attrIds) {
        if (CollectionUtils.empty(categoryIds)||CollectionUtils.empty(attrIds)) {
            return;
        }
        SearchTemplateQuery query = getSearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, AttributeRangeMode.DATA_ID, categoryIds);
        SearchUtil.fillFilterEq(filters, AttributeRangeMode.DATA_OBJECT_DESCRIBE_API_NAME, "ProductCategoryObj");
        SearchUtil.fillFilterIn(filters, AttributeRangeMode.REF_DATA_ID, attrIds);
        SearchUtil.fillFilterEq(filters, AttributeRangeMode.REF_OBJECT_DESCRIBE_API_NAME, Utils.ATTRIBUTE_OBJ_API_NAME);
        SearchUtil.fillFilterEq(filters, "is_deleted", "0");
        query.setFilters(filters);
        List<IObjectData> attrRangeList = findBySearchQueryIgnoreAll(user, AttributeRangeMode.API_NAME, query);
        if (CollectionUtils.notEmpty(attrRangeList)) {
            serviceFacade.bulkDeleteWithInternalDescribe(attrRangeList, user);
        }
    }

    @Override
    public void handleBomRootNodeAttrRange(IObjectData data, List<String> idList, Object rangeObj, List<Attribute> attributeList, List<IObjectData> nonAttributeList) {
        if(Objects.nonNull(rangeObj) && rangeObj instanceof List){
            List<AttributeRangeMode.AttributeRange> rangeList = (List<AttributeRangeMode.AttributeRange>) rangeObj;
            CollectionUtils.nullToEmpty(attributeList).forEach(x->{
                if (!idList.contains(x.getId())) {
                    rangeList.add(AttributeRangeMode.AttributeRange.builder()
                            .id(x.getId())
                            .name(x.getName())
                            .apiName(Utils.ATTRIBUTE_OBJ_API_NAME)
                            .attrNo(10000)
                            .groupId(StringUtils.defaultIfBlank(x.getGroupId(),"99999999"))
                            .groupNo(StringUtils.isBlank(x.getGroupId())?1:x.getGroupNo())
                            .groupName(x.getGroupName())
                            .attributeValues(x.getAttributeValues())
                            .build());
                }
            });
            CollectionUtils.nullToEmpty(nonAttributeList).forEach(x->{
                if (!idList.contains(x.getId())) {
                    rangeList.add(AttributeRangeMode.AttributeRange.builder()
                            .id(x.getId())
                            .name(AttributeUtils.getI18nName(x))
                            .apiName(Utils.NONSTANDARD_ATTRIBUTE_OBJ_API_NAME)
                            .attrNo(10000)
                            .groupId(StringUtils.defaultIfBlank(x.get("groupId",String.class),"99999999"))
                            .groupNo(StringUtils.isBlank(x.get("groupId",String.class))?1:x.get("groupNo",Integer.class))
                            .groupName(x.get("groupName",String.class))
                            .build());
                }
            });
            data.set(AttributeRangeMode.ATTR_RANGE, rangeList);
        }else{
            List<AttributeRangeMode.AttributeRange> rangeList = Lists.newArrayList();
            CollectionUtils.nullToEmpty(attributeList).forEach(x-> rangeList.add(AttributeRangeMode.AttributeRange.builder()
                    .id(x.getId())
                    .name(x.getName())
                    .apiName(Utils.ATTRIBUTE_OBJ_API_NAME)
                    .attrNo(10000)
                    .groupId(StringUtils.defaultIfBlank(x.getGroupId(),"99999999"))
                    .groupNo(StringUtils.isBlank(x.getGroupId())?1:x.getGroupNo())
                    .groupName(x.getGroupName())
                    .attributeValues(x.getAttributeValues())
                    .build()));
            CollectionUtils.nullToEmpty(nonAttributeList).forEach(x-> rangeList.add(AttributeRangeMode.AttributeRange.builder()
                    .id(x.getId())
                    .name(AttributeUtils.getI18nName(x))
                    .apiName(Utils.NONSTANDARD_ATTRIBUTE_OBJ_API_NAME)
                    .attrNo(10000)
                    .groupId(StringUtils.defaultIfBlank(x.get("groupId",String.class),"99999999"))
                    .groupNo(StringUtils.isBlank(x.get("groupId",String.class))?1:x.get("groupNo",Integer.class))
                    .groupName(x.get("groupName",String.class))
                    .build()));
            data.set(AttributeRangeMode.ATTR_RANGE, rangeList);
        }
    }
}
