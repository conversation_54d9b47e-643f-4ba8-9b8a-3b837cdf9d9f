package com.facishare.crm.sfa.predefine.service.attribute;

import com.facishare.crm.sfa.predefine.service.attribute.model.Attribute;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeValue;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.wechat.proxy.common.utils.GsonUtil;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.experimental.Delegate;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 报价单/订单等从对象属性扩展类
 *
 * <AUTHOR>
 */
public class DetailAttributeExt {
    @Delegate
    @Getter
    private final IObjectData objectData;

    private DetailAttributeExt(IObjectData objectData) {
        if (objectData instanceof ProductAttributeExt) {
            this.objectData = ((ProductAttributeExt) objectData).getObjectData();
        } else {
            this.objectData = objectData;
        }
    }

    public static DetailAttributeExt of(IObjectData objectData) {
        return new DetailAttributeExt(objectData);
    }

    public static List<DetailAttributeExt> ofList(List<IObjectData> dataList) {
        if (dataList == null) {
            return Lists.newArrayList();
        }
        return dataList.stream().map(DetailAttributeExt::of).collect(Collectors.toList());
    }

    /**
     * 是否关联属性
     *
     * @return
     */
    public boolean hasAttribute() {
        return this.objectData.get(AttributeConstants.ATTRIBUTE_JSON) != null
                && StringUtils.isNotEmpty(this.objectData.get(AttributeConstants.ATTRIBUTE_JSON, String.class));
    }

    /**
     * 获取属性Map
     *
     * @return
     */

    public Map<String, String> getAttribute(String fieldName) {
        String value = this.objectData.get(fieldName, String.class);
        return Strings.isNullOrEmpty(value) ? Maps.newHashMap() : GsonUtil.fromJson(value, Map.class);
    }

    public Map<String, String> getAttribute() {
        return getAttribute(AttributeConstants.ATTRIBUTE_JSON);
    }

    /**
     * 获取属性key
     *
     * @return
     */
    public Set<String> getAttributeKeySet() {
        return getAttribute().keySet();
    }

    /**
     * 填充属性文本
     *
     * @param attributes
     * @param attributeValues
     */
    public void fillAttributeText(Map<String, Attribute> attributes, Map<String, AttributeValue> attributeValues) {
        Map<String, String> attributeMap = getAttribute();
        List<String> snippetList = Lists.newArrayList();
        attributeMap.forEach((k, v) -> {
            Attribute attribute = attributes.get(k);
            String attributeName = attribute == null ? "" : attribute.getName();
            AttributeValue attributeValue = attributeValues.get(v);
            String attributeValueName = attributeValue == null ? "" : attributeValue.getName();
            if (!Strings.isNullOrEmpty(attributeName) && !Strings.isNullOrEmpty(attributeValueName)) {
                snippetList.add(attributeName + ":" + attributeValueName);
            }
        });
        this.objectData.set(AttributeConstants.ATTRIBUTE, Joiner.on(";").join(snippetList));
        this.objectData.set(AttributeConstants.ATTRIBUTE_JSON, attributeMap);
    }
}
