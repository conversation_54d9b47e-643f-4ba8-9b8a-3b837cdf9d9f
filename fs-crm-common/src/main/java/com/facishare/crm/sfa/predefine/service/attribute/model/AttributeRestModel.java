package com.facishare.crm.sfa.predefine.service.attribute.model;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> 2020-03-09
 * @instruction
 */
public interface AttributeRestModel {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class CheckValueProductRelationArg {
        private String attributeValueId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class CheckValueProductRelationResult {
        private Boolean hasRelation;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GetAttributeValuesArg {
        /**
         * 属性ID列表
         */
        private List<String> attributeIds;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GetAttributeValuesResult {
        /**
         * 属性值Map，key为属性ID，value为该属性下的所有属性值列表
         */
        private List<ObjectDataDocument> dataList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GetAttributeText {
        private List<ObjectDataDocument> dataList;
    }
}
