package com.facishare.crm.sfa.predefine.service.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants;
import com.facishare.crm.sfa.utilities.constant.BomCoreConstants;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService.*;

/**
 * 业务配置线程级缓存类
 *
 * <AUTHOR>
 */
@Service
public class BizConfigThreadLocalCacheService {
    private static final ThreadLocal<Map<String, String>> BIZ_CONFIG_MAP = new ThreadLocal<>();
    private static final String OPEN = "1";
    private static final String CLOSE = "0";
    private static final String OPEN_DELIVERY_NOTE = "2";
    private static final String ALL = "-1";

    static {
        RequestContextManager.addContextRemoveListener(c -> BIZ_CONFIG_MAP.remove());
    }

    /**
     * 开关开启判断公共方法，只适用于开启标记是“1”的开关。
     *
     * @param tenantId 租户Id
     * @param key      开关key
     * @return 是否开启
     */
    public boolean isOpenConfig(String tenantId, String key) {
        return Objects.equals(OPEN, getBizConfig(tenantId, key));
    }

    public boolean isAvailableRangeEnabled(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.AVAILABLE_RANGE.getKey()));
    }

    public boolean isPartnerEnabled(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IsOpenPartner.getKey())) || Objects.equals("open", getBizConfig(tenantId, ConfigType.MODULE_PRM.getKey()));
    }

    public boolean spuSkuNewLogic(String tenantId) {
        return openSpu(tenantId);
    }

    public boolean openSpu(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.SPU.getKey()));
    }

    public boolean isPriceBookEnabled(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IS_PRICE_BOOK_ENABLED.getKey()));
    }

    public boolean isCurrencyEnabled(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, MULTI_CURRENCY_CONFIG));
    }

    public boolean isLeadsTransferUseAllPriceBook(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.LEADS_TRANSFER_GRAY_PRICE_BOOK.getKey()));
    }

    public boolean isCPQEnabled(String tenantId) {
        if (Objects.equals(OPEN, getBizConfig(tenantId, MODULE_CPQ))) {
            return true;
        }
        return Objects.equals(OPEN, getBizConfig(tenantId, MODULE_SIMPLE_CPQ));
    }

    public boolean isTempNodeEnabled(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, MODULE_BOM_TEMP_NODE));
    }

    public boolean isBomInstanceEnabled(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, BOM_INSTANCE));
    }

    public boolean isStockEnabled(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IsInventoryEnabled.getKey()));
    }

    public boolean isMultiUnitEnabled(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, MODULE_MULTIPLE_UNIT));
    }

    public boolean isEnforcePriority(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.ENFORCE_PRIORITY.getKey()));
    }

    public boolean isAsyncCreateOrder(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.ASYNC_CREATE_ORDER.getKey()));
    }

    public boolean isInvoiceIsAllowedOverflow(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.INVOICE_IS_ALLOWED_OVERFLOW.getKey()));
    }

    public boolean isNewInvoice(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.NEW_INVOICE.getKey()));
    }

    public boolean isMultipleUnit(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.MODULE_MULTIPLE_UNIT.getKey()));
    }

    public boolean isOpenAvailableRangePriority(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IS_OPEN_AVAILABLE_RANGE_PRIORITY.getKey()));
    }

    public boolean isOpenAvailableRange(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.AVAILABLE_RANGE.getKey()));
    }

    public boolean isCustomerAccountEnabled(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IS_CUSTOMER_ACCOUNT_ENABLED.getKey()));
    }

    public boolean isTradeProductRepeatable(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IsTradeProductRepeatable.getKey()));
    }

    public boolean isOpenAttribute(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IS_OPEN_ATTRIBUTE.getKey()));
    }

    public boolean isOpenIncrementalPricing(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IS_OPEN_INCREMENTAL_PRICING.getKey()));
    }

    public boolean isOpenNonstandardAttribute(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IS_OPEN_NONSTANDARD_ATTRIBUTE.getKey()));
    }

    public boolean isGetPriceWhenConvert(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.GET_PRICE_WHEN_CONVERT.getKey()));
    }

    public boolean isGetPriceWhenCopy(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.GET_PRICE_WHEN_COPY.getKey()));
    }

    public boolean isGetPriceWhenCopy4Quote(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.GET_PRICE_WHEN_COPY_QUOTE.getKey()));
    }

    public boolean isGetPriceWhenCopy4Contract(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.GET_PRICE_WHEN_COPY_CONTRACT.getKey()));
    }

    public boolean isGetPriceWhenCopy4NewOpportunity(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.GET_PRICE_WHEN_COPY_NEWOPPORTUNITY.getKey()));
    }

    public String productKeywordSearchMode(String tenantId) {
        return getBizConfig(tenantId, ConfigType.PRODUCT_KEYWORD_SEARCH_MODE.getKey());
    }

    public boolean isPromotionEnabled(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IsPromotionEnabled.getKey()));
    }

    public boolean isVirtualExtensionEnabled(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.VIRTUAL_EXTENSION.getKey()));
    }

    public boolean isSpuEnabled(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.SPU.getKey()));
    }

    public boolean isCloneHistoryOrderProductEnabled(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.CLONE_HISTORY_ORDER_PRODUCT.getKey()));
    }


    public boolean isOpenPricePolicy(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.PRICE_POLICY.getKey()));
    }

    public boolean isOpenPricePolicySalesOrderObj(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.PRICE_POLICY_SALES_ORDER_OBJ.getKey()));
    }

    public boolean isOpenPricePolicyQuoteObj(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.PRICE_POLICY_QUOTE_OBJ.getKey()));
    }

    public boolean isOpenPricePolicy(String tenantId, String objectApiName) {
        if (StringUtils.isBlank(objectApiName)) {
            return false;
        }
        if (!PricePolicyConstants.MASTER_DETAIL_API_NAME.containsKey(objectApiName)) {
            return false;
        }
        String configKey = ConfigType.PRICE_POLICY.getKey() + "_" + objectApiName;
        return Objects.equals(OPEN, SFAConfigUtil.getConfigValue(tenantId, configKey, User.SUPPER_ADMIN_USER_ID));
    }

    public boolean enforcePricePolicyPriority(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.ENFORCE_PRICE_POLICY_PRIORITY.getKey()));
    }

    public boolean isEnlargeEditPrivilege(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.ORDER_ENLARGE_EDIT_PRIVILEGE.getKey()));
    }

    public boolean isDeliveryNoteEnabled(String tenantId) {
        return Objects.equals(OPEN_DELIVERY_NOTE, getBizConfig(tenantId, ConfigType.DELIVERY_NOTE_STATUS.getKey()));
    }

    public boolean isDingHuoTongEnabled(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IS_DING_HUO_TONG_ENABLED.getKey()));
    }

    public boolean isAvailableRangeIgnorePriceBookValid(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IGNORE_PRICE_BOOK_VALID_PERIOD.getKey()));
    }

    public boolean bomAdaptationPriceListRules(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.BOM_ADAPTATION_PRICE_LIST_RULES.getKey()));
    }

    public boolean bomPriceCalculationConfiguration(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.BOM_PRICE_CALCULATION_CONFIGURATION.getKey()));
    }

    public boolean isOpenCoupon(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.COUPON.getKey()));
    }

    public boolean isOpenFullAmortize(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.PRICE_POLICY_CREATE_FULL_AMORTIZE.getKey()));
    }

    public boolean isEnableGiftRangeShelves(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.ENABLE_GIFT_RANGE_SHELVES.getKey()));
    }

    public boolean isOpenPaperCoupon(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.PAPER_COUPON.getKey()));
    }

    public boolean isCloseOldCategory(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.CLOSE_OLD_CATEGORY.getKey()));
    }

    public boolean allowEditPriceBookPrice(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.ALLOW_EDIT_PRICE_BOOK_PRICE.getKey()));
    }

    public long getCategoryTotalLimit(String tenantId) {
        String total = getBizConfig(tenantId, ConfigType.CATEGORY_TOTAL_LIMIT.getKey());
        if (StringUtils.isBlank(total)) {
            return 5000L;
        }
        return Long.parseLong(total);
    }

    public boolean isOpenRebate(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.REBATE.getKey()));
    }

    /**
     * @param tenantId
     * @param objectApiName
     * @return 备注：biz_function 支持在维护，维护的数据格式为 {"objectApiName": {"label":"","api_name":"bom_version_filter__c"}}
     */
    public BizFunctionInfo getFunctionInfo(String tenantId, String objectApiName) {
        String confValue = getBizConfig(tenantId, ConfigType.BIZ_FUNCTION.getKey());
        if (StringUtils.isEmpty(confValue)) {
            return null;
        }

        Map functionInfoList = JSON.parseObject(confValue, Map.class);
        Object value = functionInfoList.get(objectApiName);
        String aplApiName = null;
        if (Objects.isNull(value)) {
            return null;
        } else {
            //兼容历史数据处理
            if (value instanceof String) {
                aplApiName = (String) value;
            } else if (value instanceof JSONObject) {
                JSONObject json = (JSONObject) value;
                aplApiName = json.getString("api_name");
            } else {
                //不处理
            }
        }
        if (StringUtils.isBlank(aplApiName)) {
            return null;
        } else {
            return BizFunctionInfo.builder().objectAPIName(objectApiName).functionAPIName(aplApiName).build();
        }
    }

    public String getBizConfig(String tenantId, String key) {
        Map<String, String> map = BIZ_CONFIG_MAP.get();
        if (map == null || !map.containsKey(key)) {
            map = map == null ? Maps.newHashMap() : map;
            map.put(key, SFAConfigUtil.getConfigValue(tenantId, key, User.SUPPER_ADMIN_USER_ID));
            BIZ_CONFIG_MAP.set(map);
        }
        return map.get(key);
    }

    public boolean isOpenMultiUnitPriceBook(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.MULTI_UNIT_PRICE_BOOK.getKey()));
    }

    public boolean isOpenAllProductDisplayPromotion(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.ALL_PRODUCT_DISPLAY_PROMOTION.getKey()));
    }

    public boolean isOpenGiftAttendAmortize(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.GIFT_ATTEND_AMORTIZE.getKey()));
    }

    public boolean isOpenWhetherFilterPriceBookSelectProduct(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.WHETHER_FILTER_PRICE_BOOK_SELECT_PRODUCT.getKey()));
    }

    public boolean isOpenAvailablePriceBook(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.AVAILABLE_PRICE_BOOK.getKey()));
    }

    public boolean isOpenPriceBookProductValidPeriod(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.PRICE_BOOK_PRODUCT_VALID_PERIOD.getKey()));
    }

    public boolean isOpenPriceBookProductTieredPrice(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.PRICE_BOOK_PRODUCT_TIERED_PRICE.getKey()));
    }

    public boolean isOpenPriceBookReform(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.PRICE_BOOK_REFORM.getKey()));
    }

    public boolean isOpenShowPricePolicyName(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.SHOW_PRICE_POLICY_NAME.getKey()));
    }

    public boolean isIgnoreCheckCeilingFloorPrice(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IGNORE_CHECK_CEILING_FLOOR_PRICE.getKey()));
    }

    public boolean isOpenPartnerAddress(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.PARTNER_ADDRESS.getKey()));
    }

    public boolean isOpenPartnerSubsidiary(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.PARTNER_SUBSIDIARY.getKey()));
    }

    public boolean isOpenSaleContract(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.MODULE_SALE_CONTRACT.getKey()));
    }

    public boolean isOpenManualGift(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.MANUAL_GIFT.getKey()));
    }

    public boolean isOpenLimitMultiPolicy(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.LIMIT_MULTI_POLICY.getKey()));
    }

    public boolean isDetailMatchAsPriority(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.DETAIL_MATCH_AS_PRIORITY.getKey()));
    }

    public boolean isOpenContractConstraintMode(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.CONTRACT_CONSTRAINT_MODE.getKey()));
    }

    public boolean isOpenContractConstraintPricebookAvailableRange(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.CONTRACT_CONSTRAINT_PRICEBOOK_AVAILABLE_RANGE.getKey()));
    }

    public boolean isOpenAccountsReceivable(String tenantId) {
        return Objects.equals(OPEN_DELIVERY_NOTE, getBizConfig(tenantId, ConfigType.ACCOUNTS_RECEIVABLE_STATUS.getKey()));
    }

    public boolean isOpenPricePolicyPercentile(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.PRICE_POLICY_SUPPORT_PERCENTILE_GIFT.getKey()));
    }

    private BomCoreConstants.BomGeneratedStrategy generateStandardBom(String tenantId) {
        String value = getBizConfig(tenantId, ConfigType.GENERATE_STANDARD_BOM_BASED_ON_ORDER.getKey());
        try {
            return BomCoreConstants.BomGeneratedStrategy.getStrategy(Integer.parseInt(value));
        } catch (Exception e) {
            return BomCoreConstants.BomGeneratedStrategy.NONE;
        }
    }

    public boolean isGenerateStandardBomManual(String tenantId) {
        return BomCoreConstants.BomGeneratedStrategy.MANUAL.equals(generateStandardBom(tenantId));
    }

    public boolean isGenerateStandardBomAuto(String tenantId) {
        return BomCoreConstants.BomGeneratedStrategy.AUTO.equals(generateStandardBom(tenantId));
    }

    public boolean isGenerateStandardBomNone(String tenantId) {
        return BomCoreConstants.BomGeneratedStrategy.NONE.equals(generateStandardBom(tenantId));
    }

    public boolean isOpenPolicyMoveAmortize(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.PRICE_POLICY_SAVE_MOVE_AMORTIZE.getKey()));
    }

    public boolean isDhtMultiLevelOrder(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.DHT_MULTI_LEVEL_ORDER.getKey()));
    }

    public boolean isOpenQuoter(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IS_OPEN_QUOTER.getKey()));
    }

    public boolean isBomLeafNodeClosed(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.BOM_SINGLE_LEAF_NODE_CLOSED.getKey()));
    }

    public boolean isOpenBomDuplicateCheck(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.BOM_DUPLICATE_CHECK.getKey()));
    }

    public long getOpeningBalanceDate(String tenantId) {
        String date = getBizConfig(tenantId, ConfigType.OPENING_BALANCE_DATE.getKey());
        if (StringUtils.isBlank(date)) {
            return 0L;
        }
        return Long.parseLong(date);
    }

    public int getInteractionStrategyLimitTotal(String tenantId) {
        String limitTotal = getBizConfig(tenantId, ConfigType.INTERACTION_STRATEGY_LIMIT_TOTAL.getKey());
        return Integer.parseInt(limitTotal);
    }

    public boolean isOpeningBalanceForceCheck(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.OPENING_BALANCE_FORCE_CHECK.getKey()));
    }

    public boolean isInvoiceOrderDecoupling(String tenantId) {
        return Objects.equals(CLOSE, getBizConfig(tenantId, ConfigType.INVOICE_ORDER_BINDING_STATUS.getKey()));
    }

    public boolean isRefundOrderDecoupling(String tenantId) {
        return Objects.equals(CLOSE, getBizConfig(tenantId, ConfigType.REFUND_ORDER_BINDING_STATUS.getKey()));
    }

    public boolean isPaymentPlanOrderDecoupling(String tenantId) {
        return Objects.equals(CLOSE, getBizConfig(tenantId, ConfigType.PAYMENT_PLAN_ORDER_BINDING_STATUS.getKey()));
    }

    public boolean isInvoiceSupportNegativeAndZero(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.INVOICE_SUPPORT_NEGATIVE_AND_ZERO.getKey()));
    }

    public boolean allowDynamicAmortize(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.DYNAMIC_ALLOW_AMORTIZE.getKey()));
    }

    public boolean isOpenIncentive(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, "init_incentive"));
    }

    public boolean isOpenReceivedPayment(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IS_OPEN_RECEIVED_PAYMENT.getKey()));
    }

    public String getOrderPaymentRequiredConfig(String tenantId) {
        return getBizConfig(tenantId, ConfigType.ORDER_PAYMENT_REQUIRED.getKey());
    }

    public boolean isOpenOrderPaymentMultiSource(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IS_OPEN_ORDER_PAYMENT_MULTI_SOURCE.getKey()));
    }

    public List<String> getOrderPaymentMappingRule(String tenantId) {
        String value = getBizConfig(tenantId, ConfigType.ORDER_PAYMENT_MAPPING_RULE.getKey());
        return StringUtils.isBlank(value) ? null : Arrays.asList(value.split(","));
    }

    public boolean isMultipleObjectPricePolicy(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.MULTIPLE_OBJECT_PRICE_POLICY.getKey()));
    }

    public boolean isOpenAutoMatch(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IS_OPEN_AUTO_MATCH.getKey()));
    }

    public boolean isOpenKxAutoMatch(String tenantId) {
        return Objects.equals(OPEN_DELIVERY_NOTE, getBizConfig(tenantId, ConfigType.ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_SWITCH_KEY.getKey()));
    }

    public boolean isOpenArQuickAdd(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IS_OPEN_AR_QUICK_ADD.getKey()));
    }

    public String getCreateArByObjects(String tenantId) {
        return getBizConfig(tenantId, ConfigType.CREATE_AR_BY_OBJECTS.getKey());
    }

    public boolean isOpenAccountsReceivableExpired(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IS_OPEN_PERIODIC_ACCOUNTS_RECEIVABLE.getKey()));
    }

    public String getAccountsReceivableExpiredType(String tenantId) {
        return getBizConfig(tenantId, ConfigType.PERIODIC_ACCOUNTS_RECEIVABLE_TYPE.getKey());
    }

    public boolean enableSyncChangePartnerOwner(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.SYNC_CHANGE_PARTNER_OWNER.getKey()));
    }

    public boolean openPeriodicProduct(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.PERIODIC_PRODUCT.getKey()));
    }

    public String getInvoiceLinesRequired(String tenantId) {
        return getBizConfig(tenantId, ConfigType.INVOICE_LINES_REQUIRED.getKey());
    }

    public boolean isOpenInvoiceLinesMultiSource(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.INVOICE_LINES_MULTI_SOURCE.getKey()));
    }

    public boolean isOpenAdditionalContract(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.IS_OPEN_ADDITIONAL_CONTRACT.getKey()));
    }

    public boolean isOpenStratifiedPricing(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.STRATIFIED_PRICING.getKey()));
    }

    public String queryChannelAccess(String tenantId) {
        return getBizConfig(tenantId, ConfigType.OPEN_CHANNEL_ACCESS.getKey());
    }

    public boolean isOpenStratifiedOrTieredPrice(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.PRICE_BOOK_PRODUCT_TIERED_PRICE.getKey())) || Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.STRATIFIED_PRICING.getKey()));
    }

    public boolean isMultiShoppingMallEnabled(String tenantId) {
        return Objects.equals(OPEN, getBizConfig(tenantId, ConfigType.MULTI_SHOPPING_MALL.getKey()));
    }
}
