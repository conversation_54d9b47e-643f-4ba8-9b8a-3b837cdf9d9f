package com.facishare.crm.sfa.predefine.service.modulectrl;


import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule;
import com.facishare.paas.appframework.core.model.ServiceContext;

public interface IModuleInitService {
    String MODULE_CPQ = "cpq";
    String MODULE_SIMPLE_CPQ = "simple_cpq";
    String MODULE_STANDARD_CPQ = "standard_cpq";
    String MODULE_BOM_TEMP_NODE = "bom_temp_node";
    String MODULE_CPQ_TIEREDPRICE = "cpq_tieredprice";
    String MODULE_SPU = "config_spu_or_sku_selector";
    String MODULE_CHANGE_TO_NEW_OPPORTUNITY = "change_to_new_opportunity";
    String MODULE_MULTIPLE_UNIT = "multiple_unit";
    String MODULE_NEW_INVOICE = "new_invoice";
    String INVOICE_IS_ALLOWED_OVERFLOW = "invoice_is_allowed_overflow";
    String DEFAULT_MODULE = "default_module";
    String MODULE_SAVE_NEW_OPPORTUNITY_USER_FILTER = "new_opportuntiy_user_filter";
    String CONFIG_CHANGE_CLOSE_DATE_IF_WIN = "config_change_close_date_if_win";
    String AVAILABLE_RANGE = "available_range";
    String IS_OPEN_ATTRIBUTE = "is_open_attribute";
    String PRICE_POLICY = "price_policy";
    String MULTI_CURRENCY_CONFIG = "multi_currency_config";
    String SALE_CONTRACT = "sale_contract" ;
    String BOM_INSTANCE = "bom_instance" ;
    String BOM_NODE_SHARE = "bom_node_share" ;
    String VIRTUAL_EXTENSION = "virtual_extension";
    String REBATE = "rebate";
    String COUPON = "coupon";
    String PAPER_COUPON = "paper_coupon";
    String ORDER_CLOSE = "order_close";

    String MULTI_UNIT_PRICE_BOOK = "multi_unit_price_book";
    String AVAILABLE_PRICE_BOOK = "available_price_book";
    String MANUAL_GIFT = "manual_gift";
    String CONTRACT_CONSTRAINT_MODE = "contract_constraint_mode";
    String CONTRACT_CONSTRAINT_PRICEBOOK_AVAILABLE_RANGE = "contract_constraint_pricebook_available_range";
    String GENERATE_STANDARD_BOM_BASED_ON_ORDER = "generate_standard_bom_based_on_order";
    String getModuleCode();

    ConfigCtrlModule.Result initModule(String tenantId, String userId);

    ConfigCtrlModule.Result initModuleRepair(ServiceContext context, String tenantId);

    ConfigCtrlModule.Result closeModule(String tenantId);

    default String openStatus() {
        return null;
    }

}
