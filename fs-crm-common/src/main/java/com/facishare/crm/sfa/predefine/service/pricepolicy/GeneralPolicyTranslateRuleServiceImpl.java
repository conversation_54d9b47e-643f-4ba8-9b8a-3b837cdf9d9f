package com.facishare.crm.sfa.predefine.service.pricepolicy;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.SimplePolicyConstants;
import com.facishare.crm.sfa.predefine.service.pricepolicy.model.GeneralPricePolicy;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.ExceptionUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.OptionalUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.SimpleGeneralPricePolicyUtils;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.github.autoconf.ConfigFactory;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * 抽象简易价格翻译规则服务
 *
 * <AUTHOR>
 * @date 2022/02/17
 */
@Service
public class GeneralPolicyTranslateRuleServiceImpl implements AbsPolicyTranslateRuleService {
    private static int generalPricePolicyProductCategoryLimit;

    @PostConstruct
    private void init() {
        ConfigFactory.getConfig("fs-crm-sales-config", iConfig -> {
            generalPricePolicyProductCategoryLimit = iConfig.getInt("general_price_policy_product_category_limit", 200);
        });
    }

    private static final Logger log = org.slf4j.LoggerFactory.getLogger(GeneralPolicyTranslateRuleServiceImpl.class);
    @Autowired
    protected ServiceFacade serviceFacade;

    @Override
    public List<IObjectData> beginTranslate(IObjectData objectData, User user, String addOrEdit) {
        String modeType = objectData.get(SimplePolicyConstants.ModeType.ModeType, String.class);
        GeneralPricePolicy.ProductTableData productTableDates = getProductTableData(objectData);
        log.info("general_price_policy_objectData is {}", objectData);
        //规则校验
        validateProductJson(productTableDates);
        if (SimplePolicyConstants.ADDOREDITORLIST.ADD.getValue().equals(addOrEdit)) {
            //克隆时,去掉价格规则id 新建去掉价格规则id
            Optional.of(productTableDates.getGroupings()).orElse(new ArrayList<>()).forEach(
                    o -> Optional.of(o.getLadders()).orElse(new ArrayList<>()).forEach(i -> i.setPricePolicyRuleId("")));
        }
        //获取处理器
        GeneralDetailsPricePolicyHandler generalDetailsPricePolicyHandler = SimpleGeneralPricePolicyUtils.createGeneralPricePolicyHandler(modeType);
        //如果找不到处理器 抛出异常
        if (Objects.isNull(generalDetailsPricePolicyHandler)) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_GENERAL_PRICE_POLICY_HANDLLER_MUST_HAVE, "sfa.simple.general.price.policy.handller.must.have").get();
        }
        List<IObjectData> pricePolicyRules = generalDetailsPricePolicyHandler.createPricePolicyRule(objectData, productTableDates, user);
        //替换productTableDates,此时productTableDates含有价格政策规则id
        setProductTableData(objectData, productTableDates);
        log.info("general_price_policy_rule is {} ", pricePolicyRules);
        return pricePolicyRules;
    }

    @Override
    public void render(IObjectData objectData, User user) {

    }


    /**
     * 获取表格数据
     *
     * @param objectData 对象数据
     * @return {@code List<ProductTableData>}
     */
    protected GeneralPricePolicy.ProductTableData getProductTableData(IObjectData objectData) {
        String productConditionContent = OptionalUtils.str(objectData.get(SimplePolicyConstants.SIMPLE_POLICY_FIELD.SIMPLE_GENERAL_POLICY_DATA.getApiName(), String.class),
                SFAI18NKeyUtil.SFA_CONTACT_PARAM_EXCEPTION, "general policy json content is null ");
        return ExceptionUtils.trySupplier(() -> JSON.parseObject(productConditionContent, GeneralPricePolicy.ProductTableData.class));
    }

    /**
     * 赋值表格数据
     *
     * @param objectData       对象数据
     * @param productTableData 新表格数据,新增价格政策规则id
     */
    protected void setProductTableData(IObjectData objectData, GeneralPricePolicy.ProductTableData productTableData) {
        objectData.set(SimplePolicyConstants.SIMPLE_POLICY_FIELD.SIMPLE_GENERAL_POLICY_DATA.getApiName(), productTableData);
    }

    /**
     * 校验简易版价格政策
     */
    protected void validateProductJson(GeneralPricePolicy.ProductTableData productTableData) {
        //判断属性不能为null和空字符串
        if (SimpleGeneralPricePolicyUtils.fieldIsEmpty(GeneralPricePolicy.ProductTableData.class, productTableData)) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_ALL_ATTRIBUTE_MUST_NOT_NULL, "simple_price_policy all attribute must not null").get();
        }
        //多阶梯不能每满
        if (SimplePolicyConstants.GENERALCYCLE.CYCLE.getValue().equals(productTableData.getCycleStatus()) && SimplePolicyConstants.GENERALLADDERMODE.LADDER.getValue().equals(productTableData.getLadder())) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_CYCLE_WITHOUT_LADDER, "simple_price_policy cycle without ladder").get();
        }
        //组合不能为空
        if (CollectionUtils.isEmpty(productTableData.getGroupings())) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_GENERAL_SIMPLE_PRICE_POLICY_GROUPINGS_MUST_HAVE, "simple_general_price_policy groupings must have").get();
        }
        //检验组
        groupingVerify(productTableData);
    }

    private void groupingVerify(GeneralPricePolicy.ProductTableData productTableData) {
        //如果是赠品 多组合只能有一个 合计满
        if (SimplePolicyConstants.ModeType.DETAILS_GIFT.equals(productTableData.getModeType())
                && productTableData.getGroupings().stream()
                .filter(o -> SimplePolicyConstants.ANYORALL.ALL.getValue().equals(o.getAnyOrAll())).count() > 1) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_GENERAL_PRICE_POLICY_DETAILS_GIFT_ALL_ONLY_ONE, "sfa_simple_general_price_policy details_gift all onlyOne").get();
        }
        //校验阶梯
        productTableData.getGroupings().forEach(o -> ladderVerify(o.getLadders()));
        //校验产品和分类
        productTableData.getGroupings().forEach(o -> categoryProductVerify(o.getConditionCategoryProducts(), productTableData.getModeType()));
    }

    private void ladderVerify(List<GeneralPricePolicy.ProductTableData.Ladder> ladders) {
        //判断阶梯不能为空
        if (CollectionUtils.isEmpty(ladders)) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_LADDER_MUST_HAVE, "simple_price_policy ladder must have").get();
        }
        //遍历阶梯
        for (GeneralPricePolicy.ProductTableData.Ladder ladder : ladders) {
            //判断属性不能为null和空字符串
            if (SimpleGeneralPricePolicyUtils.fieldIsEmpty(GeneralPricePolicy.ProductTableData.Ladder.class, ladder)) {
                throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_ALL_ATTRIBUTE_MUST_NOT_NULL, "simple_price_policy all attribute must not null").get();
            }
        }
    }

    private void categoryProductVerify(GeneralPricePolicy.ProductTableData.ConditionCategoryProduct conditionCategoryProduct, String modeType) {
        //判断属性不能为null和空字符串
        if (SimpleGeneralPricePolicyUtils.fieldIsEmpty(GeneralPricePolicy.ProductTableData.ConditionCategoryProduct.class, conditionCategoryProduct)) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_ALL_ATTRIBUTE_MUST_NOT_NULL, "simple_price_policy all attribute must not null").get();
        }
        //获取产品/分类集合
        List<GeneralPricePolicy.ProductTableData.CategoryProducts> categoryProducts = conditionCategoryProduct.getCategoryProducts();

        if (categoryProducts.size() > generalPricePolicyProductCategoryLimit) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_CONDITION_TOO_MANY, "sfa_simple_price_policy_condition_too_many").get();
        }

        //判断产品不能为空
        if (modeType.startsWith("details") && CollectionUtils.isEmpty(categoryProducts)) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_GENERAL_PRICE_POLICY_CATEGORY_PRODUCT_MUST_HAVE, "simple_general_price_policy category_product must have").get();
        }
        categoryProducts.forEach(o -> {
            //判断属性不能为null和空字符串
            if (SimpleGeneralPricePolicyUtils.fieldIsEmpty(GeneralPricePolicy.ProductTableData.CategoryProducts.class, o)) {
                throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_ALL_ATTRIBUTE_MUST_NOT_NULL, "simple_price_policy all attribute must not null").get();
            }
        });
    }
}







