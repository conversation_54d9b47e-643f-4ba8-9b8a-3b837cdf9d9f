package com.facishare.crm.sfa.predefine.service.pricepolicy;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.SimplePolicyConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.service.pricepolicy.model.SimplePricePolicy;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RuleWhere;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.ExceptionUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.OptionalUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.SimpleGeneralPricePolicyUtils;
import com.facishare.crm.sfa.utilities.constant.AggregateRuleConstants;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.Where;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 抽象简易价格翻译规则服务
 *
 * <AUTHOR>
 * @date 2022/02/17
 */
@Service
public class SimplePolicyTranslateRuleServiceImpl implements AbsPolicyTranslateRuleService {
    private static int simplePricePolicyProductCategoryLimit;
    private static int simplePricePolicyProductCategoryRequiredLimit;

    @PostConstruct
    private void init() {
        ConfigFactory.getConfig("fs-crm-sales-config", iConfig -> {
            simplePricePolicyProductCategoryLimit = iConfig.getInt("simple_price_policy_product_category_limit", 200);
            simplePricePolicyProductCategoryRequiredLimit = iConfig.getInt("simple_price_policy_product_category_required_limit", 50);
        });
    }

    private static final Logger log = org.slf4j.LoggerFactory.getLogger(SimplePolicyTranslateRuleServiceImpl.class);
    @Autowired
    protected ServiceFacade serviceFacade;

    @Override
    public List<IObjectData> beginTranslate(IObjectData objectData, User user, String addOrEdit) {
        SimplePricePolicy.ProductTableData productTableDates = getProductTableData(objectData);
        log.info("simple_price_policy_objectData is {}", objectData);
        //如果是apl新建
        if (SimplePolicyConstants.APL.IS_APL.getValue().equals(productTableDates.getIsAPL()) && SimplePolicyConstants.ADDOREDITORLIST.ADD.getValue().equals(addOrEdit)) {
            SimplePricePolicy.ProductTableData productTableData = JSON.parseObject(SimplePolicyConstants.MEANINGLESS, SimplePricePolicy.ProductTableData.class);
            productTableData.setIsAPL(productTableDates.getIsAPL());
            productTableData.setIsRTD(productTableDates.getIsRTD());
            productTableData.setIsAdd(productTableDates.getIsAdd());
            productTableData.setDealer(productTableDates.getDealer());
            productTableDates = productTableData;
        }
        validateConditionCategoryGift(productTableDates);
        if (SimplePolicyConstants.ADDOREDITORLIST.ADD.getValue().equals(addOrEdit)) {
            //克隆时,去掉价格规则id 新建去掉价格规则id
            Optional.of(productTableDates.getConditionCategoryDifts()).orElse(new ArrayList<>()).forEach(o -> o.setPricePolicyRuleId(""));
        }
        //返回聚合规则集合
        saveAggregate(objectData, productTableDates, user);

        List<IObjectData> pricePolicyRules = createAbsPricePolicyRule(productTableDates, user, objectData);
        //替换productTableDates,此时productTableDates含有价格政策规则id
        setProductTableData(objectData, productTableDates);
        log.info("simple_price_policy_rule is {} ", pricePolicyRules);
        return pricePolicyRules;
    }

    @Override
    public void render(IObjectData objectData, User user) {

    }


    /**
     * 获取表格数据
     *
     * @param objectData 对象数据
     * @return {@code List<ProductTableData>}
     */
    protected SimplePricePolicy.ProductTableData getProductTableData(IObjectData objectData) {
        String productConditionContent = OptionalUtils.str(objectData.get(SimplePolicyConstants.SIMPLE_POLICY_FIELD.SIMPLE_GENERAL_POLICY_DATA.getApiName(), String.class),
                SFAI18NKeyUtil.SFA_CONTACT_PARAM_EXCEPTION, "simple policy json content is null ");
        return ExceptionUtils.trySupplier(() -> JSON.parseObject(productConditionContent, SimplePricePolicy.ProductTableData.class));
    }

    /**
     * 赋值表格数据
     *
     * @param objectData       对象数据
     * @param productTableData 新表格数据,新增价格政策规则id
     */
    protected void setProductTableData(IObjectData objectData, SimplePricePolicy.ProductTableData productTableData) {
        objectData.set(SimplePolicyConstants.SIMPLE_POLICY_FIELD.SIMPLE_GENERAL_POLICY_DATA.getApiName(), productTableData);
    }

    /**
     * 保存聚合值
     *
     * @param objectData 对象数据
     * @param user       用户
     */
    protected void saveAggregate(IObjectData objectData, SimplePricePolicy.ProductTableData simplePolicyData, User user) {

        //创建聚合规则
        List<IObjectData> aggDatas = createAgg(createAggList(simplePolicyData), user, objectData);
        Map<String, IObjectData> aggDataMap = Maps.newHashMap();
        for (IObjectData aggData : aggDatas) {
            if (!StringUtils.isBlank(String.valueOf(aggData.get(SimplePolicyConstants.SIMPLE_POLICY_FIELD.PRODUCT_ID.getApiName())))) {
                aggDataMap.put(String.valueOf(aggData.get(SimplePolicyConstants.SIMPLE_POLICY_FIELD.PRODUCT_ID.getApiName())), aggData);
            } else {
                aggDataMap.put(String.valueOf(aggData.get(SimplePolicyConstants.SIMPLE_POLICY_FIELD.CATEGORY_ID.getApiName())), aggData);
            }
        }
        //将聚合规则和条件行匹配
        simplePolicyData.getConditionCategoryDifts().forEach(o -> {
            List<Map<String, String>> marks = Lists.newArrayList();
            List<SimplePricePolicy.ProductTableData.Condition> conditions = simplePolicyData.getConditionGlobal();
            for (SimplePricePolicy.ProductTableData.Condition condition : conditions) {
                //创建阶梯和聚合规则的关系
                Map<String, String> mark = new HashMap<>();
                if (!StringUtils.isBlank(condition.getProductId())) { //选择产品
                    IObjectData aggData = aggDataMap.get(condition.getProductId());
                    if (!Objects.isNull(aggData)) {
                        //匹配必选项,获取最小数量
                        Optional<SimplePricePolicy.ProductTableData.RequireCategoryProduct> requireMinQuantity = o.getRequireCategoryProduct().stream().filter(i -> i.getCategoryProductId().equals(condition.getProductId())).findFirst();
                        aggLadder(o, aggData, mark, requireMinQuantity.orElseThrow(ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_CONDITION_REQUIRE_MUST_NOT_EMPTY)).getMinQuantity());
                        marks.add(mark);
                    }
                } else { //只选择了分类
                    IObjectData aggData = aggDataMap.get(condition.getCategoryId());
                    if (!Objects.isNull(aggData)) {
                        //匹配必选项,获取最小数量
                        Optional<SimplePricePolicy.ProductTableData.RequireCategoryProduct> requireMinQuantity = o.getRequireCategoryProduct().stream().filter(i -> i.getCategoryProductId().equals(condition.getCategoryId())).findFirst();
                        aggLadder(o, aggData, mark, requireMinQuantity.orElseThrow(ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_CONDITION_REQUIRE_MUST_NOT_EMPTY)).getMinQuantity());
                        marks.add(mark);
                    }
                }
            }// 最少总数量
            //创建阶梯和聚合规则的关系
            Map<String, String> mark = new HashMap<>();
            aggLadder(o, aggDataMap.get("ALL"), mark, o.getMinTotalQuantity());
            marks.add(mark);
            o.setAggLadder(marks);
            //优先级 倒叙赋值 优先级和阶梯数为反比
            o.setPriorityNum(String.valueOf(simplePolicyData.getConditionCategoryDifts().size() + 1 - Integer.parseInt(o.getLadderIndex())));
        });
        String tenantId = user.getTenantId();
        log.info("aggValue is {}", aggDatas);
        try {
            serviceFacade.bulkSaveObjectData(aggDatas, new User(tenantId, User.SUPPER_ADMIN_USER_ID));
        } catch (Exception e) {
            log.info("aggValue {} save failed", aggDatas);
        }
    }

    //创建阶梯和规则的关系
    private void aggLadder(SimplePricePolicy.ProductTableData.ConditionCategoryGift o, IObjectData aggData, Map<String, String> mark, String quantity) {
        mark.put(DBRecord.ID, String.valueOf(aggData.get(DBRecord.ID))); //聚合规则id
        mark.put(SimplePolicyConstants.SIMPLE_POLICY_FIELD.LADDER.getApiName(), o.getLadderIndex()); //阶梯
        mark.put(SimplePolicyConstants.SIMPLE_POLICY_FIELD.QUANTITY.getApiName(), quantity); //数量 包含最少数量和最少总数量
        mark.put(SimplePolicyConstants.SIMPLE_POLICY_FIELD.FIELD_NAME__S.getApiName(), String.valueOf(aggData.get(SimplePolicyConstants.SIMPLE_POLICY_FIELD.FIELD_NAME__S.getApiName())));
        mark.put(SimplePolicyConstants.SIMPLE_POLICY_FIELD.IS_GROUP.getApiName(), String.valueOf(aggData.get(SimplePolicyConstants.SIMPLE_POLICY_FIELD.IS_GROUP.getApiName()))); //产品组合
    }


    /**
     * 创建聚合规则
     */
    private List<IObjectData> createAgg(SimplePolicyConstants.DoubleList doubleList, User user, IObjectData objectData) {
        String apiName = SFAPreDefine.AggregateRule.getApiName();
        String tenantId = user.getTenantId();
        //获取聚合规则的describe
        IObjectDescribe aggregateRuleDescribe = serviceFacade.findObject(tenantId, apiName);
        //聚合规则结合
        List<IObjectData> aggDatas = Lists.newArrayList();
        //必选条件行,如果长度为0 证明只有订单组合,
        List<RuleWhere> listRequird = doubleList.getListRequird();
        boolean isQuantity = SimplePolicyConstants.AGG_FIELD.AGGREGATE_FIELD_QUANTITY.getValue().equals(doubleList.getQuantityOrPrice());
        if (CollectionUtils.isNotEmpty(listRequird)) {
            //每个必选条件行创建aggRule
            listRequird.forEach(o -> {
                String conditionRequire = JSON.toJSONString(Lists.newArrayList(o));
                IObjectData aggData = SimpleGeneralPricePolicyUtils.createAggData(objectData, user, doubleList.getLadderIndex(), false, ((SimplePolicyConstants.SimpleRuleWhere) o).getProductId(), ((SimplePolicyConstants.SimpleRuleWhere) o).getCategoryId(), aggregateRuleDescribe, isQuantity, Utils.SALES_ORDER_API_NAME);
                log.info("{} aggRule is {}", aggData.get(IObjectData.NAME), conditionRequire);
                //返回聚合规则对象
                aggData.set(AggregateRuleConstants.Field.CONDITION, conditionRequire);
                aggDatas.add(aggData);
            });
        }
        //创建所有条件行集合的aggRule,多阶梯只能创建一个订单产品组
        String conditionRequire = JSON.toJSONString(doubleList.getListAll());
        IObjectData aggData = SimpleGeneralPricePolicyUtils.createAggData(objectData, user, doubleList.getLadderIndex(), true, "ALL", "ALL", aggregateRuleDescribe, isQuantity, Utils.SALES_ORDER_API_NAME);
        log.info("{} aggRule is {}", aggData.get(IObjectData.NAME), conditionRequire);
        aggData.set(AggregateRuleConstants.Field.CONDITION, conditionRequire);
        aggDatas.add(aggData);
        return aggDatas;
    }


    /**
     * 针对每一个阶梯 创建必填条件rule集合和所有条件的rule结合
     *
     * @param conditions ladderIndex 通过JSON创建聚合规则的条件
     * @return {@code DoubleList}
     */
    protected SimplePolicyConstants.DoubleList createAggConditionDoubleList(List<SimplePricePolicy.ProductTableData.Condition> conditions, String ladderIndex, List<SimplePricePolicy.ProductTableData.Dealer> dealers, String isRTD) {
        //必填的规则
        List<RuleWhere> requireRules = Lists.newArrayList();
        //如果条件行只有一行,只创建所有即可
        if (CollectionUtils.isNotEmpty(conditions)) {
            conditions.stream().filter(a -> SimplePolicyConstants.REQUIREMENTS.REQUIRE.getValue().equals(a.getRequired())).forEach(i ->
                    //创建filter,经销商filter,雀巢filter
                    requireRules.add(SimpleGeneralPricePolicyUtils.createRuleWhere(i, dealers.stream().map(SimplePricePolicy.ProductTableData.Dealer::getDealerId).collect(Collectors.toList()), isRTD))
            );
        }
        //订单组
        List<RuleWhere> allRules = SimpleGeneralPricePolicyUtils.createRuleWhereUseIn(conditions, dealers.stream().map(SimplePricePolicy.ProductTableData.Dealer::getDealerId).collect(Collectors.toList()), isRTD);
        SimplePolicyConstants.DoubleList doubleList = SimplePolicyConstants.DoubleList.newArrayList();
        //条件阶梯
        doubleList.setLadderIndex(ladderIndex);
        doubleList.addRequireAll(requireRules);
        doubleList.addAll(allRules);
        return doubleList;
    }

    /**
     * 必选条件行和全部条件行
     */
    protected SimplePolicyConstants.DoubleList createAggList(SimplePricePolicy.ProductTableData simplePolicyData) {
        //阶梯不能为空
        SimplePricePolicy.ProductTableData.ConditionCategoryGift conditionCategoryGift = simplePolicyData.getConditionCategoryDifts().stream().findFirst().orElseThrow(ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_LADDER_MUST_HAVE, "simple_price_policy ladder must have"));
        //获取全局产品和分类id
        List<SimplePricePolicy.ProductTableData.Condition> conditions = simplePolicyData.getConditionGlobal();
        //获取阶梯
        String ladderIndex = conditionCategoryGift.getLadderIndex();
        //创建必选数组和所有元素数组
        SimplePolicyConstants.DoubleList aggConditionDoubleList = createAggConditionDoubleList(conditions, ladderIndex, simplePolicyData.getDealer(), simplePolicyData.getIsRTD());
        //最大数量
        aggConditionDoubleList.setTotalQuantity(conditionCategoryGift.getMinTotalQuantity());
        aggConditionDoubleList.setQuantityOrPrice(StringUtils.isBlank(simplePolicyData.getQuantityOrPrice()) ? SimplePolicyConstants.AGG_FIELD.AGGREGATE_FIELD_QUANTITY.getValue() : simplePolicyData.getQuantityOrPrice());
        return aggConditionDoubleList;
    }

    /**
     * 创建rule_condition
     */
    protected List<RuleWhere> createRuleCondition(SimplePricePolicy.ProductTableData.ConditionCategoryGift conditionCategoryGift) {
        List<RuleWhere> simplePricePolicy = Lists.newArrayList();
        String aggregate = PricePolicyConstants.FieldNameType.AGGREGATE.name().toLowerCase();
        List<RuleWhere.FiltersBean> ruleWhereFilters = Lists.newArrayList();
        List<Map<String, String>> ladderContents = conditionCategoryGift.getAggLadder();
        ladderContents.forEach(o -> {
            RuleWhere.FiltersBean filter = RuleWhere.FiltersBean.builder()
                    .fieldNameType(aggregate)
                    .objectApiName(SFAPreDefine.AggregateRule.getApiName())
                    .fieldName(o.get(DBRecord.ID))
                    .fieldValueType(aggregate)
                    .aggValueType(Boolean.parseBoolean(o.get(SimplePolicyConstants.SIMPLE_POLICY_FIELD.IS_GROUP.getApiName())) ? SimplePolicyConstants.SIMPLE_POLICY_FIELD.GROUP.getApiName() : aggregate)
                    .operator(Operator.GTE.name())
                    .fieldValues(Lists.newArrayList(o.get(SimplePolicyConstants.SIMPLE_POLICY_FIELD.QUANTITY.getApiName())))
                    .connector(Where.CONN.AND.toString())
                    .type(SimplePolicyConstants.SIMPLE_POLICY_FIELD.NUMBER.getApiName())
                    .build();
            ruleWhereFilters.add(filter);
        });
        simplePricePolicy.add(new RuleWhere(ruleWhereFilters));
        return simplePricePolicy;
    }


    /**
     * 创建ExecutionResult
     */
    protected String createExecutionResult(SimplePricePolicy.ProductTableData.ConditionCategoryGift conditionCategoryGift, String giftType, String cycleType) {
        Map<String, Object> executionResultMap = new HashMap<>();
        List<Map<String, String>> aggregateRuleMark = conditionCategoryGift.getAggLadder();
        //固定赠品/可选赠品 true为固定赠品
        boolean giftTypeBoolean = SimplePolicyConstants.FIXEDRANGE.FIXED.getValue().equals(giftType);
        //获取产品组合的id
        Map<String, String> groupAgg = aggregateRuleMark.stream().filter(o ->
                Boolean.parseBoolean(o.get(SimplePolicyConstants.SIMPLE_POLICY_FIELD.IS_GROUP.getApiName()))
        ).findFirst().orElseThrow(ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_MUST_CONTAINS_GROUP, "execution_result must contains group"));
        //判断是固定赠品还是可选赠品 true 为固定赠品
        if (giftTypeBoolean) {
            giftFixed(conditionCategoryGift, cycleType, executionResultMap, groupAgg);
        } else {
            giftRange(conditionCategoryGift, cycleType, executionResultMap, groupAgg);
        }
        executionResultMap.put(SimplePolicyConstants.EXECUTIONRESULT.OBJECT_API_NAME.getValue(), SimplePolicyConstants.SALES_ORDER_PRODUCT_API_NAME);
        executionResultMap.put(SimplePolicyConstants.EXECUTIONRESULT.OBJECT_API_NAME__S.getValue(), I18N.text(SFAI18NKeyUtil.ORDER_PRODUCT));
        executionResultMap.put(SimplePolicyConstants.EXECUTIONRESULT.TYPE.getValue(), giftTypeBoolean ? SimplePolicyConstants.EXECUTIONRESULT.FIX : SimplePolicyConstants.EXECUTIONRESULT.OPTIONAL);
        return JSON.toJSONString(executionResultMap);
    }

    private void giftFixed(SimplePricePolicy.ProductTableData.ConditionCategoryGift conditionCategoryGift, String cycleType, Map<String, Object> executionResultMap, Map<String, String> groupAgg) {
        //固定赠品
        List<SimplePricePolicy.ProductTableData.GiftList> giftLists = conditionCategoryGift.getGiftLists();
        //获取gift_list
        List<Map<String, Object>> giftDataList = SimpleGeneralPricePolicyUtils.createGiftList(giftLists, true);
        //判断是否每满 多阶梯不能有每满
        if (SimplePolicyConstants.CYCLE.CYCLE.getValue().equals(cycleType)) {
            //创建聚合规则的每满
            List<Map<String, Object>> cycleInfo = SimpleGeneralPricePolicyUtils.createCycleInfo(groupAgg.get(SimplePolicyConstants.SIMPLE_POLICY_FIELD.QUANTITY.getApiName()), groupAgg.get(DBRecord.ID), false, false, "", null,null);
            //创建ExecutionResult中的condition属性
            List<SimplePolicyConstants.RuleInfoWhere> executionResultCondition = SimpleGeneralPricePolicyUtils.createExecutionResultCondition(groupAgg);
            executionResultMap.put("cycle_info", cycleInfo);
            executionResultMap.put("condition", executionResultCondition);
        }
        executionResultMap.put("gift_list", giftDataList);
    }

    private void giftRange(SimplePricePolicy.ProductTableData.ConditionCategoryGift conditionCategoryGift,
                           String cycleType, Map<String, Object> executionResultMap, Map<String, String> groupAgg) {
        //赠品条件,可为空,如果不为空只能有一条,可选范围也可以只选固定赠品
        Optional.ofNullable(conditionCategoryGift.getGiftConditions()).ifPresent(giftConditions -> {
            SimplePricePolicy.ProductTableData.GiftCondition giftCondition = giftConditions.stream().findFirst().orElse(null);
            //没有赠品,直接返回
            if (Objects.isNull(giftCondition)) return;
            executionResultMap.put("gift_condition", SimpleGeneralPricePolicyUtils.createRuleWhereOnlyCategory(Lists.newArrayList(giftCondition.getCategoryId()), Operator.EQ.name()));
        });
        //获取固定赠品
        List<SimplePricePolicy.ProductTableData.GiftList> giftLists = conditionCategoryGift.getGiftLists();
        //如果没有固定赠品
        if (CollectionUtils.isEmpty(giftLists)) {
            executionResultMap.put("gift_list", Lists.newArrayList());
        } else {
            //获取gift_list
            List<Map<String, Object>> giftDataList = SimpleGeneralPricePolicyUtils.createGiftList(giftLists, false);
            executionResultMap.put("gift_list", giftDataList);
        }
        //判断是否每满 多阶梯不能有每满
        if (SimplePolicyConstants.CYCLE.CYCLE.getValue().equals(cycleType)) {
            List<Map<String, Object>> cycleInfo = SimpleGeneralPricePolicyUtils.createCycleInfo(conditionCategoryGift.getMinTotalQuantity(), groupAgg.get(DBRecord.ID), false, false, "", null,null);
            //创建ExecutionResult中的condition属性
            List<SimplePolicyConstants.RuleInfoWhere> executionResultCondition = SimpleGeneralPricePolicyUtils.createExecutionResultCondition(groupAgg);
            executionResultMap.put("cycle_info", cycleInfo);
            executionResultMap.put("condition", executionResultCondition);
        }
        //品类和总数量
        executionResultMap.put(SimplePolicyConstants.SIMPLE_POLICY_FIELD.GIFT_KIND_UPPER_LIMIT.getApiName(), conditionCategoryGift.getGiftKindUpperLimit());
        executionResultMap.put(SimplePolicyConstants.SIMPLE_POLICY_FIELD.GIFT_TOTAL_NUM.getApiName(), conditionCategoryGift.getGiftTotalNum());
        //多单位
        executionResultMap.put(SimplePolicyConstants.SIMPLE_POLICY_FIELD.GIFT_CONDITION_UNIT_ID.getApiName(), conditionCategoryGift.getGiftConditionUnitId());
        executionResultMap.put(SimplePolicyConstants.SIMPLE_POLICY_FIELD.GIFT_CONDITION_UNIT__S.getApiName(), conditionCategoryGift.getGiftConditionUnitS());
    }

    /**
     * 创建价格政策规则
     */
    protected List<IObjectData> createAbsPricePolicyRule(SimplePricePolicy.ProductTableData productTableDates, User user, IObjectData objectData) {
        IObjectDescribe objectPricePolicyRuleObj = serviceFacade.findObject(user.getTenantId(), SimplePolicyConstants.SIMPLE_POLIC_RULE_API_NAME);
        //获取该租户下的PricePolicyRuleObj的describe id
        String describePricePolicyRuleObjId = objectPricePolicyRuleObj.getId();
        List<IObjectData> pricePolicyRuleObjs = Lists.newArrayList();
        //创建价格政策规则
        productTableDates.getConditionCategoryDifts().forEach(o -> {
            List<RuleWhere> ruleConditionRuleWhere = createRuleCondition(o);
            String ruleCondition = JSON.toJSONString(ruleConditionRuleWhere);
            String executionResult = createExecutionResult(o, productTableDates.getGiftType(), productTableDates.getCycleStatus());
            //创建价格政策规则id
            String pricePolicyRuleId = Optional.of(o.getPricePolicyRuleId()).filter(StringUtils::isNotBlank).orElse(serviceFacade.generateId());
            o.setPricePolicyRuleId(pricePolicyRuleId);
            //普通/阶梯 true为普通
            boolean isCommon = SimplePolicyConstants.LADDERMODE.COMMON.getValue().equals(productTableDates.getLadder());
            //价格政策规则名称 如果是普通模式直接使用价格政策名称 不是价格政策名称+阶梯数
            //优先级,阶梯数越大,优先级越高,数值越小
            IObjectData pricePolicyRuleObj = SimpleGeneralPricePolicyUtils.createPricePolicyRuleObj(ruleCondition, executionResult, describePricePolicyRuleObjId,
                    isCommon ? "1" : o.getPriorityNum(),
                    isCommon ? objectData.getName() : objectData.getName() + o.getLadderIndex(), pricePolicyRuleId, "1", SimplePolicyConstants.EXPRESSIONS.COMBINATION_GIFT.getValue(), "detail", "");
            pricePolicyRuleObjs.add(pricePolicyRuleObj);
        });
        return pricePolicyRuleObjs;
    }

    /**
     * 校验简易版价格政策
     */
    protected void validateConditionCategoryGift(SimplePricePolicy.ProductTableData productTableData) {
        //判断属性不能为null和空字符串
        if (SimpleGeneralPricePolicyUtils.fieldIsEmpty(SimplePricePolicy.ProductTableData.class, productTableData)) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_ALL_ATTRIBUTE_MUST_NOT_NULL, "simple_price_policy all attribute must not null").get();
        }
        //多阶梯不能每满
        if (SimplePolicyConstants.CYCLE.CYCLE.getValue().equals(productTableData.getCycleStatus()) && SimplePolicyConstants.LADDERMODE.LADDER.getValue().equals(productTableData.getLadder())) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_CYCLE_WITHOUT_LADDER, "simple_price_policy cycle without ladder").get();
        }
        //判断阶梯不能为空
        if (CollectionUtils.isEmpty(productTableData.getConditionCategoryDifts())) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_LADDER_MUST_HAVE, "simple_price_policy ladder must have").get();
        }
        //校验阶梯
        ladderVerify(productTableData);
    }

    private void ladderVerify(SimplePricePolicy.ProductTableData productTableData) {
        //阶梯排序 小阶梯在前
        productTableData.getConditionCategoryDifts().sort((o1, o2) -> {
            Integer integer1 = Integer.valueOf(o1.getLadderIndex());
            Integer integer2 = Integer.valueOf(o2.getLadderIndex());
            return integer1 - integer2;
        });
        //限制产品和分类的数量
        if (productTableData.getConditionGlobal().size() > simplePricePolicyProductCategoryLimit) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_CONDITION_TOO_MANY, "sfa_simple_price_policy_condition_too_many").get();
        }

        //遍历阶梯
        for (int i = 0; i < productTableData.getConditionCategoryDifts().size(); i++) {
            SimplePricePolicy.ProductTableData.ConditionCategoryGift conditionCategoryGift = productTableData.getConditionCategoryDifts().get(i);

            if (conditionCategoryGift.getRequireCategoryProduct().size() > simplePricePolicyProductCategoryRequiredLimit) {
                throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_CONDITION_REQUIRE_TOO_MANY, "sfa_simple_price_policy_condition_require_too_many").get();
            }

            //判断属性不能为null和空字符串
            if (SimpleGeneralPricePolicyUtils.fieldIsEmpty(SimplePricePolicy.ProductTableData.ConditionCategoryGift.class, conditionCategoryGift)) {
                throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_ALL_ATTRIBUTE_MUST_NOT_NULL, "simple_price_policy all attribute must not null").get();
            }
            //不选产品,只选分类的时候,分类不能重复
            List<String> categoryIdWithoutProductIdList = productTableData.getConditionGlobal().stream().filter(o ->
                            StringUtils.isBlank(o.getProductId()) && !StringUtils.isBlank(o.getCategoryId())
                    ).map(SimplePricePolicy.ProductTableData.Condition::getCategoryId)
                    .collect(Collectors.toList());
            //不选产品,只选分类的时候,分类不能重复
            if (categoryIdWithoutProductIdList.size() > 1 && categoryIdWithoutProductIdList.size() > categoryIdWithoutProductIdList.stream().distinct().count()) {
                throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_ONLY_CATEGORY_MUST_NOT_REPETITION, "simple_price_policy only category must not repetition").get();
            }
            //如果是固定赠品
            if (SimplePolicyConstants.FIXEDRANGE.FIXED.getValue().equals(productTableData.getGiftType())) {
                //如果是固定赠品,固定赠品不能空
                if (CollectionUtils.isEmpty(conditionCategoryGift.getGiftLists())) {
                    throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_GIFT_FIXED_MUST_GIFTLIST, "simple_price_policy gift fixed must giftlist").get();
                }
            } else {
                //如果是可选赠品  特定赠品和条件赠品不能都为空
                if (CollectionUtils.isEmpty(conditionCategoryGift.getGiftLists()) && CollectionUtils.isEmpty(conditionCategoryGift.getGiftConditions())) {
                    throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_GIFT_OPTIONAL_MUST_GIFTLIST_OR_GIFTCONDITION, "simple_price_policy gift optional must giftlist or giftcondition ").get();
                }
            }
            //获取下一个阶梯
            SimplePricePolicy.ProductTableData.ConditionCategoryGift conditionCategoryGiftNext;
            if (i + 1 < productTableData.getConditionCategoryDifts().size()) {
                //获取下一个阶梯
                conditionCategoryGiftNext = productTableData.getConditionCategoryDifts().get(i + 1);
                //下一个阶梯的最少总数量要大于当前的数量
                if (SimpleGeneralPricePolicyUtils.compare(conditionCategoryGiftNext.getMinTotalQuantity(), Operator.LT, conditionCategoryGift.getMinTotalQuantity())) {
                    throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_NEXT_LADDER_MINTOTALQUANTITY_MUST_GTE_CURRENT, "simple_price_policy next ladder mintotalquantity must gte current").get();
                }
            }
            //校验产品条件
            conditionVerify(conditionCategoryGift);
        }
    }

    private void conditionVerify(SimplePricePolicy.ProductTableData.ConditionCategoryGift conditionCategoryGift) {
        //必选最小数量
        BigDecimal allConditionsRequireQuantity = BigDecimal.ZERO;
        for (SimplePricePolicy.ProductTableData.RequireCategoryProduct requireCategoryProduct : conditionCategoryGift.getRequireCategoryProduct()) {
            //判断属性不能为null和空字符串
            if (SimpleGeneralPricePolicyUtils.fieldIsEmpty(SimplePricePolicy.ProductTableData.RequireCategoryProduct.class, requireCategoryProduct)) {
                throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_ALL_ATTRIBUTE_MUST_NOT_NULL, "simple_price_policy all attribute must not null").get();
            }
            allConditionsRequireQuantity = allConditionsRequireQuantity.add(BigDecimal.valueOf(Double.parseDouble(requireCategoryProduct.getMinQuantity())));
        }
        //如果最少总数量小于必选最少数量之和
        if (SimpleGeneralPricePolicyUtils.compare(conditionCategoryGift.getMinTotalQuantity(), Operator.LT, allConditionsRequireQuantity)) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_SIMPLE_PRICE_POLICY_LADDER_MINTOTALQUANTITY_MUST_GTE_REQUIRE_MINQUANTITY_SUM, "simple_price_policy ladder mintotalquantity must gte require minquantity sum").get();
        }
    }

}




