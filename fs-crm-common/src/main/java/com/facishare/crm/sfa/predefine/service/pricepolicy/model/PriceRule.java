package com.facishare.crm.sfa.predefine.service.pricepolicy.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants.PricingRuleOperator;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants.SalesOrderProductField;
import com.facishare.crm.sfa.utilities.util.PricePolicyUtils;
import com.facishare.crm.util.DomainPluginDescribeExt;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

import static com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants.DATA_INDEX;
import static com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants.POLICY_DYNAMIC_AMOUNT;

/**
 * 价格规则
 *
 * <AUTHOR>
 */
public interface PriceRule {

    @Data
    class ExecutionRule {
        @JSONField(name = "object_api_name")
        @JsonProperty("object_api_name")
        private String objectApiName;
        private String condition;
        @JSONField(name = "object_api_name__s")
        @JsonProperty("object_api_name__s")
        private String objectApiName__s;
        @JSONField(name = "cycle_info")
        @JsonProperty("cycle_info")
        private List<CycleInfo> cycleInfos;
        @JSONField(name = "rule_type")
        @JsonProperty("rule_type")
        private String ruleType;
        @JSONField(name = "limit_info")
        @JsonProperty("limit_info")
        private LimitInfo limitInfo;
        @JSONField(name = "object_detail_name")
        @JsonProperty("object_detail_name")
        private String objectDetailName;
        /**
         * bom 关联信息
         */
        @JSONField(name = "bom_info")
        @JsonProperty("bom_info")
        private Map<String, List<PriceRule.BomInfo>> bomInfo;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class BomInfo implements Serializable {
        @JSONField(name = "product_id")
        @JsonProperty("product_id")
        private String productId;
        @JSONField(name = "product__r")
        @JsonProperty("product__r")
        private String productName;
        @JSONField(name = "share_rate")
        @JsonProperty("share_rate")
        private BigDecimal shareRate;
        @JSONField(name = "amount")
        @JsonProperty("amount")
        private String amount;
    }

    @Data
    @EqualsAndHashCode(callSuper=false)
    class CycleInfo {
        @JSONField(name = "field_name_type")
        @JsonProperty("field_name_type")
        private String fieldNameType;
        @JSONField(name = "object_api_name")
        @JsonProperty("object_api_name")
        private String objectApiName;
        @JSONField(name = "field_name")
        @JsonProperty("field_name")
        private String fieldName;
        @JSONField(name = "field_value")
        @JsonProperty("field_value")
        private BigDecimal fieldValue;
        @JSONField(name = "object_api_name__s")
        @JsonProperty("object_api_name__s")
        private String objectApiName__s;
        @JSONField(name = "field_name__s")
        @JsonProperty("field_name__s")
        private String fieldName__s;
        @JSONField(name = "agg_value_type")
        @JsonProperty("agg_value_type")
        private String aggValueType;
    }

    @Data
    @EqualsAndHashCode(callSuper=false)
    class PercentileInfo extends CycleInfo {

        public boolean isField() {
            return PricePolicyConstants.FieldNameType.FIELD.toString().toLowerCase().equals(super.getFieldNameType());
        }

        public boolean isAggregateRule() {
            return PricePolicyConstants.FieldNameType.AGGREGATE.toString().toLowerCase().equals(super.getFieldNameType());
        }

        public boolean isNormalAgg() {
            return PricePolicyConstants.AggregateRuleType.aggregate.equals(super.getAggValueType());
        }

        public boolean isGroupAgg() {
            return PricePolicyConstants.AggregateRuleType.group.equals(super.getAggValueType());
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class LimitInfo {
        @JSONField(name = "limit_type")
        @JsonProperty("limit_type")
        private LimitType limitType;
        @JSONField(name = "total_max")
        @JsonProperty("total_max")
        private BigDecimal totalMax;
        @JSONField(name = "each_max")
        @JsonProperty("each_max")
        private BigDecimal eachMax;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ChangeInfo {
        String leftFieldName;
        //原值
        Object originalValue;
        //变更值
        Object changeValue;
        //最终值
        Object rightValue;
    }

    /**
     * 定价规则
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class PricingRule extends ExecutionRule {
        @Builder.Default
        private List<ExecuteExpression> expressions = Lists.newArrayList();
        private String decimalPlacesApiName = "decimal_places";

        /**
         * 单品拼装
         *
         * @return List < Tuple < fieldName, 变化后结果值>>
         */
        public List<ChangeInfo> evaluate(String fieldName
                , IObjectData objectData
                , Optional<Double> cycleCount
                , Map<String, IFieldDescribe> fieldDescribe
                , Match.ExecutionParam param
                , Map<String, String> aggValueMap) {
            List<ChangeInfo> val = Lists.newArrayList();
            String priceBookPriceFieldName = PricePolicyConstants.PRICE_BOOK_PRICE;
            String policyDynamicAmountName = PricePolicyConstants.POLICY_DYNAMIC_AMOUNT;
            DomainPluginDescribeExt pluginParam = param.getPluginParam();
            if (pluginParam != null) {
                priceBookPriceFieldName = pluginParam.getDefaultDetailFieldApiName(PricePolicyConstants.PRICE_BOOK_PRICE);
                policyDynamicAmountName = pluginParam.getDefaultDetailFieldApiName(PricePolicyConstants.POLICY_DYNAMIC_AMOUNT);
            }
            for (ExecuteExpression expression : expressions) {
                BigDecimal originValue = getOriginValue(Lists.newArrayList(objectData), expression, fieldName, fieldDescribe, priceBookPriceFieldName);
                if (param.getRule().isDiscountOnDiscount()
                        && null != param.getFirstChangeInfo() && !param.getFirstChangeInfo().isEmpty()
                        && PricePolicyConstants.VirtualField.VIRTUAL_DISCOUNT.equals(expression.getLeft())) {
                    PriceRule.ChangeInfo changeInfo = param.getFirstChangeInfo().get(policyDynamicAmountName);
                    originValue = originValue.add(new BigDecimal(changeInfo.getChangeValue().toString()));
                }
                int resultValueFieldDecimalPlaces = fieldDescribe.get(expression.getActualLeft()).get(decimalPlacesApiName, Integer.class);
                ChangeInfo changeInfo = ChangeInfo.builder().leftFieldName(expression.getActualLeft())
                        .originalValue(getOriginValue1(objectData, fieldDescribe, expression))
                        .changeValue(getResultValue(calRightValue(expression, fieldDescribe, originValue, cycleCount, param.getDetailDataList(), objectData, aggValueMap)
                                , expression.getLeft(), objectData, fieldDescribe)
                                .setScale(resultValueFieldDecimalPlaces, RoundingMode.HALF_UP)
                        )
                        .build();
                changeInfo.setRightValue(new BigDecimal(changeInfo.getOriginalValue().toString())
                        .add(new BigDecimal(changeInfo.getChangeValue().toString()))
                        .setScale(resultValueFieldDecimalPlaces, RoundingMode.HALF_UP));
                val.add(changeInfo);
            }
            return val;
        }

        public List<BigDecimal> checkEvaluate(String fieldName
                , IObjectData objectData
                , Optional<Double> cycleCount
                , Map<String, IFieldDescribe> fieldDescribe
                , Map<String, String> aggValueMap) {
            List<BigDecimal> val = Lists.newArrayList();
            expressions.stream().filter(x -> !x.getLeft().endsWith("__c")).forEach(expression -> {
                BigDecimal originValue = getOriginValue(Lists.newArrayList(objectData), expression, fieldName, fieldDescribe, PricePolicyConstants.PRICE_BOOK_PRICE);
                int resultValueFieldDecimalPlaces = fieldDescribe.get(expression.getActualLeft()).get(decimalPlacesApiName, Integer.class);
                BigDecimal changeValue = getResultValue(calRightValue(expression, fieldDescribe, originValue, cycleCount, Lists.newArrayList(objectData), objectData, aggValueMap)
                        , expression.getLeft(), objectData, fieldDescribe)
                        .setScale(resultValueFieldDecimalPlaces, RoundingMode.HALF_UP);
                val.add(changeValue);
            });
            return val;
        }

        /**
         * 产品组合拼装
         *
         * @param amortizeFieldApiName:计算分摊比例的依赖字段
         * @return HashMap<dataIndex, List < Tuple < fieldName, 变化后结果值>>>
         */
        public Map<String, List<ChangeInfo>> groupEvaluate(String amortizeFieldApiName
                , Map<String, IFieldDescribe> fieldDescribe
                , List<IObjectData> objectDataList
                , Optional<Double> cycleCount, DomainPluginDescribeExt pluginParam
                , Map<String, String> aggValueMap) {
            Map<String, List<ChangeInfo>> rst = Maps.newHashMap();
            int amortizeFieldDecimalPlaces = fieldDescribe.get(amortizeFieldApiName).get(decimalPlacesApiName, Integer.class);
            String priceBookPriceFieldName = PricePolicyConstants.PRICE_BOOK_PRICE;
            if (pluginParam != null) {
                priceBookPriceFieldName = pluginParam.getDefaultDetailFieldApiName(PricePolicyConstants.PRICE_BOOK_PRICE);
            }
            IObjectData mergeData = new ObjectData();
            for (IObjectData objectData : objectDataList) {
                if (mergeData.getDescribeApiName() == null) {
                    ObjectDataExt.of(mergeData).putAll(ObjectDataExt.of(objectData).toMap());
                    mergeData.set("product_id", "");
                    mergeData.set("product_id__r", "MERGEDDATA");
                } else {
                    mergeData.set(PricePolicyConstants.QUANTITY, addBigDecimalsByStr(mergeData.get(PricePolicyConstants.QUANTITY,String.class),objectData.get(PricePolicyConstants.QUANTITY,String.class)));
                    mergeData.set(PricePolicyConstants.PRICE_BOOK_PRICE, addBigDecimalsByStr(mergeData.get(PricePolicyConstants.PRICE_BOOK_PRICE,String.class),objectData.get(PricePolicyConstants.PRICE_BOOK_PRICE,String.class)));
                    mergeData.set(PricePolicyConstants.PRICE_BOOK_SUBTOTAL, addBigDecimalsByStr(mergeData.get(PricePolicyConstants.PRICE_BOOK_SUBTOTAL,String.class),objectData.get(PricePolicyConstants.PRICE_BOOK_SUBTOTAL,String.class)));
                }
            }
            List<IObjectData> mergedDataList = Lists.newArrayList();
            mergedDataList.add(mergeData);
            for (ExecuteExpression expression : expressions) {
                //产品组合的总原值
                BigDecimal groupOriginValue = getOriginValue(objectDataList, expression, amortizeFieldApiName, fieldDescribe, priceBookPriceFieldName);
                //分摊依据中的分母
                BigDecimal amortizeDenominator = getAmortizeDenominator(objectDataList, amortizeFieldApiName, amortizeFieldDecimalPlaces);
                //待减值,举例：如配置为小计减100，该值为-100
                BigDecimal groupRightValue = calRightValue(expression, fieldDescribe, groupOriginValue, cycleCount, mergedDataList, null, aggValueMap);
                Map<String, List<ChangeInfo>> tmpRst = simpleExpressionAmortizeEvaluate(amortizeFieldApiName, fieldDescribe
                        , objectDataList, expression, amortizeDenominator, groupRightValue);
                rst.putAll(tmpRst);
            }
            return rst;
        }

        private BigDecimal addBigDecimalsByStr(String oneStr, String twoStr) {
            return addBigDecimals(
                    new BigDecimal(Optional.ofNullable(oneStr).filter(StringUtils::isNotBlank).orElse("0")),
                    new BigDecimal(Optional.ofNullable(twoStr).filter(StringUtils::isNotBlank).orElse("0")));
        }

        private BigDecimal addBigDecimals(BigDecimal oneValue, BigDecimal twoValue) {
            return oneValue.add(twoValue);
        }

        public List<BigDecimal> checkGroupEvaluate(String amortizeFieldApiName
                , Map<String, IFieldDescribe> fieldDescribe
                , List<IObjectData> objectDataList
                , Optional<Double> cycleCount
                , Map<String, String> aggValueMap) {
            List<BigDecimal> val = Lists.newArrayList();

            // 暂存不必要的数据
            List<Map<String, Object>> notUseDataList = Lists.newArrayList();
            for (int i = 0; i < objectDataList.size(); i++) {
                Map<String, Object> notUseData = Maps.newHashMap();
                IObjectData data = objectDataList.get(i);
                //  促销优惠额要设置为0 进行重新计算
                notUseData.put(POLICY_DYNAMIC_AMOUNT, data.get(POLICY_DYNAMIC_AMOUNT));
                data.set(POLICY_DYNAMIC_AMOUNT, BigDecimal.ZERO);

                for (ExecuteExpression expression : expressions) {
                    String actualLeft = expression.getActualLeft();
                    if (!StringUtils.isBlank(actualLeft) && !POLICY_DYNAMIC_AMOUNT.equals(actualLeft)) {
                        notUseData.put(actualLeft, data.get(actualLeft));
                        data.set(actualLeft, BigDecimal.ZERO);
                    }
                }
                notUseDataList.add(notUseData);
            }

            Map<String, List<ChangeInfo>> rst = groupEvaluate(amortizeFieldApiName, fieldDescribe, objectDataList, cycleCount, null, aggValueMap);
            for (Map.Entry<String, List<ChangeInfo>> entry : rst.entrySet()) {
                entry.getValue().forEach(x -> val.add((BigDecimal) x.rightValue));
            }

            // 还原数据
            for (int i = 0; i < objectDataList.size(); i++) {
                IObjectData data = objectDataList.get(i);
                Map<String, Object> notUseData = notUseDataList.get(i);
                notUseData.forEach((k, v) -> data.set(k, v));
            }

            return val;
        }

        /**
         * 整单分摊拼装
         *
         * @param amortizeFieldApiName:计算分摊比例的依赖字段
         * @return HashMap<dataIndex, List < Tuple < fieldName, 变化后结果值>>>
         */
        public Map<String, List<ChangeInfo>> wholeEvaluate(String amortizeFieldApiName
                , Map<String, IFieldDescribe> masterFieldDescribe
                , Map<String, IFieldDescribe> detailFieldDescribe
                , IObjectData masterData
                , List<IObjectData> detailDataList
                , Optional<Double> cycleCount, DomainPluginDescribeExt pluginParam
                , Map<String, String> aggValueMap) {

            Map<String, List<ChangeInfo>> rst = Maps.newHashMap();
            String priceBookPriceFieldName = PricePolicyConstants.PRICE_BOOK_PRICE;
            if (pluginParam != null) {
                priceBookPriceFieldName = pluginParam.getDefaultDetailFieldApiName(PricePolicyConstants.PRICE_BOOK_PRICE);
            }
            for (ExecuteExpression expression : expressions) {
                int amortizeFieldDecimalPlaces = detailFieldDescribe.get(amortizeFieldApiName).get(decimalPlacesApiName, Integer.class);
                //产品组合的总原值
                BigDecimal groupOriginValue = getOriginValue(Lists.newArrayList(masterData), expression, PricePolicyConstants.PRICE_BOOK_AMOUNT, masterFieldDescribe, priceBookPriceFieldName);
                //分摊依据中的分母
                BigDecimal amortizeDenominator = getAmortizeDenominator(detailDataList, amortizeFieldApiName, amortizeFieldDecimalPlaces);
                //待减值,举例：如配置为小计减100，该值为-100
                BigDecimal wholeRightValue = calRightValue(expression, masterFieldDescribe, groupOriginValue, cycleCount, Lists.newArrayList(), masterData, aggValueMap);
                Map<String, List<ChangeInfo>> tmpRst = simpleExpressionAmortizeEvaluate(amortizeFieldApiName, detailFieldDescribe
                        , detailDataList, expression, amortizeDenominator, wholeRightValue);
                rst.putAll(tmpRst);
            }
            return rst;
        }

        public List<BigDecimal> checkWholeEvaluate(String amortizeFieldApiName
                , Map<String, IFieldDescribe> masterFieldDescribe
                , Map<String, IFieldDescribe> detailFieldDescribe
                , IObjectData masterData
                , Optional<Double> cycleCount
                , List<IObjectData> detailDataList
                , Map<String, String> aggValueMap) {

            List<BigDecimal> rst = Lists.newArrayList();
            expressions.forEach(expression -> {
                int amortizeFieldDecimalPlaces = detailFieldDescribe.get(amortizeFieldApiName).get(decimalPlacesApiName, Integer.class);
                //产品组合的总原值
                BigDecimal groupOriginValue = getOriginValue(Lists.newArrayList(masterData), expression, PricePolicyConstants.PRICE_BOOK_AMOUNT, masterFieldDescribe, PricePolicyConstants.PRICE_BOOK_PRICE);
                //待减值,举例：如配置为小计减100，该值为-100
                BigDecimal wholeRightValue = calRightValue(expression, masterFieldDescribe, groupOriginValue, cycleCount, detailDataList, masterData, aggValueMap).setScale(amortizeFieldDecimalPlaces, RoundingMode.HALF_UP);
                rst.add(wholeRightValue);
            });
            return rst;
        }

        private Map<String, List<ChangeInfo>> simpleExpressionAmortizeEvaluate(String amortizeFieldApiName, Map<String, IFieldDescribe> fieldDescribe, List<IObjectData> objectDataList, ExecuteExpression expression
                , BigDecimal amortizeDenominator, BigDecimal groupRightValue) {
            Map<String, List<ChangeInfo>> rst = Maps.newHashMap();
            if (amortizeDenominator.compareTo(BigDecimal.ZERO) == 0) return rst;
            int amortizeFieldDecimalPlaces = fieldDescribe.get(amortizeFieldApiName).get(decimalPlacesApiName, Integer.class);
            //已用值
            BigDecimal usedGroupRightValue = BigDecimal.ZERO;
            //组合循环处理增减值
            List<ChangeInfo> simpleRst;
            BigDecimal tmpRightValue;
            ChangeInfo changeInfo;
            for (int i = 0, size = objectDataList.size(); i < size; i++) {
                simpleRst = Lists.newArrayList();
                //tmpRightValue=(当前data的报价小计*总共需扣减值）/报价小计sum值*
                tmpRightValue = objectDataList.get(i).get(amortizeFieldApiName, BigDecimal.class, BigDecimal.ZERO)
                        .multiply(groupRightValue).setScale(amortizeFieldDecimalPlaces, RoundingMode.HALF_UP);
                tmpRightValue = tmpRightValue.divide(amortizeDenominator, RoundingMode.HALF_UP);
                //最后一条数据兼容除不尽的情况
                if (i == objectDataList.size() - 1) {
                    tmpRightValue = groupRightValue.subtract(usedGroupRightValue);
                }
                int resultValueFieldDecimalPlaces = fieldDescribe.get(expression.getActualLeft()).get(decimalPlacesApiName, Integer.class);

                tmpRightValue = tmpRightValue.setScale(resultValueFieldDecimalPlaces, RoundingMode.HALF_UP);

                changeInfo = ChangeInfo.builder().leftFieldName(expression.getActualLeft())
                        .originalValue(getOriginValue1(objectDataList.get(i), fieldDescribe, expression))
                        .changeValue(getResultValue(tmpRightValue, expression.getLeft(), objectDataList.get(i), fieldDescribe)
                                .setScale(resultValueFieldDecimalPlaces, RoundingMode.HALF_UP))
                        .build();
                changeInfo.setRightValue(new BigDecimal(changeInfo.getOriginalValue().toString())
                        .add(new BigDecimal(changeInfo.getChangeValue().toString()))
                        .setScale(resultValueFieldDecimalPlaces, RoundingMode.HALF_UP));
                simpleRst.add(changeInfo);
                usedGroupRightValue = usedGroupRightValue.add(tmpRightValue);
                rst.put(objectDataList.get(i).get(DATA_INDEX, String.class), simpleRst);
            }
            return rst;
        }

        //获取分母值
        private BigDecimal getAmortizeDenominator(List<IObjectData> objectDataList, String amortizeFieldApiName, int amortizeFieldDecimalPlaces) {
            BigDecimal rst = BigDecimal.ZERO;
            for (IObjectData objectData : objectDataList) {
                rst = rst.add(getBigDecimal(objectData, amortizeFieldApiName, amortizeFieldDecimalPlaces));
            }
            return rst;
        }

        //获取原值信息，包括虚拟字段的处理
        private BigDecimal getOriginValue(List<IObjectData> objectDataList
                , ExecuteExpression expression
                , String virtualFieldName
                , Map<String, IFieldDescribe> fieldDescribe
                , String priceBookPriceFieldName) {
            BigDecimal rst = BigDecimal.ZERO;
            switch (expression.getLeft()) {
                case PricePolicyConstants.VirtualField.VIRTUAL_SUBTOTAL:
                case PricePolicyConstants.VirtualField.VIRTUAL_DISCOUNT:
                case PricePolicyConstants.VirtualField.VIRTUAL_AMOUNT:
                    for (IObjectData objectData : objectDataList) {
                        rst = rst.add(getBigDecimal(objectData, virtualFieldName
                                , fieldDescribe.get(virtualFieldName).get(decimalPlacesApiName, Integer.class)));
                    }
                    break;
                case PricePolicyConstants.VirtualField.VIRTUAL_PRICE:
                    //组合产品不能设置该虚拟字段
                    for (IObjectData objectData : objectDataList) {
                        rst = rst.add(getBigDecimal(objectData, priceBookPriceFieldName, fieldDescribe.get(priceBookPriceFieldName).get(decimalPlacesApiName, Integer.class)));
                    }
                    break;
                default:
                    for (IObjectData objectData : objectDataList) {
                        //自定义字段为空串 “” 处理，防止NumberFormatException
                        if (StringUtils.isBlank(objectData.get(expression.getLeft(), String.class))) {
                            objectData.set(expression.getLeft(), "0");
                        }
                        rst = rst.add(getBigDecimal(objectData, expression.getLeft()
                                , fieldDescribe.get(expression.getLeft()).get(decimalPlacesApiName, Integer.class)));
                    }
            }
            return rst;
        }

        //获取实际原值信息
        private BigDecimal getOriginValue1(IObjectData objectData, Map<String, IFieldDescribe> fieldDescribe, ExecuteExpression expression) {
            BigDecimal rst = BigDecimal.ZERO;
            switch (expression.getLeft()) {
                case PricePolicyConstants.VirtualField.VIRTUAL_SUBTOTAL:
                case PricePolicyConstants.VirtualField.VIRTUAL_DISCOUNT:
                    //组合产品不能设置该虚拟字段
                case PricePolicyConstants.VirtualField.VIRTUAL_PRICE:
                case PricePolicyConstants.VirtualField.VIRTUAL_AMOUNT:
                    int virtualFieldDecimalPlaces = fieldDescribe.get(POLICY_DYNAMIC_AMOUNT).get(decimalPlacesApiName, Integer.class);
                    rst = rst.add(getBigDecimal(objectData, POLICY_DYNAMIC_AMOUNT, virtualFieldDecimalPlaces));
                    break;
                default:
                    int fieldDecimalPlaces = fieldDescribe.get(expression.getLeft()).get(decimalPlacesApiName, Integer.class);
                    rst = rst.add(getBigDecimal(objectData, expression.getLeft(), fieldDecimalPlaces));
            }
            return rst;
        }

        //获取结果值
        private BigDecimal getResultValue(BigDecimal rightValue, String leftFieldName, IObjectData objectData, Map<String, IFieldDescribe> fieldDescribe) {
            BigDecimal rst;
            switch (leftFieldName) {
                case PricePolicyConstants.VirtualField.VIRTUAL_PRICE:
                    //处理后价格*数量
                    int scale = rightValue.scale();
                    int decimalPlaces = fieldDescribe.get(PricePolicyConstants.QUANTITY).get(decimalPlacesApiName, Integer.class);
                    //为了兼容数量传空的情况
                    if (StringUtils.isBlank(objectData.get(PricePolicyConstants.QUANTITY, String.class))) {
                        objectData.set(PricePolicyConstants.QUANTITY, null);
                    }
                    rst = rightValue.multiply(getBigDecimal(objectData, PricePolicyConstants.QUANTITY, decimalPlaces)).setScale(scale, RoundingMode.HALF_UP);
                    break;
                case PricePolicyConstants.VirtualField.VIRTUAL_DISCOUNT:
                case PricePolicyConstants.VirtualField.VIRTUAL_SUBTOTAL:
                case PricePolicyConstants.VirtualField.VIRTUAL_AMOUNT:
                default:
                    rst = rightValue;
            }
            return rst;
        }

        private BigDecimal calRightValue(ExecuteExpression expression,
                                         Map<String, IFieldDescribe> fieldDescribe,
                                         BigDecimal originValue,
                                         Optional<Double> cycleCount,
                                         List<IObjectData> detailDataList,
                                         IObjectData masterData,
                                         Map<String, String> aggValueMap) {
            int decimalPlaces = fieldDescribe.get(expression.getActualLeft()).get(decimalPlacesApiName, Integer.class);
            String rightValueStr = expression.getRight();
            //兼容right 中带有空格的情况
            if (StringUtils.isNotBlank(rightValueStr)) {
                rightValueStr = rightValueStr.trim();
            }
            BigDecimal rightValue;
            if (Objects.equals(PricePolicyConstants.RightType.FORMULA.getRightType(), expression.getRightType())) {
                BigDecimal rightValueResult = calculateRightResult(decimalPlaces, expression, detailDataList, masterData, aggValueMap);
                rightValue = rightValueResult.setScale(decimalPlaces, RoundingMode.HALF_UP);
            } else {
                rightValue = new BigDecimal(rightValueStr).setScale(decimalPlaces, RoundingMode.HALF_UP);
            }
            if (PricePolicyConstants.VirtualField.VIRTUAL_DISCOUNT.equals(expression.getLeft())) {
                //针对虚拟值【折扣】的特殊处理
                rightValue = rightValue.multiply(originValue).divide(new BigDecimal("100"), RoundingMode.HALF_UP);
            }
            PricingRuleOperator pricingRuleOperator = PricingRuleOperator.valueOf(expression.getOperator().toUpperCase());
            if (cycleCount.isPresent()) {
                rightValue = calCycleValue(pricingRuleOperator, rightValue, BigDecimal.valueOf(cycleCount.get()));
            }
            BigDecimal resultValue;
            switch (pricingRuleOperator) {
                case EQUAL:
                    resultValue = rightValue.subtract(originValue);
                    break;
                case ADD:
                    resultValue = rightValue;
                    break;
                case SUBTRACT:
                    resultValue = BigDecimal.ZERO.subtract(rightValue);
                    break;
                case MULTIPLY:
                    //折扣不允许配置该操作符
                    resultValue = (originValue.multiply(rightValue)).subtract(originValue);
                    break;
                case DIVIDE:
                    //折扣不允许配置该操作符
                    resultValue = (originValue.divide(rightValue, RoundingMode.HALF_UP)).subtract(originValue);

                    break;
                default:
                    throw new IllegalStateException("Unexpected value: " + pricingRuleOperator);
            }
            //不返回结果，只返回需要减的值
            return resultValue.setScale(decimalPlaces, RoundingMode.HALF_UP);
        }

        private BigDecimal calculateRightResult(int decimalPlaces, ExecuteExpression expression, List<IObjectData> detailDataList, IObjectData masterData, Map<String, String> aggValueMap) {
            return PricePolicyUtils.calculateDefaultVal(RequestContextManager.getContext().getUser(), decimalPlaces, expression, detailDataList, masterData, aggValueMap, this.getObjectApiName());
        }

        private BigDecimal calCycleValue(PricingRuleOperator pricingRuleOperator, BigDecimal value, BigDecimal cycleCount) {
            switch (pricingRuleOperator) {
                case EQUAL:
                    break;
                case ADD:
                case SUBTRACT:
                    int scale = value.scale();
                    value = value.multiply(cycleCount).setScale(scale, RoundingMode.HALF_UP);
                    break;
                case MULTIPLY:
                case DIVIDE:
                    value = value.pow(cycleCount.intValue());
                    break;
                default:
                    throw new IllegalStateException("Unexpected value: " + pricingRuleOperator);
            }

            return value;
        }

        private BigDecimal getBigDecimal(IObjectData data, String key, int keyDecimalPlaces) {
            return data.get(key, BigDecimal.class, BigDecimal.ZERO).setScale(keyDecimalPlaces, RoundingMode.HALF_UP);
        }
    }

    /**
     * 定价规则执行表达式
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class ExecuteExpression {
        private String left;
        private String operator;
        private String right;
        @JSONField(name = "right_type")
        @JsonProperty("right_type")
        private String rightType;
        @JSONField(name = "left__s")
        @JsonProperty("left__s")
        private String leftS;
        @JSONField(name = "right__s")
        @JsonProperty("right__s")
        private String rightS;
        @JSONField(name = "right_type__s")
        @JsonProperty("right_type__s")
        private String rightTypeS;

        //实际计算列
        public String getActualLeft() {
            switch (this.left) {
                case PricePolicyConstants.VirtualField.VIRTUAL_DISCOUNT:
                case PricePolicyConstants.VirtualField.VIRTUAL_PRICE:
                case PricePolicyConstants.VirtualField.VIRTUAL_SUBTOTAL:
                case PricePolicyConstants.VirtualField.VIRTUAL_AMOUNT:
                    return POLICY_DYNAMIC_AMOUNT;//所有适配对象均需强制使用该apiName作为促销优惠额的apiName
                default:
                    return left;
            }
        }
    }

    /**
     * 赠品规则
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class GiftRule extends ExecutionRule {
        /**
         * 存储比例信息
         */
        @JSONField(name = "percentile_info")
        @JsonProperty("percentile_info")
        private List<PercentileInfo> percentileInfos;
        /**
         * 赠送一定数量
         * 赠送一定金额
         */
        @JSONField(name = "gift_basis")
        @JsonProperty("gift_basis")
        private String giftBasis;

        private GiftType type;
        /**
         * 赠品总数量
         */
        @JSONField(name = "gift_total_num")
        @JsonProperty("gift_total_num")
        private String giftTotalNum;
        /**
         * 赠品总数量，按照比例计算的时候会进行替换
         * 此处保留原值返回给端上，方便测试和查看，无其他逻辑计算用途
         */
        @JSONField(name = "gift_total_num_source")
        @JsonProperty("gift_total_num_source")
        private String giftTotalNumSource;

        /**
         * 赠品总数量比例，仅用作前端展示
         */
        @JSONField(name = "gift_total_num_percentile")
        @JsonProperty("gift_total_num_percentile")
        private String giftTotalNumPercentile;
        /**
         * 赠品种类上限
         */
        @JSONField(name = "gift_kind_upper_limit")
        @JsonProperty("gift_kind_upper_limit")
        private Integer giftKindUpperLimit;

        @JSONField(name = "gift_list")
        @JsonProperty("gift_list")
        private List<GiftLine> giftList;

        /**
         * 每满循环次数
         */
        @JSONField(name = "cycle_count")
        @JsonProperty("cycle_count")
        private Double cycleCount;

        /**
         *
         */
        @JSONField(name = "gift_condition")
        @JsonProperty("gift_condition")
        private String giftCondition;

        /**
         * 保留前端该规则选择赠品标识,为1则保留
         */
        @JSONField(name = "hold_chose")
        @JsonProperty("hold_chose")
        private String holdChose;

        /**
         * 条件多单位枚举值
         */
        @JSONField(name = "gift_condition_unit_id")
        @JsonProperty("gift_condition_unit_id")
        private String giftConditionUnitId;

        /**
         * 条件多单位名称
         */
        @JSONField(name = "gift_condition_unit__s")
        @JsonProperty("gift_condition_unit__s")
        private String giftConditionUnitName;

        /**
         * 组合赠品的groupKey，由于多阶梯累进，需要保持阶梯低的groupKey与第一个规则相同
         */
        @JSONField(name = "group_key")
        @JsonProperty("group_key")
        private String groupKey;

        public boolean isQuantityGift() {
            return PricePolicyConstants.GiftBasisType.QUANTITY.equals(getGiftBasis()) || StringUtils.isBlank(getGiftBasis());
        }

        public boolean isAmountGift() {
            return PricePolicyConstants.GiftBasisType.AMOUNT.equals(getGiftBasis());
        }

        public void clean() {
            super.setCondition(null);
            super.setCycleInfos(null);
            super.setObjectApiName__s(null);
        }
    }

    /**
     * 赠品明细
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class GiftLine {
        @JSONField(name = "product_id")
        @JsonProperty("product_id")
        private String productId;
        @JSONField(name = "operator")
        @JsonProperty("operator")
        private String operator;
        @JSONField(name = "max_value")
        @JsonProperty("max_value")
        private BigDecimal maxValue;
        /**
         * 最大值比例
         */
        @JSONField(name = "max_value_percentile")
        @JsonProperty("max_value_percentile")
        private String maxValuePercentile;
        @JSONField(name = "product_id__s")
        @JsonProperty("product_id__s")
        private String productName;
        @JSONField(name = "min_value")
        @JsonProperty("min_value")
        private BigDecimal minValue;
        /**
         * 最小值比例
         */
        @JSONField(name = "min_value_percentile")
        @JsonProperty("min_value_percentile")
        private String minValuePercentile;
        private BigDecimal price;
        private Boolean required;
        /**
         * match 接口处理单位用 unit 和 unitName字段
         */
        private String unit;
        @JSONField(name = "unit_name")
        @JsonProperty("unit_name")
        private String unitName;
        /**
         * add action 中用unitId 和 unitIdName记录大小单位，多单位，跟随本品信息
         */
        @JSONField(name = "unit_id")
        @JsonProperty("unit_id")
        private String unitId;
        @JSONField(name = "unit__s")
        @JsonProperty("unit__s")
        private String unitIdName;
        @JSONField(name = "is_multiple_unit")
        @JsonProperty("is_multiple_unit")
        private boolean isMultipleUnit;
        /**
         * 最大值，最小值原值，按比例计算的时候会进行替换，这里返回给端上，
         * 便于进行调试和计算，无其他逻辑计算用途
         */
        @JSONField(name = "min_source_value")
        @JsonProperty("min_source_value")
        private BigDecimal minSourceValue;
        @JSONField(name = "max_source_value")
        @JsonProperty("max_source_value")
        private BigDecimal maxSourceValue;

        /**
         * 可用赠品总量
         * 表示本价格规则中当前赠品最多能赠多少，是所有客户共享的数量
         */
        @JSONField(name = "policy_max_value")
        @JsonProperty("policy_max_value")
        private BigDecimal policyMaxValue;

        /**
         * 每客户最大赠品量
         * 表示本价格规则的所有适用客户中，每一个客户最多能获得的该赠品数量
         */
        @JSONField(name = "account_max_value")
        @JsonProperty("account_max_value")
        private BigDecimal accountMaxValue;

        @JSONField(name = "is_this_product")
        @JsonProperty("is_this_product")
        private boolean isThisProduct;

        @JSONField(name = "display_name")
        @JsonProperty("display_name")
        private String displayName;

        @JSONField(name = "attribute_map")
        @JsonProperty("attribute_map")
        private Map<String, Object> attributeMap;

        @JSONField(name = "periodic_map")
        @JsonProperty("periodic_map")
        private Map<String, Object> periodicMap;

        @JSONField(name = "pricing_period")
        @JsonProperty("pricing_period")
        private String pricingPeriod;

        @JSONField(name = "pricing_cycle")
        @JsonProperty("pricing_cycle")
        private String pricingCycle;

        @JSONField(name = "pricing_rate")
        @JsonProperty("pricing_rate")
        private String pricingRate;

        @JSONField(name = "pricing_mode")
        @JsonProperty("pricing_mode")
        private String pricingMode;
    }

    /**
     * 阶梯价格规则
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class TieredPricingRule extends ExecutionRule {
        @JSONField(name = "field_name")
        @JsonProperty("field_name")
        private String fieldName;
        @JSONField(name = "tiered_list")
        @JsonProperty("tiered_list")
        private List<TieredLine> tieredList;
        @JSONField(name = "field_name__s")
        @JsonProperty("field_name__s")
        private String fieldName__s;

        /**
         * 匹配阶梯
         *
         * @param quantity
         * @return
         */
        public String matchTiered(Double quantity) {
            AtomicReference<String> resultValue = new AtomicReference<>("");
            tieredList.forEach(line -> {
                double endingQuantity = Objects.isNull(line.getEndingQuantity())
                        ? Double.MAX_VALUE : line.getEndingQuantity();
                if (quantity > line.getStartingQuantity() && quantity <= endingQuantity) {
                    resultValue.set(line.getResultValue());
                }
            });

            return resultValue.get();
        }
    }

    /**
     * 阶梯明细
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class TieredLine {
        @JSONField(name = "starting_quantity")
        @JsonProperty("starting_quantity")
        private Double startingQuantity;
        @JSONField(name = "ending_quantity")
        @JsonProperty("ending_quantity")
        private Double endingQuantity;
        @JSONField(name = "result_value")
        @JsonProperty("result_value")
        private String resultValue;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class ProgressiveInfo extends CycleInfo {
        private boolean cycleField;
        private BigDecimal useValue;
        private String groupKey;

        public static ProgressiveInfo of(CycleInfo cycleInfo) {
            ProgressiveInfo progressiveInfo = ProgressiveInfo.builder().cycleField(true).build();
            progressiveInfo.setObjectApiName(cycleInfo.getObjectApiName());
            progressiveInfo.setFieldValue(cycleInfo.getFieldValue());
            progressiveInfo.setUseValue(BigDecimal.ZERO);
            progressiveInfo.setAggValueType(cycleInfo.getAggValueType());
            progressiveInfo.setFieldName(cycleInfo.getFieldName());
            progressiveInfo.setFieldNameType(cycleInfo.getFieldNameType());
            return progressiveInfo;
        }

        public static ProgressiveInfo of(PriceRuleFilter ruleFilter) {
            ProgressiveInfo progressiveInfo = ProgressiveInfo.builder().cycleField(false).build();
            progressiveInfo.setObjectApiName(ruleFilter.getObjectApiName());
            if (CollectionUtils.isNotEmpty(ruleFilter.getFieldValues())) {
                progressiveInfo.setFieldValue(new BigDecimal(ruleFilter.getFieldValues().get(0)));
            } else {
                progressiveInfo.setFieldValue(BigDecimal.ZERO);
            }
            progressiveInfo.setUseValue(BigDecimal.ZERO);
            progressiveInfo.setAggValueType(ruleFilter.getAggValueType());
            progressiveInfo.setFieldName(ruleFilter.getFieldName());
            progressiveInfo.setFieldNameType(ruleFilter.getFieldNameType());
            return progressiveInfo;
        }

        public static String getKey(ProgressiveInfo progressiveInfo) {
            return progressiveInfo.getFieldName() + progressiveInfo.getObjectApiName();
        }

    }

    /**
     * 赠品赠送类型
     */
    enum GiftType {
        FIX,
        OPTIONAL
    }

    /**
     * 限额限量类型
     */
    enum LimitType {
        /**
         * 赠品数量
         */
        GIFT_QUANTITY(SalesOrderProductField.QUANTITY.getApiName()),
        /**
         * 赠品金额
         */
        GIFT_AMOUNT(SalesOrderProductField.GIFT_AMORTIZE_SUBTOTAL.getApiName()),
        /**
         * 本品数量
         */
        ORIGINAL_QUANTITY(SalesOrderProductField.QUANTITY.getApiName()),
        /**
         * 本品优惠额
         */
        ORIGINAL_AMOUNT();

        String aggregateApiName;

        LimitType(String aggregateApiName) {
            this.aggregateApiName = aggregateApiName;
        }

        LimitType() {
        }

        public String getAggregateApiName() {
            return aggregateApiName;
        }
    }
}
