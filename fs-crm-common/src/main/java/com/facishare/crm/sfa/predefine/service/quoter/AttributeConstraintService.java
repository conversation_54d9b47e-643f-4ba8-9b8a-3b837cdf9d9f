package com.facishare.crm.sfa.predefine.service.quoter;

import com.facishare.crm.sfa.predefine.service.quoter.model.AttributeConstraintModel;
import com.facishare.crm.sfa.utilities.constant.AttributeConstaintLinesConstants;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @描述说明：属性级联约束服务类
 * @作者：chench
 * @创建日期：2023-12-15
 */
@Service
@Slf4j
public class AttributeConstraintService {

    @Autowired
    private ServiceFacade serviceFacade;

    /**
     * 根据属性约束ID获取属性约束明细
     * @param user
     * @param attributeId
     * @return
     */
    public List<IObjectData> getAttributeConstraintLinesByAttributeConstraintId(User user, String attributeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(2000);
        query.setOffset(0);
        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName(AttributeConstaintLinesConstants.FIELD_ATTRIBUTE_CONSTRAINT_ID);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(attributeId));
        filters.add(filter);
        query.setFilters(filters);
        query.setOrders(Lists.newArrayList(new OrderBy(IObjectData.CREATE_TIME, false)));
        QueryResult<IObjectData> data =  serviceFacade.findBySearchQueryIgnoreAll(user, AttributeConstaintLinesConstants.DESC_API_NAME, query);
        return data.getData();
    }

    /**
     * 将树形结构转成列表
     * @param detailList
     * @return
     */
    public List<IObjectData> tree2List(List<AttributeConstraintModel.AttributeConstraintNode> detailList, String masterDataId) {
        List<IObjectData> list = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(detailList)) {
            detailList.parallelStream().forEach(node -> node2List(list, node, masterDataId, null, null));
        }
        return list;
    }


    /**
     * 将列表转成树形结构
     * @param detailList
     * @return
     */
    public List<AttributeConstraintModel.AttributeConstraintNode> list2MultiTree(List<IObjectData> detailList) {
        List<AttributeConstraintModel.AttributeConstraintNode> nodeTree = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(detailList)) {
            //构建一个虚拟的node,作为根节点
            ObjectDataDocument objectDataDocument = new ObjectDataDocument();
            objectDataDocument.put(DBRecord.ID, "-1");
            detailList.add(objectDataDocument.toObjectData());
            //将原来父节点的ID设置为虚拟的根节点
            detailList.parallelStream().filter(e -> !Objects.equals("-1", e.getId()) &&
                                                    StringUtils.isEmpty(e.get(AttributeConstaintLinesConstants.FIELD_PARENT_ID, String.class)))
                                        .forEach(e -> e.set(AttributeConstaintLinesConstants.FIELD_PARENT_ID, "-1"));
            AttributeConstraintModel.AttributeConstraintNode node = buildTree(documents2Nodes(detailList));
            if(node != null) {
                nodeTree = node.getChildren();
                nodeTree.forEach(e -> {
                    e.setParentId(null);
                    e.setMaxLevel(node.getMaxLevel());
                });
            }
        }
        return nodeTree;
    }

    private AttributeConstraintModel.AttributeConstraintNode buildTree(List<AttributeConstraintModel.AttributeConstraintNode> nodes) {
        AttributeConstraintModel.AttributeConstraintNode root = null;
        Map<String, AttributeConstraintModel.AttributeConstraintNode> nodeMap = nodes.parallelStream().collect(Collectors.toMap(AttributeConstraintModel.AttributeConstraintNode::getId, node -> node));
        int maxLevel = 0;
        for (AttributeConstraintModel.AttributeConstraintNode node : nodes) {
            if(maxLevel < node.getNodeLevel()) {
                maxLevel = node.getNodeLevel();
            }
            if (StringUtils.isEmpty(node.getParentId())) {
                root = node;
            } else {
                AttributeConstraintModel.AttributeConstraintNode parent = nodeMap.get(node.getParentId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(Lists.newArrayList());
                    }
                    parent.getChildren().add(node);
                }
            }
        }
        if(root != null) {
            root.setMaxLevel(maxLevel);
        }
        return root;
    }

    private List<AttributeConstraintModel.AttributeConstraintNode> documents2Nodes(List<IObjectData> datas) {
        return datas.parallelStream().map(this::document2Node).collect(Collectors.toList());
    }

    private AttributeConstraintModel.AttributeConstraintNode document2Node(IObjectData objectData) {
        AttributeConstraintModel.AttributeConstraintNode node = new AttributeConstraintModel.AttributeConstraintNode();
        node.setId(objectData.getId());
        node.setAttributeConstraintId(objectData.get(AttributeConstaintLinesConstants.FIELD_ATTRIBUTE_CONSTRAINT_ID, String.class));
        node.setNodeLevel(objectData.get(AttributeConstaintLinesConstants.FIELD_NODE_LEVEL, Integer.class, 1));
        node.setRootNodeId(objectData.get(AttributeConstaintLinesConstants.FIELD_ROOT_NODE_ID, String.class));
        node.setIsStandardAttribute(objectData.get(AttributeConstaintLinesConstants.FIELD_IS_STANDARD_ATTRIBUTE, Boolean.class, false));
        node.setIsMultiSelected(objectData.get(AttributeConstaintLinesConstants.FIELD_IS_MULTI_SELECTED, Boolean.class, false));
        node.setIsRequired(objectData.get(AttributeConstaintLinesConstants.FIELD_IS_REQUIRED, Boolean.class, false));
        node.setParentId(objectData.get(AttributeConstaintLinesConstants.FIELD_PARENT_ID, String.class));
        int nodeType = objectData.get(AttributeConstaintLinesConstants.FIELD_NODE_TYPE, Integer.class, 1);
        node.setNodeType(nodeType);
        List<String> requiredValues = objectData.get(AttributeConstaintLinesConstants.FIELD_DEFAULT_ATTR_VALUES_IDS, List.class, Lists.newArrayList());
        if(CollectionUtils.isNotEmpty(requiredValues)) {
            node.setRequiredSelected(requiredValues);
        }
        if(AttributeConstaintLinesConstants.NodeType.ATTRIBUTE.getValue() == nodeType) {
            node.setValues(Lists.newArrayList(objectData.get(AttributeConstaintLinesConstants.FIELD_ATTRIBUTE_ID, String.class)));
        } else {
            node.setValues(Lists.newArrayList(objectData.get(AttributeConstaintLinesConstants.FIELD_ATTRIBUTE_VALUE_IDS, List.class, Lists.newArrayList())));
        }
        node.setNodeLevel(objectData.get(AttributeConstaintLinesConstants.FIELD_NODE_LEVEL, Integer.class, 1));
        return node;
    }

    private void node2List(List<IObjectData> toAppendList, AttributeConstraintModel.AttributeConstraintNode node, String masterDataId, String parentId, final String rootNodeId) {
        if(node != null) {
            //兼容编辑时的逻辑
            String id = IdGenerator.get();
            String tempRootNodeId = StringUtils.isEmpty(rootNodeId) ? id : rootNodeId;
            ObjectDataDocument objectDataDocument = new ObjectDataDocument();
            objectDataDocument.put(DBRecord.ID, id);
            objectDataDocument.put(AttributeConstaintLinesConstants.FIELD_OBJECT_DESCRIBE_API_NAME, AttributeConstaintLinesConstants.DESC_API_NAME);
            objectDataDocument.put(AttributeConstaintLinesConstants.FIELD_ATTRIBUTE_CONSTRAINT_ID, masterDataId);
            objectDataDocument.put(AttributeConstaintLinesConstants.FIELD_NODE_TYPE, node.getNodeType());
            objectDataDocument.put(AttributeConstaintLinesConstants.FIELD_NODE_LEVEL, node.getNodeLevel());
            objectDataDocument.put(AttributeConstaintLinesConstants.FIELD_ROOT_NODE_ID, tempRootNodeId);
            objectDataDocument.put(AttributeConstaintLinesConstants.FIELD_PARENT_ID, parentId);
            objectDataDocument.put("record_type", "default__c");
            if(AttributeConstaintLinesConstants.NodeType.ATTRIBUTE.getValue() == node.getNodeType()) {
                objectDataDocument.put(AttributeConstaintLinesConstants.FIELD_ATTRIBUTE_ID, node.getValues().get(0));
            } else {
                objectDataDocument.put(AttributeConstaintLinesConstants.FIELD_ATTRIBUTE_VALUE_IDS, node.getValues());
            }
            //属性设置必选属性值
            if(CollectionUtils.isNotEmpty(node.getRequiredSelected())) {
                objectDataDocument.put(AttributeConstaintLinesConstants.FIELD_DEFAULT_ATTR_VALUES_IDS, node.getRequiredSelected());
            }
            objectDataDocument.put(AttributeConstaintLinesConstants.FIELD_IS_STANDARD_ATTRIBUTE, node.getIsStandardAttribute());
            objectDataDocument.put(AttributeConstaintLinesConstants.FIELD_IS_MULTI_SELECTED, node.getIsMultiSelected());
            objectDataDocument.put(AttributeConstaintLinesConstants.FIELD_IS_REQUIRED, node.getIsRequired());

            toAppendList.add(objectDataDocument.toObjectData());
            if(CollectionUtils.isNotEmpty(node.getChildren())) {
                node.getChildren().parallelStream().forEach(subNode -> node2List(toAppendList, subNode, masterDataId, id, tempRootNodeId));
            }
        }
    }

    /**
     * 检查属性、属性值、非标属性 是否有变动过（删除、作废）
     * @param datas
     */
    public void check(List<IObjectData> datas, User user) {
        if(CollectionUtils.isEmpty(datas)) {
            return;
        }
        List<String> attrIdList = Lists.newArrayList();
        List<String> nonAttrIdList = Lists.newArrayList();
        List<String> attrValueIdList = Lists.newArrayList();
        datas.stream().forEach(e -> {
            int nodeType = e.get(AttributeConstaintLinesConstants.FIELD_NODE_TYPE, Integer.class, 1);
            boolean isStandardAttr  = e.get(AttributeConstaintLinesConstants.FIELD_IS_STANDARD_ATTRIBUTE, Boolean.class, Boolean.TRUE).booleanValue();
            if(AttributeConstaintLinesConstants.NodeType.ATTRIBUTE.getValue() == nodeType && isStandardAttr) {
                //标准属性
                attrIdList.add(e.get(AttributeConstaintLinesConstants.FIELD_ATTRIBUTE_ID, String.class));
            } else if(AttributeConstaintLinesConstants.NodeType.ATTRIBUTE.getValue() == nodeType && !isStandardAttr){
                //非标属性
                nonAttrIdList.add(e.get(AttributeConstaintLinesConstants.FIELD_ATTRIBUTE_ID, String.class));
            } else if(AttributeConstaintLinesConstants.NodeType.ATTRIBUTE_VALUE.getValue() == nodeType && isStandardAttr) {
                //属性值
                attrValueIdList.addAll(e.get(AttributeConstaintLinesConstants.FIELD_ATTRIBUTE_VALUE_IDS, List.class));
            }
        });
        checkData(attrIdList, user, AttributeConstants.API_NAME, SFAI18NKeyUtil.QUOTER_ATTRIBUTE_DELETED);
        checkData(attrValueIdList, user, AttributeConstants.API_NAME_Detail, SFAI18NKeyUtil.QUOTER_ATTRIBUTE_VALUE_DELETED);
        checkData(nonAttrIdList, user, "NonstandardAttributeObj", SFAI18NKeyUtil.QUOTER_NON_ATTRIBUTE_DELETED);
    }

    private void checkData(List<String> ids, User user, String apiName, String i18nKey) {
        if(CollectionUtils.isNotEmpty(ids)) {
            IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(), apiName);
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            IFilter filter = new Filter();
            filter.setOperator(Operator.IN);
            filter.setFieldName(IObjectData.ID);
            filter.setFieldValues(ids);
            searchTemplateQuery.getFilters().add(filter);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryWithDeleted(user, objectDescribe, searchTemplateQuery);
            StringBuffer sb = new StringBuffer();
            if(CollectionUtils.isNotEmpty(queryResult.getData())) {
                queryResult.getData().stream()
                        .filter(e->e.get("is_deleted", Boolean.class, Boolean.FALSE).booleanValue())
                        .forEach(d -> sb.append(d.get("name", String.class) + ";"));
            }
            if(StringUtils.isNotBlank(sb.toString())) {
                throw new ValidateException(I18N.text(i18nKey)+":"+sb.toString());
            }
        }
    }
}
