package com.facishare.crm.sfa.predefine.service.real;

import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dataconvert.BatchFieldDataConverter;
import com.facishare.paas.appframework.metadata.dataconvert.BatchFieldDataConverterManager;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.exception.SFAErrorCode.FIND_DESC_NOT_EXIST_ERROR;

/**
 * Created by luxin on 2018/12/5.
 */
@Service
public class TransformData4ViewService {

    @Autowired
    private BatchFieldDataConverterManager fieldDataConverterManager;
    private static final Set<String> addVField = Sets.newHashSet(IFieldType.TRUE_OR_FALSE,IFieldType.SELECT_ONE);

    /**
     * 把数据转成终端或web端可展示的数据,处理 单选/多选/时间类型 但是不处理 引用类型
     */
    public List<IObjectData> batchTransformDataForView(User user, IObjectDescribe objectDescribe, List<IObjectData> objectDataList) {
        if (Objects.isNull(objectDescribe)){
            throw new SFABusinessException(FIND_DESC_NOT_EXIST_ERROR);
        }
        List<IFieldDescribe> fieldDescribeList = objectDescribe.getFieldDescribes().stream()
                .filter(o -> !IFieldType.OBJECT_REFERENCE.equals(o.getType())).collect(Collectors.toList());

        fieldDescribeList.forEach(fieldDescribe -> {
            if (addVField.contains(fieldDescribe.getType())) {
                for (IObjectData iObjectData : objectDataList) {
                    iObjectData.set(fieldDescribe.getApiName() + "__v", iObjectData.get(fieldDescribe.getApiName()));
                }
            }
            BatchFieldDataConverter fieldDataConverter = fieldDataConverterManager.getBatchFieldDataConverter(fieldDescribe.getType());
            fieldDataConverter.convertFieldData(objectDataList, fieldDescribe, user);
        });
        return objectDataList;
    }


}
