package com.facishare.crm.sfa.predefine.service.rebatecoupon;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.constants.RebateConstants;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.service.aggregatevalue.AggregateRuleDao;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants;
import com.facishare.crm.sfa.predefine.service.pricepolicy.RuleEngineLogicService;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.dao.CouponDao;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.dao.RebateDao;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.dao.RebateRuleDao;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.*;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RebateCouponQuery.Result;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RebateCouponUse.UseData;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.translate.TranslateManager;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.BigDecimalUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.CouponUtils;
import com.facishare.crm.sfa.utilities.constant.dmConstants.DmDefineConstants;
import com.facishare.crm.sfa.utilities.util.PricePolicyUtils;
import com.facishare.crm.util.DomainPluginDescribeExt;
import com.facishare.marketing.outapi.arg.ReceiveSingleCouponArg;
import com.facishare.marketing.outapi.service.OutWxCouponService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.dto.DescribeResult;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.CurrencyFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.fxiaoke.common.StopWatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
public class BaseRebateCouponServiceImpl implements BaseRebateCouponService {
    @Resource
    protected TranslateManager translateManager;
    @Resource
    protected RuleEngineLogicService engineLogicService;
    @Resource
    protected BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Resource
    protected CouponDao couponDao;
    @Resource
    protected CouponProduct couponProduct;
    @Resource
    protected ServiceFacade serviceFacade;
    @Resource
    protected AggregateRuleDao aggregateRuleDao;
    @Resource
    protected RebateDao rebateDao;
    @Resource
    protected RebateRuleDao rebateRuleDao;

    @Resource
    protected InfraServiceFacade infraServiceFacade;

    @Resource(name = "wxCouponService")
    private OutWxCouponService wxCouponService;

    @Resource
    private EIEAConverter eieaConverter;

    @Override
    public String getType() {
        return DmDefineConstants.COUPON;
    }

    @Override
    public RebateCouponConditionField.Result getConditionFields(RebateCouponConditionField.Arg arg) {
        StopWatch stopWatch = StopWatch.createStarted("getConditionFields");
        RebateCouponConditionField.Result result = RebateCouponConditionField.Result.builder().build();
        if (StringUtils.isBlank(arg.getDataId())) {
            return result;
        }

        IObjectData oldData = serviceFacade.findObjectDataIgnoreAll(arg.getUser(), arg.getDataId(), arg.getMasterObjectApiName());
        stopWatch.lap("findOldData");

        if (ObjectLifeStatus.INEFFECTIVE.getCode().equals(oldData.get(arg.getPluginParam().getFieldApiName(CouponConstants.PluginField.LIFE_STATUS), String.class))) {
            return result;
        }

        String miscContent = oldData.get(arg.getPluginParam().getFieldApiName(CouponConstants.PluginField.MISC_CONTENT), String.class);
        if (StringUtils.isEmpty(miscContent)) {
            return result;
        }

        JSONObject jsonObject = (JSONObject) JSON.parse(miscContent);
        getCouponConditionFields(arg, stopWatch, result, jsonObject);

        getRebateConditionFields(arg, stopWatch, result, oldData, jsonObject);

        log.info("RebateCouponService:{} getConditionFields：{} result:{}", arg.getRequestId(), JSON.toJSONString(arg), JSON.toJSONString(result));
        return result;
    }

    protected void getRebateConditionFields(RebateCouponConditionField.Arg arg, StopWatch stopWatch, RebateCouponConditionField.Result result, IObjectData oldData, JSONObject jsonObject) {
        JSONArray rebates = jsonObject.getJSONArray(UseData.DATA_REBATE);
        if (CollectionUtils.notEmpty(rebates)) {
            DomainPluginParam pluginParam = infraServiceFacade.findPluginParam(arg.getUser().getTenantId()
                    , arg.getMasterObjectApiName(), DmDefineConstants.REBATE);
            DomainPluginDescribeExt pluginParamExt = DomainPluginDescribeExt.of(DmDefineConstants.REBATE, pluginParam);
            stopWatch.lap("findRebatePluginParam");

            // 查询当前使用的返利单是否还有效
            List<String> dataList = getDataIds(rebates);
            List<Rebate> rebates1 = (List<Rebate>) rebateDao.getCurrUseData(arg.getUser(), dataList, true);
            stopWatch.lap("queryRebateDataList");
            Set<String> useIds = Sets.newHashSet();
            rebates1.forEach(re -> useIds.add(re.getId()));
            result.setRebateDatas(Lists.newArrayList(useIds));
            if (rebates.size() > useIds.size()) {
                result.setRebateChange(true);
            } else {
                Long now = rebateDao.getToday();
                rebates1.forEach(x -> {
                    if (now.compareTo(x.getEndDate()) > 0) result.setRebateChange(true);
                });
            }

            // 查询返利单相关字段
            String rebateRuleId = (String) oldData.get(pluginParam.getFieldMapping().get(RebateConstants.PluginField.REBATE_RULE_ID));
            List<RebateRule.Rule> rules = (List<RebateRule.Rule>) this.rebateRuleDao.getCurrUseData(arg.getUser(), Lists.newArrayList(rebateRuleId), false);
            if (CollectionUtils.empty(rules)) {
                result.setRebateChange(true);
            } else {
                RebateRule.Rule rule = rules.get(0);
                stopWatch.lap("queryRebateRuleDataList");
                getRebateFields(arg.getUser(), result.getRebateConditionField(), arg.getDetailObjectApiName(), pluginParamExt, Lists.newArrayList(rule), stopWatch);
            }
        }
    }

    @NotNull
    protected void getCouponConditionFields(RebateCouponConditionField.Arg arg, StopWatch stopWatch, RebateCouponConditionField.Result result, JSONObject jsonObject) {
        JSONArray coupons = jsonObject.getJSONArray(UseData.DATA_COUPON);

        if (CollectionUtils.notEmpty(coupons)) {
            List<String> dataList = getDataIds(coupons);
            stopWatch.lap("getCouponDataIds");

            // 查询当前使用的优惠券是否有效
            List<Coupon> coupons1 = (List<Coupon>) couponDao.getCurrUseData(getCouponObjUser(arg.getUser(), getPublicObjectInfo(arg.getUser())), dataList, true);
            stopWatch.lap("queryCouponDataList");

            Set<String> useIds = Sets.newHashSet();
            coupons1.forEach(coupon -> useIds.add(coupon.getId()));
            result.setCouponDatas(Lists.newArrayList(useIds));
            if (coupons.size() > useIds.size()) {
                result.setCouponChange(true);
            } else {
                Long now = couponDao.getToday();
                coupons1.forEach(x -> {
                    if (now.compareTo(x.getEndDate()) > 0) result.setCouponChange(true);
                });
            }

            // 查询优惠券相关字段
            getCouponField(arg.getUser(), result.getCouponConditionField(), arg.getDetailObjectApiName(), arg.getPluginParam(), coupons1, stopWatch);
            stopWatch.lap("getCouponConditionFields");
        }
    }

    private List<String> getDataIds(JSONArray coupons) {
        List<String> dataList = Lists.newArrayList();
        for (Object object : coupons) {
            JSONObject json = (JSONObject) JSON.toJSON(object);
            dataList.add(json.getString(UseData.DATA_ID));
        }
        return dataList;
    }

    protected void getCouponField(User user, Map<String, Set<String>> conditionField,
                                  String detailObjectApiName, DomainPluginDescribeExt pluginParam, List<?> couponList, StopWatch stopWatch) {
        stopWatch.lap("getCouponField");
        List<Coupon> coupons = (List<Coupon>) couponList;
        Set<String> pids = Sets.newHashSet();
        coupons.forEach(coupon -> pids.add(coupon.getCouponPlanId()));

        PricePolicyUtils.mergeFieldMap(PricePolicyUtils.getAggregateField(user, Lists.newArrayList(pids)), conditionField);
        stopWatch.lap("getCouponAggregateField");

        RebateCouponConditionField.Result.addField(conditionField, detailObjectApiName,
                pluginParam.getDefaultDetailFieldApiName(couponProduct.getAmountKey(user.getTenantId(), pluginParam.getObjectApiName())));
    }

    protected void getRebateFields(User user, Map<String, Set<String>> conditionField,
                                   String detailObjectApiName, DomainPluginDescribeExt pluginParam, List<RebateRule.Rule> rules, StopWatch stopWatch) {
        stopWatch.lap("getRebateFields");
        Set<String> aggRules = Sets.newHashSet();
        for (RebateRule.Rule rule : rules) {
            JSONObject jRuleContent = JSON.parseObject(rule.getRuleContent());
            String calculateType =
                    jRuleContent.getObject(RebateConstants.RuleContentField.CALCULATE_TYPE.getApiName(), String.class);

            if (RebateConstants.CalculateType.CYCLE.getApiName().equals(calculateType)) {
                getCycleField(conditionField, jRuleContent, aggRules);
            } else if (RebateConstants.CalculateType.EXPRESSION.getApiName().equals(calculateType)) {
                getExpressionField(conditionField, jRuleContent, aggRules);

            }
            stopWatch.lap("groupRule");
        }
        PricePolicyUtils.mergeFieldMap(PricePolicyUtils.getAggregateField(user, Lists.newArrayList(aggRules)), conditionField);
        stopWatch.lap("getAggregateField");
        RebateCouponConditionField.Result.addField(conditionField, detailObjectApiName,
                pluginParam.getDefaultDetailFieldApiName(PricePolicyConstants.PRODUCT_ID));
        RebateCouponConditionField.Result.addField(conditionField, detailObjectApiName,
                pluginParam.getDefaultDetailFieldApiName(couponProduct.getAmountKey(user.getTenantId(), pluginParam.getObjectApiName())));
    }

    private static void getExpressionField(Map<String, Set<String>> conditionField, JSONObject jRuleContent, Set<String> aggRules) {
        String expressions = jRuleContent.getObject(RebateConstants.RuleContentField.EXPRESSIONS.getApiName(), String.class);
        List<RebateRule.ExpressionData> expressionDataList = JSON.parseArray(expressions, RebateRule.ExpressionData.class);
        for (RebateRule.ExpressionData expressionData : expressionDataList) {
            if (RebateConstants.ExecuteType.CALCULATE.getApiName().equals(expressionData.getExecuteType())) {
                RebateRule.CycleInfoData.CycleDataBean.LeftBean leftBean = expressionData.getLeft();
                if (CouponUtils.isAggregateType(leftBean.getFieldNameType())) {
                    aggRules.add(leftBean.getFieldName());
                } else if (CouponUtils.isFieldType(leftBean.getFieldNameType())) {
                    RebateCouponConditionField.Result.addField(conditionField, leftBean.getObjectApiName(), leftBean.getFieldName());
                }
            }
        }
    }

    private static void getCycleField(Map<String, Set<String>> conditionField, JSONObject jRuleContent, Set<String> aggRules) {
        String cycleInfo = jRuleContent.getObject(RebateConstants.RuleContentField.CYCLE_INFO.getApiName(), String.class);
        RebateRule.CycleInfoData cycleInfoData = JSON.parseObject(cycleInfo, RebateRule.CycleInfoData.class);
        for (RebateRule.CycleInfoData.CycleDataBean cycleDatum : cycleInfoData.getCycleData()) {
            RebateRule.CycleInfoData.CycleDataBean.LeftBean cycle = cycleDatum.getLeft();
            if (CouponUtils.isAggregateType(cycle.getFieldNameType())) {
                aggRules.add(cycle.getFieldName());
            } else if (CouponUtils.isFieldType(cycle.getFieldNameType())) {
                RebateCouponConditionField.Result.addField(conditionField, cycle.getObjectApiName(), cycle.getFieldName());
            }
        }
    }


    @Override
    public Result query(RebateCouponQuery.Arg arg) {
        StopWatch stopWatch = StopWatch.createStarted("query");
        Result result = Result.builder().build();
        arg.setPublicObjInfo(getPublicObjectInfo(arg.getUser()));
        List<Coupon> couponList = (List<Coupon>) getDataList(arg, stopWatch);
        stopWatch.lap("getDataList");

        List<Coupon> canUseList = (List<Coupon>) filterData(arg, couponList, result, stopWatch);
        Set<String> ids = canUseList.stream().map(Coupon::getId).collect(Collectors.toSet());
        for (Coupon coupon : couponList) {
            if (!ids.contains(coupon.getId())) {
                coupon.setCanUse("1");
            }
        }
        sortUseCoupon(couponList);
        result.setDatas(changeResultData(couponList));
        stopWatch.lap("changeResultData");
        stopWatch.logSlow(500);
        log.info("RebateCouponService:{} getConditionFields：{} result:{}", arg.getRequestId(), JSON.toJSONString(arg), JSON.toJSONString(result));
        return result;
    }

    private RebateCouponQuery.PublicObjInfo getPublicObjectInfo(User user) {
        return CouponUtils.getPublicObjectInfo(user);
    }

    protected List<?> changeResultData(List<?> couponList) {
        return RebateCouponQuery.CouponResult.ofList((List<Coupon>) couponList);
    }

    protected List<?> filterData(RebateCouponQuery.Arg arg, List<?> couponList, Result result, StopWatch stopWatch) {
        stopWatch.lap("filterDataStart");
        Map<String, List<Coupon>> mlCoupon = Maps.newHashMap();
        Set<String> planIds = Sets.newHashSet();
        Set<String> hardPlanIds = Sets.newHashSet();
        for (Coupon coupon : (List<Coupon>) couponList) {

            List<Coupon> couponList1 = mlCoupon.computeIfAbsent(coupon.getCouponPlanId(), key -> Lists.newArrayList());

            if (CouponConstants.ProductConditionType.HARD.getValue().equals(coupon.getProductConditionType())) {
                hardPlanIds.add(coupon.getCouponPlanId());
            } else {
                planIds.add(coupon.getCouponPlanId());
            }

            couponList1.add(coupon);

        }
        stopWatch.lap("filterDataGroup");

        Map<String, List<String>> matchPlans = engineLogicService.matchRuleCondition(getCouponObjUser(arg.getUser(), arg.getPublicObjInfo()),
                Lists.newArrayList(planIds),
                arg.getMasterObjectApiName(),
                arg.getMasterData().toObjectData(),
                arg.toDetailDataList());

        stopWatch.lap("filterMatchRuleCondition");

        if (CollectionUtils.notEmpty(hardPlanIds)) {
            List<String> matchHardPl = translateManager.getTranslateService(CouponConstants.ProductConditionType.HARD.getValue()).check(CouponUtils.getCouponObjUser(arg.getUser(), arg.getPublicObjInfo()),
                    Lists.newArrayList(hardPlanIds),
                    arg.getMasterObjectApiName(),
                    arg.getMasterData().toObjectData(),
                    arg.toDetailDataList(),
                    arg.getPluginParam());

            hardPlanIds = Sets.newHashSet(matchHardPl);

            stopWatch.lap("filterMatchHard");
        }

        List<Coupon> couponFilter = Lists.newArrayList();
        for (String plan : matchPlans.keySet()) {
            List<Coupon> coupons = mlCoupon.get(plan);
            couponFilter.addAll(coupons);
        }
        for (String hardPlanId : hardPlanIds) {
            List<Coupon> coupons = mlCoupon.get(hardPlanId);
            if (CollectionUtils.notEmpty(coupons)) {
                couponFilter.addAll(coupons);
            }
        }

        stopWatch.lap("filterDataEnd");
        return couponFilter;
    }

    protected List<?> getDataList(RebateCouponQuery.Arg arg, StopWatch stopWatch) {
        List<Coupon> couponList = (List<Coupon>) couponDao.getDataByAccountId
                (getCouponObjUser(arg.getUser(), arg.getPublicObjInfo()), arg.getMasterObjectApiName(), arg.getAccountId(), arg);
        List<Coupon> currList = (List<Coupon>) getCurrOldDataList(arg, UseData.DATA_COUPON);
        if (CollectionUtils.notEmpty(currList)) {
            Set<String> mCurrList = Sets.newConcurrentHashSet();
            currList.forEach(coupon -> mCurrList.add(coupon.getId()));
            couponList.removeIf(coupon -> mCurrList.contains(coupon.getId()));

            couponList.addAll(currList);
        }
        stopWatch.lap("getDataListEnd");
        return couponList;
    }

    protected List<?> getCurrOldDataList(RebateCouponQuery.Arg arg, String key) {
        Map<String, UseData> useDataList = Maps.newHashMap();
        getOldUseData(arg, useDataList, key);
        if (useDataList.size() == 0) {
            return Lists.newArrayList();
        }

        return queryDataList(getCouponObjUser(arg.getUser(), arg.getPublicObjInfo()), Lists.newArrayList(useDataList.keySet()), useDataList, true);
    }

    protected User getCouponObjUser(User srcUser, RebateCouponQuery.PublicObjInfo publicObjInfo) {
        return CouponUtils.getCouponObjUser(srcUser, publicObjInfo);
    }

    protected List<?> queryDataList(User user, List<String> strings, Map<String, UseData> useDataList, boolean needAll) {
        return couponDao.getCurrUseData(user, strings, needAll);
    }

    protected void getOldUseData(RebateCouponQuery.Arg arg, Map<String, UseData> useDataList, String key) {
        if (StringUtils.isBlank(arg.getMasterData().getId())) {
            return;
        }
        String lifeStatus = (String) arg.getMasterData().get(arg.getPluginParam().getFieldApiName(CouponConstants.PluginField.LIFE_STATUS));
        if (ObjectLifeStatus.INEFFECTIVE.getCode().equals(lifeStatus)) {
            return;
        }

        IObjectData oldData = serviceFacade.findObjectDataIgnoreAll(arg.getUser(), arg.getMasterData().getId(), arg.getMasterObjectApiName());
        if (null == oldData) {
            return;
        }
        Object miscContent = oldData.get(arg.getPluginParam().getFieldApiName(CouponConstants.PluginField.MISC_CONTENT));
        if (null == miscContent) {
            return;
        }
        arg.setOldRuleId((String) oldData.get(arg.getPluginParam().getFieldApiName(RebateConstants.PluginField.REBATE_RULE_ID)));
        if (!arg.isEdit()) {
            arg.setEdit(true);
        }

        Map jsonObject = CouponUtils.getMiscMap(miscContent);
        getMiscContentUseData(arg, useDataList, key, jsonObject);
    }

    protected void getMiscContentUseData(RebateCouponQuery.Arg arg, Map<String, UseData> useDataList, String key, Map jsonObject) {
        if (null == jsonObject) {
            return;
        }
        List coupons = (List) jsonObject.get(key);
        if (CollectionUtils.notEmpty(coupons)) {
            for (Object object : coupons) {
                JSONObject json = (JSONObject) JSON.toJSON(object);
                getUseDataFromJson(useDataList, json);
            }
        }

        getRangeMiscContentUseData(arg, useDataList, key, jsonObject);
    }

    private void getUseDataFromJson(Map<String, UseData> useDataList, JSONObject json) {
        UseData useData = UseData.builder().id(json.getString(UseData.DATA_ID)).amount(json.getBigDecimal(UseData.DATA_AMOUNT)).name(json.getString(UseData.DATA_NAME)).quantity(json.getBigDecimal(UseData.DATA_QUANTITY)).price(json.getBigDecimal(UseData.DATA_PRICE)).build();
        useDataList.put(useData.getId(), useData);
    }

    protected void getRangeMiscContentUseData(RebateCouponQuery.Arg arg, Map<String, UseData> useDataList, String key, Map jsonObject) {
        if (!UseData.DATA_REBATE.equals(key)) {
            return;
        }
        if (CollectionUtils.notEmpty(useDataList)) {
            arg.setOldRebateIds(Sets.newHashSet(useDataList.keySet()));
        } else {
            arg.setOldRebateIds(Sets.newHashSet());
        }

        List list = (List) jsonObject.get(UseData.DATA_RANGE_REBATE);
        if (CollectionUtils.empty(list)) {
            return;
        }

        arg.setOldRangeRebateIds(Maps.newHashMap());
        for (Object object : list) {
            JSONObject rangeRebate = (JSONObject) JSON.toJSON(object);
            List rebates = (List) rangeRebate.get(UseData.DATA_RANGE_REBATES);
            if (CollectionUtils.notEmpty(list)) {
                Set<String> rebateIds = Sets.newHashSet();
                for (Object rebate : rebates) {
                    JSONObject json = (JSONObject) JSON.toJSON(rebate);
                    getUseDataFromJson(useDataList, json);
                    rebateIds.add(json.getString(UseData.DATA_ID));
                }
                arg.getOldRangeRebateIds().put(rangeRebate.getString(UseData.DATA_RANGE_RULE_ID), rebateIds);
            }
        }
    }

    @Override
    public RebateCouponMatch.Result matchAmortize(RebateCouponMatch.Arg arg, RebateCouponMatch.Result... result1) {
        StopWatch stopWatch = arg.getStopWatch();
        RebateCouponMatch.Result result = RebateCouponMatch.Result.builder().build();
        if (!bizConfigThreadLocalCacheService.isOpenCoupon(arg.getUser().getTenantId())) {
            return result;
        }
        Amortize.AmortizeKey amortizeKey = Amortize.AmortizeKey.builder()
                .amortizeKey(CouponConstants.PluginDetailField.COUPON_AMORTIZE_AMOUNT)
                .dynamicKey(CouponConstants.PluginDetailField.COUPON_DYNAMIC_AMOUNT)
                .masterAmountKey(getMasterAmountKey())
                .contentKey(getAmortizeKey()).build();
        resetAmortize(arg, result, amortizeKey);
        stopWatch.lap("resetCouponAmortize");

        List<UseData> useDataList = arg.getCouponDatas();
        if (CollectionUtils.empty(useDataList)) {
            useDataList = getUseData(arg, UseData.DATA_COUPON);
            if (CollectionUtils.empty(useDataList)) {
                return result;
            }
        }
        RebateCouponQuery.PublicObjInfo publicObjInfo = getPublicObjectInfo(arg.getUser());
        arg.setPublicObjInfo(publicObjInfo);
        List<String> useDataIds = Lists.newArrayList();
        useDataList.forEach(data -> useDataIds.add(data.getId()));
        List<?> currList = couponDao.getCurrUseData(getCouponObjUser(arg.getUser(), publicObjInfo), useDataIds, false);
        stopWatch.lap("getCouponCurrUseData");

        currList = filterUseData(arg, currList, stopWatch, publicObjInfo);
        stopWatch.lap("filterCouponData");
        if (CollectionUtils.empty(currList)) {
            result.setCouponChange(true);
            stopWatch.logSlow(500);
            return result;
        }

        amortize(arg, currList, result, amortizeKey, stopWatch);

        result.setCouponDatas(makeUseData(currList));
        result.getMiscContent().put(UseData.DATA_COUPON, result.getCouponDatas());

        result.getMasterData().put(arg.getPluginParam().getFieldApiName(CouponConstants.PluginField.MISC_CONTENT), result.getMiscContent());
        stopWatch.lap("amortizeCoupon");

        getCouponField(arg.getUser(), result.getCouponConditionField(), arg.getDetailObjectApiName(), arg.getPluginParam(), currList, stopWatch);
        stopWatch.lap("getCouponFieldEnd");

        setDetailSubtotalLessZero(arg, result, false);

        log.info("RebateCouponService:{} matchAmortize：{} result:{}", arg.getRequestId(), JSON.toJSONString(arg), JSON.toJSONString(result));
        return result;
    }

    protected List<UseData> getUseData(RebateCouponMatch.Arg arg, String useKey) {
        Map jsonObject = CouponUtils.getMiscMap(arg.getMasterData().get(arg.getPluginParam().getFieldApiName(CouponConstants.PluginField.MISC_CONTENT)));
        return getUseData(useKey, jsonObject);
    }

    @NotNull
    protected List<UseData> getUseData(String useKey, Map miscContent) {
        List<UseData> useDataTemp = Lists.newArrayList();
        if (null == miscContent) {
            return useDataTemp;
        }
        List coupons = (List) miscContent.get(useKey);
        if (CollectionUtils.empty(coupons)) {
            return useDataTemp;
        }

        for (Object object : coupons) {
            JSONObject json = (JSONObject) JSON.toJSON(object);
            useDataTemp.addAll(UseData.fromJson(json));
        }
        return useDataTemp;
    }

    protected void resetAmortize(RebateCouponMatch.Arg arg, RebateCouponMatch.Result result, Amortize.AmortizeKey amortizeKey) {
        result.getMasterData().put(amortizeKey.getMasterAmountKey(), BigDecimal.ZERO);

        arg.toDetailDataList().forEach(detail -> {
            if (isAmortize(arg, detail, amortizeKey)) {
                Map<String, Object> amortizeObj = result.getDetailDataMap().getOrDefault(detail.get(PricePolicyConstants.DATA_INDEX, String.class), Maps.newHashMap());
                amortizeObj.put(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getAmortizeKey()), BigDecimal.ZERO);
                amortizeObj.put(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getDynamicKey()), BigDecimal.ZERO);
                Map misContent = (Map) amortizeObj.getOrDefault(CouponConstants.PluginField.MISC_CONTENT,
                        detail.get(arg.getPluginParam().getDefaultDetailFieldApiName(CouponConstants.PluginField.MISC_CONTENT)));
                if (null != misContent) {
                    misContent.remove(amortizeKey.getContentKey());
                    // 返利同步清空货返数据
                    if (Amortize.AmortizeData.REBATE_AMORTIZE.equals(amortizeKey.getContentKey())) {
                        misContent.remove(Amortize.AmortizeData.PRODUCT_REBATE_AMORTIZE);
                    }
                }
                amortizeObj.put(arg.getPluginParam().getDefaultDetailFieldApiName(CouponConstants.PluginField.MISC_CONTENT), misContent);
                result.getDetailDataMap().put(detail.get(PricePolicyConstants.DATA_INDEX, String.class), amortizeObj);
            }
        });

        arg.setOpenCoupon(bizConfigThreadLocalCacheService.isOpenCoupon(arg.getUser().getTenantId()));
        arg.setOpenPricePolicy(bizConfigThreadLocalCacheService.isOpenPricePolicy(arg.getUser().getTenantId(), arg.getMasterObjectApiName()));
    }

    protected boolean isAmortize(RebateCouponMatch.Arg arg, IObjectData detail, Amortize.AmortizeKey amortizeKey) {
        boolean isAmortize = false;
        if ((BigDecimal.ZERO.compareTo(detail.get(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getAmortizeKey()), BigDecimal.class, BigDecimal.ZERO)) != 0) ||
                (BigDecimal.ZERO.compareTo(detail.get(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getDynamicKey()), BigDecimal.class, BigDecimal.ZERO)) != 0)) {
            isAmortize = true;
        }
        return isAmortize;
    }

    protected List<?> filterUseData(RebateCouponMatch.Arg arg, List<?> currList, StopWatch stopWatch, RebateCouponQuery.PublicObjInfo publicObjInfo) {
        if (arg.isEdit() || arg.isChange()) {
            RebateCouponQuery.Arg qarg = RebateCouponQuery.Arg.builder()
                    .user(arg.getUser())
                    .masterData(arg.getMasterData())
                    .detailDataMap(arg.getDetailDataMap())
                    .masterObjectApiName(arg.getMasterObjectApiName())
                    .publicObjInfo(publicObjInfo)
                    .pluginParam(arg.getPluginParam()).build();
            currList = this.filterData(qarg, currList, null, stopWatch);
        }
        return currList;
    }

    private List<UseData> makeUseData(List<?> currList) {
        List<Coupon> coupons = (List<Coupon>) currList;
        List<UseData> finalUseDataList = Lists.newArrayList();
        coupons.forEach(data -> finalUseDataList.add(UseData.builder().id(data.getId()).amount(data.getAmount())
                .name(data.getName()).useType(data.getUseType()).lowerLimit(data.getLowerLimit())
                .repelRebate(data.getRepelRebate()).repelPricePolicy(data.getRepelPricePolicy()).build()));
        return finalUseDataList;
    }

    protected void amortize(RebateCouponMatch.Arg arg, List<?> currList, RebateCouponMatch.Result result, Amortize.AmortizeKey amortizeKey, StopWatch stopWatch) {
        stopWatch.lap("amortizeStart");
        // 获取分摊字段精度
        Amortize.AmortizeDecimal amortizeDecimal = getAmortizeDecimal(arg, CouponConstants.PluginDetailField.COUPON_AMORTIZE_AMOUNT, CouponConstants.PluginDetailField.COUPON_DYNAMIC_AMOUNT);
        stopWatch.lap("getAmortizeDecimal");

        Map<String, List<Amortize.ProductData>> couponProducts = couponProduct.getProduct(currList, arg);
        stopWatch.lap("getAmortizeProduct");

        // 处理分摊
        for (Coupon coupon : (List<Coupon>) currList) {
            Amortize.AmortizeCalcData calcData = Amortize.AmortizeCalcData.of(coupon);
            calculateActualAmount(calcData, couponProducts, coupon);
            amortizeOneData(arg, result, amortizeDecimal, couponProducts, calcData, amortizeKey);
        }
    }

    /**
     * 计算实际
     *
     * @param calcData       calc数据
     * @param couponProducts 优惠产品
     * @param coupon
     */
    private void calculateActualAmount(Amortize.AmortizeCalcData calcData, Map<String, List<Amortize.ProductData>> couponProducts, Coupon coupon) {
        BigDecimal faceAmount = calcData.getAmount();
        if (faceAmount == null) {
            return;
        }
        //获取当前优惠券适用产品
        List<Amortize.ProductData> productData = couponProducts.get(calcData.getId());
        if (CollectionUtils.empty(productData)) {
            //当前优惠券适用产品为空，分摊金额为0
            calcData.setAmount(BigDecimal.ZERO);
            coupon.setAmount(BigDecimal.ZERO);
            return;
        }
        //获取当前优惠券适用产品总金额
        BigDecimal sumAmount = productData.stream()
                .filter(Objects::nonNull)
                .map(Amortize.ProductData::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //如果总金额小于面额
        if (BigDecimalUtils.compare(sumAmount, Operator.LT, faceAmount)) {
            //产品总金额作为分摊金额
            calcData.setAmount(sumAmount);
            coupon.setAmount(sumAmount);
        }
    }

    protected void amortizeOneData(RebateCouponMatch.Arg arg, RebateCouponMatch.Result result,
                                   Amortize.AmortizeDecimal amortizeDecimal, Map<String, List<Amortize.ProductData>> couponProducts,
                                   Amortize.AmortizeCalcData calcData, Amortize.AmortizeKey amortizeKey) {
        List<Amortize.ProductData> useProductList = couponProducts.get(calcData.getId()).stream().filter(x -> (null != x.getAmount())).collect(Collectors.toList());
        BigDecimal total = couponProduct.calcTotal(useProductList);
        if (CollectionUtils.notEmpty(useProductList) && useProductList.size() > 1) {
            useProductList.sort(Comparator.comparing(Amortize.ProductData::getAmount));
        }
        BigDecimal amortizeMoney = BigDecimal.ZERO;
        for (int i = 0; i < useProductList.size(); i++) {
            Amortize.ProductData productData = useProductList.get(i);
            BigDecimal amount = getAmortizeAmount(arg, result, calcData, useProductList, total, amortizeMoney, i, productData);

            if (BigDecimal.ZERO.compareTo(amount) == 0) {
                continue;
            }

            Map<String, Object> amortizeObj = result.getDetailDataMap().computeIfAbsent(productData.getId(), key -> {
                Map<String, Object> amortizeTemp = Maps.newHashMap();
                amortizeTemp.put(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getAmortizeKey()), BigDecimal.ZERO);
                amortizeTemp.put(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getDynamicKey()), BigDecimal.ZERO);
                Map<String, Object> miscContent = productData.getMiscContent();
                if (CollectionUtils.empty(miscContent)) {
                    miscContent = Maps.newLinkedHashMap();
                }
                miscContent.put(amortizeKey.getContentKey(), Lists.newArrayList());
                amortizeTemp.put(CouponConstants.PluginField.MISC_CONTENT, miscContent);
                return amortizeTemp;
            });

            addAmortizeKey(arg, amortizeKey, amortizeObj);

            if (CouponConstants.UseType.CASH.getValue().equals(calcData.getUseType())) {
                amount = amount.setScale(amortizeDecimal.getDynamicAmountDecimal(), BigDecimal.ROUND_HALF_UP);
                if (!RebateConstants.RebateType.PRODUCT.getValue().equals(calcData.getRebateType())) {
                    String masterAmountKey = arg.getPluginParam().getFieldApiName(this.getMasterAmountKey());
                    result.getMasterData().put(masterAmountKey, ((BigDecimal) result.getMasterData().getOrDefault(masterAmountKey, BigDecimal.ZERO)).subtract(amount));
                }
            } else if (CouponConstants.UseType.DISCOUNT.getValue().equals(calcData.getUseType())) {
                amount = amount.setScale(amortizeDecimal.getAmortizeAmountDecimal(), BigDecimal.ROUND_HALF_UP);
                amortizeObj.put(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getDynamicKey()),
                        ((BigDecimal) amortizeObj.get(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getDynamicKey()))).subtract(amount));
            }

            amortizeObj.put(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getAmortizeKey()),
                    ((BigDecimal) amortizeObj.get(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getAmortizeKey()))).subtract(amount));

            addMiscContent(calcData.getId(), BigDecimal.ZERO.subtract(amount), amortizeObj, amortizeKey);

            amortizeMoney = amortizeMoney.add(amount);
        }
    }

    private BigDecimal getAmortizeAmount(RebateCouponMatch.Arg arg, RebateCouponMatch.Result result, Amortize.AmortizeCalcData calcData, List<Amortize.ProductData> useProductList, BigDecimal total, BigDecimal amortizeMoney, int i, Amortize.ProductData productData) {
        BigDecimal amount;
        if (calcData.isAllUseRebate()) {
            amount = getAllRebateAmount(arg, result, calcData, useProductList, amortizeMoney, i, productData);
        } else {
            if (i == useProductList.size() - 1) {
                // 最后一个产品分摊剩余金额
                amount = calcData.getAmount().subtract(amortizeMoney);
            } else {
                if (BigDecimal.ZERO.compareTo(total) == 0) {
                    amount = BigDecimal.ZERO;
                } else {
                    amount = productData.getAmount().multiply(calcData.getAmount()).divide(total, RoundingMode.HALF_UP);
                }
            }
        }
        return amount;
    }

    private BigDecimal getAllRebateAmount(RebateCouponMatch.Arg arg, RebateCouponMatch.Result result, Amortize.AmortizeCalcData calcData, List<Amortize.ProductData> useProductList, BigDecimal amortizeMoney, int i, Amortize.ProductData productData) {
        BigDecimal amount;
        Map<String, Object> detail = result.getDetailDataMap().computeIfAbsent(productData.getId(), k -> new HashMap<>());
        BigDecimal subtotal = productData.getAmount();
        subtotal = getSubtotalNoRebateCoupon(arg, arg.isOpenCoupon(), true, detail, subtotal);
        amount = calcData.getAmount().subtract(amortizeMoney);
        if (BigDecimal.ZERO.compareTo(amount) >= 0) {
            amount = BigDecimal.ZERO;
        } else {
            if (amount.compareTo(subtotal) > 0 && i != useProductList.size() - 1) {
                amount = subtotal;
            }
        }
        return amount;
    }

    protected void addAmortizeKey(RebateCouponMatch.Arg arg, Amortize.AmortizeKey amortizeKey, Map<String, Object> amortizeObj) {
        // 优惠券不需要
    }

    protected Amortize.AmortizeDecimal getAmortizeDecimal(RebateCouponMatch.Arg arg, String amortizeKey, String dynamicKey) {
        DescribeResult describeDetail = serviceFacade.findDescribeAndLayout(arg.getUser(), arg.getDetailObjectApiName(), false, null);
        IObjectDescribe desc = describeDetail.getObjectDescribe();
        CurrencyFieldDescribe fieldDescribe1 = (CurrencyFieldDescribe) desc.getFieldDescribe(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey));
        Integer amortizeAmountDecimal = fieldDescribe1 == null ? Integer.valueOf(2) : fieldDescribe1.getDecimalPlaces();
        CurrencyFieldDescribe fieldDescribe2 = (CurrencyFieldDescribe) desc.getFieldDescribe(arg.getPluginParam().getDefaultDetailFieldApiName(dynamicKey));
        Integer dynamicAmountDecimal = fieldDescribe2 == null ? Integer.valueOf(2) : fieldDescribe2.getDecimalPlaces();

        return Amortize.AmortizeDecimal.builder().amortizeAmountDecimal(amortizeAmountDecimal).dynamicAmountDecimal(dynamicAmountDecimal).build();
    }

    protected void addMiscContent(String id, BigDecimal amount, Map<String, Object> amortizeObj, Amortize.AmortizeKey amortizeKey) {
        Amortize.AmortizeData amortizeData = Amortize.AmortizeData.builder().amount(amount).id(id).build();
        Map<String, Object> miscContent = (Map<String, Object>) amortizeObj.get(CouponConstants.PluginField.MISC_CONTENT);

        if (CollectionUtils.empty(miscContent)) {
            miscContent = Maps.newLinkedHashMap();
            miscContent.put(amortizeKey.getContentKey(), Lists.newArrayList());
            amortizeObj.put(CouponConstants.PluginField.MISC_CONTENT, miscContent);
        }

        List<Object> couponAmortize = (List<Object>) miscContent.get(amortizeKey.getContentKey());
        if (CollectionUtils.empty(couponAmortize)) {
            couponAmortize = Lists.newArrayList();
            miscContent.put(amortizeKey.getContentKey(), couponAmortize);
        }
        couponAmortize.add(amortizeData);
    }

    protected String getAmortizeKey() {
        return Amortize.AmortizeData.COUPON_AMORTIZE;
    }

    protected String getMasterAmountKey() {
        return CouponConstants.PluginField.COUPON_AMOUNT;
    }

    @Override
    public RebateCouponAutoUse.Result autoUse(RebateCouponAutoUse.Arg arg, RebateCouponMatch.Result... result2) {
        StopWatch stopWatch = StopWatch.createStarted("autoUse");
        Result resultQuery = Result.builder().build();
        arg.setPublicObjInfo(getPublicObjectInfo(arg.getUser()));
        List<Coupon> couponList = (List<Coupon>) getDataList(arg, stopWatch);
        stopWatch.lap("getDatas");

        couponList = filterRepel(arg, couponList);

        couponList = (List<Coupon>) filterData(arg, couponList, resultQuery, stopWatch);
        stopWatch.lap("filterData");
        RebateCouponAutoUse.Result result = RebateCouponAutoUse.Result.builder().build();
        if (CollectionUtils.notEmpty(couponList)) {
            Coupon coupon = useCoupon(arg, couponList);

            List<Coupon> currList = Lists.newArrayList(coupon);

            Amortize.AmortizeKey amortizeKey = Amortize.AmortizeKey.builder()
                    .amortizeKey(CouponConstants.PluginDetailField.COUPON_AMORTIZE_AMOUNT)
                    .dynamicKey(CouponConstants.PluginDetailField.COUPON_DYNAMIC_AMOUNT)
                    .masterAmountKey(getMasterAmountKey())
                    .contentKey(getAmortizeKey()).build();
            RebateCouponMatch.Arg arg1 = RebateCouponMatch.Arg.builder().user(arg.getUser())
                    .pluginParam(arg.getPluginParam()).masterObjectApiName(arg.getMasterObjectApiName())
                    .detailObjectApiName(arg.getDetailObjectApiName()).masterData(arg.getMasterData())
                    .publicObjInfo(arg.getPublicObjInfo())
                    .detailDataMap(arg.getDetailDataMap()).build();
            RebateCouponMatch.Result result1 = RebateCouponMatch.Result.builder().build();
            amortize(arg1, currList, result1, amortizeKey, stopWatch);
            result1.getMiscContent().put(UseData.DATA_COUPON, makeUseData(currList));
            result1.getMasterData().put(arg.getPluginParam().getFieldApiName(CouponConstants.PluginField.MISC_CONTENT), result1.getMiscContent());
            stopWatch.lap("amortize");
            stopWatch.logSlow(500);
            result.setDetailDataMap(result1.getDetailDataMap());
            result.setMasterData(result1.getMasterData());
        }

        return result;
    }

    private Coupon useCoupon(RebateCouponAutoUse.Arg arg, List<Coupon> couponList) {
        // 在可用券中选取 “面额 最接近【适用产品总金额】的那张券”；（因为只有进入订单那一下自动用券，此时还没有用返利和券；没有直接用“面额”最大的券，是因为存在“低于面额用券”的情况）；“面额最接近”指  差额的绝对值最小，如果向上、向下差额都一样，则优先向上取（用比产品金额大一点的那个）
        // 如果符合上述条件的有多张券（面额相同），则继续按以下顺序找：失效时间正序-->生效时间正序-->优惠券实例产生时间正序-->优惠券实例id
        sortUseCoupon(couponList);

        if (StringUtils.isNotBlank(arg.getCouponInstanceId())) {
            Optional<Coupon> coupon = couponList.stream().filter(x -> x.getId().equals(arg.getCouponInstanceId())).findFirst();
            if (coupon.isPresent()) {
                return coupon.get();
            }
        }

        Coupon coupon = couponList.get(0);
        BigDecimal orderAmount = TypeUtils.castToBigDecimal(arg.getMasterData().get(CouponConstants.PluginField.ORDER_AMOUNT));
        if (coupon.getAmount().compareTo(orderAmount) <= 0) {
            return coupon;
        }
        for (int i = 1; i < couponList.size(); i++) {
            Coupon couponNew = couponList.get(i);
            if (couponNew.getAmount().compareTo(orderAmount) >= 0) {
                if (couponNew.getAmount().compareTo(coupon.getAmount()) < 0) {
                    coupon = couponNew;
                }
            } else {
                if (couponNew.getAmount().subtract(orderAmount).abs().compareTo(coupon.getAmount().subtract(orderAmount).abs()) < 0) {
                    coupon = couponNew;
                }
                break;
            }
        }
        return coupon;
    }

    private void sortUseCoupon(List<Coupon> couponList) {
        if (CollectionUtils.empty(couponList)) return;
        Comparator<Coupon> byAmountDesc = Comparator.comparing(Coupon::getAmount).reversed();
        Comparator<Coupon> byEndDateAsc = Comparator.comparing(Coupon::getEndDate);
        Comparator<Coupon> byStartDateAsc = Comparator.comparing(Coupon::getStartDate);
        Comparator<Coupon> byCreateTimeAsc = Comparator.comparing(Coupon::getCreateTime);
        Comparator<Coupon> byIdAsc = Comparator.comparing(Coupon::getId);
        Comparator<Coupon> comparator = byAmountDesc.thenComparing(byEndDateAsc).thenComparing(byStartDateAsc).thenComparing(byCreateTimeAsc).thenComparing(byIdAsc);
        couponList.sort(comparator);
    }

    private List<Coupon> filterRepel(RebateCouponAutoUse.Arg arg, List<Coupon> couponList) {
        // 过滤互斥券
        String mPricePolicy = (String) arg.getMasterData().get(arg.getPluginParam().getFieldApiName(PricePolicyConstants.PRICE_POLICY_ID));
        boolean hasPolicy = StringUtils.isNotEmpty(mPricePolicy) || arg.toDetailDataList().stream().anyMatch(x -> StringUtils.isNotEmpty(x.get(PricePolicyConstants.PRICE_POLICY_ID, String.class)));
        if (hasPolicy) {
            couponList = couponList.stream().filter(x -> !Boolean.TRUE.equals(x.getRepelPricePolicy())).collect(Collectors.toList());
        }
        return couponList;
    }

    /**
     * 获取分摊后小计(报价小计+促销分摊金额(不含赠品分摊)+返利分摊金额+优惠券分摊)是否小于0
     *
     * @param arg             入参
     * @param openCoupon      是否开启优惠券
     * @param openPricePolicy 是否开启价格政策
     * @param data            主对象
     * @param detail          从对象
     * @return subtotal
     */
    protected BigDecimal getSubtotal(RebateCouponMatch.Arg arg, boolean openCoupon, boolean openPricePolicy, boolean openRebate, IObjectData data, Map<String, Object> detail) {
        BigDecimal subtotal = data.get(arg.getPluginParam().getDefaultDetailFieldApiName(CouponConstants.PluginDetailField.PRICE_BOOK_SUBTOTAL), BigDecimal.class);
        if (SFAPreDefine.SalesOrder.getApiName().equals(arg.getMasterObjectApiName()) && openPricePolicy) {
            subtotal = data.get(arg.getPluginParam().getDefaultDetailFieldApiName(CouponConstants.PluginDetailField.POLICY_SUBTOTAL), BigDecimal.class);
        }
        if(null == subtotal){
            subtotal =BigDecimal.ZERO;
        }

        return getSubtotalNoRebateCoupon(arg, openCoupon, openRebate, detail, subtotal);
    }

    private BigDecimal getSubtotalNoRebateCoupon(RebateCouponMatch.Arg arg, boolean openCoupon, boolean openRebate, Map<String, Object> detail, BigDecimal subtotal) {
        if (openCoupon) {
            BigDecimal amortizeCoupon = (BigDecimal) detail.get(arg.getPluginParam().getDefaultDetailFieldApiName(CouponConstants.PluginDetailField.COUPON_AMORTIZE_AMOUNT));
            subtotal = addSubtotal(subtotal, amortizeCoupon);
        }
        if (openRebate) {
            BigDecimal amortizeRebate = (BigDecimal) detail.get(arg.getPluginParam().getDefaultDetailFieldApiName(CouponConstants.PluginDetailField.REBATE_AMORTIZE_AMOUNT));
            subtotal = addSubtotal(subtotal, amortizeRebate);
        }

        return subtotal;
    }

    protected BigDecimal addSubtotal(BigDecimal subtotal, BigDecimal amortizeSubtotal) {
        if (null != amortizeSubtotal) {
            subtotal = subtotal.add(amortizeSubtotal);
        }
        return subtotal;
    }

    protected void setDetailSubtotalLessZero(RebateCouponMatch.Arg arg, RebateCouponMatch.Result result, boolean openRebate) {
        boolean openCoupon = bizConfigThreadLocalCacheService.isOpenCoupon(arg.getUser().getTenantId());
        //todo 此处需要插件化调整
        boolean openPricePolicy = bizConfigThreadLocalCacheService.isOpenPricePolicySalesOrderObj(arg.getUser().getTenantId());

        for (IObjectData data : arg.toDetailDataList()) {
            String dataIndex = data.get(PricePolicyConstants.DATA_INDEX, String.class);
            BigDecimal dynamicAmount = StringUtils.isBlank(data.get(CouponConstants.PluginDetailField.DYNAMIC_AMOUNT, String.class)) ?
                    BigDecimal.ZERO : data.get(CouponConstants.PluginDetailField.DYNAMIC_AMOUNT, BigDecimal.class);
            if (!result.getDetailDataMap().containsKey(dataIndex) || BigDecimal.ZERO.compareTo(dynamicAmount) == 0) {
                continue;
            }


            Map<String, Object> detail = result.getDetailDataMap().get(dataIndex);
            BigDecimal subtotal = getSubtotal(arg, openCoupon, openPricePolicy, openRebate, data, detail);
            subtotal = subtotal.add(dynamicAmount);
            if (BigDecimal.ZERO.compareTo(subtotal) > 0) {
                detail.put(CouponConstants.PluginDetailField.SUBTOTAL_LESS_ZERO, "1");
            }

        }
    }

    @Override
    public RebateCouponReceive.Result receive(RebateCouponReceive.Arg arg) {
        StopWatch stopWatch = StopWatch.createStarted("receive");
        RebateCouponReceive.Result result = RebateCouponReceive.Result.builder().build();

        ReceiveSingleCouponArg arg1 = new ReceiveSingleCouponArg();
        arg1.setCustomerId(arg.getAccountId());
        arg1.setObjectDataId(arg.getBatchId());
        arg1.setEa(eieaConverter.enterpriseIdToAccount(arg.getUser().getTenantIdInt()));
        com.facishare.marketing.common.result.Result<String> result1 = wxCouponService.receiveSingleCoupon(arg1);
        log.info("receiveSingleCoupon{},arg:{},result{}", arg.getRequestId(), JSON.toJSONString(arg1), JSON.toJSONString(result1));
        if (!Objects.equals(result1.getErrCode(), 0)) {
            throw new ValidateException(result1.getErrMsg());
        }
        arg.setPublicObjInfo(getPublicObjectInfo(arg.getUser()));
        String couponId = result1.getData();
        List<Coupon> couponList = (List<Coupon>) couponDao.getCurrUseData(getCouponObjUser(arg.getUser(), arg.getPublicObjInfo()), Lists.newArrayList(couponId), true);

        Result resultQuery = Result.builder().build();
        if (CollectionUtils.empty(filterData(arg, couponList, resultQuery, stopWatch))) {
            couponList.forEach(x -> x.setCanUse("1"));
        }
        result.setDatas(changeResultData(couponList));
        return result;
    }

    @Override
    public RebateUseAmount.Result canUseAmount(RebateUseAmount.Arg arg, RebateCouponMatch.Result... result1) {
        return null;
    }


    /**
     * 当强制清理互斥的优惠券时，清理优惠券
     *
     * @param arg
     * @param result
     */
    protected void cleanRepelCoupon(RebateCouponMatch.Arg arg, RebateCouponMatch.Result result) {
        if (!Boolean.TRUE.equals(arg.getCompulsoryRebate())) {
            return;
        }

        boolean hasRepelCoupon = isRepelCoupon(arg);
        if (!hasRepelCoupon) {
            return;
        }

        result.getMiscContent().remove(UseData.DATA_COUPON);

        Amortize.AmortizeKey amortizeKey = Amortize.AmortizeKey.builder()
                .amortizeKey(CouponConstants.PluginDetailField.COUPON_AMORTIZE_AMOUNT)
                .dynamicKey(CouponConstants.PluginDetailField.COUPON_DYNAMIC_AMOUNT)
                .masterAmountKey(CouponConstants.PluginField.COUPON_AMOUNT)
                .contentKey(Amortize.AmortizeData.COUPON_AMORTIZE).build();
        resetAmortize(arg, result, amortizeKey);
        result.setCouponDatas(null);
    }

    protected boolean isRepelCoupon(RebateCouponMatch.Arg arg) {
        List<UseData> useDataList = getUseData(arg, UseData.DATA_COUPON);
        boolean hasRepelCoupon = false;
        if (!CollectionUtils.empty(useDataList)) {
            hasRepelCoupon = useDataList.stream().anyMatch(x -> Boolean.TRUE.equals(x.getRepelRebate()));
        }
        return hasRepelCoupon;
    }

    @Override
    public Result queryCoupons(RebateCouponQuery.QueryCouponsArg arg) {
        StopWatch stopWatch = StopWatch.createStarted("queryCoupons");
        RebateCouponQuery.Result result = RebateCouponQuery.Result.builder().build();
        arg.setPublicObjInfo(getPublicObjectInfo(arg.getUser()));
        stopWatch.lap("setPublicObjInfo");
        List<Coupon> couponList = (List<Coupon>) couponDao.getCurrUseData(getCouponObjUser(arg.getUser(), arg.getPublicObjInfo()), arg.getCouponInstanceIds(), true);
        stopWatch.lap("getCurrUseData");
        result.setDatas(changeResultData(couponList));
        stopWatch.logSlow(100);
        return result;
    }
}
