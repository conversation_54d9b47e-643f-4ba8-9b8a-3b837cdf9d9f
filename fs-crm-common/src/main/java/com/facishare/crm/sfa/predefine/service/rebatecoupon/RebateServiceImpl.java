package com.facishare.crm.sfa.predefine.service.rebatecoupon;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.constants.RebateConstants;
import com.facishare.crm.rest.FundAccountProxy;
import com.facishare.crm.rest.dto.FundAccountModel;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.service.AvailableRangeCoreService;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.*;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RebateCouponUse.UseData;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.CouponUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.ExceptionUtils;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crm.sfa.utilities.constant.dmConstants.DmDefineConstants;
import com.facishare.crm.sfa.utilities.constant.dmConstants.PeriodicProductDm;
import com.facishare.crm.sfa.utilities.util.Price.RealPriceModel;
import com.facishare.crm.util.DomainPluginDescribeExt;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.fxiaoke.common.StopWatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants.PERIODIC_API_NAMES;

@Slf4j
@Component
public class RebateServiceImpl extends BaseRebateCouponServiceImpl {
    @Autowired
    RebateProduct rebateProduct;

    @Autowired
    AvailableRangeCoreService availableRangeCoreService;

    @Resource(name = "fundAccountProxySFA")
    private FundAccountProxy fundAccountProxy;

    @Override
    public String getType() {
        return DmDefineConstants.REBATE;
    }

    @Override
    protected List<?> changeResultData(List<?> dataList) {
        return RebateCouponQuery.RebateResult.ofList((List<Rebate>) dataList);
    }

    @Override
    public RebateCouponQuery.Result query(RebateCouponQuery.Arg arg) {
        StopWatch stopWatch = StopWatch.createStarted("query");
        RebateCouponQuery.Result result = RebateCouponQuery.Result.builder().build();
        getCanUseRule(arg, result, stopWatch);
        List<?> rebateList = getDataList(arg, stopWatch);
        stopWatch.lap("getDataList");

        rebateList = filterData(arg, rebateList, result, stopWatch);
        if (CollectionUtils.notEmpty(rebateList)) {
            List<Rebate> rebates = (List<Rebate>) rebateList;
            rebates.sort(Comparator.comparing(Rebate::getEndDate));
        }

        result.setDatas(changeResultData(rebateList));

        getRangeData(arg, stopWatch, result, (List<Rebate>) rebateList);

        stopWatch.lap("filterData");
        stopWatch.logSlow(500);
        log.info("RebateCouponService:{} getConditionFields：{} result:{}", arg.getRequestId(), JSON.toJSONString(arg), JSON.toJSONString(result));
        return result;
    }

    private boolean hasRebateCondition(RebateRule.Rule rule) {
        boolean hasRebateCondition = false;
        if (null != rule && StringUtils.isNotBlank(rule.getRebateCondition()) && CollectionUtils.notEmpty(rebateDao.getWheres(rule.getRebateCondition()))) {
            hasRebateCondition = true;
        }
        return hasRebateCondition;
    }


    private void getRangeData(RebateCouponQuery.Arg arg, StopWatch stopWatch, RebateCouponQuery.Result result, List<Rebate> rebateList) {
        Map<String, Set<String>> ruleIdToDataIndexes = filterRangRule(arg, result);
        List<String> rangeRuleIds = arg.getRangeRuleIds();
        if (CollectionUtils.empty(rangeRuleIds) && CollectionUtils.notEmpty(arg.getOldRangeRebateIds())) {

            rangeRuleIds = Lists.newArrayList(arg.getOldRangeRebateIds().keySet());
        }
        if (CollectionUtils.notEmpty(rangeRuleIds) && CollectionUtils.notEmpty(arg.getRangeRules())) {
            Map<String, RebateRule.Rule> canUseRuleMap = arg.getRangeRules().stream().collect(Collectors.toMap(RebateRule.Rule::getId, x -> x));
            List<Rebate> dataListAll = rebateList;
            if (hasRebateCondition(arg.getRule()) || CollectionUtils.empty(rebateList)) {
                arg.setRule(null);
                dataListAll = (List<Rebate>) rebateDao.getDataByAccountId(arg.getUser(), arg.getMasterObjectApiName(), arg.getAccountId(), arg);
                dataListAll = filterDataByRule(arg, dataListAll, arg.getRangeRules().get(0), stopWatch);
            }
            List<Rebate> rangeData = Lists.newArrayList();

            RebateCouponMatch.Arg matchArg = RebateCouponMatch.Arg.builder().user(arg.getUser()).detailDataList(arg.getDetailDataList()).masterObjectApiName(arg.getMasterObjectApiName()).build();
            Map<String, List<Amortize.ProductData>> rebateProducts = rebateProduct.getProduct(dataListAll, matchArg);

            List<String> rangeRuleIdsResult = Lists.newArrayList();
            for (String rangeRuleId : rangeRuleIds) {
                Set<String> dataIndexes = ruleIdToDataIndexes.get(rangeRuleId);
                RebateRule.Rule rule = canUseRuleMap.get(rangeRuleId);
                if (CollectionUtils.empty(dataIndexes) || null == rule) {
                    continue;
                }

                List<Rebate> dataList = Lists.newArrayList(dataListAll);
                dataList = filterRuleRebates(arg, rule, dataList);

                addCurrRangeData(arg, rangeRuleId, dataList);

                BigDecimal limitMoney = calcLimitMoney(arg, rule, false);
                List<Rebate> canUseRebate = filterRangeRebate(rebateProducts, dataIndexes, dataList);
                List<String> rebateIds = canUseRebate.stream().map(Rebate::getId).collect(Collectors.toList());
                rangeData.addAll(canUseRebate);
                result.getRangeRules().forEach(x -> {
                    if (x.getId().equals(rangeRuleId)) {
                        x.setLimitMoney(limitMoney);
                        x.setRangeRebateIds(rebateIds);
                        rangeRuleIdsResult.add(rangeRuleId);
                    }
                });

            }

            result.setRangeRuleIds(rangeRuleIdsResult);
            List<RebateCouponQuery.RebateResult> rangeDataList = makeRangeData(result, rangeData);
            result.setRangeData(rangeDataList);
        }
    }

    private List<Rebate> filterRuleRebates(RebateCouponQuery.Arg arg, RebateRule.Rule rule, List<Rebate> dataList) {
        if (hasRebateCondition(rule)) {
            arg.setRule(rule);
            arg.setNeedThisZero(true);
            List<Rebate> dataListTempDb = (List<Rebate>) rebateDao.getDataByAccountId(arg.getUser(), arg.getMasterObjectApiName(), arg.getAccountId(), arg);
            Set<String> tempIds = dataListTempDb.stream().map(Rebate::getId).collect(Collectors.toSet());
            dataList = dataList.stream().filter(x -> tempIds.contains(x.getId())).collect(Collectors.toList());
            arg.setNeedThisZero(false);
        }
        return dataList;
    }

    private void addCurrRangeData(RebateCouponQuery.Arg arg, String rangeRuleId, List<Rebate> dataList) {
        List<Rebate> currList = Lists.newArrayList();
        List<Rebate> oldRangeRebates = arg.getOldRangeRebates();
        if (CollectionUtils.notEmpty(oldRangeRebates) && CollectionUtils.notEmpty(arg.getOldRangeRebateIds())
                && CollectionUtils.notEmpty(arg.getOldRangeRebateIds().get(rangeRuleId))) {
            Set<String> oldRebateIds = arg.getOldRangeRebateIds().get(rangeRuleId);
            oldRangeRebates.forEach(rebate -> {
                if (oldRebateIds.contains(rebate.getId())) currList.add(rebate);
            });
        }
        addCurrData(dataList, currList);
    }

    @NotNull
    private List<Rebate> filterRangeRebate(Map<String, List<Amortize.ProductData>> rebateProducts, Set<String> dataIndexes, List<Rebate> dataList) {

        List<Rebate> canUseRebate = Lists.newArrayList();
        for (Rebate rebate : dataList) {
            if (RebateConstants.ProductConditionType.ALL.getValue().equals(rebate.getProductConditionType())) {
                canUseRebate.add(rebate);
            } else {
                List<Amortize.ProductData> productDataList = rebateProducts.get(rebate.getId());
                for (Amortize.ProductData productData : productDataList) {
                    if (dataIndexes.contains(productData.getId())) {
                        canUseRebate.add(rebate);
                        break;
                    }
                }
            }

        }
        return canUseRebate;
    }

    private Map<String, Set<String>> filterRangRule(RebateCouponQuery.Arg arg, RebateCouponQuery.Result result) {
        List<RebateRule.Rule> rangeRules = arg.getRangeRules();
        Map<String, Set<String>> ruleIdToDataIndexes = Maps.newHashMap();
        if (CollectionUtils.empty(rangeRules)) {
            return ruleIdToDataIndexes;
        }

        Map<String, String> ruleIdToAggId = Maps.newHashMap();

        for (RebateRule.Rule rule : rangeRules) {
            if (RebateConstants.HAS_PRODUCT_RANGE.equals(rule.getProductRangeType())) {
                JSONObject jsonObject = JSON.parseObject(rule.getRuleContent());
                JSONArray masterConditionObject = ExceptionUtils.trySupplier(() -> jsonObject.getJSONArray(RebateConstants.RuleContentField.MASTER_CONDITION.getApiName()));
                List<RuleWhere> ruleWheres = JSON.parseArray(masterConditionObject.toString(), RuleWhere.class);
                for (RuleWhere where : ruleWheres) {
                    List<RuleWhere.FiltersBean> filters = where.getFilters();
                    for (RuleWhere.FiltersBean filter : filters) {
                        if (RebateConstants.REBATE_RANGE.equals(filter.getAggValueType())) {
                            ruleIdToAggId.put(rule.getId(), filter.getFieldName());
                        }
                    }
                }
            }
        }
        Map<String, Set<String>> aggIdToDataIndexes = engineLogicService.getProdIDsForAggregateValues(arg.getUser(), Sets.newHashSet(ruleIdToAggId.values()), arg.getMasterObjectApiName(), arg.getDetailDataList());


        for (RebateRule.Rule rule : rangeRules) {
            String aggId = ruleIdToAggId.get(rule.getId());
            Set<String> indexes = aggIdToDataIndexes.get(aggId);
            if (CollectionUtils.notEmpty(indexes)) {
                ruleIdToDataIndexes.put(rule.getId(), indexes);
            }
        }

        if (rangeRules.size() > aggIdToDataIndexes.size()) {
            arg.setRangeRules(rangeRules.stream().filter(x -> ruleIdToDataIndexes.containsKey(x.getId())).collect(Collectors.toList()));
            if (result.getRangeRules().size() > arg.getRangeRules().size()) {
                result.setRangeRules(RebateCouponQuery.RuleResult.ofList(arg.getRangeRules()));
            }
        }

        return ruleIdToDataIndexes;
    }

    @NotNull
    private List<RebateCouponQuery.RebateResult> makeRangeData(RebateCouponQuery.Result result, List<Rebate> rangeData) {
        Set<String> dataIds = ((List<RebateCouponQuery.RebateResult>) result.getDatas()).stream().map(RebateCouponQuery.RebateResult::getId).collect(Collectors.toSet());
        Set<String> useIds = Sets.newHashSet();
        List<RebateCouponQuery.RebateResult> rangeDataList = Lists.newArrayList();
        for (int i = rangeData.size() - 1; i >= 0; i--) {
            Rebate rebate = rangeData.get(i);
            if (useIds.contains(rebate.getId())) {
                // 去重
                rangeData.remove(i);
                continue;
            }
            useIds.add(rebate.getId());
            if (dataIds.contains(rebate.getId())) {
                rangeData.remove(i);
                rangeDataList.add(RebateCouponQuery.RebateResult.builder().fromDatasId(rebate.getId()).fundAccountId(rebate.getFundAccountId()).build());
            }
        }
        rangeDataList.addAll(((List<RebateCouponQuery.RebateResult>) changeResultData(rangeData)));
        return rangeDataList;
    }


    @Override
    protected List<?> getDataList(RebateCouponQuery.Arg arg, StopWatch stopWatch) {
        RebateRule.Rule rule = getUseRule(arg);
        arg.setRule(rule);

        List<Rebate> dataList = (List<Rebate>) rebateDao.getDataByAccountId(arg.getUser(), arg.getMasterObjectApiName(), arg.getAccountId(), arg);
        List<Rebate> currList = (List<Rebate>) getCurrOldDataList(arg, getUseDataKey(arg));
        if (StringUtils.isNotBlank(arg.getFundAccountId())) {
            currList = currList.stream().filter(x -> arg.getFundAccountId().equals(x.getFundAccountId())).collect(Collectors.toList());
        }
        if (UseData.DATA_REBATE.equals(getUseDataKey(arg))) {
            Set<String> dataIds = dataList.stream().map(Rebate::getId).collect(Collectors.toSet());
            List<Rebate> oldMoneyRebate = Lists.newArrayList();
            List<Rebate> oldRangeMoneyRebate = Lists.newArrayList();
            for (Rebate rebate : currList) {
                if (arg.getOldRebateIds().contains(rebate.getId())) {
                    oldMoneyRebate.add(rebate);
                } else {
                    if (dataIds.contains(rebate.getId())) {
                        oldMoneyRebate.add(rebate);
                    }
                    oldRangeMoneyRebate.add(rebate);
                }
            }
            arg.setOldRangeRebates(oldRangeMoneyRebate);
            currList = oldMoneyRebate;
        }
        addCurrData(dataList, currList);
        setProductPrice(arg, dataList);
        setProductDisplayName(arg, dataList);
        stopWatch.lap("getDataListRebate");
        return dataList;
    }

    private void setProductDisplayName(RebateCouponQuery.Arg arg, List<Rebate> dataList) {
        if (!RebateConstants.RebateType.PRODUCT.getValue().equals(arg.getRebateType())) {
            return;
        }
        Set<String> productIds = Sets.newHashSet();
        for (Rebate rebate : dataList) {
            if (RebateConstants.ProductConditionType.FIXED.getValue().equals(rebate.getProductRangeType())) {
                Map productRange = (Map) rebate.getProductRange();
                List products = (List) productRange.get("data");
                for (Object productObj : products) {
                    Map product = (Map) productObj;
                    String productId = product.get("product_id").toString();
                    productIds.add(productId);
                }
            }
        }
        boolean isOpenPeriodicProduct = bizConfigThreadLocalCacheService.openPeriodicProduct(arg.getUser().getTenantId());
        Optional.ofNullable(serviceFacade.findObjectDataByIds(arg.getUser().getTenantId(), Lists.newArrayList(productIds), SFAPreDefine.Product.getApiName()))
                .filter(CollectionUtils::notEmpty)
                .ifPresent(x -> {
                    Map<String, IObjectData> productIdToData = x.stream().collect(Collectors.toMap(IObjectData::getId, d -> d, (v1, v2) -> v2));
                    for (Rebate rebate : dataList) {
                        if (RebateConstants.ProductConditionType.FIXED.getValue().equals(rebate.getProductRangeType())) {
                            Map productRange = (Map) rebate.getProductRange();
                            List products = (List) productRange.get("data");
                            for (Object productObj : products) {
                                Map product = (Map) productObj;
                                String productId = product.get("product_id").toString();
                                IObjectData data = productIdToData.get(productId);
                                if (null != data) {
                                    if (isOpenPeriodicProduct) {
                                        Map<String, Object> periodicMap = Maps.newHashMap();
                                        for (String fieldName : PERIODIC_API_NAMES) {
                                            periodicMap.put(fieldName, data.get(fieldName));
                                        }
                                        if(ProductConstants.PricingModeEnum.CYCLE.getValue().equals(periodicMap.get(ProductConstants.PRICING_MODE))){
                                            periodicMap.put(PeriodicProductDm.DetailField.SERVICE_START_TIME, LocalDate.now().atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli());
                                        }
                                        product.put("periodic_map", periodicMap);
                                    }
                                    if (StringUtils.isNotBlank(data.getDisplayName())) {
                                        product.put(IObjectData.DISPLAY_NAME, data.getDisplayName());
                                    }
                                }

                            }
                        }
                    }
                });

    }

    private void addCurrData(List<Rebate> dataList, List<Rebate> currList) {
        if (CollectionUtils.notEmpty(currList)) {
            // 查询的所有返利单包含已经使用的返利单，先移除，然后添加处理后的已使用返利单。
            Set<String> mCurrList = Sets.newConcurrentHashSet();
            currList.forEach(rebate -> mCurrList.add(rebate.getId()));
            dataList.removeIf(rebate -> mCurrList.contains(rebate.getId()));

            dataList.addAll(currList);
        }
    }

    /**
     * 产品返利设置产品信息
     *
     * @param arg      入参
     * @param dataList 返利单
     */
    private void setProductPrice(RebateCouponQuery.Arg arg, List<Rebate> dataList) {
        if (!RebateConstants.RebateType.PRODUCT.getValue().equals(arg.getRebateType())) {
            return;
        }

        List<Rebate> fixRebate = Lists.newArrayList();
        List<RealPriceModel.FullProduct> fullProductList = Lists.newArrayList();
        String priceBookId = (String) arg.getMasterData().get(arg.getPluginParam().getFieldApiName(CouponConstants.PluginField.PRICE_BOOK_ID));
        Set<String> productIds = Sets.newHashSet();
        for (Rebate rebate : dataList) {
            if (RebateConstants.ProductConditionType.FIXED.getValue().equals(rebate.getProductRangeType())) {
                fixRebate.add(rebate);
                Map productRange = (Map) rebate.getProductRange();
                List products = (List) productRange.get("data");

                for (Object productObj : products) {
                    Map product = (Map) productObj;
                    String productId = product.get("product_id").toString();
                    String unit = getOrDefault(product, "unit_id", "");
                    if (productIds.contains(productId + unit)) continue;

                    productIds.add(productId + unit);
                    RealPriceModel.FullProduct entity = new RealPriceModel.FullProduct();
                    entity.setProductId(productId);
                    entity.setUnit(unit);
                    entity.setPriceBookId(priceBookId);
                    fullProductList.add(entity);
                }
            }
        }


        if (fullProductList.size() > 0) {
            RealPriceModel.Arg param = new RealPriceModel.Arg();
            param.setAccountId(arg.getAccountId());
            param.setFullProductList(fullProductList);
            param.setObjectData(arg.getMasterData());
            Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();
            details.put(arg.getDetailObjectApiName(), CollectionUtils.empty(arg.getDetailDataMap()) ?
                    Lists.newArrayList() : Lists.newArrayList(arg.getDetailDataMap().values()));
            param.setDetails(details);
            RealPriceModel.Result realPrice = availableRangeCoreService.getRealPrice(arg.getUser(), param);
            List<ObjectDataDocument> newRst = realPrice.getNewRst();
            Map<String, ObjectDataDocument> mRst = Maps.newHashMap();
            for (ObjectDataDocument x : newRst) {
                mRst.put(x.get("product_id").toString() + getOrDefault(x, "param_unit", ""), x);
            }
            for (Rebate rebate : fixRebate) {
                Map productRange = (Map) rebate.getProductRange();
                List products = (List) productRange.get("data");
                for (int i = products.size() - 1; i > -1; i--) {
                    Map product = (Map) products.get(i);
                    String key = product.get("product_id").toString() + getOrDefault(product, "unit_id", "");
                    ObjectDataDocument price = mRst.get(key);
                    if (null != price) {
                        product.putAll(price);
                    } else {
                        products.remove(i);
                    }
                }
            }
        }

    }

    private String getUseDataKey(RebateCouponQuery.Arg arg) {
        String useKey = UseData.DATA_REBATE;
        if (RebateConstants.RebateType.PRODUCT.getValue().equals(arg.getRebateType())) {
            useKey = UseData.DATA_PRODUCT_REBATE;
        }
        return useKey;
    }

    @Override
    protected List<Rebate> queryDataList(User user, List<String> usedId, Map<String, UseData> useDataList, boolean needAll) {
        List<Rebate> useList = (List<Rebate>) rebateDao.getCurrUseData(user, usedId, needAll);

        // 已经使用的返利单，可以使用金额为 剩余金额+已使用金额
        useList.forEach(rebate -> {
            if (RebateConstants.UseType.QUANTITY.getValue().equals(rebate.getUseType())) {
                rebate.setUnusedAmount(rebate.getUnusedAmount().add(useDataList.get(rebate.getId()).getQuantity()));
            } else {
                rebate.setUnusedAmount(rebate.getUnusedAmount().add(useDataList.get(rebate.getId()).getAmount()));
            }
        });
        return useList;
    }

    @Override
    protected List<?> filterData(RebateCouponQuery.Arg arg, List<?> dataList, RebateCouponQuery.Result result, StopWatch stopWatch) {
        RebateRule.Rule rule = arg.getRule();
        if (null == rule) {
            return Lists.newArrayList();
        }
        result.setRuleId(rule.getId());

        result.setLimitMoney(calcLimitMoney(arg, rule, false));
        stopWatch.lap("calcLimitMoney");

        List<Rebate> canUseRebate = filterDataByRule(arg, (List<Rebate>) dataList, rule, stopWatch);
        stopWatch.lap("filterDataByRule");

        return canUseRebate;
    }

    private RebateRule.Rule getUseRule(RebateCouponQuery.Arg arg) {
        List<RebateRule.Rule> canUseRules = arg.getCanUseRules();
        if (CollectionUtils.empty(canUseRules)) {
            return null;
        }

        RebateRule.Rule rule = canUseRules.get(0);
        if (StringUtils.isNotEmpty(arg.getRuleId())) {
            for (RebateRule.Rule canUseRule : canUseRules) {
                if (canUseRule.getId().equals(arg.getRuleId())) rule = canUseRule;
            }
        }
        return rule;
    }

    private List<Rebate> filterDataByRule(RebateCouponQuery.Arg arg, List<Rebate> dataList, RebateRule.Rule rule, StopWatch stopWatch) {
        stopWatch.lap("filterDataByRule");
        List<Rebate> canUseRebate = Lists.newArrayList();
        Map<String, Rebate> mRuleRebate = Maps.newHashMap();
        // 没有产品范围的返利单直接返回可用，有条件的返利单使用规则引擎进行过滤
        for (Rebate rebate : dataList) {
            if (RebateConstants.ProductConditionType.ALL.getValue().equals(rebate.getProductConditionType()) || StringUtils.isBlank(rebate.getProductConditionContent())) {
                canUseRebate.add(rebate);
            } else {
                mRuleRebate.put(rebate.getId(), rebate);
            }
        }

        if (CollectionUtils.notEmpty(mRuleRebate)) {
            Map<String, List<String>> matchRebates = engineLogicService.matchRuleCondition(arg.getUser(),
                    Lists.newArrayList(mRuleRebate.keySet()),
                    arg.getMasterObjectApiName(),
                    arg.getMasterData().toObjectData(),
                    arg.toDetailDataList());
            stopWatch.lap("filterDataMatchRuleCondition");
            if (CollectionUtils.notEmpty(matchRebates)) {
                for (String rebateId : matchRebates.keySet()) {
                    canUseRebate.add(mRuleRebate.get(rebateId));
                }
            }

        }
        return canUseRebate;
    }

    public BigDecimal calcLimitMoney(RebateCouponQuery.Arg arg, RebateRule.Rule rule, boolean needReset) {
        if (null == rule) {
            return BigDecimal.ZERO;
        }
        String ruleContent = rule.getRuleContent();
        if (needReset && ruleContent.indexOf(SalesOrderConstants.SalesOrderField.ORDER_AMOUNT.getApiName()) >= 1) {
            resetOrderAmount(arg);
        }
        JSONObject jRuleContent = JSON.parseObject(ruleContent);
        String calculateType =
                jRuleContent.getObject(RebateConstants.RuleContentField.CALCULATE_TYPE.getApiName(), String.class);

        BigDecimal limitMoney = BigDecimal.ZERO;
        if (RebateConstants.CalculateType.CYCLE.getApiName().equals(calculateType)) {
            limitMoney = calcCycleMoney(arg, jRuleContent);
        } else if (RebateConstants.CalculateType.EXPRESSION.getApiName().equals(calculateType)) {
            limitMoney = calcExpress(arg, jRuleContent);
        } else {
        }

        if (BigDecimal.ZERO.compareTo(limitMoney) > 0) {
            limitMoney = BigDecimal.ZERO;
        }
        return limitMoney;
    }

    private BigDecimal calcExpress(RebateCouponQuery.Arg arg, JSONObject jRuleContent) {
        String expressions = jRuleContent.getObject(RebateConstants.RuleContentField.EXPRESSIONS.getApiName(), String.class);

        List<RebateRule.ExpressionData> expressionData = JSON.parseArray(expressions, RebateRule.ExpressionData.class);

        Map<String, List<RebateRule.ExpressionData>> aggCycle = Maps.newHashMap();
        BigDecimal canUseAmount = null;
        for (RebateRule.ExpressionData expression : expressionData) {
            String executeType = expression.getExecuteType();

            if (RebateConstants.ExecuteType.CONSTANT.getApiName().equals(executeType)) {
                BigDecimal currLimit = new BigDecimal(expression.getRight());
                canUseAmount = getCanUseAmount(canUseAmount, currLimit);

            } else if (RebateConstants.ExecuteType.CALCULATE.getApiName().equals(executeType)) {
                String fieldNameType = expression.getLeft().getFieldNameType();

                String fieldName = expression.getLeft().getFieldName();
                if (CouponUtils.isFieldType(fieldNameType)) {

                    BigDecimal srcRightValue = new BigDecimal(getOrDefault(arg.getMasterData(), fieldName, "0"));
                    BigDecimal currLimit = calcExpression(expression, srcRightValue);
                    canUseAmount = getCanUseAmount(canUseAmount, currLimit);

                } else if (CouponUtils.isAggregateType(fieldNameType)) {
                    if (aggCycle.containsKey(fieldName)) {
                        aggCycle.get(fieldName).add(expression);
                    } else {
                        List<RebateRule.ExpressionData> list = Lists.newArrayList();
                        list.add(expression);
                        aggCycle.put(fieldName, list);
                    }
                }
            }
        }

        if (CollectionUtils.notEmpty(aggCycle)) {
            Map<String, String> aggValues = engineLogicService.computeAggregateValues(arg.getUser(),
                    aggCycle.keySet(),
                    arg.getMasterObjectApiName(),
                    arg.getMasterData().toObjectData(),
                    arg.toDetailDataList());

            for (Map.Entry<String, String> aggValue : aggValues.entrySet()) {

                BigDecimal srcRightValue = new BigDecimal(aggValue.getValue());
                List<RebateRule.ExpressionData> cycles = aggCycle.get(aggValue.getKey());
                for (RebateRule.ExpressionData executeInfo : cycles) {
                    BigDecimal currLimit = calcExpression(executeInfo, srcRightValue);
                    canUseAmount = getCanUseAmount(canUseAmount, currLimit);
                }
            }

        }

        return canUseAmount == null ? BigDecimal.ZERO : canUseAmount;
    }

    protected BigDecimal calcCycleMoney(RebateCouponQuery.Arg arg, JSONObject jRuleContent) {
        BigDecimal canUseAmount = BigDecimal.ZERO;
        String cycleInfo = jRuleContent.getObject(RebateConstants.RuleContentField.CYCLE_INFO.getApiName(), String.class);
        if (StringUtils.isEmpty(cycleInfo)) {
            return canUseAmount;
        }

        RebateRule.CycleInfoData cycleInfoData = JSON.parseObject(cycleInfo, RebateRule.CycleInfoData.class);
        if (cycleInfoData == null || CollectionUtils.empty(cycleInfoData.getCycleData())) {
            return canUseAmount;
        }

        Map<String, RebateRule.CycleInfoData.CycleDataBean> aggCycle = Maps.newHashMap();
        List<RebateRule.CycleInfoData.CycleDataBean> cycleData = cycleInfoData.getCycleData();
        canUseAmount = cycleInfoData.getMaxAmount();
        for (RebateRule.CycleInfoData.CycleDataBean cycleDate : cycleData) {
            RebateRule.CycleInfoData.CycleDataBean.LeftBean left = cycleDate.getLeft();
            String fieldNameType = left.getFieldNameType();
            String fieldName = left.getFieldName();
            if (CouponUtils.isFieldType(fieldNameType)) {
                BigDecimal srcValue = new BigDecimal(getOrDefault(arg.getMasterData(), fieldName, "0"));
                canUseAmount = getCycleMinNum(canUseAmount, cycleDate, srcValue, RoundingMode.HALF_DOWN);
            } else if (CouponUtils.isAggregateType(fieldNameType)) {
                if (aggCycle.containsKey(left.getFieldName())) {
                    if (cycleDate.getUsedAmount().compareTo(aggCycle.get(left.getFieldName()).getUsedAmount()) < 0) {
                        aggCycle.put(left.getFieldName(), cycleDate);
                    }
                } else {
                    aggCycle.put(left.getFieldName(), cycleDate);
                }
            }
        }

        if (CollectionUtils.notEmpty(aggCycle)) {
            Map<String, String> aggValues = engineLogicService.computeAggregateValues(arg.getUser(),
                    aggCycle.keySet(),
                    arg.getMasterObjectApiName(),
                    arg.getMasterData().toObjectData(),
                    arg.toDetailDataList());

            for (Map.Entry<String, String> aggValue : aggValues.entrySet()) {

                BigDecimal srcValue = new BigDecimal(aggValue.getValue());
                RebateRule.CycleInfoData.CycleDataBean cycle = aggCycle.get(aggValue.getKey());
                canUseAmount = getCycleMinNum(canUseAmount, cycle, srcValue, RoundingMode.DOWN);
            }

        }

        return canUseAmount;
    }

    @NotNull
    private String getOrDefault(Map data, String fieldName, String defaultValue) {
        Object oValue = data.getOrDefault(fieldName, defaultValue);
        if(oValue == null){
            return defaultValue;
        }
        String sValue = oValue.toString();
        if (StringUtils.isBlank(sValue)) {
            sValue = defaultValue;
        }
        return sValue;
    }

    private BigDecimal getCycleMinNum(BigDecimal canUseAmount, RebateRule.CycleInfoData.CycleDataBean cycle, BigDecimal srcValue, RoundingMode halfDown) {
        int cycleNum = srcValue.divide(cycle.getFieldValue(), 1, halfDown).intValue();
        BigDecimal currLimit = cycle.getUsedAmount().multiply(BigDecimal.valueOf(cycleNum));
        if (canUseAmount == null || currLimit.compareTo(canUseAmount) < 0) {
            canUseAmount = currLimit;
        }
        return canUseAmount;
    }

    private BigDecimal getCanUseAmount(BigDecimal canUseAmount, BigDecimal currLimit) {
        if (null == canUseAmount) {
            canUseAmount = currLimit;
        } else {
            if (currLimit.compareTo(canUseAmount) < 0) {
                canUseAmount = currLimit;
            }
        }
        return canUseAmount;
    }

    private BigDecimal calcExpression(RebateRule.ExpressionData executeInfo, BigDecimal srcRightValue) {
        PricePolicyConstants.PricingRuleOperator pricingRuleOperator = PricePolicyConstants.PricingRuleOperator.valueOf(executeInfo.getOperator().toUpperCase());
        BigDecimal rightValue = new BigDecimal(executeInfo.getRight());
        BigDecimal currLimit;
        switch (pricingRuleOperator) {
            case ADD:
                currLimit = srcRightValue.add(rightValue);
                break;
            case SUBTRACT:
                currLimit = srcRightValue.subtract(rightValue);
                break;
            case MULTIPLY:
                currLimit = srcRightValue.multiply(rightValue);
                break;
            case DIVIDE:
                currLimit = srcRightValue.divide(rightValue, RoundingMode.HALF_UP);
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + pricingRuleOperator);
        }
        return currLimit;
    }


    private List<RebateRule.Rule> getCanUseRule(RebateCouponQuery.Arg arg, RebateCouponQuery.Result result, StopWatch stopWatch) {
        List<RebateRule.Rule> rules;
        if (arg.isNeedRule() || StringUtils.isEmpty(arg.getRuleId()) || CollectionUtils.empty(arg.getRangeRuleIds())) {
            rules = (List<RebateRule.Rule>) rebateRuleDao.getDataByAccountId(arg.getUser(), arg.getMasterObjectApiName(), arg.getAccountId(), arg);
        } else {
            List<String> ruleIds = Lists.newArrayList();
            if (StringUtils.isNotEmpty(arg.getRuleId())) {
                ruleIds.add(arg.getRuleId());
            }
            if (CollectionUtils.notEmpty(arg.getRangeRuleIds())) {
                ruleIds.addAll(arg.getRangeRuleIds());
            }
            rules = (List<RebateRule.Rule>) this.rebateRuleDao.getCurrUseData(arg.getUser(), ruleIds, false);
        }
        stopWatch.lap("queryRebateRule");

        if (CollectionUtils.empty(rules)) {
            return Lists.newArrayList();
        }

        return filterRules(arg, result, stopWatch, rules);
    }

    @NotNull
    public List<RebateRule.Rule> filterRules(RebateCouponQuery.Arg arg, RebateCouponQuery.Result result, StopWatch stopWatch, List<RebateRule.Rule> rules) {
        List<RebateRule.Rule> canUseRules = Lists.newArrayList();
        List<RebateRule.Rule> condUseRules = Lists.newArrayList();
        List<String> ruleIds = Lists.newArrayList();

        boolean useOrderAmount = false;
        for (RebateRule.Rule rule : rules) {
            if (StringUtils.isNotEmpty(rule.getRuleContent())) {

                if (rule.getRuleContent().indexOf(SalesOrderConstants.SalesOrderField.ORDER_AMOUNT.getApiName()) >= 1) {
                    useOrderAmount = true;
                }

                JSONObject jsonObject = JSON.parseObject(rule.getRuleContent());
                // MASTER_CONDITION 为空代表不设置任何条件
                Object condition = jsonObject.get(RebateConstants.RuleContentField.MASTER_CONDITION.getApiName());
                if (null != condition && (condition instanceof List && ((List) condition).size() > 0)) {
                    // 设置了限定条件的使用规则 用规则引擎过滤是否可用。
                    ruleIds.add(rule.getId());
                    condUseRules.add(rule);
                    continue;
                }
            }

            // 没有条件的使用规则都可以使用。
            canUseRules.add(rule);
        }
        stopWatch.lap("getRuleRebateRule");


        // 如果规则使用的订单销售金额，重置下销售金额
        if (useOrderAmount) {
            resetOrderAmount(arg);
        }

        if (ruleIds.size() > 0) {
            Map<String, List<String>> matchRules = engineLogicService.matchRuleCondition(arg.getUser(),
                    ruleIds,
                    arg.getMasterObjectApiName(),
                    arg.getMasterData().toObjectData(),
                    arg.toDetailDataList());

            if (CollectionUtils.notEmpty(matchRules)) {
                condUseRules.forEach(rule -> {
                    if (matchRules.containsKey(rule.getId())) canUseRules.add(rule);
                });
            }

            stopWatch.lap("rebateRuleMatchRuleCondition");
        }

        List<RebateRule.Rule> noRangeRules = Lists.newArrayList();
        List<RebateRule.Rule> rangeRules = Lists.newArrayList();
        for (RebateRule.Rule rule : canUseRules) {
            if ("1".equals(rule.getProductRangeType())) {
                rangeRules.add(rule);
            } else {
                noRangeRules.add(rule);
            }
        }
        arg.setCanUseRules(noRangeRules);
        arg.setRangeRules(rangeRules);
        if (arg.isNeedRule()) {
            result.setRules(RebateCouponQuery.RuleResult.ofList(noRangeRules));
        }
        result.setRangeRules(RebateCouponQuery.RuleResult.ofList(rangeRules));
        return noRangeRules;
    }

    private void resetOrderAmount(RebateCouponQuery.Arg arg) {
        String amountKey = rebateProduct.getAmountKey(arg.getUser().getTenantId(), arg.getMasterObjectApiName());

        final BigDecimal[] orderAmount = {BigDecimal.ZERO};
        arg.toDetailDataList().forEach(detail -> orderAmount[0] = orderAmount[0].add(detail.get(amountKey, BigDecimal.class)));

        arg.getMasterData().put(arg.getPluginParam().getFieldApiName(CouponConstants.PluginField.ORDER_AMOUNT), orderAmount[0].toString());
    }

    @Override
    public RebateCouponMatch.Result matchAmortize(RebateCouponMatch.Arg arg, RebateCouponMatch.Result... result1) {
        StopWatch stopWatch = arg.getStopWatch();
        // 返利和优惠券同时分摊时，先分摊优惠券，再分摊返利单
        RebateCouponMatch.Result result;
        if (null == result1 || result1.length == 0 || null == result1[0]) {
            result = RebateCouponMatch.Result.builder().build();
        } else {
            result = result1[0];
        }

        if (!bizConfigThreadLocalCacheService.isOpenRebate(arg.getUser().getTenantId())) {
            return result;
        }
        Amortize.AmortizeKey amortizeKey = Amortize.AmortizeKey.builder()
                .amortizeKey(CouponConstants.PluginDetailField.REBATE_AMORTIZE_AMOUNT)
                .dynamicKey(CouponConstants.PluginDetailField.REBATE_DYNAMIC_AMOUNT)
                .masterAmountKey(getMasterAmountKey())
                .contentKey(Amortize.AmortizeData.REBATE_AMORTIZE).build();
        resetAmortize(arg, result, amortizeKey);
        stopWatch.lap("resetRebateAmortize");

        String ruleId = arg.getRuleId();
        List<UseData> useDataList = arg.getRebateDatas();
        List<RebateCouponUse.RangeRebateData> rangeRebateData = arg.getRangeRebateDatas();
        if (CollectionUtils.empty(useDataList) && CollectionUtils.empty(rangeRebateData)) {
            // 货返和金额返利同时没有传参数，从misc_content中获取之前的数据
            if (CollectionUtils.empty(arg.getProductRebateDatas()) && null == arg.getCalcParam()) {
                useDataList = getUseData(arg, UseData.DATA_REBATE);
                // 获取范围产品返利
                rangeRebateData = getUseRangeData(arg);
            }
            ruleId = (String) arg.getMasterData().get("rebate_rule_id");
            arg.setRuleId(ruleId);
        } else {
            if (CollectionUtils.notEmpty(useDataList)) {
                useDataList.removeIf(x -> null == x.getAmount() || BigDecimal.ZERO.equals(x.getAmount()));
            }
        }
        result.getMasterData().put(arg.getPluginParam().getFieldApiName(CouponConstants.PluginField.MISC_CONTENT), result.getMiscContent());
        if ((!Boolean.TRUE.equals(arg.getCompulsoryRebate())) && isRepelCoupon(arg) &&
                ((CollectionUtils.notEmpty(useDataList)) || CollectionUtils.notEmpty(rangeRebateData))) {
            result.setRebateChange(true);
            useDataList = Lists.newArrayList();
            rangeRebateData = Lists.newArrayList();
        }

        if (CollectionUtils.empty(useDataList) && CollectionUtils.empty(rangeRebateData)) {
            amortizeProductRebate(arg, result, amortizeKey, stopWatch);
            fillFundAccount(arg, result);
            result.setMiscContent(null);
            return result;
        }

        List<RebateRule.Rule> rules = (List<RebateRule.Rule>) this.rebateRuleDao.getCurrUseData(arg.getUser(), getRuleIds(ruleId, rangeRebateData), false);
        if (CollectionUtils.empty(rules)) {
            result.setRebateChange(true);
            amortizeProductRebate(arg, result, amortizeKey, stopWatch);
            fillFundAccount(arg, result);
            result.setMiscContent(null);
            return result;
        }
        RebateRule.Rule rule = rules.get(0);
        stopWatch.lap("rebateGetCurrUseDataRule");

        List<Rebate> currList = (List<Rebate>) rebateDao.getCurrUseData(arg.getUser(), getUseRebateIds(useDataList, rangeRebateData), false);
        stopWatch.lap("rebateGetCurrUseData");

        if (arg.isEdit() || arg.isChange()) {
            RebateCouponQuery.Arg qArg = RebateCouponQuery.Arg.builder().user(arg.getUser()).masterData(arg.getMasterData()).detailDataMap(arg.getDetailDataMap()).masterObjectApiName(arg.getMasterObjectApiName()).build();
            currList = this.filterDataByRule(qArg, currList, rule, stopWatch);
            removeAllNotUseData(stopWatch, ruleId, useDataList, rangeRebateData, rules, currList, qArg);
        }
        stopWatch.lap("rebateFilterData");
        if (CollectionUtils.empty(currList) || CollectionUtils.empty(arg.getDetailDataList())) {
            result.setRebateChange(true);
            stopWatch.logSlow(500);
            amortizeProductRebate(arg, result, amortizeKey, stopWatch);
            fillFundAccount(arg, result);
            result.setMiscContent(null);
            return result;
        }

        currList = checkAllMoneyRebate(arg, result, ruleId, useDataList, rangeRebateData, rules, currList);
        stopWatch.lap("rebateCheckOver");

        amortize(arg, currList, result, amortizeKey, stopWatch);
        stopWatch.lap("rebateAmortize");

        subAmortizeDown(arg, result, currList, amortizeKey);
        stopWatch.lap("rebateSubtotalDown");

        setResultRebateData(result, useDataList, rangeRebateData, currList, ruleId);

        getRebateFields(arg.getUser(), result.getRebateConditionField(), arg.getDetailObjectApiName(), arg.getPluginParam(), rules, stopWatch);
        stopWatch.lap("getRebateFields");

        amortizeProductRebate(arg, result, amortizeKey, stopWatch);
        stopWatch.lap("amortizeProductRebate");

        setDetailSubtotalLessZero(arg, result, true);

        fillFundAccount(arg, result);
        result.setMiscContent(null); // MiscContent已经在master中返回了，不用重复返回
        stopWatch.lap("fillResult");
        return result;
    }

    protected void removeAllNotUseData(StopWatch stopWatch, String ruleId, List<UseData> useDataList, List<RebateCouponUse.RangeRebateData> rangeRebateData, List<RebateRule.Rule> rules, List<Rebate> currList, RebateCouponQuery.Arg qArg) {
        RebateCouponQuery.Result qResult = RebateCouponQuery.Result.builder().build();
        filterRules(qArg, qResult, stopWatch, rules);
        if (StringUtils.isNotEmpty(ruleId) && CollectionUtils.empty(qArg.getCanUseRules())) {
            removeCanNotUseData(ruleId, useDataList, rules, currList);

        }
        if (CollectionUtils.notEmpty(rangeRebateData)) {
            Set<String> ids = qArg.getRangeRules().stream().map(RebateRule.Rule::getId).collect(Collectors.toSet());
            for (RebateCouponUse.RangeRebateData rd : rangeRebateData) {
                if (!ids.contains(rd.getRangeRuleId())) {
                    removeCanNotUseData(rd.getRangeRuleId(), rd.getRangeRebates(), rules, currList);
                }
            }
        }
    }

    private void setResultRebateData(RebateCouponMatch.Result result, List<UseData> useDataList, List<RebateCouponUse.RangeRebateData> rangeRebateData, List<Rebate> currList, String ruleId) {
        if (CollectionUtils.notEmpty(useDataList)) {
            Set<String> ids = useDataList.stream().map(UseData::getId).collect(Collectors.toSet());
            List<Rebate> rebates = currList.stream().filter(x -> ids.contains(x.getId())).collect(Collectors.toList());
            result.setRebateDatas(makeRebateUseData(rebates));
            result.getMiscContent().put(UseData.DATA_REBATE, result.getRebateDatas());
            result.getMasterData().put(RebateConstants.PluginField.REBATE_RULE_ID, ruleId);
        }
        if (CollectionUtils.notEmpty(rangeRebateData)) {
            List<String> rangRules = Lists.newArrayList();
            List<RebateCouponUse.RangeRebateData> rangeRebateDataUse = Lists.newArrayList();
            for (RebateCouponUse.RangeRebateData data : rangeRebateData) {
                rangRules.add(data.getRangeRuleId());
                Set<String> ids = data.getRangeRebates().stream().map(UseData::getId).collect(Collectors.toSet());
                List<Rebate> rebates = currList.stream().filter(x -> ids.contains(x.getId())).collect(Collectors.toList());
                if (CollectionUtils.notEmpty(rebates)) {
                    RebateCouponUse.RangeRebateData dataUse = RebateCouponUse.RangeRebateData.builder().rangeRuleId(data.getRangeRuleId()).build();
                    dataUse.setRangeRebates(makeRebateUseData(rebates));
                    rangeRebateDataUse.add(dataUse);
                }
            }
            if (CollectionUtils.notEmpty(rangeRebateDataUse)) {
                result.getMiscContent().put(UseData.DATA_RANGE_REBATE, rangeRebateDataUse);

                result.getMasterData().put(RebateConstants.PluginField.RANGE_REBATE_RULE_IDS, rangRules);
            }

        }
    }

    protected List<RebateCouponUse.RangeRebateData> getUseRangeData(RebateCouponMatch.Arg arg) {
        Map jsonObject = CouponUtils.getMiscMap(arg.getMasterData().get(arg.getPluginParam().getFieldApiName(CouponConstants.PluginField.MISC_CONTENT)));
        return getRangeRebateData(jsonObject);
    }

    @NotNull
    private List<RebateCouponUse.RangeRebateData> getRangeRebateData(Map miscContent) {
        List<RebateCouponUse.RangeRebateData> useDataTemp = Lists.newArrayList();
        if (null == miscContent) {
            return useDataTemp;
        }
        List coupons = (List) miscContent.get(UseData.DATA_RANGE_REBATE);
        if (CollectionUtils.empty(coupons)) {
            return useDataTemp;
        }

        for (Object object : coupons) {
            RebateCouponUse.RangeRebateData data = RebateCouponUse.RangeRebateData.builder().build();
            JSONObject json = (JSONObject) JSON.toJSON(object);
            data.setRangeRuleId(json.getString("rangeRuleId"));
            List rangeRebates = (List) json.get("rangeRebates");
            List<UseData> rangeRebateData = Lists.newArrayList();
            for (Object rangeRebateObj : rangeRebates) {
                JSONObject rangeRebate = (JSONObject) JSON.toJSON(rangeRebateObj);
                rangeRebateData.addAll(UseData.fromJson(rangeRebate));
            }
            data.setRangeRebates(rangeRebateData);
            useDataTemp.add(data);
        }
        return useDataTemp;
    }

    private List<Rebate> checkAllMoneyRebate(RebateCouponMatch.Arg arg, RebateCouponMatch.Result result, String ruleId, List<UseData> useDataList, List<RebateCouponUse.RangeRebateData> rangeRebateData, List<RebateRule.Rule> rules, List<Rebate> currList) {
        List<Rebate> useList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(useDataList)) {
            useList.addAll(checkMoneyRebate(arg, result, ruleId, useDataList, rules, currList));
        }
        if (CollectionUtils.notEmpty(rangeRebateData)) {
            for (RebateCouponUse.RangeRebateData data : rangeRebateData) {
                useList.addAll(checkMoneyRebate(arg, result, data.getRangeRuleId(), data.getRangeRebates(), rules, currList));
            }
        }
        return useList;
    }

    @Nullable
    private List<Rebate> checkMoneyRebate(RebateCouponMatch.Arg arg, RebateCouponMatch.Result result, String ruleId, List<UseData> useDataList, List<RebateRule.Rule> rules, List<Rebate> currList) {
        List<Rebate> useList = Lists.newArrayList();
        final String moneyRuleId = ruleId;
        RebateRule.Rule rule = rules.stream().filter(x -> x.getId().equals(moneyRuleId)).findFirst().orElse(null);
        if (null == rule) {
            result.setRebateChange(true);
            return useList;
        }
        Integer priority = Integer.MAX_VALUE;
        if (null != rule.getPriority() && RebateConstants.HAS_PRODUCT_RANGE.equals(rule.getProductRangeType())) {
            priority = rule.getPriority();
        }
        Map<String, UseData> useDataMap = Maps.newHashMap();
        useDataList.forEach(data -> useDataMap.put(data.getId(), data));

        for (Rebate x : currList) {
            if (useDataMap.containsKey(x.getId())) {
                x.setPriority(priority);
                x.setRuleLastModifiedTime(rule.getLastModifiedTime());
                x.setRuleId(rule.getId());
                useList.add(x);
            }
        }

        if (checkOver(arg, rule, useDataMap, useList)) {
            result.setRebateChange(true);
        }
        return useList;
    }

    @NotNull
    private List<String> getUseRebateIds(List<UseData> useDataList, List<RebateCouponUse.RangeRebateData> rangeRebateData) {
        List<String> rebateIds = Lists.newArrayList();
        if (CollectionUtils.notEmpty(useDataList)) {
            useDataList.forEach(x -> rebateIds.add(x.getId()));
        }

        if (CollectionUtils.notEmpty(rangeRebateData)) {
            rangeRebateData.forEach(x -> x.getRangeRebates().forEach(y -> rebateIds.add(y.getId())));
        }
        return rebateIds;
    }

    @NotNull
    private List<String> getRuleIds(String ruleId, List<RebateCouponUse.RangeRebateData> rangeRebateData) {
        List<String> ruleIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(ruleId)) {
            ruleIds.add(ruleId);
        }
        if (CollectionUtils.notEmpty(rangeRebateData)) {
            rangeRebateData.forEach(x -> ruleIds.add(x.getRangeRuleId()));
        }
        return ruleIds;
    }

    private boolean checkOver(RebateCouponMatch.Arg arg, RebateRule.Rule rule, Map<String, UseData> useDataMap, List<Rebate> currList) {
        if (rule == null) {
            currList.clear();
            return true;
        }
        boolean isOver = false;
        BigDecimal useAmount = BigDecimal.ZERO;
        for (Rebate data : currList) {
            data.setUnusedAmount(useDataMap.get(data.getId()).getAmount());
            useAmount = useAmount.add(data.getUnusedAmount());
        }

        BigDecimal limitMoney = calcLimitMoney(RebateCouponQuery.Arg.of(arg), rule, true);
        if (limitMoney.compareTo(useAmount) < 0) {
            isOver = true;
            if (RebateConstants.RebateType.PRODUCT.getValue().equals(currList.get(0).getRebateType())) {
                return true;
            }

            // 钱返回退可用返利。
            BigDecimal overMoney = useAmount.subtract(limitMoney);
            currList.sort(Comparator.comparing(Rebate::getEndDate));
            for (int i = currList.size() - 1; i >= 0; i--) {
                Rebate data = currList.get(i);
                if (overMoney.compareTo(currList.get(i).getUnusedAmount()) >= 0) {
                    overMoney = overMoney.subtract(data.getUnusedAmount());
                    currList.remove(i);
                } else {
                    data.setUnusedAmount(data.getUnusedAmount().subtract(overMoney));
                    break;
                }
            }
        }

        return isOver;
    }


    protected List<UseData> makeRebateUseData(List<Rebate> currList) {
        List<UseData> finalUseDataList = Lists.newArrayList();
        if (CollectionUtils.empty(currList)) {
            return finalUseDataList;
        }
        currList.forEach(data -> {
            if (BigDecimal.ZERO.compareTo(data.getUnusedAmount()) < 0)
                finalUseDataList.add(UseData.builder().id(data.getId()).amount(data.getUnusedAmount()).name(data.getName())
                        .topic(data.getTopic()).useType(data.getUseType()).fundAccountId(data.getFundAccountId()).build());
        });
        return finalUseDataList;
    }


    protected void subAmortizeDown(RebateCouponMatch.Arg arg, RebateCouponMatch.Result result, List<Rebate> currList, Amortize.AmortizeKey amortizeKey) {
        boolean openCoupon = bizConfigThreadLocalCacheService.isOpenCoupon(arg.getUser().getTenantId());
        boolean openPricePolicy = bizConfigThreadLocalCacheService.isOpenPricePolicy(arg.getUser().getTenantId(), arg.getMasterObjectApiName());

        for (IObjectData data : arg.toDetailDataList()) {
            String dataIndex = data.get(PricePolicyConstants.DATA_INDEX, String.class);
            if (!result.getDetailDataMap().containsKey(dataIndex)) {
                continue;
            }

            Map<String, Object> detail = result.getDetailDataMap().get(dataIndex);
            Map<String, Object> miscContent = (Map<String, Object>) detail.get(arg.getPluginParam().getDefaultDetailFieldApiName(CouponConstants.PluginField.MISC_CONTENT));
            if (CollectionUtils.empty(miscContent)) {
                continue;
            }
            List<Object> amortizeList = (List<Object>) miscContent.get(Amortize.AmortizeData.REBATE_AMORTIZE);
            if (CollectionUtils.empty(amortizeList)) {
                continue;
            }

            BigDecimal subtotal = getSubtotal(arg, openCoupon, openPricePolicy, true, data, detail);
            // 判断分摊后小计(报价小计+促销分摊金额(不含赠品分摊)+返利分摊金额+优惠券分摊)是否小于0
            if (BigDecimal.ZERO.compareTo(subtotal) > 0) {
                Map<String, Rebate> mUseRebate = currList.stream()
                        .collect(Collectors.toMap(Rebate::getId, x -> x, (v1, v2) -> v2));
                Map<String, Amortize.AmortizeData> mUseRebateAmount = Maps.newHashMap();

                List<Rebate> thisUse = Lists.newArrayList();
                for (Object amortizeObj : amortizeList) {
                    Amortize.AmortizeData amortize = (Amortize.AmortizeData) amortizeObj;
                    thisUse.add(mUseRebate.get(amortize.getId()));
                    mUseRebateAmount.put(amortize.getId(), amortize);
                }

                Comparator<Rebate> byPriorityDesc = Comparator.comparing(Rebate::getPriority).reversed();
                Comparator<Rebate> byLastModifiedTimeAsc = Comparator.comparing(Rebate::getRuleLastModifiedTime);
                Comparator<Rebate> byIdDesc = Comparator.comparing(Rebate::getRuleId).reversed();
                Comparator<Rebate> byEndDateDesc = Comparator.comparing(Rebate::getEndDate).reversed();
                Comparator<Rebate> comparator = byPriorityDesc.thenComparing(byLastModifiedTimeAsc).thenComparing(byIdDesc).thenComparing(byEndDateDesc);
                thisUse.sort(comparator);

                for (Rebate rebate : thisUse) {
                    Amortize.AmortizeData amortizeData = mUseRebateAmount.get(rebate.getId());
                    BigDecimal rebateAmount = amortizeData.getAmount();
                    BigDecimal amount = rebateAmount;// 回退金额
                    if (amount.compareTo(subtotal) < 0) {
                        amount = subtotal;
                        amortizeData.setAmount(rebateAmount.subtract(subtotal));
                    } else {
                        mUseRebateAmount.remove(rebate.getId());
                    }

                    subtotal = subtotal.subtract(amount);
                    Rebate rebateTemp = mUseRebate.get(rebate.getId());

                    // 使用金额回退分摊金额
                    rebateTemp.setUnusedAmount(rebateTemp.getUnusedAmount().add(amount));

                    // 分摊数据回退分摊金额
                    if (CouponConstants.UseType.DISCOUNT.getValue().equals(rebate.getUseType())) {
                        detail.put(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getDynamicKey()),
                                ((BigDecimal) detail.get(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getDynamicKey()))).subtract(amount));

                    }

                    if (CouponConstants.UseType.CASH.getValue().equals(rebate.getUseType())) {
                        String masterAmountKey = arg.getPluginParam().getFieldApiName(this.getMasterAmountKey());
                        result.getMasterData().put(masterAmountKey, ((BigDecimal) result.getMasterData().get(masterAmountKey)).subtract(amount));
                    }

                    detail.put(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getAmortizeKey()),
                            ((BigDecimal) detail.get(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getAmortizeKey()))).subtract(amount));

                    result.setRebateChange(true);

                    if (BigDecimal.ZERO.compareTo(subtotal) <= 0) {
                        break;
                    }
                }

                miscContent.put(Amortize.AmortizeData.REBATE_AMORTIZE, Lists.newArrayList(mUseRebateAmount.values()));

            }
        }
    }


    @Override
    protected void amortize(RebateCouponMatch.Arg arg, List<?> currList, RebateCouponMatch.Result result, Amortize.AmortizeKey amortizeKey, StopWatch stopWatch) {
        stopWatch.lap("amortizeStart");
        // 返利开始分摊，且有互斥的优惠券，清空优惠券信息
        cleanRepelCoupon(arg, result);

        // 获取分摊字段精度
        Amortize.AmortizeDecimal amortizeDecimal = getAmortizeDecimal(arg, arg.getPluginParam().getDefaultDetailFieldApiName(CouponConstants.PluginDetailField.REBATE_AMORTIZE_AMOUNT),
                arg.getPluginParam().getDefaultDetailFieldApiName(CouponConstants.PluginDetailField.REBATE_DYNAMIC_AMOUNT));
        stopWatch.lap("getAmortizeDecimal");

        Map<String, List<Amortize.ProductData>> rebateProducts = rebateProduct.getProduct(currList, arg);
        stopWatch.lap("getAmortizeProduct");

        for (Rebate rebate : (List<Rebate>) currList) {
            String useType = RebateConstants.RebateType.PRODUCT.getValue().equals(rebate.getRebateType()) ? RebateConstants.UseType.CASH.getValue() : rebate.getUseType();
            if (CouponConstants.UseType.CASH.getValue().equals(useType)) {
                rebate.setUnusedAmount(rebate.getUnusedAmount().setScale(amortizeDecimal.getDynamicAmountDecimal(), BigDecimal.ROUND_HALF_UP));
            } else if (CouponConstants.UseType.DISCOUNT.getValue().equals(useType)) {
                rebate.setUnusedAmount(rebate.getUnusedAmount().setScale(amortizeDecimal.getAmortizeAmountDecimal(), BigDecimal.ROUND_HALF_UP));
            }
        }

        List<Rebate> rebateList = (List<Rebate>) currList;
        boolean allUseRebate = isAllUseRebate(arg, currList, result, rebateProducts);
        if (allUseRebate) {
            // 全额使用返利
            Comparator<Rebate> byEndDateAsc = Comparator.comparing(Rebate::getEndDate);
            Comparator<Rebate> byStartDateAsc = Comparator.comparing(Rebate::getStartDate);
            Comparator<Rebate> byIdAsc = Comparator.comparing(Rebate::getId);
            Comparator<Rebate> comparator = byEndDateAsc.thenComparing(byStartDateAsc).thenComparing(byIdAsc);
            rebateList.sort(comparator);
        }

        for (Rebate rebate : rebateList) {
            Amortize.AmortizeCalcData calcData = Amortize.AmortizeCalcData.of(rebate);
            calcData.setAllUseRebate(allUseRebate);
            amortizeOneData(arg, result, amortizeDecimal, rebateProducts, calcData, amortizeKey);
        }

    }

    private boolean isAllUseRebate(RebateCouponMatch.Arg arg, List<?> currList, RebateCouponMatch.Result result, Map<String, List<Amortize.ProductData>> rebateProducts) {
        if (StringUtils.isBlank(arg.getRuleId()) || CollectionUtils.empty(currList)) {
            return false;
        }
        List<Rebate> rebateList = (List<Rebate>) currList;
        boolean isAllNoRange = rebateList.stream().allMatch(x -> arg.getRuleId().equals(x.getRuleId()));
        boolean openCoupon = bizConfigThreadLocalCacheService.isOpenCoupon(arg.getUser().getTenantId());
        boolean openPricePolicy = bizConfigThreadLocalCacheService.isOpenPricePolicy(arg.getUser().getTenantId(), arg.getMasterObjectApiName());

        BigDecimal allSubtotal = BigDecimal.ZERO;
        List<IObjectData> details = arg.toDetailDataList();
        for (IObjectData data : details) {
            String dataIndex = data.get(PricePolicyConstants.DATA_INDEX, String.class);
            Map<String, Object> detail = result.getDetailDataMap().computeIfAbsent(dataIndex, k -> new HashMap<>());

            BigDecimal subtotal = getSubtotal(arg, openCoupon, openPricePolicy, false, data, detail);
            if (BigDecimal.ZERO.compareTo(subtotal) < 0) {
                allSubtotal = allSubtotal.add(subtotal);
            }
        }

        boolean rebateUseAllProduct = true;

        BigDecimal allRebateAmount = BigDecimal.ZERO;
        for (Rebate rebate : rebateList) {
            if (BigDecimal.ZERO.compareTo(rebate.getUnusedAmount()) < 0) {
                allRebateAmount = allRebateAmount.add(rebate.getUnusedAmount());
            }
            if (details.size() > rebateProducts.get(rebate.getId()).size()) {
                rebateUseAllProduct = false;
            }
        }

        if (isAllNoRange && rebateUseAllProduct && BigDecimal.ZERO.compareTo(allSubtotal) < 0 && allRebateAmount.compareTo(allSubtotal) >= 0) {
            return true;
        }
        return false;
    }

    @Override
    protected String getAmortizeKey() {
        return Amortize.AmortizeData.REBATE_AMORTIZE;
    }

    @Override
    protected String getMasterAmountKey() {
        return CouponConstants.PluginField.REBATE_AMOUNT;
    }

    @Override
    protected void addAmortizeKey(RebateCouponMatch.Arg arg, Amortize.AmortizeKey amortizeKey, Map<String, Object> amortizeObj) {
        if (!amortizeObj.containsKey(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getAmortizeKey()))) {
            amortizeObj.put(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getAmortizeKey()), BigDecimal.ZERO);
            amortizeObj.put(arg.getPluginParam().getDefaultDetailFieldApiName(amortizeKey.getDynamicKey()), BigDecimal.ZERO);
        }
    }

    protected void amortizeProductRebate(RebateCouponMatch.Arg arg, RebateCouponMatch.Result result, Amortize.AmortizeKey amortizeKey, StopWatch stopWatch) {
        String ruleId = arg.getProductRuleId();
        List<UseData> useDataList = arg.getProductRebateDatas();
        if (CollectionUtils.empty(useDataList)) {
            // 货返和金额返利同时没有传参数，从misc_content中获取之前的数据
            if (CollectionUtils.empty(arg.getRebateDatas()) || null != arg.getCalcParam()) {
                useDataList = getUseData(arg, UseData.DATA_PRODUCT_REBATE);
            }
        } else {
            useDataList.removeIf(x -> null == x.getAmount() || (BigDecimal.ZERO.equals(x.getAmount()) && BigDecimal.ZERO.equals(x.getQuantity())));
        }
        if (CollectionUtils.empty(useDataList)) {
            return;
        }

        if (!Boolean.TRUE.equals(arg.getCompulsoryRebate()) && isRepelCoupon(arg) && CollectionUtils.notEmpty(useDataList)) {
            result.setProductRebateChange(true);
            return;
        }

        if (StringUtils.isBlank(ruleId)) {
            ruleId = (String) arg.getMasterData().get(arg.getPluginParam().getFieldApiName(RebateConstants.PluginField.PRODUCT_REBATE_RULE_ID));
        }

        List<RebateRule.Rule> rules = (List<RebateRule.Rule>) this.rebateRuleDao.getCurrUseData(arg.getUser(), Lists.newArrayList(ruleId), false);
        if (CollectionUtils.empty(rules)) {
            result.setProductRebateChange(true);
            return;
        }
        RebateRule.Rule rule = rules.get(0);

        RebateCouponQuery.Result qResult = RebateCouponQuery.Result.builder().build();
        RebateCouponQuery.Arg qArg = RebateCouponQuery.Arg.builder().user(arg.getUser()).masterData(arg.getMasterData()).detailDataMap(arg.getDetailDataMap()).masterObjectApiName(arg.getMasterObjectApiName()).build();
        List<RebateRule.Rule> canUseRules = filterRules(qArg, qResult, stopWatch, rules);
        if (CollectionUtils.empty(canUseRules)) {
            result.setProductRebateChange(true);
            return;
        }

        stopWatch.lap("ProductRebateGetCurrUseDataRule");

        Map<String, UseData> useDataMap = Maps.newHashMap();
        useDataList.forEach(data -> {
            if (useDataMap.containsKey(data.getId())) {
                UseData useData = useDataMap.get(data.getId());
                useData.setAmount(useData.getAmount().add(data.getAmount()));
                useData.setQuantity(useData.getQuantity().add(data.getQuantity()));
            } else {
                useDataMap.put(data.getId(), UseData.builder().id(data.getId()).topic(data.getTopic())
                        .useType(data.getUseType()).quantity(data.getQuantity()).name(data.getName())
                        .amount(data.getAmount()).productId(data.getProductId()).fundAccountId(data.getFundAccountId()).build());
            }
        });
        List<Rebate> currList = (List<Rebate>) rebateDao.getCurrUseData(arg.getUser(), Lists.newArrayList(useDataMap.keySet()), false);
        stopWatch.lap("ProductRebateGetCurrUseData");
        if (CollectionUtils.empty(currList) || currList.size() < useDataMap.size()) {
            result.setProductRebateChange(true);
            stopWatch.logSlow(500);
            return;
        }

        if (checkOver(arg, rule, useDataMap, currList)) {
            result.setProductRebateChange(true);
            stopWatch.logSlow(500);
            return;
        }
        stopWatch.lap("ProductRebateCheckOver");

        amortizeKey.setContentKey(Amortize.AmortizeData.PRODUCT_REBATE_AMORTIZE);
        amortize(arg, currList, result, amortizeKey, stopWatch);
        stopWatch.lap("ProductRebateAmortize");

        result.getMasterData().put(arg.getPluginParam().getFieldApiName(CouponConstants.PluginField.MISC_CONTENT), result.getMiscContent());
        Map<String, Rebate> currListMap = Maps.newHashMap();
        currList.forEach(rebate -> currListMap.put(rebate.getId(), rebate));
        useDataList.forEach(data -> {
            Rebate rebate = currListMap.get(data.getId());
            data.setTopic(rebate.getTopic());
            data.setName(rebate.getName());
            data.setFundAccountId(rebate.getFundAccountId());
            data.setUseType(rebate.getUseType());
        });
        result.setProductRebateDatas(useDataList);
        result.getMiscContent().put(UseData.DATA_PRODUCT_REBATE, RebateCouponUse.UseData.toProductRebateList(result.getProductRebateDatas()));
        result.getMasterData().put(RebateConstants.PluginField.PRODUCT_REBATE_RULE_ID, rule.getId());

        getRebateFields(arg.getUser(), result.getRebateConditionField(), arg.getDetailObjectApiName(), arg.getPluginParam(), Lists.newArrayList(rule), stopWatch);
        stopWatch.lap("getRebateFields");
    }

    private void removeCanNotUseData(String ruleId, List<UseData> useDataList, List<RebateRule.Rule> rules, List<Rebate> currList) {
        for (int i = rules.size() - 1; i >= 0; i--) {
            if (ruleId.equals(rules.get(i).getId())) {
                rules.remove(i);
                break;
            }
        }

        if (CollectionUtils.notEmpty(useDataList) && CollectionUtils.notEmpty(currList)) {
            Set<String> ids = useDataList.stream().map(UseData::getId).collect(Collectors.toSet());
            for (int i = currList.size() - 1; i >= 0; i--) {
                if (ids.contains(currList.get(i).getId())) {
                    currList.remove(i);
                }
            }

        }
    }

    @Override
    public RebateCouponAutoUse.Result autoUse(RebateCouponAutoUse.Arg arg, RebateCouponMatch.Result... result1) {
        StopWatch stopWatch = StopWatch.createStarted("autoUse");
        RebateCouponAutoUse.Result result = RebateCouponAutoUse.Result.builder().build();
        RebateCouponAutoUse.CalcParam calcParam = getCalcParam(arg);

        // 查询规则
        RebateCouponQuery.Result queryResult = RebateCouponQuery.Result.builder().build();
        List<RebateRule.Rule> rules = (List<RebateRule.Rule>) rebateRuleDao.getDataByAccountId(arg.getUser(), arg.getMasterObjectApiName(), arg.getAccountId(), arg);
        stopWatch.lap("getRule");
        filterRules(arg, queryResult, stopWatch, rules);
        stopWatch.lap("filterRules");

        // 查询返利单
        findRebate(arg, calcParam, stopWatch);
        stopWatch.lap("findRebate");

        // 使用产品范围返利单
        useRangeRebate(arg, calcParam);
        stopWatch.lap("useRangeRebates");

        // 使用无范围返利单
        useNoRangeRebate(arg, calcParam);
        stopWatch.lap("useRebates");

//        if (CollectionUtils.notEmpty(calcParam.getUseRebateIds())
//                || CollectionUtils.notEmpty(calcParam.getOldOtherUseRebate())
//                || CollectionUtils.notEmpty(calcParam.getOldOtherRangeRebate())) {
        // 分摊
        List<UseData> useData = makeRebateUseData(calcParam.getUseRebate());
        useData.addAll(calcParam.getOldOtherUseRebate());
        RebateCouponMatch.Result match = useMatch(arg, stopWatch, calcParam, useData, result1);
        result.setRebateConditionField(match.getRebateConditionField());
        result.setMasterData(match.getMasterData());
        result.setDetailDataMap(match.getDetailDataMap());
        result.setAmount(calcParam.getTotalUseAmount());
        if (null == calcParam.getTotalUseAmount() || calcParam.getTotalUseAmount().compareTo(calcParam.getArg().getAmount()) < 0) {
            result.setAmountChange(true);
        }
//        } else {
//            result.setAmountChange(true);
//            result.setAmount(BigDecimal.ZERO);
//        }

        stopWatch.lap("fillResult");
        stopWatch.logSlow(500);
        return result;
    }

    protected RebateCouponMatch.Result useMatch(RebateCouponAutoUse.Arg arg, StopWatch stopWatch, RebateCouponAutoUse.CalcParam calcParam, List<UseData> useData, RebateCouponMatch.Result... result1) {
        RebateCouponMatch.Arg matchArg = RebateCouponMatch.Arg.builder()
                .masterData(calcParam.getArg().getMasterData())
                .detailDataList(calcParam.getArg().getDetailDataList())
                .detailDataMap(calcParam.getArg().getDetailDataMap())
                .detailObjectApiName(calcParam.getArg().getDetailObjectApiName())
                .masterObjectApiName(calcParam.getArg().getMasterObjectApiName())
                .edit(calcParam.getArg().isEdit())
                .user(calcParam.getArg().getUser())
                .rangeRebateDatas(makeRangeRebateData(calcParam))
                .rebateDatas(useData)
                .ruleId(StringUtils.isNotBlank(calcParam.getRuleId()) ? calcParam.getRuleId() : calcParam.getOldRuleId())
                .stopWatch(stopWatch)
                .calcParam(calcParam)
                .compulsoryRebate(calcParam.getArg().getCompulsoryRebate())
                .pluginParam(calcParam.getArg().getPluginParam()).build();
//        RebateCouponMatch.Result matchResult = RebateCouponMatch.Result.builder().build();
//        Map miscMap = CouponUtils.getMiscMap(arg.getMasterData().get(arg.getPluginParam().getFieldApiName(CouponConstants.PluginField.MISC_CONTENT)));
//        if (null != miscMap && CollectionUtils.notEmpty((List) miscMap.get(UseData.DATA_COUPON))) {
//            matchResult.getMiscContent().put(UseData.DATA_COUPON, (List) miscMap.get(UseData.DATA_COUPON));
//        }
        RebateCouponMatch.Result match = matchAmortize(matchArg, result1);
        return match;
    }

    protected RebateCouponAutoUse.CalcParam getCalcParam(RebateCouponAutoUse.Arg arg) {
        RebateCouponAutoUse.CalcParam calcParam = RebateCouponAutoUse.CalcParam.builder().arg(arg).build();

        String accountId = (String) arg.getMasterData().get(arg.getPluginParam().getFieldApiName(CouponConstants.PluginField.ACCOUNT_ID));
        arg.setAccountId(accountId);
        calcParam.setOldRuleId((String) arg.getMasterData().get(arg.getPluginParam().getFieldApiName(RebateConstants.PluginField.REBATE_RULE_ID)));
        Map miscMap = CouponUtils.getMiscMap(arg.getMasterData().get(arg.getPluginParam().getFieldApiName(CouponConstants.PluginField.MISC_CONTENT)));
        calcParam.setOldAllUseRebate(this.getUseData(UseData.DATA_REBATE, miscMap));
        calcParam.setOldAllRangeRebate(this.getRangeRebateData(miscMap));

        List<RebateCouponUse.UseData> oldOtherUseRebate = Lists.newArrayList();
        List<RebateCouponUse.UseData> oldUseRebate = Lists.newArrayList();
        splitOldUseData(arg.getFundAccountId(), calcParam.getOldAllUseRebate(), oldOtherUseRebate, oldUseRebate);
        calcParam.setOldOtherUseRebate(oldOtherUseRebate);
        calcParam.setOldUseRebate(oldUseRebate);

        List<RebateCouponUse.RangeRebateData> oldOtherRangeRebate = Lists.newArrayList();
        List<RebateCouponUse.RangeRebateData> oldRangeRebate = Lists.newArrayList();
        for (RebateCouponUse.RangeRebateData data : calcParam.getOldAllRangeRebate()) {
            String rangeRuleId = data.getRangeRuleId();
            List<RebateCouponUse.UseData> oldOtherUseData = Lists.newArrayList();
            List<RebateCouponUse.UseData> oldUseData = Lists.newArrayList();
            splitOldUseData(arg.getFundAccountId(), data.getRangeRebates(), oldOtherUseData, oldUseData);
            if (CollectionUtils.notEmpty(oldOtherUseData)) {
                oldOtherRangeRebate.add(RebateCouponUse.RangeRebateData.builder().rangeRuleId(rangeRuleId).rangeRebates(oldOtherUseData).build());
            }

            if (CollectionUtils.notEmpty(oldUseData)) {
                oldRangeRebate.add(RebateCouponUse.RangeRebateData.builder().rangeRuleId(rangeRuleId).rangeRebates(oldUseData).build());
            }
        }

        calcParam.setOldOtherRangeRebate(oldOtherRangeRebate);
        calcParam.setOldRangeRebate(oldRangeRebate);

        return calcParam;
    }

    private void splitOldUseData(String fundAccountId, List<RebateCouponUse.UseData> oldData, List<UseData> oldOtherUseRebate, List<UseData> oldUseRebate) {
        if (null == fundAccountId) {
            return;
        }
        for (UseData useData : oldData) {
            if (fundAccountId.equals(useData.getFundAccountId())) {
                oldUseRebate.add(useData);
            } else {
                oldOtherUseRebate.add(useData);
            }
        }
    }

    private void fillFundAccount(RebateCouponMatch.Arg arg, RebateCouponMatch.Result result) {
        List<UseData> useData = result.getRebateDatas();
        Map<String, BigDecimal> fundAccountUseAmount = Maps.newHashMap();
        sumFundAccountAmount(useData, fundAccountUseAmount);
        sumFundAccountAmount(result.getProductRebateDatas(), fundAccountUseAmount);

        List<RebateCouponUse.RangeRebateData> rangeRebateData = (List<RebateCouponUse.RangeRebateData>) result.getMiscContent().get(UseData.DATA_RANGE_REBATE);

        if (CollectionUtils.notEmpty(rangeRebateData)) {
            for (RebateCouponUse.RangeRebateData rebateData : rangeRebateData) {
                List<UseData> useRange = rebateData.getRangeRebates();
                sumFundAccountAmount(useRange, fundAccountUseAmount);
            }
        }

        if (null != arg.getCalcParam()) {
            arg.getCalcParam().setTotalUseAmount(fundAccountUseAmount.get(arg.getCalcParam().getArg().getFundAccountId()));
        }

        setFundAccount(arg, result, fundAccountUseAmount);
    }

    private void setFundAccount(RebateCouponMatch.Arg arg, RebateCouponMatch.Result result, Map<String, BigDecimal> fundAccountUseAmount) {
        String authorizedObjectDataId = arg.getMasterData().getId();
        FundAccountModel.AuthorizeResult fundAuths = fundAccountProxy.getAuthorization(FundAccountModel.Arg.builder()
                        .authorizedType(FundAccountModel.AUTHORIZED_TYPE)
                        .authorizedObjectDataId(authorizedObjectDataId)
                        .authorizedObjectApiName(arg.getMasterObjectApiName()).build(),
                SFAHeaderUtil.getHeaders(User.systemUser(arg.getUser().getTenantId())));
        if (null != fundAuths && null != fundAuths.getResult() && CollectionUtils.notEmpty(fundAuths.getResult().getDatas())) {
            for (FundAccountModel.AuthorizeData data : fundAuths.getResult().getDatas()) {
                BigDecimal amount = fundAccountUseAmount.get(data.getAuthorizeAccountId());
                if (FundAccountModel.ACCESS_MODULE_REBATE.equals(data.getAccessModule())
                        && (FundAccountModel.ACCOUNT_TYPE_MONEY.equals(data.getAccountType())
                        || FundAccountModel.ACCOUNT_TYPE_PRODUCT.equals(data.getAccountType()))) {

                    if (null == data.getTradeAmountFieldapiname()) {
                        log.warn("fundAccountProxy.getTradeAmountFieldapiname error:" + data.getAuthorizeAccountId());
                        // 返利账户未设置扣减字段。
                        throw new ValidateException(I18N.text("sfa.rebate.account.field.api.name.not.set"));
                    }

                    if (null != amount) {
                        result.getMasterData().put(data.getTradeAmountFieldapiname(), amount);
                    } else {
                        result.getMasterData().put(data.getTradeAmountFieldapiname(), null);
                    }
                }
                // 传入账户未使用，设置为0
                if (null != arg.getCalcParam() && arg.getCalcParam().getArg().getFundAccountId().equals(data.getAuthorizeAccountId()) && null == amount) {
                    result.getMasterData().put(data.getTradeAmountFieldapiname(), BigDecimal.ZERO);
                    arg.getCalcParam().setTotalUseAmount(BigDecimal.ZERO);
                }
            }
        } else {
            log.warn("fundAccountProxy.getAuthorization error:" + arg.getMasterObjectApiName());
            // 调用账户授权查询接口失败。 先记录日志，后续再放开，防止没有升级的租户报错
            //throw new ValidateException(I18N.text("sfa.rebate.account.get.auth.fail"));
        }
    }

    private static void sumFundAccountAmount(List<UseData> useData, Map<String, BigDecimal> fundAccountUseAmount) {
        if (CollectionUtils.notEmpty(useData)) {
            for (UseData data : useData) {
                fundAccountUseAmount.merge(data.getFundAccountId(), data.getAmount(), BigDecimal::add);
            }
        }
    }

    protected void useNoRangeRebate(RebateCouponAutoUse.Arg arg, RebateCouponAutoUse.CalcParam calcParam) {
        if (calcParam.getTotalUseAmount().compareTo(calcParam.getArg().getAmount()) < 0 && CollectionUtils.notEmpty(arg.getCanUseRules())) {
            RebateRule.Rule rule = arg.getCanUseRules().get(0);
            if (CollectionUtils.notEmpty(calcParam.getOldOtherUseRebate())) {
                rule = arg.getCanUseRules().stream().filter(x -> x.getId().equals(calcParam.getOldRuleId())).findFirst().orElse(rule);
            }
            List<Rebate> useRebate = useRebateByRule(arg, calcParam, rule);
            if (CollectionUtils.notEmpty(useRebate)) {
                calcParam.setUseRebate(useRebate);
            }

            calcParam.setRuleId(rule.getId());
        }
    }

    @NotNull
    protected void useRangeRebate(RebateCouponAutoUse.Arg arg, RebateCouponAutoUse.CalcParam calcParam) {
        List<RebateRule.Rule> rangeRules = calcParam.getArg().getRangeRules();
        Comparator<RebateRule.Rule> byPriorityAsc = Comparator.comparing(RebateRule.Rule::getPriority);
        Comparator<RebateRule.Rule> byLastModifiedTimeDesc = Comparator.comparing(RebateRule.Rule::getLastModifiedTime).reversed();
        Comparator<RebateRule.Rule> byIdDesc = Comparator.comparing(RebateRule.Rule::getId).reversed();
        Comparator<RebateRule.Rule> comparator = byPriorityAsc.thenComparing(byLastModifiedTimeDesc).thenComparing(byIdDesc);
        rangeRules.sort(comparator);

        List<RebateCouponAutoUse.RangeRebate> rangeRebates = Lists.newArrayList();
        for (RebateRule.Rule rule : rangeRules) {
            List<Rebate> rebateUseList = useRebateByRule(arg, calcParam, rule);
            if (CollectionUtils.empty(rebateUseList)) {
                continue;
            }

            rangeRebates.add(RebateCouponAutoUse.RangeRebate.builder().rangeRuleId(rule.getId()).rangeRebates(rebateUseList).build());

            if (calcParam.getTotalUseAmount().compareTo(calcParam.getArg().getAmount()) >= 0) {
                break;
            }
        }

        if (CollectionUtils.notEmpty(rangeRebates)) {
            calcParam.setUseRangeRebate(rangeRebates);
        }
    }

    @NotNull
    protected List<RebateCouponUse.RangeRebateData> makeRangeRebateData(RebateCouponAutoUse.CalcParam calcParam) {
        List<RebateCouponAutoUse.RangeRebate> rangeRebates = calcParam.getUseRangeRebate();
        List<RebateCouponUse.RangeRebateData> rangeRebateDataUse = Lists.newArrayList();
        Map<String, List<UseData>> oldRangeRebatesMap = Maps.newHashMap();
        List<RebateCouponUse.RangeRebateData> oldOtherRangeRebate = calcParam.getOldOtherRangeRebate();
        if (CollectionUtils.notEmpty(oldOtherRangeRebate)) {
            for (RebateCouponUse.RangeRebateData rangeRebate : oldOtherRangeRebate) {
                oldRangeRebatesMap.put(rangeRebate.getRangeRuleId(), rangeRebate.getRangeRebates());
            }
        }
        if (CollectionUtils.notEmpty(rangeRebates)) {
            for (RebateCouponAutoUse.RangeRebate rangeRebate : rangeRebates) {
                List<UseData> useData = makeRebateUseData(rangeRebate.getRangeRebates());
                List<UseData> useDataOld = oldRangeRebatesMap.get(rangeRebate.getRangeRuleId());
                if (CollectionUtils.notEmpty(useDataOld)) {
                    useData.addAll(useDataOld);
                    oldRangeRebatesMap.remove(rangeRebate.getRangeRuleId());
                }
                RebateCouponUse.RangeRebateData dataUse = RebateCouponUse.RangeRebateData.builder().rangeRuleId(rangeRebate.getRangeRuleId()).rangeRebates(useData).build();
                rangeRebateDataUse.add(dataUse);
            }
            if (CollectionUtils.notEmpty(oldRangeRebatesMap)) {
                for (Map.Entry<String, List<UseData>> entry : oldRangeRebatesMap.entrySet()) {
                    rangeRebateDataUse.add(RebateCouponUse.RangeRebateData.builder().rangeRuleId(entry.getKey()).rangeRebates(entry.getValue()).build());
                }
            }
        } else {
            rangeRebateDataUse.addAll(oldOtherRangeRebate);
        }

        return rangeRebateDataUse;
    }

    protected void findRebate(RebateCouponAutoUse.Arg arg, RebateCouponAutoUse.CalcParam calcParam, StopWatch stopWatch) {
        List<Rebate> dataList = (List<Rebate>) rebateDao.getDataByAccountId(arg.getUser(), arg.getMasterObjectApiName(), arg.getAccountId(), arg);
        Map<String, UseData> useDataList = Maps.newHashMap();
        getOldUseData(arg, useDataList, getUseDataKey(arg));

        if (CollectionUtils.notEmpty(useDataList)) {
            List<Rebate> currList = queryDataList(arg.getUser(), Lists.newArrayList(useDataList.keySet()), useDataList, true);
            addCurrData(dataList, currList);
        }

        dataList = this.filterDataByRule(arg, dataList, null, stopWatch);

        calcParam.setAllRebate(dataList);

        calcParam.setAllRebateIds(dataList.stream().map(Rebate::getId).collect(Collectors.toSet()));
        if (null != arg.getFundAccountId()) {
            calcParam.setFundAccountRebate(dataList.stream().filter(x -> arg.getFundAccountId().equals(x.getFundAccountId())).collect(Collectors.toList()));
        }
    }

    @Nullable
    private List<Rebate> useRebateByRule(RebateCouponAutoUse.Arg arg, RebateCouponAutoUse.CalcParam calcParam, RebateRule.Rule rule) {
        if (null == calcParam.getArg().getAmount() || BigDecimal.ZERO.compareTo(calcParam.getArg().getAmount()) == 0) {
            return null;
        }

        List<Rebate> dataListTemp = Lists.newArrayList(calcParam.getFundAccountRebate());
        dataListTemp = filterRuleRebates(arg, rule, dataListTemp);

        if (CollectionUtils.empty(dataListTemp)) {
            return null;
        }

        BigDecimal limitMoney = calcLimitMoney(arg, rule, false);
        BigDecimal otherUse = getOtherUse(calcParam, rule);

        limitMoney = limitMoney.subtract(otherUse);

        BigDecimal unUseAmount = calcParam.getArg().getAmount().subtract(calcParam.getTotalUseAmount());
        limitMoney = limitMoney.compareTo(unUseAmount) > 0 ? unUseAmount : limitMoney;
        if (limitMoney.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }

        Comparator<Rebate> byEndDateAsc = Comparator.comparing(Rebate::getEndDate);
        Comparator<Rebate> byStartDateAsc = Comparator.comparing(Rebate::getStartDate);
        Comparator<Rebate> byLastModifiedTimeDesc = Comparator.comparing(Rebate::getLastModifiedTime).reversed();
        Comparator<Rebate> byIdAsc = Comparator.comparing(Rebate::getId);
        Comparator<Rebate> comparator = byEndDateAsc.thenComparing(byStartDateAsc).thenComparing(byLastModifiedTimeDesc).thenComparing(byIdAsc);
        dataListTemp.sort(comparator);

        return getRebateList(calcParam, dataListTemp, limitMoney);
    }

    private static @NotNull BigDecimal getOtherUse(RebateCouponAutoUse.CalcParam calcParam, RebateRule.Rule rule) {
        BigDecimal otherUse = BigDecimal.ZERO;
        if (null != rule && RebateConstants.HAS_PRODUCT_RANGE.equals(rule.getProductRangeType())) {
            if (CollectionUtils.notEmpty(calcParam.getOldOtherRangeRebate())) {
                RebateCouponUse.RangeRebateData oldOtherRangeRebate = calcParam.getOldOtherRangeRebate().stream().filter(x -> x.getRangeRuleId().equals(rule.getId())).findFirst().orElse(null);
                if (null != oldOtherRangeRebate && CollectionUtils.notEmpty(oldOtherRangeRebate.getRangeRebates())) {
                    otherUse = oldOtherRangeRebate.getRangeRebates().stream().map(UseData::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
            }
        } else {
            if (CollectionUtils.notEmpty(calcParam.getOldOtherUseRebate())) {
                otherUse = calcParam.getOldOtherUseRebate().stream().map(UseData::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
        }
        return otherUse;
    }

    private  List<Rebate> getRebateList(RebateCouponAutoUse.CalcParam calcParam, List<Rebate> dataListTemp, BigDecimal limitMoney) {
        List<Rebate> rebateUseList = Lists.newArrayList();
        for (Rebate rebate : dataListTemp) {
            if (calcParam.getUseRebateIds().contains(rebate.getId())) {
                continue;
            }
            calcParam.getUseRebateIds().add(rebate.getId());
            BigDecimal useAmount = rebate.getUnusedAmount().compareTo(limitMoney) > 0 ? limitMoney : rebate.getUnusedAmount();
            limitMoney = limitMoney.subtract(useAmount);
            rebate.setUnusedAmount(useAmount);
            rebateUseList.add(rebate);
            calcParam.setTotalUseAmount(calcParam.getTotalUseAmount().add(useAmount));
            if (limitMoney.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
        }
        return rebateUseList;
    }


    /**
     * 自动使用
     *
     * @param arg
     * @return
     */
    @Override
    public RebateUseAmount.Result canUseAmount(RebateUseAmount.Arg arg, RebateCouponMatch.Result... result1) {
        StopWatch stopWatch = StopWatch.createStarted("canUseAmount");
        arg.setCompulsoryRebate(Boolean.TRUE);
        arg.setNeedRule(Boolean.TRUE);
        arg.setAccountId((String) arg.getMasterData().get(arg.getPluginParam().getFieldApiName(CouponConstants.PluginField.ACCOUNT_ID)));
        RebateUseAmount.Result result = RebateUseAmount.Result.builder().build();
        List<String> fundAccountIds = arg.getFundAccountIds();
        List<RebateRule.Rule> rules = (List<RebateRule.Rule>) rebateRuleDao.getDataByAccountId(arg.getUser(), arg.getMasterObjectApiName(), arg.getAccountId(), arg);
        stopWatch.lap("getRule");
        filterRules(arg, RebateCouponQuery.Result.builder().build(), stopWatch, rules);

        //arg.setFundAccountId(fundAccountIds.get(0));
        RebateCouponAutoUse.CalcParam calcParam = getCalcParam(arg);
        // 查询返利单
        findRebate(arg, calcParam, stopWatch);
        List<RebateUseAmount.UseAmount> useAmounts = Lists.newArrayList();
        for (String fundAccountId : fundAccountIds) {
            arg.setAmount(BigDecimal.valueOf(Long.MAX_VALUE));
            arg.setFundAccountId(fundAccountId);
            List<RebateCouponUse.UseData> oldOtherUseRebate = Lists.newArrayList();
            List<RebateCouponUse.UseData> oldUseRebate = Lists.newArrayList();
            splitOldUseData(arg.getFundAccountId(), calcParam.getOldAllUseRebate(), oldOtherUseRebate, oldUseRebate);
            calcParam.setOldOtherUseRebate(oldOtherUseRebate);
            calcParam.setOldUseRebate(oldUseRebate);
            RebateUseAmount.UseAmount useAmount = RebateUseAmount.UseAmount.builder().fundAccountId(fundAccountId).build();
            calcParam.setFundAccountRebate(calcParam.getAllRebate().stream().filter(x -> fundAccountId.equals(x.getFundAccountId())).collect(Collectors.toList()));

            // 使用产品范围返利单
            useRangeRebate(arg, calcParam);
            stopWatch.lap("useRangeRebates");

            // 使用无范围返利单
            useNoRangeRebate(arg, calcParam);
            stopWatch.lap("useRebates");

            List<RebateCouponUse.UseData> useData = makeRebateUseData(calcParam.getUseRebate());
            if (CollectionUtils.empty(useData) && CollectionUtils.empty(calcParam.getUseRangeRebate())) {
                useAmount.setAmount(BigDecimal.ZERO);
            } else {
                useMatch(arg, stopWatch, calcParam, useData);
                useAmount.setAmount(calcParam.getTotalUseAmount());
            }
            stopWatch.lap("useMatch");
            useAmounts.add(useAmount);
        }
        result.setUseAmount(useAmounts);
        stopWatch.logSlow(500);
        return result;
    }

    @Override
    public RebateCouponConditionField.Result getConditionFields(RebateCouponConditionField.Arg arg) {
        if (StringUtils.isNotBlank(arg.getDataId())) {
            return super.getConditionFields(arg);
        } else {
            StopWatch stopWatch = StopWatch.createStarted("getConditionFields");
            DomainPluginParam pluginParam = infraServiceFacade.findPluginParam(arg.getUser().getTenantId()
                    , arg.getMasterObjectApiName(), DmDefineConstants.REBATE);
            DomainPluginDescribeExt pluginParamExt = DomainPluginDescribeExt.of(DmDefineConstants.REBATE, pluginParam);
            List<IFilter> filters = Lists.newArrayList();
            RebateCouponQuery.Arg qArg = RebateCouponQuery.Arg.builder().user(arg.getUser()).build();
            rebateRuleDao.addByAccountFillFilter(arg.getUser(), arg.getMasterObjectApiName(), "", filters, qArg);
            List<RebateRule.Rule> rules = (List<RebateRule.Rule>) rebateRuleDao.queryData(arg.getUser(), filters, false, qArg);
            RebateCouponConditionField.Result result = RebateCouponConditionField.Result.builder().build();
            getRebateFields(arg.getUser(), result.getRebateConditionField(), arg.getDetailObjectApiName(), pluginParamExt, rules, stopWatch);
            return result;
        }
    }
}
