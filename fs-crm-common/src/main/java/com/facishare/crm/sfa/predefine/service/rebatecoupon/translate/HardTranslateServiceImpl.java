package com.facishare.crm.sfa.predefine.service.rebatecoupon.translate;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.CommonProductConstants;
import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.HardAggregateRuleData;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.HardModeValidateContext;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.HardProductData;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RuleWhere;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.BigDecimalUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.CouponUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.ExceptionUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.OptionalUtils;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.AggregateRuleConstants;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.util.AggregateRuleUtil;
import com.facishare.crm.util.DomainPluginDescribeExt;
import com.facishare.crm.util.SearchTemplateQueryPlus;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.reference.data.EntityReferenceArg;
import com.facishare.paas.reference.service.EntityReferenceService;
import com.google.common.collect.*;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 复杂翻译产品条件
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class HardTranslateServiceImpl extends AbsTranslateValidateService {
    private static final String MULTI_UNIT_PRODUCT_ID = "product_id";
    private static final String MULTI_UNIT_IS_ENABLE = "is_enable";
    private static final String MULTI_UNIT_UNIT_ID = "unit_id";
    private static final String HAS_PRODUCT_FLAG = "has_product_flag";
    private static final int MAX_AGGREGATE_COUNT = 5;
    private static final int MAX_AGGREGATE_MUST_COUNT = 1;
    private static final String AGG_RULE_IDSUFFIX = "_agg";


    @Override
    public boolean create(IObjectData objectData, User user) {
        if (BooleanUtils.isTrue(objectData.get(HAS_PRODUCT_FLAG, Boolean.class))) {
            boolean success = super.create(objectData, user);
            if (success) {
                createReferenceRelation(objectData, user, CouponConstants.REFERENCE_SOURCE_LABEL);
            }
        } else {
            createReferenceRelation(objectData, user, CouponConstants.REFERENCE_SOURCE_LABEL);

        }
        return true;
    }

    @Override
    protected String createAggCondition(IObjectData objectData, User user) {
        HardProductData hardProductData = getHardProductData(objectData);
        String aggCondition = createAggCondition(hardProductData, user.getTenantId());
        log.info("{} Translate condition is {}",
                objectData.get(CouponConstants.CouponPlanField.PRODUCT_CONDITION_CONTENT.getApiName(), String.class),
                aggCondition);
        return aggCondition;
    }

    /**
     * 创建聚合条件
     *
     * @param hardProductData 困难的产品数据
     * @return {@code String}
     */
    private String createAggCondition(HardProductData hardProductData, String tenantId) {
        //所有产品的id
        List<String> productIds = hardProductData.getData().stream()
                .filter(x -> !isCouponRangeType(x))
                .map(HardProductData.ProductData::getProductId)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(productIds)) {
            return Strings.EMPTY;
        }
        if (!bizConfigThreadLocalCacheService.isMultipleUnit(tenantId)) {
            List<RuleWhere> wheres = Lists.newArrayList();
            getProductIdsCondition(wheres, productIds);
            return JSON.toJSONString(wheres);
        }
        List<RuleWhere> couponWheres = Lists.newArrayList();
        List<String> filterProductIds = Lists.newArrayList();
        List<IObjectData> productDataList = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, productIds, Utils.PRODUCT_API_NAME);
        Map<String, Boolean> productIdToMultiple = productDataList.stream()
                .collect(Collectors.toMap(IObjectData::getId, data -> data.get(CommonProductConstants.Field.IsMultipleUnit.apiName, Boolean.class, false), (v1, v2) -> v2));
        Map<String, List<String>> unitToProductIds = Maps.newHashMap();
        for (HardProductData.ProductData productData : hardProductData.getData()) {
            String unitValue = productData.getUnitValue();
            //如果是没有单位的产品，或者非多单位产品，不拼单位，统一放到一个filter中
            if (StringUtils.isBlank(unitValue) || Boolean.FALSE.equals(productIdToMultiple.get(productData.getProductId()))) {
                filterProductIds.add(productData.getProductId());
                continue;
            }
            unitToProductIds.computeIfAbsent(unitValue, k -> Lists.newArrayList()).add(productData.getProductId());
        }
        unitToProductIds.forEach((unitValue, multipleUnitProductIdList) -> {
            List<RuleWhere.FiltersBean> filters = Lists.newArrayList();
            filters.add(RuleWhere.FiltersBean.builder()
                    .fieldName(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName())
                    .operator(Operator.IN.name())
                    .type(IFieldType.OBJECT_REFERENCE)
                    .fieldValues(multipleUnitProductIdList)
                    .build());
            filters.add(RuleWhere.FiltersBean.builder()
                    .fieldName(CouponConstants.PluginDetailField.ACTUAL_UNIT)
                    .operator(Operator.EQ.name())
                    .type(IFieldType.SELECT_ONE)
                    .fieldValues(Lists.newArrayList(unitValue))
                    .build());
            couponWheres.add(new RuleWhere(filters));
        });
        getProductIdsCondition(couponWheres, filterProductIds);
        if (couponWheres.size() > 20) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.COUPON_HARD_MODE_PRODUCT_TOTAL_TOO_MANY));
        }
        return JSON.toJSONString(couponWheres);
    }


    private HardProductData getHardProductData(IObjectData objectData) {
        String productConditionContent = OptionalUtils.str(objectData.get(CouponConstants.CouponPlanField.PRODUCT_CONDITION_CONTENT.getApiName(), String.class),
                SFAI18NKeyUtil.SFA_CONTACT_PARAM_EXCEPTION, "product condition content is null ");
        return ExceptionUtils.trySupplier(() -> JSON.parseObject(productConditionContent, HardProductData.class));
    }

    @Override
    public List<String> check(User user, List<String> planIds, String masterApiName, IObjectData masterData, List<IObjectData> detailDataList, DomainPluginDescribeExt pluginParam) {

        if (CollectionUtils.empty(planIds)) {
            return Lists.newArrayList();
        }
        //planIds已经根据客户和启用状态查询出来，此处可以直接查询
        List<IObjectData> couponPlans = serviceFacade.findObjectDataByIds(user.getTenantId(), planIds, CouponConstants.COUPON_PLAN_API_NAME);
        if (CollectionUtils.empty(couponPlans)) {
            return Lists.newArrayList();
        }
        //获取聚合规则ID
        Map<String, HardAggregateRuleData> aggregateRuleIds = getAggregateRuleIds(user, couponPlans);
        //计算聚合规则值
        Map<String, BigDecimal> aggregateRuleIdToValue = calculateAggregation(user, masterApiName, masterData, detailDataList, aggregateRuleIds, couponPlans);
        List<String> matchPlanIds = Lists.newArrayListWithCapacity(planIds.size());
        //对订单产品根据product_id,和unitValue来进行分组
        HashBasedTable<String, String, List<IObjectData>> productIdToUnitValToData = groupByProductIdToUnitVal(detailDataList, user, pluginParam);
        for (IObjectData couponPlan : couponPlans) {
            //非hard模式，不处理
            if (!CouponConstants.ProductConditionType.HARD.getValue().equals(couponPlan.get(CouponConstants.CouponPlanField.PRODUCT_CONDITION_TYPE.getApiName(), String.class))) {
                continue;
            }
            HardProductData hardProductData = getHardProductData(couponPlan);
            if (hardProductData == null) {
                continue;
            }
            if (!execHardValidate(aggregateRuleIds.get(couponPlan.getId()), aggregateRuleIdToValue, couponPlan,
                    hardProductData, detailDataList, productIdToUnitValToData)) {
                continue;
            }
            //表格数据中无必含的产品，不处理
            if (hardProductData.getData().stream().noneMatch(HardProductData.ProductData::getMustContain)) {
                matchPlanIds.add(couponPlan.getId());
                continue;
            }
            boolean result = doCheck(hardProductData, productIdToUnitValToData, aggregateRuleIdToValue, pluginParam, aggregateRuleIds.get(couponPlan.getId()));
            if (result) {
                matchPlanIds.add(couponPlan.getId());
            }
        }
        return matchPlanIds;
    }

    private boolean execHardValidate(HardAggregateRuleData hardAggregateRuleData, Map<String, BigDecimal> aggregateRuleIdToValue,
                                     IObjectData couponPlan, HardProductData hardProductData, List<IObjectData> detailDataList,
                                     HashBasedTable<String, String, List<IObjectData>> productIdToUnitValToData) {
        boolean passFlag = true;
        HardModeValidateContext context = HardModeValidateContext.builder()
                .aggregateRuleIdToValueMap(aggregateRuleIdToValue)
                .couponPlan(couponPlan)
                .hardProductData(hardProductData)
                .hardAggregateRuleData(hardAggregateRuleData)
                .detailDataList(detailDataList)
                .productIdToUnitValToData(productIdToUnitValToData)
                .build();
        for (Predicate<HardModeValidateContext> predicate : HardValidate.validatePredicate) {
            if (!predicate.test(context)) {
                passFlag = false;
                break;
            }
        }
        return passFlag;
    }


    /**
     * 当前订单计算聚合值
     *
     * @param user                  用户
     * @param masterApiName         主表apiName
     * @param masterData            主表数据
     * @param detailDataList        明细数据
     * @param hardAggregateRuleData
     * @return
     */
    private Map<String, BigDecimal> calculateAggregation(User user, String masterApiName, IObjectData masterData, List<IObjectData> detailDataList, Map<String, HardAggregateRuleData> hardAggregateRuleData, List<IObjectData> couponPlans) {
        //计算聚合规则值
        return doCalculateAggregation(user, masterApiName, masterData, detailDataList, hardAggregateRuleData, couponPlans);
    }

    private Map<String, BigDecimal> doCalculateAggregation(User user, String masterApiName, IObjectData masterData, List<IObjectData> detailDataList, Map<String, HardAggregateRuleData> hardAggregateRuleData, List<IObjectData> couponPlans) {
        if (hardAggregateRuleData == null) {
            return Maps.newHashMap();
        }
        Set<String> aggregateRuleIds = Sets.newHashSet();
        hardAggregateRuleData.forEach((k, v) -> {
            List<String> conditionAggregateRuleIds = v.getConditionAggregateRuleIds();
            List<String> hardAggregateRuleIds = v.getHardAggregateRuleIds();
            List<String> hardRelationAggregateRuleIds = v.getHardRelationAggregateRuleIds();
            aggregateRuleIds.addAll(conditionAggregateRuleIds);
            aggregateRuleIds.addAll(hardAggregateRuleIds);
            aggregateRuleIds.addAll(hardRelationAggregateRuleIds);
        });

        Map<String, String> aggregateRuleIdToStrValue = ruleEngineLogicService.computeAggregateValues(user, Sets.newHashSet(aggregateRuleIds)
                , masterApiName, masterData, detailDataList);
        if (CollectionUtils.empty(aggregateRuleIdToStrValue)) {
            return Maps.newHashMap();
        }

        // 获取重复项的聚合值
        getRepeatInAggregateRule(user, aggregateRuleIds, masterApiName, detailDataList, hardAggregateRuleData, couponPlans);

        //将聚合值转成BigDecimal
        return aggregateRuleIdToStrValue.entrySet()
                .stream()
                .filter(entry -> StringUtils.isNotBlank(entry.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> new BigDecimal(entry.getValue())));
    }

    // 获取重复项的聚合值
    private void getRepeatInAggregateRule(User user,
                                          Set<String> aggregateRuleIds,
                                          String masterApiName,
                                          List<IObjectData> detailDataList,
                                          Map<String, HardAggregateRuleData> hardAggregateRuleData,
                                          List<IObjectData> couponPlans) {
        // 获取每个聚合规则下对应的产品行
        Map<String, Set<String>> prodIDsForAggregateValues = ruleEngineLogicService.getProdIDsForAggregateValues(user, aggregateRuleIds, masterApiName, detailDataList);
        if (CollectionUtils.empty(prodIDsForAggregateValues)) {
            return;
        }
        // 查询聚合规则对应的聚合字段
        List<IObjectData> aggregateRuleDetail = queryAggregateRuleInBatches(user, Lists.newArrayList(aggregateRuleIds));

        Map<String, String> aggregateRuleIdToFieldMap = aggregateRuleDetail.stream().collect(Collectors.toMap(key -> key.get(DBRecord.ID, String.class), value -> value.get(AggregateRuleConstants.Field.AGGREGATE_FIELD, String.class), (v1, v2) -> v2));

        Map<String, IObjectData> rowToDetailMap = detailDataList.stream().collect(Collectors.toMap(a -> a.get(PricePolicyConstants.DATA_INDEX, String.class), Function.identity()));

        Map<String, Map<String, Boolean>> couponPlanIdToAggreRuleMustMap = Maps.newHashMap();
        //couponPlanId -> aggRuleId -> 是否必含
        aggregateRuleMustContainDeal(couponPlans, couponPlanIdToAggreRuleMustMap);

        repeatInAggregateRuleDeal(hardAggregateRuleData, couponPlanIdToAggreRuleMustMap, prodIDsForAggregateValues, aggregateRuleIdToFieldMap, rowToDetailMap);

    }

    /**
     * 处理必含聚合规则的数据
     * @param couponPlans
     * @param couponPlanIdToAggreRuleMustMap
     */
    private void aggregateRuleMustContainDeal(List<IObjectData> couponPlans, Map<String, Map<String, Boolean>> couponPlanIdToAggreRuleMustMap){
        for (IObjectData couponPlan : couponPlans) {
            HardProductData hardProductData = getHardProductData(couponPlan);
            Map<String, Boolean> aggregateRuleMustContainMap = hardProductData.getData().stream()
                    .filter(this::isCouponRangeType)
                    .collect(Collectors.toMap(HardProductData.ProductData::getProductId, HardProductData.ProductData::getMustContain));
            if(MapUtils.isNotEmpty(aggregateRuleMustContainMap)){
                couponPlanIdToAggreRuleMustMap.put(couponPlan.getId(), aggregateRuleMustContainMap);
            }
        }
    }

    /**
     * 计算聚合规则上重复的数据
     * @param hardAggregateRuleData
     * @param couponPlanIdToAggreRuleMustMap
     * @param prodIDsForAggregateValues
     * @param aggregateRuleIdToFieldMap
     * @param rowToDetailMap
     */
    private void repeatInAggregateRuleDeal(Map<String, HardAggregateRuleData> hardAggregateRuleData,
                      Map<String, Map<String, Boolean>> couponPlanIdToAggreRuleMustMap,
                      Map<String, Set<String>> prodIDsForAggregateValues,
                      Map<String, String> aggregateRuleIdToFieldMap,
                      Map<String, IObjectData> rowToDetailMap){
        hardAggregateRuleData.forEach((k, v) -> {
            Map<String, Set<String>> hardAggregateRuleIdToRowData = Maps.newHashMap();
            List<String> productIdList = Lists.newArrayList();

            // key -> 方案id
            // value -> HardAggregateRuleData 对应的聚合规则id相关数据
            // 聚合规则，聚合数量的，有多个聚合规则，则会有多个值
            List<String> hardAggregateRuleIds = v.getHardAggregateRuleIds();
            Map<String, Boolean> aggregateRuleMustContainMap = couponPlanIdToAggreRuleMustMap.get(k);

            if(!hardAggregateRuleIds.isEmpty()){
                // 将必含的移动到最前,只存在一个必含的聚合规则，移动后相当于置灰保留第一个聚合规则的产品行相关数据，移除其他聚合规则下的重复产品行，
                hardAggregateRuleIdSort(hardAggregateRuleIds, aggregateRuleMustContainMap);
                for (String hardAggregateRuleId : hardAggregateRuleIds) {
                    // 计算聚合规则间互相重复的产品行数据
                    hardAggregateRuleDuplicates(hardAggregateRuleIds, hardAggregateRuleId, prodIDsForAggregateValues, hardAggregateRuleIdToRowData, v);

                    // 聚合规则与产品项的重复产品行数据
                    hardAggregateWithProductRuleDuplicates(prodIDsForAggregateValues, hardAggregateRuleId,
                            v, aggregateRuleMustContainMap, hardAggregateRuleIdToRowData, rowToDetailMap, productIdList);
                }
            }
            // 产品条件 聚合 的产品重复项
            v.setRepeatproductIdList(productIdList);
            calculateDuplicateAggregation(hardAggregateRuleIdToRowData, aggregateRuleIdToFieldMap, rowToDetailMap, v);
        });
    }

    /**
     * 计算聚合规则与产品条件重复的 产品行数据
     * @param prodIDsForAggregateValues  产品条件 聚合出来的 产品行数据
     * @param hardAggregateRuleId        聚合规则 id
     * @param hardAggregateRuleData      hardAggregateRuleData
     * @param aggregateRuleMustContainMap  key -> 聚合规则id， value -> 是否必含
     * @param hardAggregateRuleIdToRowData  每个聚合规则下对应的产品行数据，即需要在后面计算的时候排除
     */
    private void hardAggregateWithProductRuleDuplicates(Map<String, Set<String>> prodIDsForAggregateValues,
                                                        String hardAggregateRuleId,
                                                        HardAggregateRuleData hardAggregateRuleData,
                                                        Map<String, Boolean> aggregateRuleMustContainMap,
                                                        Map<String, Set<String>> hardAggregateRuleIdToRowData,
                                                        Map<String, IObjectData> rowToDetailMap,
                                                        List<String> productIdList) {
        List<String> conditionAggregateRuleIds = hardAggregateRuleData.getConditionAggregateRuleIds();
        if (conditionAggregateRuleIds.isEmpty()) {
            return;
        }

        Set<String> hardAggregateRuleRowIds = prodIDsForAggregateValues.get(hardAggregateRuleId);
        for (String conditionAggregateRuleId : conditionAggregateRuleIds) {
            Set<String> productAggregateRowIds = prodIDsForAggregateValues.get(conditionAggregateRuleId);
            if (CollectionUtils.empty(hardAggregateRuleRowIds) || CollectionUtils.empty(productAggregateRowIds)) {
                continue;
            }

            Set<String> duplicatesRows = duplicates(hardAggregateRuleRowIds, productAggregateRowIds);
            if (CollectionUtils.empty(duplicatesRows)) {
                continue;
            }

            if (Boolean.TRUE.equals(aggregateRuleMustContainMap.get(hardAggregateRuleId))) {
                processMustContainAggregateRule(duplicatesRows, conditionAggregateRuleId, hardAggregateRuleIdToRowData, rowToDetailMap, productIdList);
            } else {
                processNonMustContainAggregateRule(duplicatesRows, hardAggregateRuleId, hardAggregateRuleData, hardAggregateRuleIdToRowData);
            }
        }
    }

    private void processMustContainAggregateRule(Set<String> duplicatesRows, String conditionAggregateRuleId,
                                                 Map<String, Set<String>> hardAggregateRuleIdToRowData,
                                                 Map<String, IObjectData> rowToDetailMap,
                                                 List<String> productIdList) {
        // 聚合规则是必含，记录重复项到产品的规则上，即扣除时扣除的是产品项聚合规则上的聚合值，保留聚合规则条件上的聚合值
        hardAggregateRuleIdToRowData.put(conditionAggregateRuleId, duplicatesRows);
        // 同时记录重复项产品行的productId，用于产品总数的计算
        for (String duplicatesRow : duplicatesRows) {
            IObjectData data = rowToDetailMap.get(duplicatesRow);
            if (data != null) {
                productIdList.add(data.get(MULTI_UNIT_PRODUCT_ID, String.class));
            }
        }
    }

    private void processNonMustContainAggregateRule(Set<String> duplicatesRows, String hardAggregateRuleId,
                                                    HardAggregateRuleData hardAggregateRuleData,
                                                    Map<String, Set<String>> hardAggregateRuleIdToRowData) {
        hardAggregateRuleIdToRowData.put(hardAggregateRuleId, duplicatesRows);
        // 聚合条件中，聚合数量对应的聚合金额的规则id
        String hardAggregateRuleIdRelationId = hardAggregateRuleData.getHardAggregateRuleIdRelationMap().get(hardAggregateRuleId);
        hardAggregateRuleIdToRowData.put(hardAggregateRuleIdRelationId, duplicatesRows);
    }

    /**
     * 计算聚合规则间的重复产品行数据
     * @param hardAggregateRuleIds          聚合规则的 id集合
     * @param hardAggregateRuleId          产品条件是 聚合规则 的 聚合规则 id
     * @param prodIDsForAggregateValues    所有聚合规则下对应的产品行
     * @param hardAggregateRuleIdToRowData key -> 聚合规则id， value -> 聚合规则下与其他聚合规则重复的产品行 data_index
     */
    private void hardAggregateRuleDuplicates(List<String> hardAggregateRuleIds,
                                             String hardAggregateRuleId,
                                             Map<String, Set<String>> prodIDsForAggregateValues,
                                             Map<String, Set<String>> hardAggregateRuleIdToRowData,
                                             HardAggregateRuleData hardAggregateRuleData) {
        Set<String> hardAggregateRuleRowIds = prodIDsForAggregateValues.get(hardAggregateRuleId);
        if (CollectionUtils.empty(hardAggregateRuleRowIds)) {
            return;
        }
        // 聚合规则下的产品行
        for (String rowId : hardAggregateRuleRowIds) {
            for (String otherHardAggregateRuleId : hardAggregateRuleIds) {
                if (!hardAggregateRuleId.equals(otherHardAggregateRuleId)) {
                    Set<String> otherHardAggregateRuleRowIds = prodIDsForAggregateValues.get(otherHardAggregateRuleId);
                    if (otherHardAggregateRuleRowIds != null && otherHardAggregateRuleRowIds.contains(rowId)) {
                        storeDuplicateRowData(otherHardAggregateRuleId, rowId, hardAggregateRuleIdToRowData, prodIDsForAggregateValues, hardAggregateRuleData, otherHardAggregateRuleRowIds);
                    }
                }
            }
        }
    }


    private void storeDuplicateRowData(String otherHardAggregateRuleId, String rowId,
                                       Map<String, Set<String>> hardAggregateRuleIdToRowData,
                                       Map<String, Set<String>> prodIDsForAggregateValues,
                                       HardAggregateRuleData hardAggregateRuleData,
                                       Set<String> otherHardAggregateRuleRowIds) {
        String otherHardAggregateRuleIdKey = otherHardAggregateRuleId + AGG_RULE_IDSUFFIX;
        Set<String> aggregateRepeatProductRow = hardAggregateRuleIdToRowData.get(otherHardAggregateRuleIdKey);
        // 聚合规则数量聚合对应的金额的聚合
        String hardAggregateRuleIdRelationId = hardAggregateRuleData.getHardAggregateRuleIdRelationMap().get(otherHardAggregateRuleId);
        if(CollectionUtils.empty(aggregateRepeatProductRow)){
            aggregateRepeatProductRow = new HashSet<>();
            hardAggregateRuleIdToRowData.put(otherHardAggregateRuleIdKey, aggregateRepeatProductRow);
            hardAggregateRuleIdToRowData.put(hardAggregateRuleIdRelationId+AGG_RULE_IDSUFFIX, aggregateRepeatProductRow);
        }
        aggregateRepeatProductRow.add(rowId);
        // 移除操作
        otherHardAggregateRuleRowIds.remove(rowId);
        // 设置回去,聚合数量和聚合金额的
        prodIDsForAggregateValues.put(otherHardAggregateRuleId, otherHardAggregateRuleRowIds);
        prodIDsForAggregateValues.put(hardAggregateRuleIdRelationId, otherHardAggregateRuleRowIds);



    }



    /**
     * 计算重复产品行的聚合值
     * @param hardAggregateRuleIdToRowData
     * @param aggregateRuleIdToFieldMap
     * @param rowToDetailMap
     * @param hardAggregateRuleData
     */
    private void calculateDuplicateAggregation(Map<String, Set<String>> hardAggregateRuleIdToRowData,
                      Map<String, String> aggregateRuleIdToFieldMap,
                      Map<String, IObjectData> rowToDetailMap,
                      HardAggregateRuleData hardAggregateRuleData){
        // 计算每个聚合规则与产品项重复的聚合值
        hardAggregateRuleIdToRowData.forEach((aggRuleId, productRows) -> {
            // 聚合的字段
            // aggRuleId 也可能为 aggRuleId_agg
            String ruleId = aggRuleId;
            int index = aggRuleId.indexOf("_");
            if (index != -1) {
                ruleId = aggRuleId.substring(0, index);
            }

            String aggregateField = aggregateRuleIdToFieldMap.get(ruleId);
            BigDecimal aggregateValue = new BigDecimal(BigInteger.ZERO);
            for (String productRowId : productRows) {
                String aggregateFieldValue = rowToDetailMap.get(productRowId).get(aggregateField, String.class);
                if(StringUtils.isNotBlank(aggregateFieldValue)){
                    aggregateValue = aggregateValue.add(new BigDecimal(aggregateFieldValue));
                }
            }
            hardAggregateRuleData.getAggregateRuleRepeat().put(aggRuleId, aggregateValue);
        });
    }

    private void hardAggregateRuleIdSort(List<String> hardAggregateRuleIds, Map<String, Boolean> aggregateRuleMustContainMap){
        // 将必含的移动到最前
        for (int i = 0; i < hardAggregateRuleIds.size(); i++) {
            if (MapUtils.isNotEmpty(aggregateRuleMustContainMap)
                    && Boolean.TRUE.equals(aggregateRuleMustContainMap.get(hardAggregateRuleIds.get(i)))) {
                String element = hardAggregateRuleIds.remove(i);
                hardAggregateRuleIds.add(0, element);
                break;
            }
        }
    }

    private List<IObjectData> queryAggregateRuleInBatches(User user, List<String> aggregateIds){
        List<IObjectData> aggregateRuleList = Lists.newArrayList();
        List<List<String>> partition = Lists.partition(aggregateIds, 200);
        for (List<String> ids : partition) {
            aggregateRuleList.addAll( queryAggregateRule(user, ids));
        }
        return aggregateRuleList;
    }

    /**
     * 查询聚合规则
     *
     * @param user               用户
     * @param aggregateIds       聚合规则id
     * @return {@code List<IObjectData>}
     */
    private List<IObjectData> queryAggregateRule(User user, List<String> aggregateIds) {
        SearchTemplateQuery searchTemplateQuery = buildAggregateSearchTemplateQuery(aggregateIds);
        return Optional.ofNullable(serviceFacade.findBySearchTemplateQueryWithFields(ActionContextExt.of(user, RequestContextManager.getContext()).getContext(), SFAPreDefine.AggregateRule.getApiName(),
                        searchTemplateQuery, Lists.newArrayList(DBRecord.ID, AggregateRuleConstants.Field.AGGREGATE_FIELD)))
                .map(QueryResult::getData)
                .orElse(Lists.newArrayList());
    }

    /**
     * 构建聚合规则查询模板
     *
     * @param aggregateIds id
     * @return {@code SearchTemplateQuery}
     */
    private SearchTemplateQuery buildAggregateSearchTemplateQuery(List<String> aggregateIds) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, DBRecord.ID, aggregateIds);
        searchTemplateQuery.addFilters(filters);
        searchTemplateQuery.setLimit(500);
        searchTemplateQuery.setOffset(0);
        return searchTemplateQuery;
    }

    private Set<String> duplicates(Set<String> set1, Set<String> set2){
        Set<String> duplicates = new HashSet<>(set1);
        duplicates.retainAll(set2);
        return duplicates;
    }

    private Map<String, HardAggregateRuleData> getAggregateRuleIds(User user, List<IObjectData> couponPlans) {
        Map<String, HardAggregateRuleData> couponPlanIdToHardAggregateRuleData = Maps.newHashMap();
        for (IObjectData couponPlan : couponPlans) {
            HardAggregateRuleData hardAggregateRuleData = new HardAggregateRuleData();
            //非hard模式，不处理
            if (!CouponConstants.ProductConditionType.HARD.getValue().equals(couponPlan.get(CouponConstants.CouponPlanField.PRODUCT_CONDITION_TYPE.getApiName(), String.class))) {
                continue;
            }
            //复杂条件中的聚合规则ID
            Optional.ofNullable(getHardProductData(couponPlan))
                    .map(HardProductData::getData)
                    .ifPresent(productDataList -> productDataList.stream()
                            .filter(this::isCouponRangeType)
                            .forEach(productData ->
                                    hardAggregateRuleData.getHardAggregateRuleIds().add(productData.getProductId())
                            ));
            String conditionRule = couponPlan.get(CouponConstants.CouponPlanField.PRODUCT_CONDITION_RULE.getApiName(), String.class);
            //解析product_condition_rule,获取聚合规则ID
            Optional.ofNullable(JSON.parseArray(conditionRule, RuleWhere.class))
                    .ifPresent(couponWheres -> {
                        //获取aggId
                        List<String> aggIds = couponWheres.stream()
                                .filter(aggRuleWhere -> CollectionUtils.notEmpty(aggRuleWhere.getFilters()))
                                //合并filter stream
                                .flatMap(aggRuleWhere -> aggRuleWhere.getFilters().stream())
                                .filter(filter -> isAggregateType(filter.getFieldNameType()))
                                .map(RuleWhere.FiltersBean::getFieldName)
                                .collect(Collectors.toList());
                        hardAggregateRuleData.getConditionAggregateRuleIds().addAll(aggIds);
                        hardAggregateRuleData.getCouponPlanIdToAggregateRuleIds().put(couponPlan.getId(), aggIds);
                    });
            couponPlanIdToHardAggregateRuleData.put(couponPlan.getId(), hardAggregateRuleData);
        }
        couponPlanIdToHardAggregateRuleData.forEach((k, hardAggregateRuleData) -> {
            List<String> hardAggregateRuleIds = hardAggregateRuleData.getHardAggregateRuleIds();
            //复杂条件中没有聚合规则ID，不处理
            if (CollectionUtils.empty(hardAggregateRuleIds)) {
                return;
            }
            //查询引用关系，获取引用关系的聚合规则ID
            List<EntityReferenceArg> byTargetTypeAndTargetValues = entityReferenceService.findByTargetTypeAndTargetValues(user.getTenantId(),
                    EntityReferenceService.COUPON_PLAN_RELATION, hardAggregateRuleIds, hardAggregateRuleIds.size());
            //容错处理，此处不应该为空
            if (CollectionUtils.empty(byTargetTypeAndTargetValues)) {
                return;
            }
            for (EntityReferenceArg entityReferenceArg : byTargetTypeAndTargetValues) {
                String sourceValue = entityReferenceArg.getSourceValue();
                hardAggregateRuleData.getHardRelationAggregateRuleIds().add(sourceValue);
                hardAggregateRuleData.getHardAggregateRuleIdRelationMap().put(entityReferenceArg.getTargetValue(), sourceValue);
            }
        });
        return couponPlanIdToHardAggregateRuleData;
    }


    /**
     * 执行校验
     *
     * @param hardProductData          校验数据
     * @param productIdToUnitValToData 产品ID->单位->数据
     * @param aggregateRuleIdToValue   聚合规则ID->聚合值
     * @param pluginParam              插件参数
     * @return boolean 是否通过校验
     */
    private boolean doCheck(HardProductData hardProductData, HashBasedTable<String, String, List<IObjectData>> productIdToUnitValToData, Map<String, BigDecimal> aggregateRuleIdToValue, DomainPluginDescribeExt pluginParam, HardAggregateRuleData hardAggregateRuleData) {
        Tuple<Integer, BigDecimal> result;
        if (CouponConstants.MustRelation.ALL.getValue().equals(hardProductData.getMustRela())) {
            result = doAllCheck(hardProductData, productIdToUnitValToData, aggregateRuleIdToValue, pluginParam, hardAggregateRuleData);
        } else if (CouponConstants.MustRelation.ANYN.getValue().equals(hardProductData.getMustRela())) {
            result = doAnyNCheck(hardProductData, productIdToUnitValToData, aggregateRuleIdToValue, pluginParam, hardAggregateRuleData);
        } else if (CouponConstants.MustRelation.ANYONE.getValue().equals(hardProductData.getMustRela())) {
            result = doAnyOneCheck(hardProductData, productIdToUnitValToData, aggregateRuleIdToValue, pluginParam, hardAggregateRuleData);
        } else {
            return false;
        }
        return getResult(result, hardProductData);
    }

    private Tuple<Integer, BigDecimal> doAnyOneCheck(HardProductData hardProductData, HashBasedTable<String, String, List<IObjectData>> productIdToUnitValToData, Map<String, BigDecimal> aggregateRuleIdToValue, DomainPluginDescribeExt pluginParam, HardAggregateRuleData hardAggregateRuleData) {
        return checkContainsRelation(1, hardProductData, productIdToUnitValToData, aggregateRuleIdToValue, pluginParam, hardAggregateRuleData);
    }

    private Tuple<Integer, BigDecimal> doAnyNCheck(HardProductData hardProductData, HashBasedTable<String, String, List<IObjectData>> productIdToUnitValToData,
                                                   Map<String, BigDecimal> aggregateRuleIdToValue, DomainPluginDescribeExt pluginParam, HardAggregateRuleData hardAggregateRuleData) {
        if (hardProductData.getMustN() == null) {
            return new Tuple<>(null, null);
        }
        return checkContainsRelation(hardProductData.getMustN(), hardProductData, productIdToUnitValToData, aggregateRuleIdToValue, pluginParam, hardAggregateRuleData);
    }

    private Tuple<Integer, BigDecimal> doAllCheck(HardProductData hardProductData, HashBasedTable<String, String, List<IObjectData>> productIdToUnitValToData, Map<String, BigDecimal> aggregateRuleIdToValue, DomainPluginDescribeExt pluginParam, HardAggregateRuleData hardAggregateRuleData) {
        //获取所有必含
        List<HardProductData.ProductData> mustContainProduct = hardProductData.getData().stream().filter(HardProductData.ProductData::getMustContain).collect(Collectors.toList());
        int mustContainProductCount = mustContainProduct.size();
        return checkContainsRelation(mustContainProductCount, hardProductData, productIdToUnitValToData, aggregateRuleIdToValue, pluginParam, hardAggregateRuleData);
    }

    private boolean getResult(Tuple<Integer, BigDecimal> resultTuple, HardProductData hardProductData) {
        return hardProductData.getMustNum() == null ? resultTuple.getKey() <= 0 : resultTuple.getKey() <= 0 && BigDecimalUtils.compare(resultTuple.getValue(), Operator.GTE, hardProductData.getMustNum());
    }

    /**
     * 必含所有校验，表格中，勾选必含的，必须所有的都要有,
     * mustContainProductCount最后必含产品数小于 0 则校验成功
     * count 统计满足条件数量
     *
     * @param mustContainProductCount 必须包含产品数
     * @param hardProductData         校验的产品数据
     * @param aggregateRuleIdToValue  聚合规则ID->聚合值
     * @param pluginParam             插件参数
     * @return int
     */
    private Tuple<Integer, BigDecimal> checkContainsRelation(Integer mustContainProductCount, HardProductData hardProductData,
                                                             HashBasedTable<String, String, List<IObjectData>> productIdToUnitValToData,
                                                             Map<String, BigDecimal> aggregateRuleIdToValue, DomainPluginDescribeExt pluginParam,
                                                             HardAggregateRuleData hardAggregateRuleData) {
        //必含产品
        List<HardProductData.ProductData> productMustContainList = Lists.newArrayList();
        //必含聚合规则
        List<HardProductData.ProductData> aggregateRuleMustContainList = Lists.newArrayList();
        splitData(hardProductData, productMustContainList, aggregateRuleMustContainList);
        //循环必选产品数据
        BigDecimal count = BigDecimal.ZERO;
        List<String> repeatproductIdList = hardAggregateRuleData.getRepeatproductIdList();
        for (HardProductData.ProductData productData : productMustContainList) {
            String productId = productData.getProductId();
            String unitValue = productData.getUnitValue();
            List<IObjectData> currentProductDates;
            if (StringUtils.isBlank(unitValue)) {
                //没有单位的时候，取所有单位
                currentProductDates = productIdToUnitValToData.row(productId).values().stream()
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());
            } else {
                currentProductDates = productIdToUnitValToData.get(productId, unitValue);
            }
            //必含产品中，没有值
            if (CollectionUtils.empty(currentProductDates)) {
                continue;
            }
            //获取产品数量
            @SuppressWarnings("java:S2259")
            BigDecimal sumQuantity = currentProductDates.stream()
                    .map(data -> data.get(pluginParam.getDefaultDetailFieldApiName(CouponConstants.PluginDetailField.QUANTITY), BigDecimal.class, BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //如果存在则继续校验数量
            if (productData.getContainNum() != null && BigDecimalUtils.compare(sumQuantity, Operator.LT, productData.getContainNum())) {
                continue;
            }
            // 如果已经包含在聚合规则上了，这里不进行累加计算，单上面每行的必含数量还是要计算
            if(!repeatproductIdList.contains(productId)){
                count = count.add(sumQuantity);
            }
            mustContainProductCount--;
        }
        //循环必选聚合规则数据
        for (HardProductData.ProductData productData : aggregateRuleMustContainList) {
            String aggregateRuleId = productData.getProductId();
            BigDecimal sumQuantity = aggregateRuleIdToValue.getOrDefault(aggregateRuleId, BigDecimal.ZERO);
            count = count.add(sumQuantity);
            //如果存在则继续校验数量
            if (productData.getContainNum() != null && BigDecimalUtils.compare(sumQuantity, Operator.LT, productData.getContainNum())) {
                continue;
            }
            mustContainProductCount--;
        }
        log.info("----->必含商品总数量:{}", count);

        return new Tuple<>(mustContainProductCount, count);
    }

    private void splitData(HardProductData hardProductData, List<HardProductData.ProductData> productMustContainList, List<HardProductData.ProductData> aggregateRuleMustContainList) {
        for (HardProductData.ProductData datum : hardProductData.getData()) {
            //不必含不用处理
            if (!Boolean.TRUE.equals(datum.getMustContain())) {
                continue;
            }
            if (isCouponRangeType(datum)) {
                aggregateRuleMustContainList.add(datum);
            } else {
                productMustContainList.add(datum);
            }
        }
    }

    /**
     * 根据产品id和单位进行分组
     *
     * @param detailDataList 详细的数据清单
     * @param user           用户
     * @param pluginParam    pluginParam
     * @return {@code HashBasedTable<String, String, List<IObjectData>>}
     */
    @NotNull
    private HashBasedTable<String, String, List<IObjectData>> groupByProductIdToUnitVal(List<IObjectData> detailDataList, User user, DomainPluginDescribeExt pluginParam) {
        List<String> productIdList = detailDataList.stream()
                .map(detail -> detail.get(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName(), String.class))
                .collect(Collectors.toList());
        List<IObjectData> productDataList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), productIdList, Utils.PRODUCT_API_NAME);

        HashBasedTable<String, String, List<IObjectData>> productIdToUnitVToData = HashBasedTable.create();
        if (CollectionUtils.empty(productDataList)) {
            return productIdToUnitVToData;
        }
        Map<String, Boolean> productIdToMultiple = productDataList.stream()
                .collect(Collectors.toMap(IObjectData::getId, data -> data.get(CommonProductConstants.Field.IsMultipleUnit.apiName, Boolean.class, false), (v1, v2) -> v2));
        for (IObjectData detail : detailDataList) {
            String productId = detail.get(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName(), String.class);
            String unitValue;
            if (Boolean.TRUE.equals(productIdToMultiple.getOrDefault(detail.get(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName(), String.class), false))) {
                String unitIdApiName = pluginParam.getDefaultDetailFieldApiName(CouponConstants.PluginDetailField.ACTUAL_UNIT);
                unitValue = detail.get(unitIdApiName, String.class);
            } else {
                String unitApiName = pluginParam.getDefaultDetailFieldApiName(CouponConstants.PluginDetailField.UNIT_FLAG);
                unitValue = detail.get(unitApiName, String.class);
            }
            unitValue = StringUtils.isBlank(unitValue) ? "non" : unitValue;
            List<IObjectData> iObjectData = productIdToUnitVToData.get(productId, unitValue);
            if (iObjectData == null) {
                iObjectData = Lists.newArrayList(detail);
                productIdToUnitVToData.put(productId, unitValue, iObjectData);
            } else {
                iObjectData.add(detail);
            }
        }
        return productIdToUnitVToData;
    }


    @Override
    public void validate(IObjectData objectData, User user) {
        HardProductData hardProductData = getHardProductData(objectData);
        if (null == hardProductData) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_COUPON_RULE_CONDITION_CANNOT_EMPTY, "hard condition is empty").get();
        }
        if (CollectionUtils.empty(hardProductData.getData())) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_COUPON_RULE_CONDITION_CANNOT_EMPTY, "hard condition is empty").get();
        }
        int productCount = hardProductData.getData().size();
        if (productCount > MAX_PRODUCT_COUNT) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_COUPON_RULE_CONDITION_HARD_PRODUCT_MAX).get();
        }
        OptionalUtils.str(hardProductData.getMustRela(), SFAI18NKeyUtil.SFA_COUPON_RULE_CONDITION_MUST_REAL_CANNOT_EMPTY);
        if (hardProductData.getData().stream()
                .anyMatch(x -> x.getMustContain() && x.getContainNum() == null)) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_COUPON_RULE_CONDITION_CONTAIN_NUM_CANNOT_EMPTY, "hard condition contain num is empty").get();
        }

        if (CouponConstants.MustRelation.ANYN.getValue().equals(hardProductData.getMustRela())) {
            if (hardProductData.getMustN() == null) {
                throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_COUPON_RULE_CONDITION_N_CANNOT_EMPTY, "hard condition n value is empty").get();
            }
            //必含产品的个数，小于 N值,则永远不会匹配到
            Integer mustContainCount = hardProductData.getData().stream().filter(HardProductData.ProductData::getMustContain).map(x -> 1).reduce(0, Integer::sum);
            if (mustContainCount != 0 && mustContainCount < hardProductData.getMustN()) {
                throw ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_COUPON_RULE_CONDITION_N_CANNOT_GT_MUST_CONTAIN, "hard condition n gt must contain").get();
            }
        }
        //校验产品是否存在
        Map<String, String> idToName = hardProductData.getData().stream()
                .filter(x -> !isCouponRangeType(x))
                .collect(Collectors.toMap(HardProductData.ProductData::getProductId,
                        HardProductData.ProductData::getProduct_id__r,
                        (v1, v2) -> v2));
        objectData.set(HAS_PRODUCT_FLAG, !idToName.isEmpty());
        validateProduct(idToName, user, SFAPreDefine.Product.getApiName());
        validateAggregateRule(hardProductData, user, objectData);
        //校验必含商品数量
        validateMustNum(hardProductData);
        Optional.ofNullable(hardProductData.getLimitTotal())
                .ifPresent(limitTotal -> {
                    if (BigDecimalUtils.compare(limitTotal, Operator.LTE, BigDecimal.ZERO)) {
                        throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_COUPON_RULE_CONDITION_LIMIT_TOTAL_CANNOT_LT_ZERO));
                    }
                });
    }

    /**
     * 校验聚合规则的itme
     *
     * @param hardProductData
     * @param user
     * @param objectData
     */
    private void validateAggregateRule(HardProductData hardProductData, User user, IObjectData objectData) {
        List<HardProductData.ProductData> data = hardProductData.getData();
        //收集聚合规则ID，来进行校验
        Map<String, String> idToName = data.stream()
                .filter(this::isCouponRangeType)
                .collect(Collectors.toMap(HardProductData.ProductData::getProductId,
                        HardProductData.ProductData::getProduct_id__r,
                        (v1, v2) -> v2));
        if (CollectionUtils.empty(idToName)) {
            return;
        }
        // 校验聚合规则的总数量及必含数量
        validateAggregateRuleCount(objectData);
        validateAggItem(data);
        List<IObjectData> aggregateRuleList = queryAggregateRuleById(Lists.newArrayList(idToName.keySet()), user);
        validateAggExist(idToName, aggregateRuleList);
        //查询聚合规则的关联数据
        List<EntityReferenceArg> entityReferenceArgs = queryRelationData(idToName, user);
        //校验关联关系是否存在
        validateRelationDataExist(idToName, entityReferenceArgs);
        //查询关联的聚合规则数据
        List<IObjectData> objectDataList = queryAggregateRuleById(entityReferenceArgs.stream().map(EntityReferenceArg::getSourceValue).collect(Collectors.toList()), user);
        //校验关联的聚合规则是否存在
        validateRelationAggDataExist(objectDataList, idToName, entityReferenceArgs);
        //回填聚合规则ID的值，后续进行关联
        Tuple<HashMultimap<String, String>, Set<String>> hashMultimapSetTuple = new Tuple<>(HashMultimap.create(), idToName.keySet());
        objectData.set(CONDITION_FIELD_AND_AGGREGATE_INFO_KEY, hashMultimapSetTuple);
    }

    private void validateRelationAggDataExist(List<IObjectData> objectDataList, Map<String, String> idToName, List<EntityReferenceArg> entityReferenceArgs) {
        if (idToName.size() == objectDataList.size()) {
            return;
        }
        //entityReferenceArgs 转成map，key是sourceValue，value是targetValue
        Map<String, String> sourceValueToTargetValue = entityReferenceArgs.stream()
                .collect(Collectors.toMap(EntityReferenceArg::getSourceValue, EntityReferenceArg::getTargetValue, (v1, v2) -> v2));
        Set<String> relationAggId = objectDataList.stream()
                .map(DBRecord::getId)
                .collect(Collectors.toSet());
        idToName.forEach((k, v) -> {
            String targetValue = sourceValueToTargetValue.get(k);
            if (!relationAggId.contains(targetValue)) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_COUPON_RULE_CONDITION_RELATION_AGGREGATE_RULE_NOT_EXIST, v));
            }
        });
    }

    private void validateRelationDataExist(Map<String, String> idToName, List<EntityReferenceArg> entityReferenceArgs) {
        if (idToName.size() != entityReferenceArgs.size()) {
            //收集目标值
            Set<String> dbIdSet = entityReferenceArgs.stream()
                    .map(EntityReferenceArg::getTargetValue)
                    .collect(Collectors.toSet());
            for (Map.Entry<String, String> entry : idToName.entrySet()) {
                if (!dbIdSet.contains(entry.getKey())) {
                    throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_COUPON_RULE_CONDITION_AGGREGATE_RULE_NOT_EXIST, entry.getValue()));
                }
            }
        }
    }

    private List<EntityReferenceArg> queryRelationData(Map<String, String> idToName, User user) {
        return Optional.ofNullable(entityReferenceService.findByTargetTypeAndTargetValues(user.getTenantId(), EntityReferenceService.COUPON_PLAN_RELATION, Lists.newArrayList(idToName.keySet()), idToName.size()))
                .orElse(Lists.newArrayList());
    }

    private void validateAggExist(Map<String, String> idToName, List<IObjectData> aggregateRuleList) {
        if (idToName.size() != aggregateRuleList.size()) {
            Set<String> dbIdSet = aggregateRuleList.stream()
                    .map(IObjectData::getId)
                    .collect(Collectors.toSet());
            for (Map.Entry<String, String> entry : idToName.entrySet()) {
                if (!dbIdSet.contains(entry.getKey())) {
                    throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_COUPON_HARD_NOT_FIND_AGGREGATE_RULE, entry.getValue()));
                }
            }
        }
        for (IObjectData objectData : aggregateRuleList) {
            //是否是优惠券使用范围
            if (!AggregateRuleUtil.isCouponPlanRange((objectData))) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_COUPON_RULE_CONDITION_AGGREGATE_RULE_NOT_COUPON_RANGE, objectData.get(IObjectData.NAME)));
            }
        }
    }

    private List<IObjectData> queryAggregateRuleById(List<String> ids, User user) {
        SearchTemplateQueryPlus queryPlus = new SearchTemplateQueryPlus();
        queryPlus.addFilter(DBRecord.ID, Operator.IN, ids)
                .addFilter(DBRecord.IS_DELETED, Operator.EQ, String.valueOf(DELETE_STATUS.NORMAL.getValue()));
        queryPlus.setLimit(1000);
        queryPlus.setOffset(0);
        queryPlus.setPermissionType(0);
        //这里配置的总数不会超过200个，可以直接使用
        queryPlus.setLimit(ids.size());
        queryPlus.setNeedReturnCountNum(Boolean.FALSE);
        queryPlus.setNeedReturnQuote(Boolean.FALSE);
        return Optional.ofNullable(serviceFacade.findBySearchQuery(user, SFAPreDefine.AggregateRule.getApiName(), queryPlus))
                .map(QueryResult::getData)
                .orElse(Lists.newArrayList());
    }

    /**
     * 优惠券可用范围的item不可以有单位
     * 不可以有重复的聚合规则
     *
     * @param data 配置数据
     */
    private void validateAggItem(List<HardProductData.ProductData> data) {
        Set<String> aggregateRuleIds = Sets.newHashSet();
        for (HardProductData.ProductData datum : data) {
            if (!isCouponRangeType(datum)) {
                continue;
            }
            if (aggregateRuleIds.contains(datum.getProductId())) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_COUPON_RULE_CONDITION_AGGREGATE_RULE_CANNOT_REPEAT, datum.getProduct_id__r()));
            }
            aggregateRuleIds.add(datum.getProductId());
            //判断单位是否有值，有值则抛出异常
            if (StringUtils.isNotBlank(datum.getUnitValue())) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_COUPON_RULE_CONDITION_AGGREGATE_RULE_UNIT_CANNOT_EMPTY, datum.getProduct_id__r()));
            }
        }
    }

    /**
     * 是否是优惠券使用范围
     *
     * @param data
     * @return boolean
     */
    private boolean isCouponRangeType(HardProductData.ProductData data) {
        return CouponUtils.isCouponRangeType(data);
    }

    /**
     * 验证必含数量
     *
     * @param hardProductData 困难的产品数据
     */
    private void validateMustNum(HardProductData hardProductData) {
        //是否存在必含商品
        boolean hasMustContains = hardProductData.getData().stream().anyMatch(x -> Optional.ofNullable(x.getMustContain()).orElse(false));
        if (hasMustContains) {
            BigDecimal mustContainsNumOut = Optional.ofNullable(hardProductData.getMustNum())
                    .filter(mustContainsNum -> BigDecimalUtils.compare(mustContainsNum, Operator.GT, BigDecimal.ZERO))
                    .orElseThrow(ExceptionUtils.supplier(SFAI18NKeyUtil.SFA_COUPON_RULE_CONDITION_MUST_CONTAINS_NUM, "must contains num is null"));
            log.info("mustContainsNum is {}", mustContainsNumOut);
        }
    }

    @Override
    public void render(IObjectData objectData, User user) {

        HardProductData hardProductData = getHardProductData(objectData);


        renderProduct(user, hardProductData);
        renderAggregateRule(user, hardProductData);
        String hardProductJson = JSON.toJSONString(hardProductData);
        objectData.set(CouponConstants.CouponPlanField.PRODUCT_CONDITION_CONTENT.getApiName(), hardProductJson);

    }

    private void renderAggregateRule(User user, HardProductData hardProductData) {
        List<String> aggregateRuleIds = hardProductData.getData().stream()
                .filter(this::isCouponRangeType)
                .map(HardProductData.ProductData::getProductId)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(aggregateRuleIds)) {
            return;
        }
        List<IObjectData> objectDataList = queryAggregateRuleById(aggregateRuleIds, user);
        Map<String, IObjectData> idToAggDataMap = objectDataList.stream()
                .collect(Collectors.toMap(IObjectData::getId, data -> data, (v1, v2) -> v2));
        for (HardProductData.ProductData data : hardProductData.getData()) {
            if (!isCouponRangeType(data)) {
                continue;
            }
            String aggName = Optional.ofNullable(idToAggDataMap.get(data.getProductId()))
                    .map(aggData -> aggData.get(IObjectData.NAME, String.class))
                    .orElse(Strings.EMPTY);
            data.setProduct_id__r(aggName);
        }
    }

    private void renderProduct(User user, HardProductData hardProductData) {
        List<String> productIds = hardProductData.getData().stream()
                .filter(data -> !isCouponRangeType(data))
                .map(HardProductData.ProductData::getProductId)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(productIds)) {
            return;
        }
        //查询产品信息
        Map<String, IObjectData> productIdToData = Optional.ofNullable(
                serviceFacade.findObjectDataByIds(user.getTenantId(), productIds, Utils.PRODUCT_API_NAME))
                .map(productDates -> productDates.stream().collect(Collectors.toMap(IObjectData::getId, data -> data, (v1, v2) -> v2)))
                .orElse(Maps.newHashMap());
        //获取有多单位的产品信息
        List<String> hasMultipleUnitProductIds = productIdToData.values().stream()
                //保留有多单位的产品
                .filter(data -> Optional.ofNullable(data.get(CommonProductConstants.Field.IsMultipleUnit.apiName, Boolean.class)).orElse(false))
                .map(IObjectData::getId)
                .collect(Collectors.toList());
        //查询多单位关联信息
        HashBasedTable<String, String, IObjectData> multiUnitRelatedData = HashBasedTable.create();
        if (CollectionUtils.notEmpty(hasMultipleUnitProductIds)) {
            multiUnitRelatedData.putAll(getMultiUnitRelatedData(hasMultipleUnitProductIds, user));
        }
        //查询多单位信息
        Map<String, String> unitIdToName = queryMultiUnitInfo(multiUnitRelatedData, user);
        Map<String, String> optionsUnit = getOptionsUnit(user);
        //判断displayName是否开启
        IObjectDescribe object = serviceFacade.findObject(user.getTenantId(), Utils.PRODUCT_API_NAME);
        // 回显字段设置
        for (HardProductData.ProductData data : hardProductData.getData()) {
            if (isCouponRangeType(data)) {
                continue;
            }
            String productId = data.getProductId();
            IObjectData productIdObjectData = productIdToData.get(productId);
            if (null == productIdObjectData) {
                data.setProduct_id__r(Strings.EMPTY);
                continue;
            } else {
                if (Boolean.TRUE.equals(object.isOpenDisplayName())) {
                    data.setProduct_id__r(productIdObjectData.getDisplayName());
                } else {
                    data.setProduct_id__r(productIdObjectData.getName());
                }
            }
            //如果条件中没有设置单位条件，continue
            String conditionUnitValue = data.getUnitValue();
            if (StringUtils.isBlank(conditionUnitValue)) {
                continue;
            }
            //产品没开多单位，跟产品的unit进行比较
            if (Optional.ofNullable(productIdObjectData.get(CommonProductConstants.Field.IsMultipleUnit.apiName, Boolean.class)).orElse(false)) {
                data.setIsMultipleUnit(true);
                //多单位关联信息
                IObjectData relatedObjectData = multiUnitRelatedData.get(productId, conditionUnitValue);
                if (relatedObjectData == null) {
                    data.setUnit__r(Strings.EMPTY);
                    continue;
                }
                data.setUnit__r(unitIdToName.get(conditionUnitValue));
            } else {
                data.setIsMultipleUnit(false);
                String productUnit = productIdObjectData.get(ProductConstants.UNIT, String.class);
                if (conditionUnitValue.equals(productUnit)) {
                    data.setUnit__r(optionsUnit.get(conditionUnitValue));
                } else {
                    data.setUnit__r(Strings.EMPTY);
                }
            }
        }
    }

    /**
     * 得到选择单位
     *
     * @param user 用户
     * @return {@code Map<String, String>}
     */
    private Map<String, String> getOptionsUnit(User user) {
        IObjectDescribe productDescribe = serviceFacade.findObject(user.getTenantId(), Utils.PRODUCT_API_NAME);
        Map<String, IFieldDescribe> fieldDescribeMap = productDescribe.getFieldDescribeMap();
        IFieldDescribe unitFieldDesc = fieldDescribeMap.get(ProductConstants.UNIT);
        if (!(unitFieldDesc instanceof SelectOneFieldDescribe)) {
            return Maps.newHashMap();
        }
        SelectOneFieldDescribe unitSelectOne = (SelectOneFieldDescribe) unitFieldDesc;
        return unitSelectOne.getSelectOptions().stream()
                .collect(Collectors.toMap(ISelectOption::getValue, ISelectOption::getLabel, (v1, v2) -> v2));
    }

    /**
     * 查询多单位信息
     *
     * @param multiUnitRelatedData 多单元相关数据
     * @param user                 用户
     * @return {@code Map<String, String>}
     */
    private Map<String, String> queryMultiUnitInfo(HashBasedTable<String, String, IObjectData> multiUnitRelatedData, User user) {
        Set<String> unitIds = multiUnitRelatedData.columnKeySet();
        if (CollectionUtils.empty(unitIds)) {
            return Maps.newHashMap();
        }
        return Optional.ofNullable(serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(unitIds), Utils.UNIT_INFO_API_NAME))
                .map(iObjectData -> iObjectData.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName, (v1, v2) -> v2)))
                .orElse(Maps.newHashMap());
    }

    /**
     * 多单元相关数据
     *
     * @param hasMultipleUnitProductIds 有多个单位产品id
     * @param user                      用户
     * @return {@code HashBasedTable<String, String, IObjectData>}
     */
    private HashBasedTable<String, String, IObjectData> getMultiUnitRelatedData(List<String> hasMultipleUnitProductIds, User user) {
        SearchTemplateQueryExt ext = SearchTemplateQueryExt.of(new SearchTemplateQuery())
                .addFilter(Operator.IN, MULTI_UNIT_PRODUCT_ID, hasMultipleUnitProductIds)
                .addFilter(Operator.EQ, MULTI_UNIT_IS_ENABLE, Lists.newArrayList(Boolean.TRUE.toString()));
        ext.addDeletedFilterIfNoDeletedFilter();
        ext.setLimit(100);
        ext.setOffset(0);
        ext.setPermissionType(0);
        List<IObjectData> multiUnitDates = Optional.ofNullable(serviceFacade.findBySearchQuery(user, Utils.MULTI_UNIT_RELATED_API_NAME, (SearchTemplateQuery) ext.getQuery()))
                .map(QueryResult::getData)
                .orElse(Lists.newArrayList());
        HashBasedTable<String, String, IObjectData> productIdToUnitToData = HashBasedTable.create();
        for (IObjectData multiUnitData : multiUnitDates) {
            productIdToUnitToData.put(multiUnitData.get(MULTI_UNIT_PRODUCT_ID, String.class), multiUnitData.get(MULTI_UNIT_UNIT_ID, String.class), multiUnitData);
        }
        return productIdToUnitToData;
    }

    // 校验聚合规则的总数量及必含数量
    public void validateAggregateRuleCount(IObjectData objectData){
        // 新增或编辑时没有优惠券实例，进行校验
        HardProductData hardProductData = getHardProductData(objectData);
        List<HardProductData.ProductData> data = hardProductData.getData();
        // 聚合条件最多5个，只有一个是必含
        //收集聚合规则ID，来进行校验
        int aggregateRuleNum = 0;
        int aggregateRuleMustContainNum = 0;
        for (HardProductData.ProductData datum : data) {
            if(this.isCouponRangeType(datum)){
                aggregateRuleNum++;
                if(Boolean.TRUE.equals(datum.getMustContain())){
                    aggregateRuleMustContainNum++;
                }
            }
        }
        if(aggregateRuleNum > MAX_AGGREGATE_COUNT){
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_COUPON_PLAN_AGGREGATE_RULE_LIMIT_TOTAL_CANNOT_GT_FIVE));
        }
        if(aggregateRuleMustContainNum > MAX_AGGREGATE_MUST_COUNT){
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_COUPON_PLAN_AGGREGATE_MUST_CONTAIN_RULE_LIMIT_TOTAL_CANNOT_GT_ONE));
        }
    }

}
