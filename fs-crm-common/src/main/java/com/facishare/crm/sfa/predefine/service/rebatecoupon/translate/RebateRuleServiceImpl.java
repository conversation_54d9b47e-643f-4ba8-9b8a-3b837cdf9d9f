package com.facishare.crm.sfa.predefine.service.rebatecoupon.translate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.constants.RebateConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.ConditionData;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RebateRule;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RuleWhere;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.BigDecimalUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.ExceptionUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.OptionalUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.RebatePolicyCalculateProUtil;
import com.facishare.crm.sfa.utilities.common.PricePolicyRuleDataParse;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.util.DescribeI18NUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 使用返利规则条件保存逻辑
 *
 * <AUTHOR>
 * @date 2021/12/20
 */
@Service
@Slf4j
public class RebateRuleServiceImpl extends AbsTranslateValidateService {
    @Autowired
    private PricePolicyRuleDataParse pricePolicyRuleDataParse;

    @Override
    public boolean create(IObjectData objectData, User user) {
        //1、解析条件
        List<RuleWhere> masterCondition = getMasterCondition(objectData);
        //没有condition，适用所有产品
        if (CollectionUtils.empty(masterCondition)) {
            createReferenceRelation(objectData, user, RebateConstants.REBATE_RULE_REFERENCE_SOURCE_LABEL);
            return true;
        }
        //2、保存到规则引擎
        boolean success = saveRuleToEngine(masterCondition, objectData, user);
        if (success) {
            //创建关联关系
            createReferenceRelation(objectData, user, RebateConstants.REBATE_RULE_REFERENCE_SOURCE_LABEL);
        }
        return success;
    }


    /**
     * 获取条件
     *
     * @param objectData 对象数据
     * @return {@code List<RuleWhere>}
     */
    private List<RuleWhere> getMasterCondition(IObjectData objectData) {
        JSONObject jsonObject = getRuleJSONObject(objectData);
        return getAggRuleWheres(jsonObject);
    }

    /**
     * 获取条件
     *
     * @param ruleContentJsonObject 规则内容json对象
     * @return {@code List<RuleWhere>}
     */
    private List<RuleWhere> getMasterCondition(JSONObject ruleContentJsonObject) {
        return getAggRuleWheres(ruleContentJsonObject);
    }

    @Nullable
    private List<RuleWhere> getAggRuleWheres(JSONObject jsonObject) {
        JSONArray masterConditionObject = ExceptionUtils.trySupplier(() -> jsonObject.getJSONArray(RebateConstants.RuleContentField.MASTER_CONDITION.getApiName()));
        if (masterConditionObject != null) {
            return ExceptionUtils.trySupplier(() -> JSON.parseArray(masterConditionObject.toString(), RuleWhere.class));
        }
        return Lists.newArrayList();
    }

    /**
     * 不限制产品条件，无需校验
     *
     * @param objectData 对象数据
     */
    @Override
    public void validate(IObjectData objectData, User user) {
        //校验规则
        validateRule(this, objectData, user);
        validateRebateCondition(objectData, user);
    }

    /**
     * 验证返利单条件
     *
     * @param objectData 对象数据
     * @param user       用户
     */
    private void validateRebateCondition(IObjectData objectData, User user) {
        getRebateConditionData(objectData).ifPresent(rebateConditionData -> {
            Tuple<HashMultimap<String, String>, Set<String>> validateTuple = validateRuleCondition(rebateConditionData.getData(), user);
            Tuple<HashMultimap<String, String>, Set<String>> hashMultimapSetTuple = objectData.get(CONDITION_FIELD_AND_AGGREGATE_INFO_KEY, Tuple.class);
            HashMultimap<String, String> key = hashMultimapSetTuple.getKey();
            key.putAll(validateTuple.getKey());
        });
    }

    private Optional<ConditionData> getRebateConditionData(IObjectData objectData) {
        String rebateCondition = objectData.get(RebateConstants.RebateRuleField.REBATE_CONDITION.getApiName(), String.class);
        if (StringUtils.isBlank(rebateCondition)) {
            return Optional.empty();
        }
        ConditionData rebateConditionData = ExceptionUtils.trySupplier(() -> JSON.parseObject(rebateCondition, ConditionData.class));
        if (rebateConditionData == null || CollectionUtils.empty(rebateConditionData.getData())) {
            return Optional.empty();
        }
        rebateConditionData.getData().stream()
                .filter(x -> CollectionUtils.notEmpty(x.getFilters()))
                .flatMap(x -> x.getFilters().stream())
                .forEach(filter ->
                        filter.setObjectApiName(rebateConditionData.getObject_api_name())
                );
        return Optional.of(rebateConditionData);
    }


    /**
     * 验证规则
     *
     * @param rebateRuleService
     * @param objectData        对象数据
     */
    private static void validateRule(RebateRuleServiceImpl rebateRuleService, IObjectData objectData, User user) {
        JSONObject ruleContentJsonObject = rebateRuleService.getRuleJSONObject(objectData);
        List<RuleWhere> masterCondition = rebateRuleService.getMasterCondition(ruleContentJsonObject);
        //apiName 对应的fieldName
        HashMultimap<String, String> objApiNameToFields = HashMultimap.create();
        //聚合值id
        Set<String> aggregateValueIds = Sets.newHashSet();
        if (!CollectionUtils.empty(masterCondition)) {
            //校验规则条件
            Tuple<HashMultimap<String, String>, Set<String>> hashMultimapSetTuple = rebateRuleService.validateRuleCondition(masterCondition, user);
            objApiNameToFields.putAll(hashMultimapSetTuple.getKey());
            aggregateValueIds.addAll(hashMultimapSetTuple.getValue());

            validateAggRule(rebateRuleService, masterCondition);
        }
        // 校验有范围金额返利规则是否选择产品范围
        rebateRuleService.validateProductRange(objectData, masterCondition);

        //校验计算类型
        Tuple<HashMultimap<String, String>, Set<String>> calculateTuple = rebateRuleService.validateCalculate(ruleContentJsonObject, user, true);
        objApiNameToFields.putAll(calculateTuple.getKey());
        aggregateValueIds.addAll(calculateTuple.getValue());
        objectData.set(CONDITION_FIELD_AND_AGGREGATE_INFO_KEY, new Tuple<>(objApiNameToFields, aggregateValueIds));
    }

    /**
     * 校验返利使用规则的聚合条件操作符
     *
     * @param rebateRuleService 返利规则服务
     * @param masterCondition   条件
     */
    private static void validateAggRule(RebateRuleServiceImpl rebateRuleService, List<RuleWhere> masterCondition) {
        for (RuleWhere aggRuleWhere : masterCondition) {
            for (RuleWhere.FiltersBean filter : aggRuleWhere.getFilters()) {
                if (rebateRuleService.isAggregateType(filter.getFieldNameType())) {
                    String operator = filter.getOperator();
                    if ((Operator.EQ.toString().equals(operator) || Operator.GTE.toString().equals(operator) || Operator.LTE.toString().equals(operator)) && "0".equals(filter.getFieldValuesS())) {
                        // 单据条件{0}不能等于0
                        throw new ValidateException(I18N.text("sfa.rebate.rule.condition.aggregate.value.is.zero", filter.getFieldNameS()));
                    }
                    if (Operator.IS.toString().equals(operator)) {
                        // 单据条件{0}不能设置为空
                        throw new ValidateException(I18N.text("sfa.rebate.rule.condition.aggregate.operator.is.not.empty", filter.getFieldNameS()));
                    }
                }
            }
        }
    }

    private void validateProductRange(IObjectData objectData, List<RuleWhere> masterCondition) {
        if (RebateConstants.RebateType.MONEY.getValue().equals(objectData.get(RebateConstants.RebateRuleField.RULE_TYPE.getApiName(), String.class))
                && StringUtils.isBlank(objectData.get(RebateConstants.RebateRuleField.PRODUCT_RANGE_TYPE.getApiName(), String.class))) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_REBATE_RULE_PRODUCT_RANGE_IS_EMPTY));
        }
        if (RebateConstants.HAS_PRODUCT_RANGE.equals(objectData.get(RebateConstants.RebateRuleField.PRODUCT_RANGE_TYPE.getApiName(), String.class))) {
            if (StringUtils.isBlank(objectData.get(RebateConstants.RebateRuleField.PRIORITY.getApiName(), String.class))) {
                throw new ValidateException(I18N.text("sfa.rebate.rule.product.priority.is.empty"));
            }

            Set<String> rangeId = Sets.newHashSet();
            if (CollectionUtils.notEmpty(masterCondition)) {
                for (RuleWhere aggRuleWhere : masterCondition) {
                    for (RuleWhere.FiltersBean filter : aggRuleWhere.getFilters()) {
                        if (RebateConstants.REBATE_RANGE.equals(filter.getAggValueType()) && StringUtils.isNotBlank(filter.getFieldName())) {
                            rangeId.add(filter.getFieldName());
                        }
                    }
                }
            }

            if (rangeId.size() == 0) {
                throw new ValidateException(I18N.text("sfa.rebate.rule.product.range.not.select"));
            }

            if (rangeId.size() > 1) {
                throw new ValidateException(I18N.text("sfa.rebate.rule.product.range.over.one"));
            }
        }
    }

    private JSONObject getRuleJSONObject(IObjectData objectData) {
        String ruleContent = OptionalUtils.str(
                objectData.get(RebateConstants.RebateRuleField.RULE_CONTENT.getApiName(), String.class)
                , SFAI18NKeyUtil.SFA_REBATE_RULE_CANNOT_EMPTY, "rule content is empty");
        return ExceptionUtils.trySupplier(() -> JSON.parseObject(ruleContent));
    }

    /**
     * 验证计算类型
     *
     * @param ruleContentJsonObject 规则内容json对象
     * @param user                  用户
     * @param isAmountType
     * @return
     */
    public Tuple<HashMultimap<String, String>, Set<String>> validateCalculate(JSONObject
                                                                                      ruleContentJsonObject, User user, boolean isAmountType) {
        String calculateType = getCalculateType(ruleContentJsonObject);
        Tuple<HashMultimap<String, String>, Set<String>> hashMultimapSetTuple;
        if (RebateConstants.CalculateType.CYCLE.getApiName().equals(calculateType)) {
            hashMultimapSetTuple = doCycleValidate(ruleContentJsonObject, isAmountType);
            //全量计算
        } else if (RebateConstants.CalculateType.EXPRESSION.getApiName().equals(calculateType)) {
            hashMultimapSetTuple = doExpressionValidate(ruleContentJsonObject);
        } else {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.REBATE_RULE_CONTENT_CALCULATE_UN_KNOW, "un know calculate type is {}", calculateType).get();
        }
        HashMultimap<String, String> objApiNameToFields = hashMultimapSetTuple.getKey();
        Set<String> aggregateValueIds = hashMultimapSetTuple.getValue();
        if (!objApiNameToFields.isEmpty()) {
            validateObjectFields(user.getTenantId(), objApiNameToFields);
        }
        if (!aggregateValueIds.isEmpty()) {
            validateAggregateValue(aggregateValueIds, user);
        }
        return hashMultimapSetTuple;
    }

    private String getCalculateType(JSONObject ruleContentJsonObject) {
        return OptionalUtils.str(
                ruleContentJsonObject.getObject(RebateConstants.RuleContentField.CALCULATE_TYPE.getApiName(), String.class),
                SFAI18NKeyUtil.REBATE_RULE_CONTENT_CALCULATE_CANNOT_EMPTY,
                "calculate type is null ");
    }

    /**
     * 做表达的校验
     *
     * @param ruleContentJsonObject 规则内容json对象
     */
    public Tuple<HashMultimap<String, String>, Set<String>> doExpressionValidate(JSONObject ruleContentJsonObject) {
        //表达式
        List<RebateRule.ExpressionData> expressionDates = getExpressionData(ruleContentJsonObject);

        //apiName 对应的fieldName
        HashMultimap<String, String> objApiNameToFields = HashMultimap.create();
        //聚合值id
        Set<String> aggregateValueIds = Sets.newHashSet();
        if (CollectionUtils.empty(expressionDates)) {
            throw ExceptionUtils.supplier("rebate.rule.content.expression.cannot.empty").get();
        }
        if (expressionDates.size() > 5) {
            throw ExceptionUtils.supplier("rebate.rule.content.expression.right.cannot.more.then.five").get();
        }
        for (RebateRule.ExpressionData expressionData : expressionDates) {
            if (expressionData == null) {
                throw ExceptionUtils.supplier("rebate.rule.content.expression.cannot.empty").get();
            }
            if (isCalculateType(expressionData)) {
                OptionalUtils.str(expressionData.getOperator(), "rebate.rule.content.expression.cannot.empty");
                RebateRule.CycleInfoData.CycleDataBean.LeftBean left = Optional.ofNullable(expressionData.getLeft())
                        .orElseThrow(ExceptionUtils.supplier("rebate.rule.content.expression.cannot.empty"));
                if (isFieldType(left.getFieldNameType())) {
                    objApiNameToFields.put(left.getObjectApiName(), left.getFieldName());
                } else if (isAggregateType(left.getFieldNameType())) {
                    aggregateValueIds.add(left.getFieldName());
                }
                OptionalUtils.str(expressionData.getRight(), "rebate.rule.content.expression.right.cannot.empty");
            } else if (isConstantType(expressionData)) {
                OptionalUtils.str(expressionData.getRight(), "rebate.rule.content.expression.right.cannot.empty");
                // 新增校验：确保 expressionData.getRight() 不小于 0
                if (Double.parseDouble(expressionData.getRight()) <= 0) {
                    throw ExceptionUtils.supplier("rebate.rule.content.expression.right.cannot.less.zero").get();
                }
            } else if (isCalculateProType(expressionData)) {
                RebateRule.CycleInfoData.CycleDataBean.LeftBean left = Optional.ofNullable(expressionData.getLeft())
                        .orElseThrow(ExceptionUtils.supplier("rebate.rule.content.expression.cannot.empty"));
                calculateProValidate(objApiNameToFields, aggregateValueIds, left);
            } else {
                throw ExceptionUtils.supplier("rebate.rule.content.expression.execute.type.un.know").get();
            }
        }

        return new Tuple<>(objApiNameToFields, aggregateValueIds);
    }

    private void calculateProValidate
            (HashMultimap<String, String> objApiNameToFields, Set<String> aggregateValueIds, RebateRule.CycleInfoData.CycleDataBean.LeftBean
                    left) {
        OptionalUtils.str(left.getDefaultValue(), "rebate.rule.content.expression.default.value.cannot.empty");
        Set<String> fields = Sets.newHashSet();
        RebatePolicyCalculateProUtil.splitAggIdAndField(left.getDefaultValue(), aggregateValueIds, fields);
        if (CollectionUtils.notEmpty(fields)) {
            OptionalUtils.str(left.getObjectApiName(), "rebate.rule.content.expression.object.api.name.cannot.empty");
        }
        OptionalUtils.str(left.getReturnType(), "rebate.rule.content.expression.return.type.cannot.empty");
        objApiNameToFields.putAll(left.getObjectApiName(), fields);
    }

    private boolean isConstantType(RebateRule.ExpressionData expressionData) {
        return RebateConstants.ExecuteType.CONSTANT.getApiName().equals(expressionData.getExecuteType());
    }

    private boolean isCalculateType(RebateRule.ExpressionData expressionData) {
        return RebateConstants.ExecuteType.CALCULATE.getApiName().equals(expressionData.getExecuteType());
    }

    private boolean isCalculateProType(RebateRule.ExpressionData expressionData) {
        return RebateConstants.ExecuteType.CALCULATE_PRO.getApiName().equals(expressionData.getExecuteType());
    }

    private List<RebateRule.ExpressionData> getExpressionData(JSONObject ruleContentJsonObject) {
        String expression = OptionalUtils.str(ruleContentJsonObject.getObject(RebateConstants.RuleContentField.EXPRESSIONS.getApiName(), String.class),
                "rebate.rule.content.expression.cannot.empty",
                "expression is empty"
        );
        return ExceptionUtils.trySupplier(() -> JSON.parseArray(expression, RebateRule.ExpressionData.class));
    }

    /**
     * 做每满验证
     *
     * @param ruleContentJsonObject 规则内容json对象
     * @param isAmountType
     */
    private Tuple<HashMultimap<String, String>, Set<String>> doCycleValidate(JSONObject ruleContentJsonObject,
                                                                             boolean isAmountType) {
        //apiName 对应的fieldName
        HashMultimap<String, String> objApiNameToFields = HashMultimap.create();
        //聚合值id
        Set<String> aggregateValueIds = Sets.newHashSet();
        RebateRule.CycleInfoData cycleInfoData = getCycleInfoData(ruleContentJsonObject);
        if (cycleInfoData == null || CollectionUtils.empty(cycleInfoData.getCycleData())) {
            throw ExceptionUtils.supplier(SFAI18NKeyUtil.REBATE_RULE_CONTENT_CYCLE_CANNOT_EMPTY, "cycle info is null").get();
        }

        for (RebateRule.CycleInfoData.CycleDataBean cycleDatum : cycleInfoData.getCycleData()) {
            RebateRule.CycleInfoData.CycleDataBean.LeftBean leftBean = Optional.ofNullable(cycleDatum)
                    .map(RebateRule.CycleInfoData.CycleDataBean::getLeft)
                    .orElseThrow(ExceptionUtils.supplier(SFAI18NKeyUtil.REBATE_RULE_CONTENT_CYCLE_CANNOT_EMPTY, "cycle info is null"));
            // NOSONAR
            if (RebateConstants.ExecuteType.CALCULATE_PRO.getApiName().equals(cycleDatum.getExecuteType())) {
                calculateProValidate(objApiNameToFields, aggregateValueIds, leftBean);
            } else {
                if (isFieldType(leftBean.getFieldNameType())) {
                    objApiNameToFields.put(leftBean.getObjectApiName(), leftBean.getFieldName());
                } else if (isAggregateType(leftBean.getFieldNameType())) {
                    aggregateValueIds.add(leftBean.getFieldName());
                }
                if (cycleDatum.getFieldValue() == null) {
                    throw ExceptionUtils.supplier("rebate.rule.content.cycle.field.value.cannot.empty").get();
                }
            }
            //整单使用金额上限，可以为空，但是不为空情况应该校验和使用金额的大小关系
            Optional.ofNullable(cycleInfoData.getMaxAmount()).ifPresent(maxAmount -> {
                BigDecimal usedAmount = Optional.ofNullable(cycleDatum.getUsedAmount()).orElseThrow(ExceptionUtils.supplier(isAmountType ? "rebate.rule.content.cycle.used.amount.cannot.empty" : "rebate.rule.content.cycle.used.quantity.cannot.empty"));
                if (BigDecimalUtils.compare(usedAmount, Operator.LTE, BigDecimal.ZERO)) {
                    throw ExceptionUtils.supplier(isAmountType ? "rebate.rule.content.cycle.max.used.amount.less.then.zero" : "rebate.rule.content.cycle.max.used.quantity.less.then.zero", "used amount is {}", usedAmount).get();
                }
                if (BigDecimalUtils.compare(maxAmount, Operator.LT, usedAmount)) {
                    throw ExceptionUtils.supplier(isAmountType ? "rebate.rule.content.cycle.max.amount.less.then.used.amount" : "rebate.rule.content.cycle.max.quantity.less.then.used.quantity", "max amount is {},used amount is {}", maxAmount, usedAmount).get();
                }
            });
        }

        return new Tuple<>(objApiNameToFields, aggregateValueIds);
    }

    private RebateRule.CycleInfoData getCycleInfoData(JSONObject ruleContentJsonObject) {
        //每满计算
        String cycleInfo = OptionalUtils.str(
                ruleContentJsonObject.getObject(RebateConstants.RuleContentField.CYCLE_INFO.getApiName(), String.class),
                SFAI18NKeyUtil.REBATE_RULE_CONTENT_CYCLE_CANNOT_EMPTY,
                "cycle info is null"
        );
        return ExceptionUtils.trySupplier(() -> JSON.parseObject(cycleInfo, RebateRule.CycleInfoData.class));
    }


    /**
     * 使用规则，不会系统产生聚合值，因此无需删除聚合值，
     * 只需要删除ruleCode，也就是对象的id
     *
     * @param objectData 对象数据
     * @param user       用户
     */
    @Override
    public void deleteAggRuleEngine(IObjectData objectData, User user) {
        rollBackRuleEngineData(objectData, user);
        //删除引用关系
        entityReferenceService.delete(user.getTenantId(), PricePolicyConstants.REFERENCE_SOURCE_TYPE, objectData.getId());
    }

    @Override
    public void render(IObjectData objectData, User user) {
        JSONObject ruleContentJsonObject = getRuleJSONObject(objectData);
        List<RuleWhere> aggRuleWheres = getMasterCondition(ruleContentJsonObject);
        User admin = new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        String masterApiName = Optional.ofNullable(ruleContentJsonObject.getString(RebateConstants.RuleContentField.OBJECT_API_NAME.getApiName()))
                .filter(str -> !str.isEmpty()).orElse(Utils.SALES_ORDER_API_NAME);
        String detailApiName = PricePolicyConstants.MASTER_DETAIL_API_NAME.get(masterApiName);
        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(user.getTenantId(), Lists.newArrayList(masterApiName, detailApiName, Utils.PRODUCT_API_NAME, Utils.AGGREGATE_RULE_API_NAME));
        //条件
        if (CollectionUtils.notEmpty(aggRuleWheres)) {
            String conditionJson = renderMasterCondition(admin, aggRuleWheres, describeMap);
            ruleContentJsonObject.put(RebateConstants.RuleContentField.MASTER_CONDITION.getApiName(), conditionJson);
        }
        String calculateType = getCalculateType(ruleContentJsonObject);
        String renderExecStr = renderExec(ruleContentJsonObject, admin, describeMap, calculateType);
        objectData.set(RebateConstants.RebateRuleField.RULE_CONTENT.getApiName(), renderExecStr);
        //渲染返利单条件
        getRebateConditionData(objectData).ifPresent(rebateConditionData -> {
            Map<String, IObjectDescribe> rebateConditionMap = serviceFacade.findObjects(user.getTenantId(), Lists.newArrayList(rebateConditionData.getObject_api_name()));
            List<RuleWhere> rebateConditionWhere = rebateConditionData.getData();
            renderFieldAndCollectorAggIds(user, rebateConditionMap, rebateConditionWhere);
            rebateConditionData.setData(rebateConditionWhere);
            objectData.set(RebateConstants.RebateRuleField.REBATE_CONDITION.getApiName(), JSON.toJSONString(rebateConditionData));
        });
    }

    public String renderExec(JSONObject ruleContentJsonObject, User
            admin, Map<String, IObjectDescribe> describeMap, String calculateType) {
        //每满
        if (RebateConstants.CalculateType.CYCLE.getApiName().equals(calculateType)) {
            String cycleJson = renderCycle(ruleContentJsonObject, admin, describeMap);
            ruleContentJsonObject.put(RebateConstants.RuleContentField.CYCLE_INFO.getApiName(), cycleJson);
            //全量计算
        } else if (RebateConstants.CalculateType.EXPRESSION.getApiName().equals(calculateType)) {
            String expressionJson = renderExpression(ruleContentJsonObject, admin, describeMap);
            ruleContentJsonObject.put(RebateConstants.RuleContentField.EXPRESSIONS.getApiName(), expressionJson);
        }
        return ruleContentJsonObject.toJSONString();
    }

    /**
     * 每满数据
     *
     * @param ruleContentJsonObject 规则内容json对象
     * @param admin                 用户
     * @param describeMap           描述
     * @return {@code String}
     */
    private String renderCycle(JSONObject ruleContentJsonObject, User
            admin, Map<String, IObjectDescribe> describeMap) {
        RebateRule.CycleInfoData cycleInfoData = getCycleInfoData(ruleContentJsonObject);
        Boolean isCalculatePro = getCalculatePro(cycleInfoData);
        if (Boolean.TRUE.equals(isCalculatePro)) {
            renderCycleCalculatePro(admin, describeMap, cycleInfoData);
        } else {
            Set<String> aggregateIds = renderCycleAndCollectorAggIds(describeMap, cycleInfoData);
            if (CollectionUtils.notEmpty(aggregateIds)) {
                Map<String, String> aggregateNameByIds = getAggregateNameByIds(admin, aggregateIds);
                renderCycleAggregate(cycleInfoData, aggregateNameByIds);
            }
        }
        renderDescribeName(cycleInfoData, describeMap);
        return ExceptionUtils.trySupplier(() -> JSON.toJSONString(cycleInfoData));
    }

    private void renderDescribeName(RebateRule.CycleInfoData cycleInfoData, Map<String, IObjectDescribe> describeMap) {
        for (RebateRule.CycleInfoData.CycleDataBean cycleDatum : cycleInfoData.getCycleData()) {
            Optional.ofNullable(cycleDatum)
                    .map(RebateRule.CycleInfoData.CycleDataBean::getLeft)
                    .ifPresent(leftBean -> {
                        if (RebateConstants.REBATE_RANGE.equals(leftBean.getFieldNameType())) {
                            leftBean.setObjectApiNameS(I18N.text(SFAI18NKeyUtil.REBATE_RULE_CONDITION_RANGE));
                            return;
                        }
                        String objectApiName = leftBean.getObjectApiName();
                        if (StringUtils.isNotBlank(objectApiName)) {
                            IObjectDescribe objectDescribe = describeMap.get(objectApiName);
                            Optional.ofNullable(objectDescribe)
                                    .map(IObjectDescribe::getDisplayName)
                                    .ifPresent(leftBean::setObjectApiNameS);
                        }
                    });

        }
    }

    @NotNull
    private Boolean getCalculatePro(RebateRule.CycleInfoData cycleInfoData) {
        return Optional.ofNullable(cycleInfoData)
                .map(RebateRule.CycleInfoData::getCycleData)
                .filter(CollectionUtils::notEmpty)
                .map(x -> x.get(0))
                .map(x -> RebateConstants.ExecuteType.CALCULATE_PRO.getApiName().equals(x.getExecuteType()))
                .orElse(false);
    }

    private void renderCycleCalculatePro(User
                                                 admin, Map<String, IObjectDescribe> describeMap, RebateRule.CycleInfoData cycleInfoData) {
        Optional<RebateRule.CycleInfoData.CycleDataBean.LeftBean> leftBeanOpt = Optional.ofNullable(cycleInfoData)
                .map(RebateRule.CycleInfoData::getCycleData)
                .filter(CollectionUtils::notEmpty)
                .map(x -> x.get(0))
                .map(RebateRule.CycleInfoData.CycleDataBean::getLeft);
        if (!leftBeanOpt.isPresent()) {
            return;
        }
        RebateRule.CycleInfoData.CycleDataBean.LeftBean leftBean = leftBeanOpt.get();
        setDefaultName(admin, describeMap, leftBean);
    }

    private void setDefaultName(User
                                        admin, Map<String, IObjectDescribe> describeMap, RebateRule.CycleInfoData.CycleDataBean.LeftBean leftBean) {
        Set<String> aggregateValueIds = Sets.newHashSet();
        Set<String> fields = Sets.newHashSet();
        RebatePolicyCalculateProUtil.splitAggIdAndField(leftBean.getDefaultValue(), aggregateValueIds, fields);
        Map<String, String> aggregateNameByIds = getAggregateNameByIds(admin, aggregateValueIds);
        Map<String, String> fieldApiNameToLabel = Maps.newHashMap();
        if (CollectionUtils.notEmpty(fields)) {
            fieldApiNameToLabel.putAll(getLabel(describeMap, leftBean, fields));
        }
        String result = RebatePolicyCalculateProUtil.setAggNameAndFieldLabel(leftBean.getDefaultValue(), aggregateNameByIds, fieldApiNameToLabel);
        leftBean.setDefaultValueName(result);
    }

    private Map<String, String> getLabel
            (Map<String, IObjectDescribe> describeMap, RebateRule.CycleInfoData.CycleDataBean.LeftBean
                    leftBean, Set<String> fields) {
        Map<String, String> fieldApiNameToLabel = Maps.newHashMap();
        String objectApiName = leftBean.getObjectApiName();
        Optional.ofNullable(describeMap.get(objectApiName))
                .map(IObjectDescribe::getFieldDescribeMap)
                .ifPresent(fieldDescribeMap -> {
                    for (String fieldApiName : fields) {
                        IFieldDescribe fieldDescribe = fieldDescribeMap.get(fieldApiName);
                        if (fieldDescribe != null) {
                            String label = fieldDescribe.getLabel();
                            fieldApiNameToLabel.put(fieldApiName, label);
                        }
                    }
                });
        return fieldApiNameToLabel;
    }


    private Set<String> renderCycleAndCollectorAggIds
            (Map<String, IObjectDescribe> objectDescribeMap, RebateRule.CycleInfoData cycleInfoData) {
        Set<String> aggIds = Sets.newHashSet();
        for (RebateRule.CycleInfoData.CycleDataBean filter : cycleInfoData.getCycleData()) {
            Optional.ofNullable(filter)
                    .map(RebateRule.CycleInfoData.CycleDataBean::getLeft)
                    .ifPresent(left ->
                            dealLeft(objectDescribeMap, aggIds, left)
                    );
        }
        return aggIds;
    }

    private void dealLeft
            (Map<String, IObjectDescribe> objectDescribeMap, Set<String> aggIds, RebateRule.CycleInfoData.CycleDataBean.LeftBean
                    left) {
        if (isAggregateType(left.getFieldNameType())) {
            aggIds.add(left.getFieldName());
            return;
        }
        IFieldDescribe fieldDescribe = objectDescribeMap.getOrDefault(left.getObjectApiName(), new ObjectDescribe())
                .getFieldDescribe(left.getFieldName());
        if (fieldDescribe == null) {
            left.setFieldNameS(Strings.EMPTY);
            return;
        }
        left.setFieldNameS(fieldDescribe.getLabel());
    }

    private void renderCycleAggregate(RebateRule.CycleInfoData
                                              cycleInfoData, Map<String, String> aggregateIdToName) {
        for (RebateRule.CycleInfoData.CycleDataBean cycleDatum : cycleInfoData.getCycleData()) {
            Optional.ofNullable(cycleDatum)
                    .map(RebateRule.CycleInfoData.CycleDataBean::getLeft)
                    .ifPresent(leftBean -> {
                            dealAggregateLeft(aggregateIdToName, leftBean);
                            leftBean.setObjectApiNameS(I18N.text(DescribeI18NUtils.getObjectNameKey(leftBean.getObjectApiName())));
                    });

        }
    }

    private void dealAggregateLeft
            (Map<String, String> aggregateIdToName, RebateRule.CycleInfoData.CycleDataBean.LeftBean leftBean) {
        if (!isAggregateType(leftBean.getFieldNameType())) {
            return;
        }
        leftBean.setFieldNameS(aggregateIdToName.getOrDefault(leftBean.getFieldName(), Strings.EMPTY));
    }


    /**
     * 表达式数据
     *
     * @param ruleContentJsonObject 规则内容json对象
     * @param admin                 用户
     * @param describeMap           描述
     * @return {@code String}
     */
    private String renderExpression(JSONObject ruleContentJsonObject, User
            admin, Map<String, IObjectDescribe> describeMap) {
        List<RebateRule.ExpressionData> expressionDates = getExpressionData(ruleContentJsonObject);
        Set<String> aggregateIds = renderExpressionAndCollectorAggIds(describeMap, expressionDates);
        if (CollectionUtils.notEmpty(aggregateIds)) {
            Map<String, String> aggregateNameByIds = getAggregateNameByIds(admin, aggregateIds);
            renderExpressionAggregate(expressionDates, aggregateNameByIds);
        }
        renderExpressionCalculatePro(admin, describeMap, expressionDates);
        renderExpressionDescribeName(expressionDates, describeMap);
        return ExceptionUtils.trySupplier(() -> JSON.toJSONString(expressionDates));
    }

    private void renderExpressionDescribeName(List<RebateRule.ExpressionData> expressionDates, Map<String, IObjectDescribe> describeMap) {
        for (RebateRule.ExpressionData expressionDate : expressionDates) {
            Optional.ofNullable(expressionDate)
                    .map(RebateRule.ExpressionData::getLeft)
                    .ifPresent(leftBean -> {
                        if (RebateConstants.REBATE_RANGE.equals(leftBean.getFieldNameType())) {
                            leftBean.setObjectApiNameS(I18N.text(SFAI18NKeyUtil.REBATE_RULE_CONDITION_RANGE));
                            return;
                        }
                        String objectApiName = leftBean.getObjectApiName();
                        if (StringUtils.isBlank(objectApiName)) {
                            return;
                        }
                        IObjectDescribe objectDescribe = describeMap.get(objectApiName);
                        if (objectDescribe == null) {
                            return;
                        }
                        leftBean.setObjectApiNameS(objectDescribe.getDisplayName());
                    });
        }
    }

    private void renderExpressionCalculatePro(User
                                                      admin, Map<String, IObjectDescribe> describeMap, List<RebateRule.ExpressionData> expressionDates) {
        for (RebateRule.ExpressionData expressionDate : expressionDates) {
            if (isCalculateProType(expressionDate)) {
                RebateRule.CycleInfoData.CycleDataBean.LeftBean leftBean = expressionDate.getLeft();
                setDefaultName(admin, describeMap, leftBean);
            }
        }
    }

    private void renderExpressionAggregate
            (List<RebateRule.ExpressionData> expressionDates, Map<String, String> aggregateNameByIds) {
        for (RebateRule.ExpressionData expressionDate : expressionDates) {
            if (isCalculateType(expressionDate)) {
                dealAggregateLeft(aggregateNameByIds, expressionDate.getLeft());
            }
        }
    }

    private Set<String> renderExpressionAndCollectorAggIds
            (Map<String, IObjectDescribe> describeMap, List<RebateRule.ExpressionData> expressionDates) {
        Set<String> aggregateIds = Sets.newHashSet();
        for (RebateRule.ExpressionData expressionDate : expressionDates) {
            if (isCalculateType(expressionDate)) {
                dealLeft(describeMap, aggregateIds, expressionDate.getLeft());
            }
        }
        return aggregateIds;
    }

    /**
     * 渲染主条件
     *
     * @param user          用户
     * @param aggRuleWheres 规则where
     * @param describeMap   描述
     * @return {@code String}
     */
    public String renderMasterCondition(User
                                                user, List<RuleWhere> aggRuleWheres, Map<String, IObjectDescribe> describeMap) {
        Set<String> aggregateIds = renderFieldAndCollectorAggIds(user, describeMap, aggRuleWheres);
        if (CollectionUtils.notEmpty(aggregateIds)) {
            Map<String, String> aggregateNameByIds = getAggregateNameByIds(user, aggregateIds);
            renderAggregate(aggRuleWheres, aggregateNameByIds);
        }
        return ExceptionUtils.trySupplier(() -> JSON.toJSONString(aggRuleWheres));
    }

    /**
     * 得到聚合值名称
     *
     * @param user         用户
     * @param aggregateIds 聚合值id
     * @return {@code Map<String, String>}
     */
    private Map<String, String> getAggregateNameByIds(User user, Set<String> aggregateIds) {
        return Optional.ofNullable(serviceFacade.findRecordName(ActionContextExt.of(user).getContext(),
                SFAPreDefine.AggregateRule.getApiName(), Lists.newArrayList(aggregateIds)))
                .map(x -> x.stream().collect(Collectors.toMap(INameCache::getId,
                        cache -> Optional.ofNullable(cache.getName()).orElse(I18N.text("sfa.agg.rule.data.is.delete")), (v1, v2) -> v2)))
                .orElse(Maps.newHashMap());
    }

    /**
     * 渲染聚合值
     *
     * @param aggRuleWheres     条件
     * @param aggregateIdToName idToName
     */
    private void renderAggregate(List<RuleWhere> aggRuleWheres, Map<String, String> aggregateIdToName) {
        forEachFilters(aggRuleWheres, filter -> {
            if (!isAggregateType(filter.getFieldNameType())) {
                return;
            }
            if (StringUtils.isBlank(filter.getAggValueType())) {
                filter.setAggValueType(PricePolicyConstants.AggregateRuleType.aggregate);
            }
            filter.setFieldNameS(aggregateIdToName.getOrDefault(filter.getFieldName(), Strings.EMPTY));
            filter.setObjectApiNameS(I18N.text(DescribeI18NUtils.getObjectNameKey(filter.getObjectApiName())));
            filter.setOperatorName(pricePolicyRuleDataParse.getOperatorName(filter.getOperator()));
        });
    }

}
