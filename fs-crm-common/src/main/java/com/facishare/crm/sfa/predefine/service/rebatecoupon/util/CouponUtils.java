package com.facishare.crm.sfa.predefine.service.rebatecoupon.util;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.constants.RebateConstants;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants;
import com.facishare.crm.sfa.predefine.service.pricepolicy.RuleEngineLogicService;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.dao.CouponDao;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.HardProductData;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RebateCouponQuery;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RuleWhere;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.translate.TranslateConditionToRuleService;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.translate.TranslateManager;
import com.facishare.crm.sfa.utilities.util.PricePolicyUtils;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ObjectInfo;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IGroupByParameter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.GroupByParameter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.TabSection;
import com.facishare.paas.metadata.impl.ui.layout.component.ComponentFactory;
import com.facishare.paas.metadata.impl.ui.layout.component.NavigationComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TabsComponent;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.ui.layout.*;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 优惠券工具类
 *
 * <AUTHOR>
 * @date 2021/12/21
 */
@Slf4j
public class CouponUtils {
    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
    private static final RuleEngineLogicService ruleEngineLogicService = SpringUtil.getContext().getBean(RuleEngineLogicService.class);
    private static final List<String> TO_REMOVE_BUTTON = Lists.newArrayList(ObjectAction.BATCH_IMPORT.getActionCode(), ObjectAction.BATCH_EXPORT.getActionCode(), ObjectAction.INTELLIGENTFORM.getActionCode());
    private static final TranslateManager translateManager = SpringUtil.getContext().getBean(TranslateManager.class);
    private static final ObjectDataServiceImpl ObjectDataServiceImpl = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private static final BizConfigThreadLocalCacheService config = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    private static final CouponDao couponDao = SpringUtil.getContext().getBean(CouponDao.class);
    private static final String RULE_CONDITION_JSON = "{\n" +
            "                    \"api_name\": \"related_rule_condition\",\n" +
            "                    \"nameI18nKey\": \"paas.udobj.related_rule_condition\",\n" +
            "                    \"header\": \"产品条件\",\n" +
            "                    \"type\": \"rule_condition\",\n" +
            "                    \"order\": 999,\n" +
            "                    \"is_hidden\": false,\n" +
            "                    \"_id\": \"related_rule_condition\"" +
            "                }";
    private static final String RULE_RANGE_JSON = "{\n" +
            "                    \"api_name\": \"related_rule_condition\",\n" +
            "                    \"nameI18nKey\": \"paas.udobj.related_product_range\",\n" +
            "                    \"header\": \"返利品范围\",\n" +
            "                    \"type\": \"rule_condition\",\n" +
            "                    \"order\": 999,\n" +
            "                    \"is_hidden\": false,\n" +
            "                    \"_id\": \"related_rule_condition\"" +
            "                }";

    /**
     * 保存规则引擎时，condition需要保存两次规则，一次是聚合值 大于满额，另外一次是规则本身
     * 此方法就是用来拼接规则本身一次的rule_code
     *
     * @param id id
     * @return {@code String}
     */
    public static String getSpecialId(String id) {
        return id.concat("_c");
    }


    /**
     * 是否是优惠券使用范围
     *
     * @param data
     * @return boolean
     */
    public static boolean isCouponRangeType(HardProductData.ProductData data) {
        return CouponConstants.HardModeItemType.AGGREGATE_RULE.getValue().equals(data.getType());
    }

    /**
     * 有优惠券实例
     *
     * @param user       用户
     * @param objectData 对象数据
     * @return boolean
     */
    public static boolean hasCouponInstance(User user, IObjectData objectData) {
        SearchTemplateQueryExt ext = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        ext.addFilter(Operator.EQ, CouponConstants.CouponInstanceField.COUPON_PLAN_ID.getApiName(), objectData.getId());
        ext.addFilter(Operator.IN, DBRecord.IS_DELETED, Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue()), String.valueOf(DELETE_STATUS.NORMAL.getValue())));
        ext.setLimit(1);
        ext.setOffset(0);
        QueryResult<IObjectData> couponInstanceDates = serviceFacade.findBySearchQueryIgnoreAll(user, CouponConstants.COUPON_INSTANCE_API_NAME, (SearchTemplateQuery) ext.getQuery());
        return couponInstanceDates != null && CollectionUtils.notEmpty(couponInstanceDates.getData());
    }

    /**
     * 有优惠券实例的set集合
     *
     * @param user 用户
     * @param ids  id
     * @return {@code Set<String>}
     */
    public static Set<String> hasCouponInstance(User user, List<String> ids) {
        return hasCouponDown(user, ids, CouponConstants.COUPON_INSTANCE_API_NAME, CouponConstants.CouponInstanceField.COUPON_PLAN_ID.getApiName());
    }

    /**
     * 查询优惠券批次是否存在
     *
     * @param user
     * @param ids
     * @return
     */
    public static Set<String> hasCoupon(User user, List<String> ids) {
        try {
            IObjectDescribe couponDesc = serviceFacade.findObject(user.getTenantId(), CouponConstants.COUPON_API_NAME);
            if (couponDesc == null) {
                return Sets.newHashSet();
            }
        } catch (Exception e) {
            log.warn(" has not couponObj" + e.getMessage());
            return Sets.newHashSet();
        }


        return hasCouponDown(user, ids, CouponConstants.COUPON_API_NAME, CouponConstants.CouponInstanceField.COUPON_PLAN_ID.getApiName());
    }

    public static Set<String> hasCouponDown(User user, List<String> ids, String objApiName, String planField) {
        SearchTemplateQueryExt ext = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        ext.setLimit(ids.size());
        ext.setOffset(0);
        ext.addFilter(Operator.EQ, planField, ids);
        ext.addFilter(Operator.IN, DBRecord.IS_DELETED, Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue()), String.valueOf(DELETE_STATUS.INVALID.getValue())));
        SearchTemplateQuery query = (SearchTemplateQuery) ext.getQuery();
        IGroupByParameter groupByParameter = new GroupByParameter();
        groupByParameter.setGroupBy(Lists.newArrayList(planField));
        query.setGroupByParameter(groupByParameter);
        try {
            IActionContext context = ActionContextExt.of(user).getContext();
            context.put(ActionContextKey.IGNORE_BASE_VISIBLE_RANGE, true);
            List<IObjectData> objectDataList = ObjectDataServiceImpl.aggregateFindBySearchQuery(context, (SearchTemplateQuery) ext.getQuery(),
                    objApiName);
            if (CollectionUtils.empty(objectDataList)) {
                return Sets.newHashSet();
            }
            return objectDataList.stream()
                    .filter(o -> o.get("groupbycount", Long.class) > 0)
                    .map(o -> o.get(planField, String.class))
                    .collect(Collectors.toSet());
        } catch (MetadataServiceException e) {
            log.error("aggregateFindBySearchQuery error,SearchTemplateQuery is {},error msg is {}", JSON.toJSONString(query), e.getMessage());
        }
        return Sets.newHashSet();
    }


    public static boolean isFieldType(String fieldNameType) {
        return PricePolicyConstants.FieldNameType.FIELD.toString().toLowerCase().equals(fieldNameType);
    }

    public static boolean isAggregateType(String fieldNameType) {
        return PricePolicyConstants.FieldNameType.AGGREGATE.toString().toLowerCase().equals(fieldNameType) || RebateConstants.REBATE_RANGE.equals(fieldNameType);
    }

    public static void removeButton(StandardListHeaderController.Result result) {
        removeButton(result, null);
    }

    public static void removeButton(StandardListHeaderController.Result result, List<String> removes) {
        if (null == result || null == result.getLayout()) {
            return;
        }
        ILayout layout = new Layout(result.getLayout());
        List<IButton> buttonList = layout.getButtons();
        if (CollectionUtils.empty(buttonList)) {
            return;
        }
        List<String> removeButton = Lists.newArrayList(TO_REMOVE_BUTTON);
        if (CollectionUtils.notEmpty(removes)) {
            removeButton.addAll(removes);
        }
        if (null != result.getObjectDescribe() && RebateConstants.REBATE_API_NAME.equals(result.getObjectDescribe().get("api_name"))) {
            removeButton.remove(ObjectAction.BATCH_EXPORT.getActionCode());
            removeButton.remove(ObjectAction.BATCH_IMPORT.getActionCode());
        }
        if (null != result.getObjectDescribe() && CouponConstants.COUPON_PLAN_API_NAME.equals(result.getObjectDescribe().get("api_name"))) {
            removeButton.remove(ObjectAction.BATCH_EXPORT.getActionCode());
        }
        buttonList.removeIf(button -> removeButton.contains(button.getAction()));
        //移动端移除新建按钮
        if (!RequestUtil.isWebRequest()) {
            buttonList.removeIf(button -> ObjectAction.CREATE.getActionCode().contains(button.getAction()));
        }
        layout.setButtons(buttonList);
    }


    /**
     * 删除规则并保存规则
     *
     * @param objectDataCp                    对象数据cp
     * @param dbMasterData                    数据库中主数据
     * @param user                            用户
     * @param translateConditionToRuleService 翻译条件规则服务
     * @param consumer                        消费者
     */
    public static void deleteRuleAndSaveRule(IObjectData objectDataCp, IObjectData dbMasterData, User user, TranslateConditionToRuleService translateConditionToRuleService,
                                             Consumer<String> consumer) {
        //先删除之前的聚合规则，和规则引擎中的数据
        translateConditionToRuleService.deleteAggRuleEngine(dbMasterData, user);
        //log.info("delete agg rule and engine data success");
        try {
            //创建新的聚合规则，和保存到规则引擎
            //log.info("begin create update agg rule and engine data");
            translateConditionToRuleService.create(objectDataCp, user);
            //log.info("success create update agg rule and engine data");
            //log.info("begin update data");
            //执行具体的保存，或者更新逻辑
            consumer.accept(null);
            log.info("success update data");
        } catch (Exception e) {
            translateConditionToRuleService.deleteAggRuleEngine(objectDataCp, user);
            log.error("create data exception rollback data msg is{}", e.getMessage());
            //如果发生异常，则把之前的规则重新进行保存
            translateConditionToRuleService.create(dbMasterData, user);
            log.error("rollback data success");
            throw e;
        }
    }


    public static JSONObject parseMiscContent(String miscContent) {
        JSONObject jsonObject;
        try {
            jsonObject = (JSONObject) JSON.parse(miscContent);
        } catch (Exception e) {
            log.error("parseMiscContent error" + miscContent);
            jsonObject = new JSONObject();
        }

        return jsonObject;
    }

    public static Map getMiscMap(Object miscContent) {
        if (miscContent != null) {
            if (miscContent instanceof Map) {
                return (Map) miscContent;
            } else {
                String miscContentStr = miscContent.toString();
                if (org.apache.commons.lang3.StringUtils.isNotBlank(miscContentStr)) {
                    return parseMiscContent(miscContentStr);
                }
            }
        }

        return null;
    }

    /**
     * 得到聚合值id
     *
     * @param aggRuleWheres aggRuleWheres
     * @return {@code List<String>}
     */
    public static List<String> getAggregateValueIds(List<RuleWhere> aggRuleWheres) {
        //聚合值id
        List<String> aggregateValueIds = Lists.newArrayList();
        for (RuleWhere aggRuleWhere : aggRuleWheres) {
            List<RuleWhere.FiltersBean> filters = aggRuleWhere.getFilters();
            if (CollectionUtils.empty(filters)) {
                continue;
            }
            for (RuleWhere.FiltersBean filter : filters) {
                if (isAggregateType(filter.getFieldNameType())) {
                    aggregateValueIds.add(filter.getFieldName());
                }
            }
        }
        return aggregateValueIds;
    }

    /**
     * 删除聚合和规则引擎
     *
     * @param user          用户
     * @param dataList      数据列表
     * @param objectApiName 对象名称
     */
    public static void deleteAggValueAndEngineRule(User user, List<IObjectData> dataList, String objectApiName) {
        int limit = AppFrameworkConfig.getBulkDeleteLimit();
        List<List<IObjectData>> partition = Lists.partition(dataList, limit);
        //分批删除
        for (List<IObjectData> iObjectDates : partition) {
            //agg id
            List<String> aggregateValueIds = Lists.newArrayListWithCapacity(limit);
            //规则引擎id
            List<String> ids = Lists.newArrayListWithCapacity(limit);
            //condition 类型的规则id，用来删除引用关系
            Set<String> conditionTypeIds = Sets.newHashSet();
            Set<String> hasRebateDetail;
            if (CouponConstants.COUPON_PLAN_API_NAME.equals(objectApiName)) {
                hasRebateDetail = hasCouponInstance(user, iObjectDates.stream().map(IObjectData::getId).collect(Collectors.toList()));
            } else if (RebateConstants.REBATE_API_NAME.equals(objectApiName)) {
                hasRebateDetail = RebateUtils.hasRebateDetail(user, iObjectDates.stream().map(IObjectData::getId).collect(Collectors.toList()));
            } else {
                continue;
            }
            for (IObjectData objectData : iObjectDates) {
                //已经使用的返利单\或者优惠券，不可删除
                if (hasRebateDetail.contains(objectData.getId())) {
                    continue;
                }
                //产品返利没有规则，只有返货的产品范围
                if (RebateConstants.REBATE_API_NAME.equals(objectApiName)
                        && RebateConstants.RebateType.PRODUCT.getValue().equals(objectData.get(RebateConstants.RebateField.REBATE_TYPE.getApiName(), String.class))) {
                    conditionTypeIds.add(objectData.getId());
                }

                String productConditionRule = objectData.get(RebateConstants.RebateField.PRODUCT_CONDITION_RULE.getApiName(), String.class);
                // 如果是复杂产品模式，则向conditionTypeIds加入id
                if (CouponConstants.ProductConditionType.HARD.getValue().equals(objectData.get(RebateConstants.RebateField.PRODUCT_CONDITION_TYPE.getApiName()))) {
                    conditionTypeIds.add(objectData.getId());
                }
                if (StringUtils.isBlank(productConditionRule)) {
                    continue;
                }
                List<RuleWhere> aggRuleWheres;
                try {
                    aggRuleWheres = ExceptionUtils.trySupplier(() -> JSON.parseArray(productConditionRule, RuleWhere.class));
                } catch (Exception e) {
                    //遇到异常继续删除，不退出循环
                    log.error("delete agg thread has error msg is {}", e.getMessage());
                    continue;
                }
                if (CollectionUtils.empty(aggRuleWheres)) {
                    continue;
                }
                aggregateValueIds.addAll(getAggregateValueIds(aggRuleWheres));
                ids.add(objectData.getId());
                //如果是条件字段是condition 或者hard，则把注册规则也进行删除
                if (CouponConstants.ProductConditionType.CONDITION.getValue()
                        .equals(objectData.get(RebateConstants.RebateField.PRODUCT_CONDITION_TYPE.getApiName()))) {
                    ids.add(getSpecialId(objectData.getId()));
                    conditionTypeIds.add(objectData.getId());
                }
            }
            List<IObjectData> aggDates = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), aggregateValueIds, SFAPreDefine.AggregateRule.getApiName());
            if (CollectionUtils.notEmpty(aggDates)) {
                serviceFacade.bulkDeleteDirect(aggDates, new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID));
            }
            // ids非空的时候调用规则引擎，删除ids
            if (!ids.isEmpty()) {
                ruleEngineLogicService.deleteRule(new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID), ids);
            }
            //删除引用
            PricePolicyUtils.deleteReferenceRelation(user.getTenantId(), Lists.newArrayList(conditionTypeIds));
        }
    }

    public static boolean isCouponPlanObj(IObjectData objectData) {
        String describeApiName = Optional.ofNullable(objectData.get(IObjectData.DESCRIBE_API_NAME, String.class)).orElse("");
        return CouponConstants.COUPON_PLAN_API_NAME.equals(describeApiName);
    }

    /**
     * 呈现记录列表
     * 先获取原始DB中的类型，因为必须根据这个类型才能获取到，
     * 如果前后更改了类型，则使用已经更改的类型
     *
     * @param recordList              记录列表
     * @param user                    用户
     * @param apiName                 api名称
     * @param conditionTypeApiName    条件类型api名称
     * @param conditionContentApiName 条件内容api名称
     */
    public static void renderRecordList(List<LogRecord> recordList, User user, String apiName, String conditionTypeApiName, String conditionContentApiName) {
        String dataId = getDataIdByLogRecordList(recordList, apiName);
        if (org.apache.commons.lang.StringUtils.isBlank(dataId)) {
            return;
        }
        //获取修改类型
        LogInfo.DiffObjectData beforeDiffType = null;
        //倒序循环日志记录，一定要倒叙才是操作的顺序
        for (int i = recordList.size() - 1; i >= 0; i--) {
            LogRecord logRecord = recordList.get(i);
            if (!ActionType.Modify.getId().equals(logRecord.getOperationType())) {
                continue;
            }
            List<LogInfo.DiffObjectData> diffObjectDataList = logRecord.getObjectData();
            //获取修改类型
            LogInfo.DiffObjectData diffType = getDiffObjectData(conditionTypeApiName, diffObjectDataList);
            //不为空的是否赋值上一个conditionType
            if (diffType != null) {
                beforeDiffType = diffType;
            }
            //获取修改类型
            LogInfo.DiffObjectData diffConditionContent = getDiffObjectData(conditionContentApiName, diffObjectDataList);
            if (diffConditionContent != null) {
                ObjectData oldObjectData = new ObjectData(diffConditionContent.getOldValue());
                ObjectData newObjectData = new ObjectData(diffConditionContent.getValue());
                if (diffType != null) {
                    Object oldType = diffType.getOldValue().get(conditionTypeApiName);
                    Object newType = diffType.getValue().get(conditionTypeApiName);
                    if (oldType != null && newType != null) {
                        renderType(diffConditionContent, oldObjectData, newObjectData, oldType.toString(), newType.toString(), conditionContentApiName, user);
                    }
                } else {
                    String dbConditionType;
                    if (beforeDiffType == null) {
                        //此次并未变更 conditionType，则查出库中
                        IObjectData objectData = serviceFacade.findObjectData(user, dataId, apiName);
                        dbConditionType = objectData.get(conditionTypeApiName, String.class);
                    } else {
                        dbConditionType = beforeDiffType.getValue().get(conditionTypeApiName).toString();
                    }
                    renderType(diffConditionContent, oldObjectData, newObjectData, dbConditionType, dbConditionType, conditionContentApiName, user);
                }
            }
        }
    }

    public static LogInfo.DiffObjectData getDiffObjectData(String fieldApiName, List<LogInfo.DiffObjectData> diffObjectDataList) {
        if (CollectionUtils.empty(diffObjectDataList)) {
            return null;
        }
        return diffObjectDataList.stream()
                .filter(diffObjectData -> fieldApiName.equals(diffObjectData.getFieldApiName()))
                .findFirst()
                .orElse(null);
    }

    public static void renderType(LogInfo.DiffObjectData diffObjectData, ObjectData oldObjectData, ObjectData newObjectData,
                                  String oldType, String newType, String conditionContentApiName, User user) {
        translateManager.getTranslateService(oldType).render(oldObjectData, user);
        translateManager.getTranslateService(newType).render(newObjectData, user);
        diffObjectData.getOldValue().put(conditionContentApiName, oldObjectData.get(conditionContentApiName));
        diffObjectData.getValue().put(conditionContentApiName, newObjectData.get(conditionContentApiName));
    }

    /**
     * 在recordList获取指定对象的id
     *
     * @param recordList 记录列表
     * @param apiName    api名称
     * @return {@code String}
     */
    public static String getDataIdByLogRecordList(List<LogRecord> recordList, String apiName) {
        return recordList.stream()
                .filter(logRecord -> ActionType.Add.getId().equals(logRecord.getOperationType()))
                .map(LogRecord::getObjectInfo)
                .filter(x -> apiName.equals(x.getObjectApiName()))
                .findFirst()
                .map(ObjectInfo::getObjectDatas)
                .map(objectDate -> objectDate
                        .stream()
                        .map(ObjectInfo.ObjectData::getDataId)
                        .findFirst()
                        .orElse(Strings.EMPTY)
                )
                .orElse(Strings.EMPTY);

    }

    public static void formFieldConsumer(StandardDescribeLayoutController.Result result, Consumer<List<IFormField>> consumer) {
        Optional.ofNullable(result)
                .map(StandardDescribeLayoutController.Result::getLayout)
                .map(LayoutExt::of)
                .flatMap(LayoutExt::getFormComponent)
                .ifPresent(x -> {
                    FormComponentExt ext = FormComponentExt.of(x);
                    List<IFieldSection> fieldSections = ext.getFieldSections();
                    for (IFieldSection fieldSection : fieldSections) {
                        if (!"base_field_section__c".equals(fieldSection.getName())) {
                            continue;
                        }
                        List<IFormField> fields = fieldSection.getFields();
                        if (CollectionUtils.empty(fields)) {
                            return;
                        }
                        consumer.accept(fields);
                        fieldSection.setFields(fields);
                    }
                    ext.setFieldSections(fieldSections);
                });
    }

    /**
     * 下发条件选项卡，并且根据返利类型修改其选项卡名称
     *
     * @param result 结果
     */
    public static void issueConditionTab(AbstractStandardDetailController.Result result) {
        String componentName = "related_rule_condition";
        //判断是不是货返,如果是货返显示的名称不一样
        boolean isProductType = Optional.ofNullable(result)
                .map(AbstractStandardDetailController.Result::getData)
                .map(x -> x.get(RebateConstants.RebateField.REBATE_TYPE.getApiName()))
                .map(rebateType -> RebateConstants.RebateType.PRODUCT.getValue().equals(rebateType))
                .orElse(false);

        Optional.ofNullable(result).map(AbstractStandardDetailController.Result::getLayout)
                .ifPresent(x -> {
                    ILayout layout = new Layout(x);
                    LayoutExt layoutExt = LayoutExt.of(layout);
                    try {
                        List<IComponent> components = layoutExt.getComponents();
                        //如果布局中已经存在 产品条件，则不再下发
                        IComponent iComponent = components.stream()
                                .filter(component -> componentName.equals(component.getName()))
                                .findFirst()
                                .orElse(null);
                        if (iComponent == null) {
                            TabSection tabSection = new TabSection();
                            tabSection.setApiName("tab_related_rule_condition");
                            List<String> whatComponents = Lists.newArrayList(componentName);
                            if (isProductType) {
                                tabSection.setHeader(I18N.text("paas.udobj.related_product_range"));
                                CouponUtils.issueWhatTab(result, tabSection, whatComponents, RULE_RANGE_JSON);
                            } else {
                                tabSection.setHeader(I18N.text("paas.udobj.related_rule_condition"));
                                CouponUtils.issueWhatTab(result, tabSection, whatComponents, RULE_CONDITION_JSON);
                            }
                        } else {
                            //如果是货返进行名称修改
                            if (isProductType) {
                                iComponent.setHeader(I18N.text("paas.udobj.related_product_range"));
                                iComponent.set("nameI18nKey", "paas.udobj.related_product_range");
                                //处理布局在tab时候的名称
                                components.stream()
                                        .filter(component -> "tabs".equals(component.getType()))
                                        .findFirst()
                                        .map(TabsComponent.class::cast)
                                        .ifPresent(tabsComponent -> {
                                            List<TabSection> tabs = tabsComponent.getTabs();
                                            if (CollectionUtils.notEmpty(tabs)) {
                                                tabs.stream()
                                                        .filter(tab -> Optional.ofNullable(tab.getApiName()).orElse("").startsWith(componentName))
                                                        .findFirst()
                                                        .ifPresent(tabSection -> {
                                                            tabSection.setHeader(I18N.text("paas.udobj.related_product_range"));
                                                            tabSection.set("nameI18nKey", "paas.udobj.related_product_range");
                                                        });
                                            }
                                            tabsComponent.setTabs(tabs);
                                        });
                            } else {
                                iComponent.setHeader(I18N.text("paas.udobj.related_rule_condition"));
                                iComponent.set("nameI18nKey", "paas.udobj.related_rule_condition");
                            }


                        }
                    } catch (MetadataServiceException e) {
                        log.error("getComponents has error msg is{}", e.getMessage());
                    }
                });
    }

    /**
     * 下发what标签
     *
     * @param result         结果
     * @param tabSection     标签部分
     * @param whatComponents what组件
     * @param componentJson  组件json
     */
    public static void issueWhatTab(AbstractStandardDetailController.Result result, TabSection tabSection, List<String> whatComponents, String componentJson) {
        Optional.ofNullable(result).map(AbstractStandardDetailController.Result::getLayout)
                .ifPresent(x -> {
                    ILayout layout = new Layout(x);
                    LayoutExt layoutExt = LayoutExt.of(layout);
                    try {
                        //web端显示tab按钮
                        List<IComponent> components = layoutExt.getComponents();
                        components.stream().filter(z -> "tabs".equals(z.getType()))
                                .findFirst().ifPresent(component -> {
                            TabsComponent tabsComponent = (TabsComponent) component;
                            List<TabSection> tabs = tabsComponent.getTabs();
                            tabs.add(tabSection);
                            tabsComponent.setTabs(tabs);

                            List<List<String>> componentsList = tabsComponent.getComponents();
                            componentsList.add(whatComponents);
                            tabsComponent.setComponents(componentsList);
                        });
                        //移动端显示tab按钮
                        components.stream().filter(z -> "navigation".equals(z.getType()))
                                .findFirst().ifPresent(component -> {
                            NavigationComponent navigationComponent = (NavigationComponent) component;
                            List<String> componentsList = navigationComponent.getComponents();
                            componentsList.addAll(whatComponents);
                            navigationComponent.setComponents(componentsList);
                        });
                        IComponent iComponent = ComponentFactory.newInstance(componentJson);
                        iComponent.setHeader(I18N.text((String) iComponent.get("nameI18nKey")));
                        components.add(iComponent);
                        layoutExt.setComponents(components);
                    } catch (MetadataServiceException e) {
                        log.error("getComponents error");
                    }
                });
    }

    public static void setDefaultCouponType(BaseListController.Result result, String tenantId) {
        if (!config.isOpenPaperCoupon(tenantId)) {
            return;
        }
        Optional.ofNullable(result)
                .map(BaseListController.Result::getDataList)
                .filter(CollectionUtils::notEmpty)
                .ifPresent(dataList -> {
                    for (ObjectDataDocument objectDataDocument : dataList) {
                        IObjectData objectData = objectDataDocument.toObjectData();
                        String couponType = objectData.get(CouponConstants.CouponPlanField.COUPON_TYPE.getApiName(), String.class);
                        if (StringUtils.isBlank(couponType)) {
                            objectData.set(CouponConstants.CouponPlanField.COUPON_TYPE.getApiName(), CouponConstants.CouponType.ELECTRONIC.getValue());
                        }
                    }
                });
    }

    /**
     * 获取公共对象的用户
     *
     * @param actionContext
     * @param objectDescribe
     * @return
     */
    public static User getPublicUser(ActionContext actionContext, IObjectDescribe objectDescribe) {
        User user = actionContext.getUser();
        if (objectDescribe.isPublicObject()) {
            user = User.systemUser(objectDescribe.getUpstreamTenantId());
        }
        return user;
    }

    public static User getCouponObjUser(User srcUser, RebateCouponQuery.PublicObjInfo publicObjInfo) {
        User user = srcUser;
        if (null != publicObjInfo && publicObjInfo.isPublicObject()) {
            user = User.systemUser(publicObjInfo.getUpstreamTenantId());
        }
        return user;
    }

    public static RebateCouponQuery.PublicObjInfo getPublicObjectInfo(User user) {
        RebateCouponQuery.PublicObjInfo info = RebateCouponQuery.PublicObjInfo.builder().build();
        if (config.isOpenCoupon(user.getTenantId())) {
            IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), CouponConstants.COUPON_PLAN_API_NAME);
            if (describe.isPublicObject()) {
                info.setPublicObject(true);
                info.setUpstreamTenantId(describe.getUpstreamTenantId());
                info.setTenantId(user.getTenantId());
            }
        }
        return info;
    }

    public static void setDTenantId(IObjectDescribe objectDescribe, String tenantId, IObjectData objectData) {
        if (objectDescribe.isPublicObject()) {
            String firstTenantId = objectDescribe.getUpstreamTenantId();
            if (tenantId.equals(firstTenantId)) {
                objectData.setIsPublic(true);
            } else {
                Set<String> allTenantIds = com.beust.jcommander.internal.Sets.newHashSet();
                allTenantIds.add(firstTenantId);
                allTenantIds.add(tenantId);
                allTenantIds = couponDao.getDownAndUpTenantIds(allTenantIds, tenantId);
                objectData.set(CouponConstants.D_TENANT_ID, Lists.newArrayList(allTenantIds));
            }
        }
    }


    public static void checkMemberProgramMatch(User user, IObjectData couponInstanceData, IObjectData couponPlanData) {
        if (!config.isOpenIncentive(user.getTenantId())) {
            return;
        }
        String memberId = couponInstanceData.get(CouponConstants.LOYALTY_MEMBER_ID, String.class);
        if (StringUtils.isBlank(memberId)) {
            return;
        }
        Optional<IObjectData> iObjectDataOptional = Optional.ofNullable(serviceFacade.findObjectDataIgnoreAll(user, memberId, CouponConstants.LoyaltyMemberObj));
        if (!iObjectDataOptional.isPresent()) {
            return;
        }
        IObjectData memberData = iObjectDataOptional.get();
        String memberProgramId = memberData.get(CouponConstants.PROGRAM_ID, String.class);
        String couponPlanProgramId = couponPlanData.get(CouponConstants.PROGRAM_ID, String.class);
        if (!Objects.equals(memberProgramId, couponPlanProgramId)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_COUPON_MEMBER_PROGRAM_NOT_MATCH));
        }

    }
}
