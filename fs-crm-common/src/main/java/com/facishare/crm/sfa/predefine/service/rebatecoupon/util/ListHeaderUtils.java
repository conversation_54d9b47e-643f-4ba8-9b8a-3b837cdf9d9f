package com.facishare.crm.sfa.predefine.service.rebatecoupon.util;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.QueryTemplateDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;

import java.util.*;
import java.util.function.Consumer;

public class ListHeaderUtils {
    public static void removeButton(StandardListHeaderController.Result result, List<String> toRemoveButton) {
        buttonConsumer(result, buttonList ->
                buttonList.removeIf(button -> toRemoveButton.contains(button.getAction()))
        );
    }

    public static void buttonConsumer(StandardListHeaderController.Result result, Consumer<List<IButton>> btnConsumer) {
        if (null == result.getLayout()) {
            return;
        }
        ILayout layout = new Layout(result.getLayout());
        List<IButton> buttonList = layout.getButtons();
        if (CollectionUtils.empty(buttonList)) {
            return;
        }
        btnConsumer.accept(buttonList);
        layout.setButtons(buttonList);
    }

    public static void removerField(StandardListHeaderController.Result result, Set<String> removeFields) {
        Optional.ofNullable(result)
                .map(StandardListHeaderController.Result::getVisibleFields)
                .filter(data -> !data.isEmpty())
                .ifPresent(data ->
                        data.removeIf(removeFields::contains)
                );
        Optional.ofNullable(result)
                .map(StandardListHeaderController.Result::getVisibleFieldsWidth)
                .filter(data -> !data.isEmpty())
                .ifPresent(data ->
                        data.removeIf(x -> removeFields.contains(Optional.ofNullable(x.get("field_name")).map(Objects::toString).orElse("")))
                );
        Optional.ofNullable(result)
                .map(StandardListHeaderController.Result::getTemplates)
                .filter(data -> !data.isEmpty())
                .ifPresent(templates -> {
                    for (QueryTemplateDocument template : templates) {
                        List<Map> fieldList = (List<Map>) template.get("field_list");
                        if (CollectionUtils.notEmpty(fieldList)) {
                            fieldList.removeIf(x -> removeFields.contains(x.get("field_name").toString()));
                        }
                    }
                });
    }

    public static void remainButtons(StandardListHeaderController.Result result, List<String> actions) {
        if (Objects.isNull(result)) {
            return;
        }
        ListHeaderUtils.buttonConsumer(result, buttonList -> buttonList.removeIf(button -> !actions.contains(button.getAction())));
        result.getButtons().removeIf(button -> !actions.contains(button.get("action").toString()));
    }
}
