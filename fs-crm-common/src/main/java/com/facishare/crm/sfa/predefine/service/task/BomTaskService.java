package com.facishare.crm.sfa.predefine.service.task;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.task.AsyncTaskProducer;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.constant.BomCoreConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crm.sfa.utilities.enums.EnumUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.SFABizLogUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * bom-task
 */
@Service
@Slf4j
public class BomTaskService {


    @Autowired
    private AsyncTaskProducer asyncTaskProducer;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;

    private static final String BIZ = "sync_bom_instance_change";

    /**
     * 订单、销售合同、报价单共用同一个消息队列
     */
    private static final String COMMON_CREATE_STANDARD_BOM = "common_create_standard_bom";


    /**
     * 创建选配实例
     * @param user
     * @param apiName
     * @param instanceDataList
     */

    public void sendCreateBomInstanceMessage(User user, String apiName, List<IObjectData> instanceDataList) {
        sendMessage(user,apiName,instanceDataList);
    }

    public void cleanFieldValue(User user, ObjectDataDocument objectData, List<ObjectDataDocument> details) {
        if(!bizConfigThreadLocalCacheService.isGenerateStandardBomNone(user.getTenantId()) && objectData != null && CollectionUtils.notEmpty(details)){
            objectData.toObjectData().set(SalesOrderConstants.SalesOrderField.BOM_CREATED_STATUS.getApiName(), false);
            details.stream().forEach(x->{
                x.toObjectData().set(SalesOrderConstants.SalesOrderProductField.STANDARD_BOM_ID.getApiName(), null);
                x.toObjectData().set(SalesOrderConstants.SalesOrderProductField.STANDARD_BOM_LINE_ID.getApiName(), null);
            });
        }
        //sonar 处理
        if(objectData != null) {
            handleMasterDataFieldValue(objectData.toObjectData(), ObjectDataDocument.ofDataList(details));
        }
    }

    /**
     * 如何当前单据不是通过BOM下的订单，则将 bom_created_status 字段默认设置为 true
     * @param objectData
     */
    public void handleMasterDataFieldValue(IObjectData objectData, List<IObjectData> details) {
        if(objectData == null || CollectionUtils.empty(details)){
            return;
        }
        List<IObjectData> tempList = details.stream().filter(
                    x->StringUtils.isNotBlank(x.get(SalesOrderConstants.SalesOrderProductField.ROOT_PROD_PKG.getApiName(), String.class))
                ).collect(Collectors.toList());
        //不是通过BOM下的单
        if(CollectionUtils.empty(tempList)){
            objectData.set(SalesOrderConstants.SalesOrderField.BOM_CREATED_STATUS.getApiName(), true);
        }
    }

    /**
     * 创建标准BOM
     * @param user
     * @param objectData
     * @param standardDataList
     */
    public void sendCreateStandardBomMessage(User user, IObjectData objectData, List<IObjectData> standardDataList) {
        createStandardBomTask(user, objectData, standardDataList);
    }

    private void sendMessage(User user, String apiName, List<IObjectData> dataList) {
        if(CollectionUtils.empty(dataList)){
            return;
        }
        Predicate<IObjectData> predicate = x->(StringUtils.isNotBlank(x.get(BomConstants.FIELD_BOM_ID, String.class, ""))
                ||(Objects.equals(EnumUtil.nodeType.temp.getValue(),x.get(BomConstants.FIELD_NODE_TYPE, String.class, ""))))
                &&StringUtils.isNotBlank(x.get("root_prod_pkg_key", String.class));
        List<IObjectData> bomList = dataList.stream().filter(Objects::nonNull).filter(predicate)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(bomList)) {
            return ;
        }
        createBomInstanceTask(user, apiName, bomList);
    }
    //下单自动生成标准bom
    private void createStandardBomTask(User user, IObjectData objectData, List<IObjectData> standardDataList) {
        if(CollectionUtils.empty(standardDataList)){
            return;
        }
        boolean autoCreatedStandardBom = bizConfigThreadLocalCacheService.isGenerateStandardBomAuto(user.getTenantId());
        log.info("createBom:autoCreatedStandardBom :"+autoCreatedStandardBom + ", masterDataId:"+objectData.getId()+", ObjApiName: "+objectData.getDescribeApiName());
        if (BomCoreConstants.SUPPORT_CREATBOMOBJ_MASTER_LIST.contains(objectData.getDescribeApiName())
                && GrayUtil.bomMasterSlaveMode(user.getTenantId())
                && autoCreatedStandardBom) {
            Predicate<IObjectData> predicate = x->(StringUtils.isNotBlank(x.get(BomConstants.FIELD_BOM_ID, String.class, ""))
                    ||(Objects.equals(EnumUtil.nodeType.temp.getValue(),x.get(BomConstants.FIELD_NODE_TYPE, String.class, ""))))
                    &&StringUtils.isNotBlank(x.get("root_prod_pkg_key", String.class));
            List<IObjectData> bomList = standardDataList.stream().filter(Objects::nonNull).filter(predicate)
                    .collect(Collectors.toList());
            if (CollectionUtils.empty(bomList)) {
                return ;
            }
            List<ObjectDataDocument> objectDataList = bomList.stream().map(BomTaskService::of).collect(Collectors.toList());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("detailList", objectDataList);
            jsonObject.put("objectData", ofMaster(objectData));
            jsonObject.put("tenantId",user.getTenantId());
            String flag = "success" ;
            try {
                jsonObject.put("traceId",TraceContext.get().getTraceId());
                asyncTaskProducer.create(COMMON_CREATE_STANDARD_BOM, jsonObject.toJSONString(),user.getTenantId().concat(":").concat(objectData.getId()));
            } catch (Exception e) {
                flag = "fail" ;
                log.error("createBom:create standard bom sendMQ fail:"+e.getMessage(),e);
            }finally {
                SFABizLogUtil.sendAuditLog(SFABizLogUtil.Arg.builder()
                        .parameters(jsonObject.toJSONString())
                        .objectApiNames(objectData.getDescribeApiName())
                        .message(COMMON_CREATE_STANDARD_BOM)
                        .extra(flag)
                        .build(), user);
            }
        }
    }
    //生成选配实例
    private void createBomInstanceTask(User user, String apiName, List<IObjectData> bomList) {
        if (!bizConfigThreadLocalCacheService.isBomInstanceEnabled(user.getTenantId())) {
            return ;
        }
        List<ObjectDataDocument> objectDataList = bomList.stream().map(BomTaskService::of).collect(Collectors.toList());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("apiName",apiName);
        jsonObject.put("dataList", objectDataList);
        jsonObject.put("user",user);
        String flag = "success" ;
        try {
            asyncTaskProducer.create(BIZ, jsonObject.toJSONString(),user.getTenantId().concat(":").concat(TraceContext.get().getTraceId()));
        } catch (Exception e) {
            flag = "fail" ;
            log.error(" create bomInstance sendMQ fail:"+e.getMessage(),e);
        }finally {
            SFABizLogUtil.sendAuditLog(SFABizLogUtil.Arg.builder()
                    .parameters(jsonObject.toJSONString())
                    .objectApiNames(apiName)
                    .message(BIZ)
                    .extra(flag)
                    .build(), user);
        }
    }

    private static ObjectDataDocument ofMaster(IObjectData x) {
        ObjectDataDocument data = new ObjectDataDocument();
        data.put("_id",x.getId());
        data.put(SystemConstants.ObjectDescribeApiName,x.getDescribeApiName());
        data.put("name", x.getName());
        data.put("tenant_id",x.get("tenant_id"));
        return data;
    }

    private static ObjectDataDocument of(IObjectData x) {
        ObjectDataDocument data = new ObjectDataDocument();
        data.put("_id",x.getId());
        data.put(SystemConstants.ObjectDescribeApiName,x.getDescribeApiName());
        data.put(SalesOrderConstants.SalesOrderProductField.ROOT_PROD_PKG.getApiName(),x.get(SalesOrderConstants.SalesOrderProductField.ROOT_PROD_PKG.getApiName()));
        data.put(SalesOrderConstants.SalesOrderProductField.PARENT_PROD_PKG.getApiName(),x.get(SalesOrderConstants.SalesOrderProductField.PARENT_PROD_PKG.getApiName()));
        data.put(SalesOrderConstants.SalesOrderProductField.PROD_PKG.getApiName(),x.get(SalesOrderConstants.SalesOrderProductField.PROD_PKG.getApiName()));
        data.put(BomConstants.FIELD_TEMP_NODE_BOM_ID,x.get(BomConstants.FIELD_TEMP_NODE_BOM_ID));
        data.put(BomConstants.FIELD_BOM_ID,x.get(BomConstants.FIELD_BOM_ID) );
        data.put(BomConstants.FIELD_BOM_CORE_ID,x.get(BomConstants.FIELD_BOM_CORE_ID));
        data.put(SalesOrderConstants.SalesOrderProductField.QUANTITY.getApiName(),x.get(SalesOrderConstants.SalesOrderProductField.QUANTITY.getApiName()));
        data.put(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName(),x.get(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName()));
        data.put(BomConstants.FIELD_NODE_TYPE,x.get(BomConstants.FIELD_NODE_TYPE));
        data.put("tenant_id",x.get("tenant_id"));
        data.put(SystemConstants.Field.OutTenantId.apiName,x.getOutTenantId());
        data.put(SystemConstants.Field.OutOwner.apiName,x.getOutOwner());
        data.put(SystemConstants.Field.Owner.apiName,x.getOwner());
        data.put(BomConstants.FIELD_PRODUCT_GROUP_ID, x.get(BomConstants.FIELD_PRODUCT_GROUP_ID));
        data.put(BomConstants.FIELD_TEMP_NODE_GROUP_ID, x.get(BomConstants.FIELD_TEMP_NODE_GROUP_ID));
        data.put(BomConstants.FIELD_ORDER_FIELD, x.get(BomConstants.FIELD_ORDER_FIELD, Integer.class, Integer.valueOf(0)));
        data.put(BomConstants.FIELD_NODE_NO, x.get(BomConstants.FIELD_NODE_NO, Integer.class, Integer.valueOf(0)));
        return data;
    }


    public boolean needCreateStandardBom(String tenantId, String detailObjectApiName, String checkFieldApiName, List<IObjectData> detailsToAdd, List<IObjectData> detailsToUpdate, List<IObjectData> detailsToDelete, Map<String, List<IObjectData>> dbDetailDataMap) {
        boolean autoCreatedStandardBom = bizConfigThreadLocalCacheService.isGenerateStandardBomAuto(tenantId);
        if(autoCreatedStandardBom) {
            Set<String> rootId = Sets.newHashSet();
            if(CollectionUtils.notEmpty(detailsToAdd)) {
                detailsToAdd.forEach(x -> rootId.add(x.get(BomConstants.FIELD_ROOT_PROD_PKG_KEY, String.class)));
            }
            if(CollectionUtils.empty(dbDetailDataMap)) {
                return false;
            }
            List<IObjectData> orderProductList = dbDetailDataMap.getOrDefault(detailObjectApiName, Lists.newArrayList());
            Map<String, IObjectData> orderProductMap = orderProductList.stream().collect(Collectors.toMap(DBRecord::getId, Function.identity(), (v1, v2) -> v1));
            if(CollectionUtils.notEmpty(detailsToUpdate)) {
                detailsToUpdate.forEach(x -> {
                    IObjectData tmpObj = orderProductMap.get(x.getId());
                    if (Objects.nonNull(tmpObj) && org.apache.commons.lang.StringUtils.isNotBlank(x.get(BomConstants.FIELD_ROOT_PROD_PKG_KEY, String.class))
                            && x.get(checkFieldApiName, BigDecimal.class, BigDecimal.ONE).compareTo(tmpObj.get(checkFieldApiName, BigDecimal.class, BigDecimal.ONE)) != 0) {
                        rootId.add(x.get(BomConstants.FIELD_ROOT_PROD_PKG_KEY, String.class));
                    }
                });
            }
            if(CollectionUtils.notEmpty(detailsToDelete)) {
                detailsToDelete.forEach(x -> {
                    if (org.apache.commons.lang.StringUtils.isNotBlank(x.get(BomConstants.FIELD_ROOT_PROD_PKG_KEY, String.class)) && Objects.equals(x.getDescribeApiName(), detailObjectApiName)) {
                        rootId.add(x.get(BomConstants.FIELD_ROOT_PROD_PKG_KEY, String.class));
                    }
                });
            }
            return CollectionUtils.notEmpty(rootId);
        } else {
            return false;
        }
    }

    public void createBomTask(User user, String masterApiName, IObjectData objectData, List<IObjectData> detailDataList) {
        log.info("createBom:start to send mq for creating bom, needCreateStandardBom:"+true);
        try {
            sendCreateBomInstanceMessage(user, masterApiName, detailDataList);
            sendCreateStandardBomMessage(user, objectData, detailDataList);
        } catch (Exception e) {
            log.error("createBom:send createBomTask error", e);
        }
    }
}
