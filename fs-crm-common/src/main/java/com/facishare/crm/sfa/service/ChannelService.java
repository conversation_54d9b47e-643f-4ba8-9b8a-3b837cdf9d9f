package com.facishare.crm.sfa.service;

import com.facishare.crm.enums.ReminderTriggerEnums;
import com.facishare.crm.model.ChannelManagementDTO;
import com.facishare.crm.model.ChannelRpcModel;
import com.facishare.crm.model.PartnerChannelManage;
import com.facishare.crm.sfa.model.ChannelServiceModel;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @time 2024-06-29 11:45
 * @Description
 */
public interface ChannelService {
    String SIGN_CUSTOM_TEXT = "sign_custom_text";
    String RENEWAL_CUSTOM_TEXT = "renewal_custom_text";
    String REGISTER_CUSTOM_TEXT = "register_custom_text";
    String ADMISSION_CONFIG = "admission_config";

    List<String> getOccupiedPartnerProvisionIds(User user);

    List<String> getOccupiedPartnerAgreementIds(User user);

    ChannelServiceModel.@NotNull MatchScheme matchPriorityLayoutApiName(User user, String admissionApiName, IObjectData admissionData);

    @NotNull
    ChannelServiceModel.MatchScheme matchSignScheme(User user, String admissionObject, IObjectData admissionData);

    @NotNull
    ChannelServiceModel.MatchScheme matchProvisionScheme(User user, IObjectData admissionData, String admissionObject);

    String queryAgreementTemplateById(User user, String partnerAgreementId);

    PartnerChannelManage.SignScheme querySignSchemeById(User user, String signSchemeId);

    IObjectData queryAgreementDataById(User user, String partnerAgreementId);

    void executeLayoutFunction(User user, String aplApiName, String admissionObject, String crmMapperId);

    boolean notExistsModule(User user);

    ChannelManagementDTO.MatchSignSchemeWithPrmReminder matchSignSchemeWithReminderInfo(User user, IObjectData partnerData, String admissionObject);

    ChannelRpcModel.SignReminderInfoResult querySignReminderInfo(User user, String signSchemeId, ReminderTriggerEnums reminderTrigger);

    String querySignEmailSender(User systemUser);

    Boolean clearAlterReminderTrigger(User systemUser, String outTenantId);

    boolean allowInitiateRenewal(User user, String admissionObject, String belongDataId);

    Set<String> allowInitiateRenewal(User user, Set<String> admissionDataIds, String admissionObject);

    PartnerChannelManage.CustomText fetchCustomText(User user, String key);

    String fetchChannelAdmissionObject(User user);

    String getAdmissionObjectDataId(IObjectData objectData, String admissionObject);

    String fetchAdmissionDataId(User user, IObjectData data);
}
