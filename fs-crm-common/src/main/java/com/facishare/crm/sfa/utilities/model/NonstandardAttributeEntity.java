package com.facishare.crm.sfa.utilities.model;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;

import java.util.Map;

public class NonstandardAttributeEntity extends ObjectData {
    private String name__r;
    private String code__r;

    public static NonstandardAttributeEntity newInstance(Map<String, Object> iObjectData) {
        if (iObjectData == null) {
            return null;
        }
        NonstandardAttributeEntity obj = new NonstandardAttributeEntity();
        obj.map = iObjectData;
        return obj;
    }
    public static NonstandardAttributeEntity newInstance(IObjectData iObjectData) {
        ObjectData objectData = (ObjectData) iObjectData;
        return newInstance(objectData.getContainerDocument());
    }

    public String getName__r() {
        return get("name__r",String.class);
    }

    public void setName__r(String name__r) {
        this.name__r = name__r;
    }

    public String getCode__r() {
        return get("code__r",String.class);
    }

    public void setCode__r(String code__r) {
        this.code__r = code__r;
    }
}
