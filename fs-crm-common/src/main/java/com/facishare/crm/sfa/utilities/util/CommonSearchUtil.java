package com.facishare.crm.sfa.utilities.util;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.List;

@Slf4j
public class CommonSearchUtil {

    private static final ServiceFacade SERVICE_FACADE = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);
    private static final int BATCH_MAX_LIMIT = 1000;
    private static final int MAX_LOOP_COUNT = 100;

    /**
     * 根据条件获取对象列表
     *
     * @param user
     * @param filters
     * @return
     */
    public static List<IObjectData> getObjectList(User user, String apiName, List<IFilter> filters) {
        return getObjectList(user, apiName, filters, true, false);
    }

    /**
     * 根据条件获取对象列表
     *
     * @param user
     * @param filters
     * @return
     */
    public static List<IObjectData> getObjectList(User user, String apiName, List<IFilter> filters,
                                                  Boolean needReturnQuote, Boolean skipRelevantTeam) {
        if (CollectionUtils.empty(filters)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filters);
        searchTemplateQuery.setNeedReturnQuote(needReturnQuote);
        IActionContext actionContext = buildActionContext(user, skipRelevantTeam);
        QueryResult<IObjectData> objectList = SERVICE_FACADE.findBySearchQuery(actionContext, apiName,
                searchTemplateQuery);
        if (CollectionUtils.empty(objectList.getData())) {
            return Lists.newArrayList();
        }
        return objectList.getData();
    }
//    /**
//     * 保证查询searchQuery所有数据，
//     * 每次查询出来的limit数据，
//     * 执行comsumer的入参
//     * @param searchQuery searchQuery
//     * @param user user
//     * @param apiName apiName
//     * @param consumer 执行结果的消费者
//     */
//    public static void templateQueryAll(SearchTemplateQuery searchQuery, User user, String apiName, Consumer<List<IObjectData>> consumer){
//        int offset;
//        int limit = searchQuery.getLimit();
//        for (int i = 0; i < Integer.MAX_VALUE; i++) {
//            offset = i * limit;
//            searchQuery.setOffset(offset);
//            //1、根据源价目表ID查询价目表明细数据
//            QueryResult<IObjectData> queryResult =SERVICE_FACADE.findBySearchQuery(user, apiName, searchQuery);
//            List<IObjectData> detailDatas = Optional.ofNullable(queryResult).map(QueryResult::getData).orElse(Lists.newArrayList());
//            if (null == queryResult || CollectionUtils.empty(detailDatas)) {
//                break;
//            }
//            consumer.accept(detailDatas);
//            if (queryResult.getData().size() < limit) {
//                break;
//            }
//        }
//    }

    public static List<IObjectData> findDataBySearchQuery(User user, String describeName, SearchTemplateQuery searchTemplateQuery) {
        List<IObjectData> objectDataList = Lists.newArrayList();
        int offset = 0;
        int loopCount = 0;
        while (true) {
            if (MAX_LOOP_COUNT == loopCount) {
                log.warn("findDataBySearchQuery reaches loop limit, limit:{}", MAX_LOOP_COUNT);
                SFABizLogUtil.sendAuditLog(SFABizLogUtil.Arg.builder()
                        .action("sfa_loop_limit")
                        .objectApiNames(describeName)
                        .message("CommonSearchUtil.findDataBySearchQuery").build(), user);
            }
            loopCount++;
            searchTemplateQuery.setOffset(offset);
            QueryResult<IObjectData> queryResult = SERVICE_FACADE.findBySearchQuery(user, describeName, searchTemplateQuery);
            if (null == queryResult || CollectionUtils.empty(queryResult.getData())) {
                break;
            }
            objectDataList.addAll(queryResult.getData());
            if (queryResult.getData().size() < BATCH_MAX_LIMIT) {
                break;
            }
            offset += BATCH_MAX_LIMIT;
        }
        return objectDataList;
    }

    public static List<IObjectData> findDataBySearchQueryIgnoreAll(User user, String describeName, SearchTemplateQuery searchTemplateQuery) {
        List<IObjectData> objectDataList = Lists.newArrayList();
        int offset = 0;
        int loopCount = 0;
        while (true) {
            if (MAX_LOOP_COUNT == loopCount) {
                log.warn("findDataBySearchQueryIgnoreAll reaches loop limit, limit:{}", MAX_LOOP_COUNT);
                SFABizLogUtil.sendAuditLog(SFABizLogUtil.Arg.builder()
                        .action("sfa_loop_limit")
                        .objectApiNames(describeName)
                        .message("CommonSearchUtil.findDataBySearchQueryIgnoreAll").build(), user);
            }
            loopCount++;
            searchTemplateQuery.setOffset(offset);
            QueryResult<IObjectData> queryResult = SERVICE_FACADE.findBySearchQueryIgnoreAll(user, describeName, searchTemplateQuery);
            if (null == queryResult || CollectionUtils.empty(queryResult.getData())) {
                break;
            }
            objectDataList.addAll(queryResult.getData());
            if (queryResult.getData().size() < BATCH_MAX_LIMIT) {
                break;
            }
            offset += BATCH_MAX_LIMIT;
        }
        return objectDataList;
    }


    public static List<IObjectData> findBySearchQueryWithFields(ActionContext actionContext, String describeName, SearchTemplateQuery searchTemplateQuery, List<String> fields, Integer cycles) {
        List<IObjectData> objectDataList = Lists.newArrayList();
        int offset = 0;
        int executeCount = 0;
        while (true) {
            if (MAX_LOOP_COUNT == executeCount) {
                log.warn("findBySearchQueryWithFields reaches loop limit, limit:{}", MAX_LOOP_COUNT);
                SFABizLogUtil.sendAuditLog(SFABizLogUtil.Arg.builder()
                        .action("sfa_loop_limit")
                        .objectApiNames(describeName)
                        .message("CommonSearchUtil.findBySearchQueryWithFields").build(), User.systemUser(actionContext.getEnterpriseId()));
            }
            ++ executeCount;
            searchTemplateQuery.setOffset(offset);
            QueryResult<IObjectData> queryResult = SERVICE_FACADE.findBySearchTemplateQueryWithFields(actionContext, describeName, searchTemplateQuery, fields);
            if (null == queryResult || CollectionUtils.empty(queryResult.getData())) {
                break;
            }
            objectDataList.addAll(queryResult.getData());
            if (queryResult.getData().size() < BATCH_MAX_LIMIT) {
                break;
            }
            offset += BATCH_MAX_LIMIT;
            // -1 查全部
            if (null != cycles && -1 != cycles && executeCount >= cycles) {
                log.warn("cycles {} describeName: {}, query {} ", cycles, describeName, JSON.toJSONString(searchTemplateQuery));
                break;
            }
        }
        return objectDataList;
    }

//    /**
//     * 根据条件获取对象列表
//     *
//     * @param user
//     * @param filters
//     * @return
//     */
//    public static List<IObjectData> getObjectList(User user, String apiName, List<IFilter> filters,
//                                                  Boolean needReturnQuote, Boolean skipRelevantTeam, int limit) {
//        if (CollectionUtils.empty(filters)) {
//            return Lists.newArrayList();
//        }
//        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filters, limit);
//        searchTemplateQuery.setNeedReturnQuote(needReturnQuote);
//        IActionContext actionContext = buildActionContext(user, skipRelevantTeam);
//        QueryResult<IObjectData> objectList = SERVICE_FACADE.findBySearchQuery(actionContext, apiName,
//                searchTemplateQuery);
//        if (CollectionUtils.empty(objectList.getData())) {
//            return Lists.newArrayList();
//        }
//        return objectList.getData();
//    }

    /**
     * 根据id获取对象列表
     *
     * @param user
     * @param objectIds
     * @return
     */
    public static List<IObjectData> getObjectListById(User user, String apiName, List<String> objectIds) {
        if (CollectionUtils.empty(objectIds)) {
            return Lists.newArrayList();
        }
        List<IFilter> filters = Lists.newArrayList();
        if (objectIds.size() > 1) {
            SearchUtil.fillFilterIn(filters, DBRecord.ID, objectIds);
        } else {
            SearchUtil.fillFilterEq(filters, DBRecord.ID, objectIds);
        }
        return getObjectList(user, apiName, filters);
    }

    @NotNull
    public static SearchTemplateQuery getSearchTemplateQuery(List<IFilter> filters) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(2000);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setFilters(filters);
        return searchTemplateQuery;
    }

    @NotNull
    public static SearchTemplateQuery getSearchTemplateQuery(List<IFilter> filters, int limit) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(limit);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setFilters(filters);
        return searchTemplateQuery;
    }

    public static IActionContext buildActionContext(User user, Boolean skipRelevantTeam) {
        IActionContext context = ActionContextExt.of(user, RequestContextManager.getContext()).getContext();
        if (skipRelevantTeam) {
            context.put("skip_relevantTeam", true);
        }
        return context;
    }
}
