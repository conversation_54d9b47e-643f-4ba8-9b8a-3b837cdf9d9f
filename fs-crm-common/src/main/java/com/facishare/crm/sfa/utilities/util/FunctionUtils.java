package com.facishare.crm.sfa.utilities.util;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.config.BizFunctionInfo;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.appframework.function.util.FunctionQueryTemplateUtils;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FunctionUtils {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;

    public SearchTemplateQuery handleFiltersByFunction(User user, String objectApiName, SearchTemplateQuery query, IObjectData masterData, Map<String, List<IObjectData>> details) {
        if (StringUtils.isEmpty(objectApiName)) {
            return query;
        }
        if (null == masterData) {
            return query;
        }
        BizFunctionInfo objectAndFunctionApiName = bizConfigThreadLocalCacheService.getFunctionInfo(user.getTenantId(), objectApiName);
        if (objectAndFunctionApiName == null) {
            return query;
        }

        IUdefFunction function = serviceFacade.getFunctionLogicService().findUDefFunction(user
                , objectAndFunctionApiName.getFunctionAPIName()
                , objectAndFunctionApiName.getObjectAPIName());
        if (org.springframework.util.ObjectUtils.isEmpty(function)) {
            throw new ValidateException(I18N.text(I18NKey.NOT_FIND_FUNCTION, objectAndFunctionApiName.getFunctionAPIName()));
        }
        if (!"scope_rule".equals(function.getNameSpace()) || !function.isActive()){
            return query;
        }

        RunResult runResult = serviceFacade.getFunctionLogicService().executeUDefFunction(user, function, null, masterData, details);
        if (!runResult.isSuccess()) {
            throw new FunctionException(runResult.getErrorInfo());
        }
        if(Objects.equals(Utils.BOM_API_NAME,objectApiName) || Objects.equals(Utils.BOM_CORE_API_NAME,objectApiName)){
            SearchQuery searchQuery = FunctionQueryTemplateUtils.transfer2SearchQuery((Map<String, Object>) runResult.getFunctionResult());
            handleSearchQuery(searchQuery);
            return getSearchTemplateQuery(query, searchQuery);
        } else{
            return handleFunctionResult(user.getTenantId(), query, runResult.getFunctionResult());
        }
    }

    public SearchTemplateQuery listFilterByFunction(User user, String objectApiName, SearchTemplateQuery query) {
        if (StringUtils.isEmpty(objectApiName)) {
            return query;
        }

        BizFunctionInfo objectAndFunctionApiName = bizConfigThreadLocalCacheService.getFunctionInfo(user.getTenantId(), objectApiName);
        if (objectAndFunctionApiName == null) {
            return query;
        }

        IUdefFunction function = serviceFacade.getFunctionLogicService().findUDefFunction(user
                , objectAndFunctionApiName.getFunctionAPIName()
                , objectAndFunctionApiName.getObjectAPIName());
        if (org.springframework.util.ObjectUtils.isEmpty(function)) {
            throw new ValidateException(I18N.text(I18NKey.NOT_FIND_FUNCTION, objectAndFunctionApiName.getFunctionAPIName()));
        }

        if (!"scope_rule".equals(function.getNameSpace()) || !function.isActive()){
            return query;
        }
        //列表查询，没有数据时，直接返回
        RunResult runResult = serviceFacade.getFunctionLogicService().executeUDefFunction(user, function, null, null, null);
        if (!runResult.isSuccess()) {
            throw new FunctionException(runResult.getErrorInfo());
        }
        //列表查询，没有数据时，直接返回
        if(runResult.getFunctionResult() == null) {
            return query;
        }
        SearchQuery searchQuery = FunctionQueryTemplateUtils.transfer2SearchQuery((Map<String, Object>) runResult.getFunctionResult());
        handleSearchQuery(searchQuery);
        return getSearchTemplateQuery(query, searchQuery);
    }

    /**
     * 处理isMasterField字段
     */
    private void handleSearchQuery(SearchQuery searchQuery) {
        if (CollectionUtils.isEmpty(searchQuery.toSearchTemplateQuery().getFilters())) {
            return;
        }

        searchQuery.toSearchTemplateQuery().getFilters().stream().forEach(filter -> {
            if (filter.getFieldName() != null && filter.getFieldName().contains(".") && filter.getIsMasterField() == null) {
                filter.setIsMasterField(Boolean.TRUE);
            }
        });
    }
    private SearchTemplateQuery getSearchTemplateQuery(SearchTemplateQuery searchTemplateQuery, SearchQuery searchQuery) {
        Query query = Query.fromSearchTemplateQuery(searchTemplateQuery);
        // 判空
        if (Objects.isNull(searchQuery)) {
            return searchTemplateQuery;
        }
        Optional<SearchQuery> before = query.getSearchQuery();
        if (before.isPresent()) {
            SearchQuery after = before.get().and(searchQuery);
            query.setSearchQuery(after);
        } else {
            query.setSearchQuery(searchQuery);
        }
        return (SearchTemplateQuery) query.toSearchTemplateQuery();
    }

    private SearchTemplateQuery handleFunctionResult(String tenantId, SearchTemplateQuery query, Object functionResult) {
        SearchTemplateQuery originalQuery = query;
        if (query == null) {
            query = new SearchTemplateQuery();
            query.setLimit(1000);
        }
       if (functionResult instanceof List) {
            //函数返回List<String> 时 替换 _id 并且传入数据id
            Integer limit = AppFrameworkConfig.getRelatedListFunctionGrayCountMap()
                    .getOrDefault(tenantId, SearchTemplateQueryExt.FUNCTION_RESULT_MAX_SIZE);
            IFilter filter = new Filter();
            filter.setFieldName(IFieldDescribe.ID);
            filter.setValueType(FilterExt.FilterValueTypes.CONSTANT);
            filter.setOperator(Operator.IN);
            filter.setFieldValues(((List<String>) functionResult).stream().limit(limit).collect(Collectors.toList()));
            query.getFilters().add(filter);
        } else if (functionResult instanceof Map) {
            //函数返回查询模版会转换成map，当走到这个方法时代表没有灰度 '或' 查询
            //递归生成filter 都是是 '且' 的关系
            List<IFilter> filter = new ArrayList<>();
            FunctionQueryTemplateUtils.transfer2FilterList((Map<String, Object>) functionResult, filter);
            query.getFilters().addAll(filter);
        } else {
            query = originalQuery;
        }
        return query;
    }

    public void fillFunctionFilter(User user, SearchTemplateQuery searchTemplateQuery, IObjectData objectData, Map<String, List<IObjectData>> details, String functionObjectApiName) {
        if (ObjectUtils.isEmpty(objectData)) {
            return;
        }
        if (ObjectUtils.isEmpty(searchTemplateQuery)) {
            return;
        }
        Tuple<Wheres, Integer> functionFilterInfo = getFunctionFilter(searchTemplateQuery.getWheres());
        if (functionFilterInfo == null) {
            return;
        }
        String functionAPIName = functionFilterInfo.getKey().getFilters().get(functionFilterInfo.getValue()).getFieldValues().get(0);
        if (StringUtils.isEmpty(functionAPIName)) {
            return;
        }
        IUdefFunction function = serviceFacade.getFunctionLogicService().findUDefFunction(user, functionAPIName, functionObjectApiName);
        if (ObjectUtils.isNotEmpty(function)) {
            RunResult runResult = serviceFacade.getFunctionLogicService().executeUDefFunction(user, function, null, objectData, details);
            if (!runResult.isSuccess()) {
                throw new FunctionException(runResult.getErrorInfo());
            }
            handleFunctionResult(user.getTenantId(), searchTemplateQuery, runResult.getFunctionResult());
        }
    }

    private Tuple<Wheres, Integer> getFunctionFilter(List<Wheres> wheres) {
        Wheres where = null;
        Integer filterIndex = null;
        for (int whereIndex = 0; whereIndex < wheres.size(); whereIndex++) {
            Wheres currentWhere = wheres.get(whereIndex);
            for (int i = 0; i < currentWhere.getFilters().size(); i++) {
                IFilter filter = currentWhere.getFilters().get(i);
                if (Objects.equals(filter.getValueType(), FilterExt.FilterValueTypes.FUNCTION_VARIABLE)) {
                    where = currentWhere;
                    filterIndex = i;
                    break;
                }
            }
        }
        if (where == null || filterIndex == null) {
            return null;
        }
        return Tuple.of(where, filterIndex);
    }

    public void handleFunctionFilter(User user, ObjectDataDocument objectDocument, ObjectDataDocument masterDocument, Map<String, List<ObjectDataDocument>> details, SearchTemplateQuery query) {
        Tuple<Wheres, Integer> functionFilterInfo = getFirstFunctionFilter(query.getWheres());
        if (functionFilterInfo == null) {
            return;
        }
        if (null == objectDocument) {
            return;
        }
        Tuple<String, String> objectAndFunctionAPIName = getObjectAndFunctionAPIName(objectDocument.toObjectData().get(IObjectData.DESCRIBE_API_NAME, String.class), functionFilterInfo.getKey().getFilters().get(functionFilterInfo.getValue()));
        if (null == objectAndFunctionAPIName) {
            return;
        }
        IUdefFunction function = serviceFacade.getFunctionLogicService().findUDefFunction(user, objectAndFunctionAPIName.getValue(), objectAndFunctionAPIName.getKey());
        if (ObjectUtils.isEmpty(function)) {
            throw new ValidateException(I18N.text(I18NKey.NOT_FIND_FUNCTION, objectAndFunctionAPIName.getValue()));
        }
        Tuple<IObjectData, Map<String, List<IObjectData>>> result = getData4Function(objectDocument, masterDocument, details);
        RunResult runResult = serviceFacade.getFunctionLogicService().executeUDefFunction(user, function, null, result.getKey(), result.getValue());
        if (!runResult.isSuccess()) {
            throw new FunctionException(runResult.getErrorInfo());
        }
        addFunctionResult2Filter(user, functionFilterInfo.getKey(), functionFilterInfo.getValue(), runResult.getFunctionResult());
    }

    private Tuple<Wheres, Integer> getFirstFunctionFilter(List<Wheres> wheres) {
        Wheres where = null;
        Integer filterIndex = null;
        int size = wheres.size();
        Wheres currentWhere;
        IFilter filter;
        for (int whereIndex = 0; whereIndex < size; whereIndex++) {
            currentWhere = wheres.get(whereIndex);
            int size1 = currentWhere.getFilters().size();
            for (int i = 0; i < size1; i++) {
                filter = currentWhere.getFilters().get(i);
                if (null != filter.getValueType() && FilterExt.FilterValueTypes.FUNCTION_VARIABLE == filter.getValueType()) {
                    where = currentWhere;
                    filterIndex = i;
                    break;
                }
            }
        }
        if (null == where) {
            return null;
        }
        return Tuple.of(where, filterIndex);
    }

    private Tuple<String, String> getObjectAndFunctionAPIName(String objectAPIName, IFilter filter) {
        String functionAPIName = filter.getFieldValues().get(0);
        if (Strings.isNullOrEmpty(functionAPIName)) {
            return null;
        }
        return Tuple.of(objectAPIName, functionAPIName);
    }

    private Tuple<IObjectData, Map<String, List<IObjectData>>> getData4Function(ObjectDataDocument objectDocument, ObjectDataDocument masterDocument, Map<String, List<ObjectDataDocument>> detailsDocument) {
        if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(masterDocument)) {
            IObjectData master = masterDocument.toObjectData();
            Map<String, List<IObjectData>> details = new HashMap<>();
            IObjectData detail = objectDocument.toObjectData();
            if (com.facishare.paas.appframework.common.util.CollectionUtils.empty(detailsDocument)) {
                //主从模式下(函数绑定从对象)网页不是返回全部的数据，而是只返回触发的那条从
                details.put(detail.getDescribeApiName(), Arrays.asList(detail));
            } else {
                List<ObjectDataDocument> detailList = detailsDocument.get(detail.getDescribeApiName());
                List<IObjectData> detailDataList = com.facishare.paas.appframework.common.util.CollectionUtils.nullToEmpty(detailList).stream()
                        .filter(com.facishare.paas.appframework.common.util.CollectionUtils::notEmpty)
                        .map(ObjectDataDocument::toObjectData)
                        .collect(Collectors.toList());

                //前端会把当前操作的从放在第一条的位置，
                //保障从对象列表第一条从是当前操作的从数据，否则以前的自定义函数执行会有问题
                detailDataList.set(0, detail);
                details.put(detail.getDescribeApiName(), detailDataList);
            }
            return Tuple.of(master, details);
        } else {
            return Tuple.of(objectDocument.toObjectData(), new HashMap<>());
        }
    }

    private void addFunctionResult2Filter(User user, Wheres wheres, Integer filterIndex, Object functionResult) {
        if (ObjectUtils.isEmpty(functionResult)) {
            //前端传入的filterName是id 不是 _id 这里需要替换，并且传入一个查询不到的value
            IFilter filter = wheres.getFilters().get(filterIndex);
            filter.setFieldName(DBRecord.ID);
            filter.setFieldValues(Arrays.asList(""));
            return;
        }
        //函数返回List<String> 时 替换 _id 并且传入数据id
        if (functionResult instanceof List) {
            Integer limit = AppFrameworkConfig.getRelatedListFunctionGrayCountMap()
                    .getOrDefault(user.getTenantId(), SearchTemplateQueryExt.FUNCTION_RESULT_MAX_SIZE);

            IFilter filter = wheres.getFilters().get(filterIndex);
            filter.setFieldName(DBRecord.ID);
            filter.setValueType(FilterExt.FilterValueTypes.CONSTANT);
            filter.setFieldValues(((List<String>) functionResult).stream().limit(limit).collect(Collectors.toList()));
            //去掉，会多拼一次条件
            //wheres.getFilters().add(filter);
        }
        //函数返回查询模版会转换成map，当走到这个方法时代表没有灰度 '或' 查询
        //递归生成filter 都是是 '且' 的关系
        if (functionResult instanceof Map) {
            wheres.getFilters().remove(filterIndex.intValue());
            List<IFilter> filter = new ArrayList<>();
            FunctionQueryTemplateUtils.transfer2FilterList((Map<String, Object>) functionResult, filter);
            wheres.getFilters().addAll(filter);
        }
    }
}
