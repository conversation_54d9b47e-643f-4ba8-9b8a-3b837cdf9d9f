package com.facishare.crm.sfa.utilities.util.Price;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreService;
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreServiceImpl;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class BOMObjRealPriceService extends BaseRealPriceService {
    private List<IObjectData> bomNodeList = Lists.newArrayList();
    //不在入参中的产品id，在init前插入入参，一起参与获取最优价格的操作，后期用于运算，出参过滤掉
    private List<String> extraProductIdList = Lists.newArrayList();
    private final Map<String, BigDecimal> defaultPriceBookPriceData = Maps.newHashMap();
    private IObjectDescribe bomDescribe;

    private static final BomCoreService bomCoreService = SpringUtil.getContext().getBean(BomCoreServiceImpl.class);
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    public BOMObjRealPriceService(RealPriceModel.Arg arg, User user, boolean availableRangeFlag, boolean priceBookFlag) {
        super(arg, user, availableRangeFlag, priceBookFlag);
    }

    @Override
    public void init(List<RealPriceModel.FullProduct> productList) {
        //只需获取root下的直属子节点
        bomDescribe = SERVICE_FACADE.findObject(user.getTenantId(), Utils.BOM_API_NAME);
        List<String> rootBomIdList = arg.getRootBomIds();
        if (CollectionUtils.notEmpty(rootBomIdList)) {
            List<String> prodIdList = productList.stream().map(RealPriceModel.FullProduct::getProductId).collect(Collectors.toList());
            if (CollectionUtils.notEmpty(arg.getRootBomList())) {
                bomNodeList = arg.getRootBomList();
            }else{
                bomNodeList = bomCoreService.findBomByRootId(user, rootBomIdList);
            }
            //设置里默认选择，且设置了价目表价格的根节点直属子节点数据

            extraProductIdList = bomNodeList.stream().filter(
                    x -> rootBomIdList.contains(x.get("parent_bom_id", String.class))
                            && x.get("selected_by_default",Boolean.class,false) && Objects.equals("2",x.get("price_mode",String.class))
            ).map(x -> x.get("product_id", String.class)).collect(Collectors.toList());
            extraProductIdList.removeAll(prodIdList);
            for (String s : extraProductIdList) {
                RealPriceModel.FullProduct t = new RealPriceModel.FullProduct();
                t.setProductId(s);
                productList.add(t);
            }
        }
        super.init(productList);
    }

    @Nullable
    @Override
    public IObjectData getHighPriorityData(RealPriceModel.FullProduct fullProduct, List<String> priceBookProductIds,
                                           List<IObjectData> multiUnitRelateProductList, String standardPriceBookId,
                                           boolean isEnforcePriority, boolean isOpenIncrementalPricing, String mcCurrency) {
        IObjectData rst = super.getHighPriorityData(fullProduct, priceBookProductIds, multiUnitRelateProductList, standardPriceBookId, isEnforcePriority, isOpenIncrementalPricing, mcCurrency);
        if (rst == null) return null;
        if (extraProductIdList.contains(fullProduct.getProductId())) {
            rst.set("selling_data_define_type", "bom");
        }
        rst.set("prod_pkg_key", fullProduct.getProdKey());
        rst.set("parent_prod_pkg_key", fullProduct.getParentProdKey());
        rst.set("root_prod_pkg_key", fullProduct.getRootProdKey());
        if (!fullProduct.isBom()) return rst;
        rst.set("bom_id", fullProduct.getBomId());
        rst.set("parent_bom_id", fullProduct.getParentBomId());
        rst.set("root_bom_id", fullProduct.getRootBomId());
        return rst;
    }

    //保存默认最优价格
    @Override
    protected void saveDefaultHighPriorityData(RealPriceModel.FullProduct fullProduct, IObjectData sortedData) {
        if (priceBookFlag) {
            defaultPriceBookPriceData.put(fullProduct.getProductId()
                    , new BigDecimal(sortedData.get("selling_price", String.class,"0"))
                            .multiply(new BigDecimal(sortedData.get("discount", String.class,"100"))
                                    .divide(new BigDecimal(100)))
            );
        }
    }

    @Override
    protected void fillData(IObjectData data) {
        super.fillData(data);
        List<String> tempNodesIds = arg.getTempNodesIds();
        if (CollectionUtils.notEmpty(tempNodesIds)) {
            if (tempNodesIds.contains(data.get(SalesOrderConstants.SalesOrderProductField.ROOT_PROD_PKG.getApiName(),String.class,"").concat(data.get(SalesOrderConstants.SalesOrderProductField.PROD_PKG.getApiName(),String.class,"")))) {
                IFieldDescribe fieldDescribe = bomDescribe.getFieldDescribe(BomConstants.FIELD_PRICE_MODE);
                Object defaultValue = Optional.ofNullable(fieldDescribe).map(IFieldDescribe::getDefaultValue).orElseGet(()->"");
                if (Objects.equals("2",String.valueOf(defaultValue))) {
                    data.set("selling_price", data.get("param_price") != null && StringUtils.isNotEmpty(data.get("param_price", String.class)) ? data.get("param_price") : data.get("selling_price"));
                } else {
                    Optional<IObjectData> product = CollectionUtils.nullToEmpty(productInfoList).stream().filter(d -> Objects.equals(data.get(BomConstants.FIELD_PRODUCT_ID, String.class), d.getId())).findFirst();
                    data.set("selling_price", data.get("param_price") != null && StringUtils.isNotEmpty(data.get("param_price", String.class)) ? data.get("param_price") : product.isPresent()?product.get().get("price",String.class):null);
                }
            }
        }
        if (data.get("bom_id") == null || StringUtils.isBlank(data.get("bom_id", String.class))) return;
        String bomId = data.get("bom_id", String.class);
        IObjectData bomNode = bomNodeList.stream().filter(x -> bomId.equals(x.getId())).findFirst().orElse(null);
        if (bomNode == null) return;

        if (!isRootBom(data)) {
            //非根节点取价
            if ("2".equals(bomNode.get("price_mode"))) {
                //价目表取价
                data.set("selling_price", data.get("param_price") != null && StringUtils.isNotEmpty(data.get("param_price", String.class)) ? data.get("param_price") : data.get("selling_price"));
            } else {
                data.set("selling_price", data.get("param_price") != null && StringUtils.isNotEmpty(data.get("param_price", String.class)) ? data.get("param_price") : bomNode.get("adjust_price"));
            }
            return;
        }
        if (arg.isCalculateRootBomPrice()) {
            //根节点取价
            String rootProdKey = data.get("prod_pkg_key", String.class);
            BigDecimal defaultSubNodePrice = getDefaultSubNodePrice(bomId);
            BigDecimal parmSubNodePrice = getParmSubNodePrice(bomId, rootProdKey);
            if (bizConfigThreadLocalCacheService.bomPriceCalculationConfiguration(user.getTenantId())) {
                //根节点的计算方式:根节点价格-所有DB中直属子节点(选项为默认)的价格+选择的所有直属子节点价格
                data.set("selling_price", data.get("selling_price", BigDecimal.class, BigDecimal.ZERO).add(parmSubNodePrice));
            } else {
                data.set("selling_price", data.get("selling_price", BigDecimal.class, BigDecimal.ZERO).subtract(defaultSubNodePrice).add(parmSubNodePrice));
            }
        } else {
            data.set("selling_price", data.get("selling_price", BigDecimal.class, BigDecimal.ZERO));
        }
    }

    private boolean isRootBom(IObjectData data) {
        if (data.get("bom_id") == null || StringUtils.isEmpty(data.get("bom_id", String.class))) return false;

        if (data.get("prod_pkg_key") != null && StringUtils.isNotBlank(data.get("prod_pkg_key", String.class))) {
            return data.get("parent_prod_pkg_key") == null || StringUtils.isBlank(data.get("parent_prod_pkg_key", String.class));
        } else {
            return StringUtils.isEmpty(data.get("parent_bom_id", String.class));

        }

    }

    //获取根节点的直属子节点(入参)的价格合计
    private BigDecimal getParmSubNodePrice(String rootBomId, String rootProdKey) {
        BigDecimal parmSubNodePrice = new BigDecimal("0");
        List<RealPriceModel.FullProduct> parmDirectSubProduct;
        //获取直属子节点(入参)产品信息
        if (arg.hasBomKey()) {
            parmDirectSubProduct = arg.getFullProductList().stream().filter(
                    x -> Objects.equals(rootProdKey,x.getParentProdKey())).collect(Collectors.toList());
        } else {
            parmDirectSubProduct = arg.getFullProductList().stream().filter(
                    x -> Objects.equals(rootBomId,x.getParentBomId())).collect(Collectors.toList());
        }

        if (CollectionUtils.notEmpty(parmDirectSubProduct)) {
            for (RealPriceModel.FullProduct data : parmDirectSubProduct) {
                BigDecimal amount = data.getAmount() == null ? new BigDecimal("1") : data.getAmount();
                parmSubNodePrice = parmSubNodePrice.add((data.getPrice() == null) ? new BigDecimal("0") : data.getPrice().multiply(amount));
            }
        }
        return parmSubNodePrice;
    }

    //获取根节点的直属子节点(db预设)的价格合计
    private BigDecimal getDefaultSubNodePrice(String rootBomId) {
        //默认选择的价格汇总(不考虑price_mode是价目表价格还是配置价格,统一按配置价格扣减)
        BigDecimal defaultSubNodePrice = new BigDecimal("0");
        //获取默认选中直属子节点(db)bom信息
        List<IObjectData> dbDefaultDirectSubNode = bomNodeList.stream().filter(
                x -> rootBomId.equals(x.get("parent_bom_id", String.class))
                        && x.get("selected_by_default", Boolean.class, false)
        ).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(dbDefaultDirectSubNode)) {
            for (IObjectData data : dbDefaultDirectSubNode) {
                BigDecimal price = new BigDecimal("0");
                if ("1".equals(data.get("price_mode"))) {
                    //配置价格
                    price = data.get("adjust_price") == null ? new BigDecimal("0") : new BigDecimal(data.get("adjust_price", String.class));
                } else {
                    //价目表价格
                    if (defaultPriceBookPriceData.containsKey(data.get("product_id", String.class))) {
                        price = defaultPriceBookPriceData.get(data.get("product_id", String.class));
                    }
                }

                defaultSubNodePrice = defaultSubNodePrice.add(price.multiply(new BigDecimal(data.get("amount", String.class))));
            }
        }
        return defaultSubNodePrice;
    }
}
