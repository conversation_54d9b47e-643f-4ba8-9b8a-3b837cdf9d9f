package com.facishare.crm.sfa.utilities.util.Price;

import com.facishare.crm.constants.SaleContractConstants;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.holder.DataHolder;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.PriceBookCommonService;
import com.facishare.crm.sfa.predefine.service.attribute.AttributeCoreService;
import com.facishare.crm.sfa.predefine.service.attribute.AttributeCoreServiceImpl;
import com.facishare.crm.sfa.predefine.service.attribute.model.Attribute;
import com.facishare.crm.sfa.predefine.service.attributepricebook.AttributePriceBookService;
import com.facishare.crm.sfa.predefine.service.attributepricebook.AttributePriceBookServiceImpl;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl;
import com.facishare.crm.sfa.utilities.constant.*;
import com.facishare.crm.sfa.utilities.constant.AttributePriceBookConstants.AttributePriceBookField;
import com.facishare.crm.sfa.utilities.constant.AttributePriceBookConstants.AttributePriceBookLinesField;
import com.facishare.crm.sfa.utilities.model.PriceTieredRecord;
import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.crm.sfa.utilities.util.ValidDateUtils;
import com.facishare.crm.util.MtCurrentUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class BaseRealPriceService {

    protected static ServiceFacade SERVICE_FACADE = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);
    protected static AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);
    protected static AttributePriceBookService attributePriceBookService = SpringUtil.getContext().getBean(AttributePriceBookServiceImpl.class);
    protected static AttributeCoreService attributeCoreService = SpringUtil.getContext().getBean(AttributeCoreServiceImpl.class);
    protected static MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitServiceImpl.class);
    //数据定义类型标识符  sys为入参传入的标准数据，会拼装到返回值中，其他标识数据一概抛弃
    private static String SELLING_DATA_DEFINE_TYPE = "sys";
    private static BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private static PriceBookCommonService priceBookCommonService = SpringUtil.getContext().getBean(PriceBookCommonService.class);
    private static MetaDataFindServiceExt metaDataFindServiceExt = SpringUtil.getContext().getBean(MetaDataFindServiceExt.class);
    /**
     * 标准价目表产品信息
     */
    protected List<IObjectData> standardPriceBookProductList = Lists.newArrayList();
    /**
     * 涉及到的产品信息
     */
    protected List<IObjectData> productInfoList = Lists.newArrayList();
    /**
     * 可售范围信息
     */
    protected List<String> availableRangeIdList = Lists.newArrayList();
    /**
     * 可售产品范围信息
     */
    protected List<IObjectData> availableProductList = Lists.newArrayList();
    /**
     * 属性信息
     */
    protected List<Attribute> attributeInfoList = Lists.newArrayList();
    /**
     * 属性价目表明细(开增量属性定价时为全量属性价目表明细) product_id -> list
     */
    protected Map<String, List<IObjectData>> productAttrPriceBookLinesMap = Maps.newHashMap();
    /**
     * 增量属性价目表明细 product_id -> list
     */
    protected Map<String, List<IObjectData>> productIncrementAttrPriceBookLinesMap = Maps.newHashMap();
    /**
     * 价目表名称
     */
    protected Map<String, String> priceBookNameMap = Maps.newHashMap();
    /**
     * 销售合同明细
     */
    protected Map<String, IObjectData> saleContractLineMap = Maps.newHashMap();
    /**
     * 适配价目表产品信息/标准产品信息/经过处理的价目表产品(带可售范围信息)
     */
    protected List<IObjectData> baseProductList = Lists.newArrayList();
    /**
     * 币种转换比率
     */
    Map<String, String> exchangeRateMap = Maps.newHashMap();
    protected String standardPriceBookId = "";
    protected User user;
    protected boolean availableRangeFlag = false;
    protected boolean priceBookFlag = false;
    protected boolean attributeFlag = false;
    protected RealPriceModel.Arg arg;

    public BaseRealPriceService(RealPriceModel.Arg arg, User user, boolean availableRangeFlag, boolean priceBookFlag) {
        this.user = user;
        this.availableRangeFlag = availableRangeFlag;
        this.priceBookFlag = priceBookFlag;
        this.arg = arg;
    }

    public void init(List<RealPriceModel.FullProduct> productList) {
        StopWatch stopWatch = StopWatch.create("BaseRealPriceService init");
        List<String> priceBookIdList = Lists.newArrayList();
        List<String> prodIdList = productList.stream().map(RealPriceModel.FullProduct::getProductId).collect(Collectors.toList());
        List<String> actualUnitIdList = new ArrayList<>();
        if (bizConfigThreadLocalCacheService.isOpenMultiUnitPriceBook(user.getTenantId())) {
            actualUnitIdList = productList.stream()
                    .filter(x -> !Strings.isNullOrEmpty(x.getUnit()))
                    .map(RealPriceModel.FullProduct::getUnit).collect(Collectors.toList());
        }
        if (priceBookFlag && arg.hasAttribute()) {
            attributeFlag = true;
        }
        if (CollectionUtils.empty(arg.getProductDataList())) {
            if (!GrayUtil.isSpuRelatedListOptimize(user.getTenantId()) || !(Boolean.TRUE.equals(arg.getFromSpuList()) || Boolean.TRUE.equals(arg.getFromProductList()))
                    || !priceBookFlag || attributeFlag || bizConfigThreadLocalCacheService.isCPQEnabled(user.getTenantId())) {
                productInfoList = findStandardProductList(prodIdList);
            }
        } else {
            productInfoList = arg.getProductDataList();
        }
        stopWatch.lap("findStandardProductList");
        Long businessDate = ValidDateUtils.getValidDate(user.getTenantId(), arg.getBusinessDate(), arg.getObjectData());
        IObjectData objectData = null == arg.getObjectData() ? null : arg.getObjectData().toObjectData();
        IObjectData saleContractObjectData = availableRangeUtils.findSaleContractInfo(user, objectData);
        saleContractLineMap = getSaleContractLineInfo(user, arg.getRequestSource(), productList, objectData, saleContractObjectData);
        stopWatch.lap("getSaleContractConstraintInfo");
        if (availableRangeFlag) {
            if (Strings.isNullOrEmpty(arg.getAvailableRangeId())) {
                if (GrayUtil.isSpuRelatedListOptimize(user.getTenantId()) && Boolean.TRUE.equals(arg.getFromSpuList())) {
                    availableRangeIdList = DataHolder.getContext();
                } else {
                    availableRangeIdList = availableRangeUtils.getAvailableRangeIdList(user, arg.getAccountId(), arg.getPartnerId(), ""
                            , objectData, Maps.newHashMap(), saleContractObjectData);
                    stopWatch.lap("getAvailableRangeIdList");
                }
                if (CollectionUtils.empty(availableRangeIdList)) {
                    log.warn("no available range, tenantId:{}", user.getTenantId());
                    return;
                }
            } else {
                availableRangeIdList.add(arg.getAvailableRangeId());
            }
            availableProductList = availableRangeUtils.getAvailableProductDataList(user, availableRangeIdList, prodIdList);
            stopWatch.lap("getAvailableProductDataList");

            if (priceBookFlag) {
                List<IObjectData> availablePriceBookList = availableRangeUtils.getPriceBookListByRangeIds(user, arg.getAccountId(), arg.getPartnerId(), availableRangeIdList, businessDate,
                        objectData, arg.getDetails(), PriceBookConstants.PriceBookRangeType.DETAIL.getRangeType(), arg.isActCurrentDetailRow(), saleContractObjectData);
                filterPriceBookByCurrency(user, availablePriceBookList, arg.getMcCurrency());
                getPriceBookNameMap(availablePriceBookList);
                stopWatch.lap("getPriceBookListByRangeIds");
                priceBookIdList.addAll(availablePriceBookList.stream().map(x -> x.get(AvailableConstants.PriceBookField.PRICE_BOOK_ID, String.class)).distinct().collect(Collectors.toList()));
                getProductByPriceBookList(availablePriceBookList, prodIdList, actualUnitIdList, businessDate, arg.getOnlySimpleSearch(), saleContractObjectData);
                stopWatch.lap("findPriceBookProductList");
            } else {
                baseProductList.addAll(productInfoList);
            }
        } else {
            if (priceBookFlag) {
                List<IObjectData> priceBookList;
                SearchTemplateQuery priceBookRangeQuery = availableRangeUtils.buildPriceBookRangeSearchQuery(user, objectData, arg.getDetails(), PriceBookConstants.PriceBookRangeType.DETAIL.getRangeType(), arg.isActCurrentDetailRow());
                if (GrayUtil.isPriceBookReform(user.getTenantId())) {
                    priceBookList = priceBookCommonService.getPriceBookList(user, arg.getAccountId(), arg.getPartnerId(), businessDate, priceBookRangeQuery, saleContractObjectData);
                } else {
                    priceBookList = availableRangeUtils.getAllPriceBookList(user, businessDate, priceBookRangeQuery, saleContractObjectData);
                }
                getPriceBookNameMap(priceBookList);
                stopWatch.lap("getAllPriceBookList");
                priceBookIdList.addAll(priceBookList.stream().parallel().map(DBRecord::getId).distinct().collect(Collectors.toList()));
                getProductByPriceBookList(priceBookList, prodIdList, actualUnitIdList, businessDate, arg.getOnlySimpleSearch(), saleContractObjectData);
                stopWatch.lap("getProductByPriceBookList");
            } else {
                baseProductList.addAll(productInfoList);
            }
        }

        if (CollectionUtils.notEmpty(arg.getPriceBookIds()) && !GrayUtil.BomAttrPrice(user.getTenantId())) {
            priceBookIdList.retainAll(arg.getPriceBookIds());
        }
        //获取属性价目表明细
        getAttrPriceBookLinesList(priceBookIdList, prodIdList, productList, stopWatch);
        stopWatch.logSlow(300);
    }

    public final RealPriceModel.Result act() {
        StopWatch stopWatch = StopWatch.create("BaseRealPriceService act");
        RealPriceModel.Result rst = new RealPriceModel.Result();
        rst.setRst(Maps.newHashMap());
        rst.setNewRst(Lists.newArrayList());
        List<RealPriceModel.FullProduct> productList = getSortedProductList();
        if (CollectionUtils.empty(productList)) {
            return rst;
        }
        init(productList);
        stopWatch.lap("init");
        List<IObjectData> multiUnitDataList = new ArrayList<>();
        if (multiUnitService.isOpenMultiUnit(user.getTenantId())) {
            multiUnitDataList = getMultiUnitDataList();
        }
        List<IObjectData> tmpRst = Lists.newArrayList();
        boolean isEnforcePriority = bizConfigThreadLocalCacheService.isEnforcePriority(user.getTenantId());
        boolean isCurrencyEnabled = bizConfigThreadLocalCacheService.isCurrencyEnabled(user.getTenantId());
        if (isCurrencyEnabled && StringUtils.isNotEmpty(arg.getMcCurrency())) {
            exchangeRateMap = MtCurrentUtil.getExchangeRateMap(user, arg.getMcCurrency());
        }
        boolean isOpenStratifiedPrice = bizConfigThreadLocalCacheService.isOpenStratifiedPricing(user.getTenantId());
        boolean isOpenTieredPrice = bizConfigThreadLocalCacheService.isOpenPriceBookProductTieredPrice(user.getTenantId());
        boolean isOpenIncrementalPricing = bizConfigThreadLocalCacheService.isOpenIncrementalPricing(user.getTenantId());
        Map<String, List<IObjectData>> productIdDataMap = Maps.newHashMap();
        for (IObjectData objectData : multiUnitDataList) {
            productIdDataMap.computeIfAbsent(objectData.get(MultiUnitRelatedConstants.PRODUCT_ID, String.class), k -> Lists.newArrayList()).add(objectData);
        }
        for (RealPriceModel.FullProduct prod : productList) {
            List<IObjectData> multiUnitRelateProductList = productIdDataMap.getOrDefault(prod.getProductId(), Lists.newArrayList());

            //单条数据拼装
            IObjectData d = getHighPriorityData(prod, arg.getPriceBookProductIds(), multiUnitRelateProductList, standardPriceBookId, isEnforcePriority, isOpenIncrementalPricing, arg.getMcCurrency());
            //适配多单位
            if (d == null) continue;
            if (CollectionUtils.notEmpty(multiUnitRelateProductList)) {
                matchMultiUnit(multiUnitRelateProductList, prod, d);
            }
            processSameTieredPrice(prod, d, isOpenStratifiedPrice, isOpenTieredPrice);
            d.set("param_priceBookId", prod.getPriceBookId());
            d.set("param_price", prod.getPrice());
            if (!Objects.isNull(prod.getUnit())) {
                d.set("param_unit", prod.getUnit());
            }
            if (!Objects.isNull(prod.getRowId())) {
                d.set("rowId", prod.getRowId());
            }
            if (!Objects.isNull(prod.getAmount())) {
                d.set("param_amount", prod.getAmount());
            }
            tmpRst.add(d);
        }
        stopWatch.lap("getHighPriorityData");
        if (GrayUtil.finalFillRefObject(user.getTenantId())) {
            if (!Boolean.TRUE.equals(arg.getFromSpuList()) || !GrayUtil.isSpuRelatedListOptimize(user.getTenantId())) {
                fillObjectDataWithRefObject(user, tmpRst);
                stopWatch.lap("fillObjectDataWithRefObject");
            }
        }
        for (IObjectData data : tmpRst) {
            if (!SELLING_DATA_DEFINE_TYPE.equals(data.get("selling_data_define_type"))) continue;
            //依赖其他数据拼装该条数据写在这里
            fillData(data);
            //仅仅为兼容老版本,后续考虑去掉
            ObjectDataDocument d = ObjectDataDocument.of(data);
            rst.getRst().put(data.get("product_id", String.class), d);
            rst.getNewRst().add(d);
        }
        stopWatch.lap("fillData");
        stopWatch.logSlow(300);
        return rst;
    }

    protected void fillData(IObjectData data) {

    }

    private void fillObjectDataWithRefObject(User user, List<IObjectData> dataList) {
        IObjectDescribe describe = SERVICE_FACADE.findObject(user.getTenantId(), Utils.PRICE_BOOK_PRODUCT_API_NAME);
        SERVICE_FACADE.fillObjectDataWithRefObject(describe, dataList, user);
    }

    //适配多单位
    private void matchMultiUnit(List<IObjectData> multiUnitRelateProductList, RealPriceModel.FullProduct prod, IObjectData data) {
        if (StringUtils.isNotEmpty(prod.getUnit())) {
            if (CollectionUtils.notEmpty(multiUnitRelateProductList)) {
                BigDecimal conversionRatio = multiUnitRelateProductList.stream().filter(x ->
                                prod.getUnit().equals(x.get(MultiUnitRelatedConstants.UNIT_ID)))
                        .map(x -> x.get(MultiUnitRelatedConstants.CONVERSION_RATIO, BigDecimal.class)).findFirst().orElse(BigDecimal.ONE);
                BigDecimal isPriceConversionRatio = BigDecimal.ONE;
                if (bizConfigThreadLocalCacheService.isPriceBookEnabled(user.getTenantId())) {
                    Optional<IObjectData> isPriceRelateData = multiUnitRelateProductList.stream()
                            .filter(o -> Objects.equals(o.get("is_pricing", Boolean.class), Boolean.TRUE))
                            .collect(Collectors.toList()).stream().findFirst();
                    if (isPriceRelateData.isPresent()) {
                        isPriceConversionRatio = new BigDecimal(isPriceRelateData.get().get("conversion_ratio", String.class));
                    }
                }
                if (bizConfigThreadLocalCacheService.isOpenMultiUnitPriceBook(user.getTenantId())) {
                    if (!Objects.isNull(prod.getUnit()) && prod.getUnit().equals(data.get("actual_unit"))) {
                        return;
                    }
                }
                ConvertPrice("selling_price", data, conversionRatio, isPriceConversionRatio);
                ConvertPrice("pricebook_sellingprice", data, conversionRatio, isPriceConversionRatio);
                ConvertPrice("pricebook_price", data, conversionRatio, isPriceConversionRatio);
                ConvertPrice("ceiling_price", data, conversionRatio, isPriceConversionRatio);
                ConvertPrice("floor_price", data, conversionRatio, isPriceConversionRatio);
                ConvertPrice("price_book_subtotal", data, conversionRatio, isPriceConversionRatio);
            }
        }
    }

    private void ConvertPrice(String fieldName, IObjectData data, BigDecimal conversionRatio, BigDecimal isPriceConversionRatio) {
        RoundingMode roundingMode = RoundingMode.HALF_UP;
        if (GrayUtil.isMultiUnitDecimal(user.getTenantId())) {
            roundingMode = RoundingMode.UP;
        }
        BigDecimal price = data.get(fieldName, BigDecimal.class);
        if (price != null) {
            BigDecimal buyPrice = price.multiply(conversionRatio).divide(isPriceConversionRatio, 16, roundingMode);
            data.set(fieldName, buyPrice);
        }
    }

    protected List<IObjectData> getMultiUnitDataList() {
        List<String> productIdList = arg.getNeedMultiUnitProductIdList();
        if (CollectionUtils.empty(productIdList)) return Lists.newArrayList();
        return multiUnitService.getMultiUnitDataByProductIds(user, productIdList);
    }

    protected List<RealPriceModel.FullProduct> getSortedProductList() {
        return arg.getProductList();
    }

    @Nullable
    protected IObjectData getHighPriorityData(RealPriceModel.FullProduct fullProduct, List<String> priceBookProductIds,
                                              List<IObjectData> multiUnitRelateProductList, String standardPriceBookId,
                                              boolean isEnforcePriority, boolean isOpenIncrementalPricing, String mcCurrency) {
        List<IObjectData> effectivePriceBookProductList = availableRangeUtils.getEffectivePriceBookProductList(availableRangeFlag, priceBookFlag, fullProduct.getProductId()
                , baseProductList, availableProductList, standardPriceBookProductList, false, user, fullProduct.getAmount());
        if (CollectionUtils.empty(effectivePriceBookProductList)) return null;
        IObjectData sortedData = null;
        contractConstraintFillPriceBookId(user, fullProduct);
        if (bizConfigThreadLocalCacheService.isOpenMultiUnitPriceBook(user.getTenantId())) {
            sortedData = fillFullProductPricebookId(fullProduct, effectivePriceBookProductList, priceBookProductIds);
        }

        if (!Strings.isNullOrEmpty(fullProduct.getPriceBookId())) {
            if (bizConfigThreadLocalCacheService.isOpenMultiUnitPriceBook(user.getTenantId())) {
                sortedData = findEffectiveDataForMultiUnitPriceBook(multiUnitRelateProductList, fullProduct
                        , effectivePriceBookProductList, standardPriceBookId);
            } else {
                if (!isEnforcePriority) {
                    List<IObjectData> sortedDataList = effectivePriceBookProductList.stream()
                            .filter(x -> fullProduct.getPriceBookId().equals(x.get("pricebook_id", String.class))).collect(Collectors.toList());
                    if (bizConfigThreadLocalCacheService.isOpenStratifiedOrTieredPrice(user.getTenantId())
                            && CollectionUtils.notEmpty(sortedDataList)
                            && sortedDataList.size() > 1) {
                        sortedData = availableRangeUtils.findMinTieredPriceData(sortedDataList).stream().findFirst().orElse(null);
                    } else {
                        sortedData = sortedDataList.stream().findFirst().orElse(null);
                    }
                }
            }
        }
        if (Objects.isNull(sortedData)) {
            if (!CollectionUtils.empty(priceBookProductIds)) {
                effectivePriceBookProductList = effectivePriceBookProductList.stream().filter(d -> priceBookProductIds.contains(d.getId())).collect(Collectors.toList());
            }
            if (CollectionUtils.empty(effectivePriceBookProductList)) {
                return null;
            }
            sortedData = sortedByPriorityAndLastModiTime(effectivePriceBookProductList);
        }
        saveDefaultHighPriorityData(fullProduct, sortedData);
        IObjectData rst = ObjectDataExt.of(sortedData).copy();
        //分层定价
        stratifiedPriceHandler(fullProduct, rst);
        rst.set("selling_price", rst.get("pricebook_sellingprice") == null ? rst.get("price") : rst.get("pricebook_sellingprice"));
        rst.set("selling_data_define_type", SELLING_DATA_DEFINE_TYPE);
        String dataMcCurrency = rst.get("mc_currency", String.class);
        if (!Strings.isNullOrEmpty(mcCurrency) && StringUtils.isNotBlank(dataMcCurrency)) {
            processCurrencyConvert(rst, mcCurrency, dataMcCurrency);
        }
        //属性适配
        fillAttrPriceBookLines(fullProduct, rst, isOpenIncrementalPricing);
        saveDefaultHighPriorityData(fullProduct, rst);
        return rst;
    }

    //保存默认最优价格
    protected void saveDefaultHighPriorityData(RealPriceModel.FullProduct fullProduct, IObjectData sortedData) {

    }

    private void fillAttrPriceBookLines(RealPriceModel.FullProduct fullProduct, IObjectData data, boolean isOpenIncrementalPricing) {
        if (!fullProduct.hasAttr()) {
            return;
        }
        data.set("attr_group", fullProduct.getAttrMap());
        String priceBookId = data.get("pricebook_id", String.class);
        String productId = fullProduct.getProductId();
        IObjectData productInfo = productInfoList.stream().filter(x -> productId.equals(x.getId())).findFirst().orElse(null);
        //进入计价的属性列表
        List<String> pricingAttributeIds = Lists.newArrayList();
        if (productInfo != null) {
            pricingAttributeIds = availableRangeUtils.castList(productInfo.get("pricing_attribute_ids"), String.class);
        }
        if (CollectionUtils.empty(pricingAttributeIds)) {
            return;
        }
        //产品属性参数只保留定价属性
        Map<String, String> currAttr = processCurrAttr(fullProduct, pricingAttributeIds);
        if (CollectionUtils.empty(currAttr)) {
            return;
        }
        //匹配全量属性价目表明细
        boolean isMatchFullPricing = matchAttrPriceBookLines(productAttrPriceBookLinesMap, productId, priceBookId,
                currAttr, data, true, null);
        //不开属性增量定价直接返回，开了，如果匹配到全量则返回，优先级：全量属性价目表>增量属性价目表
        if (!isOpenIncrementalPricing || isMatchFullPricing) {
            return;
        }
        //单个属性的增量属性价目表明细
        List<IObjectData> singleAttrPriceBookLines = Lists.newArrayList();
        //匹配增量属性价目表明细
        boolean isMatchIncrementPricing = matchAttrPriceBookLines(productIncrementAttrPriceBookLinesMap, productId,
                priceBookId, currAttr, data, false, singleAttrPriceBookLines);
        if (isMatchIncrementPricing) {
            return;
        }
        //增量属性价目表全属性如果没有匹配到，则拆分单个属性进行匹配
        matchSingleAttrPriceBookLines(singleAttrPriceBookLines, currAttr, data);
    }

    /**
     * 全属性匹配属性价目表明细，回填属性价目表及价格等
     *
     * @param productAttrPriceBookLinesMap
     * @param productId
     * @param priceBookId
     * @param currAttr
     * @param data
     * @param isFullPricing
     * @param singleAttrPriceBookLines
     * @return
     */
    private boolean matchAttrPriceBookLines(Map<String, List<IObjectData>> productAttrPriceBookLinesMap, String productId,
                                            String priceBookId, Map<String, String> currAttr, IObjectData data,
                                            boolean isFullPricing, List<IObjectData> singleAttrPriceBookLines) {
        boolean isMatchAttrPriceBookLines = false;
        List<IObjectData> attrPriceBookLinesList = productAttrPriceBookLinesMap.get(productId);
        if (CollectionUtils.empty(attrPriceBookLinesList)) {
            return false;
        }
        for (IObjectData attrPriceBookLinesData : attrPriceBookLinesList) {
            List<String> priceBookIds = attrPriceBookLinesData.get(AttributePriceBookConstants.AttributeApplicablePriceBookField.PRICE_BOOK_IDS, List.class);
            Map<String, List<String>> priceBookAttr = attrPriceBookLinesData.get(AttributePriceBookLinesField.ATTRIBUTE_GROUP, Map.class);
            if (!priceBookIds.contains(priceBookId) || CollectionUtils.empty(priceBookAttr)) {
                continue;
            }
            if (!isFullPricing && priceBookAttr.size() == 1) {
                singleAttrPriceBookLines.add(attrPriceBookLinesData);
            }
            if (!compareAttr(currAttr, priceBookAttr)) {
                continue;
            }
            data.set(AttributePriceBookLinesField.ATTRIBUTE_PRICE_BOOK_ID, attrPriceBookLinesData.get(AttributePriceBookLinesField.ATTRIBUTE_PRICE_BOOK_ID));
            data.set(AttributePriceBookLinesField.ATTRIBUTE_PRICE_BOOK_ID_R, attrPriceBookLinesData.get(AttributePriceBookLinesField.ATTRIBUTE_PRICE_BOOK_ID_R));
            data.set(AttributePriceBookLinesField.ATTRIBUTE_PRICE_BOOK_LINES_IDS, Lists.newArrayList(attrPriceBookLinesData.getId()));
            if (isFullPricing) {
                data.set(AttributePriceBookLinesField.SELLING_PRICE, attrPriceBookLinesData.get(AttributePriceBookLinesField.SELLING_PRICE));
            } else {
                data.set(AttributePriceBookLinesField.SELLING_PRICE, data.get(AttributePriceBookLinesField.SELLING_PRICE, BigDecimal.class, BigDecimal.ZERO)
                        .add(attrPriceBookLinesData.get(AttributePriceBookLinesField.SELLING_PRICE, BigDecimal.class, BigDecimal.ZERO)));
            }
            String pricingMethod = attrPriceBookLinesData.get(AttributePriceBookLinesField.PRICING_METHOD, String.class);
            if (AttributePriceBookConstants.PricingMethodEnum.SPECIFIED_DISCOUNT.getValue().equals(pricingMethod)) {
                //指定折扣
                data.set(AttributePriceBookLinesField.DISCOUNT, attrPriceBookLinesData.get(AttributePriceBookLinesField.DISCOUNT));
            }
            isMatchAttrPriceBookLines = true;
            break;
        }
        return isMatchAttrPriceBookLines;
    }

    /**
     * 拆单个属性匹配增量属性价目表明细，回填属性价目表及价格等
     *
     * @param attrPriceBookLinesList
     * @param currAttr
     * @param data
     */
    private void matchSingleAttrPriceBookLines(List<IObjectData> attrPriceBookLinesList, Map<String, String> currAttr, IObjectData data) {
        if (CollectionUtils.empty(attrPriceBookLinesList)) {
            return;
        }
        BigDecimal incrementPriceSum = BigDecimal.ZERO;
        boolean isMatchAttrPriceBookLines = false;
        String attrPriceBookId = "";
        String attrPriceBookIdR = "";
        List<String> attributePriceBookLinesIds = Lists.newArrayList();
        for (IObjectData attrPriceBookLinesData : attrPriceBookLinesList) {
            Map<String, List<String>> priceBookAttr = attrPriceBookLinesData.get(AttributePriceBookLinesField.ATTRIBUTE_GROUP, Map.class);
            if (CollectionUtils.empty(priceBookAttr)) {
                continue;
            }
            if (StringUtils.isEmpty(attrPriceBookId)) {
                attrPriceBookId = attrPriceBookLinesData.get(AttributePriceBookLinesField.ATTRIBUTE_PRICE_BOOK_ID, String.class);
                attrPriceBookIdR = attrPriceBookLinesData.get(AttributePriceBookLinesField.ATTRIBUTE_PRICE_BOOK_ID_R, String.class);
            }
            Map.Entry<String, List<String>> entry = priceBookAttr.entrySet().iterator().next();
            if (attrPriceBookId.equals(attrPriceBookLinesData.get(AttributePriceBookLinesField.ATTRIBUTE_PRICE_BOOK_ID, String.class))
                    && currAttr.containsKey(entry.getKey()) && entry.getValue().contains(currAttr.get(entry.getKey()))) {
                isMatchAttrPriceBookLines = true;
                attributePriceBookLinesIds.add(attrPriceBookLinesData.getId());
                incrementPriceSum = incrementPriceSum.add(attrPriceBookLinesData.get(AttributePriceBookLinesField.SELLING_PRICE, BigDecimal.class, BigDecimal.ZERO));
            }
        }
        if (isMatchAttrPriceBookLines) {
            data.set(AttributePriceBookLinesField.ATTRIBUTE_PRICE_BOOK_ID, attrPriceBookId);
            data.set(AttributePriceBookLinesField.ATTRIBUTE_PRICE_BOOK_ID_R, attrPriceBookIdR);
            data.set(AttributePriceBookLinesField.ATTRIBUTE_PRICE_BOOK_LINES_IDS, attributePriceBookLinesIds);
            data.set(AttributePriceBookLinesField.SELLING_PRICE, data.get(AttributePriceBookLinesField.SELLING_PRICE, BigDecimal.class, BigDecimal.ZERO).add(incrementPriceSum));
        }
    }

    //获取优先级最高的价目表产品数据
    private IObjectData sortedByPriorityAndLastModiTime(List<IObjectData> effectivePriceBookProductList) {
        if (bizConfigThreadLocalCacheService.isOpenStratifiedOrTieredPrice(user.getTenantId())) {
            //如果开启阶梯价，如果没传数量，需要去最小阶梯价的价格
            effectivePriceBookProductList = availableRangeUtils.findMinTieredPriceData(effectivePriceBookProductList);
        }
        //优先以priority正序，同priority以last_modified_time倒序
        return effectivePriceBookProductList.stream().min((x, y) -> {
            Integer firstPriority = x.get("price_book_priority", Integer.class);
            Integer secondPriority = y.get("price_book_priority", Integer.class);
            if (Objects.equals(firstPriority, secondPriority)) {
                return y.getLastModifiedTime().compareTo(x.getLastModifiedTime());
            }
            return firstPriority.compareTo(secondPriority);
        }).orElse(null);
    }

    //返回优先级最高的一组数据
    private List<IObjectData> getTopPriorityPriceBookProducts(List<IObjectData> priceBookProducts) {
        Comparator<IObjectData> byPriority = Comparator.comparing(x -> x.get("price_book_priority", Integer.class), Comparator.nullsLast(Integer::compareTo));
        priceBookProducts.sort(byPriority);

        List<IObjectData> topPriorityPriceBookProducts = Lists.newArrayList(priceBookProducts.get(0));
        Integer topPriority = priceBookProducts.get(0).get("price_book_priority", Integer.class);

        for (int i = 1; i < priceBookProducts.size(); i++) {
            Integer priority = priceBookProducts.get(i).get("price_book_priority", Integer.class);
            if (Objects.equals(topPriority, priority)) {
                topPriorityPriceBookProducts.add(priceBookProducts.get(i));
            } else {
                return topPriorityPriceBookProducts;
            }
        }

        return topPriorityPriceBookProducts;
    }

    /**
     * 验证属性是否匹配属性价目表
     *
     * @param currAttr      当前所选的产品的属性集合
     * @param priceBookAttr 已适配该产品的单条属性价目表明细内的属性集合
     * @return 是否匹配
     */
    private boolean compareAttr(Map<String, String> currAttr, Map<String, List<String>> priceBookAttr) {
        if (priceBookAttr.size() != currAttr.size()) {
            return false;
        }
        for (Map.Entry<String, String> entry : currAttr.entrySet()) {
            if (priceBookAttr.containsKey(entry.getKey())) {
                if (!priceBookAttr.get(entry.getKey()).contains(entry.getValue())) {
                    return false;
                }
            } else {
                return false;
            }
        }
        return true;
    }

    private Map<String, String> processCurrAttr(RealPriceModel.FullProduct fullProduct, List<String> pricingAttributeIds) {
        Map<String, String> currAttr = Maps.newHashMap();
        for (Map.Entry<String, String> entry : fullProduct.getAttrMap().entrySet()) {
            if (pricingAttributeIds.contains(entry.getKey())) {
                currAttr.put(entry.getKey(), entry.getValue());
            }
        }
        return currAttr;
    }

    public final List<IObjectData> findStandardProductList(List<String> productIdList) {
        //未考虑上下架
        List<IObjectData> tmp = GrayUtil.skipEditFunction(user.getTenantId())
                ? SERVICE_FACADE.findObjectDataByIdsIgnoreAll(user.getTenantId(), productIdList, Utils.PRODUCT_API_NAME)
                : SERVICE_FACADE.findObjectDataByIdsIgnoreFormula(user.getTenantId(), productIdList, Utils.PRODUCT_API_NAME);
        List<IObjectData> rst = Lists.newArrayList();
        for (IObjectData data : tmp) {
            ObjectDataExt objectDataExt = ObjectDataExt.of(data);
            if (!objectDataExt.getLifeStatus().equals(ObjectLifeStatus.INVALID)) {
                //特殊实体必需字段
                data.set("product_id", data.getId());
                data.set(PriceBookConstants.Field.PRIORITY.getApiName(), "0");
                rst.add(data);
            }
        }
        return rst;
    }

    private void processSameTieredPrice(RealPriceModel.FullProduct prod, IObjectData objectData, boolean isOpenStratifiedPrice, boolean isOpenTieredPrice) {
        // 如果分层定价和阶梯定价都未开启，或者objectData为空，则直接返回
        if (objectData == null || (!isOpenStratifiedPrice && !isOpenTieredPrice)) {
            return;
        }
        // 检查价目表明细ID是否一样
        String priceBookProductId = prod.getPriceBookProductId();
        if (StringUtils.isEmpty(priceBookProductId) || !priceBookProductId.equals(objectData.getId())) {
            return;
        }
        // 获取是否为分层定价
        boolean isStratifiedPrice = objectData.get(PriceBookConstants.ProductField.IS_STRATIFIED_PRICING.getApiName(), Boolean.class, Boolean.FALSE);
        // 设置折扣值（无论是否为分层定价）
        if (StringUtils.isNotEmpty(prod.getDiscount())) {
            objectData.set("detail_discount", prod.getDiscount());
        }
        // 非分层定价的处理
        if (!isStratifiedPrice) {
            // 设置价格
            if (prod.getPrice() != null) {
                objectData.set("selling_price", prod.getPrice());
                // 分层定价开启时的额外处理
                if (isOpenStratifiedPrice) {
                    BigDecimal amount = prod.getAmount() == null ? BigDecimal.ONE : prod.getAmount();
                    BigDecimal discount = objectData.get("discount", BigDecimal.class, new BigDecimal(100));
                    BigDecimal priceBookPrice = prod.getPrice().multiply(discount).divide(new BigDecimal(100));
                    objectData.set(PriceBookConstants.ProductField.PRICEBOOKPRICE.getApiName(), priceBookPrice);
                    objectData.set(PriceBookConstants.ProductField.PRICE_BOOK_SUBTOTAL.getApiName(), amount.multiply(priceBookPrice));
                }
            }
        }
    }

    private IObjectData fillFullProductPricebookId(RealPriceModel.FullProduct fullProduct
            , List<IObjectData> effectivePriceBookProductList, List<String> priceBookProductIds) {
        IObjectData sortedData = null;
        if (Strings.isNullOrEmpty(fullProduct.getPriceBookId())) {
            List<IObjectData> list = new ArrayList<>();
            if (!CollectionUtils.empty(priceBookProductIds)) {
                list = effectivePriceBookProductList.stream().filter(d -> priceBookProductIds.contains(d.getId())).collect(Collectors.toList());
            }
            if (CollectionUtils.empty(list)) {
                list = effectivePriceBookProductList;
            }
            sortedData = sortedByPriorityAndLastModiTime(list);
            if (!Objects.isNull(sortedData)) {
                fullProduct.setPriceBookId(sortedData.get("pricebook_id", String.class));
            }
        }
        return sortedData;
    }

    private IObjectData findEffectiveDataForMultiUnitPriceBook(List<IObjectData> multiUnitRelateProductList
            , RealPriceModel.FullProduct fullProduct
            , List<IObjectData> effectivePriceBookProductList, String standardPriceBookId) {
        IObjectData sortedData = null;
        String pricingUnit = StringUtils.EMPTY;
        if (CollectionUtils.notEmpty(multiUnitRelateProductList)) {
            Optional<IObjectData> isPriceRelateData = multiUnitRelateProductList.stream()
                    .filter(o -> Objects.equals(o.get("is_pricing", Boolean.class), Boolean.TRUE))
                    .collect(Collectors.toList()).stream().findFirst();
            if (isPriceRelateData.isPresent()) {
                pricingUnit = isPriceRelateData.get().get("unit_id", String.class);
            }
        }
        if (!Strings.isNullOrEmpty(fullProduct.getUnit())) {
            sortedData = effectivePriceBookProductList.stream()
                    .filter(x -> fullProduct.getPriceBookId().equals(x.get("pricebook_id", String.class))
                            && fullProduct.getUnit().equals(x.get("actual_unit", String.class)))
                    .findFirst().orElse(null);
        }

        if (Objects.isNull(sortedData)) {
            final String finalPricingUnit = pricingUnit;
            sortedData = effectivePriceBookProductList.stream()
                    .filter(x -> fullProduct.getPriceBookId().equals(x.get("pricebook_id", String.class))
                            && finalPricingUnit.equals(x.get("actual_unit", String.class)))
                    .findFirst().orElse(null);
        }
        if (Objects.isNull(sortedData)) {
            if (!Strings.isNullOrEmpty(standardPriceBookId)) {
                sortedData = effectivePriceBookProductList.stream()
                        .filter(x -> standardPriceBookId.equals(x.get("pricebook_id", String.class)))
                        .findFirst().orElse(null);
            }
        }
        if (Objects.isNull(sortedData)) {
            if (!Strings.isNullOrEmpty(standardPriceBookId)) {
                //List<IObjectData> list = priceBookCommonService.getPriceBookProducts(user, standardPriceBookId, Lists.newArrayList(fullProduct.getProductId()));
                if (CollectionUtils.notEmpty(standardPriceBookProductList)) {
                    List<IObjectData> list = standardPriceBookProductList.stream()
                            .filter(x -> fullProduct.getProductId().equals(x.get("product_id", String.class)))
                            .collect(Collectors.toList());
                    if (CollectionUtils.notEmpty(list)) {
                        sortedData = list.get(0);
                        sortedData.set("pricebook_id__r", "标准价目表"); // ignoreI18n  异常走不到
                    }
                }
            }
        }
        return sortedData;
    }

    private void stratifiedPriceHandler(RealPriceModel.FullProduct fullProduct, IObjectData sortedData) {
        if (!bizConfigThreadLocalCacheService.isOpenStratifiedPricing(user.getTenantId()) || Objects.isNull(sortedData)) {
            return;
        }
        List<IObjectData> stratifiedPriceBookProductList = Lists.newArrayList(sortedData);
        boolean isStratifiedPrice = sortedData.get(PriceBookConstants.ProductField.IS_STRATIFIED_PRICING.getApiName(), Boolean.class, Boolean.FALSE);
        //获取所有分层数据
        collectStratifiedPriceData(isStratifiedPrice, stratifiedPriceBookProductList, sortedData, fullProduct.getProductId());
        //分层排序
        sortStratifiedPriceData(isStratifiedPrice, stratifiedPriceBookProductList);
        //计算分层价格
        calculateStratifiedPrice(isStratifiedPrice, fullProduct, sortedData, stratifiedPriceBookProductList);
    }

    private void collectStratifiedPriceData(boolean isStratifiedPrice, List<IObjectData> stratifiedPriceBookProductList, IObjectData sortedData, String productId) {
        // 如果不是分层定价或起始数量为0，则直接返回
        if (!isStratifiedPrice) {
            return;
        }
        BigDecimal startCount = sortedData.get(PriceBookConstants.ProductField.START_COUNT.getApiName(), BigDecimal.class, BigDecimal.ZERO);
        if (startCount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        String priceBookId = sortedData.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName(), String.class);
        String actualUnit = sortedData.get(PriceBookConstants.ProductField.ACTUAL_UNIT.getApiName(), String.class);
        boolean isMultiUnitPriceBook = bizConfigThreadLocalCacheService.isOpenMultiUnitPriceBook(user.getTenantId());
        baseProductList.stream()
            .filter(data -> !Objects.equals(sortedData.getId(), data.getId()))
            .filter(data -> Objects.equals(priceBookId, data.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName(), String.class)))
            .filter(data -> Objects.equals(productId, data.get(PriceBookConstants.ProductField.PRODUCTID.getApiName(), String.class)))
            .filter(data -> !isMultiUnitPriceBook || Objects.equals(actualUnit, data.get(PriceBookConstants.ProductField.ACTUAL_UNIT.getApiName(), String.class)))
            .filter(data -> startCount.compareTo(data.get(PriceBookConstants.ProductField.START_COUNT.getApiName(), BigDecimal.class, BigDecimal.ZERO)) >= 0)
            .forEach(stratifiedPriceBookProductList::add);
    }

    private void sortStratifiedPriceData(boolean isStratifiedPrice, List<IObjectData> stratifiedPriceBookProductList) {
        if (!isStratifiedPrice) {
            return;
        }
        stratifiedPriceBookProductList.sort((a, b) -> {
            BigDecimal aStartCount = a.get(PriceBookConstants.ProductField.START_COUNT.getApiName(), BigDecimal.class, BigDecimal.ZERO);
            BigDecimal bStartCount = b.get(PriceBookConstants.ProductField.START_COUNT.getApiName(), BigDecimal.class, BigDecimal.ZERO);
            return aStartCount.compareTo(bStartCount);
        });
    }

    private void calculateStratifiedPrice(boolean isStratifiedPrice, RealPriceModel.FullProduct fullProduct, IObjectData sortedData, List<IObjectData> stratifiedPriceBookProductList) {
        String priceBookId = sortedData.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName(), String.class);
        BigDecimal priceBookSubtotal = BigDecimal.ZERO;
        BigDecimal priceBookDiscount = new BigDecimal(100);
        BigDecimal amount = fullProduct.getAmount() == null ? BigDecimal.ONE : fullProduct.getAmount();
        if (isStratifiedPrice) {
            PriceTieredRecord priceTieredRecord = new PriceTieredRecord();
            for (int i = 0; i < stratifiedPriceBookProductList.size(); i++) {
                IObjectData data = stratifiedPriceBookProductList.get(i);
                priceTieredRecord.getIds().add(data.getId());
                priceTieredRecord.getStartCount().add(data.get(PriceBookConstants.ProductField.START_COUNT.getApiName(), String.class, ""));
                priceTieredRecord.getEndCount().add(data.get(PriceBookConstants.ProductField.END_COUNT.getApiName(), String.class, ""));
                priceTieredRecord.getPriceBookSellingPrice().add(data.get(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName(), String.class, ""));
                priceTieredRecord.getDiscount().add(data.get(PriceBookConstants.ProductField.DISCOUNT.getApiName(), String.class, ""));
                priceTieredRecord.getPriceBookPrice().add(data.get(PriceBookConstants.ProductField.PRICEBOOKPRICE.getApiName(), String.class, ""));
                BigDecimal calAmount;
                priceBookDiscount = data.get(PriceBookConstants.ProductField.DISCOUNT.getApiName(), BigDecimal.class, new BigDecimal(100));
                if (i != stratifiedPriceBookProductList.size() - 1) {
                    calAmount = data.get(PriceBookConstants.ProductField.END_COUNT.getApiName(), BigDecimal.class, BigDecimal.ZERO).subtract(data.get(PriceBookConstants.ProductField.START_COUNT.getApiName(), BigDecimal.class, BigDecimal.ZERO));
                } else {
                    calAmount = amount.subtract(data.get(PriceBookConstants.ProductField.START_COUNT.getApiName(), BigDecimal.class, BigDecimal.ZERO));
                }
                priceBookSubtotal = priceBookSubtotal.add(calAmount.multiply(data.get(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName(), BigDecimal.class, BigDecimal.ZERO)).multiply(data.get(PriceBookConstants.ProductField.DISCOUNT.getApiName(), BigDecimal.class, new BigDecimal(100))).divide(new BigDecimal(100)));
            }
            BigDecimal priceBookPrice = priceBookSubtotal.divide(amount, 16, RoundingMode.HALF_UP);
            BigDecimal price = priceBookSubtotal.divide(priceBookDiscount, 16, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).divide(amount, 16, RoundingMode.HALF_UP);
            sortedData.set(PriceBookConstants.ProductField.PRICE_BOOK_SUBTOTAL.getApiName(), priceBookSubtotal);
            sortedData.set(PriceBookConstants.ProductField.PRICEBOOKPRICE.getApiName(), priceBookPrice);
            sortedData.set(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName(), price);
            priceTieredRecord.setId(priceBookId);
            priceTieredRecord.setName(priceBookNameMap.get(priceBookId));
            sortedData.set("price_tiered_record", priceTieredRecord);
        } else {
            // 非分层，直接计算价目表小计
            IObjectData data = stratifiedPriceBookProductList.get(0);
            priceBookSubtotal = data.get(PriceBookConstants.ProductField.PRICEBOOKPRICE.getApiName(), BigDecimal.class, BigDecimal.ZERO).multiply(amount);
            sortedData.set("price_book_subtotal", priceBookSubtotal);
        }
    }

    private void processCurrencyConvert(IObjectData rst, String mcCurrency, String dataMcCurrency) {
        if (!Strings.isNullOrEmpty(rst.get("selling_price", String.class))) {
            rst.set("selling_price", MtCurrentUtil.changePriceToCurrency(mcCurrency, new BigDecimal(rst.get("selling_price", String.class)), dataMcCurrency, exchangeRateMap).toPlainString());
        }
        if (!Strings.isNullOrEmpty(rst.get("pricebook_price", String.class))) {
            rst.set("pricebook_price", MtCurrentUtil.changePriceToCurrency(mcCurrency, new BigDecimal(rst.get("pricebook_price", String.class)), dataMcCurrency, exchangeRateMap).toPlainString());
        }
        if (!Strings.isNullOrEmpty(rst.get("pricebook_sellingprice", String.class))) {
            rst.set("pricebook_sellingprice", MtCurrentUtil.changePriceToCurrency(mcCurrency, new BigDecimal(rst.get("pricebook_sellingprice", String.class)), dataMcCurrency, exchangeRateMap).toPlainString());
        }
        if (!Strings.isNullOrEmpty(rst.get("ceiling_price", String.class))) {
            rst.set("ceiling_price", MtCurrentUtil.changePriceToCurrency(mcCurrency, new BigDecimal(rst.get("ceiling_price", String.class)), dataMcCurrency, exchangeRateMap).toPlainString());
        }
        if (!Strings.isNullOrEmpty(rst.get("floor_price", String.class))) {
            rst.set("floor_price", MtCurrentUtil.changePriceToCurrency(mcCurrency, new BigDecimal(rst.get("floor_price", String.class)), dataMcCurrency, exchangeRateMap).toPlainString());
        }
        if (!Strings.isNullOrEmpty(rst.get("price_book_subtotal", String.class))) {
            rst.set("price_book_subtotal", MtCurrentUtil.changePriceToCurrency(mcCurrency, new BigDecimal(rst.get("price_book_subtotal", String.class)), dataMcCurrency, exchangeRateMap).toPlainString());
        }
    }

    private void getPriceBookNameMap(List<IObjectData> priceBookList) {
        if (CollectionUtils.empty(priceBookList)) {
            return;
        }
        if (bizConfigThreadLocalCacheService.isOpenStratifiedPricing(user.getTenantId())) {
            priceBookNameMap = priceBookList.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName));
        }
    }

    private void getProductByPriceBookList(List<IObjectData> priceBookList, List<String> prodIdList, List<String> actualUnitIdList,
                                           Long businessDate, Boolean onlySimpleSearch, IObjectData saleContractObjectData) {
        List<IObjectData> standardPriceBookList = priceBookList.stream()
                .filter(x -> x.get("is_standard", Boolean.class, false))
                .collect(Collectors.toList());
        Boolean fillRefObject = !GrayUtil.finalFillRefObject(user.getTenantId());
        if (CollectionUtils.empty(standardPriceBookList)) {
            IObjectData standardPriceBook = priceBookCommonService.getStandardPriceBook(user);
            if (standardPriceBook != null && PriceBookConstants.ActiveStatus.ON.getStatus().equals(standardPriceBook.get(PriceBookConstants.Field.ACTIVESTATUS.getApiName()))) {
                standardPriceBookId = standardPriceBook.getId();
                priceBookList.add(standardPriceBook);
                List<IObjectData> allBaseProductList = availableRangeUtils.getProductByPriceBookList(user, prodIdList, priceBookList, actualUnitIdList, businessDate, onlySimpleSearch, fillRefObject, arg.getFromSpuList());
                Map<Boolean, List<IObjectData>> partitionedProducts = allBaseProductList.stream()
                        .collect(Collectors.partitioningBy(x -> x.get(PriceBookProductConstants.PRICEBOOK_ID, String.class).equals(standardPriceBookId)));
                standardPriceBookProductList = partitionedProducts.get(true);
                baseProductList = partitionedProducts.get(false);
            } else {
                log.warn("No standard priceBook,tenantId:{},userId:{}", user.getTenantId(), user.getUpstreamOwnerIdOrUserId());
                baseProductList = availableRangeUtils.getProductByPriceBookList(user, prodIdList, priceBookList, actualUnitIdList, businessDate, onlySimpleSearch, fillRefObject, arg.getFromSpuList());
            }
        } else {
            standardPriceBookId = standardPriceBookList.stream().findFirst().map(IObjectData::getId).orElse(null);
            baseProductList = availableRangeUtils.getProductByPriceBookList(user, prodIdList, priceBookList, actualUnitIdList, businessDate, onlySimpleSearch, fillRefObject, arg.getFromSpuList());
            Map<Boolean, List<IObjectData>> partitionedProducts = baseProductList.stream()
                    .collect(Collectors.partitioningBy(x -> x.get(PriceBookProductConstants.PRICEBOOK_ID, String.class).equals(standardPriceBookId)));
            standardPriceBookProductList = partitionedProducts.get(true);
        }
        //如果合同约束为取价服务约束，则不用标准价目表兜底
        if (availableRangeUtils.isRealPriceConstraintMode(user, saleContractObjectData)) {
            standardPriceBookProductList = Lists.newArrayList();
        }
    }

    private void getAttrPriceBookLinesList(List<String> priceBookIdList, List<String> prodIdList, List<RealPriceModel.FullProduct> productList, StopWatch stopWatch) {
        if (!attributeFlag) {
            return;
        }
        //属性价目表与价目表强关联,必须开通价目表
        List<IObjectData> allAttrPriceBookLinesList = attributePriceBookService.getAttributePriceBookLines(user, priceBookIdList, prodIdList);
        stopWatch.lap("getAttributePriceBookLines");
        if (CollectionUtils.empty(allAttrPriceBookLinesList)) {
            return;
        }
        List<String> attributePriceBookIds = allAttrPriceBookLinesList.stream().map(x -> x.get(AttributePriceBookConstants.AttributePriceBookLinesField.ATTRIBUTE_PRICE_BOOK_ID, String.class)).distinct().collect(Collectors.toList());
        List<IObjectData> attributePriceBookList = SERVICE_FACADE.findObjectDataByIdsIgnoreAll(user.getTenantId(), attributePriceBookIds, SFAPreDefine.AttributePriceBook.getApiName());
        boolean isOpenIncrementalPricing = bizConfigThreadLocalCacheService.isOpenIncrementalPricing(user.getTenantId());
        fillAttrPriceBookLinesData(allAttrPriceBookLinesList, attributePriceBookList, isOpenIncrementalPricing);
        if (isOpenIncrementalPricing) {
            //全量属性价目表明细
            productAttrPriceBookLinesMap = allAttrPriceBookLinesList.stream()
                    .filter(x -> AttributePriceBookConstants.AttributePricingModeEnum.ALL.getValue()
                            .equals(x.get(AttributePriceBookField.PRICING_MODE, String.class)))
                    .collect(Collectors.groupingBy(o -> o.get(AttributePriceBookLinesField.PRODUCT_ID, String.class)));
            //增量属性价目表明细
            productIncrementAttrPriceBookLinesMap = allAttrPriceBookLinesList.stream()
                    .filter(x -> AttributePriceBookConstants.AttributePricingModeEnum.SOME.getValue()
                            .equals(x.get(AttributePriceBookField.PRICING_MODE, String.class)))
                    .collect(Collectors.groupingBy(o -> o.get(AttributePriceBookLinesField.PRODUCT_ID, String.class)));
        } else {
            productAttrPriceBookLinesMap = allAttrPriceBookLinesList.stream()
                    .collect(Collectors.groupingBy(o -> o.get(AttributePriceBookLinesField.PRODUCT_ID, String.class)));
        }
        //拉取属性的槽位等信息
        Set<String> attrIdList = Sets.newHashSet();
        for (RealPriceModel.FullProduct fullProduct : productList) {
            if (fullProduct.hasAttr()) {
                attrIdList.addAll(fullProduct.getAttrMap().keySet());
            }
        }
        attributeInfoList = attributeCoreService.getAttributeByIds(user, attrIdList);
        stopWatch.lap("handleAttribute");
    }

    private void fillAttrPriceBookLinesData(List<IObjectData> allAttrPriceBookLinesList, List<IObjectData> attributePriceBookList, boolean isOpenIncrementalPricing) {
        for (IObjectData data : allAttrPriceBookLinesList) {
            IObjectData attrPriceBook = attributePriceBookList.stream().filter(x -> data.get("attribute_price_book_id").equals(x.getId())).findFirst().orElse(null);
            if (attrPriceBook != null) {
                data.set("attribute_price_book_id__r", attrPriceBook.getName());
                if (isOpenIncrementalPricing) {
                    data.set("pricing_mode", attrPriceBook.get(AttributePriceBookField.PRICING_MODE, String.class));
                }
            }
        }
    }

    private void filterPriceBookByCurrency(User user, List<IObjectData> availablePriceBookList, String currency) {
        if (!Strings.isNullOrEmpty(currency) && GrayUtil.needFilterCurrency(user.getTenantId())) {
            availablePriceBookList.removeIf(r -> !currency.equals(r.get(FieldDescribeExt.CURRENCY_FIELD, String.class)));
        }
    }

    private Map<String, IObjectData> getSaleContractLineInfo(User user, String requestSource, List<RealPriceModel.FullProduct> productList, IObjectData objectData, IObjectData saleContractObjectData) {
        List<String> requestSourceList = Lists.newArrayList(AvailableConstants.RequestSource.COPY.toString().toLowerCase(), AvailableConstants.RequestSource.CONVERT.toString().toLowerCase());
        if (!requestSourceList.contains(requestSource)
                || Objects.isNull(objectData)
                || !SFAPreDefine.SalesOrder.getApiName().equals(objectData.get(SystemConstants.ObjectDescribeApiName))
                || !availableRangeUtils.isDetailConstraintMode(user, saleContractObjectData)) {
            return Maps.newHashMap();
        }
        List<String> saleContractLineIds = productList.stream().map(RealPriceModel.FullProduct::getSaleContractLineId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.empty(saleContractLineIds)) {
            return Maps.newHashMap();
        }
        String saleContractId = objectData.get(SalesOrderConstants.SalesOrderField.SALE_CONTRACT_ID.getApiName(), String.class);
        SearchTemplateQuery query = SoCommonUtils.buildSearchTemplateQuery(saleContractLineIds.size());
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, SalesOrderConstants.SalesOrderField.SALE_CONTRACT_ID.getApiName(), saleContractId);
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, DBRecord.ID, saleContractLineIds);
        List<IObjectData> saleContractLineList = metaDataFindServiceExt.findBySearchQueryWithFields(user, SFAPreDefine.SaleContractLine.getApiName(),
                query, Lists.newArrayList(DBRecord.ID, SaleContractConstants.SaleContractLineField.PRICE_BOOK_ID.getApiName()), true).getData();
        return saleContractLineList.stream().collect(Collectors.toMap(DBRecord::getId, x -> x));
    }

    private void contractConstraintFillPriceBookId(User user, RealPriceModel.FullProduct fullProduct) {
        if (!bizConfigThreadLocalCacheService.isOpenContractConstraintMode(user.getTenantId())
                || CollectionUtils.empty(saleContractLineMap)) {
            return;
        }
        if (StringUtils.isNotBlank(fullProduct.getSaleContractLineId())) {
            IObjectData saleContractLineData = saleContractLineMap.get(fullProduct.getSaleContractLineId());
            if (saleContractLineData != null && StringUtils.isNotBlank(saleContractLineData.get(SaleContractConstants.SaleContractLineField.PRICE_BOOK_ID.getApiName(), String.class))) {
                fullProduct.setPriceBookId(saleContractLineData.get(SaleContractConstants.SaleContractLineField.PRICE_BOOK_ID.getApiName(), String.class));
            }
        }
    }
}
