package com.facishare.crm.sfa.utilities.util.i18n;

/**
 * <AUTHOR>
 * @date 2020/9/14 4:15 下午
 */
public interface BomI18NKeyUtil {

    /**
     * 依赖互斥节点超过100级，请从新配置数据。
     */
    String SO_CHECK_CONSTRAINT_MAX_DEPTH = "so.check.constraint.max.depth";

    /**
     * 参数不合法！
     */
    String SFA_BASIC_SETTING_BUSINESS_4519_2 = "sfa.BasicSettingBusiness.4519.2";


    /**
     * 无效的树形结构
     */
    String SFA_BOM_INVALID_TREE_STRUCTURE = "sfa.bom.invalid.tree.structure";


    /**
     * 该子产品明细是必选但未被选中
     */
    String SO_CHECK_SUB_PRO_IS_REQUIRED = "so.check.sub.pro.is.required";

    /**
     * 组内选项控制为单选，只能选一个或者不选
     */
    String SO_CHECK_PRO_PCK_GROUP_CONTROL = "so.check.pro.pck.group.control";

    /**
     * 请保证数量大于等于最小数量
     */
    String SO_CHECK_SUB_PRO_COUNT = "so.check.sub.pro.count";

    /**
     * 子产品明细数量不允许编辑
     */
    String SO_CHECK_SUB_PRO_COUNT_NOT_EDIT = "so.check.sub.pro.count.not.edit";

    /**
     * 子产品明细价格不允许编辑
     */
    String SO_CHECK_SUB_PRO_PRICE_NOT_EDIT = "so.check.sub.pro.price.not.edit";

    /**
     * 请保证已选数量大于等于最少可选个数
     */
    String SO_CHECK_SUB_PRO_CATA_COUNT = "so.check.sub.pro.cata.count";

    /**
     * 请保证已选数量小于等于最多可选个数
     */
    String SO_CHECK_SUB_PRO_CATA_COUNT_MAX = "so.check.sub.pro.cata.count.max";

    /**
     * 请保证数量小于等于最大数量
     */
    String SO_CHECK_SUB_PRO_COUNT_MAX = "so.check.sub.pro.count.max";

    /**
     * 请保证最小数量小于等于最大数量
     */
    String SO_CHECK_SUB_PRO_COUNT_MIN_MAX = "so.check.sub.pro.count.min.max";

    /**
     * 数量字段：请输入符合{增减数量幅度}*n的数值
     */
    String SO_CHECK_SUB_PRO_COUNT_FORMULA = "so.check.sub.pro.count.formula";

    /**
     * 增加数量幅度不能小于零
     */
    String SO_CHECK_SUB_PRO_FIELD_INCREMENT = "so.check.sub,pro.field.increment";

    /**
     * 数量不能小于零
     */
    String SO_CHECK_SUB_PRO_FIELD_COUNT = "so.check.sub,pro.field.count";

    /**
     * 最大数量不能小于零
     */
    String SO_CHECK_SUB_PRO_FIELD_MAX_COUNT = "so.check.sub,pro.field.max.count";

    /**
     * 必选时最大数量不能等于零
     */
    String SO_CHECK_SUB_PRO_FIELD_IS_REQUIRED = "so.check.sub,pro.field.is.required";

    /**
     * 子产品明细必选时，也应该默认选中
     */
    String SO_CHECK_SUB_PRO_FIELD_IS_REQUIRED_DEFAULT_SELECTED = "so.check.sub,pro.field.is.required.default.seleccted";

    /**
     * 子产品明细关联的产品已下架、作废或删除，不可选择
     */
    String SO_CHECK_SUB_PRO_STATUS = "so.check.sub,pro.status";

    /**
     * 根产品为无效产品
     */
    String SFA_BOM_INVALID_OBJ_PRODUCT = "sfa.bom.invalid.obj.product";

    /**
     * {0}的约束关系已存在
     */
    String SFA_PRODUCT_CONSTRAINT_LINES_EXIST = "sfa.product.constraint.lines.exist";

    /**
     * 产品约束限制最大数量{0},超{1}不能后新建
     */
    String SFA_PRODUCT_CONSTRAINT_OVER_MAX_COUNT = "sfa.product.constraint.over.max.count";

    /**
     * 单个产品约束的约束关系不能超过{0}条
     */
    String SFA_PRODUCT_CONSTRAINT_LINES_OVER_MAX_COUNT = "sfa.product.constraint.lines.over.max.count";

    /**
     * 请添加约束条件
     */
    String SFA_PRODUCT_CONSTRAINT_LINES_CAN_NOT_EMPTY = "sfa.product.constraint.lines.cannot.empty";


    /**
     * {0}约束关系超过{1}级
     */
    String SFA_PRODUCT_CONSTRAINT_LINES_OVER_MAX_LEVEL = "sfa.product.constraint.lines.over.max.level";


    /**
     * {0}和{1}约束关系冲突
     */
    String SFA_PRODUCT_CONSTRAINT_LINES_CONFLICT = "sfa.product.constraint.lines.conflict";


    /**
     * {0}约束关系成环
     */
    String SFA_PRODUCT_CONSTRAINT_LINES_CYCLE = "sfa.product.constraint.lines.cycle";


    /**
     * {0}和{1}属于同一组合产品的同一节点，不可以配置约束关系
     */
    String SFA_PRODUCT_CONSTRAINT_LINES_CANNOT_REPEATED = "sfa.product.constraint.lines.cannot.repeated";


    /**
     * {0}和{1}属于同一组合产品的同一条分支上，不可以互斥
     */
    String SFA_PRODUCT_CONSTRAINT_LINES_SAME_BRANCH_CANNOT_EXCLUSIVE = "sfa.product.constraint.lines.same.branch.cannot.exclusive";

    /**
     * {0}和{1}的父级节点配置了互斥,不可以配置约束关系
     */
    String SFA_PRODUCT_CONSTRAINT_LINES_PARENT_EXCLUSIVE_SON_NODE_CANNOT_CREATE = "sfa.product.constraint.lines.parent.exclusive.son.node.cannot.create";


    /**
     * {0}产品组合约束已存在
     */
    String SFA_PRODUCT_CONSTRAINT_EXIST = "sfa.product.constraint.exist";

    /**
     * 产品组合约束已存在
     */
    String SFA_PRODUCT_CONSTRAINT_EXIST_NEW = "sfa.product.constraint.exist.new";


    /**
     * 校验产品约束关系 必选
     */
    String SFA_CHOOSE_NORMAL_PRODUCT_CONSTRAINT_LINES = "sfa.product.constraint.lines.choose";

    /**
     * 校验产品约束 不允许选择
     */
    String SFA_NOT_MUST_CHOOSE_NORMAL_PRODUCT_CONSTRAINT_LINES = "sfa.product.constraint.lines.not.must.choose";

    /**
     * 校验产品约束 必须选择选择
     */
    String SFA_MUST_CHOOSE_NORMAL_PRODUCT_CONSTRAINT_LINES = "sfa.product.constraint.lines.must.choose";

    /**
     * 同级分组不能重名
     */
    String SFA_BOM_SAME_LEVEL_GROUP_CAN_NOT_HAVE_THE_SAME_NAME = "sfa.bom.same.level.group.can.not.have.the.same.name";


    /**
     * 缺少参数
     */
    String SFA_MISS_PARAM = "sfa.missing.parameters";


    /**
     * 业务异常
     */
    String SFS_BUSINESS_EXCEPTION = "sfa.business.exception";


    /**
     * {0}, 单选对应的最少子产品个数是0或1。
     */
    String SFA_BOM_CHECK_CHOOSE_PRODUCT_1 = "sfa.bom.check.choose.product.1";

    /**
     * {0}, 单选对应的最大子产品个数必须是1
     */
    String SFA_BOM_CHECK_CHOOSE_PRODUCT_2 = "sfa.bom.check.choose.product.2";


    /**
     * {0}, 最小子产品个数必须小于等于最大子产品个数
     */
    String SFA_BOM_CHECK_CHOOSE_PRODUCT_3 = "sfa.bom.check.choose.product.3";
    /**
     * {0}, 数量合计最小值不能大于数量合计最大值
     */
    String SFA_BOM_CHECK_GROUP_AMOUNT_COUNT_WARN = "sfa.bom.check.group.amount.count.warn";

    /**
     * 所属BOM路径存在相同值，不允许导入，请修改。
     */
    String SFA_BOM_PATH_SAME_PLEASE_MODIFY_IT = "sfa.bom.path.same.please.modify.it";

    /**
     * 根节点已存在
     */
    String SFA_BOM_ROOT_NODE_EXIST = "sfa.bom.root.node.exist";

    /**
     * 未找到根节点
     */
    String SFA_BOM_ROOT_NODE_NOT_FOUND = "sfa.bom.root.node.not.found";

    /**
     * 关联标识不正确
     */
    String SFA_BOM_IMPORT_RELATE_MARK_WARN = "sfa.bom.import.relate.mark.warn";

    /**
     * 最大数量必须大于等于最小数量。
     */
    String SFA_BOM_MAX_QUANTITY_MUST_MORE_THAN_MIN_QUANTITY = "sfa.bom.max.quantity.must.more.than.min.quantity";

    /**
     * 数量必须小于等于最大数量。
     */
    String SFA_BOM_MIN_QUANTITY_MUST_LESS_THAN_MAX_QUANTITY = "sfa.bom.min.quantity.must.less.than.max.quantity";

    /**
     * 数量必须大于等于最小数量。
     */
    String SFA_BOM_CHECK_MIN_QUANTITY_AND_MAX_QUANTITY_1 = "sfa.bom.check.min.quantity.and.max.quantity.1";

    /**
     * 数量应该为增加数量幅度的整数倍。
     */
    String SFA_BOM_CHECK_MIN_QUANTITY_AND_MAX_QUANTITY_2 = "sfa.bom.check.min.quantity.and.max.quantity.2";

    /**
     * 未开价目表,定价模式请选择配置价格
     */
    String SFA_BOM_CHECK_PRICE_BOOK_1 = "sfa.bom.check.price.book.1";

    /**
     * 节点父级不存在。
     */
    String SFA_BOM_NODE_PARENT_NOT_EXIST = "sfa.bom.node.parent.not.exist";
    /**
     * 一个产品组合下只能有一个根节点。
     */
    String SFA_BOM_ROOT_NODE_REPEAT = "sfa.bom.root.node.repeat";

    /**
     * 关联分组导入失败。
     */
    String SFA_BOM_REFERENCE_GROUP_FAIL = "sfa.bom.reference.group.fail";


    /**
     * 相同节点下的数据不一致
     */
    String SFA_INCONSISTENT_DATA_UNDER_THE_SAME_NODE = "sfa.bom.inconsistent.data.under.the.same.node";

    /**
     * bom树层级不能超过10级
     */
    String SFA_BOM_LEVEL_CAN_NOT_EXCEED_TEN_LEVELS = "sfa.bom.level.can.not.exceed.ten.levels";

    /**
     * bom树节点数量不能超过200
     */
    String SFA_BOM_NODE_CAN_NOT_EXCEED_TWO_HUNDRED = "sfa.bom.node.can.not.exceed.two.hundred";
    /**
     * bom树节点数量不能超过{0}
     */
    String SFA_BOM_NODE_CAN_NOT_EXCEED_MAX_LIMIT_COUNT = "sfa.bom.node.can.not.exceed.four.hundred";

    /**
     * 标准类型的产品组合不允许挂配置类型的子BOM
     */
    String SFA_BOM_NODE_TYPE_WARN = "sfa.bom.node.type.warn";

    /**
     * bom成环
     */
    String SFA_BOM_RELATED_BOM_CYCLE = "sfa.bom.related.bom.cycle";

    /**
     * 标准和配置类型的子件版本不能为空
     */
    //String SFA_BOM_RELATED_BOM_NN_WARN = "sfa.bom.related.bom.nn.warn";

    /**
     * 同一棵树下存在错误数据
     */
    String SFA_BOM_IN_THE_SAME_TREE_EXIST_ERROR_DATA = "sfa.bom.in.the.same.tree.exist.error.data";

    /**
     * 关联子bom不能挂下级产品
     */
    String SFA_BOM_RELATED_BOM_CAN_NOT_CHILD_NODE = "sfa.bom.related.bom.can.not.child.node";

    /**
     * 标准类型的产品组合不让更新
     */
    String SFA_STANDARD_TYPE_BOM_CAN_NOT_UPDATE = "sfa.standard.type.bom.can.not.update";

    /**
     * 服务BOM必须为配置BOM，不允许为标准BOM
     */
    String SFA_SERVICE_TYPE_BOM_CAN_NOT_STANDARD = "sfa.service.type.bom.can.not.standard";

    /**
     * 产品包关联了产品组合不能作废，请先删除产品组合
     */
    String SFA_PRODUCT_RELATED_BOM_CORE_CAN_NOT_INVALID = "sfa.product.related.bom.core.cannot.invalid";

    /**
     * 所属BOM路径{0}下同级存在同名分组
     */
    String SFA_BOM_PATH_HAS_SAME_NAME_GROUP = "sfa.bom.path.has.same.name.group";

    /**
     * 所属BOM路径
     */
    String SFA_BOM_PATH = "sfa.bom.path";

    /**
     * 产品【" + productName + "】所在价目表有调整，请重新选择
     * 产品 {0} 所在价目表有调整，请重新选择
     */
    String SFA_IN_PRICE_BOOK_IS_CHANGE = "sfa.in.price.book.is.change";

    /**
     * {0}, 不适用当前选择的价目表
     */
    String SFA_BE_APPLICABLE_CHOOSE_PRICE_BOOK = "sfa.be.applicable.choose.price.book";


    /**
     * 当前租户已经开启了大小单位，暂时不允许开启CPQ
     */
    String SFA_OPEN_MULTI_UNIT_NOT_OPEN_CPQ = "sfa.open.multi.not.open.cpq";

    /**
     * 当前租户已经开启了大小单位，暂时不允许开启固定搭配
     */
    String SFA_OPEN_MULTI_UNIT_NOT_OPEN_SIMPLE_CPQ = "sfa.open.multi.not.open.simple.cpq";

    /**
     * 当前租户已经开启了商品，暂时不允许开启CPQ
     */
    String SFA_CPQ_CONFIG_SPU_WARN = "sfa.cpq.config.spu.warn";

    /**
     * 当前租户已经开启了商品，暂时不允许开启固定搭配
     */
    String SFA_SIMPLE_CPQ_CONFIG_SPU__WARN = "sfa.simple.cpq.config.spu.warn";
    /**
     * 当前租户已经开启了CPQ，暂时不允许开启大小单位
     */
    String SFA_CPQ_CONFIG_MULTIPLE_UNIT_WARN = "sfa.cpq.config.multiple.unit.warn";
    /**
     * 当前租户已经开启了固定搭配，暂时不允许开启大小单位
     */
    String SFA_SIMPLE_CPQ_CONFIG_MULTIPLE_UNIT_WARN = "sfa.simple.cpq.config.multiple.unit.warn";

    /**
     * 当前租户未开启价目表，暂时不允许开启CPQ
     */
    String SFA_CPQ_CONFIG_PRICE_BOOK_WARN = "sfa.cpq.config.priceBook.warn";
    /**
     * 当前租户未开启价目表，暂时不允许开启固定搭配
     */
    String SFA_SIMPLE_CPQ_CONFIG_PRICE_BOOK_WARN = "sfa.simple.cpq.config.priceBook.warn";
    /**
     * 当前租户未开启价bom，暂时不允许开启临时子件
     */
    String SFA_CPQ_CONFIG_TEMP_NODE_WARN = "sfa.cpq.config.tempNode.warn";
    /**
     * 开启销售BOM，不允许开启固定组合促
     */
    String SFA_BOM_OPEN_UN_ALLOW_OPEN_FIXED_COMBINATION_PROMOTION_WARN = "sfa.bom.open.unAllow.open.fixed.combination.promotion";
    /**
     * 开启固定组合促，不允许开启销售BOM
     */
    String SFA_FIXED_COMBINATION_PROMOTION_OPEN_UN_ALLOW_OPEN_BOM_WARN = "sfa.fixed.combination.promotion.open.unAllow.open.bom";
    /**
     * 未开启价格政策,不允许开启固定搭配
     */
    String SFA_CPQ_CONFIG_PRICE_POLICY_WARN = "sfa.cpq.config.pricePolicy.warn";


    /**
     * 根节点{0}重复
     */
    String SFA_ROOT_NODE_REPEAT = "sfa.root.node.repeat";


    /**
     * 产品分组-产品选配明细
     */
    String SFA_PRODUCT_GROUP_PRODUCT_CONFIG_LINES = "sfa.product.group.product.config.lines";

    /**
     * 一次性选择数量超过限制
     */
    String SFA_OVER_LIMIT = "sfa.choose.number.over.limit";

    /**
     * 节点已删除。
     */
    String SFA_BOM_NODE_DELETED = "sfa.bom.node.deleted";

    /**
     * {0}的子节点的分摊比例不能超过100
     */
    String SFA_BOM_SHARE_RATE_LIMIT = "sfa.bom.share.rate.over.100";

    /**
     * 子节点的数量必须是父节点数量的倍数
     */
    String SFA_BOM_NUMBER_LIMIT = "sfa.bom.number.limit";
    /**
     * 虚拟key重复
     */
    String SFA_BOM_PROD_KEY_DUPLICATION = "sfa.bom.prod.key.duplication";

    /**
     * 明细数量小数位超过设定长度
     */
    String SFA_NODE_QUANTITY_LENGTH_WARN = "sfa.node.quantity.length.warn";
    /**
     * 最多选择{0}个产品
     */
    String SFA_BOM_GROUP_PRODUCT_COUNT_MAX = "sfa.bom.group.product.count.max";

    /**
     * 最少选择{0}个产品
     */
    String SFA_BOM_GROUP_PRODUCT_COUNT_MIN = "sfa.bom.group.product.count.min";

    /**
     * 请先开启cpq
     */
    String SFA_BOM_INSTANCE_CONFIG_CPQ_WARN = "sfa.bom.instance.config.cpq.warn";
    /**
     * 请先开启标准bom
     */
    String SFA_GENERATE_STANDARD_BOM_WARN = "sfa.generate.standard.bom.warn";

    //约束项和被约束项为必填
    String SFA_BOM_CONSTRAINT_ITEM_REQUIRE = "sfa.bom.constraint.item.require";

    //根节点约束类型不一致
    String SFA_ROOT_BOM_CONSTRAINT_TYPE_ERROR = "sfa.root.bom.constraint.type.error";

    //根节点属性、属性值不能为空
    String SFA_ROOT_BOM_CONSTRAINT_DATA_ERROR = "sfa.root.bom.constraint.data.error";

    //根节点不能自己约束自己
    String SFA_ROOT_BOM_CONSTRAINT_CANNOT_EQUAL = "sfa.root.bom.constraint.cannot.equal";

    //相同节点不能自己约束自己
    String SFA_BOM_NODE_CONSTRAINT_CANNOT_EQUAL = "sfa.bom.node.constraint.cannot.equal";

    //存在相同的约束明细
    String SFA_BOM_CONSTRAINT_LINES_EXIST = "sfa.bom.constraint.lines.exist";

    //子节点不能约束父节点
    String SFA_BOM_CONSTRAINT_CHILD_NODES_CANNOT_CONSTRAIN_PARENT_NODES = "sfa.bom.constraint.Child.nodes.cannot.constrain.parent.nodes";

    //被约束项选择必选节点必须带属性
    String SFA_BOM_CONSTRAINT_LINES_RIGHT_CANNOT_CHOOSE_REQUIRE = "sfa.bom.constraint.lines.right.cannot.choose.require";

    //相同约束类型的约束项不能相同
    String SFA_SAME_CONSTRAINT_TYPE_BOM_CONSTRAINT_LINES_EXIST = "sfa.same.constraint.type.bom.constraint.lines.exist";

    //节点约束关系冲突不能既依赖又互斥
    String SFA_BOM_CONSTRAINT_LINES_NODE_CONFLICT = "sfa.bom.constraint.lines.node.conflict";

    //分组关系冲突不能既依赖又互斥
    String SFA_BOM_CONSTRAINT_LINES_GROUP_CONFLICT = "sfa.bom.constraint.lines.group.conflict";

    //相同约束产品的被约束产品不能配置父级不允许选择，子级必选
    String SFA_BOM_CONSTRAINT_LINES_PARENT_CHILD_NODE_CONFLICT = "sfa.bom.constraint.lines.parent.child.node.conflict";

    //已设置分组约束不能创建分组下的子件约束
    String SFA_BOM_CONSTRAINT_LINES_GROUP_NODE_CONFLICT = "sfa.bom.constraint.lines.group.node.conflict";

    //约束产品{0}的被约束产品不允许选择{1}和必须选择{2}冲突，不允许配置父级不允许选择，子级必选
    String SFA_BOM_CONSTRAINT_LINES_PARENT_CHILD_NODE_CONFLICT_NEW = "sfa.bom.constraint.lines.parent.child.constraint.conflict";

    //节点{0}成环
    String SFA_BOM_CONSTRAINT_LINES_CYCLE = "sfa.bom.constraint.lines.cycle";

    //节点{0}已被设置为产品属性约束的被约束产品,不能设置必选
    String SFA_BOM_CONSTRAINT_LINES_CANNOT_REQUIRED = "sfa.bom.constraint.lines.cannot.required";

    //属性值不能为空
    String SFA_ROOT_BOM_CONSTRAINT_DATA_ATTR_VALUE_ERROR = "sfa.root.bom.constraint.data.attr.value.error";

    //非标属性值最小值不能大于最大值
    String SFA_ROOT_BOM_CONSTRAINT_NON_ATTR_VALUE_ERROR = "sfa.root.bom.constraint.non.attr.value.error";

    //产品组合已删除或已作废
    String SFA_ROOT_BOM_IS_NOT_EXIST = "sfa.root.bom.is.not.exist";

    //产品组合重复
    String SFA_ROOT_BOM_IS_REPEAT = "sfa.root.bom.is.repeat";

    //Bom打印开启打印层级时，不允许开启只包含母键开关
    String SFA_BOM_PRINT_MSG = "sfa.bom.print.msg";

    //Bom打印不包含母键开启时，不允许开启打印层级
    String SFA_BOM_PRINT_RULE_MSG = "sfa.bom.print.rule.msg";

    //子级产品不能和父级产品相同，更新导入失败
    String SFA_BOM_IMPORT_PARENT_CHILD_EQUAL = "sfa.bom.import.parent.child.equal";

    //导入{0}的字段【价格可编辑】为否，与 上下级产品的【价格可编辑】或【数量可编辑】为否 冲突，更新导入失败
    String SFA_BOM_IMPORT_PARENT_CHILD_FIELD_CONFLICT = "sfa.bom.import.parent.child.field.conflict";

    //数量必填,且必须大于0
    String SFA_BOM_IMPORT_QUANTITY_REQUIRED = "sfa.bom.import.quantity.required";

    //请检查数量，最大数量，最小数量之间的关系，要满足如下规则： (最小数量 <= 数量 <= 最大数量）
    String SFA_BOM_IMPORT_QUANTITY_RULE = "sfa.bom.import.quantity.rule";

    //数量，最大数量，最小数量不允许是小数
    String SFA_BOM_IMPORT_QUANTITY_NO_POINT = "sfa.bom.import.quantity.no.point";

    //产品分组或产品分组唯一性ID不允许修改
    String SFA_BOM_IMPORT_PRODUCT_GROUP_UPDATED = "sfa.bom.import.product.group.updated";

    //计算接口未返回结果
    String SFA_BOM_PRICE_CALCULATION_INTERFACE_DID_NOT_RETURN_RESULT = "sfa.bom.price.calculation.interface.did.not.return.result";

    //{0},分组为必选, 请选择子产品
    String SFA_BOM_GROUP_REQUIRED_PRODUCT = "sfa.bom.group.required.product";

    //分组【{0}】选中的子件数量之和超过该分组的数量合计最大值，请修改！
    String SFA_BOM_GROUP_MAX_AMOUNT_WARN = "sfa.bom.group.max.amount.warn";

    //分组【{0}】选中的子件数量之和小于该分组的数量合计最小值，请修改！
    String SFA_BOM_GROUP_MIN_AMOUNT_WARN = "sfa.bom.group.min.amount.warn";

    //【xxx】为空分组，下单选配时空分组不展示，如需下单当选所选子件，需先将【xxx】分组规则设为非必选
    String SFA_BOM_EMPTY_GROUP_REQUIRED_PRODUCT = "sfa.bom.empty.group.required.product";

    //产品属性约束关系:{0}已配置了产品:{1}的属性:{2},不允许解除关联关系
    String SFA_BOM_ATTR_CANNOT_REMOVE_PRODUCT_ATTR = "sfa.bom.attr.cannot.remove.product.attr";

    //产品属性约束关系:{0}已配置了产品:{1}的非标属性:{2},不允许解除关联关系
    String SFA_BOM_ATTR_CANNOT_REMOVE_PRODUCT_NON_ATTR = "sfa.bom.attr.cannot.remove.product.non.attr";

    //请先申请产品属性约束功能灰度
    String SFA_BOM_ATTR_CONSTRAINT_GRAY = "sfa.bom.attr.constraint.gray";

    //非组合产品不允许更改产品组合字段为是
    String SFA_BOM_CHANGE_PACKAGE_YES = "sfa.bom.change.package.yes";

    //组合产品下存在子产品不允许更改产品组合字段为否
    String SFA_BOM_CHANGE_PACKAGE_NO = "sfa.bom.change.package.no";

    //产品已关联了产品组合不允许更改产品组合字段为否
    String SFA_CHANGE_PRODUCT_IS_PACKAGE_NO_WARN = "sfa.change.product.is.package.no.warn";

    //产品包不存在或已删除
    String SFA_PACKAGE_NOT_FOUND = "sfa.package.not.found";

    //产品包价格不正确
    String SFA_PACKAGE_PRICE_ERROR = "sfa.package.price.error";

    //产品不在母件选择的价目表中，如需添加该产品，需将其维护至选择的价目表
    String SFA_BOM_PRICE_RANGE_WARN = "sfa.bom.price.range.warn";

    //分组【套装】子产品默认选中的个数超过该分组的最大子产品个数，请修改
    String SFA_BOM_GROUP_RULE_WARN = "sfa.bom.group.rule.warn";

    //请使用新接口去创建bom主从对象
    String SFA_BOM_CREATE_WARN = "sfa.bom.create.warn";

    //关联产品组合不存在
    String SFA_RELATED_BOM_NOT_EXIST = "sfa.related.bom.not.exist";

    //关联产品组合不能为空
    String SFA_RELATED_BOM_CAN_NOT_NULL = "sfa.related.bom.can.not.null";

    //关联产品组合和产品不匹配
    String SFA_RELATED_BOM_WITH_PRODUCT_INCONSISTENT = "sfa.related.bom.with.product.inconsistent";

    //子件与父项产品成环
    String SFA_NODE_BOM_NOT_EQUAL_MASTER = "sfa.node.bom.not.equal.master";

    //根节点产品与产品组合父项产品不匹配
    String SFA_BOM_CORE_PRODUCT_INCONSISTENT = "sfa.bom.core.product.inconsistent";

    //产品包价格计算不正确，请重新选配
    String SFA_BOM_PRICE_CAL_WARN = "sfa.bom.price.cal.warn";

    //请先申请[升级产品组合-新BOM]灰度
    String SFA_BOM_CORE_WARN = "sfa.bom.core.warn";

    //未选择子件bom版本
    String SFA_BOM_NODE_VERSION_WARN = "sfa.bom.node.version.warn";

    //上级产品名称
    String SFA_BOM_PARENT_NODE_NAME_EXPORT_WARN = "sfa.bom.parent.node.name.export.warn";

    //上级产品唯一性ID
    String SFA_BOM_PARENT_NODE_ID_EXPORT_WARN = "sfa.bom.parent.node.id.export.warn";

    //产品[]未选择bom版本
    String SFA_BOM_VERSION_IS_EMPTY_WARN = "sfa.bom.version.is.empty.warn";
    //规则定义模式为APL代码时，APL代码必填
    String SFA_BOM_CONSTRAINT_APL_REQUIRED = "sfa.bom.constrain.apl.required";
    //产品属性约束明细、新增APL代码、约束赋值APL不能同时为空
    String SFA_BOM_CONSTRAINT_APL_NOT_ALL_EMPTY = "sfa.bom.constrain.apl.not.all.empty";
    //当前产品组合已绑定过APL函数，不允许重复绑定。
    String SFA_BOM_CONSTRAINT_APL_EXIST = "sfa.bom.constrain.apl.exist";
    //规则定义模式为APL代码时，产品组合不能为空
    String SFA_APL_BOM_CAN_NOT_NULL = "sfa.apl.bom.can.not.null";
    //规则定义模式和新增APL代码不允许单独编辑，请到编辑页进行维护
    String  SFA_APL_CHECK_MODE_CAN_NOT_BE_EDITED = "sfa.apl.check.mode.can.not.be.edited";

    //表达式不能为空
    String BOM_FORMULA_EXPRESSION_NOT_NULL = "bom.formula.expression.not.null";
    //计算表达式时，已勾选子件不能空
    String BOM_FORMULA_CALCULATE_SELECTED_BOM_NOT_EMPTY = "bom.formula.calculate.selected.bom.not.empty";

    //计算表达式参数中，缺少子件ID
    String BOM_FORMULA_EXPRESSION_BOM_ID_IS_NULL = "bom.formula.expression.bom.id.is.null";

    //子件:{0} 对应的表达式为空，参数错误
    String BOM_FORMULA_EXPRESSION_PARAMETER_ERROR = "bom.formula.expression.parameter.error";

    //子件[{0}]配置的表达式，需要勾选子件[{1}]参与计算
    String BOM_FORMULA_EXPRESSION_BOM_NOT_EXIST = "bom.formula.expression.bom.not.exist";

    //表达式中所需要的子件属性信息不存在，子件ID:{0}
    String BOM_FORMULA_EXPRESSION_BOM_ATTRIBUTE_NOT_EXIST = "bom.formula.expression.bom.attribute.not.exist";
    //表达式中所需要的子件属性值信息不存在，子件ID:{0}
    String BOM_FORMULA_EXPRESSION_BOM_ATTRIBUTE_VALUE_NOT_EXIST = "bom.formula.expression.bom.attribute.value.not.exist";

    //表达式中所需要的子件非标属性信息不存在，子件ID:{0}
    String BOM_FORMULA_EXPRESSION_BOM_NON_ATTRIBUTE_NOT_EXIST = "bom.formula.expression.bom.non.attribute.not.exist";
    //表达式中所需要的子件非标属性值信息不存在，子件ID:{0}
    String BOM_FORMULA_EXPRESSION_BOM_NON_ATTRIBUTE_VALUE_NOT_EXIST = "bom.formula.expression.bom.non.attribute.value.not.exist";

    //计算表达式时，提供的数据中缺少必要参数
    String BOM_FORMULA_EXPRESSION_PARAMETER_MISS = "bom.formula.expression.parameter.miss";

    //表达式计算未返回结果
    String BOM_FORMULA_EXPRESSION_CALCULATE_ERROR = "bom.formula.expression.calculate.error";

    //高级公式校验不通过的产品包括：{0}
    String BOM_FORMULA_EXPRESSION_CALCULATE_NOT_PASS_PRODUCT = "bom.formula.expression.calculate.not.pass.product";

    //名称：{0}，当前值：{1}，计算值：{2}
    String BOM_FORMULA_EXPRESSION_CALCULATE_NOT_PASS_PRODUCT_DETAIL = "bom.formula.expression.calculate.not.pass.product.detail";
    //赋值APL函数不能为空
    String BOM_ASSIGN_APL_NOT_BE_NULL = "bom.assign.apl.not.be.null";

    //执行赋值APL函数时，返回数据格式不符合要求
    String BOM_ASSIGN_APL_RESULT_FORMAT_ERROR = "bom.assign.apl.result.format.error";
    //高级公式中所参与计算的所有子件没有被全部勾选，请勾选后再保存
    String SFA_BOM_FORMULA_SELECTED_BOM_NOT_EXIST = "sfa.bom.formula.selected.bom.not.exist";
    //子件"数量"字段参与了高级公式的计算，该子件不能再配置高级公式，涉及到的子件包括：{0}
    String SFA_BOM_FORMULA_CANNOT_BE_BOTH_IN_EXPRESSION = "sfa.bom.formula.cannot.be.both.in.expression";

    //计算表达式时，字段为：{0}，取不到对应的值
    String SFA_BOM_FORMULA_EXPRESSION_LOST_PARAMETER_VALUE = "sfa.bom.formula.expression.lost.parameter.value";

    //bompath不能为空
    String SFA_BOM_FIELD_PATH_NOT_NULL = "sfa.bom.field.path.not.null";

    //请检查函数[{0}]是否存在或处于启用状态
    String SFA_BOM_FUNCTION_NOT_EXIST_OR_NOT_ACTIVE = "sfa.bom.function.not.exist.or.not.active";

    //字段{0}不能为空
    String SFA_BOM_ADD_CHECK_FIELD_NOT_NULL = "sfa.bom.add.check.field.not.null";

    //字段{0}值不正确
    String SFA_BOM_ADD_CHECK_FIELD_CORRECT = "sfa.bom.add.check.field.correct";

    //配置项[{0}]的值[{1}]不符合要求
    String SFA_CPQ_UI_MODE_NOT_SUPPORT = "sfa.cpq.ui.mode.not.support";

    //使用{0}模式，需要开通属性！
    String SFA_CPQ_UI_MODE_NEED_OPEN_ATTRIBUTE = "sfa.cpq.ui.mode.need.open.attribute";
    //默认模式
    String SFA_CPQ_UI_MODE_0 = "sfa.cpq.ui.mode.0";
    //选品下单
    String SFA_CPQ_UI_MODE_1 = "sfa.cpq.ui.mode.1";
    //属性下单
    String SFA_CPQ_UI_MODE_2 = "sfa.cpq.ui.mode.2";
    //参数值不合法
    String PARAM_VALUE_INVALID = "param.value.invalid";

    /**
     * 启用增量报价前，必须先启用属性
     */
    String OPEN_INCREMENTAL_PRICING_MUST_OPEN_ATTRIBUTE = "open.incremental.pricing.must.open.attribute";

    /**
     * 启用后，不允许关闭
     */
    String INCREMENTAL_PRICING_NOT_ALLOW_CLOSE = "incremental.pricing.not.allow.close";

    /**
     * 启用属性增量定价时，属性价目表新增字段描述失败
     */
    String OPEN_INCREMENTAL_PRICING_ADD_FIELD_ERROR = "open.incremental.pricing.add.field.error";
    /**
     * 更新属性定价方式默认值失败
     */
    String UPDATE_PRICING_MODE_FIELD_VALUE_ERROR = "update.pricing.mode.field.value.error";

}
