package com.facishare.crm.sfa.predefine.service.quoter

import com.facishare.crm.sfa.predefine.service.quoter.model.AttributeConstraintModel
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.google.common.collect.Lists
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import spock.lang.Shared
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString

/**
 * @描述说明 ：
 *
 * @作 者：chench
 *
 * @创建日 期：2024-06-11
 */
@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([I18N])
@SuppressStaticInitializationFor(["com.facishare.paas.I18N"])
class AttributeConstraintServiceTest extends Specification {
    @Mock
    @Shared
    private ServiceFacade serviceFacade;
    @Shared
    private AttributeConstraintService attributeConstraintService;

    def setupSpec() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(I18N.class)
        attributeConstraintService = new AttributeConstraintService();
        Whitebox.setInternalState(attributeConstraintService, "serviceFacade", serviceFacade);
    }


    def "test getAttributeConstraintLinesByAttributeConstraintId method"() {
        given:
        User user = User.builder().tenantId("89242").userId("1000").build();
        when:
        PowerMockito.doReturn(new com.facishare.paas.metadata.api.QueryResult()).when(serviceFacade, "findBySearchQueryIgnoreAll", any(), any(), any());
        List<IObjectData> result = attributeConstraintService.getAttributeConstraintLinesByAttributeConstraintId(user, "id123456");
        then:
        result != null;
    }

    def "test tree2List method"() {
        given:
        List<AttributeConstraintModel.AttributeConstraintNode> detailList = Lists.newArrayList();
        AttributeConstraintModel.AttributeConstraintNode rootNode = new AttributeConstraintModel.AttributeConstraintNode();
        rootNode.setId("id123456");
        rootNode.setNodeType(attributeType);
        rootNode.setValues(Lists.newArrayList("11111"));
        rootNode.setRequiredSelected(Lists.newArrayList("aaa"));

        AttributeConstraintModel.AttributeConstraintNode subNode = new AttributeConstraintModel.AttributeConstraintNode();
        subNode.setId("sub123456");
        subNode.setNodeType(attributeType);
        subNode.setValues(Lists.newArrayList("22222"));
        subNode.setRequiredSelected(Lists.newArrayList("aaa"));

        rootNode.setChildren(Lists.newArrayList(subNode));

        detailList.add(rootNode);
        when:
        List<IObjectData> result = attributeConstraintService.tree2List(detailList, "id123456");
        then:
        result != null;
        where:
        attributeType || rr
        1             || "1"
        2             || "2"
    }

    def "test list2MultiTree method"() {
        given:
        List<IObjectData> detailList = Lists.newArrayList();
        ObjectDataDocument rootNode = new ObjectDataDocument();
        rootNode.put("_id", "r123");
        rootNode.put("attribute_value_ids", Lists.newArrayList("not_attr123456"));
        rootNode.put("default_attr_values_ids", Lists.newArrayList("not_attr123456"));
        if (attributeType == 1) {
            rootNode.put("node_type", 1);
        } else {
            rootNode.put("node_type", 2);
        }

        detailList.add(rootNode.toObjectData());

        ObjectDataDocument subNode = new ObjectDataDocument();
        subNode.put("_id", "s123");
        subNode.put("parent_id", "p123456");
        subNode.put("attribute_value_ids", Lists.newArrayList("not_attr123456"));
        subNode.put("default_attr_values_ids", Lists.newArrayList("not_attr123456"));
        if (attributeType == 1) {
            subNode.put("node_type", 1);
        } else {
            subNode.put("node_type", 2);
        }
        detailList.add(subNode.toObjectData());
        when:
        List<AttributeConstraintModel.AttributeConstraintNode> result = attributeConstraintService.list2MultiTree(detailList);
        then:
        result != null;
        where:
        attributeType || rr
        1             || "1"
        2             || "2"
    }


    def "test check method"() {
        given: "准备测试数据"

        User user = User.builder().tenantId("89242").userId("1000").build()
        List<IObjectData> datas = Lists.newArrayList()

        // 创建标准属性节点
        ObjectDataDocument standardAttrNode = new ObjectDataDocument()
        standardAttrNode.put("_id", "standardAttr123")
        standardAttrNode.put("node_type", 1) // 标准属性
        standardAttrNode.put("is_standard_attribute", true)
        standardAttrNode.put("attribute_id", "attr123")
        datas.add(standardAttrNode.toObjectData())

        // 创建非标属性节点
        ObjectDataDocument nonStandardAttrNode = new ObjectDataDocument()
        nonStandardAttrNode.put("_id", "nonStandardAttr123")
        nonStandardAttrNode.put("node_type", 1) // 非标属性
        nonStandardAttrNode.put("is_standard_attribute", false)
        nonStandardAttrNode.put("attribute_id", "nonAttr123")
        datas.add(nonStandardAttrNode.toObjectData())

        // 创建属性值节点
        ObjectDataDocument attrValueNode = new ObjectDataDocument()
        attrValueNode.put("_id", "attrValue123")
        attrValueNode.put("node_type", 2) // 属性值
        attrValueNode.put("is_standard_attribute", true)
        attrValueNode.put("attribute_value_ids", Lists.newArrayList("value123"))
        datas.add(attrValueNode.toObjectData())

        // 模拟ServiceFacade的行为
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData(Lists.newArrayList(ObjectDataDocument.of(["is_deleted": false, "name": "红色"]).toObjectData()))
        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQueryWithDeleted", any(), any(), any())

        when: "调用check方法"
        attributeConstraintService.check(datas, user)

        then: "没有异常抛出"
        true

        /*when: "模拟数据被删除的情况"
        PowerMockito.doReturn(getQueryResult(true)).when(serviceFacade, "findBySearchQueryWithDeleted", any(), any(), any())
        attributeConstraintService.check(datas, user)*/

        then: "抛出ValidateException异常"
        true
    }

    private QueryResult<IObjectData> getQueryResult(boolean checkError) {
        QueryResult<IObjectData> queryResult = new QueryResult<IObjectData>()
        if (checkError) {
            queryResult.setData(Lists.newArrayList(ObjectDataDocument.of(["is_deleted": true, "name": "红色"]).toObjectData()))
        }
        return queryResult
    }
}
