package com.facishare.crm.sfa.utilities.util.Price

import com.facishare.crm.sfa.RemoveUseless
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt
import com.facishare.crm.sfa.predefine.service.PriceBookCommonService
import com.facishare.crm.sfa.predefine.service.attribute.AttributeCoreService
import com.facishare.crm.sfa.predefine.service.attributepricebook.AttributePriceBookService
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService
import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils
import com.facishare.crm.sfa.utilities.util.GrayUtil
import com.facishare.crm.sfa.utilities.util.ValidDateUtils
import com.facishare.crm.util.MtCurrentUtil
import com.facishare.paas.appframework.common.util.StopWatch
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.util.SpringContextUtil
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.apache.commons.lang3.StringUtils
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyBoolean

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([StopWatch.class, ValidDateUtils.class, GrayUtil.class, MtCurrentUtil.class, UdobjGrayConfig.class])
class BaseRealPriceServiceTest extends RemoveUseless {

    private String tenantId = "79534"

    @Shared
    protected BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService

    @Shared
    protected AvailableRangeUtils availableRangeUtils

    @Shared
    protected MetaDataFindServiceExt metaDataFindServiceExt

    @Shared
    protected PriceBookCommonService priceBookCommonService

    @Shared
    protected AttributePriceBookService attributePriceBookService

    @Shared
    protected ServiceFacade SERVICE_FACADE

    @Shared
    protected AttributeCoreService attributeCoreService

    @Shared
    protected MultiUnitService multiUnitService

    @Shared
    protected User user

    def setupSpec() {
        removeI18N()
        removeConfigFactory()
        initSpringContext()
        Mockito.mockStatic(ValidDateUtils)
        Mockito.mockStatic(GrayUtil)
        Mockito.mockStatic(StopWatch)
        Mockito.mockStatic(UdobjGrayConfig)
        Mockito.mockStatic(MtCurrentUtil)
        RequestContextManager.setContext(RequestContext.builder().build())
        bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        availableRangeUtils = PowerMockito.mock(AvailableRangeUtils)
        priceBookCommonService = PowerMockito.mock(PriceBookCommonService)
        attributePriceBookService = PowerMockito.mock(AttributePriceBookService)
        SERVICE_FACADE = PowerMockito.mock(ServiceFacade)
        attributeCoreService = PowerMockito.mock(AttributeCoreService)
        user = PowerMockito.spy(new User("79534", "-10000"))
        multiUnitService = PowerMockito.mock(MultiUnitService)
        metaDataFindServiceExt = PowerMockito.mock(MetaDataFindServiceExt)
    }

    def "init"() {
        given:

        def arg = new RealPriceModel.Arg()
        arg.setAccountId(accountId)
        arg.setPartnerId(partnerId)
        arg.setObjectData(objectData)
        arg.setAvailableRangeId(availableRangeId)
        arg.setProductDataList(productDataList)
        def baseRealPriceService = new BaseRealPriceService(arg, user, priceBookFlag, availableRangeFlag)
        Whitebox.setInternalState(baseRealPriceService, "arg", arg)
        Whitebox.setInternalState(baseRealPriceService, "priceBookFlag", priceBookFlag)
        Whitebox.setInternalState(baseRealPriceService, "availableRangeFlag", availableRangeFlag)
        Whitebox.setInternalState(baseRealPriceService, "user", user)
        List<RealPriceModel.FullProduct> productList = Lists.newArrayList()
        RealPriceModel.FullProduct fullProduct = new RealPriceModel.FullProduct()
        fullProduct.setProductId(productId)
        fullProduct.setAmount(amount)
        productList.add(fullProduct)
        Whitebox.setInternalState(baseRealPriceService, "attributeFlag", availableRangeFlag)
        PowerMockito.field(BaseRealPriceService.class, "bizConfigThreadLocalCacheService").set(baseRealPriceService, bizConfigThreadLocalCacheService)
        PowerMockito.field(BaseRealPriceService.class, "availableRangeUtils").set(baseRealPriceService, availableRangeUtils)
        PowerMockito.field(BaseRealPriceService.class, "priceBookCommonService").set(baseRealPriceService, priceBookCommonService)
        PowerMockito.field(BaseRealPriceService.class, "attributePriceBookService").set(baseRealPriceService, attributePriceBookService)
        PowerMockito.field(BaseRealPriceService.class, "SERVICE_FACADE").set(baseRealPriceService, SERVICE_FACADE)
        PowerMockito.field(BaseRealPriceService.class, "attributeCoreService").set(baseRealPriceService, attributeCoreService)
        PowerMockito.doReturn(isMultiUnitPriceBook).when(bizConfigThreadLocalCacheService, "isOpenMultiUnitPriceBook", any())
        PowerMockito.when(ValidDateUtils.getValidDate(any(), any(), any())).thenReturn(businessDate)
        PowerMockito.when(GrayUtil.isPriceBookReform(any())).thenReturn(isPriceBookReform)
        PowerMockito.when(StopWatch.create(any())).thenReturn(new StopWatch("xxx"))
        //PowerMockito.doReturn(Lists.newArrayList()).when(baseRealPriceService, "findStandardProductList", any())
        PowerMockito.doReturn(null).when(availableRangeUtils, "findSaleContractInfo", any(), any())
        //PowerMockito.doReturn(Maps.newHashMap()).when(baseRealPriceService, "getSaleContractLineInfo", any(), any(), any(), any(), any())
        PowerMockito.doReturn(availableRangeIdList).when(availableRangeUtils, "getAvailableRangeIdList", any(), any(), any(), any(), any(), any(), any())
        PowerMockito.doReturn(Lists.newArrayList()).when(availableRangeUtils, "getAvailableProductDataList", any(), any(), any())
        PowerMockito.doReturn(Lists.newArrayList()).when(availableRangeUtils, "getPriceBookListByRangeIds", any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(), any())
        //PowerMockito.doNothing().when(baseRealPriceService, "filterPriceBookByCurrency", any(), any(), any())
        //PowerMockito.doNothing().when(baseRealPriceService, "getProductByPriceBookList", any(), any(), any(), any(), any(), any())
        PowerMockito.doReturn(null).when(availableRangeUtils, "buildPriceBookRangeSearchQuery", any(), any(), any(), any(), anyBoolean())
        PowerMockito.doReturn(Lists.newArrayList()).when(priceBookCommonService, "getPriceBookList", any(), any(), any(), any(), any())
        PowerMockito.doReturn(Lists.newArrayList()).when(availableRangeUtils, "getAllPriceBookList", any(), any(), any(), any())
        PowerMockito.doReturn(attrPriceBookLinesList).when(attributePriceBookService, "getAttributePriceBookLines", any(), any(), any())
        PowerMockito.doReturn(attributePriceBookList).when(SERVICE_FACADE, "findObjectDataByIdsIgnoreAll", any(), any(), any())
        PowerMockito.doReturn(Lists.newArrayList()).when(attributeCoreService, "getAttributeByIds", any(), any())
        PowerMockito.doReturn(isOpenIncrementalPricing).when(bizConfigThreadLocalCacheService, "isOpenIncrementalPricing", any())
        when:
        baseRealPriceService.init(productList)
        then:
        1 == 1
        where:
        accountId | partnerId | productId | priceBookFlag | availableRangeFlag | isMultiUnitPriceBook | availableRangeId | businessDate  | isPriceBookReform | isOpenIncrementalPricing | amount              | availableRangeIdList          | productDataList               | attrPriceBookLinesList                                                 | attributePriceBookList                                   | productInfoList                        | objectData
        "xxx"     | "xxx"     | "xxx"     | true          | true               | false                | null             | ************* | true              | false                    | new BigDecimal("1") | getObjectDataList(["_id"], 1) | null                          | null                                                                   | null                                                     | getObjectDataList(["_id", "price"], 1) | getObjectDataDocument("xxx")
        "xxx"     | null      | "xxx"     | true          | true               | false                | null             | null          | true              | false                    | new BigDecimal("2") | null                          | null                          | getObjectDataList(["_id", "product_id", "attribute_price_book_id"], 1) | getObjectDataList(["_id", "attribute_price_book_id"], 1) | getObjectDataList(["_id", "price"], 1) | getObjectDataDocument("xxx")
        "xxx"     | "xxx"     | "xxx"     | true          | true               | false                | "xxx"            | null          | true              | false                    | new BigDecimal("2") | getObjectDataList(["_id"], 1) | getObjectDataList(["_id"], 1) | getObjectDataList(["_id", "product_id", "attribute_price_book_id"], 1) | getObjectDataList(["_id", "attribute_price_book_id"], 1) | getObjectDataList(["_id", "price"], 1) | getObjectDataDocument("xxx")
        "xxx"     | "xxx"     | "xxx"     | true          | true               | false                | "xxx"            | null          | true              | true                     | new BigDecimal("2") | getObjectDataList(["_id"], 1) | getObjectDataList(["_id"], 1) | getObjectDataList(["_id", "product_id", "attribute_price_book_id"], 1) | getObjectDataList(["_id", "attribute_price_book_id"], 1) | getObjectDataList(["_id", "price"], 1) | getObjectDataDocument("xxx")
        "xxx"     | null      | "xxx"     | true          | false              | true                 | null             | ************* | true              | false                    | new BigDecimal("2") | getObjectDataList(["_id"], 1) | getObjectDataList(["_id"], 1) | getObjectDataList(["_id", "product_id", "attribute_price_book_id"], 1) | getObjectDataList(["_id", "attribute_price_book_id"], 1) | getObjectDataList(["_id", "price"], 1) | null
        "xxx"     | null      | "xxx"     | false         | false              | true                 | null             | null          | false             | false                    | new BigDecimal("1") | getObjectDataList(["_id"], 1) | null                          | null                                                                   | null                                                     | null                                   | null
        "xxx"     | "xxx"     | "xxx"     | false         | false              | false                | null             | null          | false             | false                    | new BigDecimal("1") | getObjectDataList(["_id"], 1) | null                          | null                                                                   | null                                                     | getObjectDataList(["_id", "price"], 1) | getObjectDataDocument("xxx")
        "xxx"     | "xxx"     | "xxx"     | false         | true               | false                | null             | null          | false             | false                    | new BigDecimal("1") | getObjectDataList(["_id"], 1) | null                          | null                                                                   | null                                                     | getObjectDataList(["_id", "price"], 1) | getObjectDataDocument("xxx")
    }

    def "act"() {
        given:
        def arg = new RealPriceModel.Arg()
        arg.setAccountId(accountId)
        arg.setPartnerId(partnerId)
        arg.setObjectData(objectData)
        arg.setFromSpuList(false)
        arg.setMcCurrency(mcCurrency)
        arg.setFullProductList(getFullProductList(productId, amount))
        def baseRealPriceService = new BaseRealPriceService(arg, user, priceBookFlag, availableRangeFlag)
        Whitebox.setInternalState(baseRealPriceService, "arg", arg)
        Whitebox.setInternalState(baseRealPriceService, "user", user)
        PowerMockito.when(StopWatch.create(any())).thenReturn(new StopWatch("xxx"))
        //PowerMockito.doReturn(getFullProductList(productId, amount)).when(baseRealPriceService, "getSortedProductList")
        //PowerMockito.doNothing().when(baseRealPriceService, "init", any())
        PowerMockito.field(BaseRealPriceService.class, "multiUnitService").set(baseRealPriceService, multiUnitService)
        PowerMockito.doReturn(isOpenMultiUnit).when(multiUnitService, "isOpenMultiUnit", any())
        //PowerMockito.doReturn(multiUnitDataList).when(baseRealPriceService, "getMultiUnitDataList")
        PowerMockito.field(BaseRealPriceService.class, "bizConfigThreadLocalCacheService").set(baseRealPriceService, bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(isEnforcePriority).when(bizConfigThreadLocalCacheService, "isEnforcePriority", any())
        PowerMockito.doReturn(isCurrencyEnabled).when(bizConfigThreadLocalCacheService, "isCurrencyEnabled", any())
        PowerMockito.doReturn(isTieredPrice).when(bizConfigThreadLocalCacheService, "isOpenPriceBookProductTieredPrice", any())
        PowerMockito.when(MtCurrentUtil.getExchangeRateMap(any(), any())).thenReturn(new HashMap<String, String>())
        //PowerMockito.doReturn(new ObjectData(new HashMap("id": "xxx", "selling_data_define_type": "sys"))).when(baseRealPriceService, "getHighPriorityData", any(), any(), any(), any(), anyBoolean(), anyBoolean(), any())
        //PowerMockito.doNothing().when(baseRealPriceService, "matchMultiUnit", any(), any(), any())
        //PowerMockito.doNothing().when(baseRealPriceService, "processSameTieredPrice", any(), any())
        PowerMockito.when(GrayUtil.finalFillRefObject(any())).thenReturn(true)
        //PowerMockito.doNothing().when(baseRealPriceService, "fillData", any())
        when:
        baseRealPriceService.act()
        then:
        1 == 1
        where:
        accountId | partnerId | productId | amount              | priceBookFlag | availableRangeFlag | isOpenMultiUnit | isEnforcePriority | isCurrencyEnabled | mcCurrency | isTieredPrice | multiUnitDataList                    | objectData                   | data
        "xxx"     | "xxx"     | "xxxx"    | new BigDecimal("1") | true          | true               | false           | true              | false             | null       | false         | getObjectDataList(["product_id"], 2) | getObjectDataDocument("xxx") | getObjectData("id", "selling_data_define_type")
        "xxx"     | "xxx"     | "xxxx"    | new BigDecimal("1") | true          | true               | true            | false             | true              | "CNY"      | true          | getObjectDataList(["product_id"], 2) | getObjectDataDocument("xxx") | getObjectData("id")
        "xxx"     | "xxx"     | null      | new BigDecimal("1") | true          | true               | false           | true              | false             | null       | false         | getObjectDataList(["product_id"], 2) | getObjectDataDocument("xxx") | getObjectData("id", "selling_data_define_type")
    }

    def "getHighPriorityData"() {
        given:
        def arg = new RealPriceModel.Arg()
        arg.setAccountId(accountId)
        arg.setPartnerId(partnerId)
        arg.setObjectData(objectData)
        RealPriceModel.FullProduct fullProduct = new RealPriceModel.FullProduct()
        fullProduct.setProductId("xxx")
        fullProduct.setAmount(amount)
        fullProduct.setUnit("1")
        fullProduct.setRowId("xxx")
        fullProduct.setPriceBookId(priceBookId)
        def baseRealPriceService = new BaseRealPriceService(arg, user, priceBookFlag, availableRangeFlag)
        PowerMockito.field(BaseRealPriceService.class, "availableRangeUtils").set(baseRealPriceService, availableRangeUtils)
        PowerMockito.doReturn(effectivePriceBookProductList).when(availableRangeUtils, "getEffectivePriceBookProductList", anyBoolean(), anyBoolean(), any(), any(), any(), any(), anyBoolean(), any(), any())
        //PowerMockito.doNothing().when(baseRealPriceService, "contractConstraintFillPriceBookId", any(), any())
        PowerMockito.field(BaseRealPriceService.class, "bizConfigThreadLocalCacheService").set(baseRealPriceService, bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(isMultiUnitPriceBook).when(bizConfigThreadLocalCacheService, "isOpenMultiUnitPriceBook", any())
        //PowerMockito.doReturn(sortedData).when(baseRealPriceService, "fillFullProductPricebookId", any(), any(), any())
        //PowerMockito.doReturn(sortedData).when(baseRealPriceService, "findEffectiveDataForMultiUnitPriceBook", any(), any(), any(), any())
        PowerMockito.doReturn(isTieredPrice).when(bizConfigThreadLocalCacheService, "isOpenPriceBookProductTieredPrice", any())
        PowerMockito.doReturn(Lists.newArrayList()).when(availableRangeUtils, "findMinTieredPriceData", any())
        //PowerMockito.doReturn(sortedData).when(baseRealPriceService, "sortedByPriorityAndLastModiTime", any())
        //PowerMockito.doNothing().when(baseRealPriceService, "saveDefaultHighPriorityData", any(), any())
        //PowerMockito.doNothing().when(baseRealPriceService, "processCurrencyConvert", any(), any(), any())
        //PowerMockito.doNothing().when(baseRealPriceService, "fillAttrPriceBookLines", any(), any(), anyBoolean())
        //PowerMockito.doNothing().when(baseRealPriceService, "saveDefaultHighPriorityData", any(), any())
        when:
        baseRealPriceService.getHighPriorityData(fullProduct, priceBookProductIds, null, null, isEnforcePriority, true, mcCurrency)
        then:
        1 == 1
        where:
        accountId | partnerId | amount              | priceBookId | priceBookFlag | availableRangeFlag | isMultiUnitPriceBook | isTieredPrice | isEnforcePriority | objectData                   | sortedData          | priceBookProductIds        | mcCurrency | effectivePriceBookProductList
        "xxx"     | "xxx"     | new BigDecimal("1") | "xxxx"      | true          | true               | false                | true          | false             | getObjectDataDocument("xxx") | getObjectData("id") | Lists.newArrayList("xxxx") | "CNY"      | getObjectDataList(["_id", "pricebook_id", "mc_currency", "last_modified_time"], 2)
        "xxx"     | "xxx"     | new BigDecimal("1") | "xxxx"      | true          | true               | false                | true          | false             | getObjectDataDocument("xxx") | getObjectData("id") | Lists.newArrayList("xxxx") | "CNY"      | getObjectDataList(["pricebook_id", "mc_currency", "last_modified_time"], 2)
        "xxx"     | "xxx"     | new BigDecimal("1") | "xxxx"      | true          | true               | false                | false         | false             | getObjectDataDocument("xxx") | getObjectData("id") | Lists.newArrayList("xxxx") | "CNY"      | getObjectDataList(["_id", "pricebook_id", "mc_currency", "last_modified_time"], 2)
        "xxx"     | "xxx"     | new BigDecimal("1") | "xxxx"      | true          | false              | true                 | false         | true              | getObjectDataDocument("xxx") | getObjectData("id") | null                       | null       | getObjectDataList(["_id", "pricebook_id", "mc_currency", "last_modified_time"], 2)
        "xxx"     | "xxx"     | new BigDecimal("1") | "xxxx"      | true          | false              | true                 | true          | false             | getObjectDataDocument("xxx") | getObjectData("id") | null                       | null       | null
    }

    def "fillObjectDataWithRefObject"() {
        given:
        def arg = new RealPriceModel.Arg()
        def baseRealPriceService = new BaseRealPriceService(arg, user, true, true)
        PowerMockito.field(BaseRealPriceService.class, "SERVICE_FACADE").set(baseRealPriceService, SERVICE_FACADE)
        PowerMockito.doReturn(null).when(SERVICE_FACADE, "findObject", any(), any())
        PowerMockito.doNothing().when(SERVICE_FACADE, "fillObjectDataWithRefObject", any(), any(), any())
        when:
        Whitebox.invokeMethod(baseRealPriceService, "fillObjectDataWithRefObject", user, getObjectDataList(["_id"], 1))
        then:
        1 == 1
    }

    def "matchMultiUnit"() {
        given:
        def arg = new RealPriceModel.Arg()
        def baseRealPriceService = new BaseRealPriceService(arg, user, true, true)
        List<RealPriceModel.FullProduct> prodList = getFullProductList("xxx", new BigDecimal("1")) as List<RealPriceModel.FullProduct>
        PowerMockito.field(BaseRealPriceService.class, "bizConfigThreadLocalCacheService").set(baseRealPriceService, bizConfigThreadLocalCacheService)
        PowerMockito.when(bizConfigThreadLocalCacheService.isPriceBookEnabled(any())).thenReturn(true)
        PowerMockito.when(bizConfigThreadLocalCacheService.isOpenMultiUnitPriceBook(any())).thenReturn(true)
        PowerMockito.when(GrayUtil.isMultiUnitDecimal(any())).thenReturn(true)
        when:
        Whitebox.invokeMethod(baseRealPriceService, "matchMultiUnit", getObjectDataList(["_id", "unit_id", "conversion_ratio"], 1), prodList.get(0), getObjectData("actual_unit", "selling_price", "pricebook_sellingprice", "pricebook_price", "ceiling_price", "floor_price"))
        then:
        1 == 1
    }

    def "getMultiUnitDataList"() {
        given:
        def arg = new RealPriceModel.Arg()
        arg.setFullProductList(getFullProductList("xxx", new BigDecimal(1)) as List<RealPriceModel.FullProduct>)
        def baseRealPriceService = new BaseRealPriceService(arg, user, true, true)
        def multiUnitService = PowerMockito.mock(MultiUnitService)
        PowerMockito.field(BaseRealPriceService.class, "multiUnitService").set(baseRealPriceService, multiUnitService)
        PowerMockito.doReturn(null).when(multiUnitService, "getMultiUnitDataByProductIds", any(), any())
        when:
        Whitebox.invokeMethod(baseRealPriceService, "getMultiUnitDataList")
        then:
        1 == 1
    }

    def "fillAttrPriceBookLines"() {
        given:
        def arg = new RealPriceModel.Arg()
        RealPriceModel.FullProduct fullProduct = new RealPriceModel.FullProduct()
        fullProduct.setProductId("xxx")
        fullProduct.setAmount(new BigDecimal(1))
        fullProduct.setUnit("1")
        fullProduct.setRowId("xxx")
        Map<String, String> attrMap = new HashMap<>()
        attrMap.put("xxx", "xxx")
        fullProduct.setAttrMap(attrMap)
        def baseRealPriceService = new BaseRealPriceService(arg, user, true, true)
        Whitebox.setInternalState(baseRealPriceService, "productInfoList", getObjectDataList(["_id", "pricing_attribute_ids"], 1))
        List<IObjectData> attrPriceBookLinesList = getObjectDataList(["_id", "price_book_ids", "product_id", "attribute_price_book_id", "attribute_price_book_id__r", "selling_price", "pricing_method", "discount"], 1)
        List<IObjectData> incrementAttrPriceBookLinesList = getObjectDataList(["_id", "price_book_ids", "product_id", "attribute_price_book_id", "attribute_price_book_id__r", "selling_price", "pricing_method", "discount"], 1)
        Map<String, List<String>> attrGroup = new HashMap()
        attrGroup.put("xxx", attrGroupValue)
        attrPriceBookLinesList.get(0).set("attribute_group", attrGroup)
        Map<String, List<String>> incrementAttrGroup = new HashMap()
        incrementAttrGroup.put("xxx", incrementAttrGroupValue)
        incrementAttrPriceBookLinesList.get(0).set("attribute_group", incrementAttrGroupValue)
        Map<String, List<IObjectData>> productAttrPriceBookLinesMap = Maps.newHashMap();
        productAttrPriceBookLinesMap.put("xxx", attrPriceBookLinesList)
        Map<String, List<IObjectData>> productIncrementAttrPriceBookLinesMap = Maps.newHashMap();
        productIncrementAttrPriceBookLinesMap.put("xxx", attrPriceBookLinesList)
        Whitebox.setInternalState(baseRealPriceService, "productAttrPriceBookLinesMap", productAttrPriceBookLinesMap)
        Whitebox.setInternalState(baseRealPriceService, "productIncrementAttrPriceBookLinesMap", productIncrementAttrPriceBookLinesMap)
        PowerMockito.field(BaseRealPriceService.class, "availableRangeUtils").set(baseRealPriceService, availableRangeUtils)
        PowerMockito.doReturn(Lists.newArrayList("xxx")).when(availableRangeUtils, "castList", any(), any())
        when:
        Whitebox.invokeMethod(baseRealPriceService, "fillAttrPriceBookLines", fullProduct, getObjectData("pricebook_id"), true)
        then:
        1 == 1
        where:
        attrGroupValue            | incrementAttrGroupValue
        Lists.newArrayList("xxx") | Lists.newArrayList("xx")
        Lists.newArrayList("xx")  | Lists.newArrayList("xxx")
    }

    def "sortedByPriorityAndLastModiTime"() {
        given:
        def arg = new RealPriceModel.Arg()
        List<IObjectData> list = getObjectDataList(["_id", "price_book_priority", "last_modified_time"], 2)
        def baseRealPriceService = new BaseRealPriceService(arg, user, true, true)
        PowerMockito.field(BaseRealPriceService.class, "bizConfigThreadLocalCacheService").set(baseRealPriceService, bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenPriceBookProductTieredPrice", any())
        PowerMockito.field(BaseRealPriceService.class, "availableRangeUtils").set(baseRealPriceService, availableRangeUtils)
        PowerMockito.doReturn(list).when(availableRangeUtils, "findMinTieredPriceData", any())
        when:
        Whitebox.invokeMethod(baseRealPriceService, "sortedByPriorityAndLastModiTime", list)
        then:
        1 == 1
    }

    def "getTopPriorityPriceBookProducts"() {
        given:
        def arg = new RealPriceModel.Arg()
        List<IObjectData> list = getObjectDataList(["_id", "price_book_priority"], 2)
        def baseRealPriceService = new BaseRealPriceService(arg, user, true, true)
        when:
        Whitebox.invokeMethod(baseRealPriceService, "getTopPriorityPriceBookProducts", list)
        then:
        1 == 1
    }

    def "findStandardProductList"() {
        given:
        def arg = new RealPriceModel.Arg()
        def baseRealPriceService = new BaseRealPriceService(arg, user, true, true)
        PowerMockito.when(GrayUtil.skipEditFunction(any())).thenReturn(true)
        PowerMockito.field(BaseRealPriceService.class, "SERVICE_FACADE").set(baseRealPriceService, SERVICE_FACADE)
        PowerMockito.doReturn(getObjectDataList(["_id", "life_status"], 1)).when(SERVICE_FACADE, "findObjectDataByIdsIgnoreAll", any(), any(), any())
        when:
        List<IObjectData> list = baseRealPriceService.findStandardProductList(Lists.newArrayList("xxx"))
        then:
        list != null
    }

    def "processSameTieredPrice"() {
        given:
        def arg = new RealPriceModel.Arg()
        RealPriceModel.FullProduct fullProduct = new RealPriceModel.FullProduct()
        fullProduct.setPriceBookProductId("xxx")
        fullProduct.setPrice(new BigDecimal(1))
        fullProduct.setDiscount("100")
        def baseRealPriceService = new BaseRealPriceService(arg, user, true, true)
        when:
        Whitebox.invokeMethod(baseRealPriceService, "processSameTieredPrice", fullProduct, getObjectData("_id", "price", "discount"), true, true)
        then:
        1 == 1
    }

    def "fillFullProductPricebookId"() {
        given:
        def arg = new RealPriceModel.Arg()
        RealPriceModel.FullProduct fullProduct = new RealPriceModel.FullProduct()
        def baseRealPriceService = new BaseRealPriceService(arg, user, true, true)
        //PowerMockito.doReturn(getObjectData("pricebook_id")).when(baseRealPriceService, "sortedByPriorityAndLastModiTime", any())
        when:
        Whitebox.invokeMethod(baseRealPriceService, "fillFullProductPricebookId", fullProduct, getObjectDataList(["_id"], 1), Lists.newArrayList("xxx"))
        then:
        1 == 1
    }

    def "findEffectiveDataForMultiUnitPriceBook"() {
        given:
        def arg = new RealPriceModel.Arg()
        RealPriceModel.FullProduct fullProduct = new RealPriceModel.FullProduct()
        fullProduct.setProductId("xxx")
        fullProduct.setUnit("xxx")
        fullProduct.setPriceBookId("xxxx")
        def baseRealPriceService = new BaseRealPriceService(arg, user, true, true)
        Whitebox.setInternalState(baseRealPriceService, "standardPriceBookProductList", getObjectDataList(["product_id"], 1))
        when:
        Whitebox.invokeMethod(baseRealPriceService, "findEffectiveDataForMultiUnitPriceBook", getObjectDataList(["_id", "is_pricing", "unit_id"], 1), fullProduct, getObjectDataList(["_id", "pricebook_id"], 1), "xxxxx")
        then:
        1 == 1
    }

    def "processCurrencyConvert"() {
        given:
        def arg = new RealPriceModel.Arg()
        IObjectData objectData = getObjectData("selling_price", "pricebook_price", "pricebook_sellingprice", "ceiling_price", "floor_price")
        def baseRealPriceService = new BaseRealPriceService(arg, user, true, true)
        PowerMockito.when(MtCurrentUtil.changePriceToCurrency(any(), any(), any(), any())).thenReturn(new BigDecimal(10))
        when:
        Whitebox.invokeMethod(baseRealPriceService, "processCurrencyConvert", objectData, "xxx", "xxx")
        then:
        1 == 1
    }

    def "getProductByPriceBookList"() {
        given:
        def arg = new RealPriceModel.Arg()
        def baseRealPriceService = new BaseRealPriceService(arg, user, true, true)
        def objectData = new ObjectData();
        objectData.setId("xxx")
        objectData.set("is_standard", isStandard)
        objectData.set("active_status", activeStatus)
        def priceBookList = Lists.newArrayList()
        priceBookList.add(objectData)
        PowerMockito.when(GrayUtil.finalFillRefObject(any())).thenReturn(true)
        PowerMockito.field(BaseRealPriceService.class, "priceBookCommonService").set(baseRealPriceService, priceBookCommonService)
        PowerMockito.doReturn(objectData).when(priceBookCommonService, "getStandardPriceBook", any())
        PowerMockito.field(BaseRealPriceService.class, "availableRangeUtils").set(baseRealPriceService, availableRangeUtils)
        PowerMockito.doReturn(getObjectDataList(["_id"], 1)).when(availableRangeUtils, "getProductByPriceBookList", any(), any(), any(), any(), any(), anyBoolean(), anyBoolean(), anyBoolean())
        PowerMockito.doReturn(true).when(availableRangeUtils, "isRealPriceConstraintMode", any(), any())
        when:
        Whitebox.invokeMethod(baseRealPriceService, "getProductByPriceBookList", priceBookList, Lists.newArrayList(), Lists.newArrayList(), 13453534534L, true, getObjectData("_id"))
        then:
        1 == 1
        where:
        isStandard | activeStatus
        true       | "1"
        false      | "1"
        false      | "0"
    }

    def "filterPriceBookByCurrency"() {
        given:
        def arg = new RealPriceModel.Arg()
        def baseRealPriceService = new BaseRealPriceService(arg, user, true, true)
        PowerMockito.when(GrayUtil.needFilterCurrency(any())).thenReturn(true)
        when:
        Whitebox.invokeMethod(baseRealPriceService, "filterPriceBookByCurrency", user, getObjectDataList(["_id"], 1), "xxx")
        then:
        1 == 1
    }

    def "getSaleContractLineInfo"() {
        given:
        def arg = new RealPriceModel.Arg()
        def baseRealPriceService = new BaseRealPriceService(arg, user, true, true)
        List<RealPriceModel.FullProduct> productList = Lists.newArrayList()
        RealPriceModel.FullProduct fullProduct = new RealPriceModel.FullProduct()
        fullProduct.setSaleContractLineId("xxx")
        productList.add(fullProduct)
        PowerMockito.field(BaseRealPriceService.class, "metaDataFindServiceExt").set(baseRealPriceService, metaDataFindServiceExt)
        PowerMockito.doReturn(buildQueryResult()).when(metaDataFindServiceExt, "findBySearchQueryWithFields", any(), any(), any(), any(), anyBoolean())
        PowerMockito.field(BaseRealPriceService.class, "availableRangeUtils").set(baseRealPriceService, availableRangeUtils)
        PowerMockito.doReturn(true).when(availableRangeUtils, "isDetailConstraintMode", any(), any())
        when:
        Whitebox.invokeMethod(baseRealPriceService, "getSaleContractLineInfo", user, "copy", productList, getObjectData("sale_contract_id", "object_describe_api_name"), getObjectData("_id"))
        then:
        1 == 1
    }

    def "contractConstraintFillPriceBookId"() {
        given:
        def arg = new RealPriceModel.Arg()
        def baseRealPriceService = new BaseRealPriceService(arg, user, true, true)
        PowerMockito.field(BaseRealPriceService.class, "bizConfigThreadLocalCacheService").set(baseRealPriceService, bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenContractConstraintMode", any())
        RealPriceModel.FullProduct fullProduct = new RealPriceModel.FullProduct()
        fullProduct.setSaleContractLineId("xxx")
        def saleContractLineMap = Maps.newHashMap()
        saleContractLineMap.put("xxx", getObjectData("_id", "price_book_id"))
        Whitebox.setInternalState(baseRealPriceService, "saleContractLineMap", saleContractLineMap)
        when:
        Whitebox.invokeMethod(baseRealPriceService, "contractConstraintFillPriceBookId", user, fullProduct)
        then:
        1 == 1
    }

    def "collectStratifiedPriceData"() {
        given:
        def arg = new RealPriceModel.Arg()
        def baseRealPriceService = new BaseRealPriceService(arg, user, true, true)
        RealPriceModel.FullProduct fullProduct = new RealPriceModel.FullProduct()
        PowerMockito.field(BaseRealPriceService.class, "bizConfigThreadLocalCacheService").set(baseRealPriceService, bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenStratifiedPricing", any())
        PowerMockito.doReturn(false).when(bizConfigThreadLocalCacheService, "isOpenMultiUnitPriceBook", any())
        def baseProductList = getObjectDataList(["_id", "pricebook_id", "product_id", "actual_unit", "start_count", "end_count"], 1)
        Whitebox.setInternalState(baseRealPriceService, "baseProductList", baseProductList)
        when:
        Whitebox.invokeMethod(baseRealPriceService, "stratifiedPriceHandler", fullProduct, getObjectData("is_stratified_pricing", "start_count", "end_count", "pricebook_id", "actual_unit"))
        then:
        1 == 1
    }

    def getObjectData(String... str) {
        List<String> list = Lists.newArrayList("conversion_ratio", "selling_price", "pricebook_sellingprice", "pricebook_price", "ceiling_price", "floor_price")
        IObjectData objectData = new ObjectData();
        for (String s : str) {
            if ("selling_data_define_type" == s) {
                objectData.set(s, "sys")
            } else if (list.contains(s)) {
                objectData.set(s, new BigDecimal(10))
            } else if ("object_describe_api_name" == s) {
                objectData.set("object_describe_api_name", "SalesOrderObj")
            } else if ("discount" == s) {
                objectData.set(s, 100)
            } else if ("is_stratified_pricing" == s) {
                objectData.set(s, true)
            } else if ("start_count" == s) {
                objectData.set(s, 1)
            } else if ("end_count" == s) {
                objectData.set(s, 100)
            } else {
                objectData.set(s, "xxx");
            }
        }
        return objectData
    }

    def getObjectDataDocument(String str) {
        def objectDataDocument = ObjectDataDocument.of(new HashMap<String, String>(str: str))
        return objectDataDocument
    }

    def getFullProductList(String productId, BigDecimal amount) {
        if (StringUtils.isEmpty(productId)) {
            return new ArrayList<>()
        } else {
            List<RealPriceModel.FullProduct> productList = Lists.newArrayList()
            RealPriceModel.FullProduct fullProduct = new RealPriceModel.FullProduct()
            fullProduct.setProductId(productId)
            fullProduct.setAmount(amount)
            fullProduct.setUnit("1")
            fullProduct.setRowId("xxx")
            productList.add(fullProduct)
            return productList
        }
    }

    def getObjectDataList(List<String> fieldList, int size) {
        def objectDataList = new ArrayList<ObjectData>();
        for (i in 0..<size) {
            def objectData = new ObjectData();
            fieldList.each {
                if ("price_book_ids" == it) {
                    objectData.set(it, Lists.newArrayList("xxx"))
                } else if ("attribute_group" == it) {
                    Map<String, List<String>> map = new HashMap()
                    map.put("xxx", Lists.newArrayList("xxx"))
                    objectData.set(it, map)
                } else if ("pricing_method" == it) {
                    objectData.set(it, "specify_discount")
                } else if ("price_book_priority" == it) {
                    objectData.set(it, 10)
                } else if ("last_modified_time" == it) {
                    objectData.set(it, 132423424243L)
                } else if ("life_status" == it) {
                    objectData.set(it, "normal")
                } else if ("is_pricing" == it) {
                    objectData.set(it, true)
                } else {
                    objectData.set(it, "xxx")
                }
            }
            objectData.setTenantId(tenantId)
            objectDataList.add(objectData)
        }
        objectDataList
    }

    def buildQueryResult() {
        QueryResult queryResult = new QueryResult();
        queryResult.setData(getObjectDataList(["account_id", "_id"], 1));
        queryResult
    }

    void initSpringContext() {
        def stubApplicationContext = Stub(ApplicationContext)
        def util = new SpringContextUtil()
        util.setApplicationContext(stubApplicationContext)
        Map<Class, Object> map = new HashMap<>()
        stubApplicationContext.getBean(_ as Class) >> { Class requiredType ->
            map.computeIfAbsent(requiredType, { key ->
                Stub(requiredType)
            })
        }

        Map<String, Object> map2 = new HashMap<>()
        stubApplicationContext.getBean(_ as String, _ as Class) >> { String name, Class requiredType ->
            map2.computeIfAbsent(name, { key ->
                Stub(requiredType)
            })
        }
    }

}
