package sfa.predefine.bizvalidator.validator

import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyCoreServiceImpl
import com.facishare.crm.sfa.utilities.util.PricePolicyUtils
import com.facishare.crm.util.DomainPluginDescribeExt
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam
import com.facishare.paas.metadata.util.SpringUtil
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.reflect.Whitebox
import org.springframework.context.ApplicationContext
import spock.lang.Shared
import spock.lang.Specification

@PowerMockIgnore(["org.mockito.*"])
@PrepareForTest([PricePolicyUtils.class])
@SuppressStaticInitializationFor(["com.facishare.crm.sfa.utilities.util.PricePolicyUtils"])
class FindPricePolicyConfigTest extends Specification {
    @Shared
    ApplicationContext applicationContext

    private MockedStatic<SpringUtil> mockedSpringUtil

    def setup() {
        applicationContext = Mockito.mock(ApplicationContext.class)

        mockedSpringUtil = Mockito.mockStatic(SpringUtil.class)
        mockedSpringUtil.when(SpringUtil.&getContext).thenReturn(applicationContext)
    }

    def cleanup() {
        mockedSpringUtil?.close()
    }

    def "测试mergeFieldMap方法"() {
        given: "两个Map，一个作为源Map，另一个作为目标Map"
        Map<String, Set<String>> sourceFields = new HashMap<>()
        Map<String, Set<String>> targetFields = new HashMap<>()

        // 初始化源Map和目标Map
        sourceFields = [
                "field1": ["value1", "value2"] as Set,
                "field2": ["value3"] as Set
        ]
        targetFields = [
                "field1": ["existingValue"] as Set,
                "field3": ["value4", "value5"] as Set
        ]

        when: "调用mergeFieldMap方法"
        PricePolicyUtils.mergeFieldMap(sourceFields, targetFields)

        then: "验证目标Map是否正确合并了源Map中的字段和值"
        targetFields == [
                "field1": ["existingValue", "value1", "value2"] as Set,
                "field3": ["value4", "value5"] as Set,
                "field2": ["value3"] as Set
        ] as Map
    }

    def "should merge source field map into target field map"() {
        given:
        def sourceFields = [
                "key1": ["value1", "value2"] as Set,
                "key2": ["value3"] as Set
        ]
        def targetFields = [
                "key1": ["value3"] as Set,
                "key3": ["value4"] as Set
        ]

        when:
        PricePolicyUtils.mergeFieldMap(sourceFields, targetFields)

        then:
        targetFields == [
                "key1": ["value3", "value1", "value2"] as Set,
                "key2": ["value3"] as Set,
                "key3": ["value4"] as Set
        ]
    }

    def "should not change target field map when source field map is empty"() {
        given:
        def sourceFields = [:]
        def targetFields = [
                "key1": ["value1", "value2"] as Set,
                "key2": ["value3"] as Set
        ]

        when:
        PricePolicyUtils.mergeFieldMap(sourceFields, targetFields)

        then:
        targetFields == [
                "key1": ["value1", "value2"] as Set,
                "key2": ["value3"] as Set
        ]
    }

    def "should not change target field map when source field map values are empty"() {
        given:
        def sourceFields = [
                "key1": [] as Set,
                "key2": [] as Set
        ]
        def targetFields = [
                "key1": ["value1", "value2"] as Set,
                "key2": ["value3"] as Set
        ]

        when:
        PricePolicyUtils.mergeFieldMap(sourceFields, targetFields)

        then:
        targetFields == [
                "key1": ["value1", "value2"] as Set,
                "key2": ["value3"] as Set
        ]
    }

/**
 * 去掉此测试用例
 */
//    def "should handle null source and target field maps"() {
//        given:
//        def sourceFields = null
//        def targetFields = null
//
//        when:
//        PricePolicyUtils.mergeFieldMap(sourceFields, targetFields)
//
//        then:
//        targetFields == null
//    }


//    def "Test renameVirtualField method"() {
//        given:
//        def pluginParam = new DomainPluginParam() // 根据你的需求创建相应的参数对象
//        def map = fieldMap
//        def pricePolicyCoreServiceImpl = PowerMockito.spy(new PricePolicyCoreServiceImpl())
//        def domainPluginDescribeExt = Whitebox.newInstance(DomainPluginDescribeExt)
//        Whitebox.setInternalState(domainPluginDescribeExt, "defaultDetailFieldMapping", ["quantity"      : "quantity",
//                                                                                         "product_price" : PricePolicyConstants.PRODUCT_PRICE,
//                                                                                         "product_amount": PricePolicyConstants.PRODUCT_AMOUNT])
//        Whitebox.setInternalState(domainPluginDescribeExt, "objectFieldMapping", ["product_amount": PricePolicyConstants.PRODUCT_AMOUNT])
//        PowerMockito.stub(PowerMockito.method(DomainPluginDescribeExt.class, "of", String.class, DomainPluginParam.class))
//                .toReturn(domainPluginDescribeExt)
//
//        when:
//        Map result = Whitebox.invokeMethod(pricePolicyCoreServiceImpl, "renameVirtualField", map, pluginParam)
//        then:
//        // 检查返回的结果是否是 Map 类型
//        result instanceof Map
//
//        // 检查 Map 中的键值对数量是否正确
//        result.size() == size
//
//
//        // 检查包含虚拟字段的对象是否正确处理
//        result.containsKey(masterApiname) == masterContains
//        result.containsKey(detailApiname) == detailContains
//        // 检查包含虚拟字段的对象的字段是否正确添加
//        result.get(masterApiname) == masterFieldsResult as Set
//        result.get(detailApiname) == detailFieldResult as Set
//        where:
//        masterApiname   | detailApiname          | fieldMap                                                                                                                                    || masterContains || detailContains || size || masterFieldsResult                                         || detailFieldResult
//        "SalesOrderObj" | "SalesOrderProductObj" | ["SalesOrderObj": ["Field1", "Field2", "virtual_amount"] as Set, "SalesOrderProductObj": ["Field3", "virtual_amount", "Field4"] as Set]     || true           || true           || 2    || ["Field1", "Field2", "virtual_amount", "product_amount"]   || ["Field3", "virtual_amount", "Field4", "quantity", "product_price"]
//        "SalesOrderObj" | "SalesOrderProductObj" | ["SalesOrderObj": ["Field1", "Field2", "virtual_subtotal"] as Set, "SalesOrderProductObj": ["Field3", "virtual_subtotal", "Field4"] as Set] || true           || true           || 2    || ["Field1", "Field2", "virtual_subtotal", "product_amount"] || ["Field3", "virtual_subtotal", "Field4", "quantity", "product_price"]
//        "SalesOrderObj" | "SalesOrderProductObj" | ["SalesOrderObj": ["Field1", "Field2", "virtual_price"] as Set, "SalesOrderProductObj": ["Field3", "virtual_price", "Field4"] as Set]       || true           || true           || 2    || ["Field1", "Field2", "virtual_price", "product_amount"]    || ["Field3", "virtual_price", "Field4", "quantity", "product_price"]
//        "SalesOrderObj" | "SalesOrderProductObj" | ["SalesOrderObj": ["Field1", "Field2", "virtual_discount"] as Set, "SalesOrderProductObj": ["Field3", "virtual_discount", "Field4"] as Set] || true           || true           || 2    || ["Field1", "Field2", "virtual_discount", "product_amount"] || ["Field3", "virtual_discount", "Field4", "quantity", "product_price"]
//        "SalesOrderObj" | "SalesOrderProductObj" | ["SalesOrderObj": ["Field1", "Field2"] as Set, "SalesOrderProductObj": ["Field3", "Field4"] as Set]                                         || false          || false          || 0    || null                                                       || null
//    }
}
