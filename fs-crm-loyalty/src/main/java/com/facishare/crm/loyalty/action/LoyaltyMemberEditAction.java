package com.facishare.crm.loyalty.action;

import com.facishare.crm.loyalty.LoyaltyPredefineObject;
import com.facishare.crm.loyalty.utils.MarketingMemberUtils;
import com.facishare.crm.loyalty.utils.ServiceFacadeUtils;
import com.facishare.crm.sfa.predefine.action.BaseEditSFAAction;
import com.facishare.crm.sfa.predefine.service.GoalValue.utilities.SearchUtil;
import com.facishare.crm.util.Safes;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LoyaltyMemberEditAction extends BaseEditSFAAction {

    private IObjectData oldIObjectData;

    @Override
    protected void before(Arg arg) {
        ObjectDataDocument objectDataDocument = arg.getObjectData();
        objectDataDocument.remove("grading_points");
        objectDataDocument.remove("consumer_points");
        objectDataDocument.remove("frozen_points");
        objectDataDocument.remove("frozen_grading_points");
        super.before(arg);
        oldIObjectData = Safes.first(dataList);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        updateList(objectData);
        return super.after(arg, result);
    }

    private void updateList(IObjectData currentData) {
        String oldPhone = oldIObjectData.get("phone", String.class);
        if (StringUtils.isEmpty(oldPhone)) {
            return;
        }
        String updateType = objectData.get("updateType", String.class);
        if (!"all".equals(updateType)) {
            return;
        }
        Map<String, Object> updateFields = new HashMap<>();
        updateFields.put("member_name", currentData.get("member_name"));
        updateFields.put("gender", currentData.get("gender"));
        updateFields.put("avatar", currentData.get("avatar"));
        updateFields.put("birthday", currentData.get("birthday"));
        updateFields.put("phone", currentData.get("phone"));
        ServiceFacadeUtils.foreach((query) -> {
            List<IFilter> filters = query.getFilters();
            SearchUtil.fillFilterNotEq(filters, IObjectData.ID, currentData.getId());
            SearchUtil.fillFilterEq(filters, "phone", oldPhone);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(actionContext.getUser(), LoyaltyPredefineObject.LoyaltyMember.getApiName(), query);
            if (queryResult != null && !CollectionUtils.isEmpty(queryResult.getData())) {
                serviceFacade.batchUpdateWithMap(actionContext.getUser(), queryResult.getData(), updateFields);
            }
            return queryResult;
        });
        updateMarketingMember(currentData);
    }

    private void updateMarketingMember(IObjectData member) {
        String tenantId = actionContext.getTenantId();
        IObjectDescribe describe = serviceFacade.findObject(tenantId, "MemberObj");
        if (describe == null) {
            return;
        }
        //关联字段，现在只有手机号
        String relatedField = "phone";
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), relatedField, member.get(relatedField));
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIfFillExtendFieldInfo(User.systemUser(tenantId), describe, query,
                false, false, false, false);
        if (queryResult.getData() == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return;
        }
        Map<String, Object> targetUpdateFields = MarketingMemberUtils.getMarketingUpdateFieldsByLoyalty(member);
        serviceFacade.updateWithMap(actionContext.getUser(), Safes.first(queryResult.getData()), targetUpdateFields);
    }
}
