package com.facishare.crm.loyalty.action;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.loyalty.LoyaltyPredefineObject;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyPointsService;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyI18nKey;
import com.facishare.crm.sfa.predefine.action.AbstractSimpleStandardAction;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.facishare.paas.metadata.util.SpringUtil;
import org.springframework.util.StringUtils;

public class LoyaltyMemberMemberChangePointsAction extends AbstractSimpleStandardAction {

    private static final LoyaltyPointsService loyaltyPointsService = SpringUtil.getContext().getBean(LoyaltyPointsService.class);

    @Override
    protected ObjectAction getButton() {
        return ObjectAction.MEMBER_CHANGE_POINTS;
    }

    @Override
    protected Result doAct(Arg arg) {
        JSONObject param = arg.getArgs();
        String tenantId = actionContext.getTenantId();
        String action = param.getString("form_action");
        String pointTypeId = param.getString("form_point_type_id");
        String poolId = param.getString("form_point_pool_id");
        long value = param.getLongValue("form_points");

        IObjectData pointType = serviceFacade.findObjectData(User.systemUser(tenantId), pointTypeId, LoyaltyPredefineObject.LoyaltyPointType.getApiName());
        boolean isQualifying = Boolean.TRUE.equals(pointType.get("is_qualifying", Boolean.class));
        if (StringUtils.isEmpty(poolId) && !isQualifying) {
            throw new ValidateException(
                    I18N.text(LoyaltyI18nKey.MISSING_PARAMETERS,
                            I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(LoyaltyPredefineObject.LoyaltyPointPool.getApiName()))
                    )
            );
        }
        Loyalty.PointsOperationParam operationParam = new Loyalty.PointsOperationParam();
        operationParam.setTenantId(tenantId);
        operationParam.setUniqueId(serviceFacade.generateId());
        operationParam.setMemberId(arg.getObjectDataId());
        operationParam.setDesc(param.getString("form_desc"));
        operationParam.setValue(Math.abs(value));
        if ("add".equals(action)) {
            if (isQualifying) {
                operationParam.setType(Loyalty.PointsOperationParam.Type.LEVEL_POINTS);
            } else {
                operationParam.setType(Loyalty.PointsOperationParam.Type.CONSUMER_POINTS_TO_MEMBER);
            }
        } else if ("reduce".equals(action)) {
            if (isQualifying) {
                operationParam.setType(Loyalty.PointsOperationParam.Type.LEVEL_POINTS);
                operationParam.setValue(-Math.abs(value));
            } else {
                operationParam.setType(Loyalty.PointsOperationParam.Type.CONSUMER_POINTS_TO_POOL);
            }
        }
        operationParam.setPointPoolId(poolId);
        operationParam.setPointTypeId(pointTypeId);
        loyaltyPointsService.operate(operationParam);
        return new AbstractSimpleStandardAction.Result();
    }
}
