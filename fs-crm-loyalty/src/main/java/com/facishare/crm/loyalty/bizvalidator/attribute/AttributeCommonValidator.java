package com.facishare.crm.loyalty.bizvalidator.attribute;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.constants.ExtendedAttributeConstants;
import com.facishare.crm.loyalty.LoyaltyPredefineObject;
import com.facishare.crm.loyalty.utils.ProgramCheckUtils;
import com.facishare.crm.sfa.predefine.bizvalidator.Validator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.ExceptionUtils;
import com.facishare.crm.sfa.utilities.util.i18n.LoyaltyKeyUtil;
import com.facishare.crm.sfa.utilities.validator.RebatePolicyValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class AttributeCommonValidator implements Validator {
    @Override
    public void validate(ValidatorContext context) {
        //校验必填字段
        checkRequireField(context);
        checkJsonFormat(context);
        checkNameUnique(context);
    }

    private void checkNameUnique(ValidatorContext context) {
        IObjectData objectData = context.getObjectData();
        String programId = objectData.get(ExtendedAttributeConstants.PROGRAM_ID, String.class);
        //判断id是否为空
        if (ObjectAction.CREATE.equals(context.getAction())) {
            ProgramCheckUtils.checkUniqueInProgram(context.getUser(), objectData.getName(), LoyaltyPredefineObject.ExtendedAttribute.getApiName(),
                    ExtendedAttributeConstants.PROGRAM_ID, programId, null);
        } else {
            ProgramCheckUtils.checkUniqueInProgram(context.getUser(), objectData.getName(), LoyaltyPredefineObject.ExtendedAttribute.getApiName(),
                    ExtendedAttributeConstants.PROGRAM_ID, programId, objectData.getId());
        }
    }

    private void checkJsonFormat(ValidatorContext context) {
        IObjectData objectData = context.getObjectData();
        String options = objectData.get(ExtendedAttributeConstants.OPTIONS, String.class);
        if (StringUtils.isNotBlank(options)) {
            JSONArray jsonArray = ExceptionUtils.trySupplier(() -> JSON.parseArray(options));
            objectData.set(ExtendedAttributeConstants.OPTIONS, jsonArray);
        }
        String fieldDescribe = objectData.get(ExtendedAttributeConstants.FIELD_DESCRIBE, String.class);
        JSONObject jsonObject = ExceptionUtils.trySupplier(() -> JSON.parseObject(fieldDescribe));
        objectData.set(ExtendedAttributeConstants.FIELD_DESCRIBE, jsonObject);
    }

    private void checkRequireField(ValidatorContext context) {
        List<String> requireFieldList = Lists.newArrayList(IObjectData.NAME,
                ExtendedAttributeConstants.PROGRAM_ID,
                ExtendedAttributeConstants.ATTR_TYPE,
                ExtendedAttributeConstants.DATA_TYPE,
                ExtendedAttributeConstants.ATTR_API_NAME,
                ExtendedAttributeConstants.FIELD_DESCRIBE);
        IObjectData objectData = context.getObjectData();
        RebatePolicyValidator.validateEmptyField(objectData, context.getDescribeApiName(), requireFieldList);
        //获取数据类型，判断是否是多选或者单选，如果是则校验选项
        String dataType = objectData.get(ExtendedAttributeConstants.DATA_TYPE, String.class);
        if (ExtendedAttributeConstants.DataType.SELECT_ONE.getDataType().equals(dataType) || ExtendedAttributeConstants.DataType.SELECT_MANY.getDataType().equals(dataType)) {
            RebatePolicyValidator.validateEmptyField(objectData, context.getDescribeApiName(), Lists.newArrayList(ExtendedAttributeConstants.OPTIONS));
        }
        //获取apiName，
        String attrApiName = objectData.get(ExtendedAttributeConstants.ATTR_API_NAME, String.class);
        //使用正则表达式校验attrApiName其格式，必须是数字字母下划线
        if (!attrApiName.matches("^[a-zA-Z]([a-zA-Z0-9]|_[a-zA-Z0-9])*__c$")) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_ATTR_API_NAME_ERROR));
        }
    }
}
