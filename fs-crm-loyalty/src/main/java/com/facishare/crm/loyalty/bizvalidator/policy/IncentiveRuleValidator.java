package com.facishare.crm.loyalty.bizvalidator.policy;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.IncentiveMetricConstants;
import com.facishare.crm.constants.IncentivePolicyConstants;
import com.facishare.crm.constants.IncentivePolicyRuleConstants;
import com.facishare.crm.loyalty.LoyaltyPredefineObject;
import com.facishare.crm.loyalty.model.IncentiveActionContext;
import com.facishare.crm.loyalty.model.RuleActionModel;
import com.facishare.crm.loyalty.service.incentive.rule.TransferRule;
import com.facishare.crm.loyalty.utils.IncentivePolicyRuleUtils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.bizvalidator.Validator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RuleWhere;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.ExceptionUtils;
import com.facishare.crm.sfa.utilities.constant.AggregateRuleConstants;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.i18n.LoyaltyKeyUtil;
import com.facishare.crm.sfa.utilities.validator.PricePolicyValidator;
import com.facishare.crm.sfa.utilities.validator.RebatePolicyValidator;
import com.facishare.crm.util.DescribeI18NUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

public class IncentiveRuleValidator implements Validator {
    private final TransferRule transferRule = SpringUtil.getContext().getBean(TransferRule.class);
    private static final int ACTION_MAX_SIZE = 10;
    private final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);

    @Override
    public void validate(@NotNull ValidatorContext context) {
        Map<String, List<IObjectData>> detailObjectData = context.getDetailObjectData();
        List<IObjectData> objectDataList = detailObjectData.get(LoyaltyPredefineObject.IncentivePolicyRule.getApiName());
        if (CollectionUtils.empty(objectDataList)) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_INCENTIVE_RULE_NOT_EMPTY));
        }
        for (IObjectData objectData : objectDataList) {
            validateCondition(context, objectData);
            validateAction(context, objectData);
        }
    }

    private void validateAction(ValidatorContext context, IObjectData objectData) {
        String actionJson = objectData.get(IncentivePolicyRuleConstants.ACTION, String.class);
        if (StringUtils.isBlank(actionJson)) {
            RebatePolicyValidator.emptyThrow(objectData, Tuple.of(IncentivePolicyRuleConstants.ACTION, DescribeI18NUtils.getFieldNameKey(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), IncentivePolicyRuleConstants.ACTION)));
        }
        List<RuleActionModel> actionModelList = ExceptionUtils.trySupplier(() -> JSON.parseArray(actionJson, RuleActionModel.class));
        detailValidate(actionModelList, context);
        if (actionModelList.size() > ACTION_MAX_SIZE) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_RULE_ACTION_SIZE_LIMIT_ERROR, ACTION_MAX_SIZE));
        }
        nameRepeatValidate(actionModelList);
        objectData.set(IncentivePolicyRuleConstants.ACTION, actionModelList);
    }

    private void nameRepeatValidate(List<RuleActionModel> actionModelList) {
        //判断是否有名字重复
        Set<String> nameSet = Sets.newHashSet();
        for (RuleActionModel ruleActionModel : actionModelList) {
            if (nameSet.contains(ruleActionModel.getName())) {
                throw new ValidateException(String.format(I18N.text(LoyaltyKeyUtil.SFA_INCENTIVE_ACTION_NAME_REPEAT), ruleActionModel.getName()));
            }
            nameSet.add(ruleActionModel.getName());
        }
    }

    private void detailValidate(List<RuleActionModel> actionModelList, ValidatorContext context) {
        if (CollectionUtils.empty(actionModelList)) {
            throw new ValidateException(String.format("%s%s", I18N.text(DescribeI18NUtils.getFieldNameKey(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), IncentivePolicyRuleConstants.ACTION)), I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR)));
        }
        IObjectData objectData = context.getObjectData();
        String usedObjectApiName = objectData.get(IncentivePolicyConstants.USED_OBJECT_API_NAME, String.class);
        IObjectDescribe object = serviceFacade.findObject(context.getUser().getTenantId(), usedObjectApiName);
        for (RuleActionModel ruleActionModel : actionModelList) {
            if (StringUtils.isAnyEmpty(ruleActionModel.getName(), ruleActionModel.getActionType(), ruleActionModel.getActiveStatus())) {
                throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_METRIC_PARAMETER_ERROR_PLEASE_CHECK));
            }
            String actionType = ruleActionModel.getActionType();
            Consumer<IncentiveActionContext> ruleActionModelStringBiConsumer = ActionTypeValidator.TYPE_MAP.get(actionType);
            if (ruleActionModelStringBiConsumer == null) {
                throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_METRIC_PARAMETER_ERROR_PLEASE_CHECK));
            }
            IncentiveActionContext incentiveActionContext = new IncentiveActionContext();
            incentiveActionContext.setRuleActionModel(ruleActionModel);
            incentiveActionContext.setTenantId(context.getUser().getTenantId());
            String programId = objectData.get(IncentivePolicyConstants.PROGRAM_ID, String.class);
            incentiveActionContext.setProgramId(programId);
            incentiveActionContext.setUsedObjectDescribe(object);
            ruleActionModelStringBiConsumer.accept(incentiveActionContext);
        }
    }

    private void validateCondition(@NotNull ValidatorContext context, @NotNull IObjectData objectData) {
        String condition = objectData.get(IncentivePolicyRuleConstants.CONDITION_CONTENT, String.class);
        if (StringUtils.isBlank(condition)) {
            return;
        }
        List<RuleWhere> ruleWhereList = ExceptionUtils.trySupplier(() -> JSON.parseArray(condition, RuleWhere.class));
        if (CollectionUtils.empty(ruleWhereList)) {
            return;
        }
        filterValidate(ruleWhereList);
        metricExistValidate(context, ruleWhereList);
        List<RuleWhere> transferRuleWhere = transferRule.transferStandRuleWhere(context.getUser().getTenantId(), ruleWhereList);
        onlyOneAplValidate(context, transferRuleWhere);
        filterNameValidate(context, transferRuleWhere);
        havePolicyAttributeValidate(transferRuleWhere);
        objectData.set(IncentivePolicyRuleConstants.CONDITION_CONTENT, ruleWhereList);
        objectData.set(PricePolicyConstants.RuleField.RULE_CONDITION, transferRuleWhere);
    }

    private void havePolicyAttributeValidate(List<RuleWhere> transferRuleWhere) {
        Set<String> removeApiNames = Sets.newHashSet(LoyaltyPredefineObject.ExtendedAttributePolicy.getApiName(),
                LoyaltyPredefineObject.ExtendedAttributeMemberIncentive.getApiName());
        boolean havePolicyAttribute = transferRuleWhere.stream()
                .map(RuleWhere::getFilters)
                .filter(CollectionUtils::notEmpty)
                .flatMap(Collection::stream)
                .anyMatch(filter -> IncentivePolicyRuleConstants.FieldNameType.ATTRIBUTE.getType().equals(filter.getFilterType()) && removeApiNames.contains(filter.getBindObjectApiName()));
        if (havePolicyAttribute && transferRuleWhere.size() > 1) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_RULE_CONDITION_ONLY_ONE_POLICY_ATTRIBUTE));
        }
    }

    private void filterNameValidate(ValidatorContext context, List<RuleWhere> transferRuleWhere) {
        Map<String, Set<String>> objectFields = Maps.newHashMap();
        Set<String> aggregateValueIds = Sets.newHashSet();
        transferRuleWhere.stream()
                .flatMap(x -> x.getFilters().stream())
                .forEach(filtersBean -> {
                    if (filtersBean.isFromMetric()) {
                        return;
                    }
                    if (IncentivePolicyRuleConstants.FieldNameType.FIELD.getType().equals(filtersBean.getFieldNameType())) {
                        objectFields.computeIfAbsent(filtersBean.getObjectApiName(), v -> Sets.newHashSet()).add(filtersBean.getFieldName());
                    } else if (IncentivePolicyRuleConstants.FieldNameType.AGGREGATE.getType().equals(filtersBean.getFieldNameType())) {
                        aggregateValueIds.add(filtersBean.getFieldName());
                    }
                });
        PricePolicyValidator.validateObjectFields(context.getUser().getTenantId(), objectFields);
        List<IObjectData> objectDataList = IncentivePolicyRuleUtils.validateReferenceIds(context.getUser().getTenantId(), SFAPreDefineObject.AggregateRule.getApiName(), aggregateValueIds);
        for (IObjectData objectData : objectDataList) {
            String ruleType = objectData.get(AggregateRuleConstants.Field.RULE_TYPE, String.class);
            if (!AggregateRuleConstants.RuleTypeEnum.INCENTIVE.getValue().equals(ruleType)) {
                throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_METRIC_AGGREGATE_RULE_TYPE_ERROR));
            }
        }
    }

    /**
     * apl只能有一个，并且apl函数和其他类型互斥
     *
     * @param context
     * @param transferRuleWhere 转换后的规则
     */
    private void onlyOneAplValidate(ValidatorContext context, @NotNull List<RuleWhere> transferRuleWhere) {
        Map<String, Set<String>> bindObjectApiName = Maps.newHashMap();
        for (RuleWhere ruleWhere : transferRuleWhere) {
            List<RuleWhere.FiltersBean> filters = ruleWhere.getFilters();
            for (RuleWhere.FiltersBean filter : filters) {
                if (IncentivePolicyRuleConstants.FieldNameType.APL.getType().equals(filter.getFieldNameType())) {
                    if (StringUtils.isBlank(filter.getBindObjectApiName())) {
                        throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_INCENTIVE_RULE_APL_BINDOBJECTAPINAME_EMPTY));
                    }
                    if (StringUtils.isBlank(filter.getFieldName())) {
                        throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_INCENTIVE_RULE_APL_FIELDNAME_EMPTY));
                    }
                    bindObjectApiName.computeIfAbsent(filter.getBindObjectApiName(), v -> Sets.newHashSet()).add(filter.getFieldName());
                }
            }
        }
        if (MapUtils.isEmpty(bindObjectApiName)) {
            return;
        }
        bindObjectApiName.forEach((bingObjectApiName, functionApiNameList) -> {
            List<IUdefFunction> functionByApiNames = serviceFacade.getFunctionLogicService().findFunctionByApiNames(context.getUser(), Lists.newArrayList(functionApiNameList), bingObjectApiName);
            if (CollectionUtils.empty(functionByApiNames) || functionApiNameList.size() != functionByApiNames.size()) {
                throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_INCENTIVE_RULE_APL_FUNCTION_NOT_EXIST));
            }
        });
    }

    private void metricExistValidate(@NotNull ValidatorContext context, List<RuleWhere> ruleWhereList) {
        User user = context.getUser();
        Set<String> metricIdsByFilter = IncentivePolicyRuleUtils.getIdsByType(ruleWhereList, IncentivePolicyRuleConstants.FieldNameType.METRIC.getType());
        List<IObjectData> objectDataList = IncentivePolicyRuleUtils.validateReferenceIds(user.getTenantId(), LoyaltyPredefineObject.IncentiveMetric.getApiName(), metricIdsByFilter);
        for (IObjectData objectData : objectDataList) {
            if (!IncentiveMetricConstants.ActiveStatusType.ENABLE.getActiveStatusType().equals(objectData.get(IncentiveMetricConstants.ACTIVE_STATUS, String.class))) {
                throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_METRIC_NOT_ACTIVE));
            }
        }
    }

    private void filterValidate(@NotNull List<RuleWhere> ruleWhereList) {
        for (RuleWhere ruleWhere : ruleWhereList) {
            List<RuleWhere.FiltersBean> filters = ruleWhere.getFilters();
            if (CollectionUtils.empty(filters)) {
                throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_INCENTIVE_RULE_NOT_EFFECTIVE));
            }
            for (RuleWhere.FiltersBean filter : filters) {
                if (StringUtils.isAnyEmpty(filter.getFieldNameType(), filter.getFieldName(), filter.getOperator())) {
                    throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_INCENTIVE_RULE_NOT_EFFECTIVE));
                }
            }
        }
    }

}
