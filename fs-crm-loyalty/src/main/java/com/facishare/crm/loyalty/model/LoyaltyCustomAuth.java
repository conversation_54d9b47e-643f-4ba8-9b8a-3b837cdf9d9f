package com.facishare.crm.loyalty.model;

import com.facishare.crm.loyalty.utils.FileUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

import java.util.List;

public interface LoyaltyCustomAuth {

    @Data
    class TokenInfo {
        private String tenantId;
        private Boolean queryRelatedFieldByToken;
        private String relatedField;
        private String relatedValue;

        /**
         * 关联字段映射db字段名
         * 默认按照手机号关联
         */
        public String mapping() {
            return "phone";
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class CommonParam extends TokenInfo {
        private String token;
        private Integer limit;
        private Integer offset;

        // 非入参，用于接口上下文使用
        /**
         * 营销通会员
         */
        private IObjectData marketingMember;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class MemberParam extends CommonParam {
        private String programId;

        public ObjectDataDocument toObjectDataDocument() {
            long currentTime = System.currentTimeMillis();
            ObjectDataDocument dataDocument = new ObjectDataDocument();
            dataDocument.put("registration_time", currentTime);
            dataDocument.put("tier_start_time", currentTime);
            dataDocument.put("program_id", programId);
            dataDocument.put("grading_points", 0L);
            dataDocument.put("consumer_points", 0L);
            dataDocument.put("frozen_points", 0L);
            dataDocument.put("frozen_grading_points", 0L);
            return dataDocument;
        }
    }

    @Data
    @NoArgsConstructor
    class MemberAndTier {
        private IObjectData member;
        private IObjectData tier;

        public MemberAndTier(IObjectData member, IObjectData tier) {
            if (member != null) {
                String phone = member.get("phone", String.class);
                if (phone != null && phone.length() >= 7) {
                    StringBuilder sb = new StringBuilder(phone);
                    sb.setCharAt(3, '*');
                    sb.setCharAt(4, '*');
                    sb.setCharAt(5, '*');
                    sb.setCharAt(6, '*');
                    member.set("phone", sb.toString());
                }
            }
            this.member = member;
            if (tier != null) {
                String icon = tier.get("tier_icon_c", String.class);
                if (!StringUtils.isEmpty(icon)) {
                    tier.set("tier_icon_c", FileUtils.buildFilePath(tier.getTenantId(), icon));
                }
            }
            this.tier = tier;
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class PointsRecordsQueryParam extends CommonParam {
        private String programId;
        private List<String> changeTypeList;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class ProgramQueryParam extends CommonParam {
        private String programId;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class StoreQueryParam extends CommonParam {
        private String name;
        private String storeId;
        private String programId;
        private List<String> gis;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class OperateParam extends CommonParam {
        private String programId;
        private String operateId;
        private String type;
        private Long value;
        private String desc;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class QueryOperationParam extends CommonParam {
        private String operateId;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class CouponMemberQueryParam extends CommonParam {
        /**
         * 语种
         */
        private String language;
        /**
         * 会员id
         */
        private String memberId;
        /**
         * 查询类型
         */
        private String queryType;
        private String programId;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class InternetParam extends CommonParam {
        private String programId;
        private String redirectUrl;
    }
}
