package com.facishare.crm.loyalty.model;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper=false)
public class RuleActionModel implements Serializable {
    @JSONField(name = "name")
    @JsonProperty("name")
    private String name;
    @JSONField(name = "active_status")
    @JsonProperty("active_status")
    private String activeStatus;
    @JSONField(name = "action_type")
    @JsonProperty("action_type")
    private String actionType;
    @JSONField(name = "point_category_id")
    @JsonProperty("point_category_id")
    private String pointCategoryId;
    @JSONField(name = "point_category_name")
    @JsonProperty("point_category_name")
    private String pointCategoryName;
    @JSONField(name = "change_type")
    @JsonProperty("change_type")
    private String changeType;
    @J<PERSON>NField(name = "value_type")
    @JsonProperty("value_type")
    private String valueType;
    @JSONField(name = "value")
    @JsonProperty("value")
    private String value;
    @JSONField(name = "value_content")
    @JsonProperty("value_content")
    private String valueContent;
    @JSONField(name = "apl_info")
    @JsonProperty("apl_info")
    private AplInfoBean aplInfo;
    @JSONField(name = "member_level_id")
    @JsonProperty("member_level_id")
    private String memberLevelId;
    @JSONField(name = "member_level_name")
    @JsonProperty("member_level_name")
    private String memberLevelName;
    @JSONField(name = "metric_info")
    @JsonProperty("metric_info")
    private MetricInfoBean metricInfo;
    @JSONField(name = "value_metric_info")
    @JsonProperty("value_metric_info")
    private Map<String, ValueMetricInfo> valueMetricInfo;
    @JSONField(name = "pool_id")
    @JsonProperty("pool_id")
    private String poolId;
    @JSONField(name = "pool_name")
    @JsonProperty("pool_name")
    private String poolName;
    @JSONField(name = "coupon_plan_id")
    @JsonProperty("coupon_plan_id")
    private String couponPlanId;
    @JSONField(name = "coupon_plan_name")
    @JsonProperty("coupon_plan_name")
    private String couponPlanName;
    @JSONField(name = "coupon_id")
    @JsonProperty("coupon_id")
    private String couponId;
    @JSONField(name = "coupon_name")
    @JsonProperty("coupon_name")
    private String couponName;
    @JSONField(name = "quantity")
    @JsonProperty("quantity")
    private Integer quantity;
    @JSONField(name = "beneficiary")
    @JsonProperty("beneficiary")
    private String beneficiary;


    @Data
    public static class ValueMetricInfo implements Serializable {
        @JSONField(name = "ext_default_value")
        @JsonProperty("ext_default_value")
        private String extDefaultValue;
        @JSONField(name = "return_type")
        @JsonProperty("return_type")
        private String returnType;
        @JSONField(name = "ext_api_name")
        @JsonProperty("ext_api_name")
        private String extApiName;
        @JSONField(name = "bind_object_api_name")
        @JsonProperty("bind_object_api_name")
        private String bindObjectApiName;
        @JSONField(name = "attr_id")
        @JsonProperty("attr_id")
        private String attrId;

    }

    @Data
    public static class AplInfoBean implements Serializable {
        @JSONField(name = "name")
        @JsonProperty("name")
        private String name;
        @JSONField(name = "api_name")
        @JsonProperty("api_name")
        private String apiName;
        @JSONField(name = "return_type")
        @JsonProperty("return_type")
        private String returnType;
        @JSONField(name = "name_space")
        @JsonProperty("name_space")
        private String nameSpace;
        @JSONField(name = "bind_object")
        @JsonProperty("bind_object")
        private String bindObject;
    }

    @Data
    public static class MetricInfoBean implements Serializable {
        @JSONField(name = "type")
        @JsonProperty("type")
        private String type;
        @JSONField(name = "info")
        @JsonProperty("info")
        private JSONObject info;
        @JSONField(name = "metric_id")
        @JsonProperty("metric_id")
        private String metricId;
        @JSONField(name = "metric_name")
        @JsonProperty("metric_name")
        private String metricName;
    }
}
