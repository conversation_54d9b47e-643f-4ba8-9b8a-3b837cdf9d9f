package com.facishare.crm.loyalty.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@EqualsAndHashCode(callSuper=false)
public class RuleActionRenderModel extends RuleActionModel{
    @JSONField(name = "name_label")
    @JsonProperty("name_label")
    private String nameLabel;
    @JSONField(name = "active_status_label")
    @JsonProperty("active_status_label")
    private String activeStatusLabel;
    @JSONField(name = "active_status_option_label")
    @JsonProperty("active_status_option_label")
    private String activeStatusOptionLabel;
    @JSONField(name = "action_type_label")
    @JsonProperty("action_type_label")
    private String actionTypeLabel;
    @JSONField(name = "action_type_option_label")
    @JsonProperty("action_type_option_label")
    private String actionTypeOptionLabel;
    @JSONField(name = "change_type_label")
    @JsonProperty("change_type_label")
    private String changeTypeLabel;
    @J<PERSON>NField(name = "change_type_option_label")
    @JsonProperty("change_type_option_label")
    private String changeTypeOptionLabel;
    @JSONField(name = "value_type_label")
    @JsonProperty("value_type_label")
    private String valueTypeLabel;
    @JSONField(name = "value_type_option_label")
    @JsonProperty("value_type_option_label")
    private String valueTypeOptionLabel;
    @JSONField(name = "value_label")
    @JsonProperty("value_label")
    private String valueLabel;
    @JSONField(name = "point_category_name_label")
    @JsonProperty("point_category_name_label")
    private String pointCategoryNameLabel;
    @JSONField(name = "member_level_name_label")
    @JsonProperty("member_level_name_label")
    private String memberLevelNameLabel;
    @JSONField(name = "apl_info_label")
    @JsonProperty("apl_info_label")
    private String aplInfoLabel;
    @JSONField(name = "metric_name_label")
    @JsonProperty("metric_name_label")
    private String metricNameLabel;
    @JSONField(name = "pool_name_label")
    @JsonProperty("pool_name_label")
    private String poolNameLabel;
    @JSONField(name = "coupon_plan_name_label")
    @JsonProperty("coupon_plan_name_label")
    private String couponPlanNameLabel;
    @JSONField(name = "coupon_name_label")
    @JsonProperty("coupon_name_label")
    private String couponNameLabel;
    @JSONField(name = "quantity_label")
    @JsonProperty("quantity_label")
    private String quantityLabel;
    @JSONField(name = "beneficiary_label")
    @JsonProperty("beneficiary_label")
    private String beneficiaryLabel;
    @JSONField(name = "beneficiary_option_label")
    @JsonProperty("beneficiary_option_label")
    private String beneficiaryOptionLabel;
}
