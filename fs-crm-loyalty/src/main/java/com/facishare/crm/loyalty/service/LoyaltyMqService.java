package com.facishare.crm.loyalty.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.loyalty.model.Loyalty;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.fxiaoke.rocketmq.producer.DefaultTopicMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class LoyaltyMqService {

    @Resource(name = "loyaltyMq")
    private AutoConfMQProducer producer;

    /**
     * 成本组织计算积分
     */
    public void send(String tenantId, String poolId) {
        Loyalty.OrgTask orgTask = new Loyalty.OrgTask();
        orgTask.setTenantId(tenantId);
        orgTask.setPoolId(poolId);
        DefaultTopicMessage msg = new DefaultTopicMessage("org", JSONObject.toJSONBytes(orgTask));
        SendResult result = producer.send(msg);
        if (result.getSendStatus() != SendStatus.SEND_OK) {
            log.error("send mq failed. " + result.getSendStatus());
        }
    }

    /**
     * 计算会员评定日
     */
    public void send(String tenantId, List<String> memberIdList) {
        Loyalty.MemberEvaluationDate memberEvaluationDate = new Loyalty.MemberEvaluationDate();
        memberEvaluationDate.setTenantId(tenantId);
        memberEvaluationDate.setMemberIdList(memberIdList);
        DefaultTopicMessage msg = new DefaultTopicMessage("memberEvaluationDate", JSONObject.toJSONBytes(memberEvaluationDate));
        SendResult result = producer.send(msg);
        if (result.getSendStatus() != SendStatus.SEND_OK) {
            log.error("send mq failed. " + result.getSendStatus());
        }
    }

    /**
     * 会员等级触发优惠卷
     */
    public void sendMqForTierUpgrade(List<IObjectData> memberList) {
        for (IObjectData data : memberList) {
            Loyalty.MemberUpgrade memberUpgrade = new Loyalty.MemberUpgrade();
            memberUpgrade.setTenantId(data.getTenantId());
            memberUpgrade.setMemberId(data.getId());
            memberUpgrade.setAfterTierId(data.get("tier_id", String.class));
            DefaultTopicMessage msg = new DefaultTopicMessage("memberUpgrade", JSON.toJSONBytes(memberUpgrade));
            SendResult result = producer.send(msg);
            if (result.getSendStatus() != SendStatus.SEND_OK) {
                log.error("send mq failed. " + result.getSendStatus());
            }
        }
    }
}
