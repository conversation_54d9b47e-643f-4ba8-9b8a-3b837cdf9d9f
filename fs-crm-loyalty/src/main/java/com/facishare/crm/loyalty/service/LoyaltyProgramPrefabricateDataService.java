package com.facishare.crm.loyalty.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.ExtendedAttributeConstants;
import com.facishare.crm.constants.IncentiveCategoryConstants;
import com.facishare.crm.constants.IncentiveMetricConstants;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * LoyaltyProgramPrefabricateDataService
 *
 * <AUTHOR> 2024/07/31
 */
@Service
@Slf4j
public class LoyaltyProgramPrefabricateDataService {
    @Resource
    ObjectDataProxy dataProxy;
    @Resource
    ServiceFacade serviceFacade;

    public void createPrefabricatedData(IObjectData program, User user) {
        createPrefabricateIncentiveCategory(program, user);
        createPrefabricateIncentiveMetric(program, user);
        createPrefabricateExtendedAttribute(program, user);
    }

    private void createPrefabricateIncentiveCategory(IObjectData program, User user) {
        List<ObjectDataDocument> objectDataDocuments = buildIncentiveCategories(program);
        IActionContext context = ActionContextExt.of(user, RequestContextManager.getContext())
                .setNotValidate(true)
                .getContext();
        try {
            dataProxy.bulkCreate(objectDataDocuments.stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList()), true, context);
        } catch (MetadataServiceException e) {
            log.warn("insertIncentiveCategoryList error,user:{},dataList:{}", user, objectDataDocuments.stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList()), e);

        }

    }

    private void createPrefabricateIncentiveMetric(IObjectData program, User user) {
        List<ObjectDataDocument> objectDataDocuments = buildIncentiveMetrics(program);
        serviceFacade.bulkSaveObjectData(objectDataDocuments.stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList()), user);
    }

    private void createPrefabricateExtendedAttribute(IObjectData program, User user) {
        List<ObjectDataDocument> objectDataDocuments = buildExtendedAttributes(program);
        serviceFacade.bulkSaveObjectData(objectDataDocuments.stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList()), user);
    }

    private List<ObjectDataDocument> buildExtendedAttributes(IObjectData program) {
        List<ObjectDataDocument> extendedAttributes = parseJsonToObjectDataDocument(ExtendedAttributeConstants.PREFABRICED_DATA);
        for (ObjectDataDocument data : extendedAttributes) {
            data.put(DBRecord.ID, IdGenerator.get());
            data.put(IObjectData.OWNER, program.getOwner());
            data.put(ExtendedAttributeConstants.ATTR_API_NAME, randomApiName());
            data.put(ExtendedAttributeConstants.PROGRAM_ID, program.getId());
            String fieldDescribeTemplate = data.get(ExtendedAttributeConstants.FIELD_DESCRIBE).toString();
            data.put(ExtendedAttributeConstants.FIELD_DESCRIBE, String.format(fieldDescribeTemplate,
                    data.get(ExtendedAttributeConstants.ATTR_API_NAME),
                    data.get(ExtendedAttributeConstants.ATTR_API_NAME),
                    data.get(IObjectData.NAME)));
            data.put(Tenantable.TENANT_ID, program.getTenantId());
        }
        return extendedAttributes;
    }
    /**
     * 生成随机的API名称，用于扩展属性字段命名
     * 注意：此方法仅用于生成非安全敏感的字段名称，不涉及密码或令牌等安全信息
     */
    @SuppressWarnings({"findsecbugs:PREDICTABLE_RANDOM", "java:S2245"})
    private String randomApiName() {
        return "field_" +
                RandomStringUtils.randomAlphabetic(5) +
                "__c";
    }

    private List<ObjectDataDocument> buildIncentiveMetrics(IObjectData program) {
        List<ObjectDataDocument> objectDataDocuments = parseJsonToObjectDataDocument(IncentiveMetricConstants.PREFABRICATED_DATA);
        for (ObjectDataDocument data : objectDataDocuments) {
            data.put(DBRecord.ID, IdGenerator.get());
            data.put(IncentiveMetricConstants.PROGRAM_ID, program.getId());
            data.put(IObjectData.OWNER, program.getOwner());
            data.put(Tenantable.TENANT_ID, program.getTenantId());
        }
        return objectDataDocuments;
    }

    private List<ObjectDataDocument> buildIncentiveCategories(IObjectData program) {
        List<ObjectDataDocument> objectDataDocuments = parseJsonToObjectDataDocument(IncentiveCategoryConstants.PREFABRICATED_DATA);
        Map<String, ObjectDataDocument> objectDataDocumentMap = Maps.newHashMap();
        for (ObjectDataDocument data : objectDataDocuments) {
            data.put(DBRecord.ID, IdGenerator.get());
            data.put(IncentiveCategoryConstants.PROGRAM_ID, program.getId());
            data.put(IObjectData.OWNER, program.getOwner());
            data.put(Tenantable.TENANT_ID, program.getTenantId());
            data.put(IncentiveCategoryConstants.PATH, data.getId());
            objectDataDocumentMap.put((String) data.get(IncentiveCategoryConstants.CODE), data);
        }
        for (Map.Entry<String, ObjectDataDocument> entry : objectDataDocumentMap.entrySet()) {
            ObjectDataDocument parent = entry.getValue();
            @SuppressWarnings("unchecked")
            List<String> children = (List<String>) parent.get("children");
            if (CollectionUtils.isNotEmpty(children)) {
                for (String child : children) {
                    ObjectDataDocument data = objectDataDocumentMap.get(child);
                    data.put(IncentiveCategoryConstants.PARENT_ID, parent.getId());
                    data.put(IncentiveCategoryConstants.PATH, parent.get(IncentiveCategoryConstants.PATH) + "." + data.getId());
                }
                parent.remove("children");
            }
        }
        return objectDataDocuments;
    }

    private List<ObjectDataDocument> parseJsonToObjectDataDocument(String jsonStr) {
        return JSON.parseArray(jsonStr, ObjectDataDocument.class);
    }
}
