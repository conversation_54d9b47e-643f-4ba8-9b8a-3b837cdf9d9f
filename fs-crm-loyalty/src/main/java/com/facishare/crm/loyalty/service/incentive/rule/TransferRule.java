package com.facishare.crm.loyalty.service.incentive.rule;

import com.esotericsoftware.minlog.Log;
import com.facishare.crm.constants.IncentiveMetricConstants;
import com.facishare.crm.constants.IncentivePolicyRuleConstants;
import com.facishare.crm.loyalty.LoyaltyPredefineObject;
import com.facishare.crm.loyalty.service.incentive.metric.IncentiveMetricRender;
import com.facishare.crm.loyalty.service.incentive.metric.IncentiveMetricTypeManger;
import com.facishare.crm.loyalty.utils.IncentivePolicyRuleUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RuleWhere;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class TransferRule {
    private static final IncentiveMetricTypeManger INCENTIVE_METRIC_TYPE_MANGER = SpringUtil.getContext().getBean(IncentiveMetricTypeManger.class);

    /**
     * 转成标准的规则，把指标内容，转成标准规则
     *
     * @param ruleWhereList 规则 where list
     * @return {@code List<RuleWhere>}
     */
    public List<RuleWhere> transferStandRuleWhere(String tenantId, List<RuleWhere> ruleWhereList) {
        if (CollectionUtils.empty(ruleWhereList)) {
            return Lists.newArrayList();
        }
        Map<String, IObjectData> idToMetricDataMap = queryMetricDataMap(tenantId, ruleWhereList);
        return doTransfer(idToMetricDataMap, ruleWhereList);
    }
    
    @SuppressWarnings("findsecbugs:BEAN_PROPERTY_INJECTION")
    private List<RuleWhere> doTransfer(Map<String, IObjectData> idToMetricDataMap, List<RuleWhere> ruleWhereList) {
        List<RuleWhere> standRuleWhereList = Lists.newArrayList();
        for (RuleWhere ruleWhere : ruleWhereList) {
            RuleWhere priceRuleWhere = new RuleWhere(Lists.newArrayList());
            for (RuleWhere.FiltersBean filter : ruleWhere.getFilters()) {
                RuleWhere.FiltersBean newFilter;
                if (IncentivePolicyRuleConstants.FieldNameType.METRIC.getType().equals(filter.getFieldNameType())) {
                    newFilter = getFilterByMetric(idToMetricDataMap, filter);
                } else {
                    newFilter = new RuleWhere.FiltersBean();
                    //复制为新的filter，不改变旧的filter
                    BeanUtils.copyProperties(filter, newFilter);
                }
                priceRuleWhere.getFilters().add(newFilter);
            }
            standRuleWhereList.add(priceRuleWhere);
        }
        return standRuleWhereList;
    }
    @SuppressWarnings("findsecbugs:BEAN_PROPERTY_INJECTION")
    private RuleWhere.FiltersBean getFilterByMetric(Map<String, IObjectData> idToMetricDataMap, RuleWhere.FiltersBean sourceFilter) {
        IObjectData metricData = idToMetricDataMap.get(sourceFilter.getFieldName());
        if (metricData != null) {
            RuleWhere.FiltersBean newFilter = new RuleWhere.FiltersBean();
            //复制为新的filter，不改变旧的filter
            BeanUtils.copyProperties(sourceFilter, newFilter);
            String metricType = metricData.get(IncentiveMetricConstants.METRIC_TYPE, String.class);
            IncentiveMetricRender render = INCENTIVE_METRIC_TYPE_MANGER.getRender(metricType);
            newFilter = render.getFiltersBean(metricData, newFilter);
            render.addInfoToSourceFilter(newFilter, sourceFilter);
            newFilter.setFromMetric(true);
            return newFilter;
        }
        Log.warn("metric data is null id is {}", sourceFilter.getFieldName());
        return null;
    }

    private static Map<String, IObjectData> queryMetricDataMap(String tenantId, List<RuleWhere> standRuleWhereList) {
        Set<String> metricIdsByFilter = IncentivePolicyRuleUtils.getIdsByType(standRuleWhereList, IncentivePolicyRuleConstants.FieldNameType.METRIC.getType());
        List<IObjectData> objectDataList = IncentivePolicyRuleUtils.validateReferenceIds(tenantId, LoyaltyPredefineObject.IncentiveMetric.getApiName(), metricIdsByFilter);
        return objectDataList.stream().collect(Collectors.toMap(IObjectData::getId, iObjectData -> iObjectData));
    }
}
