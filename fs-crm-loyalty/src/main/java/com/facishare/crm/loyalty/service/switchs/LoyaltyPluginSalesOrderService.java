package com.facishare.crm.loyalty.service.switchs;

import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.loyalty.model.LoyaltyI18nKeys;
import com.facishare.crm.loyalty.model.LoyaltyPlugin;
import com.facishare.crm.management.service.config.DefaultBizConfigProvider;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.DescribeWithSimplifiedChineseService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.CurrencyFieldDescribe;
import com.facishare.paas.metadata.impl.describe.LongTextFieldDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class LoyaltyPluginSalesOrderService extends DefaultBizConfigProvider {

    @Resource
    DescribeWithSimplifiedChineseService describeWithSimplifiedChineseService;
    @Resource
    LoyaltySwitchService loyaltySwitchService;

    @Override
    public String getConfigKey() {
        return ConfigType.LOYALTY_PLUGIN_SWITCH_SALES_ORDER.getKey();
    }

    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {
        String tenantId = user.getTenantId();
        if ("true".equals(oldValue)) {
            return;
        }
        //检测忠诚度开关是否开启
        if (!loyaltySwitchService.opened(tenantId)) {
            throw new ValidateException(I18N.text(LoyaltyI18nKeys.SFA_LOYALTY_NOT_ENABLED_LOYALTY_SWITCH));
        }
        try {
            pluginSwitchSalesOrder(user, SFAPreDefineObject.SalesOrder.getApiName());
        } catch (MetadataServiceException e) {
            log.error("会员开启插件失败", e);
            throw new ValidateException(I18N.text(LoyaltyI18nKeys.SFA_LOYALTY_PLUGIN_OPENING_FAILED));
        }
    }

    @Override
    public String getConfigValue(User user, String key) {
        ConfigType configType = ConfigType.LOYALTY_PLUGIN_SWITCH_SALES_ORDER;
        String res = configService.findTenantConfig(user, configType.getKey());
        if (StringUtils.isEmpty(res)) {
            return configType.getDefaultValue();
        }
        return res;
    }

    private void pluginSwitchSalesOrder(User user, String apiName) throws MetadataServiceException {
        String tenantId = user.getTenantId();
        //描述追加字段
        IObjectDescribe describe = describeWithSimplifiedChineseService.findByDescribeApiName(user, apiName);
        List<IFieldDescribe> fieldDescribeList = new ArrayList<>();
        if (!describe.containsField(LoyaltyPlugin.LOYALTY_AMOUNT)) {
            CurrencyFieldDescribe loyaltyAmount = new CurrencyFieldDescribe();
            loyaltyAmount.setApiName(apiName);
            loyaltyAmount.setApiName(LoyaltyPlugin.LOYALTY_AMOUNT);
            loyaltyAmount.setLabel("积分");// ignoreI18n
            loyaltyAmount.setDefineType("package");
            loyaltyAmount.setActive(true);
            loyaltyAmount.setIndex(true);
            loyaltyAmount.setDecimalPlaces(2);
            fieldDescribeList.add(loyaltyAmount);
        }
        if (!describe.containsField(LoyaltyPlugin.LOYALTY_DETAIL)) {
            LongTextFieldDescribe loyaltyDetail = new LongTextFieldDescribe();
            loyaltyDetail.setApiName(apiName);
            loyaltyDetail.setApiName(LoyaltyPlugin.LOYALTY_DETAIL);
            loyaltyDetail.setLabel("积分详情");// ignoreI18n
            loyaltyDetail.setDefineType("package");
            loyaltyDetail.setActive(true);
            loyaltyDetail.setExpressionType("json");
            fieldDescribeList.add(loyaltyDetail);
        }
        if (!CollectionUtils.isEmpty(fieldDescribeList)) {
            objectDescribeService.addFieldDescribe(describe, fieldDescribeList, new ActionContext(), IFieldDescribe.DEFINE_TYPE_PACKAGE);
        }

        // 修改订单公式
        IFieldDescribe upAmount = describe.getFieldDescribe("receivable_amount");
        String op = "+";
        if ("1".equals(configService.findTenantConfig(User.systemUser(tenantId), ConfigType.REBATE.getKey()))
                || "1".equals(configService.findTenantConfig(User.systemUser(tenantId), ConfigType.COUPON.getKey()))) {
            upAmount = describe.getFieldDescribe("paid_amount");
            op = "-";
        }
        if (!upAmount.getExpression().contains("loyalty_amount")) {
            upAmount.setExpression(upAmount.getExpression() + op + "$loyalty_amount$");
            List<IFieldDescribe> toUpdateFieldList = Lists.newArrayList();
            toUpdateFieldList.add(upAmount);
            serviceFacade.updateFieldDescribe(describe, toUpdateFieldList);
        }


        configService.upsertTenantConfig(User.systemUser(tenantId), ConfigType.LOYALTY_PLUGIN_SWITCH_SALES_ORDER.getKey(), "true", ConfigValueType.STRING);
    }

    public boolean opened(String tenantId) {
        return "true".equals(configService.findTenantConfig(User.systemUser(tenantId), ConfigType.LOYALTY_PLUGIN_SWITCH_SALES_ORDER.getKey()));
    }
}
