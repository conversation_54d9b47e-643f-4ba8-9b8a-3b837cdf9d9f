package com.facishare.crm.loyalty.service.switchs;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.loyalty.model.LoyaltyI18nKeys;
import com.facishare.crm.management.service.config.DefaultBizConfigProvider;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.service.LoyaltySwitch;
import com.facishare.crm.sfa.utilities.proxy.CRMMetaDataServiceProxy;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Slf4j
@Service
public class LoyaltySwitchService extends DefaultBizConfigProvider implements LoyaltySwitch {

    @Resource
    ServiceFacade serviceFacade;
    @Resource
    CRMMetaDataServiceProxy crmMetaDataServiceProxy;

    @Override
    public String getConfigKey() {
        return ConfigType.LOYALTY_SWITCH.getKey();
    }

    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {
        String tenantId = user.getTenantId();
        if (!serviceFacade.isExistObjectByApiName(tenantId, SFAPreDefineObject.Partner.getApiName())) {
            String param = I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(SFAPreDefineObject.Partner.getApiName()));
            throw new ValidateException(I18N.text(LoyaltyI18nKeys.SFA_LOYALTY_SWITCH_NOT_ENABLED, param, param));
        }
        if ("false".equals(oldValue)) {
            JSONObject body = new JSONObject();
            body.put("tenantIds", Lists.newArrayList(tenantId));
            body.put("module", "loyalty");
            JSONObject param = new JSONObject();
            param.put("userId", user.getUserId());
            body.put("param", param);
            crmMetaDataServiceProxy.refreshCommonModule(tenantId, body.toJSONString());
            configService.upsertTenantConfig(user, ConfigType.LOYALTY_SWITCH.getKey(), "opening", ConfigValueType.STRING);
        }
    }

    @Override
    public String getConfigValue(User user, String key) {
        ConfigType configType = ConfigType.LOYALTY_SWITCH;
        String res = configService.findTenantConfig(user, configType.getKey());
        if (StringUtils.isEmpty(res)) {
            return configType.getDefaultValue();
        }
        return res;
    }

    @Override
    public boolean opened(String tenantId) {
        return "true".equals(configService.findTenantConfig(User.systemUser(tenantId), ConfigType.LOYALTY_SWITCH.getKey()));
    }
}
