package com.facishare.crm.loyalty.utils;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Sets;

import java.util.Set;

public class LoyaltyUtils {

    public static boolean removeFallbackButton(IObjectData data) {
        String changeType = data.get(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TYPE, String.class);
        if (!"CONSUMER_POINTS_TO_MEMBER".equals(changeType) && !"CONSUMER_POINTS_TO_POOL".equals(changeType) && !"LEVEL_POINTS".equals(changeType)) {
            return true;
        }
        String fallback = data.get(LoyaltyConstants.LoyaltyMemberChangeRecords.IS_FALLBACK, String.class);
        if ("true".equals(fallback)) {
            return true;
        }
        return false;
    }

    public static Set<String> memberImportTemplateRemoveFields() {
        return Sets.newHashSet("tree_path");
    }
}
