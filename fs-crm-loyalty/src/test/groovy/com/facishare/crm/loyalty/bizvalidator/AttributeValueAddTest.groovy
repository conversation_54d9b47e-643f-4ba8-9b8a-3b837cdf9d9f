package com.facishare.crm.loyalty.bizvalidator

import com.facishare.crm.constants.ExtendedAttributeConstants
import com.facishare.crm.loyalty.bizvalidator.attribute.AttributeCommonValidator
import com.facishare.crm.loyalty.bizvalidator.attribute.AttributeValueValidator
import com.facishare.crm.loyalty.bizvalidator.attribute.UniqueValueValidator
import com.facishare.crm.loyalty.utils.IncentivePolicyRuleUtils
import com.facishare.crm.loyalty.utils.ProgramCheckUtils
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.predefine.service.pricepolicy.PolicyRuleServiceManager
import com.facishare.crm.sfa.predefine.service.pricepolicy.dao.PricePolicyTools
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil
import com.facishare.crm.sfa.utilities.util.i18n.LoyaltyKeyUtil
import com.facishare.crm.sfa.utilities.validator.PricePolicyValidator
import com.facishare.crm.util.DescribeI18NUtils
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.util.SpringUtil
import com.google.common.collect.Lists
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import org.springframework.web.context.support.XmlWebApplicationContext
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.eq

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([SpringUtil.class, UniqueValueValidator.class])
@PowerMockIgnore(["javax.management.*"])
@SuppressStaticInitializationFor([
        "com.facishare.paas.I18N",
        "com.facishare.paas.metadata.util.SpringUtil",
        "com.facishare.crm.sfa.utilities.validator.PricePolicyValidator",
        "com.facishare.crm.loyalty.utils.IncentivePolicyRuleUtils",
        "com.facishare.crm.loyalty.utils.ProgramCheckUtils"

])
class AttributeValueAddTest extends Specification {
    def setup() {
        PowerMockito.mockStatic(I18N)
        PowerMockito.mockStatic(SpringUtil)
        PowerMockito.mockStatic(PricePolicyValidator)
        PowerMockito.mockStatic(ProgramCheckUtils)
        PowerMockito.when(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR)).thenReturn("参数错误")
        PowerMockito.when(I18N.text(LoyaltyKeyUtil.SFA_ATTRIBUTE_UNIQUE_ERROR)).thenReturn("此高级属性已经存在记录值，不能重复添加")
        PowerMockito.when(I18N.text(DescribeI18NUtils.getFieldNameKey("ExtendedAttributeObj", "options"))).thenReturn("选项值")
        PowerMockito.when(I18N.text(SFAI18NKeyUtil.SFA_OBJECT_DISPLAY_NAME_NOTNULL)).thenReturn("%s 不能为空")
        PowerMockito.when(I18N.text(LoyaltyKeyUtil.SFA_ATTR_API_NAME_ERROR)).thenReturn("apiName格式不正确，只能包含字母下划线")
        mockSpringUtilsByClass([ServiceFacade.class, PolicyRuleServiceManager.class, PricePolicyTools.class, BizConfigThreadLocalCacheService.class])
        PowerMockito.doNothing().when(ProgramCheckUtils.class, "checkUniqueInProgram", any(), any(), any(), any(), any(), any())

    }

    def "attribute unique validator"() {
        given:
        def mockServiceFacade = mockServiceFacade()
        def uniqueValidator = PowerMockito.spy(new UniqueValueValidator())
        def user = User.systemUser("82681")
        def validatorContext = ValidatorContext.builder().user(user).objectData(new ObjectData(["_id": "123", "test__c": "456"])).describeApiName(objectApiName).build()
        def queryResult = new QueryResult()
        queryResult.setData(Lists.newArrayList(new ObjectData(["_id": "123", "test__c": "456"])))
        PowerMockito.doReturn(queryResult)
                .when(mockServiceFacade, "findBySearchQueryIgnoreAll", any(), eq(objectApiName), any())

        when:
        uniqueValidator.validate(validatorContext)

        then:
        def exception = thrown(ValidateException)
        exception.getMessage() == a

        where:
        objectApiName                         || a
        "ExtendedAttributeEventObj"           || "此高级属性已经存在记录值，不能重复添加"
        "ExtendedAttributeMemberObj"          || "此高级属性已经存在记录值，不能重复添加"
        "ExtendedAttributePolicyObj"          || "此高级属性已经存在记录值，不能重复添加"
        "ExtendedAttributeMemberIncentiveObj" || "此高级属性已经存在记录值，不能重复添加"


    }

    def "attribute not unique validator"() {
        given:
        def mockServiceFacade = mockServiceFacade()
        def uniqueValidator = PowerMockito.spy(new UniqueValueValidator())
        def user = User.systemUser("82681")
        def validatorContext = ValidatorContext.builder().user(user).objectData(new ObjectData(["_id": "123", "test__c": "456"])).describeApiName("ExtendedAttributeEventObj").build()
        def queryResult = new QueryResult()
        PowerMockito.when(mockServiceFacade.findBySearchQueryIgnoreAll(any(), any(), any())).thenReturn(queryResult)

        when:
        uniqueValidator.validate(validatorContext)

        then:
        notThrown(ValidateException)
    }

    ServiceFacade mockServiceFacade() {
        def mockServiceFacade = PowerMockito.mock(ServiceFacade)
        def mockXmlWebApplicationContext = PowerMockito.mock(XmlWebApplicationContext)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(mockXmlWebApplicationContext)
        PowerMockito.when(mockXmlWebApplicationContext.getBean(ServiceFacade.class)).thenReturn(mockServiceFacade)
        return mockServiceFacade
    }

    def mockSpringUtilsByClass(List<Class> typeArray) {
        def mockXmlWebApplicationContext = PowerMockito.mock(XmlWebApplicationContext)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(mockXmlWebApplicationContext)
        typeArray.forEach({ type ->
            def mockByType = PowerMockito.mock(type)
            PowerMockito.when(mockXmlWebApplicationContext.getBean(type)).thenReturn(mockByType)
        })
    }

    def "attribute common validator"() {
        given:
        def objectData = new ObjectData()
        objectData.set("name", "name")
        objectData.set("program_id", "program_id")
        objectData.set("attr_type", "attr_type")
        objectData.set("data_type", "data_type")
        objectData.set("attr_api_name", "test__c")
        objectData.set("field_describe", "{\"test__c\": {\"type\": \"text\", \"label\": \"sdfqweas\", \"api_name\": \"test__c\"}}")
        objectData.set("options", "[{\"label\":\"文本\",\"value\":\"text\"},{\"label\":\"日期\",\"value\":\"date\"}]")
        def validatorContext = ValidatorContext.builder().objectData(objectData).build()

        def validator = new AttributeCommonValidator()

        when:
        validator.validate(validatorContext)

        then:
        notThrown(ValidateException)
    }

    def "attribute common validator exception"() {
        given:
        def objectData = new ObjectData()
        objectData.set("name", "name")
        objectData.set("program_id", "program_id")
        objectData.set("attr_type", "attr_type")
        objectData.set("data_type", dataType)
        objectData.set("attr_api_name", "test__c")
        objectData.set("field_describe", fieldDescribe)
        objectData.set("options", options)
        def validatorContext = ValidatorContext.builder().objectData(objectData).describeApiName("ExtendedAttributeObj").build()
        def validator = new AttributeCommonValidator()

        when:
        validator.validate(validatorContext)

        then:
        def exception = thrown(ValidateException)
        exception.getMessage() == errorMessage

        where:
        dataType     | fieldDescribe                                                                           | options                                                                                 || errorMessage
        "select_one" | "{\"232323\": {\"type\": \"text\", \"label\": \"sdfqweas\", \"api_name\": \"232323\"}}" | ""                                                                                      || "选项值 不能为空"
        "select_one" | "{\"232323\": {\"type\": \"text\", \"label\": \"sdfqweas\", \"api_name\": \"232323\"}}" | "{\"232323\": {\"type\": \"text\", \"label\": \"sdfqweas\", \"api_name\": \"232323\"}}" || "参数错误"
        "select_one" | "123"                                                                                   | "{\"232323\": {\"type\": \"text\", \"label\": \"sdfqweas\", \"api_name\": \"232323\"}}" || "参数错误"

    }

    def "attribute validator apiName exception"() {
        given:
        def objectData = new ObjectData()
        objectData.set("name", "name")
        objectData.set("program_id", "program_id")
        objectData.set("attr_type", "attr_type")
        objectData.set("data_type", "text")
        objectData.set("attr_api_name", "232323")
        objectData.set("field_describe", "{\"232323\": {\"type\": \"text\", \"label\": \"sdfqweas\", \"api_name\": \"232323\"}}")

        def validatorContext = ValidatorContext.builder().objectData(objectData).describeApiName("ExtendedAttributeObj").build()
        def validator = new AttributeCommonValidator()

        when:
        validator.validate(validatorContext)

        then:
        def exception = thrown(ValidateException)
        exception.getMessage() == "apiName格式不正确，只能包含字母下划线"

    }

    def "attribute value validator"() {
        given:
        def objectData = new ObjectData()
        objectData.set("attr_id", "attr_id")
        objectData.set("object_describe_api_name", "ExtendedAttributeMemberObj")
        objectData.set("text_value", "1")
        def user = User.systemUser("82681")
        def validatorContext = ValidatorContext.builder().objectData(objectData).describeApiName("").user(user).build()
        def validator = new AttributeValueValidator()
        def objectDataList = [new ObjectData()]
        objectDataList[0].set("attr_type", ExtendedAttributeConstants.AttrType.MEMBER.getAttrType())
        objectDataList[0].set("data_type", "text")
        def queryResult = new QueryResult()
        queryResult.setData(objectDataList)
        PowerMockito.mockStatic(IncentivePolicyRuleUtils)
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), any(), any())).thenReturn(objectDataList)

        when:
        validator.validate(validatorContext)

        then:
        notThrown(ValidateException)
    }

    def "attribute programId same validate"() {
        given:
        def user = User.systemUser("82681")
        def validatorContext = ValidatorContext.builder().objectData(objectData).describeApiName(describeApiName).user(user).build()
        def validator = new AttributeValueValidator()
        PowerMockito.mockStatic(IncentivePolicyRuleUtils)
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq("ExtendedAttributeObj"), any())).thenReturn(attrObjectDataList)
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq("LoyaltyMemberObj"), any())).thenReturn(memberDataList)
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq("TransactionEventObj"), any())).thenReturn(eventDataList)
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq("IncentivePolicyObj"), any())).thenReturn(policyDataList)

        when:
        validator.validate(validatorContext)

        then:
        thrown(ValidateException)

        where:
        objectData                                           | describeApiName                       | attrObjectDataList                                                           | memberDataList  | eventDataList   | policyDataList
        getAttrMemberData()                                  | "ExtendedAttributeMemberObj"          | getAttrData(ExtendedAttributeConstants.AttrType.MEMBER.getAttrType())        | getResultData() | null            | null
        getAttrEventData()                                   | "ExtendedAttributeEventObj"           | getAttrData(ExtendedAttributeConstants.AttrType.EVENT.getAttrType())         | null            | getResultData() | null
        getAttrPolicyData()                                  | "ExtendedAttributePolicyObj"          | getAttrData(ExtendedAttributeConstants.AttrType.POLICY.getAttrType())        | null            | null            | getResultData()
        getAttrIncentivePolicyData()                         | "ExtendedAttributeMemberIncentiveObj" | getAttrData(ExtendedAttributeConstants.AttrType.MEMBER_POLICY.getAttrType()) | getResultData() | null            | getResultData()
        getCommonData("ExtendedAttributeMemberObj")          | "ExtendedAttributeMemberObj"          | getAttrData(ExtendedAttributeConstants.AttrType.MEMBER.getAttrType())        | null            | null            | null
        getCommonData("ExtendedAttributeEventObj")           | "ExtendedAttributeEventObj"           | getAttrData(ExtendedAttributeConstants.AttrType.EVENT.getAttrType())         | null            | null            | null
        getCommonData("ExtendedAttributePolicyObj")          | "ExtendedAttributePolicyObj"          | getAttrData(ExtendedAttributeConstants.AttrType.POLICY.getAttrType())        | null            | null            | null
        getCommonData("ExtendedAttributeMemberIncentiveObj") | "ExtendedAttributeMemberIncentiveObj" | getAttrData(ExtendedAttributeConstants.AttrType.MEMBER_POLICY.getAttrType()) | null            | null            | null
        getSelectOneData("ExtendedAttributeMemberObj")       | "ExtendedAttributeMemberObj"          | getAttrData(ExtendedAttributeConstants.AttrType.MEMBER.getAttrType())        | null            | null            | null
    }

    def "attribute programId same pass validator"() {
        given:
        def user = User.systemUser("82681")
        def validatorContext = ValidatorContext.builder().objectData(objectData).describeApiName(describeApiName).user(user).build()
        def validator = new AttributeValueValidator()
        PowerMockito.mockStatic(IncentivePolicyRuleUtils)
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq("ExtendedAttributeObj"), any())).thenReturn(attrObjectDataList)
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq("LoyaltyMemberObj"), any())).thenReturn(memberDataList)
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq("TransactionEventObj"), any())).thenReturn(eventDataList)
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq("IncentivePolicyObj"), any())).thenReturn(policyDataList)

        when:
        validator.validate(validatorContext)

        then:
        notThrown(ValidateException)

        where:
        objectData                   | describeApiName                       | attrObjectDataList                                                           | memberDataList      | eventDataList       | policyDataList
        getAttrMemberData()          | "ExtendedAttributeMemberObj"          | getAttrData(ExtendedAttributeConstants.AttrType.MEMBER.getAttrType())        | getResultDataSame() | null                | null
        getAttrEventData()           | "ExtendedAttributeEventObj"           | getAttrData(ExtendedAttributeConstants.AttrType.EVENT.getAttrType())         | null                | getResultDataSame() | null
        getAttrPolicyData()          | "ExtendedAttributePolicyObj"          | getAttrData(ExtendedAttributeConstants.AttrType.POLICY.getAttrType())        | null                | null                | getResultDataSame()
        getAttrIncentivePolicyData() | "ExtendedAttributeMemberIncentiveObj" | getAttrData(ExtendedAttributeConstants.AttrType.MEMBER_POLICY.getAttrType()) | getResultDataSame() | null                | getResultDataSame()
    }

    def getCommonData(def describeApiName) {
        def objectData = new ObjectData()
        objectData.set("attr_id", "attr_id")
        objectData.set("object_describe_api_name", describeApiName)
        objectData.set("text_value", "1")
        return objectData
    }

    def getSelectOneData(def describeApiName) {
        def objectData = new ObjectData()
        objectData.set("attr_id", "attr_id")
        objectData.set("object_describe_api_name", describeApiName)
        objectData.set("text_value", "1")
        return objectData
    }

    def getAttrIncentivePolicyData() {
        def objectData = new ObjectData()
        objectData.set("attr_id", "attr_id")
        objectData.set("object_describe_api_name", "ExtendedAttributeMemberIncentiveObj")
        objectData.set("text_value", "1")
        objectData.set("policy_id", "1")
        objectData.set("member_id", "1")
        return objectData
    }

    def getAttrPolicyData() {
        def objectData = new ObjectData()
        objectData.set("attr_id", "attr_id")
        objectData.set("object_describe_api_name", "ExtendedAttributePolicyObj")
        objectData.set("text_value", "1")
        objectData.set("policy_id", "1")
        return objectData
    }

    def getAttrEventData() {
        def objectData = new ObjectData()
        objectData.set("attr_id", "attr_id")
        objectData.set("object_describe_api_name", "ExtendedAttributeEventObj")
        objectData.set("text_value", "1")
        objectData.set("event_id", "1")
        return objectData
    }

    def getAttrMemberData() {
        def objectData = new ObjectData()
        objectData.set("attr_id", "attr_id")
        objectData.set("object_describe_api_name", "ExtendedAttributeMemberObj")
        objectData.set("text_value", "1")
        objectData.set("member_id", "1")
        return objectData
    }

    def getAttrData(def attrType) {
        def objectDataList = [new ObjectData()]
        objectDataList[0].set("attr_type", attrType)
        objectDataList[0].set("data_type", "text")
        objectDataList[0].set("program_id", "program_id")
        return objectDataList
    }


    def getResultData() {
        def objectDataList = [new ObjectData()]
        objectDataList[0].setName("名称")
        objectDataList[0].set("program_id", "program_id不同")
        return objectDataList
    }

    def getResultDataSame() {
        def objectDataList = [new ObjectData()]
        objectDataList[0].setName("名称")
        objectDataList[0].set("program_id", "program_id")
        return objectDataList
    }

}
