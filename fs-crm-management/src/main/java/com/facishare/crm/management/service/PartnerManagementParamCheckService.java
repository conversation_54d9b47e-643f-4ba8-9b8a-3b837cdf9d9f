package com.facishare.crm.management.service;

import com.facishare.crm.enums.*;
import com.facishare.crm.management.enums.AppEnums;
import com.facishare.crm.management.enums.ChannelModeEnums;
import com.facishare.crm.management.enums.ChannelModuleEnums;
import com.facishare.crm.management.enums.PrmNotificationTypeEnums;
import com.facishare.crm.management.service.access.ApprovalNoticeAccess;
import com.facishare.crm.management.service.access.SignSchemeAccess;
import com.facishare.crm.management.service.model.ApprovalNoticeModel;
import com.facishare.crm.model.PartnerChannelManage;
import com.facishare.crm.model.PrmManagementModel;
import com.facishare.crm.model.SignSchemeModel;
import com.facishare.crm.platform.time.TimeComputeService;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.ChannelCacheService;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.utilities.common.UseRangeFieldDataRender;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.constants.PrmI18NConstants.*;

/**
 * <AUTHOR>
 * @time 2023-12-04 18:03
 * @Description
 */
@Service
@Slf4j
public class PartnerManagementParamCheckService {
    private final ApprovalNoticeAccess approvalNoticeAccess;
    private final PartnerChannelService partnerChannelService;
    private final MetaDataFindServiceExt metaDataFindServiceExt;
    private final SignSchemeAccess signSchemeAccess;
    private final TimeComputeService timeComputeService;
    @Resource
    private ChannelCacheService channelCacheService;

    private static final String VAR_MESSAGE_SYMBOL = "${var_message}";

    public PartnerManagementParamCheckService(ApprovalNoticeAccess approvalNoticeAccess,
                                              PartnerChannelService partnerChannelService,
                                              MetaDataFindServiceExt metaDataFindServiceExt,
                                              SignSchemeAccess signSchemeAccess,
                                              TimeComputeService timeComputeService) {
        this.approvalNoticeAccess = approvalNoticeAccess;
        this.partnerChannelService = partnerChannelService;
        this.metaDataFindServiceExt = metaDataFindServiceExt;
        this.signSchemeAccess = signSchemeAccess;
        this.timeComputeService = timeComputeService;
    }

    public void checkPrmNoticeConfigType(PrmManagementModel.NoticeConfigArg arg) {
        checkPrmNoticeConfigType(arg.getActivateConfig());
        checkPrmNoticeConfigType(arg.getDeactivateConfig());
    }

    private void checkPrmNoticeConfigType(PrmManagementModel.NoticeConfig config) {
        String configType = config.getConfigType();
        PrmNotificationTypeEnums type = PrmNotificationTypeEnums.getType(configType);
        if (type == null) {
            throw new ValidateException(I18N.text(PRM_NOTIFICATION_TYPE_CONVERT_ERROR));
        }
    }

    public void checkPrmNoticeType(PrmManagementModel.NoticeConfigArg arg) {
        checkPrmNoticeType(arg.getActivateConfig());
        checkPrmNoticeType(arg.getDeactivateConfig());
    }

    private void checkPrmNoticeType(PrmManagementModel.NoticeConfig config) {
        List<String> noticeTypes = config.getNoticeTypes();
        if (CollectionUtils.isEmpty(noticeTypes) || noticeTypes.size() != 1 || !noticeTypes.contains("sms")) {
            throw new ValidateException(I18N.text(PRM_NOTIFICATION_TYPES_CONVERT_ERROR));
        }
    }

    public void checkPrmSmsType(PrmManagementModel.NoticeConfigArg arg) {
        checkPrmSmsType(arg.getActivateConfig().getShortMessage());
        checkPrmSmsType(arg.getDeactivateConfig().getShortMessage());
    }

    private void checkPrmSmsType(PrmManagementModel.ShortMessage shortMessage) {
        if (shortMessage == null) {
            return;
        }
        String smsType = shortMessage.getSmsType();
        SmsTypeEnums type = SmsTypeEnums.getSmsType(smsType);
        if (type == null) {
            throw new ValidateException(I18N.text(PRM_SMS_TYPE_CONVERT_ERROR));
        }
    }

    public void initDefault(PrmManagementModel.NoticeConfigArg arg) {
        PrmManagementModel.NoticeConfig activateConfig = arg.getActivateConfig();
        if (activateConfig == null) {
            activateConfig = PrmManagementModel.NoticeConfig.builder().configType(PrmNotificationTypeEnums.ACTIVATE.getType()).build();
            arg.setActivateConfig(activateConfig);
        }
        PrmManagementModel.NoticeConfig deactivateConfig = arg.getDeactivateConfig();
        if (deactivateConfig == null) {
            deactivateConfig = PrmManagementModel.NoticeConfig.builder().configType(PrmNotificationTypeEnums.DEACTIVATE.getType()).build();
            arg.setDeactivateConfig(deactivateConfig);
        }
    }

    public ConditionTypeEnums checkEnterpriseActivationSettings(PartnerChannelManage.EnterpriseActivationSettingArg activationSettingArg) {
        ConditionTypeEnums conditionType = ConditionTypeEnums.fromString(activationSettingArg.getConditionType());
        List<PartnerChannelManage.EnterpriseActivationSetting> enterpriseActivationSettings = activationSettingArg.getEnterpriseActivationSettings();
        if (CollectionUtils.isEmpty(enterpriseActivationSettings)) {
            throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "enterpriseActivationSettings"));
        }
        if (enterpriseActivationSettings.size() > 20) {
            throw new ValidateException(I18N.text(PRM_CONFIG_LIMIT, 20));
        }
        if (ConditionTypeEnums.ALL == conditionType) {
            if (enterpriseActivationSettings.size() > 1) {
                throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "enterpriseActivationSettings"));
            }
            UseRangeFieldDataRender.UseRangeInfo useRangeInfo = getUseRangeInfo(enterpriseActivationSettings.get(0).getCondition());
            if (ConditionTypeEnums.ALL != ConditionTypeEnums.fromString(useRangeInfo.getType())) {
                throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "condition"));
            }
        }
        // 校验 condition 格式
        if (ConditionTypeEnums.CONDITION == conditionType) {
            enterpriseActivationSettings.forEach(setting -> {
                UseRangeFieldDataRender.UseRangeInfo useRangeInfo = getUseRangeInfo(setting.getCondition());
                if (ConditionTypeEnums.CONDITION != ConditionTypeEnums.fromString(useRangeInfo.getType())) {
                    throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "condition"));
                }
            });
        }
        for (PartnerChannelManage.EnterpriseActivationSetting setting : enterpriseActivationSettings) {
            RecycleModeEnums.fromString(setting.getRecyclingMode());
            Integer expireDays = setting.getExpireDays();
            if (expireDays != null && (expireDays <= 0 || expireDays > 365)) {
                throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "expireDays"));
            }
            Integer expireCycleMonth = setting.getExpireCycleMonth();
            Integer expireCycleDay = setting.getExpireCycleDay();
            if (expireCycleMonth != null && (expireCycleMonth <= 0 || expireCycleMonth > 12)) {
                throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "expireCycleMonth"));
            }
            if (expireCycleDay != null && (expireCycleDay <= 0 || expireCycleDay > 31)) {
                throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "expireCycleDay"));
            }
            if (expireCycleMonth != null && expireCycleMonth == 2 && expireCycleDay != null && expireCycleDay >= 28) {
                throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "expireCycleDay"));
            }
        }
        return conditionType;
    }

    private UseRangeFieldDataRender.UseRangeInfo getUseRangeInfo(String condition) {
        if (StringUtils.isBlank(condition)) {
            throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "condition"));
        }
        UseRangeFieldDataRender.UseRangeInfo useRangeInfo = partnerChannelService.parseConditionObject(condition);
        if (conditionInvalid(useRangeInfo)) {
            throw new ValidateException(I18N.text(PRM_CONDITION_NOT_EMPTY));
        }
        return useRangeInfo;
    }

    public void checkSwitchScopeArg(User user, PartnerChannelManage.SwitchItem switchItem) {
        NotifyViaEnums notifyVia = NotifyViaEnums.of(switchItem.getNotifyVia());
        BizScopeEnums bizScope = BizScopeEnums.fromString(switchItem.getBizScope());
        if (BizScopeEnums.SIGN == bizScope && StringUtils.isBlank(switchItem.getApprovalNoticeId())) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_UNAUTHORIZED_OPERATION));
        }
        checkApprovalNoticeId(user, switchItem.getApprovalNoticeId(), notifyVia, bizScope);
    }

    public void checkApprovalNoticeId(User user, String approvalNoticeId, NotifyViaEnums notifyVia, BizScopeEnums bizScope) {
        if (StringUtils.isNotBlank(approvalNoticeId)) {
            IObjectData data = approvalNoticeAccess.queryApprovalNoticeById(user, approvalNoticeId);
            String viaDb = data.get(ApprovalNoticeModel.NOTIFY_VIA, String.class);
            if (notifyVia != NotifyViaEnums.of(viaDb)) {
                throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "notifyVia"));
            }
            return;
        }
        List<IObjectData> data = approvalNoticeAccess.queryApprovalNoticeByViaScope(user, notifyVia, bizScope);
        if (CollectionUtils.isNotEmpty(data)) {
            throw new ValidateException(I18N.text(PRM_APPROVAL_NOTICE_ID_EMPTY_ERROR, notifyVia.getVia()));
        }
    }

    public void checkApprovalNoticeSmsInstance(User user, PartnerChannelManage.ApprovalNoticeSmsInstanceArg smsArg) {
        BizScopeEnums bizScope = BizScopeEnums.fromString(smsArg.getBizScope());
        if (BizScopeEnums.SIGN == bizScope && StringUtils.isBlank(smsArg.getApprovalNoticeId())) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_UNAUTHORIZED_OPERATION));
        }
        checkApprovalNoticeId(user, smsArg.getApprovalNoticeId(), NotifyViaEnums.SMS, bizScope);
    }

    public void checkApprovalNoticeEmailInstance(User user, PartnerChannelManage.ApprovalNoticeEmailInstanceArg emailArg) {
        BizScopeEnums bizScope = BizScopeEnums.fromString(emailArg.getBizScope());
        if (BizScopeEnums.SIGN == bizScope && StringUtils.isBlank(emailArg.getApprovalNoticeId())) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_UNAUTHORIZED_OPERATION));
        }
        checkApprovalNoticeId(user, emailArg.getApprovalNoticeId(), NotifyViaEnums.EMAIL, bizScope);
    }

    public void checkAplArg(User user, PartnerChannelManage.NoticeAplArg aplArg) {
        NotifyViaEnums notifyVia = NotifyViaEnums.of(aplArg.getNotifyVia());
        BizScopeEnums bizScope = BizScopeEnums.fromString(aplArg.getBizScope());
        if (BizScopeEnums.SIGN == bizScope && StringUtils.isBlank(aplArg.getApprovalNoticeId())) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_UNAUTHORIZED_OPERATION));
        }
        checkApprovalNoticeId(user, aplArg.getApprovalNoticeId(), notifyVia, bizScope);
    }

    public void checkProvisionScheme(User user, PartnerChannelManage.ProvisionSchemeArg provisionSchemeArg) {
        List<PartnerChannelManage.ProvisionScheme> provisionSchemeList = provisionSchemeArg.getProvisionScheme();
        if (CollectionUtils.isEmpty(provisionSchemeList)) {
            return;
        }
        if (provisionSchemeList.size() > 20) {
            throw new ValidateException(I18N.text(PRM_CONFIG_LIMIT, 20));
        }
        for (PartnerChannelManage.ProvisionScheme provisionScheme : provisionSchemeList) {
            List<String> list = provisionScheme.getProvisionIds();
            Set<String> set = Sets.newHashSet(list);
            if (set.size() != list.size()) {
                throw new ValidateException(I18N.text(PRM_CHANNEL_PROVISION_UN_ALLOW_REPEAT));
            }
            String condition = provisionScheme.getCondition();
            UseRangeFieldDataRender.UseRangeInfo useRangeInfo = getUseRangeInfo(condition);
            ConditionTypeEnums conditionType = ConditionTypeEnums.fromString(useRangeInfo.getType());
            if (StringUtils.isNotBlank(provisionScheme.getAplApiName())) {
                if (provisionScheme.getConditionTypeEnum() != ConditionTypeEnums.APL) {
                    throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "conditionType"));
                }
            } else {
                if (provisionScheme.getConditionTypeEnum() == ConditionTypeEnums.APL) {
                    throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "conditionType"));
                }
                if (provisionScheme.getConditionTypeEnum() != conditionType) {
                    throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "condition"));
                }
            }
            List<String> provisionIds = provisionScheme.getProvisionIds();
            if (CollectionUtils.isNotEmpty(provisionIds) && provisionIds.size() > 20) {
                throw new ValidateException(I18N.text(PRM_CONFIG_LIMIT, 20));
            }
        }
        List<String> provisionIds = provisionSchemeList.stream()
                .flatMap(p -> p.getProvisionIds().stream())
                .collect(Collectors.toList());
        List<IObjectData> objectByIds = metaDataFindServiceExt.findObjectByIds(user, provisionIds, SFAPreDefineObject.PartnerProvision.getApiName());
        HashSet<String> setIds = Sets.newHashSet(provisionIds);
        if (setIds.size() != objectByIds.size()) {
            throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "provisionIds"));
        }
    }

    private boolean conditionInvalid(UseRangeFieldDataRender.UseRangeInfo useRangeInfo) {
        if (useRangeInfo == null) {
            return true;
        }
        if (ConditionTypeEnums.ALL.getType().equals(useRangeInfo.getType())) {
            return false;
        }
        return "[]".equals(useRangeInfo.getValue()) || StringUtils.isBlank(useRangeInfo.getValue());
    }

    public void checkSignSchemeArg(User user, PartnerChannelManage.SignSchemeArg signSchemeArg) {
        if (signSchemeArg == null) {
            throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "signSchemeArg"));
        }
        // 统计 signScheme 数量
        Integer signSchemeCount = signSchemeAccess.count(user);
        if (signSchemeCount != null && signSchemeCount + 1 > 20) {
            throw new ValidateException(I18N.text(PRM_CONFIG_LIMIT, 20));
        }
        if (BizScopeEnums.fromString(signSchemeArg.getBizScope()) != BizScopeEnums.SIGN) {
            throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "bizScope"));
        }
        PartnerChannelManage.SignScheme signScheme = signSchemeArg.getSignScheme();
        SignModelEnums.fromString(signScheme.getSignMode());
        String condition = signScheme.getCondition();
        UseRangeFieldDataRender.UseRangeInfo useRangeInfo = getUseRangeInfo(condition);
        if (signScheme.getConditionTypeEnum() != ConditionTypeEnums.fromString(useRangeInfo.getType())) {
            throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "condition"));
        }
        String agreementId = signScheme.getAgreementId();
        if (StringUtils.isBlank(agreementId)) {
            throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "agreementId"));
        }
        IObjectData partnerAgreementData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, agreementId, SFAPreDefineObject.PartnerAgreement.getApiName());
        if (partnerAgreementData == null) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_DATA_NOT_EXISTS, "agreementId"));
        }
        if (StringUtils.isNotBlank(signScheme.getSignSchemeId())) {
            IObjectData signSchemeData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, signScheme.getSignSchemeId(), SignSchemeModel.SIGN_SCHEME_OBJ);
            if (signSchemeData == null) {
                throw new ValidateException(I18N.text(PRM_CHANNEL_DATA_NOT_EXISTS, "signSchemeId"));
            }
            boolean pushed = ObjectDataUtils.getValue(signSchemeData, SignSchemeModel.PUSHED, Boolean.class, false);
            if (pushed) {
                throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_SCHEME_PUSHED_CAN_NOT_EDIT));
            }
        }
        if (signScheme.getSignTime() == null || signScheme.getSignTime() <= 0) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_TIME_MUST_GREATER_THAN_ZERO_ERROR));
        }
        checkApprovalNoticeData(signScheme);
        checkFixedDateSchedule(signScheme);
        int signDays = timeComputeService.calculateDays(signScheme.getSignTime(), TimeUnitEnums.of(signScheme.getSignTimeUnit()));
        checkReminderTimeArg(signDays, signScheme.getExpireReminderTypeView());
    }

    private void checkFixedDateSchedule(PartnerChannelManage.SignScheme signScheme) {
        ScheduleTypeEnums.of(signScheme.getScheduleType());
        if (signScheme.getFixedDay() == null && signScheme.getFixedMonth() == null) {
            return;
        }
        boolean validFixedDateParam = signScheme.getFixedDay() != null && signScheme.getFixedMonth() != null;
        if (!validFixedDateParam) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_SCHEME_MONTH_AND_DAY_MUST_FILL));
        }
        if (signScheme.getFixedMonth() < 1 || signScheme.getFixedMonth() > 12) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_SCHEME_MONTH_MUST_GREATER_THAN_ZERO_AND_LESS_THAN_12));
        }
        if (signScheme.getFixedDay() < 1 || signScheme.getFixedDay() > 31) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_SCHEME_DAY_MUST_GREATER_THAN_ZERO_AND_LESS_THAN_31));
        }
        if (signScheme.getFixedMonth() == 2 && signScheme.getFixedDay() > 29) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_SCHEME_FEB_MUST_LESS_THAN_29));
        }
        boolean isLongMonth = timeComputeService.isMonthWith31(signScheme.getFixedMonth());
        if (!isLongMonth && signScheme.getFixedDay() == 31) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_SCHEME_MONTH_MUST_LESS_THAN_30, signScheme.getFixedMonth()));
        }
    }

    private void checkApprovalNoticeData(PartnerChannelManage.SignScheme signScheme) {
        if (StringUtils.isBlank(signScheme.getSignSchemeId())) {
            return;
        }
        if (CollectionUtils.isEmpty(signScheme.getApprovalNotices()) || signScheme.getApprovalNotices().size() != 2) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_SCHEME_FIRST_SAVE_NOTICE_ERROR));
        }
        if (signScheme.getApprovalNotices().stream().anyMatch(a -> StringUtils.isNotBlank(a.getApprovalNoticeId()))) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_SCHEME_FIRST_SAVE_NOTICE_ERROR));
        }
    }

    private void checkReminderTimeArg(int signDays, PartnerChannelManage.ExpireReminderTypeView expireReminderTypeView) {
        checkReminderTypeDays(signDays, expireReminderTypeView.getAutoExpireReminderType());
        checkReminderTypeDays(signDays, expireReminderTypeView.getManualExpireReminderType());
    }

    private void checkReminderTypeDays(int signDays, PartnerChannelManage.ExpireReminderType expireReminderType) {
        PartnerChannelManage.ReminderType reminder = expireReminderType.getCrmReminder();
        checkReminderTypeDays(signDays, reminder);

        PartnerChannelManage.ReminderType emailReminder = expireReminderType.getEmailReminder();
        checkReminderTypeDays(signDays, emailReminder);

        PartnerChannelManage.ReminderType smsReminder = expireReminderType.getSmsReminder();
        checkReminderTypeDays(signDays, smsReminder);

        PartnerChannelManage.ReminderType prmCrmReminder = expireReminderType.getPrmCrmReminder();
        checkReminderTypeDays(signDays, prmCrmReminder);

        PartnerChannelManage.ReminderType prmAlertWindowReminder = expireReminderType.getPrmAlertWindowReminder();
        checkReminderTypeDays(signDays, prmAlertWindowReminder);
        checkPrmAlertWindowReminderMessage(prmAlertWindowReminder);
    }

    private void checkPrmAlertWindowReminderMessage(PartnerChannelManage.ReminderType prmAlertWindowReminder) {
        if (!prmAlertWindowReminder.getMessage().contains(VAR_MESSAGE_SYMBOL)) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_PRM_ALERT_MISSING_VAR_SYMBOL));
        }
    }

    private void checkReminderTypeDays(int signDays, PartnerChannelManage.ReminderType reminder) {
        int days = calculateReminderTypeDays(reminder);
        if (days == 0) {
            return;
        }
        if (signDays <= days) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_NOTICE_TIME_MUST_LESS_THAN_SIGN_PERIOD));
        }
    }

    private int calculateReminderTypeDays(PartnerChannelManage.ReminderType reminder) {
        if (reminder == null) {
            return 0;
        }
        if (StringUtils.isBlank(reminder.getTimeUnit())) {
            reminder.setTimeUnit(TimeUnitEnums.MONTH.getUnit());
        }
        return timeComputeService.calculateDays(reminder.getReminderTime(), TimeUnitEnums.of(reminder.getTimeUnit()));
    }

    public void checkQualificationSchemeArg(PartnerChannelManage.QualificationSchemeArg qualificationSchemeArg) {
        List<PartnerChannelManage.QualificationScheme> qualificationScheme = qualificationSchemeArg.getQualificationScheme();
        if (CollectionUtils.isEmpty(qualificationScheme)) {
            return;
        }
        if (qualificationScheme.size() > 20) {
            throw new ValidateException(I18N.text(PRM_CONFIG_LIMIT, 20));
        }
        qualificationScheme.forEach(scheme -> {
            UseRangeFieldDataRender.UseRangeInfo useRangeInfo = getUseRangeInfo(scheme.getCondition());
            ConditionTypeEnums conditionType = ConditionTypeEnums.fromString(useRangeInfo.getType());
            if (StringUtils.isNotBlank(scheme.getAplApiName())) {
                if (ConditionTypeEnums.APL != scheme.getConditionTypeEnum()) {
                    throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "conditionType"));
                }
                if (ConditionTypeEnums.ALL != conditionType) {
                    throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "condition"));
                }
            } else {
                if (ConditionTypeEnums.APL == scheme.getConditionTypeEnum()) {
                    throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "conditionType"));
                }
                if (scheme.getConditionTypeEnum() != conditionType) {
                    throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "conditionType"));
                }
            }
        });
    }

    public void checkPushSignSchemeArg(User user, String signSchemeId) {
        if (StringUtils.isBlank(signSchemeId)) {
            throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "signSchemeId"));
        }
//        if (channelCacheService.agreementDetailStatusIsChanging(user)) {
//            throw new ValidateException(I18N.text(PRM_CHANNEL_PROTOCOL_DETAIL_IN_CHANGE_STATUS, channelCacheService.calculatePendingCount(user)));
//        }
    }

    public void checkDeleteSignSchemeArg(User user, String signSchemeId) {
        if (StringUtils.isBlank(signSchemeId)) {
            throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "signSchemeId"));
        }
//        if (channelCacheService.agreementDetailStatusIsChanging(user)) {
//            throw new ValidateException(I18N.text(PRM_CHANNEL_PROTOCOL_DETAIL_IN_CHANGE_STATUS, channelCacheService.calculatePendingCount(user)));
//        }
    }

    public void checkChannelAdmissionConfig(PartnerChannelManage.AdmissionConfig admissionConfig) {
        ChannelModeEnums.of(admissionConfig.getChannelMode());
        AppEnums.of(admissionConfig.getApplyToApp());
        admissionConfig.getApplyModules().forEach(ChannelModuleEnums::of);
    }
}
