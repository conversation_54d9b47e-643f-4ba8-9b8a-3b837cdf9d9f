package com.facishare.crm.management.service.config;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.describebuilder.*;
import com.facishare.crm.management.service.config.model.InsertOrUpdataResult;
import com.facishare.crm.management.service.config.model.OutResources;
import com.facishare.crm.management.utils.SFAManagamentI18NKeys;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.ObjectDesignerService;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.LongTextFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.describe.QuoteFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DescUtil {

    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    protected IObjectDescribeService objectDescribeService;
    @Autowired
    protected ConfigService configService;
    @Autowired
    protected ObjectDesignerService objectDesignerService;


    public FieldLayoutPojo getFieldLayoutPojo(String renderType, boolean readOnly, boolean required) {
        FieldLayoutPojo fieldLayoutPojo = new FieldLayoutPojo();
        fieldLayoutPojo.setReadonly(readOnly);
        fieldLayoutPojo.setRequired(required);
        fieldLayoutPojo.setRenderType(renderType);
        return fieldLayoutPojo;
    }

    public void insertFieldToLayout(User user, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe, FieldLayoutPojo fieldLayoutPojo) {
        List<Tuple<IFieldDescribe, FieldLayoutPojo>> addFieldTupleList = Lists.newArrayList();
        addFieldTupleList.add(Tuple.of(fieldDescribe, fieldLayoutPojo));
        List<ILayout> layoutList = serviceFacade.getDetailLayouts(user.getTenantId(), objectDescribe);
        layoutList.forEach(m -> removeRepeatField(m, Lists.newArrayList(fieldDescribe.getApiName())));
        layoutList.forEach(m -> {
            addFieldTupleList.forEach(tuple -> LayoutExt.of(m).addField(tuple.getKey(), tuple.getValue()));
            serviceFacade.updateLayout(user, m);
        });
    }

    public void insertFieldToLayoutByLayoutTypes(User user, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe, FieldLayoutPojo fieldLayoutPojo, List<String> layoutTypes) {
        if(CollectionUtils.empty(layoutTypes)) {
            return;
        }
        List<Tuple<IFieldDescribe, FieldLayoutPojo>> addFieldTupleList = Lists.newArrayList();
        addFieldTupleList.add(Tuple.of(fieldDescribe, fieldLayoutPojo));
        List<ILayout> layoutList = serviceFacade.findLayoutByObjectApiName(user.getTenantId(), objectDescribe.getApiName());
        layoutList = layoutList.stream().filter(m -> layoutTypes.contains(m.getLayoutType())).collect(Collectors.toList());
        layoutList.forEach(m -> removeRepeatField(m, Lists.newArrayList(fieldDescribe.getApiName())));
        layoutList.forEach(m -> {
            addFieldTupleList.forEach(tuple -> LayoutExt.of(m).addField(tuple.getKey(), tuple.getValue()));
            serviceFacade.updateLayout(user, m);
        });
    }

    public void insertFieldsToLayoutByLayoutTypes(User user, String objectApiName, List<Tuple<IFieldDescribe, FieldLayoutPojo>> addFieldTupleList, List<String> layoutTypes) {
        List<ILayout> layoutList = serviceFacade.findByTypes(LayoutLogicService.LayoutContext.of(user), objectApiName, layoutTypes);
        layoutList.forEach(m -> removeRepeatField(m, addFieldTupleList.stream().map(n -> n.getKey().getApiName()).distinct()
                .collect(Collectors.toList())));
        layoutList.forEach(m -> {
            addFieldTupleList.forEach(tuple -> LayoutExt.of(m).addField(tuple.getKey(), tuple.getValue()));
            serviceFacade.updateLayout(user, m);
        });
    }

    public void insertFieldsToLayout(User user, IObjectDescribe objectDescribe, List<Tuple<IFieldDescribe, FieldLayoutPojo>> addFieldTupleList) {
        List<ILayout> layoutList = serviceFacade.getDetailLayouts(user.getTenantId(), objectDescribe);
        layoutList.forEach(m -> removeRepeatField(m, addFieldTupleList.stream().map(n -> n.getKey().getApiName()).distinct()
                .collect(Collectors.toList())));
        layoutList.forEach(m -> {
            addFieldTupleList.forEach(tuple -> LayoutExt.of(m).addField(tuple.getKey(), tuple.getValue()));
            serviceFacade.updateLayout(user, m);
        });
    }

    public ILayout removeRepeatField(ILayout layout, List<String> fieldNames) {
        try {
            for (IComponent component : layout.getComponents()) {
                if (component instanceof FormComponent && component != null) {
                    FormComponent formComponent = (FormComponent) component;
                    if (formComponent.getFieldSections() != null) {
                        removeField(fieldNames, formComponent.getFieldSections());
                    }
                }
            }
        } catch (MetadataServiceException e) {
            log.warn("updateFieldRenderType error occur,for exception", e);
            throw new RuntimeException(e);

        }
        return layout;
    }

    private void removeField(List<String> fieldNames, List<IFieldSection> fieldSections) {
        if (CollectionUtils.empty(fieldSections)) {
            return;
        }
        for (IFieldSection fieldSection : fieldSections) {
            List<IFormField> removeFormFields = Lists.newArrayList();
            List<IFormField> formFields = fieldSection.getFields();
            formFields.forEach(formFiled -> {
                if (fieldNames.contains(formFiled.getFieldName())) {
                    removeFormFields.add(formFiled);
                }
            });
            formFields.removeAll(removeFormFields);
            fieldSection.setFields(formFields);
        }
    }

    public ILayout updateFieldRenderType(ILayout layout, Map<String, Map<String, Object>> changeFields) {
        try {
            for (IComponent component : layout.getComponents()) {
                if (component instanceof FormComponent) {
                    FormComponent formComponent = (FormComponent) component;
                    changeField(changeFields, formComponent.getFieldSections());
                }
            }
        } catch (MetadataServiceException e) {
            log.warn("updateFieldRenderType error occur,for exception", e);
            throw new RuntimeException(e);
        }
        return layout;
    }

    private void changeField(Map<String, Map<String, Object>> changeFields, List<IFieldSection> fieldSections) {
        for (IFieldSection fieldSection : fieldSections) {
            fieldSection.getFields().forEach(formFiled -> {
                if (changeFields.containsKey(formFiled.getFieldName())) {
                    Map<String, Object> fieldDetail = changeFields.get(formFiled.getFieldName());
                    fieldDetail.forEach((m, n) -> formFiled.set(m, n));
                }
            });
        }
    }


    @NotNull
    public InsertOrUpdataResult getUpdateOrInsertFieldDescribe(IObjectDescribe objectDescribe, String fieldName
            , String fieldLabel, String apiName, String targetRelatedListLabel, String targetRelatedListName, Map configMap,boolean isUnique) {
        boolean isInsert = false;
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
        if (fieldDescribe != null) {
            fieldDescribe.setActive(true);
            changeConfigDisplay(fieldDescribe);
            if (configMap != null) {
                fieldDescribe.setConfig(configMap);
            }
        } else {
            isInsert = true;
            fieldDescribe = ObjectReferenceFieldDescribeBuilder.builder()
                    .apiName(fieldName)
                    .label(fieldLabel)
                    .targetApiName(apiName)
                    .targetRelatedListLabel(targetRelatedListLabel)
                    .targetRelatedListName(targetRelatedListName)
                    .unique(isUnique)
                    .required(false)
                    .build();
            if (configMap != null) {
                fieldDescribe.setConfig(configMap);
            }
        }
        InsertOrUpdataResult result = InsertOrUpdataResult.builder().isInsert(isInsert).fieldDescribe(fieldDescribe).build();
        return result;
    }

    @NotNull
    public InsertOrUpdataResult getUpdateOrInsertTextFieldDescribe(IObjectDescribe objectDescribe, String fieldName, String fieldLabel, boolean isJson) {
        boolean isInsert = false;
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
        if (fieldDescribe != null) {
            fieldDescribe.setActive(true);
            if (!isJson) {
                changeConfigDisplay(fieldDescribe);
            }
        } else {
            isInsert = true;
            fieldDescribe = TextFieldDescribeBuilder.builder()
                    .apiName(fieldName)
                    .label(fieldLabel)
                    .unique(false)
                    .required(false)
                    .maxLength(3000)
                    .build();
            if (isJson) {
                fieldDescribe.setExpression("json");
            }
        }
        InsertOrUpdataResult result = InsertOrUpdataResult.builder().isInsert(isInsert).fieldDescribe(fieldDescribe).build();
        return result;
    }

    @NotNull
    public InsertOrUpdataResult getUpdateOrInsertLongTextFieldDescribe(IObjectDescribe objectDescribe, String fieldName, String fieldLabel, boolean isJson) {
        boolean isInsert = false;
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
        if (fieldDescribe != null) {
            fieldDescribe.setActive(true);
            if (!isJson) {
                changeConfigDisplay(fieldDescribe);
            }
        } else {
            isInsert = true;
            fieldDescribe = LongTextFieldDescribeBuilder.builder()
                    .apiName(fieldName)
                    .label(fieldLabel)
                    .maxLength(2000)
                    .required(false)
                    .build();
            if (isJson) {
                ((LongTextFieldDescribe) fieldDescribe).setExpressionType("json");
            }
        }
        InsertOrUpdataResult result = InsertOrUpdataResult.builder().isInsert(isInsert).fieldDescribe(fieldDescribe).build();
        return result;
    }

    public InsertOrUpdataResult getUpdateOrInsertArrayFieldDescribe(IObjectDescribe objectDescribe, String fieldName, String fieldLabel) {
        boolean isInsert = false;
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
        if (fieldDescribe != null) {
            fieldDescribe.setActive(true);
        } else {
            isInsert = true;
            fieldDescribe = ArrayFieldDescribeBuilder.builder()
                    .apiName(fieldName)
                    .label(fieldLabel)
                    .unique(false)
                    .required(false)
                    .build();
        }
        InsertOrUpdataResult result = InsertOrUpdataResult.builder().isInsert(isInsert).fieldDescribe(fieldDescribe).build();
        return result;
    }

    @NotNull
    public InsertOrUpdataResult updateFieldDescribe(IObjectDescribe objectDescribe, String fieldName, String fieldLabel, String apiName, String targetRelatedListLabel, String targetRelatedListName, Map configMap) {
        boolean isInsert = false;
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
        if (fieldDescribe != null) {
            fieldDescribe.setActive(true);
            changeConfigDisplay(fieldDescribe);
            if (configMap != null) {
                fieldDescribe.setConfig(configMap);
            }
        } else {
            isInsert = true;
            fieldDescribe = ObjectReferenceFieldDescribeBuilder.builder()
                    .apiName(fieldName)
                    .label(fieldLabel)
                    .targetApiName(apiName)
                    .targetRelatedListLabel(targetRelatedListLabel)
                    .targetRelatedListName(targetRelatedListName)
                    .unique(false)
                    .required(false)
                    .build();
            if (configMap != null) {
                fieldDescribe.setConfig(configMap);
            }
        }
        InsertOrUpdataResult result = InsertOrUpdataResult.builder().isInsert(isInsert).fieldDescribe(fieldDescribe).build();
        return result;
    }

    public void changeConfigDisplay(IFieldDescribe fieldDescribe) {
        Map<String, Object> configMap = fieldDescribe.getConfig();
        if (configMap == null) {
            configMap = Maps.newHashMap();
        }
        configMap.put("display", 1);
        fieldDescribe.setConfig(configMap);
    }

    public void addFieldDescribe(IObjectDescribe objectDescribe, List<IFieldDescribe> fieldDescribeList) {
        try {
            objectDescribeService.addCustomFieldDescribe(objectDescribe, fieldDescribeList);
        } catch (MetadataServiceException e) {
            log.error("addFieldDescribe error,tenantId {} ", objectDescribe.getTenantId(), e);
            throw new RuntimeException(e);
        }
    }

    public void updateFieldDescribe(IObjectDescribe objectDescribe, List<IFieldDescribe> fieldDescribeList) {
        try {
            objectDescribeService.updateFieldDescribe(objectDescribe, fieldDescribeList);
        } catch (MetadataServiceException e) {
            log.error("updateFieldDescribe error,tenantId {} ", objectDescribe.getTenantId(), e);
            throw new RuntimeException(e);
        }
    }

    public void updateDescribe(IObjectDescribe objectDescribe) {
        try {
            objectDescribeService.update(objectDescribe);
        } catch (MetadataServiceException e) {
            log.error("updateDescribe error,tenantId {} ", objectDescribe.getTenantId(), e);
            throw new RuntimeException(e);
        }
    }


    private InsertOrUpdataResult getOutResourcesFieldDescribe(IObjectDescribe objectDescribe) {
        boolean isInsert = false;
        IFieldDescribe outResourcesFieldDescribe = objectDescribe.getFieldDescribe("out_resources");
        if (outResourcesFieldDescribe != null) {
            outResourcesFieldDescribe.setActive(true);
        } else {
            isInsert = true;
            List<ISelectOption> outResourcesSelectOptions = Arrays.stream(OutResources.values()).map(statusEnum -> SelectOptionBuilder.builder().value(statusEnum.getStatus()).label(statusEnum.getLabel()).build()).collect(Collectors.toList());
            outResourcesFieldDescribe = SelectOneFieldDescribeBuilder.builder()
                    .apiName("out_resources")
                    .label("外部来源")
                    .required(false)
                    .selectOptions(outResourcesSelectOptions)
                    .build();
        }
        InsertOrUpdataResult result = InsertOrUpdataResult.builder().isInsert(isInsert).fieldDescribe(outResourcesFieldDescribe).build();
        return result;
    }

    public void changeDescribeAndLayout(User user, Map<String, IObjectDescribe> describeMap, String apiName, String fieldName, String fieldLabel, boolean openEnterpriseRelation) {
        List<IFieldDescribe> insertFieldDescribes = Lists.newArrayList();
        List<IFieldDescribe> updateFieldDescribes = Lists.newArrayList();
        List<Tuple<IFieldDescribe, FieldLayoutPojo>> addFieldTupleList = Lists.newArrayList();
        //region 对象添加合作伙伴、外部来源
        IObjectDescribe objectDescribe = describeMap.get(apiName);
        if (objectDescribe == null) {
            log.warn("changeDescribeAndLayout>获取描述失败={},{}", user.getTenantId(), apiName);
            throw new ValidateException(I18N.text(SFAManagamentI18NKeys.SFA_CONFIG_GETDESCRIPTIONFAILED));
        }
        String targetRelatedListLabel = objectDescribe.getDisplayName();
        String targetRelatedListName = "partner_" + objectDescribe.getApiName().toLowerCase().replace("obj", "") + "_list";
        if ("owned_partner_id".equals(fieldName)) {
            targetRelatedListLabel = "合作伙伴联系人";
        }
        if ("partner_id".equals(fieldName) && "ContactObj".equals(apiName)) {
            targetRelatedListName = "partner_" + objectDescribe.getApiName().toLowerCase() + "_list";
        }
        InsertOrUpdataResult partnerIDFieldResult = getUpdateOrInsertFieldDescribe(objectDescribe, fieldName, fieldLabel, Utils.PARTNER_API_NAME,
                targetRelatedListLabel, targetRelatedListName, null,false);
        IFieldDescribe partnerField = partnerIDFieldResult.getFieldDescribe();
        if (openEnterpriseRelation
                && partnerField != null
                && "partner_id".equals(partnerField.getApiName())
                && !ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            partnerField.set("relation_outer_data_privilege", "outer_owner");
        }
        if (partnerIDFieldResult.getIsInsert()) {
            insertFieldDescribes.add(partnerIDFieldResult.getFieldDescribe());
        } else {
            updateFieldDescribes.add(partnerIDFieldResult.getFieldDescribe());
        }
        FieldLayoutPojo fieldLayoutPojo = getFieldLayoutPojo(SystemConstants.RenderType.ObjectReference.renderType, false, false);
        addFieldTupleList.add(Tuple.of(partnerIDFieldResult.getFieldDescribe(), fieldLayoutPojo));
        //外部来源
        InsertOrUpdataResult outResourcesFieldResult = getOutResourcesFieldDescribe(objectDescribe);
        if (outResourcesFieldResult.getIsInsert()) {
            insertFieldDescribes.add(outResourcesFieldResult.getFieldDescribe());
        } else {
            updateFieldDescribes.add(outResourcesFieldResult.getFieldDescribe());
        }
        fieldLayoutPojo = getFieldLayoutPojo(SystemConstants.RenderType.SelectOne.renderType, false, false);
        addFieldTupleList.add(Tuple.of(outResourcesFieldResult.getFieldDescribe(), fieldLayoutPojo));

        if (insertFieldDescribes.size() > 0) {
            addFieldDescribe(objectDescribe, insertFieldDescribes);
        }
        if (updateFieldDescribes.size() > 0) {
            serviceFacade.updateFieldDescribe(objectDescribe, updateFieldDescribes);
        }

        insertFieldsToLayout(user, objectDescribe, addFieldTupleList);

        //endregion
    }

    //合作伙伴-联系人特殊字段添加
    public void changeContactSpecialDescribeAndLayout(User user, IObjectDescribe contactDescribe) {
        List<IFieldDescribe> insertFieldDescribes = Lists.newArrayList();
        List<IFieldDescribe> updateFieldDescribes = Lists.newArrayList();
        IFieldDescribe accountIdFieldDescribe = contactDescribe.getFieldDescribe("account_id");
        if (accountIdFieldDescribe == null) {
            log.warn("setConfigValue>获取字段描述失败={},{},{}", user.getTenantId(), Utils.CONTACT_API_NAME, "account_id");
            throw new ValidateException(I18N.text(SFAManagamentI18NKeys.SFA_CONFIG_GETDESCRIPTIONFAILED));
        }
        accountIdFieldDescribe.setRequired(false);
        Map<String, Object> accountConfigMap = accountIdFieldDescribe.getConfig();
        if (accountConfigMap == null) {
            accountConfigMap = Maps.newHashMap();
        }
        Map<String, Object> attrsConfig = (Map) accountConfigMap.get("attrs");
        if (attrsConfig == null) {
            attrsConfig = Maps.newHashMap();
        }
        attrsConfig.put("is_required", 0);
        accountConfigMap.put("attrs", attrsConfig);
        accountIdFieldDescribe.setConfig(accountConfigMap);

        updateFieldDescribes.add(accountIdFieldDescribe);
        //更改所有布局
        List<ILayout> layoutList = serviceFacade.getDetailLayouts(user.getTenantId(), contactDescribe);
        Map<String, Map<String, Object>> changeFields = new HashMap<>();
        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
        stringObjectHashMap.put("is_required", false);
        changeFields.put("account_id",stringObjectHashMap);
        layoutList.forEach(m -> serviceFacade.updateLayout(user, updateFieldRenderType(m, changeFields)));
        if (!insertFieldDescribes.isEmpty()) {
            addFieldDescribe(contactDescribe, insertFieldDescribes);
        }
        if (!updateFieldDescribes.isEmpty()) {
            serviceFacade.updateFieldDescribe(contactDescribe, updateFieldDescribes);
        }
    }

    public void insertComponentToLayout(User user, IObjectDescribe objectDescribe, IComponent component, int index) {
        List<ILayout> layoutList = serviceFacade.getDetailLayouts(user.getTenantId(), objectDescribe);
        layoutList.forEach(m -> {
            List<String> hiddenComponent = m.getHiddenComponents();
            if (hiddenComponent != null && CollectionUtils.notEmpty(hiddenComponent)) {
                hiddenComponent.remove("product_attribute_component");
                m.setHiddenComponents(hiddenComponent);
            }
            WebDetailLayout.of(m).addComponentsToLeftLayout(Lists.newArrayList(component), index);
            serviceFacade.updateLayout(user, m);
        });
    }
    public void addRefField(String tenant, String fieldApiName, String fieldName, String apiName, String refObjApiName, boolean unique, boolean required) throws MetadataServiceException {
        List<IObjectDescribe> describeList = objectDescribeService.findDescribeListByApiNames(tenant, Lists.newArrayList(apiName));
        Optional<IObjectDescribe> objectDescribe = describeList.stream().filter(x -> apiName.equals(x.getApiName())).findFirst();
        if (!objectDescribe.isPresent()) {
            return;
        }
        if (objectDescribe.get().getFieldDescribe(fieldApiName) != null) {
            return;
        }

        String targetRelatedListName = refObjApiName.toLowerCase().replace("obj", "_") + objectDescribe.get().getApiName().toLowerCase().replace("obj", "") + "_list";
        String displayName = objectDescribe.get().getDisplayName();
        ObjectReferenceFieldDescribe fieldDescribe = ObjectReferenceFieldDescribeBuilder.builder().apiName(fieldApiName).label(fieldName).targetApiName(refObjApiName).targetRelatedListLabel(displayName).targetRelatedListName(targetRelatedListName).unique(unique).required(required).build();
        objectDescribeService.addCustomFieldDescribe(objectDescribe.get(), Lists.newArrayList(fieldDescribe));
    }
    public void addCategoryQuoteField(User user) throws MetadataServiceException {
        List<IObjectDescribe> describeList = objectDescribeService.findDescribeListByApiNames(user.getTenantId(), Lists.newArrayList("PriceBookProductObj"));
        Optional<IObjectDescribe> objectDescribe = describeList.stream().filter(x -> "PriceBookProductObj".equals(x.getApiName())).findFirst();
        if (!objectDescribe.isPresent()) {
            return;
        }
        if (objectDescribe.get().getFieldDescribe("product_category") != null) {
            return;
        }
        QuoteFieldDescribe quoteFieldDescribe = QuoteFieldDescribeBuilder.builder().quoteField("product_id__r.product_category_id").quoteFieldType("object_reference").apiName("product_category").unique(false).isIndex(false).required(false).label("分类").build();//ignoreI18n
        quoteFieldDescribe.setStatus("new");
        objectDescribeService.addCustomFieldDescribe(objectDescribe.get(), Lists.newArrayList(quoteFieldDescribe));
    }

    public void setRequired(User user, List<String> apiNameList, boolean required, String fieldApiName) throws MetadataServiceException {
        List<IObjectDescribe> describeList = objectDescribeService.findDescribeListByApiNames(user.getTenantId(), apiNameList);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(describeList)) {
            return;
        }
        for (IObjectDescribe describe : describeList) {
            IFieldDescribe fieldDescribe = describe.getFieldDescribe(fieldApiName);
            if (fieldDescribe == null) {
                continue;
            }
            if (fieldDescribe.isRequired() != null && fieldDescribe.isRequired() == required) {
                continue;
            }
            fieldDescribe.setRequired(required);
            objectDescribeService.updateFieldDescribe(describe, Lists.newArrayList(fieldDescribe));
        }
    }

    public void addCustomField(String tenantId, String objectApiName, List<IFieldDescribe> fieldDescribeList) throws MetadataServiceException {
        IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, objectApiName);
        if (describe == null) {
            return;
        }
        addCustomField(describe, fieldDescribeList);
    }

    public void addCustomField(IObjectDescribe describe, List<IFieldDescribe> fieldDescribeList) throws MetadataServiceException {
        List<IFieldDescribe> addFieldList = Lists.newArrayList();
        List<IFieldDescribe> updateFieldList = Lists.newArrayList();
        for (IFieldDescribe field : fieldDescribeList) {
            IFieldDescribe originalFieldDescribe = describe.getFieldDescribe(field.getApiName());
            if (originalFieldDescribe == null) {
                addFieldList.add(field);
            } else {
                if (Boolean.TRUE.equals(originalFieldDescribe.isActive())) {
                    continue;
                }
                originalFieldDescribe.setActive(true);
                updateFieldList.add(originalFieldDescribe);
            }
        }
        if (CollectionUtils.notEmpty(addFieldList)) {
            objectDescribeService.addCustomFieldDescribe(describe, addFieldList);
        }
        if (CollectionUtils.notEmpty(updateFieldList)) {
            objectDescribeService.updateFieldDescribe(describe, updateFieldList);
        }
    }

}
