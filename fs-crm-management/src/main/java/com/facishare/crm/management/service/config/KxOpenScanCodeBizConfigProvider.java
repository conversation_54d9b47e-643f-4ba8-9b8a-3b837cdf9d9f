package com.facishare.crm.management.service.config;

import com.facishare.crm.describebuilder.TextFieldDescribeBuilder;
import com.facishare.crm.management.utils.SFAManagamentI18NKeys;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.modulectrl.SFABizObjMappingRuleWrapperService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @IgnoreI18n or IgnoreI18nFile or @IgnoreI18nFile
 */
@Component
@Slf4j
public class KxOpenScanCodeBizConfigProvider extends DefaultBizConfigProvider {
    @Autowired
    SFABizObjMappingRuleWrapperService mappingRuleWrapperService;
    @Autowired
    BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Override
    public String getConfigKey() {
        return BizConfigKey.IS_KX_OPEN_SCAN_CODE_ENABLED.getId();
    }

    @Override
    public void setConfigValue(User user, String value, String oldValue,String key) {
        if (!bizConfigThreadLocalCacheService.isMultipleUnit(user.getTenantId())) {
            throw new ValidateException(I18N.text("sfa.open.kx.scan.init.0"));//"该企业未开启多单位"
        }
        IObjectDescribe describe = getDescribeWithSimplifiedChinese(user, Utils.MULTI_UNIT_RELATED_API_NAME);
        if (describe != null) {
            addField(describe);
        } else {
            log.warn("setConfigValue>获取描述失败={},{}", user.getTenantId()// ignoreI18n
                    , Lists.newArrayList(Utils.SALES_ORDER_API_NAME, Utils.SALES_ORDER_PRODUCT_API_NAME, Utils.QUOTE_API_NAME, Utils.QUOTE_LINES_API_NAME));
            throw new ValidateException(I18N.text(SFAManagamentI18NKeys.SFA_CONFIG_GETDESCRIPTIONFAILED));
        }

        super.setConfigValue(user, value, oldValue,key);
    }

    private void addField(IObjectDescribe describe) {
        addDescribeField(describe);
        descUtil.updateDescribe(describe);
    }

    private void addDescribeField(IObjectDescribe describe) {
        String constraintMode = "{\"describe_api_name\":\"MultiUnitRelatedObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"select_one\",\"is_required\":false,\"options\":[{\"label\":\"基于合同产品约束\",\"value\":\"detail\"}],\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"is_index\":true,\"is_active\":true,\"default_value\":\"detail\",\"label\":\"此合同约束产品及价格模式\",\"api_name\":\"constraint_mode\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"}";// ignoreI18n

        List<IFieldDescribe> fields = describe.getFieldDescribes();
        IFieldDescribe constraintModeField = FieldDescribeFactory.newInstance(constraintMode);
        fields.add(constraintModeField);
        describe.setFieldDescribes(fields);
    }
}
