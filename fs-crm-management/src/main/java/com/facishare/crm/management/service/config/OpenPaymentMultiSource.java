package com.facishare.crm.management.service.config;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.management.service.config.model.InsertOrUpdataResult;
import com.facishare.crm.management.utils.SFAManagamentI18NKeys;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.manager.CommonLayoutManagerNew;
import com.facishare.crm.sfa.predefine.service.modulectrl.SFABizObjMappingRuleWrapperService;
import com.facishare.crm.sfa.utilities.constant.CommonOrderPaymentConstants;
import com.facishare.crm.sfa.utilities.constant.PaymentObjConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.ObjectValueMappingService;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.CreateRule;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.UpdateRule;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.Where;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.facishare.crm.enums.ConfigType.ORDER_PAYMENT_MAPPING_RULE;

/**
 * 配置 是否开启非标属性
 *
 * <AUTHOR>
 * @date 2019/3/25
 */
@Component
@Slf4j
public class OpenPaymentMultiSource extends DefaultBizConfigProvider {

    @Autowired
    EnterpriseInitService enterpriseInitService;
    @Autowired
    BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Autowired
    ObjectMappingService objectMappingService;
    @Autowired
    ObjectValueMappingService objectValueMappingService;
    @Autowired
    CommonLayoutManagerNew commonLayoutManagerNew;
    @Autowired
    JobScheduleService jobScheduleService;

    private static final String SALES_ORDER_PRODUCT_ID = "{\"description\":\"订单产品编号\",\"is_unique\":false,\"type\":\"object_reference\",\"is_required\":false,\"define_type\":\"package\",\"is_index\":true,\"is_active\":true,\"default_value\":\"\",\"label\":\"订单产品编号\",\"target_api_name\":\"SalesOrderProductObj\",\"target_related_list_name\":\"related_list_order_product_order_payment\",\"target_related_list_label\":\"回款明细\",\"action_on_target_delete\":\"set_null\",\"api_name\":\"sales_order_product_id\",\"is_index_field\":true,\"help_text\":\"\",\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"related_order_payment_count\",\"field_values\":[\"0\"]}]}],\"status\":\"new\"}";// ignoreI18n
    private static final String SALE_CONTRACT_ID = "{\"description\":\"销售合同编号\",\"is_unique\":false,\"type\":\"object_reference\",\"is_required\":false,\"define_type\":\"package\",\"is_index\":true,\"is_active\":true,\"default_value\":\"\",\"label\":\"销售合同编号\",\"target_api_name\":\"SaleContractObj\",\"target_related_list_name\":\"related_list_sale_contract_order_payment\",\"target_related_list_label\":\"回款明细\",\"action_on_target_delete\":\"set_null\",\"api_name\":\"sale_contract_id\",\"is_index_field\":true,\"help_text\":\"\",\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"related_order_payment_count\",\"field_values\":[\"0\"]},{\"value_type\":2,\"operator\":\"EQ\",\"field_name\":\"account_id\",\"field_values\":[\"$payment_id__r.account_id$\"]}]}],\"status\":\"new\"}";// ignoreI18n
    private static final String SALE_CONTRACT_LINE_ID = "{\"description\":\"销售合同明细编号\",\"is_unique\":false,\"type\":\"object_reference\",\"is_required\":false,\"define_type\":\"package\",\"is_index\":true,\"is_active\":true,\"default_value\":\"\",\"label\":\"销售合同明细编号\",\"target_api_name\":\"SaleContractLineObj\",\"target_related_list_name\":\"related_list_contract_line_order_payment\",\"target_related_list_label\":\"回款明细\",\"action_on_target_delete\":\"set_null\",\"api_name\":\"sale_contract_line_id\",\"is_index_field\":true,\"help_text\":\"\",\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"related_order_payment_count\",\"field_values\":[\"0\"]}]}],\"status\":\"new\"}";// ignoreI18n
    private static final String ORDER_RELATED_ORDER_PAYMENT_COUNT = "{\"return_type\":\"number\",\"is_unique\":false,\"type\":\"count\",\"decimal_places\":0,\"sub_object_describe_apiname\":\"OrderPaymentObj\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"field_api_name\":\"order_id\",\"is_index\":true,\"is_active\":true,\"count_type\":\"count\",\"count_field_api_name\":\"\",\"label\":\"关联回款\",\"count_to_zero\":true,\"api_name\":\"related_order_payment_count\",\"count_field_type\":\"\",\"is_index_field\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\",\"default_result\": \"d_zero\"}";// ignoreI18n
    private static final String ORDER_PRODUCT_RELATED_ORDER_PAYMENT_COUNT = "{\"return_type\":\"number\",\"is_unique\":false,\"type\":\"count\",\"decimal_places\":0,\"sub_object_describe_apiname\":\"OrderPaymentObj\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"field_api_name\":\"sales_order_product_id\",\"is_index\":true,\"is_active\":true,\"count_type\":\"count\",\"count_field_api_name\":\"\",\"label\":\"关联回款\",\"count_to_zero\":true,\"api_name\":\"related_order_payment_count\",\"count_field_type\":\"\",\"is_index_field\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\",\"default_result\": \"d_zero\"}";// ignoreI18n
    private static final String CONTRACT_RELATED_ORDER_PAYMENT_COUNT = "{\"return_type\":\"number\",\"is_unique\":false,\"type\":\"count\",\"decimal_places\":0,\"sub_object_describe_apiname\":\"OrderPaymentObj\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"field_api_name\":\"sale_contract_id\",\"is_index\":true,\"is_active\":true,\"count_type\":\"count\",\"count_field_api_name\":\"\",\"label\":\"关联回款\",\"count_to_zero\":true,\"api_name\":\"related_order_payment_count\",\"count_field_type\":\"\",\"is_index_field\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\",\"default_result\": \"d_zero\"}";// ignoreI18n
    private static final String CONTRACT_LINE_RELATED_ORDER_PAYMENT_COUNT = "{\"return_type\":\"number\",\"is_unique\":false,\"type\":\"count\",\"decimal_places\":0,\"sub_object_describe_apiname\":\"OrderPaymentObj\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"field_api_name\":\"sale_contract_line_id\",\"is_index\":true,\"is_active\":true,\"count_type\":\"count\",\"count_field_api_name\":\"\",\"label\":\"关联回款\",\"count_to_zero\":true,\"api_name\":\"related_order_payment_count\",\"count_field_type\":\"\",\"is_index_field\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\",\"default_result\": \"d_zero\"}";// ignoreI18n
    @Override
    public String getConfigKey() {
        return BizConfigKey.IS_OPEN_ORDER_PAYMENT_MULTI_SOURCE.getId();
    }

    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {
        List<String> describeList = Lists.newArrayList(Utils.SALES_ORDER_API_NAME, Utils.SALES_ORDER_PRODUCT_API_NAME, Utils.ORDER_PAYMENT_API_NAME
                        , Utils.SALE_CONTRACT_API_NAME, Utils.SALE_CONTRACT_LINE_API_NAME);
        Map<String, IObjectDescribe> describeMap = getDescribeWithSimplifiedChineseByApiNames(user, describeList);
        for (String describeName : describeList) {
            if (Objects.equals(describeName, Utils.CUSTOMER_PAYMENT_API_NAME)) {
                continue;
            }
            IObjectDescribe describe = describeMap.get(describeName);
            if (Objects.isNull(describe)) {
                continue;
            }
            if (describe.getApiName().equals(Utils.ORDER_PAYMENT_API_NAME)) {
                IFieldDescribe fieldDescribe = describe.getFieldDescribe(CommonOrderPaymentConstants.Field.OrderId.apiName);
                if (Objects.nonNull(fieldDescribe)) {
                    fieldDescribe.setRequired(false);
                }
                try {
                    commonLayoutManagerNew.updateLayoutFieldRequired(Utils.ORDER_PAYMENT_API_NAME
                            , user.getTenantId(), Lists.newArrayList(CommonOrderPaymentConstants.Field.OrderId.apiName), false);
                } catch (MetadataServiceException e) {
                    log.error(e.getMessage());
                }
                describe = addOrderPaymentDescribeField(user.getTenantId(), describe);
            }
            if (describe.getApiName().equals(Utils.SALES_ORDER_API_NAME)) {
                describe = addRelatedOrderPaymentCountField(ORDER_RELATED_ORDER_PAYMENT_COUNT, describe);
            }
            if (describe.getApiName().equals(Utils.SALES_ORDER_PRODUCT_API_NAME)) {
                describe = addRelatedOrderPaymentCountField(ORDER_PRODUCT_RELATED_ORDER_PAYMENT_COUNT, describe);
            }
            if (bizConfigThreadLocalCacheService.isOpenSaleContract(user.getTenantId())) {
                if (describe.getApiName().equals(Utils.SALE_CONTRACT_API_NAME)) {
                    describe = addRelatedOrderPaymentCountField(CONTRACT_RELATED_ORDER_PAYMENT_COUNT, describe);
                }
                if (describe.getApiName().equals(Utils.SALE_CONTRACT_LINE_API_NAME)) {
                    describe = addRelatedOrderPaymentCountField(CONTRACT_LINE_RELATED_ORDER_PAYMENT_COUNT, describe);
                }
            }
            try {
                objectDescribeService.update(describe);
                if (Objects.nonNull(describe.getFieldDescribe("related_order_payment_count"))) {
                    jobScheduleService.submitCalculateJob(user, Lists.newArrayList("related_order_payment_count"), describe.getApiName());
                }
            } catch (MetadataServiceException e) {
                log.error("OpenPaymentMultiSource failed:",  e);
            }
            //初始化一个订单映射
            initOrderPaymentRule(user);
        }

        super.setConfigValue(user, value, oldValue, key);
    }
    private IObjectDescribe addOrderPaymentDescribeField(String tenantId, IObjectDescribe describe) {
        ObjectReferenceFieldDescribe fieldOrderId = (ObjectReferenceFieldDescribe) describe.getFieldDescribe(CommonOrderPaymentConstants.Field.OrderId.apiName);
        if (Objects.nonNull(fieldOrderId)) {
            fieldOrderId.setWheres(getOrderIdLookupWheres(fieldOrderId.getWheres()));
        }
        List<IFieldDescribe> describeFields = describe.getFieldDescribes();
        IFieldDescribe fieldSalesOrderProductId = FieldDescribeFactory.newInstance(SALES_ORDER_PRODUCT_ID);
        IFieldDescribe fieldSaleContractId = FieldDescribeFactory.newInstance(SALE_CONTRACT_ID);
        IFieldDescribe fieldSaleContractLineId = FieldDescribeFactory.newInstance(SALE_CONTRACT_LINE_ID);

        if (describe.getFieldDescribe(fieldSalesOrderProductId.getApiName()) == null) {
            describeFields.add(fieldSalesOrderProductId);
        }
        if (bizConfigThreadLocalCacheService.isOpenSaleContract(tenantId)) {
            if (describe.getFieldDescribe(fieldSaleContractId.getApiName()) == null) {
                describeFields.add(fieldSaleContractId);
            }
            if (describe.getFieldDescribe(fieldSaleContractLineId.getApiName()) == null) {
                describeFields.add(fieldSaleContractLineId);
            }
        }

        describe.setFieldDescribes(describeFields);
        return describe;
    }
    private List<LinkedHashMap> getOrderIdLookupWheres(List<LinkedHashMap> oldWheres) {
        if (oldWheres == null || oldWheres.isEmpty()) {
            return createDefaultWheres();
        }
        if (oldWheres.size() > 1) {
            return oldWheres;
        }

        // 处理已有条件
        Map<String, Object> firstMap = oldWheres.get(0);
        List<Map> oldFilters = (List<Map>) firstMap.get("filters");

        // 创建新条件
        List<Map> filters = new ArrayList<>(oldFilters != null ? oldFilters : Collections.emptyList());

        Map<String, Object> filter = createFilter("account_id", "EQ", 2, "$payment_id__r.account_id$");
        Map<String, Object> filter1 = createFilter("related_order_payment_count", "EQ", 0, "0");

        if (oldFilters != null) {
            filters.removeIf(f -> f.equals(filter));
            filters.removeIf(f -> f.equals(filter1));
        }

        filters.add(filter);
        filters.add(filter1);

        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("connector", Where.CONN.OR);
        map.put("filters", filters);

        List<LinkedHashMap> wheres = new ArrayList<>();
        wheres.add(map);

        return wheres;
    }
    private List<LinkedHashMap> createDefaultWheres() {
        List<Map> filters = new ArrayList<>();
        Map<String, Object> filter = createFilter("account_id", "EQ", 0, "$payment_id__r.account_id$");
        Map<String, Object> filter1 = createFilter("related_order_payment_count", "EQ", 0, "0");
        filters.add(filter);
        filters.add(filter1);

        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("connector", Where.CONN.OR);
        map.put("filters", filters);

        List<LinkedHashMap> wheres = new ArrayList<>();
        wheres.add(map);

        return wheres;
    }

    private Map<String, Object> createFilter(String fieldName, String operator, int valueType, String fieldValue) {
        Map<String, Object> filter = new HashMap<>();
        filter.put("field_name", fieldName);
        filter.put("operator", operator);
        filter.put("value_type", valueType);
        filter.put("field_values", Lists.newArrayList(fieldValue));
        return filter;
    }
    private IObjectDescribe addRelatedOrderPaymentCountField(String fieldApiName, IObjectDescribe describe) {
        List<IFieldDescribe> describeFields = describe.getFieldDescribes();
        IFieldDescribe field = FieldDescribeFactory.newInstance(fieldApiName);
        if (describe.getFieldDescribe(field.getApiName()) == null) {
            describeFields.add(field);
        } else {
            return describe;
        }

        describe.setFieldDescribes(describeFields);
        return describe;
    }

    private void initOrderPaymentRule(User user) {
        if (bizConfigThreadLocalCacheService.isOpenOrderPaymentMultiSource(user.getTenantId())) {
            return;
        }
        String configValue = configService.findTenantConfig(user, ORDER_PAYMENT_MAPPING_RULE.getKey());
        if (Objects.isNull(configValue)) {
            RequestContext requestContext = RequestContextManager.getContext();
            ServiceContext serviceContext = new ServiceContext(requestContext, null, null);
            enterpriseInitService.initObjectMappingRule(serviceContext, "rule_p_salesorderobj2op__c");
            String configStr = "[{\"fieldApiName\":\"order_id\",\"objectApiName\":\"SalesOrderObj\",\"ruleApiName\":\"rule_p_salesorderobj2op__c\"}]";
            configService.createTenantConfig(user, ORDER_PAYMENT_MAPPING_RULE.getKey(), configStr, ConfigValueType.STRING);
        }
    }
}
