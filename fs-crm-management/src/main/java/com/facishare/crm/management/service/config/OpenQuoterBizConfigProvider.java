package com.facishare.crm.management.service.config;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.management.utils.SFAManagamentI18NKeys;
import com.facishare.crm.sfa.predefine.service.AdvancedFormulaService;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @描述说明：配置 是否开启报价器（将初始化属性级联约束对象）
 * @作者：chench
 * @创建日期：2023-12-27
 */
@Component
@Slf4j
public class OpenQuoterBizConfigProvider  extends DefaultBizConfigProvider {

    @Autowired
    EnterpriseInitService enterpriseInitService;
    @Autowired
    private AdvancedFormulaService advancedFormulaService;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private BizConfigThreadLocalCacheService threadLocalCacheService;

    private static final String API_NAME_ATTRIBUTE_CONSTRAINT_OBJ = "AttributeConstraintObj";
    private static final String API_NAME_ATTRIBUTE_CONSTRAINT_LINES_OBJ = "AttributeConstraintLinesObj";
    private static final String API_NAME_PRODUCT_OBJ = "ProductObj";
    private static final String API_NAME_BOM_CORE_OBJ = "BomCoreObj";

    private static final String API_NAME_PRODUCT_CATEGORY_OBJ = "ProductCategoryObj";

    private static final String FIELD_NAME_ATTRIBUTE_CONSTRAINT_ID = "attribute_constraint_id";

    private static final String BOM_CORE_ATTRIBUTE_CONSTRAINT_ID_JSON = "{\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":false,\"description\":\"\",\"where_type\":\"field\",\"type\":\"object_reference\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"wheres\":[],\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"max_length\":60,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"target_api_name\":\"AttributeConstraintObj\",\"label\":\"属性级联约束方案\",\"target_related_list_name\":\"bom_core_attribute_constaint_list\",\"target_related_list_label\":\"产品组合\",\"action_on_target_delete\":\"cascade_delete\",\"related_wheres\":[],\"api_name\":\"attribute_constraint_id\",\"is_index_field\":true,\"status\":\"new\",\"help_text\":\"\"}";// ignoreI18n
    private static final String PRODUCT_ATTRIBUTE_CONSTRAINT_ID_JSON = "{\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":false,\"description\":\"\",\"where_type\":\"field\",\"type\":\"object_reference\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"wheres\":[],\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"max_length\":60,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"target_api_name\":\"AttributeConstraintObj\",\"label\":\"属性级联约束方案\",\"target_related_list_name\":\"product_attribute_constaint_list\",\"target_related_list_label\":\"产品\",\"action_on_target_delete\":\"cascade_delete\",\"related_wheres\":[],\"api_name\":\"attribute_constraint_id\",\"is_index_field\":true,\"status\":\"new\",\"help_text\":\"\"}";// ignoreI18n
    private static final String PRODUCT_CATEGORY_ATTRIBUTE_CONSTRAINT_ID_JSON = "{\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":false,\"description\":\"\",\"where_type\":\"field\",\"type\":\"object_reference\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"wheres\":[],\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"max_length\":60,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"target_api_name\":\"AttributeConstraintObj\",\"label\":\"属性级联约束方案\",\"target_related_list_name\":\"product_category_attribute_constaint_list\",\"target_related_list_label\":\"产品分类\",\"action_on_target_delete\":\"cascade_delete\",\"related_wheres\":[],\"api_name\":\"attribute_constraint_id\",\"is_index_field\":true,\"status\":\"new\",\"help_text\":\"\"}";// ignoreI18n
    @Override
    public String getConfigKey() {
        return BizConfigKey.IS_OPEN_QUOTER.getId();
    }




    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {
        if (!threadLocalCacheService.isOpenAttribute(user.getTenantId())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_OPEN_QUOTER_CONFIG_MUST_OPEN_ATTRIBUTE));
        }
        if(!Boolean.TRUE.equals(GrayUtil.isGrayEnable(user.getTenantId(), GrayUtil.QUOTER_OPEN_TENANT_ID))) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_OPEN_QUOTER_CONFIG_MUST_IN_GRAY));
        }
        //已开启则不允许关闭
        if ("1".equals(oldValue) || "0".equals(value)) {
            return;
        }
        List<String> describeList = Lists.newArrayList(API_NAME_ATTRIBUTE_CONSTRAINT_OBJ, API_NAME_ATTRIBUTE_CONSTRAINT_LINES_OBJ);
        Map<String, IObjectDescribe> describiMap = getDescribeWithSimplifiedChineseByApiNames(User.systemUser(user.getTenantId()), describeList);
        describeList.stream().forEach(apiName -> {
            IObjectDescribe describe = describiMap.get(apiName);
            if (describe == null) {
                enterpriseInitService.initDescribeForTenant(user.getTenantId(), apiName);
            }
            //先刷功能权限，从对象不处理
            if(!Objects.equals(API_NAME_ATTRIBUTE_CONSTRAINT_LINES_OBJ, apiName)) {
                enterpriseInitService.initPrivilegeRelate(Lists.newArrayList(apiName), user, null, null, null);
            }
            //刷布局
            List<ILayout> layouts = serviceFacade.findLayoutByObjectApiName(user.getTenantId(), apiName);

            if (layouts == null || layouts.isEmpty()) {
                enterpriseInitService.initMultiLayoutForOneTenant(Lists.newArrayList(apiName), user.getTenantId());
            }
        });
        advancedFormulaService.init(user.getTenantId());
        try {
            updateOtherDescribe(user);
        } catch (Exception e) {
            log.error("开通报价器时，更新产品、产品组合、产品分类字段描述失败：", e.getMessage());
            throw new ValidateException(I18N.text(SFAManagamentI18NKeys.SFA_OPEN_QUOTER_ADD_FIELD_ERROR));
        }
        super.setConfigValue(user, value, oldValue, key);
    }

    /**
     * 开通报价器后，产品、产品组合对象更新属性级联约束字段描述，默认布局不展示此字段
     * @param user
     */
    private void updateOtherDescribe(User user) throws MetadataServiceException {
        List<String> describeList = Lists.newArrayList(API_NAME_PRODUCT_OBJ, API_NAME_BOM_CORE_OBJ, API_NAME_PRODUCT_CATEGORY_OBJ);
        Map<String, IObjectDescribe> describiMap = getDescribeWithSimplifiedChineseByApiNames(User.systemUser(user.getTenantId()), describeList);
        for (Map.Entry<String, IObjectDescribe> entry : describiMap.entrySet()) {
            String apiName = entry.getKey();
            IObjectDescribe describe = entry.getValue();
            String fieldDescribeJson = null;
            if(describe.getFieldDescribe(FIELD_NAME_ATTRIBUTE_CONSTRAINT_ID) != null) {
                continue;
            }
            switch (apiName) {
                case API_NAME_PRODUCT_OBJ:
                    fieldDescribeJson = PRODUCT_ATTRIBUTE_CONSTRAINT_ID_JSON;
                    break;
                case API_NAME_BOM_CORE_OBJ:
                    fieldDescribeJson = BOM_CORE_ATTRIBUTE_CONSTRAINT_ID_JSON;
                    break;
                case API_NAME_PRODUCT_CATEGORY_OBJ:
                    fieldDescribeJson = PRODUCT_CATEGORY_ATTRIBUTE_CONSTRAINT_ID_JSON;
                    break;
                default:
                    break;
            }
            if(StringUtils.isNotBlank(fieldDescribeJson) && Boolean.TRUE.equals(describe.isUdef())){
                IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldDescribeJson);
                fieldDescribe.setDescribeApiName(apiName);
                describe.addFieldDescribe(fieldDescribe);
                objectDescribeService.update(describe);
                // 把新增的字段放入到指定的layout中的FormComponent中的基本信息FieldSection的里面
                FieldLayoutPojo fieldLayoutPojo = descUtil.getFieldLayoutPojo(SystemConstants.RenderType.ObjectReference.renderType, false, false);
                descUtil.insertFieldToLayout(user, describe, fieldDescribe, fieldLayoutPojo);
            }
        }
    }
}
