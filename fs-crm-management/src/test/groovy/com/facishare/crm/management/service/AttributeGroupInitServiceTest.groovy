package com.facishare.crm.management.service

import com.facishare.crm.RemoveUseless
import com.facishare.crm.openapi.Utils
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.service.IObjectDescribeService
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import org.junit.runner.RunWith
import org.mockito.MockitoAnnotations
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import spock.lang.Shared
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.powermock.api.mockito.PowerMockito.spy

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([SFAConfigUtil.class])
@SuppressStaticInitializationFor([ "com.facishare.crm.sfa.utilities.util.SFAConfigUtil"])
class AttributeGroupInitServiceTest extends RemoveUseless {
    @Shared
    private ServiceFacade serviceFacade;
    @Shared
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService
    @Shared
    private EnterpriseInitService enterpriseInitService
    @Shared
    private IObjectDescribeService objectDescribeService;
    @Shared
    private AttributeGroupInitService tester
    @Shared
    private User user

    def setupSpec() {
        removeConfigFactory()
        PowerMockito.mockStatic(SFAConfigUtil)
        MockitoAnnotations.initMocks(this)
        tester = spy(new AttributeGroupInitService())
        serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        Whitebox.setInternalState(tester, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        enterpriseInitService = PowerMockito.mock(EnterpriseInitService)
        Whitebox.setInternalState(tester, "enterpriseInitService", enterpriseInitService)
        objectDescribeService = PowerMockito.mock(IObjectDescribeService)
        Whitebox.setInternalState(tester, "objectDescribeService", objectDescribeService)
        user = spy(new User("71568", "-10000"))
    }

    def "test handle"() {
        given:
        Map<String, IObjectDescribe> map2 = new HashMap()
        map2.put(Utils.ATTRIBUTE_OBJ_API_NAME, new ObjectDescribe())
        map2.put(Utils.NONSTANDARD_ATTRIBUTE_OBJ_API_NAME, new ObjectDescribe())
        PowerMockito.when(serviceFacade.findObjects(any() as String, any() as Collection)).thenReturn(map2)
        PowerMockito.when(SFAConfigUtil.getConfigValue(any(), any(), any())).thenReturn("1")
        PowerMockito.when(bizConfigThreadLocalCacheService.isCloseOldCategory(any() as String)).thenReturn(true)
        when:
        tester.handle(user, descName)
        then:
        1 == 1
        where:
        dataId | descName
        "1"    | Utils.ATTRIBUTE_OBJ_API_NAME
        "1"    | Utils.NONSTANDARD_ATTRIBUTE_OBJ_API_NAME
    }
}
