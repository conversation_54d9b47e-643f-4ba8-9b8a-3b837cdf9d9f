package com.facishare.crm.management.service.config

import com.facishare.crm.management.service.config.model.*
import com.facishare.crm.sfa.utilities.util.GrayUtil
import com.facishare.crm.util.CommonSqlUtils
import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.service.OrgService
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds
import com.facishare.paas.appframework.common.util.AppIdMapping
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.function.FunctionLogicService
import com.facishare.paas.appframework.function.UdefFunctionExt
import com.facishare.paas.metadata.api.IUdefFunction
import com.facishare.paas.metadata.api.action.ActionContext
import com.facishare.paas.metadata.api.service.ICommonSqlService
import com.facishare.paas.metadata.impl.UdefFunction
import com.facishare.paas.metadata.support.GDSHandler
import com.facishare.polling.api.util.PollingMessageProducer
import com.google.common.collect.Lists
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import spock.lang.Shared
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.*

/**
 * @描述说明 ：
 *
 * @作 者：chench
 *
 * @创建日 期：2024-05-31
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([I18N.class, AppIdMapping.class, GrayUtil.class, CommonSqlUtils.class])
@SuppressStaticInitializationFor(["com.facishare.paas.I18N", "com.facishare.paas.appframework.common.util.AppIdMapping", "com.facishare.crm.sfa.utilities.util.GrayUtil", "com.facishare.crm.util.CommonSqlUtils"])

class BizConfigServiceTest extends Specification {
    @Mock
    @Shared
    private BizConfigService bizConfigService;
    @Mock
    @Shared
    private BizConfigProviderManager bizConfigProviderManager;
    @Mock
    @Shared
    private ICommonSqlService commonSqlService;
    @Mock
    @Shared
    private GDSHandler gdsHandler;
    @Mock
    @Shared
    private PollingMessageProducer pollingMessageProducer;
    @Mock
    @Shared
    private FunctionLogicService functionLogicService;
    @Mock
    @Shared
    private ServiceFacade serviceFacade;

    @Mock
    @Shared
    private DefaultBizConfigProvider defaultBizConfigProvider;

    @Shared
    private RequestContext requestContext;
    @Shared
    private ServiceContext serviceContext;

    def setupSpec() {
        PowerMockito.mockStatic(I18N.class);
        PowerMockito.mockStatic(AppIdMapping.class)
        MockitoAnnotations.initMocks(this);
        bizConfigService = new BizConfigService();
        requestContext = RequestContext.builder().tenantId("82909").user(new User("82909", "-10000")).build()
        serviceContext = new ServiceContext(requestContext, "", "");
        Whitebox.setInternalState(bizConfigService, "commonSqlService", commonSqlService);
        Whitebox.setInternalState(bizConfigService, "bizConfigProviderManager", bizConfigProviderManager);
        Whitebox.setInternalState(bizConfigService, "gdsHandler", gdsHandler);
        Whitebox.setInternalState(bizConfigService, "serviceFacade", serviceFacade);
    }

    def "test setConfigValue method"() {
        given:
        SetConfigValue.Arg arg = new SetConfigValue.Arg();
        arg.setKey("cpq");
        arg.setValue("1");

        when:
        PowerMockito.doReturn("0").when(defaultBizConfigProvider, "getConfigValue", serviceContext.getUser(), arg.getKey());
        PowerMockito.doNothing().when(defaultBizConfigProvider, "setConfigValue", serviceContext.getUser(), arg.getValue(), arg.getOldValue(), arg.getKey());
        PowerMockito.doNothing().when(defaultBizConfigProvider, "validateSetConfig", serviceContext.getUser(), arg.getKey(), arg.getValue());
        PowerMockito.doReturn(defaultBizConfigProvider).when(bizConfigProviderManager, "getProvider", arg.getKey());

        SetConfigValue.Result result = bizConfigService.setConfigValue(serviceContext, arg);
        then:
        result != null;
    }

    def "test getConfigValue method by prm"() {
        given:
        requestContext.getAppId() >> "prm";
        GetConfigValue.Arg arg = new GetConfigValue.Arg();
        arg.setKey("21");
        when:
        PowerMockito.mockStatic(AppIdMapping);
        PowerMockito.when(AppIdMapping.isPRM(serviceContext.getAppId())).thenReturn(true);
        GetConfigValue.Result result = bizConfigService.getConfigValue(serviceContext, arg);
        then:
        result != null && "0" == result.getValue();
    }

    def "test getConfigValue method"() {
        given:
        
        GetConfigValue.Arg arg = new GetConfigValue.Arg();
        arg.setKey("cpq");
        when:
        DefaultBizConfigProvider defaultBizConfigProvider = PowerMockito.mock(DefaultBizConfigProvider.class);

        PowerMockito.doReturn("1").when(defaultBizConfigProvider, "getConfigValue", serviceContext.getUser(), arg.getKey());
        PowerMockito.doReturn(defaultBizConfigProvider).when(bizConfigProviderManager, "getProvider", arg.getKey());

        PowerMockito.mockStatic(AppIdMapping);
        PowerMockito.when(AppIdMapping.isPRM(serviceContext.getAppId())).thenReturn(false);

        GetConfigValue.Result result = bizConfigService.getConfigValue(serviceContext, arg);
        then:
        result != null && "1" == result.getValue();
    }

    def "test deleteTenantConfig method"() {
        given:

        
        GetConfigValue.Arg arg = new GetConfigValue.Arg();
        arg.setKey("cpq");
        when:
        DefaultBizConfigProvider defaultBizConfigProvider = PowerMockito.mock(DefaultBizConfigProvider.class);

        PowerMockito.doNothing().when(defaultBizConfigProvider, "deleteTenantConfig", serviceContext.getUser(), "cpq");
        PowerMockito.doReturn("1").when(defaultBizConfigProvider, "getConfigValue", serviceContext.getUser(), "cpq");
        PowerMockito.doReturn(defaultBizConfigProvider).when(bizConfigProviderManager, "getDefaultProvider");

        GetConfigValue.Result result = bizConfigService.deleteTenantConfig(serviceContext, arg);
        then:
        result != null && StringUtils.isBlank(result.getValue());
    }

    def "test getConfigValues method by arg is null"() {
        given:

        
        when:
        GetConfigValues.Result result = bizConfigService.getConfigValues(serviceContext, null);
        then:
        result != null && CollectionUtils.isEmpty(result.getValues());
    }

    def "test getConfigValues method by arg with is allConfig"() {
        given:

        
        GetConfigValues.Arg arg = new GetConfigValues.Arg();
        arg.setIsAllConfig(true);
        arg.setKeys(Lists.newArrayList("cpq"))
        Map resultMap = new HashMap();
        resultMap.put("cpq", "1");
        when:
        PowerMockito.mockStatic(GrayUtil);
        PowerMockito.when(GrayUtil.isCustomerFilingChecker(serviceContext.getTenantId())).thenReturn(false);

        DefaultBizConfigProvider defaultBizConfigProvider = PowerMockito.mock(DefaultBizConfigProvider.class);

        PowerMockito.doReturn(resultMap).when(defaultBizConfigProvider, "getAllConfigValues", serviceContext.getUser());
        PowerMockito.doReturn(defaultBizConfigProvider).when(bizConfigProviderManager, "getDefaultProvider");

        GetConfigValues.Result result = bizConfigService.getConfigValues(serviceContext, arg);
        then:
        result != null && CollectionUtils.isNotEmpty(result.getValues());
    }

    def "test getConfigValues method by arg with is not allConfig"() {
        given:
        
        GetConfigValues.Arg arg = new GetConfigValues.Arg();
        arg.setIsAllConfig(false);
        arg.setKeys(Lists.newArrayList("cpq"))
        Map resultMap = new HashMap();
        resultMap.put("cpq", "1");
        when:
        PowerMockito.mockStatic(GrayUtil);
        PowerMockito.when(GrayUtil.isCustomerFilingChecker(serviceContext.getTenantId())).thenReturn(false);

        PowerMockito.mockStatic(AppIdMapping);
        PowerMockito.when(AppIdMapping.isPRM(serviceContext.getAppId())).thenReturn(false);

        DefaultBizConfigProvider defaultBizConfigProvider = PowerMockito.mock(DefaultBizConfigProvider.class);
        PowerMockito.doReturn(defaultBizConfigProvider).when(bizConfigProviderManager, "getProvider", "cpq");

        PowerMockito.doReturn("1").when(defaultBizConfigProvider, "getConfigValue", serviceContext.getUser(), "cpq");
        PowerMockito.doReturn(defaultBizConfigProvider).when(bizConfigProviderManager, "getDefaultProvider");

        GetConfigValues.Result result = bizConfigService.getConfigValues(serviceContext, arg);
        then:
        result != null && CollectionUtils.isNotEmpty(result.getValues());
    }

    def "test setConfigValues method"() {
        given:
        
        List<SetConfigValues.ArgData> list = Lists.newArrayList();
        SetConfigValues.ArgData argData = new SetConfigValues.ArgData();
        argData.setKey("cpq");
        argData.setValue("1");
        list.add(argData);
        SetConfigValues.Arg arg = new SetConfigValues.Arg();
        arg.setConfigInfoList(list);

        when:
        DefaultBizConfigProvider defaultBizConfigProvider = PowerMockito.mock(DefaultBizConfigProvider.class);
        PowerMockito.when(bizConfigProviderManager.getProvider("cpq")).thenReturn(defaultBizConfigProvider);
        PowerMockito.when(defaultBizConfigProvider.getConfigValue(serviceContext.getUser(), "cpq")).thenReturn("1");
        PowerMockito.doNothing().when(defaultBizConfigProvider).setConfigValue(serviceContext.getUser(), "1", "0", "cpq");
        SetConfigValues.Result result = bizConfigService.setConfigValues(serviceContext, arg);

        then:
        result != null;
    }


    def "test  getRemindRule method"() {
        given:

        when:
        Object exeResult = Whitebox.invokeMethod(bizConfigService, "getRemindRule", Integer.valueOf(10), Integer.valueOf(10), Integer.valueOf(10));
        then:
        exeResult != null;
    }

    def "test  getCircleInfo method"() {
        given:
        List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = Lists.newArrayList();
        QueryDeptInfoByDeptIds.DeptInfo deptInfo = new QueryDeptInfoByDeptIds.DeptInfo();
        deptInfo.setDeptId("111");
        deptInfo.setDeptName("1");
        deptInfo.setDeptType("22");
        deptInfos.add(deptInfo);
        when:
        Object exeResult = Whitebox.invokeMethod(bizConfigService, "getCircleInfo", "111", deptInfos);
        then:
        exeResult != null;
    }

    def "test  getAccountFilingCheckerIds method"() {
        given:
        
        serviceContext.getTenantId()>>requestContext.getUser().getTenantId();
        when:
        PowerMockito.mockStatic(CommonSqlUtils);
        PowerMockito.when(CommonSqlUtils.convert2ActionContext(serviceContext)).thenReturn(new ActionContext());
        PowerMockito.doReturn([["employee_id": Integer.valueOf(11)]]).when(commonSqlService, "select", anyString(), anyList(), any());
        Object exeResult = Whitebox.invokeMethod(bizConfigService, "getAccountFilingCheckerIds", serviceContext);
        then:
        exeResult != null;
    }

    def "test getSetUserConfig method with arg is null"() {
        given:
        
        when:
        GetSetConfigValue.Result result = bizConfigService.getSetUserConfig(serviceContext, null);
        then:
        result != null && result.success == true;
    }

    def "test getSetUserConfig method with arg not null"() {
        given:
        
        GetSetConfigValue.Arg arg = new GetSetConfigValue.Arg();
        arg.setKey("cpq");
        arg.setValue("1");
        GetSetConfigValue.Result mockResult = GetSetConfigValue.Result.builder().success(true).build();
        when:
        BizConfigProvider provider = PowerMockito.mock(BizConfigProvider.class);
        PowerMockito.when(provider.getSetUserConfig(serviceContext.getUser(), arg.getKey(), arg.getValue())).thenReturn(mockResult);
        PowerMockito.when(bizConfigProviderManager.getProvider(arg.getKey())).thenReturn(provider);
        GetSetConfigValue.Result result = bizConfigService.getSetUserConfig(serviceContext, arg);
        then:
        result != null && result.success == true;
    }

    def "test getUserConfig method"() {
        given:
        
        GetSetConfigValue.Arg arg = new GetSetConfigValue.Arg();
        arg.setKey("cpq");
        arg.setValue("1");
        GetSetConfigValue.Result mockResult = GetSetConfigValue.Result.builder().success(true).build();
        when:
        BizConfigProvider provider = PowerMockito.mock(BizConfigProvider.class);
        PowerMockito.when(provider.getUserConfig(serviceContext.getUser(), arg.getKey())).thenReturn(mockResult);
        PowerMockito.when(bizConfigProviderManager.getProvider(arg.getKey())).thenReturn(provider);
        GetSetConfigValue.Result result = bizConfigService.getUserConfig(serviceContext, arg);
        then:
        result != null && result.success == true;
    }

    def "test setUserConfig method"() {
        given:
        
        GetSetConfigValue.Arg arg = new GetSetConfigValue.Arg();
        arg.setKey("cpq");
        arg.setValue("1");
        GetSetConfigValue.Result mockResult = GetSetConfigValue.Result.builder().success(true).build();

        when:
        DefaultBizConfigProvider defaultBizConfigProvider = PowerMockito.mock(DefaultBizConfigProvider.class);

        PowerMockito.doReturn(mockResult).when(defaultBizConfigProvider, "setUserConfig", serviceContext.getUser(), "cpq", "1");
        PowerMockito.doReturn(defaultBizConfigProvider).when(bizConfigProviderManager, "getDefaultProvider");
        GetSetConfigValue.Result result = bizConfigService.setUserConfig(serviceContext, arg);
        then:
        result != null && result.success == true;
    }

    def "test setConfigValueWithPolling method"() {
        given:
        
        SetConfigValue.PollingArg arg = new SetConfigValue.PollingArg();
        arg.setKey("cpq");
        arg.setValue("1");
        arg.setPollingKey("cpq");
        arg.setUser(Lists.newArrayList("1000"));
        arg.setOsType(webArg);

        when:
        DefaultBizConfigProvider defaultBizConfigProvider = PowerMockito.mock(DefaultBizConfigProvider.class);

        PowerMockito.doReturn("0").when(defaultBizConfigProvider, "getConfigValue", serviceContext.getUser(), arg.getKey());
        PowerMockito.doNothing().when(defaultBizConfigProvider, "setConfigValue", serviceContext.getUser(), arg.getValue(), arg.getOldValue(), arg.getKey());
        PowerMockito.doNothing().when(defaultBizConfigProvider, "validateSetConfig", serviceContext.getUser(), arg.getKey(), arg.getValue());
        PowerMockito.doReturn(defaultBizConfigProvider).when(bizConfigProviderManager, "getProvider", arg.getKey());

        PowerMockito.doReturn("89242").when(gdsHandler).getEAByEI("89242");
        PowerMockito.doNothing().when(pollingMessageProducer, "sendMessage", any());
        SetConfigValue.Result result = bizConfigService.setConfigValueWithPolling(serviceContext, arg);
        then:
        result != null;
        where:
        webArg || pollingKey
        null   || "cpq"
        "web"  || "cpq"
    }

    def "test queryFunction method"() {
        given:
        
        Function.Arg arg = new Function.Arg();
        arg.setObjectApiName("BomCoreObj");
        arg.setNamespace("range");
        when:
        PowerMockito.doReturn(functionLogicService).when(serviceFacade, "getFunctionLogicService");
        PowerMockito.doReturn(getFunctionList()).when(functionLogicService, "findFunctionByExample", any(), any());

        Function.Result result = bizConfigService.queryFunction(serviceContext, arg);
        then:
        result != null && result.getFunctionList() != null;
    }

    def "test bindFunction method"() {
        given:
        
        Function.BindArg arg = new Function.BindArg();
        arg.setObjectApiName("BomCoreObj");
        arg.setFunctionApiName("func_test__c");
        arg.setBind(isBind);
        arg.setKey(key);
        when:
        PowerMockito.mockStatic(I18N);
        if (key == null) {
            PowerMockito.when(I18N.text(any())).thenReturn("参数不为空");
        } else {
            PowerMockito.when(I18N.text(any())).thenReturn("操作成功");
        }

        DefaultBizConfigProvider defaultBizConfigProvider = PowerMockito.spy(new DefaultBizConfigProvider());
        Whitebox.setInternalState(bizConfigService, "defaultBizConfigProvider", defaultBizConfigProvider);
        PowerMockito.doReturn(queryConfigValue).when(defaultBizConfigProvider, "getConfigValue", serviceContext.getUser(), arg.getKey());
        PowerMockito.doNothing().when(defaultBizConfigProvider, "setConfigValue", any(), any(), any(), any());
        Function.BindResult result = bizConfigService.bindFunction(serviceContext, arg);
        then:
        result != null && result.success == expValue;
        where:
        isBind || key            || expValue || queryConfigValue
        true   || null           || false    || null
        false  || "biz_function" || true     || null
        true   || "biz_function" || true     || null
        false  || "biz_function" || true     || "{}"
        true   || "biz_function" || true     || "{}"
    }


    private List<IUdefFunction> getFunctionList() {
        UdefFunction udefFunction = new UdefFunction();
        udefFunction.setApiName("funt_test__c");
        udefFunction.setBindingObjectApiName("BomCoreObj");
        udefFunction.setFunctionName("测试使用");
        IUdefFunction function = UdefFunctionExt.of(udefFunction);
        return Lists.newArrayList(function);
    }

    private List<Map> getQueryResult() {
        return Lists.newArrayList();
    }

    private List<QueryDeptInfoByDeptIds.DeptInfo> getDeptInfos() {
        return Lists.newArrayList();
    }
}
