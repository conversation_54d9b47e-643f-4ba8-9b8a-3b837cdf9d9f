package com.facishare.crm.management.service.config

import com.facishare.crm.constants.SystemConstants
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.FieldLayoutPojo
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.service.IObjectDescribeService
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.CurrencyFieldDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import spock.lang.Shared
import spock.lang.Specification

import java.lang.reflect.Method

import static org.mockito.ArgumentMatchers.*

/**
 * @描述说明 ：
 *
 * @作 者：chench
 *
 * @创建日 期：2024-10-15
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([I18N.class, DefaultBizConfigProvider.class])
@SuppressStaticInitializationFor(["com.facishare.paas.I18N", "com.facishare.crm.management.service.config.DefaultBizConfigProvider"])
class OpenIncrementalPricingBizConfigProviderTest extends Specification {
    @Mock
    @Shared
    private BizConfigThreadLocalCacheService threadLocalCacheService;
    @Mock
    @Shared
    private ServiceFacade serviceFacade;
    @Mock
    @Shared
    private IObjectDescribeService objectDescribeService;
    @Mock
    @Shared
    private DescUtil descUtil;
    @Shared
    private OpenIncrementalPricingBizConfigProvider openIncrementalPricingBizConfigProvider;

    def setupSpec() {
        MockitoAnnotations.initMocks(this);
        openIncrementalPricingBizConfigProvider = new OpenIncrementalPricingBizConfigProvider();
        Whitebox.setInternalState(openIncrementalPricingBizConfigProvider, "threadLocalCacheService", threadLocalCacheService);
        Whitebox.setInternalState(openIncrementalPricingBizConfigProvider, "serviceFacade", serviceFacade);
        Whitebox.setInternalState(openIncrementalPricingBizConfigProvider, "objectDescribeService", objectDescribeService);
        Whitebox.setInternalState(openIncrementalPricingBizConfigProvider, "descUtil", descUtil);
    }

    def "test getConfigKey method"() {
        when:
        String result = openIncrementalPricingBizConfigProvider.getConfigKey();
        then:
        result == "is_open_incremental_pricing";
    }

    def "test validateSetConfig method"() {
        given:
        User user = User.builder().tenantId("89242").userId("1000").build();
        when:
        PowerMockito.mockStatic(I18N);
        PowerMockito.when(I18N.text(any())).thenReturn("参数不合法");
        openIncrementalPricingBizConfigProvider.validateSetConfig(user, "is_open_incremental_pricing", "2");
        then:
        thrown(ValidateException);
    }

    def "test setConfigValue method"() {
        given:
        User user = User.builder().tenantId("89242").userId("1000").build();
        when:
        Method method = PowerMockito.method(DefaultBizConfigProvider.class, "setConfigValue");
        PowerMockito.suppress(method);
        PowerMockito.mockStatic(I18N);
        PowerMockito.when(I18N.text(any())).thenReturn("参数不合法");
        PowerMockito.doReturn(openAttr).when(threadLocalCacheService, "isOpenAttribute", anyString());
        PowerMockito.doReturn(getAttrPBObjDescribe(isAttrPBObjExist)).when(serviceFacade, "findObject", user.getTenantId(), "AttributePriceBookObj");
        PowerMockito.doReturn(null).when(objectDescribeService, "update", any());
        PowerMockito.doReturn(new FieldLayoutPojo()).when(descUtil, "getFieldLayoutPojo", SystemConstants.RenderType.SelectOne.renderType, false, true);
        PowerMockito.doNothing().when(descUtil, "insertFieldToLayoutByLayoutTypes", any(User.class), any(IObjectDescribe.class), any(IFieldDescribe.class), any(FieldLayoutPojo.class), anyList());

        PowerMockito.doReturn(getAttrPBLinesObjDescribe(isAttrPBLinesObjExist)).when(serviceFacade, "findObject", user.getTenantId(), "AttributePriceBookLinesObj");
        PowerMockito.doReturn(toUpdatedDataList(hasData, cnt)).when(serviceFacade, "findBySearchQuery", any(User.class), any(String.class), any(SearchTemplateQuery.class));
        PowerMockito.doReturn(null).when(serviceFacade, "batchUpdateByFields", any(User.class), anyList(), anyList());
        try {
            openIncrementalPricingBizConfigProvider.setConfigValue(user, newValue, oldValue, "is_open_incremental_pricing");
        } catch (Exception e) {
            assert e instanceof ValidateException;
        }
        then:
        1 == 1;
        where:
        oldValue || newValue || openAttr || needError || isAttrPBObjExist || isAttrPBLinesObjExist || hasData || cnt
        "1"      || "0"      || false    || true      || false            || false                 || false   || Integer.valueOf(1)
        "1"      || "1"      || false    || true      || false            || false                 || false   || Integer.valueOf(1)
        "0"      || "1"      || false    || true      || false            || false                 || false   || Integer.valueOf(1)
        "0"      || "1"      || true     || false     || false            || false                 || false   || Integer.valueOf(1)
        "0"      || "1"      || true     || false     || true             || false                 || false   || Integer.valueOf(1)
        "0"      || "1"      || true     || false     || true             || true                  || false   || Integer.valueOf(1)
        "0"      || "1"      || true     || false     || true             || true                  || true    || Integer.valueOf(1)
        "0"      || "1"      || true     || false     || true             || true                  || true    || Integer.valueOf(2001)
    }

    def "test updateAttributePriceBookData method"() {
        given:
        User user = User.builder().tenantId("89242").userId("1000").build();
        when:

        PowerMockito.doReturn(toUpdatedDataList(hasData, cnt)).when(serviceFacade, "findBySearchQuery", any(User.class), any(String.class), any(SearchTemplateQuery.class));
        PowerMockito.doReturn(null).when(serviceFacade, "batchUpdateByFields", any(User.class), anyList(), anyList());
        try {
            openIncrementalPricingBizConfigProvider.updateAttributePriceBookData(user, 1);
        } catch (Exception e) {
            e.printStackTrace();
            assert e instanceof ValidateException;
        }
        then:
        1 == 1;
        where:
        isAttrPBObjExist || hasData || cnt
        true             || true    || Integer.valueOf(1)
        true             || true    || Integer.valueOf(2001)
    }

    private IObjectDescribe getAttrPBObjDescribe(boolean isExist) {
        if (isExist) {
            IObjectDescribe describe = new ObjectDescribe();
            describe.setApiName("AttributePriceBookObj");
            return describe;
        } else {
            return null;
        }
    }

    private IObjectDescribe getAttrPBLinesObjDescribe(boolean isExist) {
        if (isExist) {
            IObjectDescribe describe = new ObjectDescribe();
            describe.setApiName("AttributePriceBookLinesObj");

            IFieldDescribe sellingPrice = new CurrencyFieldDescribe();
            sellingPrice.setApiName("selling_price");
            describe.addFieldDescribe(sellingPrice);
            return describe;
        } else {
            return null;
        }
    }

    private QueryResult<IObjectData> toUpdatedDataList(boolean hasData, Integer cnt) {
        if (hasData) {
            QueryResult<IObjectData> queryResult = new QueryResult<>();
            List<IObjectData> dataList = new ArrayList<>();
            dataList.add(new ObjectData(["_id": "100"]));
            dataList.add(new ObjectData(["_id": "200"]));
            queryResult.setData(dataList);
            queryResult.setTotalNumber(cnt);
            return queryResult;
        } else {
            return null;
        }
    }
}
