package com.facishare.crm.management.service.config


import com.facishare.crm.sfa.predefine.SFAPreDefineObject
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.utilities.util.GrayUtil
import com.facishare.paas.I18N
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.service.ButtonService
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.service.IObjectDescribeService
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.powermock.api.mockito.PowerMockito.spy

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([SFAPreDefineObject, I18N, GrayUtil])
@PowerMockIgnore(["javax.management.*"])
@SuppressStaticInitializationFor([
        "com.facishare.paas.I18N",
        "com.facishare.crm.sfa.utilities.util.GrayUtil", "com.facishare.crm.sfa.predefine.SFAPreDefineObject"])
class OrderCloseBizConfigProviderTest extends Specification {
    def setupSpec() {
        PowerMockito.mockStatic(I18N.class)
        PowerMockito.mockStatic(GrayUtil)
        PowerMockito.mockStatic(SFAPreDefineObject)
    }

    def "test setConfigValue"() {
        given:
        def tester = spy(new OrderCloseBizConfigProvider())
        def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        Whitebox.setInternalState(tester, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(deliveryNoteEnabled).when(bizConfigThreadLocalCacheService, "isDeliveryNoteEnabled", any())
        PowerMockito.doReturn(isOpenAccountsReceivable).when(bizConfigThreadLocalCacheService, "isOpenAccountsReceivable", any())
        def objectDescribeService = PowerMockito.mock(IObjectDescribeService)
        Whitebox.setInternalState(tester, "objectDescribeService", objectDescribeService)
        PowerMockito.doReturn(null).when(objectDescribeService, "update", any())
        def buttonService = PowerMockito.mock(ButtonService)
        Whitebox.setInternalState(tester, "buttonService", buttonService)
        PowerMockito.doReturn(null).when(buttonService, "create", any(), any())
        def configService = PowerMockito.mock(ConfigService)
        Whitebox.setInternalState(tester, "configService", configService)
        PowerMockito.doReturn(null).when(configService, "findTenantConfig", any(), any())
        PowerMockito.doNothing().when(configService, "createTenantConfig", any(), any(), any(), any())
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        IObjectDescribe describe = new com.facishare.paas.metadata.impl.describe.ObjectDescribe()
        describe.setApiName("SalesOrderObj")
        PowerMockito.doReturn(describe).when(serviceFacade, "findObject", any(), any())
        PowerMockito.doReturn(null).when(serviceFacade, "batchCreateFunc", any(), any(), any())
        PowerMockito.doNothing().when(serviceFacade, "updateUserDefinedFuncAccess", any(), any(), any(), any(), any())
        when:
        try {
            Whitebox.invokeMethod(tester, "configValueSet", User.systemUser("71653"), value, "order_close")
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then:
        1 == 1
        where:
        deliveryNoteEnabled | isOpenAccountsReceivable | value
        false               | false                    | ""
        true                | false                    | ""
        true                | false                    | "{\"delivery_status\":\"1\",\"accounts_receivable_status\":\"1\"}"
        true                | false                    | "{\"status\":\"1\",\"delivery_status\":\"0\",\"accounts_receivable_status\":\"1\"}"
        true                | false                    | "{\"status\":\"1\",\"delivery_status\":\"1\",\"accounts_receivable_status\":\"1\"}"
        true                | true                     | "{\"status\":\"1\",\"delivery_status\":\"1\",\"accounts_receivable_status\":\"1\"}"
    }
}
