package com.facishare.crm.management.service.config

import com.facishare.crm.RemoveUseless
import com.facishare.crm.openapi.Utils
import com.facishare.crm.rest.SettingLogProxy
import com.facishare.crm.sfa.predefine.service.DescribeWithSimplifiedChineseService
import com.facishare.crm.sfa.predefine.service.task.TaskService
import com.facishare.crm.sfa.task.AsyncTaskProducer
import com.facishare.crm.sfa.utilities.util.GrayUtil
import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.service.OrgService
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.google.common.collect.Lists
import org.junit.runner.RunWith
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.slf4j.LoggerFactory
import org.spockframework.runtime.Sputnik
import spock.lang.Unroll

import static org.powermock.reflect.Whitebox.getInternalState
import static org.powermock.reflect.Whitebox.setInternalState

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([I18N, GrayUtil])
@PowerMockIgnore(["javax.management.*"])
class OrderTransferProviderTest extends RemoveUseless {
    
    OrderTransferProvider orderTransferProvider
    AsyncTaskProducer asyncTaskProducer
    User user
    IObjectDescribe objectDescribe
    ConfigService configService
    SettingLogProxy logServiceProxy
    OrgService orgService
    DescUtil descUtil

    def setupSpec() {
        removeI18N()
        setInternalState(I18N, "I18N_RESOURCES", Lists.newArrayList())
        setInternalState(I18N, "log", LoggerFactory.getLogger(I18N.class))
        removeConfigFactory()
    }
    
    def setup() {
        orderTransferProvider = new OrderTransferProvider()
        asyncTaskProducer = Mock(AsyncTaskProducer)
        user = Mock(User)
        objectDescribe = Mock(IObjectDescribe)
        configService = Mock(ConfigService)
        logServiceProxy = Mock(SettingLogProxy)
        orgService = Mock(OrgService)
        descUtil = Mock(DescUtil)

        orderTransferProvider.asyncTaskProducer = asyncTaskProducer
        orderTransferProvider.descUtil = descUtil

        def describeWithSimplifiedChineseService = Mock(DescribeWithSimplifiedChineseService)
        setInternalState(orderTransferProvider, "describeWithSimplifiedChineseService", describeWithSimplifiedChineseService)
        setInternalState(orderTransferProvider, "configService", configService)
        setInternalState(orderTransferProvider, "logServiceProxy", logServiceProxy)
        setInternalState(orderTransferProvider, "orgService", orgService)
        setInternalState(orderTransferProvider, "descUtil", descUtil)

        user.getTenantId() >> "testTenantId"
        user.getUserId() >> "testUserId"
        user.getUserName() >> "testUserName"

    }
    
    def "测试getConfigKey应该返回正确的配置键值"() {
        when: "调用getConfigKey方法"
        def result = orderTransferProvider.getConfigKey()
        
        then: "返回值应该是ONE_CLICK_ORDER_TRANSFER的ID"
        result == BizConfigKey.ONE_CLICK_ORDER_TRANSFER.getId()
    }
    
    @Unroll
    def "测试setConfigValue方法正常场景"() {
        given: "准备测试数据和Mock"
        def fieldDescribe = Mock(IFieldDescribe)
        objectDescribe.getFieldDescribe("ship_to_party") >> fieldDescribe
        def describeWithSimplifiedChineseService = getInternalState(orderTransferProvider, "describeWithSimplifiedChineseService") as DescribeWithSimplifiedChineseService
        describeWithSimplifiedChineseService.findByDescribeApiName(user, Utils.SALES_ORDER_API_NAME) >> objectDescribe
        configService.findTenantConfig(user, key) >> null
        orgService.getUser(user.getTenantId(), user.getUserId()) >> user

        when: "调用setConfigValue方法"
        orderTransferProvider.setConfigValue(user, value, oldValue, key)
        then:
        1==1
        where:
        value   | oldValue | key
        "test1" | "old1"   | BizConfigKey.ONE_CLICK_ORDER_TRANSFER.getId()
        "test2" | "old2"   | BizConfigKey.ONE_CLICK_ORDER_TRANSFER.getId()
    }
    
    @Unroll
    def "测试setConfigValue方法需要刷新描述场景"() {
        given: "准备测试数据和Mock"
        objectDescribe.getFieldDescribe("ship_to_party") >> null
        def describeWithSimplifiedChineseService = getInternalState(orderTransferProvider, "describeWithSimplifiedChineseService") as DescribeWithSimplifiedChineseService
        describeWithSimplifiedChineseService.findByDescribeApiName(user, Utils.SALES_ORDER_API_NAME) >> objectDescribe
        configService.findTenantConfig(user, key) >> "oldConfig"
        orgService.getUser(user.getTenantId(), user.getUserId()) >> user
        
        when: "调用setConfigValue方法"
        orderTransferProvider.setConfigValue(user, value, oldValue, key)
        
        then: "验证描述刷新相关方法是否被正确调用"
        1==1
        

        
        where:
        value   | oldValue | key
        "test1" | "old1"   | BizConfigKey.ONE_CLICK_ORDER_TRANSFER.getId()
        "test2" | "old2"   | BizConfigKey.ONE_CLICK_ORDER_TRANSFER.getId()
    }
    
    def "测试setConfigValue方法当describe为null时的场景"() {
        given: "准备测试数据和Mock"
        def describeWithSimplifiedChineseService = getInternalState(orderTransferProvider, "describeWithSimplifiedChineseService") as DescribeWithSimplifiedChineseService
        describeWithSimplifiedChineseService.findByDescribeApiName(user, Utils.SALES_ORDER_API_NAME) >> null
        configService.findTenantConfig(user, BizConfigKey.ONE_CLICK_ORDER_TRANSFER.getId()) >> null
        orgService.getUser(user.getTenantId(), user.getUserId()) >> user
        
        when: "调用setConfigValue方法"
        orderTransferProvider.setConfigValue(user, "testValue", "oldValue", BizConfigKey.ONE_CLICK_ORDER_TRANSFER.getId())

        then:
        1==1
    }
} 