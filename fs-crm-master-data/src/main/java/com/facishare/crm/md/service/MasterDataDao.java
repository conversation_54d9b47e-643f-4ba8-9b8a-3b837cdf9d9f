package com.facishare.crm.md.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.facishare.crm.md.enums.StrategyStatusEnum;
import com.facishare.crm.md.enums.StrategyTypeEnum;
import com.facishare.crm.md.model.MasterDataAppModel;
import com.facishare.crm.md.model.MasterDataStrategyModel;
import com.facishare.crm.md.rest.LinkDataSyncProxy;
import com.facishare.crm.md.rest.model.DataSyncModel;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.platform.utils.ResourcesIOUtils;
import com.facishare.crm.util.RestUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.md.constant.MasterDataAppConstant.*;
import static com.facishare.crm.md.constant.MasterDataAppI18NKeyConstant.*;
import static com.facishare.crm.md.constant.MasterDataOrgMappingConstant.DOWNSTREAM_TENANT_ID;
import static com.facishare.crm.md.constant.MasterDataOrgMappingConstant.MASTER_DATA_ORG_MAPPING_OBJ;

/**
 * 把所有config，pg 库数据归集到一起
 *
 * <AUTHOR>
 * @date 2022/12/8 16:00
 */
@Service
@Slf4j
public class MasterDataDao {
    @Autowired
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private LinkDataSyncProxy linkDataSyncProxy;
    @Autowired
    private DescribeLogicService describeLogicService;

    public Set<String> getAppConfigApiNameList(User user) {
        List<IObjectData> objectDataList = metaDataFindServiceExt.findAllObjectIgnoreAll(user, MASTER_DATA_APP_OBJ);
        return objectDataList.stream().filter(d -> d.get(MASTER_OBJECT_API_NAME) != null).map(d -> d.get(MASTER_OBJECT_API_NAME).toString()).collect(Collectors.toSet());
    }

    public MasterDataAppModel.AppConfig getAppConfigByObject(User user, String objectApiName) {
        IObjectData masterDatAppObjData = getAppObjectDataByObject(user, objectApiName);
        if (masterDatAppObjData == null) {
            return null;
        }
        List<MasterDataAppModel.AppConfig> appConfigs = buildAppConfigData(Lists.newArrayList(masterDatAppObjData));
        return appConfigs.get(0);
    }

    private List<MasterDataAppModel.AppConfig> buildAppConfigData(List<IObjectData> masterDataAppDataList) {
        List<MasterDataAppModel.AppConfig> appConfigList = Lists.newArrayList();
        for (IObjectData data : masterDataAppDataList) {
            MasterDataAppModel.AppConfig appConfig = new MasterDataAppModel.AppConfig();
            appConfig.setDataId(data.getId());
            appConfig.setDisplayName(data.getDisplayName());
            appConfig.setRemark(ObjectDataUtils.getValueOrDefault(data, REMARK, ""));
            appConfig.setApiName(ObjectDataUtils.getValueOrDefault(data, MASTER_OBJECT_API_NAME, ""));
            appConfig.setCollectId(ObjectDataUtils.getValueOrDefault(data, COLLECT_ID, ""));
            appConfig.setAllocateId(ObjectDataUtils.getValueOrDefault(data, ALLOCATE_ID, ""));
            appConfig.setDownMainCollectId(ObjectDataUtils.getValueOrDefault(data, DOWN_MAIN_COLLECT_ID, ""));
            appConfig.setDownMainAllocateId(ObjectDataUtils.getValueOrDefault(data, DOWN_MAIN_ALLOCATE_ID, ""));
            appConfigList.add(appConfig);
        }
        return appConfigList;
    }


    public List<IObjectData> getAppObjectDataList(User user) {
        return metaDataFindServiceExt.findAllObjectIgnoreAll(user, MASTER_DATA_APP_OBJ);
    }

    public IObjectData getAppObjectDataByObject(User user, String objectApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setSearchSource("db");
        SearchUtil.fillFilterEq(query.getFilters(), MASTER_OBJECT_API_NAME, objectApiName);
        List<IObjectData> dataList = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, MASTER_DATA_APP_OBJ, query).getData();
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        return dataList.get(0);
    }

    public List<IObjectData> getFieldRuleObjectDataList(User user, String objectApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setSearchSource("db");
        query.setPermissionType(0);
        query.setLimit(1000);
        List<IFilter> filters = query.getFilters();
        SearchUtil.fillFilterEq(filters, RULE_OBJECT_API_NAME, objectApiName);
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, MASTER_DATA_RULE_OBJ, query).getData();
    }

    public List<IObjectData> getMasterDataRuleField(User user, List<String> fields, String objectApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = query.getFilters();
        SearchUtil.fillFilterEq(filters, RULE_OBJECT_API_NAME, objectApiName);
        SearchUtil.fillFilterIn(filters, FIELD_API_NAME, fields);
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, MASTER_DATA_RULE_OBJ, query).getData();
    }

    public List<IObjectData> bulkCreateMainConfigField(User user, List<IObjectData> saveDataList) {
        return metaDataService.bulkSaveObjectData(saveDataList, user);
    }

    public List<IObjectData> findObjectDataByIds(String tenantId, List<String> ids, String objectApiName) {
        return metaDataService.findObjectDataByIds(tenantId, Lists.newArrayList(ids), objectApiName);
    }

    public void bulkDeleteDirect(List<IObjectData> ruleDataList, User user) {
        metaDataService.bulkDeleteDirect(ruleDataList, user);
    }

    public List<IObjectData> getFieldRuleObjectDataList(User user, String ruleObjectApiName, List<String> ids) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setSearchSource("db");
        query.setPermissionType(0);
        query.setLimit(1000);
        List<IFilter> filters = query.getFilters();
        SearchUtil.fillFilterEq(filters, RULE_OBJECT_API_NAME, ruleObjectApiName);
        SearchUtil.fillFilterIn(filters, DBRecord.ID, ids);
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, MASTER_DATA_RULE_OBJ, query).getData();
    }

    public void createObjectConfigData(User user, MasterDataAppModel.StrategyDetailArg strategyDetailArg) {
        IObjectData configData = getAppObjectDataByObject(user, strategyDetailArg.getObjectApiName());
        if (configData != null) {
            metaDataService.bulkDeleteDirect(Lists.newArrayList(configData), user);
        }
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put(DISPLAY_NAME, strategyDetailArg.getDisplayName());
        dataMap.put(ALLOCATE_ID, strategyDetailArg.getStrategyDetail().getAllocateId());
        dataMap.put(COLLECT_ID, strategyDetailArg.getStrategyDetail().getCollectId());
        dataMap.put(DOWN_MAIN_COLLECT_ID, strategyDetailArg.getOpenMasterStrategyDetail().getCollectId());
        dataMap.put(DOWN_MAIN_ALLOCATE_ID, strategyDetailArg.getOpenMasterStrategyDetail().getAllocateId());
        dataMap.put(MASTER_OBJECT_API_NAME, strategyDetailArg.getObjectApiName());
        dataMap.put(INITIALIZED, Boolean.FALSE);
        IObjectData data = ObjectDataDocument.of(dataMap).toObjectData();
        data.setTenantId(user.getTenantId());
        data.setDescribeApiName(MASTER_DATA_APP_OBJ);
        // 创建
        metaDataService.bulkSaveObjectData(Lists.newArrayList(data), user);
    }

    public IObjectDescribe findDescribe(String tenantId, String objectApiName) throws MetadataServiceException {
        return objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, objectApiName);
    }

    public IObjectDescribe findLoginDescribe(String tenantId, String objectApiName) throws MetadataServiceException {
        return describeLogicService.findObject(tenantId, objectApiName);
    }

    public List<String> getObjectRequiredFields(User user, String objectApiName) throws MetadataServiceException {
        IObjectDescribe describe = findDescribe(user.getTenantId(), objectApiName);
        List<IFieldDescribe> fieldDescribes = describe.getFieldDescribes();
        List<String> requiredFields = fieldDescribes.stream().filter(f -> f.isRequired() && f.isActive() && !"system".equals(f.getDefineType())).map(IFieldDescribe::getApiName).collect(Collectors.toList());
        return requiredFields;
    }

    public List<String> getStrategyTenants(User user, String strategyId, StrategyTypeEnum plotType) {
        if (StringUtils.isBlank(strategyId)) {
            return Lists.newArrayList();
        }
        DataSyncModel.GetTenantByPloyArg arg = new DataSyncModel.GetTenantByPloyArg();
        arg.setId(strategyId);
        arg.setPloyType(plotType.getStrCode());
        DataSyncModel.TenantResult result = linkDataSyncProxy.listDownstreamTenants(RestUtils.getDDSHeaders(user), arg);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(result.getData())) {
            return result.getData().stream().map(DataSyncModel.GetTenantResult::getTenantId).distinct().collect(Collectors.toCollection(com.google.common.collect.Lists::newArrayList));
        }
        return Lists.newArrayList();
    }

    public void createFieldMappingByURI(User user, String uri, String ployId) {
        if (StringUtils.isBlank(uri) || StringUtils.isBlank(ployId)) {
            return;
        }
        // String uri = "masterdataapp/" + jsonKey + "_" + "strategy_detail.json";
        String jsonStr = ResourcesIOUtils.getJsonStrByURI(uri);
        MasterDataAppModel.UpdateFieldMappings mapping = JSONObject.parseObject(jsonStr, MasterDataAppModel.UpdateFieldMappings.class);
        if (mapping == null) {
            return;
        }
        Map<String, String> headers = RestUtils.getDDSHeaders(user);
        mapping.setId(ployId);
        linkDataSyncProxy.updateFieldMappings(headers, mapping);
    }

    public String createStrategyDetail(User user, MasterDataStrategyModel.StrategyDetailArg strategyDetailArg) {
        String strategyId = strategyDetailArg.getStrategyId();
        String sourceApiName = strategyDetailArg.getSourceObjectApiName();
        List<String> sourceTenants = strategyDetailArg.getSourceTenants();
        List<String> targetTenants = strategyDetailArg.getTargetTenants();
        String targetObjectApiName = strategyDetailArg.getTargetObjectApiName();
        DataSyncModel.StrategyDetailArg arg = new DataSyncModel.StrategyDetailArg();
        arg.setPloyId(strategyId);
        arg.setSourceObjectApiName(sourceApiName);
        arg.setSourceTenantIds(sourceTenants);
        arg.setSourceTenantType(TENANT_TYPE);
        arg.setDestTenantIds(targetTenants);
        arg.setDestTenantType(TENANT_TYPE);
        arg.setDestObjectApiName(targetObjectApiName);
        arg.setDetailObjectMappings(Lists.newArrayList());
        DataSyncModel.Result strategyDetail = linkDataSyncProxy.createStrategyDetail(RestUtils.getDDSHeaders(user), arg);
        if (strategyDetail == null || strategyDetail.getData() == null) {
            throw new ValidateException(I18N.text(SFA_EXISTS_OBJECT_STRATEGY_DETAIL, targetObjectApiName));
        }
        return strategyDetail.getData().toString();

    }

    public List<IObjectDescribe> getObjectLoginDescribeList(User user, List<String> apiNames) {
        return describeLogicService.findObjectList(user.getTenantId(), apiNames);
    }

    public List<DataSyncModel.TenantInfo> getAllDownstreamTenantInfo(User user) {
        DataSyncModel.GetTenantArg arg = new DataSyncModel.GetTenantArg();
        arg.setUpstreamTenantId(user.getTenantId());
        DataSyncModel.GetTenantsResult result = linkDataSyncProxy.listDownstreamAllTenants(RestUtils.getDDSHeaders(user), arg);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(result.getData())) {
            return com.google.common.collect.Lists.newArrayList();
        }
        return result.getData();
    }

    public boolean saveOrgMappings(User user, List<MasterDataAppModel.OrgMapping> mappings) {
        DataSyncModel.OrgVariableMappingArg arg = new DataSyncModel.OrgVariableMappingArg();
        arg.setTenantId(user.getTenantId());
        arg.setVariableKey(DATA_BELONG_ORG);
        Map<String, String> map = Maps.newHashMap();
        for (MasterDataAppModel.OrgMapping mapping : mappings) {
            map.put(mapping.getOrganization(), mapping.getDownStreamTenant());
        }
        arg.setMappings(map);
        DataSyncModel.Result result = linkDataSyncProxy.saveVariableMappings(RestUtils.getDDSHeaders(user), arg);
        if (result == null || result.getData() == null) {
            throw new ValidateException(I18N.text(SFA_ORG_RELATION_SET_FAILED));
        }
        return Boolean.TRUE.equals(result.getData());
    }

    public Map<String, String> getOrgMappings(User user) {
        DataSyncModel.OrgVariableMappingListArg arg = new DataSyncModel.OrgVariableMappingListArg();
        arg.setTenantId(user.getTenantId());
        arg.setVariableKey(DATA_BELONG_ORG);
        DataSyncModel.Result result = linkDataSyncProxy.listByVariableKey(RestUtils.getDDSHeaders(user), arg);
        if (result == null || result.getData() == null) {
            return Maps.newHashMap();
        }
        return (Map<String, String>) result.getData();
    }

    public List<DataSyncModel.Model2Rst> getOrgMappingsV2(User user) {
        DataSyncModel.OrgVariableMappingListArg arg = new DataSyncModel.OrgVariableMappingListArg();
        arg.setTenantId(user.getTenantId());
        arg.setVariableKey(DATA_BELONG_ORG);
        DataSyncModel.OrgMappingModel2Info result = linkDataSyncProxy.listByVariableKeyV2(RestUtils.getDDSHeaders(user), arg);
        if (result == null || org.apache.commons.collections4.CollectionUtils.isEmpty(result.getData())) {
            return com.google.common.collect.Lists.newArrayList();
        }
        return result.getData();
    }

    public DataSyncModel.DetailInfo getStrategyDetailById(User user, String id, String objectApiName, StrategyTypeEnum strategyTypeEnum, StrategyStatusEnum statusEnum) {
        DataSyncModel.DetailResult detailResult = null;
        try {
            if (StrategyTypeEnum.COLLECT.equals(strategyTypeEnum)) {
                DataSyncModel.GetCollectDetailArg arg = new DataSyncModel.GetCollectDetailArg();
                arg.setStatus(statusEnum.getCode());
                arg.setDestObjectApiName(objectApiName);
                detailResult = linkDataSyncProxy.listByDestTenantId(RestUtils.getDDSHeaders(user), arg);
            } else {
                DataSyncModel.GetAllocateDetailArg arg = new DataSyncModel.GetAllocateDetailArg();
                arg.setStatus(statusEnum.getCode());
                arg.setSourceObjectApiName(objectApiName);
                detailResult = linkDataSyncProxy.listBySourceTenantId(RestUtils.getDDSHeaders(user), arg);
            }
        } catch (Exception e) {
            log.error("调用数据同步接口出现错误， tenant:{}", user.getTenantId(), e);
        }
        return getDetailInfo(id, detailResult);
    }

    private DataSyncModel.DetailInfo getDetailInfo(String id, DataSyncModel.DetailResult detailResult) {
        if (detailResult == null || CollectionUtils.isEmpty(detailResult.getData())) {
            return null;
        }
        return detailResult.getData().stream().filter(x -> id.equals(x.getId())).findAny().orElse(null);
    }

    public void updateFieldMapping(User user, DataSyncModel.DetailInfo originalDetailInfo, List<MasterDataAppModel.FieldMappings> mappingList) {
        MasterDataAppModel.UpdateFieldMappings arg = new MasterDataAppModel.UpdateFieldMappings();
        arg.setId(originalDetailInfo.getId());
        MasterDataAppModel.MasterObjectMapping masterObjectMapping = new MasterDataAppModel.MasterObjectMapping();
        masterObjectMapping.setFieldMappings(mappingList);
        masterObjectMapping.setSourceObjectApiName(originalDetailInfo.getSourceObjectApiName());
        masterObjectMapping.setDestObjectApiName(originalDetailInfo.getDestObjectApiName());
        arg.setDetailObjectMappings(originalDetailInfo.getDetailObjectMappings());
        arg.setMasterObjectMapping(masterObjectMapping);
        linkDataSyncProxy.updateFieldMappings(RestUtils.getDDSHeaders(user), arg);
    }

    public void updateSyncRules(User user, String polyId, DataSyncModel.SyncRulesData syncRule) {
        if (StringUtils.isBlank(polyId)) {
            return;
        }
        DataSyncModel.UpdateSyncRulesArg arg = new DataSyncModel.UpdateSyncRulesArg();
        arg.setId(polyId);
        arg.setSyncRules(syncRule);
        linkDataSyncProxy.updateSyncRules(RestUtils.getDDSHeaders(user), arg);
    }

    public void updateSyncConditions(User user, String objectApiName, String polyId, StrategyTypeEnum type, DataSyncModel.SyncConditionsData syncConditionsData, List<DataSyncModel.SyncConditionsData> detailObjectSyncConditions) {
        if (StringUtils.isBlank(polyId)) {
            return;
        }
        syncConditionsData.setApiName(objectApiName);
        DataSyncModel.UpdateSyncConditionsArg arg = new DataSyncModel.UpdateSyncConditionsArg();
        arg.setId(polyId);
        arg.setType(type.getStrCode());
        arg.setSyncConditions(syncConditionsData);
        arg.setDetailObjectSyncConditions(detailObjectSyncConditions);
        linkDataSyncProxy.updateSyncConditions(RestUtils.getDDSHeaders(user), arg);
    }

    public List<MasterDataAppModel.AppConfig> getAppConfigs(User user) {
        List<MasterDataAppModel.AppConfig> appConfigList = Lists.newArrayList();
        List<IObjectData> masterDataAppDataList = getAppObjectDataList(user);
        for (IObjectData data : masterDataAppDataList) {
            MasterDataAppModel.AppConfig appConfig = new MasterDataAppModel.AppConfig();
            appConfig.setDataId(data.getId());
            appConfig.setDisplayName(data.getDisplayName());
            appConfig.setRemark(ObjectDataUtils.getValueOrDefault(data, REMARK, ""));
            appConfig.setApiName(ObjectDataUtils.getValueOrDefault(data, MASTER_OBJECT_API_NAME, ""));
            appConfig.setCollectId(ObjectDataUtils.getValueOrDefault(data, COLLECT_ID, ""));
            appConfig.setAllocateId(ObjectDataUtils.getValueOrDefault(data, ALLOCATE_ID, ""));
            appConfig.setDownMainCollectId(ObjectDataUtils.getValueOrDefault(data, DOWN_MAIN_COLLECT_ID, ""));
            appConfig.setDownMainAllocateId(ObjectDataUtils.getValueOrDefault(data, DOWN_MAIN_ALLOCATE_ID, ""));
            appConfigList.add(appConfig);
        }
        return appConfigList;

    }

    public void deleteStrategyDetailById(User user, String detailId) {
        DataSyncModel.DelStrategyDetailArg arg = new DataSyncModel.DelStrategyDetailArg();
        arg.setId(detailId);
        linkDataSyncProxy.delete(RestUtils.getDDSHeaders(user), arg);
    }

    public void deleteAllMainDataObjectData(User user) {
        List<IObjectData> appObjectDataList = getAppObjectDataList(user);
        metaDataService.bulkDeleteDirect(appObjectDataList, user);
    }

    public List<IObjectData> createFieldFillRuleData(User user, List<MasterDataAppModel.FillField> fillFields, boolean excludedField, String masterFieldConfigId) {
        List<IObjectData> createDataList = Lists.newArrayList();
        for (MasterDataAppModel.FillField field : fillFields) {
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put(MASTER_OBJECT_API_NAME, field.getMasterObjectApiName());
            dataMap.put(FIELD_API_NAME, field.getFieldApiName());
            dataMap.put(FILL_TYPE, field.getFillType());
            dataMap.put(FILL_SYMBOL, field.getFillSymbol());
            dataMap.put(EXCLUDED, excludedField);
            dataMap.put(FIELD_FILL_CONFIG_ID, masterFieldConfigId);
            IObjectData data = ObjectDataDocument.of(dataMap).toObjectData();
            data.setTenantId(user.getTenantId());
            data.setDescribeApiName(FIELD_FILL_RULE_OBJ);
            createDataList.add(data);
        }
        // 创建
        return metaDataFindServiceExt.bulkSaveObjectData(createDataList, user);
    }

    public IObjectData createMasterFieldFillConfig(User user, MasterDataAppModel.FieldFillConfig fieldFillConfig, String masterObjectApiName) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put(TEXT_FILL_TYPE, fieldFillConfig.getTextFillType());
        dataMap.put(ATTACHMENT_FILL_TYPE, fieldFillConfig.getAttachmentFillType());
        dataMap.put(MANY_SELECT_FILL_TYPE, fieldFillConfig.getManySelectFillType());
        dataMap.put(SINGLE_SELECT_FILL_TYPE, fieldFillConfig.getSingleSelectFillType());
        dataMap.put(OTHER_FILL_TYPE, fieldFillConfig.getOtherFillType());
        dataMap.put(MANY_SYMBOL, fieldFillConfig.getManySymbol());
        dataMap.put(ATTACHMENT_SYMBOL, fieldFillConfig.getAttachmentSymbol());
        dataMap.put(TEXT_SYMBOL, fieldFillConfig.getTextSymbol());
        dataMap.put(MASTER_OBJECT_API_NAME, masterObjectApiName);
        IObjectData data = ObjectDataDocument.of(dataMap).toObjectData();
        data.setTenantId(user.getTenantId());
        data.setDescribeApiName(FIELD_FILL_CONFIG_OBJ);
        return metaDataService.saveObjectData(user, data);
    }

    public void deleteFieldFillRuleData(User user, boolean excludedField, String masterObjectApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        if (!excludedField) {
            SearchUtil.fillFilterEq(query.getFilters(), EXCLUDED, false);
            SearchUtil.fillFilterIsNull(query.getFilters(), FIELD_FILL_CONFIG_ID);
            SearchUtil.fillFilterEq(query.getFilters(), MASTER_OBJECT_API_NAME, masterObjectApiName);
        } else {
            SearchUtil.fillFilterEq(query.getFilters(), EXCLUDED, true);
            SearchUtil.fillFilterIsNotNull(query.getFilters(), FIELD_FILL_CONFIG_ID);
            SearchUtil.fillFilterEq(query.getFilters(), MASTER_OBJECT_API_NAME, masterObjectApiName);
        }
        query.setSearchSource("db");
        // 1000 个字段
        query.setLimit(1000);
        List<IObjectData> dataList = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, FIELD_FILL_RULE_OBJ, query).getData();
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        metaDataService.bulkDeleteDirect(dataList, user);
    }

    public void deleteMasterFieldFillConfigData(User user, String masterObjectApiName, String fieldFillConfigId) {
        if (StringUtils.isBlank(fieldFillConfigId)) {
            return;
        }
        IObjectData objectByIdIgnoreAll = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, fieldFillConfigId, FIELD_FILL_CONFIG_OBJ);
        if (objectByIdIgnoreAll == null) {
            return;
        }
        metaDataService.bulkDeleteDirect(Lists.newArrayList(objectByIdIgnoreAll), user);
    }

    public List<IObjectData> getAllFieldRuleDataList(User user) {
        return metaDataFindServiceExt.findAllObjectIgnoreAll(user, MASTER_DATA_RULE_OBJ);
    }

    public List<IObjectData> bulkSaveObjectData(User user, List<IObjectData> dataList) {
        return metaDataService.bulkSaveObjectData(dataList, user);
    }

    public List<IObjectData> getAllOrgMappingList(User user, String apiName, int limit) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(limit);
        List<OrderBy> orderByList = Lists.newArrayList();
        orderByList.add(new OrderBy("last_modified_time", true));
        query.setOrders(orderByList);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, DBRecord.IS_DELETED, "0");
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, apiName, query).getData();
    }

    public void bulkDelete(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        metaDataService.bulkDeleteDirect(objectDataList, user);
    }

    public List<IObjectData> getOrgMappingByQuery(User user, String apiName, SearchTemplateQuery query) {
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, apiName, query).getData();
    }

    public Set<String> getDownMappingTenantsV2(User user) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(0);
        List<IObjectData> dataList = metaDataFindServiceExt.findBySearchQueryWithFieldsIgnoreAll(user, MASTER_DATA_ORG_MAPPING_OBJ, query, Lists.newArrayList(DOWNSTREAM_TENANT_ID));
        return dataList.stream()
                .filter(d -> d.get(DOWNSTREAM_TENANT_ID) != null && StringUtils.isNotBlank(d.get(DOWNSTREAM_TENANT_ID).toString()))
                .map(x -> (x.get(DOWNSTREAM_TENANT_ID).toString()))
                .collect(Collectors.toSet());
    }

    public boolean deleteObjectItemById(User user, String dataId) {
        IObjectData deleteData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, dataId, MASTER_DATA_APP_OBJ);
        if (deleteData == null) {
            return false;
        }
        List<IObjectData> deletedList = metaDataService.bulkDeleteDirect(Lists.newArrayList(deleteData), user);
        return CollectionUtils.isNotEmpty(deletedList);
    }

    public IObjectData findObjectByMasterDataObjectApiName(User user, String apiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        SearchUtil.fillFilterEq(query.getFilters(), MASTER_OBJECT_API_NAME, apiName);
        List<IObjectData> dataList = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, MASTER_DATA_APP_OBJ, query).getData();
        if (CollectionUtils.isEmpty(dataList)){
            return null;
        }
        return dataList.get(0);
    }

    public void deleteLockFields(User user, String objectApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterIn(query.getFilters(), RULE_OBJECT_API_NAME, objectApiName);
        List<IObjectData> deleteDataList = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, MASTER_DATA_RULE_OBJ, query).getData();
        if (CollectionUtils.isEmpty(deleteDataList)) {
            return;
        }
        metaDataService.bulkDeleteDirect(deleteDataList, user);
    }

    public List<IObjectData> getNotInitializedAppObject(User user) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        // null 和 true 都代表已被初始化，null是为了兼容老企业
        SearchUtil.fillFilterEq(query.getFilters(), INITIALIZED, Boolean.FALSE);
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, MASTER_DATA_APP_OBJ, query).getData();
    }
}
