package com.facishare.crm.md.service;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.md.enums.*;
import com.facishare.crm.md.model.MasterDataAppModel;
import com.facishare.crm.md.model.MasterDataFieldModel;
import com.facishare.crm.md.model.MasterDataServiceModel;
import com.facishare.crm.md.rest.model.DataSyncModel;
import com.facishare.crm.md.utils.MasterDataConfigUtil;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.DescribeServiceExt;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.service.IModuleServiceProxy;
import com.facishare.crm.sfa.task.AsyncTaskProducer;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.*;
import com.facishare.organization.api.model.department.result.*;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.model.type.DepartmentStatus;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ConvertUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchResult;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IImmutableResource;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.api.service.ImmutableResourceService;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.ImmutableResource;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseArg;
import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseResult;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.SimpleEnterprise;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.facishare.crm.md.constant.MasterDataAppConstant.*;
import static com.facishare.crm.md.constant.MasterDataAppI18NKeyConstant.*;
import static com.facishare.crm.md.constant.MasterDataOrgMappingConstant.*;
import static com.facishare.organization.adapter.api.util.Constant.COMPANY_DEPARTMENT_ID;
import static com.facishare.organization.api.model.department.DepartmentDto.RECORD_TYPE_ORGANIZATION;

/**
 * 主数据应用逻辑的具体实现类
 *
 * <AUTHOR>
 * @date 2022/5/20 17:33
 */
@Service
@Slf4j
public class MasterDataService implements IModuleServiceProxy {
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private DescribeServiceExt describeServiceExt;
    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Autowired
    private MasterDataConfigService masterDataConfigService;
    @Autowired
    private MasterDataStrategyService masterDataStrategyService;
    @Autowired
    private DepartmentProviderService departmentProviderService;
    @Autowired
    private ImmutableResourceService immutableFieldService;
    @Autowired
    private SpecialTableMapper tableMapper;
    @Autowired
    private MasterDataRedisCacheService masterDataRedisCacheService;
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private EIEAConverter converter;
    @Autowired
    private MasterDataDao masterDataDao;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private AsyncTaskProducer asyncTaskProducer;

    private static final String MASTER_DATA_APP_SYNC_MAPPING = "master_data_app_sync_mapping";

    public static final String NAME = "name";
    /**
     * 业务类型
     */
    public static final String RECORD_TYPE = "record_type";
    /**
     * 字段尾部标记
     */
    public static final String OLD = "-旧的";//ignoreI18n

    public List<MasterDataAppModel.FieldRule> getFieldRuleList(User user, String objectApiName) throws MetadataServiceException {
        List<IObjectData> masterDataRuleList = masterDataDao.getFieldRuleObjectDataList(user, objectApiName);
        return getFieldRuleList(user, objectApiName, masterDataRuleList);
    }

    public List<MasterDataAppModel.FieldRule> getFieldRuleList(User user, String objectApiName, List<IObjectData> masterDataRuleList) throws MetadataServiceException {
        return getFieldRules(user, objectApiName, masterDataRuleList);
    }

    private List<MasterDataAppModel.FieldRule> getFieldRules(User user, String objectApiName, List<IObjectData> masterDataRuleList) throws MetadataServiceException {
        IObjectDescribe describe = masterDataDao.findDescribe(user.getTenantId(), objectApiName);
        ModelEnum model = masterDataConfigService.getModel(user);
        List<IFieldDescribe> accountCustomFieldList = Lists.newArrayList();
        // 因为这些自定义字段在客户主数据上找不到
        if (ModelEnum.MODEL1.equals(model) && SFAPreDefineObject.AccountMainData.getApiName().equals(objectApiName)) {
            IObjectDescribe accountDescribe = masterDataDao.findDescribe(user.getTenantId(), SFAPreDefine.Account.getApiName());
            accountCustomFieldList = accountDescribe.getFieldDescribes().stream().filter(f -> FieldDescribeExt.of(f).isCustomField()).collect(Collectors.toList());
        }
        Map<String, IObjectData> fieldConfigMap = masterDataRuleList.stream().collect(Collectors.toMap(x -> x.get(FIELD_API_NAME).toString(), x -> x));
        List<String> shareFieldApiName = Lists.newArrayList(fieldConfigMap.keySet());
        // 描述中存在
        List<IFieldDescribe> fieldResult = describe.getFieldDescribes().stream().filter(f -> shareFieldApiName.contains(f.getApiName())).collect(Collectors.toList());
        // 去除重复的字段描述
        List<String> fieldResultFieldApiName = fieldResult.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
        accountCustomFieldList = accountCustomFieldList.stream().filter(f -> !fieldResultFieldApiName.contains(f.getApiName())).collect(Collectors.toList());
        fieldResult.addAll(accountCustomFieldList);
        List<MasterDataAppModel.FieldRule> fieldRuleList = Lists.newArrayList();
        // 根据描述过滤字段，拿到多语Label
        for (IFieldDescribe fieldDescribe : fieldResult) {
            IObjectData ruleData = fieldConfigMap.get(fieldDescribe.getApiName());
            if (ruleData == null) {
                continue;
            }
            MasterDataAppModel.FieldRule fieldRule = MasterDataAppModel.FieldRule.builder().ruleId(fieldConfigMap.get(fieldDescribe.getApiName()).getId()).objectApiName(describe.getApiName()).objectDisplayName(describe.getDisplayName()).fieldApiName(fieldDescribe.getApiName()).fieldDisplayName(fieldDescribe.getLabel()).type(ruleData.get(RULE_TYPE).toString()).build();
            fieldRuleList.add(fieldRule);
        }
        return fieldRuleList;
    }

    public void downStreamFieldHandler(String downStreamTenant) throws MetadataServiceException {
        User user = new User(downStreamTenant, User.SUPPER_ADMIN_USER_ID);
        IObjectDescribe accountMainDataDescribe = findDescribe(user, SFAPreDefineObject.AccountMainData.getApiName());
        if (accountMainDataDescribe == null) {
            IFieldDescribe connectCoding = MasterDataFieldModel.ConnectCoding.getConnectCoding();
            describeServiceExt.addCustomField(downStreamTenant, SFAPreDefineObject.Account.getApiName(), Lists.newArrayList(connectCoding));
        } else {
            IObjectDescribe accountDescribe = findDescribe(user, SFAPreDefineObject.Account.getApiName());
            IFieldDescribe connectCoding = accountDescribe.getFieldDescribe(CONNECT_CODING);
            IFieldDescribe connectCodingField = MasterDataFieldModel.ConnectCoding.getConnectCoding();
            /**
             * 防止重名
             */
            if (connectCoding != null && connectCodingField.getLabel().equals(connectCoding.getLabel())) {
                connectCoding.setLabel(connectCoding.getLabel() + OLD);
                objectDescribeService.updateFieldDescribe(accountDescribe, Lists.newArrayList(connectCoding));
            }
            describeServiceExt.addCustomField(accountMainDataDescribe, Lists.newArrayList(connectCodingField));
            IFieldDescribe groupMasterDataCode = MasterDataFieldModel.GroupMasterDataCode.getGroupMasterDataCode();
            describeServiceExt.addCustomField(downStreamTenant, SFAPreDefineObject.Account.getApiName(), Lists.newArrayList(groupMasterDataCode));
        }
        IFieldDescribe connectCoding = MasterDataFieldModel.ConnectCoding.getConnectCoding();
        IObjectDescribe productDescribe = findDescribe(user, SFAPreDefineObject.Product.getApiName());
        if (productDescribe == null) {
            return;
        }
        describeServiceExt.addCustomField(productDescribe, Lists.newArrayList(connectCoding));
    }

    public void bulkUpdateConfigField(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        metaDataService.batchUpdateByFields(user, objectDataList, Lists.newArrayList(RULE_TYPE));
    }

    public boolean openAccountMainData(String tenantId) {
        boolean openAccountMainData;
        try {
            openAccountMainData = masterDataDao.findDescribe(tenantId, SFAPreDefineObject.AccountMainData.getApiName()) != null;
        } catch (MetadataServiceException e) {
            throw new ValidateException(I18N.text(SFA_MASTER_NET_ERROR_CAN_NOT_FIND_DESCRIBE));
        }
        return openAccountMainData;
    }

    public List<IObjectData> addMainConfigFields(User user, List<MasterDataAppModel.FieldConfig> fieldConfigList, String objectApiName) throws MetadataServiceException {
        List<String> fields = fieldConfigList.stream().map(MasterDataAppModel.FieldConfig::getFieldApiName).collect(Collectors.toList());
        List<IObjectData> masterDataRuleList = masterDataDao.getMasterDataRuleField(user, fields, objectApiName);
        List<String> existsFieldList = masterDataRuleList.stream().map(r -> r.get(FIELD_API_NAME).toString()).collect(Collectors.toList());
        List<IObjectData> saveDataList = buildMainConfigFieldData(user, fieldConfigList, objectApiName, existsFieldList);
        if (CollectionUtils.isEmpty(saveDataList)) {
            return Lists.newArrayList();
        }
        List<MasterDataAppModel.SimpleFieldRule> updateConfigFieldList = buildFieldMapping(saveDataList);
        masterDataStrategyService.updateFieldMapping(user, objectApiName, updateConfigFieldList);
        return masterDataDao.bulkCreateMainConfigField(user, saveDataList);
    }

    private List<MasterDataAppModel.SimpleFieldRule> buildFieldMapping(List<IObjectData> saveDataList) {
        List<MasterDataAppModel.SimpleFieldRule> updateConfigFieldList = Lists.newArrayList();
        for (IObjectData data : saveDataList) {
            MasterDataAppModel.SimpleFieldRule simpleFieldRule = new MasterDataAppModel.SimpleFieldRule();
            simpleFieldRule.setRuleId(data.getId());
            simpleFieldRule.setFieldApiName(data.get(FIELD_API_NAME).toString());
            simpleFieldRule.setType(data.get(RULE_TYPE).toString());
            updateConfigFieldList.add(simpleFieldRule);
        }
        return updateConfigFieldList;
    }

    private List<IObjectData> buildMainConfigFieldData(User user, List<MasterDataAppModel.FieldConfig> fieldConfigList, String objectApiName, List<String> existsFieldList) {
        List<IObjectData> rst = Lists.newArrayList();
        for (MasterDataAppModel.FieldConfig fieldConfig : fieldConfigList) {
            if (existsFieldList.contains(fieldConfig.getFieldApiName())) {
                // 排除存在的字段
                continue;
            }
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put(RULE_TYPE, fieldConfig.getType());
            dataMap.put(FIELD_API_NAME, fieldConfig.getFieldApiName());
            dataMap.put(RULE_OBJECT_API_NAME, objectApiName);
            IObjectData data = ObjectDataDocument.of(dataMap).toObjectData();
            data.setTenantId(user.getTenantId());
            data.setDescribeApiName(MASTER_DATA_RULE_OBJ);
            rst.add(data);
        }
        return rst;
    }

    public void lockFields(User user, String objectApiName, List<String> fieldApiNameList, LockFieldEnum lockEnum) {
        List<String> appTenants = masterDataConfigService.getConfigDownStreamTenants(user.getTenantId());
        if (CollectionUtils.isEmpty(appTenants)) {
            throw new ValidateException(I18N.text(SFA_NONE_SET_DOWNSTREAM_TENANT));
        }
        if (!MasterDataConfigUtil.is850VersionBeforeTenant(user.getTenantId())) {
            lockFields(objectApiName, fieldApiNameList, lockEnum, appTenants);
            return;
        }
        // 锁下发
        MasterDataAppModel.AppConfig appConfigByObject = masterDataDao.getAppConfigByObject(user, objectApiName);
        if (appConfigByObject == null) {
            throw new ValidateException(I18N.text(SFA_MASTER_DATA_APP_CONFIG_NOT_EXISTS_OBJECT));
        }


        // 模式2 下， 下游均开启客户主数据
        ModelEnum model = masterDataConfigService.getModel(user);
        if (ModelEnum.MODEL2.equals(model) && SFAPreDefineObject.AccountMainData.getApiName().equals(objectApiName)) {
            // 模式2 只锁客户主数据即可, appTenants 全是开启主数据的企业
            // 采集下发：客户主数据到客户主数据，客户到客户
            String downMainAllocateId = appConfigByObject.getDownMainAllocateId();
            masterDataStrategyService.syncObjectFields(user, appTenants, downMainAllocateId, objectApiName, fieldApiNameList, lockEnum);
            return;
        }
        // 模式1 或者不是客户主数据
        String allocateId = appConfigByObject.getAllocateId();
        if (SFAPreDefineObject.AccountMainData.getApiName().equals(objectApiName)) {
            // 开客户主数据的下游锁客户主数据，否则锁客户
            List<String> mainDataTenants = Lists.newArrayList();
            List<String> notMainDataTenants = Lists.newArrayList();
            for (String appTenant : appTenants) {
                if (openAccountMainData(appTenant)) {
                    mainDataTenants.add(appTenant);
                } else {
                    notMainDataTenants.add(appTenant);
                }
            }
            masterDataStrategyService.syncObjectFields(user, notMainDataTenants, allocateId, SFAPreDefineObject.Account.getApiName(), fieldApiNameList, lockEnum);
            String downMainAllocateId = appConfigByObject.getDownMainAllocateId();
            // 下游开了多组织的，只锁客户主数据
            masterDataStrategyService.syncObjectFields(user, mainDataTenants, downMainAllocateId, SFAPreDefineObject.AccountMainData.getApiName(), fieldApiNameList, lockEnum);
        } else {
            masterDataStrategyService.syncObjectFields(user, appTenants, allocateId, objectApiName, fieldApiNameList, lockEnum);
        }
    }

    private void lockFields(String objectApiName, List<String> fieldApiNameList, LockFieldEnum lockEnum, List<String> appTenants) {
        if (!SFAPreDefineObject.AccountMainData.getApiName().equals(objectApiName)) {
            return;
        }
        for (String tenant : appTenants) {
            User downStreamUser = new User(tenant, User.SUPPER_ADMIN_USER_ID);
            String control = "1";
            if (LockFieldEnum.LOCK.equals(lockEnum)) {
                control = "0";
            }
            upsertLockFiled(downStreamUser, SFAPreDefineObject.Account.getApiName(), fieldApiNameList, control);
        }
    }

    public boolean deleteConfigFields(User user, String ruleId, String objectApiName) throws MetadataServiceException {
        List<IObjectData> ruleDataList = masterDataDao.findObjectDataByIds(user.getTenantId(), Lists.newArrayList(ruleId), MASTER_DATA_RULE_OBJ);
        if (CollectionUtils.isEmpty(ruleDataList)) {
            return false;
        }
        List<String> fieldApiNameList = ruleDataList.stream().map(x -> x.get(FIELD_API_NAME).toString()).collect(Collectors.toList());
        List<MasterDataAppModel.SimpleFieldRule> simpleFieldRuleList = buildSimpleFieldRules(ruleDataList);
        masterDataStrategyService.updateFieldMapping(user, objectApiName, simpleFieldRuleList);
        lockFields(user, objectApiName, fieldApiNameList, LockFieldEnum.UNLOCK);
        masterDataDao.bulkDeleteDirect(ruleDataList, user);
        return true;
    }

    @NotNull
    private List<MasterDataAppModel.SimpleFieldRule> buildSimpleFieldRules(List<IObjectData> ruleDataList) {
        List<MasterDataAppModel.SimpleFieldRule> simpleFieldRuleList = Lists.newArrayList();
        for (IObjectData data : ruleDataList) {
            MasterDataAppModel.SimpleFieldRule simpleFieldRule = new MasterDataAppModel.SimpleFieldRule();
            simpleFieldRule.setRuleId(data.getId());
            simpleFieldRule.setFieldApiName(data.get(FIELD_API_NAME).toString());
            simpleFieldRule.setType(StrategyTypeEnum.NONE.getStrCode());
            simpleFieldRuleList.add(simpleFieldRule);
        }
        return simpleFieldRuleList;
    }

    /**
     * 获取还可以被添加的对象 apiNames
     *
     * @param user
     * @return
     */
    private List<String> getUsableApiNames(User user) {
        List<String> presetApiNames = masterDataConfigService.getSupportMasterDataAppApiNames();
        Set<String> excludedApiNames = masterDataDao.getAppConfigApiNameList(user);
        // 移除已被添加的对象 apiNames
        presetApiNames.removeAll(excludedApiNames);
        return presetApiNames;
    }

    public List<String> availableObjectList(User user) {
        List<String> usableApiNames = getUsableApiNames(user);
        // 开启客户主数据则以 AccountMainDataObj 为主对象，去掉 AccountObj
        if (openAccountMainData(user.getTenantId())) {
            usableApiNames.remove(SFAPreDefineObject.Account.getApiName());
        } else {
            usableApiNames.remove(SFAPreDefineObject.AccountMainData.getApiName());
        }
        // 主数据应用 v2 版本
        if (!MasterDataConfigUtil.is850VersionBeforeTenant(user.getTenantId())) {
            usableApiNames.remove(SFAPreDefineObject.Account.getApiName());
        }
        return usableApiNames;
    }

    public void addShareField(User user, String objectApiName) throws MetadataServiceException {
        // 上游的必填字段
        List<String> requiredFields = masterDataDao.getObjectRequiredFields(user, objectApiName);
        // 共享字段
        List<String> shareFields = masterDataConfigService.getShareFieldFromConfig();
        // 取出共享字里的必填字段
        for (String shareField : shareFields) {
            if (requiredFields.contains(shareField)) {
                continue;
            }
            requiredFields.add(shareField);
        }
        // 对象支持的字段
        List<MasterDataAppModel.FieldConfig> fieldConfigList = buildFieldConfigList(objectApiName, requiredFields);
        if (CollectionUtils.isEmpty(fieldConfigList)) {
            return;
        }
        addMainConfigFields(user, fieldConfigList, objectApiName);
    }

    @NotNull
    private List<MasterDataAppModel.FieldConfig> buildFieldConfigList(String objectApiName, List<String> requiredFields) {
        List<String> supportFields = masterDataConfigService.getSupportFields(objectApiName);
        List<MasterDataAppModel.FieldConfig> fieldConfigList = Lists.newArrayList();
        for (String field : requiredFields) {
            if (!supportFields.contains(field)) {
                continue;
            }
            MasterDataAppModel.FieldConfig fieldConfig = new MasterDataAppModel.FieldConfig();
            fieldConfig.setFieldApiName(field);
            if (NAME.equals(field) || RECORD_TYPE.equals(field)) {
                fieldConfig.setType(StrategyTypeEnum.COLLECT_AND_ALLOCATE.getStrCode());
            } else {
                fieldConfig.setType(StrategyTypeEnum.ALLOCATE.getStrCode());
            }
            fieldConfigList.add(fieldConfig);
        }
        return fieldConfigList;
    }

    public void addObjectConfig(User user, MasterDataAppModel.StrategyDetailArg strategyDetailArg) {
        // 上游开启客户主数据
        String argCodeGenerateField = strategyDetailArg.getCodeGenerateField();
        boolean openAccountMainData;
        try {
            openAccountMainData = openAccountMainData(user.getTenantId());
        } catch (Exception e) {
            log.error("addObjectConfig#openAccountMainData find describe error,tenant:{}", user.getTenantId(), e);
            throw new ValidateException(I18N.text(SFA_MASTER_NET_ERROR_CAN_NOT_FIND_DESCRIBE));
        }
        if (!openAccountMainData && GROUP_ACCOUNT_NO.equals(argCodeGenerateField)) {
            throw new ValidateException(I18N.text(SFA_MASTER_CAN_NOT_SET_ACCOUNT_MAIN_DATA_GROUP_CODE));
        }
        String originalCodeGenerateField = masterDataConfigService.getCodeGenerateField(user);
        // 不可以从【外部系统赋码生成】变为【在 CRM 系统生成】
        if (MAIN_ACCOUNT_NO.equals(argCodeGenerateField) && GROUP_ACCOUNT_NO.equals(originalCodeGenerateField)) {
            throw new ValidateException(I18N.text(SFA_MASTER_CODE_GENERATE_RULE_CAN_NOT_CHANGE));
        }
        masterDataDao.createObjectConfigData(user, strategyDetailArg);
        masterDataConfigService.specifyCodeGenerateField(user, argCodeGenerateField);
        initSaveFieldRule(user);
    }

    private void initSaveFieldRule(User user) {
        List<MasterDataAppModel.SyncField> configuredSyncFields = masterDataConfigService.getConfiguredSyncFields(user);
        deleteFieldRule(user);
        saveFieldRule(user, configuredSyncFields);
    }

    public void updateFieldRule(User user, String objectApiName, List<MasterDataAppModel.SimpleFieldRule> fieldRuleList) {
        /**
         * ruleId，type
         */
        Map<String, String> ruleMap = fieldRuleList.stream().collect(Collectors.toMap(MasterDataAppModel.SimpleFieldRule::getRuleId, MasterDataAppModel.SimpleFieldRule::getType));
        List<String> ruleIds = fieldRuleList.stream().map(MasterDataAppModel.SimpleFieldRule::getRuleId).collect(Collectors.toList());
        List<IObjectData> ruleObjectDataList = masterDataDao.getFieldRuleObjectDataList(user, objectApiName, ruleIds);
        List<IObjectData> updateDataList = buildUpdateDataList(ruleMap, ruleObjectDataList);
        // 更新策略明细的字段映射
        bulkUpdateConfigField(user, updateDataList);
    }

    private List<IObjectData> buildUpdateDataList(Map<String, String> ruleMap, List<IObjectData> ruleObjectDataList) {
        List<IObjectData> updateDataList = Lists.newArrayList();
        for (IObjectData data : ruleObjectDataList) {
            Object originalRuleType = data.get(RULE_TYPE);
            String newRuleType = ruleMap.get(data.getId());
            if (Objects.equals(originalRuleType, newRuleType)) {
                continue;
            }
            data.set(RULE_TYPE, newRuleType);
            updateDataList.add(data);
        }
        return updateDataList;
    }


    /**
     * 支持多次重置
     *
     */
    public void resetAll(User user) {
        // 重新创建策略
        // 获取到的对象列表
        List<MasterDataAppModel.AppConfig> appConfigList = masterDataDao.getAppConfigs(user);
        if (CollectionUtils.isEmpty(appConfigList)) {
            return;
        }
        // 先把所有策略明细删除掉
        for (MasterDataAppModel.AppConfig appConfig : appConfigList) {
            masterDataStrategyService.clearMasterDataStrategyDetailByConfig(user, appConfig);
        }
        // 清楚所有主数据应用对象数据
        clearAllMasterDataAppObjData(user);
        // 清楚所有字段规则数据
        clearAllFieldRuleData(user);
        clearTempStrategyDetail(user);
    }

    private void clearAllFieldRuleData(User user) {
        List<IObjectData> fieldRuleObjectDataList = masterDataDao.getAllFieldRuleDataList(user);
        if (CollectionUtils.isEmpty(fieldRuleObjectDataList)) {
            return;
        }
        metaDataService.bulkDeleteDirect(fieldRuleObjectDataList, user);
    }

    public void clearAllMasterDataAppObjData(User user) {
        List<IObjectData> appObjectDataList = masterDataDao.getAppObjectDataList(user);
        if (CollectionUtils.isEmpty(appObjectDataList)) {
            return;
        }
        metaDataService.bulkDeleteDirect(appObjectDataList, user);
    }

    private void clearTempStrategyDetail(User user) {
        ModelEnum model = masterDataConfigService.getModel(user);
        if (ModelEnum.MODEL1.equals(model)) {
            return;
        }
        MasterDataAppModel.TempIdRecord masterDataAppTempLog = masterDataConfigService.getMasterDataAppTempLog(user.getTenantId());
        if (masterDataAppTempLog == null) {
            return;
        }
        String allocateId = masterDataAppTempLog.getAllocateId();
        String collectId = masterDataAppTempLog.getCollectId();
        masterDataStrategyService.clearMasterDataStrategyDetailById(user, SFAPreDefineObject.Account.getApiName(), allocateId, StrategyTypeEnum.ALLOCATE);
        masterDataStrategyService.clearMasterDataStrategyDetailById(user, SFAPreDefineObject.Account.getApiName(), collectId, StrategyTypeEnum.COLLECT);
    }

    public void clearAllLockFieldConfig(User user, String objectApiName) throws MetadataServiceException {
        List<String> appTenants = masterDataConfigService.getConfigDownStreamTenants(user.getTenantId());
        if (CollectionUtils.isNotEmpty(appTenants)) {
            IActionContext action = ActionContextExt.of(user, RequestContextManager.getContext()).getContext();
            // 少的时候可以, 多了需要异步优化
            if (SFAPreDefineObject.AccountMainData.getApiName().equals(objectApiName)) {
                immutableFieldService.deleteImmutableFieldResource(SFAPreDefineObject.AccountMainData.getApiName(), Lists.newArrayList(), action);
                immutableFieldService.deleteImmutableFieldResource(SFAPreDefineObject.Account.getApiName(), Lists.newArrayList(), action);
            } else {
                immutableFieldService.deleteImmutableFieldResource(objectApiName, Lists.newArrayList(), action);
            }
        }
    }

    public void updateSyncRules(User user, MasterDataAppModel.StrategyDetailArg strategyDetail) {
        String allocateId = strategyDetail.getStrategyDetail().getAllocateId();
        String collectId = strategyDetail.getStrategyDetail().getCollectId();
        String mainAllocateId = strategyDetail.getOpenMasterStrategyDetail().getAllocateId();
        String mainCollectId = strategyDetail.getOpenMasterStrategyDetail().getCollectId();
        DataSyncModel.SyncRulesData syncRulesData = buildSyncRuleData();
        masterDataStrategyService.updateSyncRules(user, allocateId, syncRulesData);
        masterDataStrategyService.updateSyncRules(user, collectId, syncRulesData);
        masterDataStrategyService.updateSyncRules(user, mainAllocateId, syncRulesData);
        masterDataStrategyService.updateSyncRules(user, mainCollectId, syncRulesData);
    }

    @NotNull
    private DataSyncModel.SyncRulesData buildSyncRuleData() {
        DataSyncModel.InitData initData = new DataSyncModel.InitData();
        initData.setStatus(1);
        DataSyncModel.SyncRulesData syncRulesData = new DataSyncModel.SyncRulesData();
        syncRulesData.setInitData(initData);
        syncRulesData.setEvents(Lists.newArrayList(1, 7));
        syncRulesData.setSyncBetweenTimeForce(false);
        syncRulesData.setUpdateUpstreamDataOuterOwner(false);
        syncRulesData.setSyncDuplicateDataFailThenMakeMapping(false);
        syncRulesData.setSyncDependForce(false);
        return syncRulesData;
    }

    public DataSyncModel.SyncConditionsData buildSyncCondition() {
        DataSyncModel.FilterData filterData = new DataSyncModel.FilterData();
        filterData.setFieldApiName("name");
        filterData.setOperate("ISN");
        filterData.setFieldValue(Lists.newArrayList());
        filterData.setFieldType("text");
        DataSyncModel.SyncConditionsData syncConditionsData = new DataSyncModel.SyncConditionsData();
        List<DataSyncModel.FilterData> filterDataList = Lists.newArrayList();
        filterDataList.add(filterData);
        List<List<DataSyncModel.FilterData>> filters = Lists.newArrayList();
        filters.add(filterDataList);
        syncConditionsData.setFilters(filters);
        return syncConditionsData;
    }

    public void updateSyncConditions(User user, MasterDataAppModel.StrategyDetailArg strategyDetail) throws MetadataServiceException {
        DataSyncModel.SyncConditionsData syncConditionsData = buildSyncCondition();
        updateSyncConditions(user, syncConditionsData, strategyDetail);
        // ModelEnum model = masterDataConfigService.getModel(user);
        // if (ModelEnum.MODEL2.equals(model)) {
        //     updateSyncConditionOfModel2(user, allocateId, collectId, mainAllocateId, mainCollectId, syncConditionsData);
        //     return;
        // }
        // // 模式1
        // // 采集
        // masterDataStrategyService.updateSyncConditions(user, SFAPreDefineObject.Account.getApiName(), collectId, StrategyTypeEnum.COLLECT, syncConditionsData, Lists.newArrayList());
        // masterDataStrategyService.updateSyncConditions(user, SFAPreDefineObject.Account.getApiName(), mainCollectId, StrategyTypeEnum.COLLECT, syncConditionsData, Lists.newArrayList());
        // // 下发
        // if (openAccountMainData(user.getTenantId())) {
        //     DataSyncModel.FilterData filterData = buildAllocateFilterData();
        //     List<DataSyncModel.FilterData> filterDataList = Lists.newArrayList();
        //     filterDataList.add(filterData);
        //     List<List<DataSyncModel.FilterData>> filters = Lists.newArrayList();
        //     filters.add(filterDataList);
        //     syncConditionsData.setFilters(filters);
        // }
        // masterDataStrategyService.updateSyncConditions(user,
        //         SFAPreDefineObject.Account.getApiName(),
        //         allocateId,
        //         StrategyTypeEnum.ALLOCATE,
        //         syncConditionsData,
        //         Lists.newArrayList());
        // masterDataStrategyService.updateSyncConditions(user,
        //         SFAPreDefineObject.AccountMainData.getApiName(),
        //         mainAllocateId, StrategyTypeEnum.ALLOCATE,
        //         syncConditionsData,
        //         Lists.newArrayList());
    }

    /**
     * 默认采集下发都是客户名称不为空
     *
     * @param user
     * @param syncConditionsData 预设的数据范围：客户名称不为空
     * @param strategyDetail     策略详情信息
     */
    protected void updateSyncConditions(User user, DataSyncModel.SyncConditionsData syncConditionsData, MasterDataAppModel.StrategyDetailArg strategyDetail) {
        masterDataStrategyService.updateSyncConditions(user, strategyDetail.getObjectApiName(), strategyDetail.getStrategyDetail().getCollectId(), StrategyTypeEnum.COLLECT, syncConditionsData, Lists.newArrayList());
        masterDataStrategyService.updateSyncConditions(user, strategyDetail.getObjectApiName(), strategyDetail.getOpenMasterStrategyDetail().getCollectId(), StrategyTypeEnum.COLLECT, syncConditionsData, Lists.newArrayList());
        masterDataStrategyService.updateSyncConditions(user, strategyDetail.getObjectApiName(), strategyDetail.getStrategyDetail().getAllocateId(), StrategyTypeEnum.ALLOCATE, syncConditionsData, Lists.newArrayList());
        masterDataStrategyService.updateSyncConditions(user, strategyDetail.getObjectApiName(), strategyDetail.getOpenMasterStrategyDetail().getAllocateId(), StrategyTypeEnum.ALLOCATE, syncConditionsData, Lists.newArrayList());
    }

    public void fillOrgInfo(IObjectData data, String upstreamTenant, String downstreamTenant, StrategyTypeEnum strategyTypeEnum) {
        List<String> parameterDataOwnOrganization = data.getDataOwnOrganization();
        if (CollectionUtils.isEmpty(parameterDataOwnOrganization)) {
            return;
        }
        String paramOrg = parameterDataOwnOrganization.get(0);
        User upUser = new User(upstreamTenant, User.SUPPER_ADMIN_USER_ID);
        List<DataSyncModel.Model2Rst> orgMappingsOrgInfo = getOrgMappingsOrgInfo(upUser);
        if (CollectionUtils.isEmpty(orgMappingsOrgInfo)) {
            log.warn("组织映射关系为空，去除参数中的归属组织");
            data.setDataOwnOrganization(Lists.newArrayList());
            return;
        }
        AtomicReference<String> mappingOrg = new AtomicReference<>();
        orgMappingsOrgInfo.stream().filter(info -> downstreamTenant.equals(info.getDownstreamTenantId())).findFirst().ifPresent(first -> {
            // <上游组织，下游组织>
            Map<String, String> mappings = first.getMappings();
            if (MapUtils.isEmpty(mappings)) {
                return;
            }
            if (StrategyTypeEnum.COLLECT.equals(strategyTypeEnum)) {
                // 采集，下游数据到上游数据，根据下游数据传过来的组织找到上有对应的组织
                // 根据 value 找 key
                try {
                    mappings.forEach((upstreamOrg, downstreamOrg) -> {
                        if (paramOrg.equals(downstreamOrg)) {
                            mappingOrg.set(upstreamOrg);
                            throw new RuntimeException("break loop ex");
                        }
                    });
                } catch (RuntimeException e) {
                    //ignore
                }
            } else {
                // 下发，上游数据到下游数据，根据上游床过来的组织找到下游对应的组织
                mappingOrg.set(mappings.get(paramOrg));
            }
        });
        if (StringUtils.isBlank(mappingOrg.get())) {
            log.warn("在组织映射关系中未找到对应的组织信息，去除参数中的归属组织");
            data.setDataOwnOrganization(Lists.newArrayList());
            return;
        }
        data.setDataOwnOrganization(Lists.newArrayList(mappingOrg.get()));
    }

    public IObjectData updateDownstreamConnectCoding(User upstreamUser, String downstreamDataId, String downstreamTenantId, String upstreamDataId, StrategyTypeEnum strategyTypeEnum) throws MetadataServiceException {
        IObjectData downstreamData = setConnectCoding(upstreamUser, downstreamDataId, downstreamTenantId, upstreamDataId);
        metaDataFindServiceExt.bulkUpdateByFields(downstreamTenantId, Lists.newArrayList(downstreamData), Lists.newArrayList(CONNECT_CODING));
        setOriginSource(downstreamTenantId, downstreamDataId, SFAPreDefine.AccountMainData.getApiName());
        if (StrategyTypeEnum.COLLECT.equals(strategyTypeEnum)) {
            // 返回上游数据
            return metaDataFindServiceExt.findObjectData(upstreamUser, upstreamDataId, SFAPreDefine.AccountMainData.getApiName());
        } else {
            // 返回下游数据
            return downstreamData;
        }
    }

    @NotNull
    private IObjectData setConnectCoding(User upstreamUser, String downstreamDataId, String downstreamTenantId, String upstreamDataId) {
        IObjectData duplicateObjectData = metaDataFindServiceExt.findObjectByIdIgnoreAll(upstreamUser, upstreamDataId, SFAPreDefineObject.AccountMainData.getApiName());
        String connectCoding = ObjectDataUtils.getValueOrDefault(duplicateObjectData, MAIN_ACCOUNT_NO, "");
        // 把互联编码设置为下游客户主数据的connect_coding
        IObjectData downstreamData = metaDataFindServiceExt.findObjectByIdIgnoreAll(downstreamTenantId, downstreamDataId, SFAPreDefine.AccountMainData.getApiName());
        downstreamData.set(CONNECT_CODING, connectCoding);
        return downstreamData;
    }

    private String getUpstreamTenantBySyncInfo(String targetTenantId, MasterDataAppModel.SyncDataSourceInfo syncDataSourceInfo) {
        Integer syncType = syncDataSourceInfo.getSyncType();
        // 采集, 源企业是下游
        if (StrategyTypeEnum.COLLECT.getTypeCode().equals(syncType)) {
            return targetTenantId;
        } else if (StrategyTypeEnum.ALLOCATE.getTypeCode().equals(syncType)) {
            // 下发, 源企业是上游
            return syncDataSourceInfo.getSourceTenantId();
        } else {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_UNSUPPORTED_COLLECTION_METHOD));
        }
    }

    private String getDownstreamTenantBySyncInfo(String targetTenantId, MasterDataAppModel.SyncDataSourceInfo syncDataSourceInfo) {
        Integer syncType = syncDataSourceInfo.getSyncType();
        // 采集, 源企业是下游
        if (StrategyTypeEnum.COLLECT.getTypeCode().equals(syncType)) {
            return syncDataSourceInfo.getSourceTenantId();
        } else if (StrategyTypeEnum.ALLOCATE.getTypeCode().equals(syncType)) {
            // 下发, 源企业是上游
            return targetTenantId;
        } else {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_UNSUPPORTED_COLLECTION_METHOD));
        }
    }

    private BaseObjectSaveAction.Result accountAddHandler(ServiceContext context, ObjectDataDocument accountDataDocumentArg, MasterDataAppModel.SyncDataSourceInfo syncDataSourceInfo) throws MetadataServiceException {
        if (Objects.equals(StrategyTypeEnum.COLLECT.getTypeCode(), syncDataSourceInfo.getSyncType())) {
            return accountCollectAddHandler(context, accountDataDocumentArg, syncDataSourceInfo);
        } else if (Objects.equals(StrategyTypeEnum.ALLOCATE.getTypeCode(), syncDataSourceInfo.getSyncType())) {
            return accountAllocateAddHandler(context, accountDataDocumentArg, syncDataSourceInfo);
        } else {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_TYPE_ERROR_IS_EMPTY));
        }
    }

    private BaseObjectSaveAction.Result accountAllocateAddHandler(ServiceContext context, ObjectDataDocument accountDataDocumentArg, MasterDataAppModel.SyncDataSourceInfo syncDataSourceInfo) throws MetadataServiceException {
        String describeApiName = accountDataDocumentArg.toObjectData().getDescribeApiName();

        if (openAccountMainData(context.getTenantId()) && SFAPreDefineObject.Account.getApiName().equals(describeApiName)) {
            String enterpriseName = getEnterpriseName(context.getTenantId(), context.getTenantId());
            if (StringUtils.isBlank(enterpriseName)) {
                enterpriseName = converter.enterpriseIdToAccount(Integer.parseInt(context.getTenantId()));
            }
            throw new ValidateException(I18N.text(SFA_DOWN_OPEN_ORG, enterpriseName));
        }
        // 上游开启了客户主数据就把客户主数据的字段全都刷下去, 否则不用管(重复的情况下)
        // 没开就刷客户上的
        String upstreamTenantId = syncDataSourceInfo.getSourceTenantId();
        List<String> duplicateDataIds = findDuplicateDataIds(context.getUser(), accountDataDocumentArg.toObjectData());
        if (CollectionUtils.isEmpty(duplicateDataIds)) {
            return addObjectData(context, SFAPreDefineObject.Account.getApiName(), accountDataDocumentArg);
        }
        return allocateDuplicateDataHandler(context, accountDataDocumentArg, upstreamTenantId, duplicateDataIds);
    }

    private BaseObjectSaveAction.Result allocateDuplicateDataHandler(ServiceContext context, ObjectDataDocument dataDocument, String upstreamTenantId, List<String> duplicateDataIds) throws MetadataServiceException {
        String downstreamObjectApiName = dataDocument.toObjectData().getDescribeApiName();
        List<String> forceFieldApiNameList = Lists.newArrayList(CONNECT_CODING);
        String upstreamAccountId = dataDocument.getId();
        User upstreamUser = new User(upstreamTenantId, "-10000");
        if (openAccountMainData(upstreamTenantId)) {
            List<String> fieldApiNames = masterDataConfigService.getAllForceFieldApiNames(upstreamTenantId, SFAPreDefineObject.AccountMainData.getApiName());
            forceFieldApiNameList.addAll(fieldApiNames);
            // 强刷数据从主数据上拿
            IObjectData accountData = metaDataFindServiceExt.findObjectData(upstreamUser, upstreamAccountId, SFAPreDefineObject.Account.getApiName());
            if (upstreamAccountId == null) {
                throw new ValidateException("accountData is empty");
            }
            String accountMainDataId = ObjectDataUtils.getValueOrDefault(accountData, ACCOUNT_MAIN_DATA_ID, "");
            if (StringUtils.isBlank(accountMainDataId)) {
                throw new ValidateException("accountMainDataId is empty");
            }
            MasterDataServiceModel.ForceFieldDataArg forceFieldDataArg = MasterDataServiceModel.ForceFieldDataArg.builder().upstreamMasterCodingName(MAIN_ACCOUNT_NO).upstreamTenantId(upstreamTenantId).upstreamDataId(accountMainDataId).upstreamObjectApiName(SFAPreDefineObject.AccountMainData.getApiName()).downstreamTenantId(context.getTenantId()).downDataIdList(duplicateDataIds).downstreamObjectApiName(downstreamObjectApiName).forceFieldApiNames(forceFieldApiNameList).build();
            forcedBrushData(forceFieldDataArg);
        } else {
            // 强刷数据从客户上拿
            List<String> fieldApiNames = masterDataConfigService.getAllForceFieldApiNames(upstreamTenantId, SFAPreDefineObject.Account.getApiName());
            forceFieldApiNameList.addAll(fieldApiNames);
            MasterDataServiceModel.ForceFieldDataArg forceFieldDataArg = MasterDataServiceModel.ForceFieldDataArg.builder().upstreamMasterCodingName(UPSTREAM_MASTER_CODING).upstreamTenantId(upstreamTenantId).upstreamDataId(upstreamAccountId).upstreamObjectApiName(SFAPreDefineObject.Account.getApiName()).downstreamTenantId(context.getTenantId()).downDataIdList(duplicateDataIds).downstreamObjectApiName(downstreamObjectApiName).forceFieldApiNames(forceFieldApiNameList).build();
            forcedBrushData(forceFieldDataArg);
        }
        throw new ValidateException(I18N.text(SFA_DUPLICATE_DATA_BRUSH_FIELD_DATA));
    }

    /**
     * 数据采集, 上游新建客户
     *
     * @param context                上下文
     * @param accountDataDocumentArg 客户数据参数
     * @param syncDataSourceInfo     上下游信息
     * @return
     * @throws MetadataServiceException
     */
    private BaseObjectSaveAction.Result accountCollectAddHandler(ServiceContext context, ObjectDataDocument accountDataDocumentArg, MasterDataAppModel.SyncDataSourceInfo syncDataSourceInfo) throws MetadataServiceException {
        String downstreamTenantId = syncDataSourceInfo.getSourceTenantId();
        String sourceObjectApiName = syncDataSourceInfo.getSourceObjectApiName();

        if (openAccountMainData(downstreamTenantId) && SFAPreDefineObject.Account.getApiName().equals(sourceObjectApiName)) {
            String enterpriseName = getEnterpriseName(context.getTenantId(), downstreamTenantId);
            if (StringUtils.isBlank(enterpriseName)) {
                enterpriseName = converter.enterpriseIdToAccount(Integer.parseInt(downstreamTenantId));
            }
            throw new ValidateException(I18N.text(SFA_DOWN_OPEN_ORG, enterpriseName));
        }
        // 上游没开客户主数据, 则查重后直接在上游创建客户, 若有重复, 把重复客户的锁定字段强刷给下游
        if (!openAccountMainData(context.getTenantId())) {
            // 没开客户主数据, 先查重
            List<String> duplicateIds = findDuplicateDataIds(context.getUser(), accountDataDocumentArg.toObjectData());
            if (CollectionUtils.isEmpty(duplicateIds)) {
                return addObjectData(context, SFAPreDefineObject.Account.getApiName(), accountDataDocumentArg);
            }
            return collectDuplicateHandler(context, SFAPreDefineObject.Account.getApiName(), accountDataDocumentArg, syncDataSourceInfo, downstreamTenantId, duplicateIds);
        }

        // 开启了客户主数据
        if (StringUtils.isBlank(accountDataDocumentArg.toObjectData().getName())) {
            throw new ValidateException(I18N.text(SFA_NAME_CANNOT_BE_EMPTY));
        }
        IObjectData existAccountMainData = getDuplicateAccountMainDataByName(context.getUser(), accountDataDocumentArg.toObjectData().getName());
        // 存在客户主数据
        if (existAccountMainData != null) {
            List<String> allFields = masterDataConfigService.getAllForceFieldApiNames(context.getTenantId(), SFAPreDefineObject.AccountMainData.getApiName());
            // 把所有客户主数据上的这些字段赋值给客户上
            for (String field : allFields) {
                accountDataDocumentArg.toObjectData().set(field, existAccountMainData.get(field));
            }
            accountDataDocumentArg.toObjectData().set(ACCOUNT_MAIN_DATA_ID, existAccountMainData.getId());
            List<String> duplicateDataIds = findDuplicateDataIds(context.getUser(), accountDataDocumentArg.toObjectData());
            if (CollectionUtils.isEmpty(duplicateDataIds)) {
                return addObjectData(context, SFAPreDefineObject.Account.getApiName(), accountDataDocumentArg);
            }
            IObjectData objectData = metaDataFindServiceExt.findObjectData(context.getUser(), duplicateDataIds.get(0), SFAPreDefineObject.Account.getApiName());
            Object duplicateAccountMainDataId = objectData.get(ACCOUNT_MAIN_DATA_ID);
            if (!existAccountMainData.getId().equals(duplicateAccountMainDataId)) {
                throw new ValidateException(I18N.text(SFA_DUPLICATE_DATA_IN_SAME_ACCOUNT_MAIN_DATA));
            }
            return collectDuplicateHandler(context, SFAPreDefineObject.AccountMainData.getApiName(), accountDataDocumentArg, syncDataSourceInfo, downstreamTenantId, Lists.newArrayList(existAccountMainData.getId()));
        }
        // 客户主数据不存在, 根据客户生成客户主数据
        IObjectData cpData = ObjectDataExt.of(accountDataDocumentArg.toObjectData()).copy();
        List<String> duplicateAccountDataIds = findDuplicateDataIds(context.getUser(), accountDataDocumentArg.toObjectData());
        if (CollectionUtils.isNotEmpty(duplicateAccountDataIds)) {
            throw new ValidateException(I18N.text(SFA_DUPLICATE_DATA_IN_SAME_ACCOUNT_MAIN_DATA));
        }
        BaseObjectSaveAction.Result accountMainData = addAccountMainDataByAccountData(context, accountDataDocumentArg);
        if (accountMainData.getObjectData() == null) {
            throw new ValidateException(I18N.text(SFA_ACCOUNT_MAIN_DATA_NOT_EXISTS));
        }
        ObjectDataDocument newCreateAccountMainData = accountMainData.getObjectData();
        cpData.set(ACCOUNT_MAIN_DATA_ID, newCreateAccountMainData.getId());
        // 根据回填的客户主数据生成客户
        return addObjectData(context, SFAPreDefineObject.Account.getApiName(), ObjectDataDocument.of(cpData));
    }

    public String getEnterpriseName(String tenantId, String downstreamTenantId) {
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        List<DataSyncModel.TenantInfo> allDownstreamTenantInfo = masterDataStrategyService.getAllDownstreamTenantInfo(user);
        Optional<DataSyncModel.TenantInfo> any = allDownstreamTenantInfo.stream().filter(x -> downstreamTenantId.equals(x.getTenantId())).findAny();
        return any.map(DataSyncModel.TenantInfo::getTenantName).orElse(null);
    }

    private BaseObjectSaveAction.Result collectDuplicateHandler(ServiceContext context, String getFieldByApiName, ObjectDataDocument accountDataDocumentArg, MasterDataAppModel.SyncDataSourceInfo syncDataSourceInfo, String downstreamTenantId, List<String> duplicateDataIds) throws MetadataServiceException {
        String addApiName = accountDataDocumentArg.toObjectData().getDescribeApiName();
        if (duplicateDataIds.size() > 1) {
            return addObjectData(context, addApiName, accountDataDocumentArg);
        }
        String masterCodingField = UPSTREAM_MASTER_CODING;
        if (SFAPreDefineObject.AccountMainData.getApiName().equals(getFieldByApiName)) {
            masterCodingField = MAIN_ACCOUNT_NO;
        }
        String duplicateId = duplicateDataIds.get(0);
        // 把上游的数据全部强刷给这个数据
        // getAllForceFields
        List<String> forceFieldApiNameList = Lists.newArrayList(CONNECT_CODING);
        List<String> allForceFieldApiNames = masterDataConfigService.getAllForceFieldApiNames(context.getTenantId(), getFieldByApiName);
        forceFieldApiNameList.addAll(allForceFieldApiNames);
        MasterDataServiceModel.ForceFieldDataArg forceFieldDataArg = MasterDataServiceModel.ForceFieldDataArg.builder().upstreamMasterCodingName(masterCodingField).upstreamTenantId(context.getTenantId()).upstreamDataId(duplicateId).upstreamObjectApiName(getFieldByApiName).downstreamTenantId(downstreamTenantId).downDataIdList(Lists.newArrayList(accountDataDocumentArg.getId())).downstreamObjectApiName(syncDataSourceInfo.getSourceObjectApiName()).forceFieldApiNames(forceFieldApiNameList).build();
        forcedBrushData(forceFieldDataArg);
        return addObjectData(context, addApiName, accountDataDocumentArg);
    }

    public void setOriginSource(String tenantId, String objectId, String apiName) throws MetadataServiceException {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(objectId) || StringUtils.isBlank(apiName)) {
            return;
        }
        IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, apiName);
        if (describe == null) {
            return;
        }
        String storeTableName = describe.getStoreTableName();
        if (StringUtils.isBlank(storeTableName)) {
            return;
        }
        String sqlFormat = "UPDATE %s SET origin_source=0 WHERE id = '%s' AND tenant_id = '%s'";
        String updateOriginSourceSQL = String.format(sqlFormat, SqlEscaper.pg_escape(storeTableName), SqlEscaper.pg_escape(objectId), SqlEscaper.pg_escape(tenantId));
        tableMapper.setTenantId(tenantId).batchUpdateBySql(updateOriginSourceSQL);
    }

    public void forcedBrushData(MasterDataServiceModel.ForceFieldDataArg arg) throws MetadataServiceException {
        if (arg.getDownDataIdList().size() > 100) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MORE_THAN_100_PIECES_OF_DATA_TO_BRUSHED));
        }
        // 把上游书互联编码, 锁定字段. 主数据字段, 客户共享字段 强制更新到下游
        User downstreamUser = new User(arg.getDownstreamTenantId(), "-10000");
        User upstreamUser = new User(arg.getUpstreamTenantId(), "-10000");
        List<IObjectData> downstreamDataList = metaDataFindServiceExt.findObjectByIdsIgnoreAll(downstreamUser, arg.getDownDataIdList(), arg.getDownstreamObjectApiName());
        IObjectData upstreamData = metaDataFindServiceExt.findObjectByIdIgnoreAll(upstreamUser, arg.getUpstreamDataId(), arg.getUpstreamObjectApiName());
        List<IObjectData> updateDataList = Lists.newArrayList();
        for (IObjectData downstreamData : downstreamDataList) {
            String connectCoding = ObjectDataUtils.getValueOrDefault(downstreamData, CONNECT_CODING, "");
            if (StringUtils.isNotBlank(connectCoding)) {
                continue;
            }
            for (String fieldApiName : arg.getForceFieldApiNames()) {
                downstreamData.set(fieldApiName, upstreamData.get(fieldApiName));
            }
            Object upstreamCoding = upstreamData.get(arg.getUpstreamMasterCodingName());
            downstreamData.set(CONNECT_CODING, upstreamCoding);
            updateDataList.add(downstreamData);
        }
        metaDataFindServiceExt.bulkUpdateByFields(downstreamUser, updateDataList, arg.getForceFieldApiNames());
        // 下游数据编码设置0
        for (IObjectData data : updateDataList) {
            setOriginSource(arg.getDownstreamTenantId(), data.getId(), data.getDescribeApiName());
        }
    }

    /**
     * 主数据应用这, 这种场景只有上游开多组织, 下游也开启多组织
     * 来自数据下发
     *
     * @param accountMainDataDocumentArg 对象数据参数
     * @param context                    上下文
     * @param syncDataSourceInfo         上下游信息
     * @return
     */
    private BaseObjectSaveAction.Result accountMainDataAddHandler(ServiceContext context, ObjectDataDocument accountMainDataDocumentArg, MasterDataAppModel.SyncDataSourceInfo syncDataSourceInfo) throws MetadataServiceException {
        if (StringUtils.isBlank(accountMainDataDocumentArg.toObjectData().getName())) {
            throw new ValidateException(I18N.text(SFA_NAME_CANNOT_BE_EMPTY));
        }
        // 先查客户主数据名字是否重复
        IObjectData existsAccountMainData = getDuplicateAccountMainDataByName(context.getUser(), accountMainDataDocumentArg.toObjectData().getName());
        if (existsAccountMainData != null) {
            return allocateDuplicateDataHandler(context, accountMainDataDocumentArg, syncDataSourceInfo.getSourceTenantId(), Lists.newArrayList(existsAccountMainData.getId()));
        }

        // 根据名字不重复, 再查重规则看下
        List<String> duplicateIds = findDuplicateDataIds(context.getUser(), accountMainDataDocumentArg.toObjectData());
        if (CollectionUtils.isEmpty(duplicateIds)) {
            IObjectData data = accountMainDataDocumentArg.toObjectData();
            MasterDataServiceModel.DepartmentOrgInfo departmentOrgInfo = getDepartmentAndOrgInfo(context.getTenantId(), data);
            if (departmentOrgInfo != null) {
                data.setDataOwnOrganization(Lists.newArrayList(departmentOrgInfo.getOrganization()));
                data.setDataOwnDepartment(Lists.newArrayList(departmentOrgInfo.getDepartment()));
            }
            return addObjectData(context, SFAPreDefineObject.AccountMainData.getApiName(), accountMainDataDocumentArg);
        }
        if (duplicateIds.size() > 1) {
            return addObjectData(context, SFAPreDefineObject.AccountMainData.getApiName(), accountMainDataDocumentArg);
        }
        return allocateDuplicateDataHandler(context, accountMainDataDocumentArg, syncDataSourceInfo.getSourceTenantId(), Lists.newArrayList(duplicateIds.get(0)));
    }

    public MasterDataServiceModel.DepartmentOrgInfo getDepartmentAndOrgInfo(String tenantId, IObjectData data) {
        List<String> ownerList = data.getOwner();
        if (CollectionUtils.isEmpty(ownerList)) {
            return null;
        }
        String owner = ownerList.get(0);
        MasterDataServiceModel.DepartmentOrgInfo departmentOrgInfo = new MasterDataServiceModel.DepartmentOrgInfo();
        if (CollectionUtils.isNotEmpty(data.getDataOwnDepartment()) && StringUtils.isNotBlank(data.getDataOwnDepartment().get(0))) {
            String department = data.getDataOwnDepartment().get(0);
            departmentOrgInfo.setDepartment(department);
        } else {
            String mainDepartmentId = getMainDepartmentId(tenantId, owner);
            if (StringUtils.isBlank(mainDepartmentId)) {
                throw new ValidateException(I18N.text(SFA_OWNER_NOT_HAVE_MAIN_DEPT));
            }
            departmentOrgInfo.setDepartment(mainDepartmentId);
        }
        if (CollectionUtils.isNotEmpty(data.getDataOwnOrganization()) && StringUtils.isNotBlank(data.getDataOwnOrganization().get(0))) {
            String org = data.getDataOwnOrganization().get(0);
            departmentOrgInfo.setOrganization(org);
        } else {
            String orgId = getMainOrgId(tenantId, owner);
            departmentOrgInfo.setOrganization(orgId);
        }
        return departmentOrgInfo;
    }

    private String getMainOrgId(String tenantId, String owner) {
        GetLevelOneDepartmentByMainDepartmentArg arg = new GetLevelOneDepartmentByMainDepartmentArg();
        arg.setEnterpriseId(Integer.parseInt(tenantId));
        arg.setEmployeeId(Integer.parseInt(owner));
        GetLevelOneDepartmentByMainDepartmentResult orgInfoResult = departmentProviderService.getLevelOneDepartmentByMainDepartment(arg);
        DepartmentDto orgInfo = orgInfoResult.getDepartmentDto();
        String orgId;
        if (RECORD_TYPE_ORGANIZATION.equals(orgInfo.getRecordType())) {
            orgId = String.valueOf(orgInfo.getDepartmentId());
        } else {
            orgId = String.valueOf(DepartmentDto.COMPANY_DEPARTMENT_ID);
        }
        return orgId;
    }

    private String getMainDepartmentId(String tenantId, String owner) {
        GetEmployeeDtoArg getEmployeeDtoArg = new GetEmployeeDtoArg();
        getEmployeeDtoArg.setEnterpriseId(Integer.parseInt(tenantId));
        getEmployeeDtoArg.setEmployeeId(Integer.parseInt(owner));
        GetEmployeeDtoResult employeeDto = employeeProviderService.getEmployeeDto(getEmployeeDtoArg);
        Integer mainDepartmentId = employeeDto.getEmployeeDto().getMainDepartmentId();
        if (mainDepartmentId == null) {
            return null;
        }
        return String.valueOf(mainDepartmentId);
    }

    public BaseObjectSaveAction.Result addObjectData(ServiceContext context, String objectApiName, ObjectDataDocument dataDocument) {
        if (dataDocument == null) {
            throw new ValidateException("accountDataDocument is blank");
        }
        ObjectDataDocument document = ObjectDataDocument.of(dataDocument.toObjectData());
        ActionContext actionContext = buildAction(context, objectApiName);
        BaseObjectSaveAction.Arg arg = buildAddActionArg(document);
        return serviceFacade.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
    }


    public BaseObjectSaveAction.Result addAccountMainDataByAccountData(ServiceContext context, ObjectDataDocument accountDataDocument) throws MetadataServiceException {
        if (accountDataDocument == null) {
            throw new ValidateException("accountDataDocument is blank");
        }
        accountDataDocument.toObjectData().setId(null);
        accountDataDocument.toObjectData().setDescribeApiName(SFAPreDefine.AccountMainData.getApiName());
        List<String> duplicateDataIds = findDuplicateDataIds(context.getUser(), accountDataDocument.toObjectData());
        if (CollectionUtils.isEmpty(duplicateDataIds)) {
            return createAccountMainData(context, accountDataDocument);
        }
        if (duplicateDataIds.size() > 1) {
            return createAccountMainData(context, accountDataDocument);
        }
        BaseObjectSaveAction.Result rst = new BaseObjectSaveAction.Result();
        IObjectData duplicateData = metaDataFindServiceExt.findObjectData(context.getUser(), duplicateDataIds.get(0), SFAPreDefineObject.AccountMainData.getApiName());
        rst.setObjectData(ObjectDataDocument.of(duplicateData));
        return rst;
    }

    private BaseObjectSaveAction.Result createAccountMainData(ServiceContext context, ObjectDataDocument accountDataDocument) throws MetadataServiceException {
        // List<String> departmentId = getDepartmentIdList(context.getUser(), accountDataDocument);
        IObjectData data = accountDataDocument.toObjectData();
        MasterDataServiceModel.DepartmentOrgInfo departmentOrgInfo = getDepartmentAndOrgInfo(context.getTenantId(), data);
        if (departmentOrgInfo != null) {
            List<String> dataOwnOrganization = data.getDataOwnOrganization();
            if (CollectionUtils.isEmpty(dataOwnOrganization)) {
                throw new ValidateException(I18N.text(SFA_ORG_CANNOT_BE_EMPTY));
            }
            String orgIdArg = dataOwnOrganization.get(0);
            String newOrgId = departmentOrgInfo.getOrganization();
            if (!orgIdArg.equals(newOrgId)) {
                throw new ValidateException(I18N.text(SFA_OWNER_NOT_BELONG_ARG_ORG));
            }
            data.setDataOwnOrganization(Lists.newArrayList(departmentOrgInfo.getOrganization()));
            data.setDataOwnDepartment(Lists.newArrayList(departmentOrgInfo.getDepartment()));
        }
        IObjectDescribe accountMainDataObjDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(context.getTenantId(), SFAPreDefineObject.AccountMainData.getApiName());
        ObjectDataDocument accountMainDataDocument = ObjectDataDocument.of(accountDataDocument.toObjectData());
        accountMainDataDocument.toObjectData().set("object_describe_api_name", accountMainDataObjDescribe.getApiName());
        accountMainDataDocument.toObjectData().set("object_describe_id", accountMainDataObjDescribe.getId());
        ActionContext actionContext = buildAction(context, SFAPreDefineObject.AccountMainData.getApiName());
        BaseObjectSaveAction.Arg arg = buildAddActionArg(accountMainDataDocument);
        return serviceFacade.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
    }

    public IObjectData getDuplicateAccountMainDataByName(User user, String name) {
        return metaDataFindServiceExt.findObjectByNameIgnoreAll(user, name, SFAPreDefineObject.AccountMainData.getApiName());
    }

    @NotNull
    private BaseObjectSaveAction.Arg buildAddActionArg(ObjectDataDocument dataDocument) {
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(dataDocument);
        BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
        optionInfo.setIsDuplicateSearch(true);
        optionInfo.setUseValidationRule(true);
        optionInfo.setSkipFuncValidate(false);
        arg.setOptionInfo(optionInfo);
        return arg;
    }

    private ActionContext buildAction(ServiceContext context, String objectApiName) {
        return new ActionContext(context.getRequestContext(), objectApiName, SystemConstants.ActionCode.Add.getActionCode());
    }


    public void cleanAllConfigFields(User user, String ruleObjectApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setSearchSource("db");
        List<IFilter> filters = query.getFilters();
        SearchUtil.fillFilterEq(filters, RULE_OBJECT_API_NAME, ruleObjectApiName);
        List<IObjectData> dataList = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, MASTER_DATA_RULE_OBJ, query).getData();
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        List<String> fieldApiNameList = dataList.stream().map(x -> x.get(FIELD_API_NAME).toString()).collect(Collectors.toList());
        lockFields(user, ruleObjectApiName, fieldApiNameList, LockFieldEnum.UNLOCK);
        metaDataService.bulkDeleteDirect(dataList, user);
    }

    public List<String> findDuplicateDataIds(User user, IObjectData objectData) throws MetadataServiceException {
        String describeApiName = objectData.getDescribeApiName();
        IObjectDescribe objectDescribe = findDescribe(user, describeApiName);
        List<DuplicateSearchResult.DuplicateData> duplicateDataResult = serviceFacade.searchDuplicateDataByType(user, objectData, IDuplicatedSearch.Type.NEW, objectDescribe);
        List<String> rst = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(duplicateDataResult)) {
            for (DuplicateSearchResult.DuplicateData duplicateData : duplicateDataResult) {
                rst.addAll(duplicateData.getDataIds());
            }
        }
        return rst;
    }

    public void createMasterDataRuleObj(String tenantId) throws MetadataServiceException {
        describeServiceExt.createDescribeByLocalUri(tenantId, MASTER_DATA_RULE_OBJ);
    }

    public boolean isFillExpireButtonClick(String tenantId) {
        try {
            String value = masterDataRedisCacheService.getFillButtonExpire(tenantId);
            return StringUtils.isBlank(value);
        } catch (Exception e) {
            log.error("get isFillExpireButtonClick redis error, tenant:{}", tenantId, e);
        }
        return true;
    }

    public void setFillExpireButtonCache(String tenantId) {
        try {
            masterDataRedisCacheService.setFillButtonExpire(tenantId);
        } catch (Exception e) {
            log.error("setFillExpireButtonCache redis error, tenant:{}", tenantId, e);
        }
    }

    public List<String> addDownField(List<String> tenants) throws MetadataServiceException {
        if (CollectionUtils.isEmpty(tenants)) {
            return Lists.newArrayList();
        }
        for (String downstreamTenant : tenants) {
            downStreamFieldHandler(downstreamTenant);
        }
        return tenants;
    }

    public void createMasterDataAppObj(String tenantId) throws MetadataServiceException {
        describeServiceExt.createDescribeByLocalUri(tenantId, MASTER_DATA_APP_OBJ);
    }

    public void createFieldFillConfigObj(String tenantId) throws MetadataServiceException {
        describeServiceExt.createDescribeByLocalUri(tenantId, FIELD_FILL_CONFIG_OBJ);
    }

    public void createFieldFillRuleObj(String tenantId) throws MetadataServiceException {
        describeServiceExt.createDescribeByLocalUri(tenantId, FIELD_FILL_RULE_OBJ);
    }

    public void editObject(User user, MasterDataAppModel.ObjectArg arg) {
        IObjectData data = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, arg.getDataId(), MASTER_DATA_APP_OBJ);
        if (data == null) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MASTER_DATA_OBJECT_CONFIGURATION_DOES_NOT_EXIST));
        }
        String masterDataObjectApiName = ObjectDataUtils.getValueOrDefault(data, MASTER_OBJECT_API_NAME, "");
        if (!masterDataObjectApiName.equals(arg.getObjectApiName())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_DATA_DOES_NOT_MATCH_OBJECT_APINAME));
        }
        String originalCodeGenerateField = masterDataConfigService.getCodeGenerateField(user);
        boolean change = editDiff(originalCodeGenerateField, arg, data);
        if (!change) {
            return;
        }
        // 不可以从【外部系统赋码生成】变为【在 CRM 系统生成】
        if (MAIN_ACCOUNT_NO.equals(arg.getCodeGenerateField()) && GROUP_ACCOUNT_NO.equals(originalCodeGenerateField)) {
            throw new ValidateException(I18N.text(SFA_MASTER_CODE_GENERATE_RULE_CAN_NOT_CHANGE));
        }
        data.set(DISPLAY_NAME, arg.getMasterDataName());
        data.set(REMARK, arg.getRemark());
        metaDataFindServiceExt.bulkUpdateByFields(user, Lists.newArrayList(data), Lists.newArrayList(DISPLAY_NAME, REMARK));
        masterDataConfigService.specifyCodeGenerateField(user, arg.getCodeGenerateField());
    }

    private boolean editDiff(String codeGenerateField, MasterDataAppModel.ObjectArg arg, IObjectData data) {
        String masterDataName = arg.getMasterDataName();
        String remark = arg.getRemark();
        String originalDisplayName = ObjectDataUtils.getValueOrDefault(data, DISPLAY_NAME, "");
        String originalRemark = ObjectDataUtils.getValueOrDefault(data, REMARK, "");
        if (!originalRemark.equals(remark) || !originalDisplayName.equals(masterDataName)) {
            return true;
        }
        return !codeGenerateField.equals(arg.getCodeGenerateField());
    }

    public void setModel(User user, ModelEnum modelEnum) {
        masterDataConfigService.setModel(user, modelEnum);
    }

    public ModelEnum getModel(User user) {
        return masterDataConfigService.getModel(user);
    }

    public List<DepartmentDto> getAllOrgInfo(User user, int tenantId) {
        GetAllDepartmentDtoArg arg = new GetAllDepartmentDtoArg();
        arg.setEnterpriseId(tenantId);
        GetAllDepartmentDtoResult rst = departmentProviderService.getAllDepartmentDto(arg);
        if (rst == null) {
            return Lists.newArrayList();
        }
        return rst.getDepartments().stream().filter(x -> {
            if (COMPANY_DEPARTMENT_ID == x.getDepartmentId()) {
                return false;
            }
            return DepartmentStatus.NORMAL.equals(x.getStatus());
        }).collect(Collectors.toList());
    }

    public boolean setOrgMappingOrg(User user, List<MasterDataAppModel.OrgMappingV2> orgMapping) {
        return masterDataStrategyService.saveOrgMappingsV2(user, orgMapping);
    }

    public List<DataSyncModel.Model2Rst> getOrgMappingsOrgInfo(User user) {
        return masterDataStrategyService.getOrgMappingsV2(user);
    }

    public void clearModel1Data(User user) throws MetadataServiceException {
        ModelEnum modelEnum = getModel(user);
        if (ModelEnum.MODEL2.equals(modelEnum)) {
            return;
        }
        List<MasterDataAppModel.AppConfig> configs = masterDataDao.getAppConfigs(user);
        // 先把所有策略明细删除掉
        for (MasterDataAppModel.AppConfig appConfig : configs) {
            masterDataStrategyService.clearMasterDataStrategyDetailByConfig(user, appConfig);
        }
        for (MasterDataAppModel.AppConfig appConfig : configs) {
            String objectApiName = appConfig.getApiName();
            masterDataStrategyService.checkStrategyExists(user, appConfig);
            // 删除配置字段
            clearAllLockFieldConfig(user, objectApiName);
            cleanAllConfigFields(user, objectApiName);
        }
        masterDataDao.deleteAllMainDataObjectData(user);
    }

    public void updateFieldLockStatus(User user, LockFieldEnum lockFieldEnum) throws MetadataServiceException {
        Set<String> masterDataApiNameList = masterDataDao.getAppConfigApiNameList(user);
        if (CollectionUtils.isEmpty(masterDataApiNameList)) {
            return;
        }
        for (String objectApiName : masterDataApiNameList) {
            List<MasterDataAppModel.FieldRule> fieldRuleList = getFieldRuleList(user, objectApiName);
            List<String> fieldApiNames = fieldRuleList.stream().map(MasterDataAppModel.FieldRule::getFieldApiName).collect(Collectors.toList());
            lockFields(user, objectApiName, fieldApiNames, lockFieldEnum);
        }
    }

    public List<String> getConfigDownStreamTenants(String tenantId) {
        return masterDataConfigService.getConfigDownStreamTenants(tenantId);
    }

    public boolean isGrayMasterAppTenant(String tenantId) {
        return MasterDataConfigUtil.isGrayMasterAppTenant(tenantId);
    }

    public List<IObjectDescribe> getObjectLoginDescribeList(User user, List<String> apiNames) {
        return masterDataDao.getObjectLoginDescribeList(user, apiNames);
    }

    public List<MasterDataAppModel.AppConfig> getAppObjectDataList(User user) {
        return masterDataDao.getAppConfigs(user);
    }

    public List<IFieldDescribe> getSupportConfigFieldList(User user, String objectApiName) throws MetadataServiceException {
        List<IFieldDescribe> fieldList = Lists.newArrayList();
        List<String> supportConfigFieldList = masterDataConfigService.getSupportFields(objectApiName);
        if (CollectionUtils.isEmpty(supportConfigFieldList)) {
            return Lists.newArrayList();
        }
        IObjectDescribe describe = masterDataDao.findLoginDescribe(user.getTenantId(), objectApiName);
        if (describe == null) {
            return Lists.newArrayList();
        }
        for (String fieldApiName : supportConfigFieldList) {
            IFieldDescribe fieldDescribe = describe.getFieldDescribe(fieldApiName);
            if (fieldDescribe != null) {
                fieldList.add(fieldDescribe);
            }
        }
        // 支持自定义字段
        // 模式1 从客户上取自定义字段
        // 模式2 从客户主数据上取自定义字段
        ModelEnum model = getModel(user);
        List<IFieldDescribe> customFields;
        if (ModelEnum.MODEL1.equals(model) && SFAPreDefineObject.AccountMainData.getApiName().equals(objectApiName)) {
            IObjectDescribe accountDescribe = describeLogicService.findObject(user.getTenantId(), SFAPreDefineObject.Account.getApiName());
            customFields = accountDescribe.getFieldDescribes().stream().filter(f -> FieldDescribeExt.of(f).isCustomField()).collect(Collectors.toList());
            customFields.forEach(f -> f.setDescribeApiName(SFAPreDefineObject.AccountMainData.getApiName()));
        } else {
            customFields = describe.getFieldDescribes().stream().filter(f -> FieldDescribeExt.of(f).isCustomField()).collect(Collectors.toList());
        }
        fieldList.addAll(customFields);
        return fieldList;
    }

    public void upsertMasterDataAppTenants(String tenantId, List<String> downstreamTenants) {
        masterDataConfigService.upsertMasterDataAppTenants(tenantId, downstreamTenants);
    }

    public Map<String, String> getOrgMappings(User user) {
        return masterDataDao.getOrgMappings(user);
    }

    public boolean openMasterDataApp(String tenantId) {
        return masterDataConfigService.openMasterDataApp(tenantId);
    }

    public IObjectDescribe findDescribe(User user, String objectApiName) throws MetadataServiceException {
        return masterDataDao.findDescribe(user.getTenantId(), objectApiName);
    }

    public IObjectDescribe findLoginDescribe(User user, String objectApiName) throws MetadataServiceException {
        RequestContextManager.getContext().setAttribute(RequestContext.DIRECT_KEY, true);
        return masterDataDao.findLoginDescribe(user.getTenantId(), objectApiName);
    }

    public Map<String, List<String>> getSupportFullFields(String masterObjectApiName) {
        List<MasterDataAppModel.SupportFillFieldResult> supportFillFields = MasterDataConfigUtil.getSupportFillFields();
        if (CollectionUtils.isEmpty(supportFillFields)) {
            return Maps.newHashMap();
        }
        Optional<MasterDataAppModel.SupportFillFieldResult> any = supportFillFields.stream().filter(x -> masterObjectApiName.equals(x.getMasterObjectApiName())).findAny();
        if (any.isPresent()) {
            return any.get().getFillFieldMap();
        }
        return Maps.newHashMap();
    }

    /**
     * 先删后插吧
     */
    public List<MasterDataAppModel.FillField> saveFieldFillRule(User user, List<MasterDataAppModel.FillField> fillFields, String masterObjectApiName, boolean excludedField, String masterFieldConfigId) throws MetadataServiceException {
        if (CollectionUtils.isEmpty(fillFields)) {
            return Lists.newArrayList();
        }
        masterDataDao.deleteFieldFillRuleData(user, excludedField, masterObjectApiName);
        List<IObjectData> fieldFillRuleData = masterDataDao.createFieldFillRuleData(user, fillFields, excludedField, masterFieldConfigId);
        return buildFieldFillRuleDataList(user, masterObjectApiName, fieldFillRuleData);
    }

    public MasterDataAppModel.FieldFillConfig saveMasterFieldFillConfig(User user, MasterDataAppModel.FieldFillConfig fieldFillConfig, String masterObjectApiName) {
        MasterDataAppModel.FieldFillConfig fieldFillConfigData = getFieldFillConfigData(user, masterObjectApiName);
        if (StringUtils.isNotBlank(fieldFillConfigData.getFieldFillConfigId())) {
            masterDataDao.deleteMasterFieldFillConfigData(user, masterObjectApiName, fieldFillConfigData.getFieldFillConfigId());
        }
        IObjectData masterFieldFillConfig = masterDataDao.createMasterFieldFillConfig(user, fieldFillConfig, masterObjectApiName);
        return buildMasterFieldFillConfig(masterFieldFillConfig, masterObjectApiName);
    }

    private MasterDataAppModel.FieldFillConfig buildMasterFieldFillConfig(IObjectData data, String masterObjectApiName) {
        String textFillType = ObjectDataUtils.getValueOrDefault(data, TEXT_FILL_TYPE, "1");
        String attachmentFillType = ObjectDataUtils.getValueOrDefault(data, ATTACHMENT_FILL_TYPE, "3");
        String manySelectFillType = ObjectDataUtils.getValueOrDefault(data, MANY_SELECT_FILL_TYPE, "1");
        String singleSelectFillType = ObjectDataUtils.getValueOrDefault(data, SINGLE_SELECT_FILL_TYPE, "1");
        String otherFillType = ObjectDataUtils.getValueOrDefault(data, OTHER_FILL_TYPE, "1");
        String textSymbol = ObjectDataUtils.getValueOrDefault(data, TEXT_SYMBOL, "1");
        // String attachmentSymbol = ObjectDataUtils.getValueOrDefault(data, ATTACHMENT_SYMBOL, "1");
        // String manySymbol = ObjectDataUtils.getValueOrDefault(data, MANY_SYMBOL, "1");
        return MasterDataAppModel.FieldFillConfig.builder().fieldFillConfigId(data.getId()).masterObjectApiName(masterObjectApiName).textFillType(textFillType).attachmentFillType(attachmentFillType).manySelectFillType(manySelectFillType).singleSelectFillType(singleSelectFillType).otherFillType(otherFillType).textSymbol(textSymbol).build();
    }

    public void saveFillModel(User user, FillModelEnum fillModel) {
        masterDataConfigService.saveFillModel(user, fillModel);
    }

    public FillModelEnum getFillModel(User user) {
        return masterDataConfigService.getFillModel(user);
    }

    public List<MasterDataAppModel.FillField> getFieldFillRuleList(User user, String masterObjectApiName) throws MetadataServiceException {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1000);
        SearchUtil.fillFilterEq(query.getFilters(), MASTER_OBJECT_API_NAME, masterObjectApiName);
        List<IObjectData> dataList = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, FIELD_FILL_RULE_OBJ, query).getData();
        return buildFieldFillRuleDataList(user, masterObjectApiName, dataList);
    }

    private List<MasterDataAppModel.FillField> buildFieldFillRuleDataList(User user, String masterObjectApiName, List<IObjectData> dataList) throws MetadataServiceException {
        List<MasterDataAppModel.FillField> result = Lists.newArrayList();
        IObjectDescribe loginDescribe = findLoginDescribe(user, masterObjectApiName);
        Map<String, IFieldDescribe> fieldDescribeMap = loginDescribe.getFieldDescribes().stream().filter(MasterDataAppTransferService::filterSupportFields).collect(Collectors.toMap(IFieldDescribe::getApiName, x -> x));
        Map<String, List<String>> supportFullFields = getSupportFullFields(masterObjectApiName);

        for (IObjectData data : dataList) {
            String fieldKey = ObjectDataUtils.getValueOrDefault(data, "field_api_name", "");
            if (StringUtils.isBlank(fieldKey)) {
                continue;
            }
            IFieldDescribe fieldDescribe = fieldDescribeMap.get(fieldKey);
            if (fieldDescribe == null) {
                continue;
            }

            String fieldApiName = ObjectDataUtils.getValueOrDefault(data, FIELD_API_NAME, "");
            String fillType = ObjectDataUtils.getValueOrDefault(data, FILL_TYPE, "1");
            String fillSymbol = ObjectDataUtils.getValueOrDefault(data, FILL_SYMBOL, "1");
            MasterDataAppModel.FillField fillField = MasterDataAppModel.FillField.builder().fillFieldId(data.getId()).masterObjectApiName(masterObjectApiName).fieldApiName(fieldApiName).fillType(fillType).fillSymbol(fillSymbol).fieldFillConfigId(ObjectDataUtils.getValueOrDefault(data, FIELD_FILL_CONFIG_ID, "")).supportRuleType(supportFullFields.get(fieldDescribe.getApiName())).excluded(ObjectDataUtils.getBooleanOrDefault(data, EXCLUDED, Boolean.FALSE)).displayName(fieldDescribe.getLabel()).build();
            result.add(fillField);
        }
        return result;
    }

    public MasterDataAppModel.FieldFillConfig getFieldFillConfigData(User user, String masterObjectApiName) {
        // 现在只支持客户主数据对象，支持其他对象的时候需要在库加上master_object_api_name 字段，且搜索时根据 master_object_api_name 搜素
        IObjectData objectByField = metaDataFindServiceExt.findObjectByField(user, FIELD_FILL_CONFIG_OBJ, MASTER_OBJECT_API_NAME, masterObjectApiName);
        if (objectByField == null) {
            return MasterDataAppModel.FieldFillConfig
                    .builder()
                    .fieldFillConfigId("")
                    .masterObjectApiName(masterObjectApiName)
                    .textFillType("1")
                    .attachmentFillType("3")
                    .manySelectFillType("1")
                    .singleSelectFillType("1")
                    .otherFillType("1")
                    .textSymbol("1")
                    .attachmentSymbol("")
                    .manySymbol("")
                    .build();
        }
        return buildMasterFieldFillConfig(objectByField, masterObjectApiName);
    }

    public List<MasterDataAppModel.FillField> getExcludeFillFields(User user, String masterObjectApiName, String fieldConfigDataId) throws MetadataServiceException {
        if (StringUtils.isBlank(fieldConfigDataId)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1000);
        SearchUtil.fillFilterEq(query.getFilters(), MASTER_OBJECT_API_NAME, masterObjectApiName);
        SearchUtil.fillFilterEq(query.getFilters(), FIELD_FILL_CONFIG_ID, fieldConfigDataId);
        List<IObjectData> dataList = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, FIELD_FILL_RULE_OBJ, query).getData();
        return buildFieldFillRuleDataList(user, masterObjectApiName, dataList);
    }

    public List<DepartmentDto> getOrgDeptInfo(String tenantId, String orgId) {
        List<DepartmentDto> departmentDtoList;
        if (StringUtils.isNotBlank(orgId)) {
            GetLowDepartmentsDtoArg arg = new GetLowDepartmentsDtoArg();
            arg.setDepartmentId(ConvertUtils.convertStringToInteger(orgId));
            arg.setSelf(false);
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setRunStatus(RunStatus.ACTIVE);
            GetLowDepartmentsDtoResult result = departmentProviderService.getLowDepartmentsDto(arg);
            if (result == null || CollectionUtils.isEmpty(result.getDepartmentDtos())) {
                return Lists.newArrayList();
            }
            departmentDtoList = result.getDepartmentDtos();
        } else {
            GetAllDepartmentDtoArg arg = new GetAllDepartmentDtoArg();
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setRunStatus(RunStatus.ACTIVE);
            GetAllDepartmentDtoResult result = departmentProviderService.getAllDepartmentDto(arg);
            if (result == null || CollectionUtils.isEmpty(result.getDepartments())) {
                return Lists.newArrayList();
            }
            departmentDtoList = result.getDepartments();
        }
        return departmentDtoList;
    }

    public List<DepartmentDto> getOrgDeptByIds(String tenantId, List<Integer> orgIds) {
        if (CollectionUtils.isEmpty(orgIds) || StringUtils.isEmpty(tenantId)) {
            return Lists.newArrayList();
        }
        BatchGetDepartmentDtoArg arg = new BatchGetDepartmentDtoArg();
        arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
        arg.setDepartmentIds(orgIds);
        arg.setRunStatus(RunStatus.ACTIVE);
        BatchGetDepartmentDtoResult result = departmentProviderService.batchGetDepartmentDto(arg);
        return result.getDepartments();
    }

    public String upstreamDownstreamOrgMapping(MasterDataAppModel.UpDownStreamOrgMappingArg arg) {
        boolean isGetOrg = DBRecord.DATA_OWN_ORGANIZATION.equals(arg.getFieldApiName());
        boolean isCollect = arg.getSyncPloyType() == SyncPloyTypeEnum.COLLECT.getType();
        String sourceFieldName = UPSTREAM_ORG_ID;
        String targetFieldName = DOWNSTREAM_ORG_ID;
        if (isCollect) {
            sourceFieldName = DOWNSTREAM_ORG_ID;
            targetFieldName = UPSTREAM_ORG_ID;
        }
        User upstreamAdminUser = new User(arg.getUpstreamTenantId(), User.SUPPER_ADMIN_USER_ID);
        // 采集的情况下，下游未开启客户主数据
        if (isCollect && !openAccountMainData(arg.getDownstreamTenantId()) && isGetOrg) {
            return getOrgByMapping(upstreamAdminUser, String.valueOf(DepartmentDto.COMPANY_DEPARTMENT_ID), arg.getDownstreamTenantId(),
                    sourceFieldName, targetFieldName, true);
        }


        // ------下游开启了客户主数据------
        List<String> orgIds = arg.getObjectData().toObjectData().get(arg.getFieldApiName(), List.class);
        if (CollectionUtils.isEmpty(orgIds)) {
            throw new ValidateException(I18N.text(SFA_MASTER_DATA_NONE_ORG_DEPT_ID_PARAM));
        }
        // 虽是数组，但是只有一个元素，故取第一位即可
        String orgDeptId = orgIds.get(0);
        return isGetOrg ?
                getOrgByMapping(upstreamAdminUser, orgDeptId, arg.getDownstreamTenantId(), sourceFieldName, targetFieldName, isCollect)
                : getDeptByMapping(upstreamAdminUser, orgDeptId, arg.getDownstreamTenantId(), sourceFieldName, targetFieldName);
    }

    private String getDeptByMapping(User user, String orgDeptId, String downstreamTenantId, String sourceFieldName, String targetFieldName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, sourceFieldName, orgDeptId);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, DOWNSTREAM_TENANT_ID, downstreamTenantId);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, DBRecord.IS_DELETED, "0");
        List<IObjectData> objectDataList = masterDataDao.getOrgMappingByQuery(user, MASTER_DATA_ORG_MAPPING_OBJ, query);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return Strings.EMPTY;
        }
        return objectDataList.get(0).get(targetFieldName, String.class);
    }

    private String getOrgByMapping(User upstreamAdminUser, String orgIdParam, String downstreamTenantId, String sourceFieldName, String targetFieldName, boolean isCollect) {
        List<IObjectData> deptOrgMappingDataList = getAllDeptOrgMappingDataByDownstram(upstreamAdminUser, downstreamTenantId);
        if (CollectionUtils.isEmpty(deptOrgMappingDataList)) {
            return Strings.EMPTY;
        }
        // 根据 orgId 直接获取映射的组织
        String targetOrgId = getTargetFieldValueBySourceField(targetFieldName, sourceFieldName, orgIdParam, deptOrgMappingDataList);
        if (StringUtils.isNotBlank(targetOrgId)) {
            return targetOrgId;
        }
        // 为空代表映射中没有配置对应映射关系，此时需要根据 orgId 所属上级在映射配置中对应的映射关系取组织，若都没有，则返回空。
        // 拿到第一层节点
        Optional<String> parentNodeOptional = deptOrgMappingDataList.stream()
                .filter(d -> StringUtils.isBlank(ObjectDataUtils.getValueOrDefault(d, PARENT_ID, "")))
                .map(d -> ObjectDataUtils.getValueOrDefault(d, sourceFieldName, ""))
                .findFirst();
        if (!parentNodeOptional.isPresent()) {
            return Strings.EMPTY;
        }
        // 如果是采集则查下游组织架构，下发则查上游组织架构
        List<DepartmentDto> orgInfoList;
        DepartmentDto orgInfo;
        if (isCollect) {
            orgInfo = getOrgInfo(downstreamTenantId, parentNodeOptional.get());
            orgInfoList = getLowOrgInfoList(downstreamTenantId, parentNodeOptional.get());
        } else {
            orgInfoList = getLowOrgInfoList(upstreamAdminUser.getTenantId(), parentNodeOptional.get());
            orgInfo = getOrgInfo(upstreamAdminUser.getTenantId(), parentNodeOptional.get());
        }
        if (CollectionUtils.isEmpty(orgInfoList) || orgInfo == null) {
            return Strings.EMPTY;
        }
        orgInfoList.add(orgInfo);
        Map<String, DepartmentDto> orgItemMapping = orgInfoList.stream().collect(Collectors.toMap(o -> String.valueOf(o.getDepartmentId()), o -> o, (k1, k2) -> k1));
        Set<String> orgIdSetsFromMapping = deptOrgMappingDataList
                .stream()
                .filter(d -> StringUtils.isNotBlank(ObjectDataUtils.getValueOrDefault(d, sourceFieldName, "")))
                .map(d -> ObjectDataUtils.getValueOrDefault(d, sourceFieldName, ""))
                .collect(Collectors.toSet());
        // 递归次数计数，防止递归死循环
        int recursionCount = 0;
        String recentOrgId = getRecentParentId(orgItemMapping, orgIdSetsFromMapping, orgIdParam, recursionCount);
        if (StringUtils.isBlank(recentOrgId)) {
            return Strings.EMPTY;
        }
        return getTargetFieldValueBySourceField(targetFieldName, sourceFieldName, recentOrgId, deptOrgMappingDataList);
    }

    private String getRecentParentId(Map<String, DepartmentDto> orgItemMapping, Set<String> orgIdSetsFromMapping, String orgId, int recursionCount) {
        if (recursionCount >= 100) {
            return Strings.EMPTY;
        }
        DepartmentDto node = orgItemMapping.get(orgId);
        if (node == null) {
            return Strings.EMPTY;
        }
        if (orgIdSetsFromMapping.contains(orgId)) {
            return orgId;
        }
        String parentOrgId = String.valueOf(node.parentId());
        return getRecentParentId(orgItemMapping, orgIdSetsFromMapping, parentOrgId, recursionCount);
    }

    private String getTargetFieldValueBySourceField(String targetFieldName, String sourceFieldName, String orgId, List<IObjectData> deptOrgMappingDataList) {
        Optional<String> orgOptional = deptOrgMappingDataList.stream()
                .filter(d -> ObjectDataUtils.getValueOrDefault(d, sourceFieldName, "").equals(orgId))
                .map(d -> ObjectDataUtils.getValueOrDefault(d, targetFieldName, ""))
                .findFirst();
        return orgOptional.orElse(Strings.EMPTY);
    }

    /**
     * 获取
     *
     * @param tenantId
     * @param orgId
     * @return
     */
    private List<DepartmentDto> getLowOrgInfoList(String tenantId, String orgId) {
        List<DepartmentDto> orgDeptInfoList = getOrgDeptInfo(tenantId, orgId);
        if (CollectionUtils.isEmpty(orgDeptInfoList)) {
            return Lists.newArrayList();
        }
        return orgDeptInfoList.stream().filter(o -> RECORD_TYPE_ORGANIZATION.equals(o.getRecordType())).collect(Collectors.toList());
    }

    @Nullable
    private List<IObjectData> getAllDeptOrgMappingDataByDownstram(User user, String downstreamTenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(0);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, DOWNSTREAM_TENANT_ID, downstreamTenantId);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, DBRecord.IS_DELETED, "0");
        List<IObjectData> objectDataList = masterDataDao.getOrgMappingByQuery(user, MASTER_DATA_ORG_MAPPING_OBJ, query);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return null;
        }
        return objectDataList;
    }

    private DepartmentDto getOrgInfo(String tenantId, String orgId) {
        GetDepartmentDtoArg arg = new GetDepartmentDtoArg();
        arg.setDepartmentId(Integer.parseInt(orgId));
        arg.setEnterpriseId(Integer.parseInt(tenantId));
        GetDepartmentDtoResult departmentDto = departmentProviderService.getDepartmentDto(arg);
        if (departmentDto == null || departmentDto.getDepartment() == null || !RECORD_TYPE_ORGANIZATION.equals(departmentDto.getDepartment().getRecordType())) {
            return null;
        }
        return departmentDto.getDepartment();
    }

    public boolean deleteLockField(User user, String objectApiName, List<String> lockFieldApiNames) {
        IActionContext action = ActionContextExt.of(user).getContext();
        try {
            immutableFieldService.deleteImmutableFieldResource(objectApiName, lockFieldApiNames, action);
        } catch (MetadataServiceException e) {
            log.warn("deleteLockField failed, tenantId:{}, objectApiName:{}, fields:{}", user.getTenantId(), objectApiName, lockFieldApiNames);
            return false;
        }
        return true;
    }

    public boolean upsertLockFiled(User downStreamUser, String objectApiName, List<String> lockFieldApiNames, String control) {
        IActionContext action = ActionContextExt.of(downStreamUser).getContext();
        List<IImmutableResource> iImmutableFields = Lists.newArrayList();
        for (String lockFieldApiName : lockFieldApiNames) {
            IImmutableResource immutableField = new ImmutableResource();
            immutableField.setResourceValue(lockFieldApiName);
            immutableField.setControlLevel(control);
            iImmutableFields.add(immutableField);
        }
        try {
            immutableFieldService.upsert(objectApiName, iImmutableFields, action);
        } catch (MetadataServiceException e) {
            log.error("upsertLockFiled failed, tenantId:{}, objectApiName:{}, fields:{}", downStreamUser.getTenantId(), objectApiName, lockFieldApiNames);
            return false;
        }
        return true;
    }

    public void updateConnectCoding(String downstreamTenantId, IObjectData downstreamAccountMainData, IObjectData upstreamAccountMainData) {
        if (downstreamAccountMainData == null || upstreamAccountMainData == null) {
            return;
        }
        String connectCoding = ObjectDataUtils.getValueOrDefault(upstreamAccountMainData, MAIN_ACCOUNT_NO, "");
        if (downstreamAccountMainData.get(CONNECT_CODING) == null) {
            downstreamAccountMainData.set(CONNECT_CODING, connectCoding);
            metaDataFindServiceExt.bulkUpdateByFields(downstreamTenantId, Lists.newArrayList(downstreamAccountMainData),
                    Lists.newArrayList(CONNECT_CODING));
        }
    }

    public IObjectData getAccountMainDataByAccountData(String tenantId, IObjectData accountData) {
        if (StringUtils.isBlank(tenantId) || accountData == null) {
            return null;
        }
        String accountMainDataId = ObjectDataUtils.getValueOrDefault(accountData, ACCOUNT_MAIN_DATA_ID, "");
        if (StringUtils.isBlank(accountMainDataId)) {
            return null;
        }
        return metaDataFindServiceExt.findObjectByIdIgnoreAll(tenantId, accountMainDataId, SFAPreDefineObject.AccountMainData.getApiName());
    }

    public IObjectData getAccountMainDataByAccountData(String tenantId, String accountDataId) {
        if (StringUtils.isAnyBlank(tenantId, accountDataId)) {
            return null;
        }
        IObjectData accountData = metaDataFindServiceExt.findObjectByIdIgnoreAll(tenantId, accountDataId, SFAPreDefineObject.Account.getApiName());
        return getAccountMainDataByAccountData(tenantId, accountData);
    }

    @Override
    public void sendMasterDataSyncLog(User user, IObjectDescribe objectDescribe, IObjectData objectData, Object syncObject) {
        if (objectData == null || objectDescribe == null || user == null || syncObject == null) {
            return;
        }
        MasterDataAppModel.SyncDataSourceInfo syncDataSourceInfo = (MasterDataAppModel.SyncDataSourceInfo) syncObject;
        String textMessage;
        String sourceTenantId = syncDataSourceInfo.getSourceTenantId();
        String enterpriseName = getEnterpriseName(sourceTenantId);
        if (StringUtils.isBlank(enterpriseName)) {
            return;
        }
        if (StrategyTypeEnum.COLLECT.getTypeCode().equals(syncDataSourceInfo.getSyncType())) {
            // 该数据由$子公司$通过数据同步采集（子公司企业取自于数据同步采集策略中的数据源企业）
            textMessage = I18N.text(SFA_MASTER_DATA_COLLECT_LOG, enterpriseName);
        } else {
            // 该数据由$集团企业$通过数据同步下发（集团企业取自于数据同步下发策略中的数据源企业）
            textMessage = I18N.text(SFA_MASTER_DATA_ALLOCATE_LOG, enterpriseName);
        }
        if (StringUtils.isBlank(textMessage)) {
            return;
        }
        textMessage = "[master-data-app] " + textMessage;
        serviceFacade.logWithCustomMessage(user, EventType.ADD, ActionType.Add, objectDescribe, objectData, textMessage);
    }

    public String getEnterpriseName(String tenantId) {
        GetSimpleEnterpriseDataArg simpleEnterpriseDataArg = new GetSimpleEnterpriseDataArg();
        simpleEnterpriseDataArg.setEnterpriseId(Integer.parseInt(tenantId));
        GetSimpleEnterpriseDataResult simpleEnterpriseDataResult = enterpriseEditionService.getSimpleEnterpriseData(simpleEnterpriseDataArg);
        return Optional.ofNullable(simpleEnterpriseDataResult).map(GetSimpleEnterpriseDataResult::getEnterpriseData).map(SimpleEnterpriseData::getEnterpriseName).orElse(null);
    }

    /**
     * Map: tenantId, tenantName
     *
     * @param tenants
     * @return
     */
    public Map<String, String> getEnterpriseName(Set<String> tenants) {
        BatchGetSimpleEnterpriseArg arg = new BatchGetSimpleEnterpriseArg();
        List<Integer> tenantsOfInteger = tenants.stream().filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
        arg.setEnterpriseIds(tenantsOfInteger);
        BatchGetSimpleEnterpriseResult result = enterpriseEditionService.batchGetSimpleEnterprise(arg);
        return Optional.ofNullable(result)
                .map(BatchGetSimpleEnterpriseResult::getSimpleEnterpriseList)
                .orElseGet(Collections::emptyList)
                .stream()
                .collect(Collectors.toMap(
                        x -> String.valueOf(x.getEnterpriseId()),
                        SimpleEnterprise::getEnterpriseName
                ));
    }

    public Boolean deleteObject(User user, MasterDataAppModel.ObjectArg arg) {
        // 删除主数据应用对象数据
        boolean deleteAppObject = masterDataDao.deleteObjectItemById(user, arg.getDataId());
        // 删除锁定字段
        masterDataDao.deleteLockFields(user, arg.getObjectApiName());
        if (arg.getObjectApiName().equals(SFAPreDefineObject.AccountMainData.getApiName()))
            clearTempStrategyDetail(user);
        return deleteAppObject;
    }

    public void asyncSaveMasterDataSyncMapping(User upUser, MasterDataAppModel.SyncMapping syncMapping) {
        if (syncMapping == null) {
            log.info("MasterDataService#asyncSaveMasterDataSyncMapping but syncMapping is null, tenantId:{}", upUser.getTenantId());
            return;
        }
        if (StringUtils.isAnyBlank(syncMapping.getSourceDataId(),
                syncMapping.getTargetDataId(),
                syncMapping.getSourceObjectApiName(),
                syncMapping.getTargetObjectApiName(),
                syncMapping.getDownstreamTenantId())) {
            log.info("asyncSaveMasterDataSyncMapping# but param is null syncMapping:{}", syncMapping);
            return;
        }
        syncMapping.setUpstreamTenantId(upUser.getTenantId());
        asyncTaskProducer.create(MASTER_DATA_APP_SYNC_MAPPING, JSON.toJSONString(syncMapping));
    }

    public void updateInheritFieldValue(User upUser, String downstreamTenantId, IObjectData downstreamAccountMainData, IObjectData upstreamAccountMainData) {
        List<MasterDataAppModel.SyncField> configuredSyncFields = masterDataConfigService.getConfiguredSyncFields(upUser);
        if (CollectionUtils.isEmpty(configuredSyncFields)) {
            return;
        }
        Set<String> syncFields = configuredSyncFields
                .stream()
                .map(MasterDataAppModel.SyncField::getFieldApiName)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        syncFields.forEach(fieldApiName -> {
            Object value = upstreamAccountMainData.get(fieldApiName);
            downstreamAccountMainData.set(fieldApiName, value);
        });
        metaDataFindServiceExt.bulkUpdateByFields(downstreamTenantId, Lists.newArrayList(downstreamAccountMainData),
                Lists.newArrayList(syncFields));
    }

    public void deleteFieldRule(User user) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(0);
        SearchUtil.fillFilterEq(query.getFilters(), RULE_OBJECT_API_NAME, SFAPreDefineObject.AccountMainData.getApiName());
        List<IObjectData> fieldRuleDataList = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, MASTER_DATA_RULE_OBJ, query).getData();
        if (CollectionUtils.isEmpty(fieldRuleDataList)) {
            return;
        }
        masterDataDao.bulkDeleteDirect(fieldRuleDataList, user);
    }

    public void saveFieldRule(User user, List<MasterDataAppModel.SyncField> syncFieldList) {
        List<MasterDataAppModel.FieldConfig> fieldConfigList = converterFieldConfigData(syncFieldList);
        List<IObjectData> saveDataList = buildMainConfigFieldData(user, fieldConfigList, SFAPreDefineObject.AccountMainData.getApiName(), Lists.newArrayList());
        if (CollectionUtils.isEmpty(saveDataList)) {
            return;
        }
        masterDataDao.bulkCreateMainConfigField(user, saveDataList);
    }

    private List<MasterDataAppModel.FieldConfig> converterFieldConfigData(List<MasterDataAppModel.SyncField> syncFieldList) {
        List<MasterDataAppModel.FieldConfig> fieldConfigList = Lists.newArrayList();
        for (MasterDataAppModel.SyncField syncField : syncFieldList) {
            MasterDataAppModel.FieldConfig fieldConfig = new MasterDataAppModel.FieldConfig();
            fieldConfig.setFieldApiName(syncField.getFieldApiName());
            fieldConfig.setType(StrategyTypeEnum.ALLOCATE.getStrCode());
            fieldConfigList.add(fieldConfig);
        }
        return fieldConfigList;
    }

    public MasterDataAppModel.DataRangeConvertResult dataRangeConvert(MasterDataAppModel.DataRangeConvertArg arg, StrategyTypeEnum syncPloyType, String orgId, String deptId) {
        MasterDataAppModel.DataRangeConvertResult result = MasterDataAppModel.DataRangeConvertResult.builder().build();
        if (syncPloyType == null) {
            return result;
        }
        User downstreamUser = new User(arg.getDownstreamTenantId(), User.SUPPER_ADMIN_USER_ID);
        User upstreamUser = new User(arg.getUpstreamTenantId(), User.SUPPER_ADMIN_USER_ID);
        MasterDataAppModel.DataRange dataRange = MasterDataAppModel.DataRange.builder().build();
        switch (syncPloyType) {
            case COLLECT:
                dataRange = convertCollectRange(upstreamUser, downstreamUser, orgId, deptId);
                break;
            case ALLOCATE:
                dataRange = convertAllocateRange(upstreamUser, downstreamUser, orgId, deptId);
                break;
            default:
                break;
        }
        result.setDataRange(dataRange);
        return result;
    }

    private MasterDataAppModel.DataRange convertAllocateRange(User upstreamUser, User downstreamUser, String orgId, String deptId) {
        MasterDataAppModel.DataRange dataRange = MasterDataAppModel.DataRange.builder().build();
        List<String> convertOrgIds = getConvertOrgIds(upstreamUser, downstreamUser, orgId, UPSTREAM_ORG_ID, false);
        dataRange.getConvertOrgIds().addAll(convertOrgIds);
        List<String> convertDeptIds = getConvertDeptIds(upstreamUser, downstreamUser, deptId);
        dataRange.getConvertDeptIds().addAll(convertDeptIds);
        return dataRange;
    }

    private List<String> getConvertDeptIds(User upstreamUser, User downstreamUser, String deptId) {
        if (StringUtils.isBlank(deptId)) {
            return Lists.newArrayList();
        }
        String deptByMapping = getDeptByMapping(upstreamUser, deptId, downstreamUser.getTenantId(), UPSTREAM_ORG_ID, UPSTREAM_ORG_ID);
        if (StringUtils.isBlank(deptByMapping)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(deptByMapping);
    }

    private List<String> getConvertOrgIds(User upstreamUser, User downstreamUser, String orgId, String fieldApiName, boolean isCollect) {
        if (StringUtils.isBlank(orgId)) {
            return Lists.newArrayList();
        }
        String orgByMapping = getOrgByMapping(upstreamUser, orgId, downstreamUser.getTenantId(), fieldApiName, fieldApiName, isCollect);
        // 找到 orgByMapping 的所有下级
        if (StringUtils.isBlank(orgByMapping)) {
            return Lists.newArrayList();
        }
        if (orgByMapping.equals(orgId)) {
            return Lists.newArrayList(orgByMapping);
        }
        String tenantId = upstreamUser.getTenantId();
        if (isCollect) {
            tenantId = downstreamUser.getTenantId();
        }
        List<DepartmentDto> lowOrgInfoList = getLowOrgInfoList(tenantId, orgByMapping);
        return Optional.ofNullable(lowOrgInfoList).orElse(Lists.newArrayList()).stream().map(d -> String.valueOf(d.getDepartmentId())).collect(Collectors.toList());
    }

    private MasterDataAppModel.DataRange convertCollectRange(User upstreamUser, User downstreamUser, String orgId, String deptId) {
        MasterDataAppModel.DataRange dataRange = MasterDataAppModel.DataRange.builder().build();
        List<String> convertOrgIds = getConvertOrgIds(upstreamUser, downstreamUser, orgId, DOWNSTREAM_ORG_ID, true);
        dataRange.getConvertOrgIds().addAll(convertOrgIds);
        List<String> convertDeptIds = getConvertDeptIds(upstreamUser, downstreamUser, deptId);
        dataRange.getConvertDeptIds().addAll(convertDeptIds);
        return dataRange;
    }

    public Boolean deleteObjectRest(User user, MasterDataAppModel.ObjectArg arg) {
        // 删除主数据应用对象数据
        boolean deleteAppObject = masterDataDao.deleteObjectItemById(user, arg.getDataId());
        // 删除锁定字段
        masterDataDao.deleteLockFields(user, arg.getObjectApiName());
        return deleteAppObject;
    }
}


