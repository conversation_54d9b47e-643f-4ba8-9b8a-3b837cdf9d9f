package com.facishare.crm.newpayment.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.crm.newpayment.manager.NewPaymentManger;
import com.facishare.crm.newpayment.util.NewPaymentUtil;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class NewOrderPaymentListHeaderController extends StandardListHeaderController {
    private final NewPaymentManger newPaymentManger = SpringUtil.getContext().getBean(NewPaymentManger.class);
    private final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        LayoutDocument layoutDocument = result.getLayout();
        NewPaymentUtil.removeFields(layoutDocument, Sets.newHashSet(NewPaymentConst.FIELD_APPROVE_EMPLOYEE_ID));
        layoutDocument = newPaymentManger.addPaymentButton(controllerContext.getUser(), layoutDocument);
        result.setLayout(layoutDocument);
        List<ButtonDocument> buttonDocuments = result.getButtons();
        if (CollectionUtils.empty(buttonDocuments)) {
            buttonDocuments = Lists.newArrayList();
        }
        List<String> functionCodes = StandardAction.Invalid.getFunPrivilegeCodes();
        Map<String, Boolean> functionMap = serviceFacade.funPrivilegeCheck(controllerContext.getUser(), Utils.ORDER_PAYMENT_API_NAME, functionCodes);
        boolean hasFunction = functionCodes.stream().anyMatch(x -> {
            Boolean function = functionMap.getOrDefault(x, Boolean.FALSE);
            return BooleanUtils.isTrue(function);
        });
        boolean noInvalidButton = buttonDocuments.stream().noneMatch(x -> {
            String actionCode = ButtonExt.of(x).getActionCode();
            return ObjectAction.BULK_INVALID.getActionCode().equals(actionCode) || ObjectAction.INVALID.getActionCode().equals(actionCode);
        });
        if (hasFunction && noInvalidButton) {
            buttonDocuments.add(ButtonDocument.fromButton(NewPaymentUtil.getAsyncBulkButton(ObjectAction.INVALID)));
            result.setButtons(buttonDocuments);
        }
        List<IButton> btnList = result.getLayout().toLayout().getButtons();
        btnList.removeIf(o -> "IntelligentForm_button_default".equals(o.getName()));
        result.getLayout().toLayout().setButtons(btnList);
        return result;
    }
    @Override
    protected List<IButton> getButtons() {
        List<IButton> buttons = super.getButtons();
        if (bizConfigThreadLocalCacheService.isOpenOrderPaymentMultiSource(controllerContext.getUser().getTenantId())) {
            buttons.removeIf(button -> Objects.equals(button.getAction(), ObjectAction.CREATE.getActionCode()));
            //buttons.removeIf(button -> Objects.equals(button.getAction(), ObjectAction.BATCH_IMPORT.getActionCode()));
        }
        return buttons;
    }
}
