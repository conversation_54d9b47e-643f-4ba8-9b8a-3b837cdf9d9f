package com.facishare.crm.newpayment.controller;

import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.crm.newpayment.util.NewPaymentUtil;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Sets;

import java.util.List;

public class NewPaymentListHeaderController extends StandardListHeaderController {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        LayoutDocument layoutDocument = NewPaymentUtil.removeFields(result.getLayout(),
                Sets.newHashSet(NewPaymentConst.FIELD_APPROVED_EMPLOYEE_ID, NewPaymentConst.FIELD_APPROVE_EMPLOYEE_ID));
        result.setLayout(layoutDocument);
        List<IButton> btnList = result.getLayout().toLayout().getButtons();
        btnList.removeIf(o -> "IntelligentForm_button_default".equals(o.getName()));
        result.getLayout().toLayout().setButtons(btnList);
        return result;
    }
}
