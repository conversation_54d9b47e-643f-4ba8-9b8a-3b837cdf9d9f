package com.facishare.crm.newpayment.controller;

import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.crm.newpayment.manager.NewPaymentManger;
import com.facishare.crm.newpayment.constants.CommonConstants;
import com.facishare.crm.newpayment.util.NewPaymentUtil;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardNewDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.component.ComponentFactory;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Set;

public class NewPaymentNewDetailController extends StandardNewDetailController {
    private final NewPaymentManger newPaymentManager = SpringUtil.getContext().getBean(NewPaymentManger.class);

    @Override
    protected Result doService(Arg arg) {
        Result r = super.doService(arg);
        String paymentId = r.getData().getId();
        List<IObjectData> orderPaymentDataList = newPaymentManager.queryOrderPaymentByPaymentId(controllerContext.getUser(), paymentId);

        Set<String> orderIds = Sets.newHashSet();
        orderPaymentDataList.forEach(item -> orderIds.add(item.get(NewPaymentConst.ORDER_ID, String.class, "")));
        r.getData().put(NewPaymentConst.ORDER_ID, Joiner.on(",").join(orderIds));
        newPaymentManager.parseOrderName(controllerContext.getUser(), Lists.newArrayList(r.getData()));

        String tenantId = controllerContext.getTenantId();
        String value = serviceFacade.findTenantConfig(User.systemUser(tenantId), CommonConstants.SALES_ORDER_PAY_DIRECTLY_SWITCH_KEY);
        if (!StringUtils.equals(CommonConstants.SALES_ORDER_PAY_DIRECTLY_OPEN_VALUE, value)) {
            try {
                IComponent multiComponent = ComponentFactory.newInstance(NewPaymentUtil.builderPaymentRecordRelatedMap());
                LayoutExt.of(result.getLayout()).addComponent(multiComponent);
            } catch (MetadataServiceException e) {
                log.error("payment new detail error,context:{}", controllerContext, e);
            }
        }
        r = modifyResult(r);
        return r;
    }

    private Result modifyResult(Result result) {
        LayoutDocument layoutDocument = NewPaymentUtil.removeFields(result.getLayout(), Sets.newHashSet(NewPaymentConst.FIELD_APPROVE_EMPLOYEE_ID));
        result.setLayout(layoutDocument);
        return result;
    }
}
