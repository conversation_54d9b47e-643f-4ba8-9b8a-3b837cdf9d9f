package com.facishare.crm.payment.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.payment.constant.CustomerPaymentObj;
import com.facishare.crm.payment.constant.OrderPaymentObj;
import com.facishare.crm.payment.proxy.CustomerAccountForPaymentProxy;
import com.facishare.crm.payment.proxy.model.OrderPaymentCostByOrderPaymentIdModel;
import com.facishare.crm.payment.service.dto.Args;
import com.facishare.crm.payment.service.dto.CalculateOrdersPayment;
import com.facishare.crm.payment.service.dto.CalculateOrdersPaymentForBatch;
import com.facishare.crm.payment.utils.MetaDataServiceExt;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.POST;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@ServiceModule("order_payment")
@Component
@Slf4j
public class OrderPaymentService {
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private  MetaDataServiceExt metaDataServiceExt;
    @Autowired
    private CustomerAccountForPaymentProxy customerAccountForPaymentProxy;

    @POST
    @ServiceMethod("calculate_orders_payment_money")
    public Map<String, BigDecimal> calculateOrdersPaymentMoney(ServiceContext context, Args.CalculateOrdersPaymentMoneyArg arg) {
        if (log.isDebugEnabled()) {
            log.debug("calculateOrdersPaymentMoney arg: {} context: {}", JsonUtil.toJson(arg), context);
        }
        Map<String, BigDecimal> map = new HashMap<>();
        if (CollectionUtils.isEmpty(arg.getIds()) || StringUtils.isBlank(arg.getStatus())) {
            return map;
        }
        arg.getIds().forEach(orderId -> {
            map.put(orderId, BigDecimal.ZERO);
        });
        String sql = appendSql(arg.getIds(), arg.isDnr(), arg.getStatus());
        QueryResult<IObjectData> dataResult;
        try {
            dataResult = objectDataService.findBySql(sql, context.getTenantId(), Utils.ORDER_PAYMENT_API_NAME);
        } catch (MetadataServiceException e) {
            log.error("metadata findBySql error. sql ", sql);
            throw new APPException("system error.");
        }
        List<IObjectData> datas = dataResult.getData();
        if (datas.isEmpty()) {
            return map;
        }
        datas.forEach(data -> {
            BigDecimal amount = BigDecimal.valueOf(Double.parseDouble(data.get("sum").toString()));
            map.put(data.get("order_id").toString(), amount);
        });
        if (log.isDebugEnabled()) {
            log.debug("end calculateOrdersPaymentMoney arg: {} context: {}", JsonUtil.toJson(arg), context);
        }
        return map;
    }
    @POST
    @ServiceMethod("new_calculate_orders_payment_money_batch")
    public CalculateOrdersPaymentForBatch.Result getUsedCreditAmountBatch(ServiceContext context, CalculateOrdersPaymentForBatch.Arg arg) {
        if (arg.getCustomerIDList() == null || CollectionUtils.isEmpty(arg.getCustomerIDList())) {
            throw new ValidateException("customerId is empty");
        }
        CalculateOrdersPaymentForBatch.Result result = CalculateOrdersPaymentForBatch.Result.builder().value(BigDecimal.ZERO.toString()).build();
        IFilter settleTypeFilter = new Filter();
        settleTypeFilter.setFieldName("settle_type");
        settleTypeFilter.setOperator(Operator.IN);
        if (arg.getIsContainPrepay().equals(arg.getIsContainCredit())) {
            settleTypeFilter.setFieldValues(Lists.newArrayList("1", "3"));
        }
        if (arg.getIsContainPrepay() && !arg.getIsContainCredit()) {
            settleTypeFilter.setFieldValues(Lists.newArrayList("1"));
        }
        if (!arg.getIsContainPrepay() && arg.getIsContainCredit()) {
            settleTypeFilter.setFieldValues(Lists.newArrayList("3"));
        }
        List<IFilter> filters = Lists.newArrayList(settleTypeFilter);
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, "0");
        SearchUtil.fillFilterIn(filters, "account_id", arg.getCustomerIDList());
        SearchUtil.fillFilterIn(filters, "life_status", Lists.newArrayList("under_review", "normal"));
        //SearchUtil.fillFilterIn(filters, "order_status", Lists.newArrayList("6", "7", "10", "11"));
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(1000);
        searchQuery.setOffset(0);
        searchQuery.setFilters(filters);
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setPermissionType(0);
        searchQuery.setDataRightsParameter(null);
        searchQuery.setNeedReturnQuote(false);
        QueryResult<IObjectData> orderQueryRst = metaDataServiceExt.findBySearchQuery(context.getUser(), "SalesOrderObj", searchQuery);
        if (orderQueryRst.getData() == null || CollectionUtils.isEmpty(orderQueryRst.getData())) {
            return result;
        }
        List<String> orderIds = orderQueryRst.getData().stream().map(DBRecord::getId).collect(Collectors.toList());
        BigDecimal receivableAmountAccount = BigDecimal.ZERO;
        for (IObjectData o : orderQueryRst.getData()) {
            String receivableAmount = o.get("receivable_amount", String.class);
            if (!Strings.isNullOrEmpty(receivableAmount)) {
                receivableAmountAccount = receivableAmountAccount.add(new BigDecimal(receivableAmount));
            }
        }
        BigDecimal ordersPaymentMoneyAccount = BigDecimal.ZERO;
        Map<String, BigDecimal> ordersPaymentMoney = calculateOrdersPaymentMoney(context, Args.CalculateOrdersPaymentMoneyArg.builder().ids(orderIds)
                .dnr(true).status("under_review").build());

        if (MapUtils.isEmpty(ordersPaymentMoney)) {
            return CalculateOrdersPaymentForBatch.Result.builder()
                    .value(receivableAmountAccount.subtract(ordersPaymentMoneyAccount).toString())
                    .build();
        }
        for (BigDecimal value : ordersPaymentMoney.values()) {
            ordersPaymentMoneyAccount = ordersPaymentMoneyAccount.add(value);
        }

        return CalculateOrdersPaymentForBatch.Result.builder()
                .value(receivableAmountAccount.subtract(ordersPaymentMoneyAccount).toString())
                .build();
    }

    @POST
    @ServiceMethod("new_calculate_orders_payment_money")
    public CalculateOrdersPayment.Result GetUsedCreditAmount(ServiceContext context, CalculateOrdersPayment.Arg arg) {
        if (log.isDebugEnabled()) {
            log.debug("calculate_orders_payment_money arg: {} context: {}", JsonUtil.toJson(arg), context);
        }
        if (StringUtils.isEmpty(arg.getCustomerID())) {
            throw new ValidateException("customerId is empty");
        }
        String sql = appendSalesOrderdSql(context.getTenantId(), arg.getCustomerID(), arg.getIsContainPrepay(), arg.getIsContainCredit());
        QueryResult<IObjectData> dataResult;
        try {
            dataResult = objectDataService.findBySql(sql, context.getTenantId(), "SalesOrderObj");
        } catch (MetadataServiceException e) {
            log.error("metadata findBySql error. sql ", sql);
            throw new APPException("system error.");
        }
        List<IObjectData> datas = dataResult.getData();
        if (CollectionUtils.isEmpty(datas)) {
            return CalculateOrdersPayment.Result.builder()
                    .value(BigDecimal.ZERO.toString())
                    .build();
        }
        List<String> orderIds = datas.stream().map(x -> x.get("id", String.class)).collect(Collectors.toList());

        BigDecimal receivableAmountAccount = BigDecimal.ZERO;
        for (IObjectData data : datas) {
            receivableAmountAccount = receivableAmountAccount.add(new BigDecimal(data.get("receivable_amount", String.class)));
        }
        BigDecimal ordersPaymentMoneyAccount = BigDecimal.ZERO;
        Map<String, BigDecimal> ordersPaymentMoney = calculateOrdersPaymentMoney(context, Args.CalculateOrdersPaymentMoneyArg.builder().ids(orderIds).dnr(true).status("under_review").build());

        if (MapUtils.isEmpty(ordersPaymentMoney)) {
            return CalculateOrdersPayment.Result.builder()
                    .value(receivableAmountAccount.subtract(ordersPaymentMoneyAccount).toString())
                    .build();
        }
        for (BigDecimal value : ordersPaymentMoney.values()) {
            ordersPaymentMoneyAccount = ordersPaymentMoneyAccount.add(value);
        }

        return CalculateOrdersPayment.Result.builder()
                .value(receivableAmountAccount.subtract(ordersPaymentMoneyAccount).toString())
                .build();
    }

    private String appendSalesOrderdSql(String tenantId, String account_id, Boolean isContainPrepay, Boolean isContainCredit) {
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT  id,receivable_amount\n" +
                "FROM biz_sales_order WHERE tenant_id = '" + tenantId
                + "' and account_id = '" + account_id
                + "' and is_deleted = '0' and order_status in ('6','7','10','11') ");
        if (Objects.equals(isContainPrepay, isContainCredit)) {
            sb.append(" and settle_type in ('1','3')");
        }
        if (isContainPrepay && !isContainCredit) {
            sb.append(" and settle_type ='1'");
        }
        if (!isContainPrepay && isContainCredit) {
            sb.append(" and settle_type ='3'");
        }
        return sb.toString();
    }

    private String appendSql(List<String> ids, Boolean isDnr, String status) {
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT  order_id,SUM(payment_amount)\n" +
                "FROM payment_order WHERE order_id in ('");
        sb.append(Joiner.on("','").join(ids));
        sb.append("')");
        if (StringUtils.isNotEmpty(status)) {
            sb.append(" and life_status = " + "'" + status + "'");
        }
        if (isDnr) {
            sb.append(" and payment_term in('");
            sb.append(Joiner.on("','").join(Lists.newArrayList(
                    CustomerPaymentObj.PAYMENT_METHOD_DNR,
                    CustomerPaymentObj.PAYMENT_METHOD_DEPOSIT,
                    CustomerPaymentObj.PAYMENT_METHOD_REBATE
            )));
            sb.append("')");

        }
        sb.append(" GROUP BY order_id");
        return sb.toString();
    }

    private OrderPaymentCostByOrderPaymentIdModel.Result getOrderPaymentCostByOrderPaymentId(String orderPaymentId, ControllerContext context) {
        OrderPaymentCostByOrderPaymentIdModel.Arg arg = OrderPaymentCostByOrderPaymentIdModel.Arg.builder().orderPaymentId(orderPaymentId).build();
        OrderPaymentCostByOrderPaymentIdModel.Result result = customerAccountForPaymentProxy.getOrderPaymentCostByOrderPaymentId(arg, SFAHeaderUtil.getHeaders(context.getUser()));
        return result;
    }

    public List<ObjectDataDocument> parseOrderPaymentCost(List<ObjectDataDocument> list, ControllerContext context) {
        for (ObjectDataDocument dataDocument : list) {
            String method = (String) dataDocument.get(CustomerPaymentObj.FIELD_PAYMENT_METHOD);
            String orderPaymentId = (String) dataDocument.get(DBRecord.ID);
            if (StringUtils.isNotBlank(method) && StringUtils.isNotBlank(orderPaymentId)
                    && CustomerPaymentObj.PAYMENT_METHOD_DNR_LABEL.equals(method)) {
                OrderPaymentCostByOrderPaymentIdModel.Result cost = null;
                try {
                    cost = this.getOrderPaymentCostByOrderPaymentId(orderPaymentId, context);
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                }
                if (cost != null) {
                    dataDocument.put(OrderPaymentObj.PREPAY, cost.getResult().getPrepayAmount());
                    dataDocument.put(OrderPaymentObj.REBATE_OUTCOME, cost.getResult().getRebateOutcomeAmount());
                }
            }
        }
        return list;
    }
}
