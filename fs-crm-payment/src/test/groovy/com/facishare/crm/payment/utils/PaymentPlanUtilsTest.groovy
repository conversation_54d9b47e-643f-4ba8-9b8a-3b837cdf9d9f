package com.facishare.crm.payment.utils

import com.facishare.crm.payment.constant.PaymentPlanObj
import spock.lang.Specification

class PaymentPlanUtilsTest extends Specification {

    def "test getPaymentPlanStatus"() {
        expect:
        PaymentPlanUtils.getPaymentPlanStatus(new BigDecimal(planPaymentAmount), new BigDecimal(realPaymentAmount), planPaymentTime) == result

        where:
        planPaymentAmount | realPaymentAmount | planPaymentTime | result
        "100"            | "100"             | System.currentTimeMillis() | PaymentPlanObj.PlanPaymentStatus.COMPLETED.getName()
        "100"            | "50"              | System.currentTimeMillis() | PaymentPlanObj.PlanPaymentStatus.INCOMPLETE.getName()
        "100"            | "50"              | System.currentTimeMillis() - 24 * 60 * 60 * 1000 | PaymentPlanObj.PlanPaymentStatus.OVERDUE.getName()
    }
}
