package com.facishare.crm.prm.access;

import com.facishare.crm.enums.TimeUnitEnums;
import com.facishare.crm.platform.time.TimeComputeService;
import com.facishare.crm.prm.constant.PartnerMembershipStatisticsConstant;
import com.facishare.crm.prm.enums.MemberTypeEnums;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2024-12-09
 * ============================================================
 */
@Service
@Slf4j
public class MembershipStatisticsAccess {
    @Resource
    private TimeComputeService timeComputeService;


    @Resource
    private MetaDataFindServiceExt metaDataFindServiceExt;

    public List<IObjectData> queryCorporateMembershipStatistics(User user, String partnerId, String loyaltyMemberId) {
        String currentPeriodKey = timeComputeService.getCurrentPeriodKey(TimeUnitEnums.MONTH);
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), PartnerMembershipStatisticsConstant.PARTNER_ID, partnerId);
        SearchUtil.fillFilterEq(query.getFilters(), PartnerMembershipStatisticsConstant.LOYALTY_MEMBER_ID, loyaltyMemberId);
        SearchUtil.fillFilterEq(query.getFilters(), PartnerMembershipStatisticsConstant.MEMBER_TYPE, MemberTypeEnums.CORPORATE.getCode());
        SearchUtil.fillFilterEq(query.getFilters(), PartnerMembershipStatisticsConstant.PERIOD_TYPE, TimeUnitEnums.MONTH.getUnit());
        SearchUtil.fillFilterEq(query.getFilters(), PartnerMembershipStatisticsConstant.OUT_ENTERPRISE_ID, user.getOutTenantId());
        SearchUtil.fillFilterEq(query.getFilters(), PartnerMembershipStatisticsConstant.PERIOD_KEY, currentPeriodKey);
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, PartnerMembershipStatisticsConstant.PARTNER_MEMBERSHIP_STATISTICS_OBJ, query).getData();
    }

    public List<IObjectData> queryIndividualMembershipStatistics(User user, String partnerId, String loyaltyMemberId) {
        String currentPeriodKey = timeComputeService.getCurrentPeriodKey(TimeUnitEnums.MONTH);
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), PartnerMembershipStatisticsConstant.PARTNER_ID, partnerId);
        SearchUtil.fillFilterEq(query.getFilters(), PartnerMembershipStatisticsConstant.LOYALTY_MEMBER_ID, loyaltyMemberId);
        SearchUtil.fillFilterEq(query.getFilters(), PartnerMembershipStatisticsConstant.MEMBER_TYPE, MemberTypeEnums.INDIVIDUAL.getCode());
        SearchUtil.fillFilterEq(query.getFilters(), PartnerMembershipStatisticsConstant.PERIOD_TYPE, TimeUnitEnums.MONTH.getUnit());
        SearchUtil.fillFilterEq(query.getFilters(), PartnerMembershipStatisticsConstant.OUT_ENTERPRISE_ID, user.getOutTenantId());
        SearchUtil.fillFilterEq(query.getFilters(), PartnerMembershipStatisticsConstant.OUT_USER_ID, user.getOutUserId());
        SearchUtil.fillFilterEq(query.getFilters(), PartnerMembershipStatisticsConstant.PERIOD_KEY, currentPeriodKey);
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, PartnerMembershipStatisticsConstant.PARTNER_MEMBERSHIP_STATISTICS_OBJ, query).getData();
    }
}
