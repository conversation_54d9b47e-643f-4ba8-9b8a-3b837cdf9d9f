package com.facishare.crm.prm.action;

import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel;
import com.facishare.crm.sfa.prm.api.channel.ChannelDataChangeService;
import com.facishare.crm.sfa.service.ChannelService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import static com.facishare.crm.prm.constant.PrmI18nConstant.PRM_AGREEMENT_DETAIL_DATA_UN_ABLE_RENEWAL;
import static com.facishare.crm.prm.constant.PrmI18nConstant.PRM_DO_NOT_FIND_PARTNER_DATA_BY_AGREEMENT_DETAIL;

/**
 * Created by Sundy on 2024/10/12 19:39
 */
@Slf4j
public class PrmPartnerAgreementDetailEditAction extends StandardEditAction {
    private static final MetaDataFindServiceExt metaDataFindServiceExt = SpringUtil.getContext().getBean(MetaDataFindServiceExt.class);
    private final ChannelService channelService = SpringUtil.getContext().getBean("channelServiceProvider", ChannelService.class);
    private final ChannelDataChangeService channelDataChangeService = SpringUtil.getContext().getBean("channelDataChangeServiceImpl", ChannelDataChangeService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        Boolean ableRenewal = ObjectDataUtils.getValue(arg.getObjectData(), PartnerAgreementDetailModel.ABLE_RENEWAL, Boolean.class, Boolean.FALSE);
        if (!ableRenewal) {
            throw new ValidateException(I18N.text(PRM_AGREEMENT_DETAIL_DATA_UN_ABLE_RENEWAL));
        }
        String admissionObject = channelService.fetchChannelAdmissionObject(actionContext.getUser());
        String belongDataId = channelService.getAdmissionObjectDataId(objectData, admissionObject);
        IObjectData admissionData = metaDataFindServiceExt.findObjectByIdIgnoreAll(actionContext.getUser(), belongDataId, admissionObject);
        if (admissionData == null) {
            throw new ValidateException(I18N.text(PRM_DO_NOT_FIND_PARTNER_DATA_BY_AGREEMENT_DETAIL));
        }
    }
}
