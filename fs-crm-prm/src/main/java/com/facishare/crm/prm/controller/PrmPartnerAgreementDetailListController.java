package com.facishare.crm.prm.controller;

import com.facishare.crm.sfa.predefine.controller.PartnerAgreementDetailListController;
import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-20
 * ============================================================
 */
public class PrmPartnerAgreementDetailListController extends PartnerAgreementDetailListController {
    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        SearchTemplateQuery query = super.customSearchTemplate(searchQuery);
        SearchUtil.fillFilterEq(query.getFilters(), "sign_status", SignStatus.PENDING_RENEWAL.getStatus());
        return query;
    }

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), "sign_status", SignStatus.PENDING_RENEWAL.getStatus());
        return query;
    }
}
