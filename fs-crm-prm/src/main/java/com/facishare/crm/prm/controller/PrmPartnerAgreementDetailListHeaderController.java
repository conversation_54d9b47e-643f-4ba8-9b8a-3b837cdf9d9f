package com.facishare.crm.prm.controller;

import com.facishare.crm.sfa.predefine.controller.PartnerAgreementDetailListHeaderController;
import com.facishare.crm.sfa.predefine.service.LayoutButtonService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * Created by Sundy on 2024/11/13 17:42
 */
public class PrmPartnerAgreementDetailListHeaderController extends PartnerAgreementDetailListHeaderController {
    private final LayoutButtonService layoutButtonService = SpringUtil.getContext().getBean(LayoutButtonService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        layoutButtonService.removeListPageButton(result, ObjectAction.INVALID.getButtonApiName());
        return after;

    }
}
