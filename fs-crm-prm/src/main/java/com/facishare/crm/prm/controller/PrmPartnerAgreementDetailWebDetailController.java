package com.facishare.crm.prm.controller;

import com.facishare.crm.sfa.predefine.controller.PartnerAgreementDetailWebDetailController;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.google.common.collect.Lists;

/**
 * Created by Sundy on 2024/10/15 19:51
 */
public class PrmPartnerAgreementDetailWebDetailController extends PartnerAgreementDetailWebDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        WebDetailLayout.of(result.getLayout().toLayout()).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.INVALID.getActionCode()));
        WebDetailLayout.of(result.getLayout().toLayout()).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.CLONE.getActionCode()));
        return after;
    }
}
