package com.facishare.crm.project.manage.action;

import com.facishare.crm.project.manage.utils.CommonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAsyncBulkAction;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkCreateAction;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class TimeSheetAsyncBulkAddAction extends AbstractStandardAsyncBulkAction<StandardBulkCreateAction.Arg, BaseObjectSaveAction.Arg> {

    @Override
    protected void before(StandardBulkCreateAction.Arg arg) {
		CommonUtils.validateSize(arg.getDataList(), 50);
		CommonUtils.batchFillDataId(arg.getDataList());
        super.before(arg);
    }

    @Override
    protected String getDataIdByParam(BaseObjectSaveAction.Arg param) {
        return param.getObjectData().getId();
    }

    @Override
    protected List<BaseObjectSaveAction.Arg> getButtonParams() {
        return arg.getDataList().stream()
                .map(data -> {
                    BaseObjectSaveAction.Arg addArg = new BaseObjectSaveAction.Arg();
                    addArg.setObjectData(data);
                    return addArg;
                })
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.CREATE.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.CREATE.getActionCode();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.CREATE.getActionCode());
    }






}