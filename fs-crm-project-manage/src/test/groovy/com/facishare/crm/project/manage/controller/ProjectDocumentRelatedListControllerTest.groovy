package com.facishare.crm.project.manage.controller

import com.facishare.crm.project.manage.BaseProjectManageTest
import com.facishare.crm.project.manage.constant.TaskObj
import com.facishare.crm.project.manage.controller.ProjectDocumentRelatedListController
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.metadata.ActionContextExt
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.action.ActionContext
import com.facishare.paas.metadata.api.action.IActionContext
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.reflect.Whitebox

import static org.mockito.ArgumentMatchers.*

@PrepareForTest([ProjectDocumentRelatedListController, ObjectDataExt, ActionContextExt, RequestContextManager])
@SuppressStaticInitializationFor([
        "com.facishare.paas.appframework.core.util.UdobjGrayConfig",
        "com.facishare.paas.appframework.core.util.RequestUtil",
        "com.facishare.paas.metadata.api.action.ActionContext"
])
class ProjectDocumentRelatedListControllerTest extends BaseProjectManageTest {
    def "findData"() {
        given:
        def tester = new ProjectDocumentRelatedListController()
        PowerMockito.when(serviceFacade.findBySearchTemplateQueryWithFields(any() as IActionContext, anyString(), any() as SearchTemplateQuery, anyList())).thenReturn(new QueryResult<IObjectData>(data: [new ObjectData(["_id": "t1"])]))
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as User, any() as IObjectDescribe, anyString(), any() as SearchTemplateQuery, anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(new QueryResult<IObjectData>(data: []))
        PowerMockito.mockStatic(ObjectDataExt)
        PowerMockito.doNothing().when(ObjectDataExt, "correctValue", any() as User, anyList(), any() as ObjectDescribeExt)
        PowerMockito.mockStatic(UdobjGrayConfig)
        PowerMockito.when(UdobjGrayConfig.isAllow(anyString(), anyString())).thenReturn(false)
        PowerMockito.mockStatic(RequestContextManager)
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(requestContext)

        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName("ProjectDocumentObj")

        and:
        def arg = new StandardRelatedListController.Arg(relatedListComponent: ["api_name": "structured_project_document"])
        Whitebox.setInternalState(tester, "arg", arg)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(tester, "controllerContext", controllerContext)
        Whitebox.setInternalState(tester, "objectDescribe", ObjectDescribeExt.of(describe))

        def query = new SearchTemplateQuery()
        SearchUtil.fillFilterEq(query.getFilters(), TaskObj.FIELD_PROJECT_STAGE_ID, "ps1")


        when:
        def result = tester.findData(query)

        then:
        result.getData().isEmpty()
    }
}
