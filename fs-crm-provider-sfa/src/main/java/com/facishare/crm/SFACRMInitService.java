package com.facishare.crm;

import com.facishare.crm.loyalty.LoyaltyPredefineObject;
import com.facishare.crm.sfa.activity.predefine.ActivityPredefineObject;
import com.facishare.crm.management.SFAManagementPredefineObject;
import com.facishare.crm.payment.PaymentObject;
import com.facishare.crm.prm.PrmPreDefineObject;
import com.facishare.crm.project.manage.ProjectManagePredefineObject;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.handler.salesorder.freezeadjustment.FreezeInventoryAdjustmentActionHandler;
import com.facishare.crm.sfa.predefine.handler.bomcore.bomcoreconfigocr.BomCoreBomCoreConfigOcrActionHandler;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.handler.HandlerResultTypeMappings;
import org.springframework.context.support.ApplicationObjectSupport;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * 初始化服务
 * <p>
 * Created by liyiguang on 2017/7/12.
 */
@Service
public class SFACRMInitService extends ApplicationObjectSupport {

    @PostConstruct
    public void init() {
        SFAPreDefineObject.init();

        PaymentObject.init();
        PrmPreDefineObject.init();

        //SFA后台管理
        SFAManagementPredefineObject.init();

        ProjectManagePredefineObject.init();

        LoyaltyPredefineObject.init();
        ActivityPredefineObject.init();

        HandlerResultTypeMappings.registerHandlerResultType(ObjectAction.FREEZE_INVENTORY_ADJUSTMENT.getActionCode(), FreezeInventoryAdjustmentActionHandler.Result.class);
        HandlerResultTypeMappings.registerHandlerResultType(ObjectAction.FREEZE_INVENTORY_BATCH_ADJUSTMENT.getActionCode(), FreezeInventoryAdjustmentActionHandler.Result.class);
        HandlerResultTypeMappings.registerHandlerResultType(ObjectAction.BOM_CORE_CONFIG_OCR.getActionCode(), BomCoreBomCoreConfigOcrActionHandler.Result.class);
    }
}