<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c" xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.1.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <import resource="classpath:spring/fs-crm-common.xml"/>
    <import resource="classpath:spring/fs-crm-management-sfa.xml"/>

    <import resource="classpath:spring/fs-crm-master-data.xml"/>
    <import resource="classpath:spring/payment_customer.xml"/>
    <import resource="classpath:spring/prm.xml"/>
    <import resource="classpath:spring/pm.xml"/>
    <import resource="classpath:spring/loyalty.xml"/>
    <import resource="classpath:spring/activity.xml"/>
    <import resource="classpath:spring/domain_sfa.xml"/>
    <import resource="classpath:spring/fs-crm-sales.xml"/>
    <context:component-scan base-package="com.facishare.crm"/>
    <context:annotation-config/>
</beans>
