<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-crm-sfa</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.5.5-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <artifactId>fs-crm-sales</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-enterpriserelation-rest-api2</artifactId>
            <version>2.1.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-paas-ext</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-enterpriserelation-rest-api2</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mockito-core</artifactId>
                    <groupId>org.mockito</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-sfa-lto</artifactId>
            <version>9.5.5-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.athaydes</groupId>
                    <artifactId>spock-reports</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-enterpriserelation-rest-api2</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-metadata-restdriver</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-fcp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-privilege-temp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-common-mq</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.facishare.open</groupId>-->
<!--            <artifactId>fs-eservice-rest-api</artifactId>-->
<!--            <version>9.1.0-SNAPSHOT</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>io.grpc</groupId>-->
<!--                    <artifactId>grpc-core</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-enterprise-id-account-converter</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-wechat-proxy-core-api</artifactId>
            <version>0.0.8-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.thoughtworks.xstream</groupId>
                    <artifactId>xstream</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-servicelib-fsi</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>

            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-servicelib-fsi</artifactId>
        </dependency>

        <!--灰度接入包-->
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>gray-release</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.promeg</groupId>
            <artifactId>tinypinyin</artifactId>
            <version>2.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-coordination</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${mockito.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-wechat-dubbo-rest-outer-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongo-java-driver</artifactId>
            <version>3.5.0</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-plat-privilege-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.bi.industry</groupId>
            <artifactId>fs-bi-industry-api</artifactId>
            <version>1.1.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.marketing</groupId>
            <artifactId>fs-marketing-outapi</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-paas-ai-api</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-sfa-expression-provider</artifactId>
            <version>9.5.5-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-metadata-provider</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-paas-expression</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>fs-sql2esdsl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-sfa-equity-es-support</artifactId>
            <version>8.7.5-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-metadata-provider</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-sfa-audit-log</artifactId>
            <version>1.1.1-SNAPSHOT</version>
        </dependency>
    </dependencies>
</project>