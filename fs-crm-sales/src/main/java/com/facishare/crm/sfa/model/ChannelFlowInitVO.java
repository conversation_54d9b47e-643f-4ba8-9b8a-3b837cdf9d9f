package com.facishare.crm.sfa.model;

import com.beust.jcommander.internal.Sets;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fxiaoke.functions.utils.Maps;
import com.github.autoconf.ConfigFactory;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-05
 * ============================================================
 */
@Data
@Slf4j
public class ChannelFlowInitVO {
    public static final String REGISTER = "register";
    public static final String SIGN = "sign";
    public static final String RENEWAL_SIGN = "renewal_sign";
    public static final String UNKNOWN = "unKnown";

    private static Map<String, List<ScenesWorkFlow>> channelFlowInitMapping;

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-prm-config",
                config -> {
                    String channelFlowConfigJson = config.get("channel_flow_config");
                    ObjectMapper objectMapper = new ObjectMapper();
                    // 反序列化为 Map<String, List<ScenesWorkFlow>>
                    try {
                        channelFlowInitMapping = objectMapper.readValue(
                                channelFlowConfigJson, new TypeReference<Map<String, List<ScenesWorkFlow>>>() {
                                }
                        );
                    } catch (Exception e) {
                        log.error("channelFlowConfigJson反序列化失败");
                        channelFlowInitMapping = Maps.newHashMap();
                    }
                });
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    static class ChannelFlowInit {
        private String objectApiName;
        private List<ScenesWorkFlow> scenesWorkFlows;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    static class ScenesWorkFlow {
        private String workFlowId;
        private String scene;
    }

    public static Set<String> getNeedInitWorkFlowIdsByObjectApiName(String objectApiName) {
        if (channelFlowInitMapping == null || StringUtils.isBlank(objectApiName)) {
            return Sets.newHashSet();
        }
        List<ScenesWorkFlow> scenesWorkFlows = channelFlowInitMapping.get(objectApiName);
        if (CollectionUtils.isEmpty(scenesWorkFlows)) {
            return Sets.newHashSet();
        }
        return scenesWorkFlows.stream().map(ScenesWorkFlow::getWorkFlowId).collect(Collectors.toSet());
    }

    public static Set<String> getNeedInitWorkFlowIds() {
        if (channelFlowInitMapping == null) {
            return Sets.newHashSet();
        }
        List<ScenesWorkFlow> ScenesWorkFlows = channelFlowInitMapping.values().stream()
                .flatMap(List::stream)
                .filter(f -> StringUtils.isNoneBlank(f.getWorkFlowId(), f.getScene()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ScenesWorkFlows)) {
            return Sets.newHashSet();
        }
        return ScenesWorkFlows.stream().map(ScenesWorkFlow::getWorkFlowId).collect(Collectors.toSet());
    }

    public static String getWorkFlowIdByScene(String objectApiName, String scenes) {
        if (channelFlowInitMapping == null || StringUtils.isAnyBlank(objectApiName, scenes)) {
            return UNKNOWN;
        }
        List<ScenesWorkFlow> scenesWorkFlows = channelFlowInitMapping.get(objectApiName);
        if (CollectionUtils.isEmpty(scenesWorkFlows)) {
            return UNKNOWN;
        }
        return scenesWorkFlows.stream().filter(scenesWorkFlow -> scenes.equals(scenesWorkFlow.getScene()))
                .findFirst().map(ScenesWorkFlow::getWorkFlowId).orElse(UNKNOWN);
    }

    public static String getSceneByFlowId(String objectApiName, String workFlowId) {
        if (channelFlowInitMapping == null || StringUtils.isAnyBlank(objectApiName, workFlowId)) {
            return UNKNOWN;
        }
        List<ScenesWorkFlow> scenesWorkFlows = channelFlowInitMapping.get(objectApiName);
        if (CollectionUtils.isEmpty(scenesWorkFlows)) {
            return UNKNOWN;
        }
        return scenesWorkFlows.stream().filter(scenesWorkFlow -> workFlowId.equals(scenesWorkFlow.getWorkFlowId()))
                .findFirst().map(ScenesWorkFlow::getScene).orElse(UNKNOWN);
    }
}
