package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.util.Safes;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;
import java.util.Map;

public abstract class AbstractSimpleStandardAction extends AbstractStandardAction<AbstractSimpleStandardAction.Arg, AbstractSimpleStandardAction.Result> {

    CRMNotificationService crmNotificationService = SpringUtil.getContext().getBean(CRMNotificationService.class);

    protected IObjectData objectData;

    @Override
    protected void init() {
        super.init();
        objectData = Safes.first(dataList);
    }

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }

    @Override
    protected List<String> getDataPrivilegeIds(AbstractSimpleStandardAction.Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }


    @Override
    protected String getButtonApiName() {
        return getButton().getButtonApiName();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(getButton().getActionCode());
    }

    @Override
    protected Map<String, Object> getArgs() {
        return arg.getArgs();
    }

    protected abstract ObjectAction getButton();

    @Data
    public static class Arg {
        private String objectDataId;
        private JSONObject args;

        public JSONObject getArgs() {
            return args == null ? new JSONObject() : args;
        }
    }

    @Data
    public static class Result {
        private boolean success;
        private String message;

        public Result() {
            this.success = true;
        }

        public Result(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
    }
}
