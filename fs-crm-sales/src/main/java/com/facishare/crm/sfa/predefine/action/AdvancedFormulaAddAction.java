package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.model.QuoterModel;
import com.facishare.crm.sfa.predefine.service.AdvancedFormulaService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

@Slf4j
public class AdvancedFormulaAddAction extends StandardAddAction {
    private final AdvancedFormulaService advancedFormulaService = SpringUtil.getContext().getBean(AdvancedFormulaService.class);
    private static final String FORMULA = "formula";
    private static final String REF_OBJECT_API_NAME = "ref_object_api_name";
    private static final String REF_FIELD_NAME = "ref_field_name";

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        check();
    }

    private void check(){
        advancedFormulaService.checkRepeat(objectData,actionContext.getUser());
        checkField();
    }

    private void checkField() {
        IObjectDescribe objectDescribe = serviceFacade.findObject(actionContext.getTenantId(), objectData.get(REF_OBJECT_API_NAME, String.class));
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(objectData.get(REF_FIELD_NAME, String.class));
        if (!StringUtils.equalsAny(fieldDescribe.getType(), IFieldType.NUMBER, IFieldType.CURRENCY)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ADD_ADVANCE_FORMULA_FIELD_RULE_WARN));
        }
        fieldDescribe.setDefaultValue(objectData.get(FORMULA, String.class));
        fieldDescribe.setDefaultIsExpression(true);
        String productId = objectData.get(QuoterModel.AdvancedFormulaModel.PRODUCT_ID, String.class);
        String bomId = objectData.get(QuoterModel.AdvancedFormulaModel.BOM_ID, String.class);
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(2000);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, REF_OBJECT_API_NAME, objectData.get(REF_OBJECT_API_NAME, String.class));
        if (StringUtils.isNotBlank(bomId)) {
            SearchUtil.fillFilterEq(filters, QuoterModel.AdvancedFormulaModel.BOM_ID, bomId);
        } else {
            if (StringUtils.isNotBlank(productId)) {
                SearchUtil.fillFilterEq(filters, QuoterModel.AdvancedFormulaModel.PRODUCT_ID, productId);
                SearchUtil.fillFilterIsNull(filters, QuoterModel.AdvancedFormulaModel.BOM_ID);
            }else {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MISS_REQUIRED_PARAMETERS));
            }
        }
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, "0");
        query.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(actionContext.getUser(), "AdvancedFormulaObj", query);
        if (Objects.nonNull(queryResult) && CollectionUtils.notEmpty(queryResult.getData())) {
            advancedFormulaService.checkFieldFormula(objectDescribe,objectData,queryResult.getData(),actionContext.getUser());
            queryResult.getData().forEach(x -> {
                IFieldDescribe field = objectDescribe.getFieldDescribe(x.get(REF_FIELD_NAME, String.class));
                if (field != null) {
                    field.setDefaultValue(x.get(FORMULA, String.class));
                    field.setDefaultIsExpression(true);
                    objectDescribe.updateFieldDescribe(field);
                }
            });
        }
        advancedFormulaService.validateByField(objectDescribe, fieldDescribe);
    }

}
