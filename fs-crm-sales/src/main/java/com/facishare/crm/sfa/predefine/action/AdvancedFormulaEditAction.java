package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.model.QuoterModel;
import com.facishare.crm.sfa.predefine.service.AdvancedFormulaService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

public class AdvancedFormulaEditAction extends StandardEditAction {
    private final AdvancedFormulaService advancedFormulaService = SpringUtil.getContext().getBean(AdvancedFormulaService.class);
    private static final String FORMULA = "formula";
    private static final String REF_OBJECT_API_NAME = "ref_object_api_name";
    private static final String REF_FIELD_NAME = "ref_field_name";

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        check();
    }

    private void check(){
        advancedFormulaService.checkRepeat(objectData,actionContext.getUser());
        String productId = objectData.get(QuoterModel.AdvancedFormulaModel.PRODUCT_ID, String.class);
        String bomId = objectData.get(QuoterModel.AdvancedFormulaModel.BOM_ID, String.class);
        if (StringUtils.isAllBlank(productId, bomId)) {
            throw new ValidateException(I18N.text(I18NKey.ERRORCODE_PARAM_WRONG));
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(2000);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, REF_OBJECT_API_NAME, objectData.get(REF_OBJECT_API_NAME, String.class));
        if (StringUtils.isNotBlank(bomId)) {
            SearchUtil.fillFilterEq(filters, QuoterModel.AdvancedFormulaModel.BOM_ID, bomId);
        } else {
            if (StringUtils.isNotBlank(productId)) {
                SearchUtil.fillFilterEq(filters, QuoterModel.AdvancedFormulaModel.PRODUCT_ID, productId);
                SearchUtil.fillFilterIsNull(filters, QuoterModel.AdvancedFormulaModel.BOM_ID);
            }else {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MISS_REQUIRED_PARAMETERS));
            }
        }
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, "0");
        query.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(actionContext.getUser(), "AdvancedFormulaObj", query);
        IObjectDescribe refObjectDescribe = serviceFacade.findObject(actionContext.getTenantId(), objectData.get(REF_OBJECT_API_NAME, String.class));
        if (Objects.nonNull(queryResult) && CollectionUtils.notEmpty(queryResult.getData())) {
            advancedFormulaService.checkFieldFormula(objectDescribe,objectData,queryResult.getData(),actionContext.getUser());
            queryResult.getData().forEach(x -> {
                IFieldDescribe field = refObjectDescribe.getFieldDescribe(x.get(REF_FIELD_NAME, String.class));
                String formula = x.get(FORMULA, String.class);
                if (Objects.nonNull(field)&&StringUtils.isNotBlank(formula)) {
                    JSONObject formulaObj = JSON.parseObject(formula);
                    field.setDefaultIsExpression(true);
                    field.setDefaultValue(formulaObj.getString("expression"));
                    refObjectDescribe.updateFieldDescribe(field);
                }
            });
        }
        IFieldDescribe fieldDescribe = refObjectDescribe.getFieldDescribe(objectData.get(REF_FIELD_NAME, String.class));
        String formula = objectData.get(FORMULA, String.class);
        if (fieldDescribe != null&&StringUtils.isNotBlank(formula)) {
            fieldDescribe.setDefaultValue(formula);
            fieldDescribe.setDefaultIsExpression(true);
            //refObjectDescribe.removeFieldDescribe(fieldDescribe.getApiName());
            advancedFormulaService.validateByField(refObjectDescribe, fieldDescribe);
        }
    }
}