package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.model.BomConstraintLineModel;
import com.facishare.crm.sfa.predefine.service.cpq.BomConstraintService;
import com.facishare.crm.sfa.utilities.constant.BomConstraintConstants;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @instruction
 */
public class BomAttributeConstraintEditAction extends StandardEditAction {
    private final BomConstraintService bomConstraintService = SpringUtil.getContext().getBean(BomConstraintService.class);
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        checkRule();
        stopWatch.lap("checkRule");
    }

    //校验约束关系
    private void checkRule(){
        List<IObjectData> dataList = Lists.newArrayList(detailsToUpdate);
        dataList.addAll(detailsToAdd);
        bomConstraintService.checkRule(dataList, actionContext.getUser(), objectData);
        //编辑时，需要对表达式中带有单引号的转义处理，否则会报错
        if(CollectionUtils.isNotEmpty(dataList)) {
            dataList.stream().forEach(this::handleExpressionSpecialChar);
        }
    }

    /**
     * 特殊字符存储时转义处理
     * @param data
     */
    private void handleExpressionSpecialChar(IObjectData data) {
        if(data == null || StringUtils.isBlank(data.get(BomConstraintConstants.RESULT_RANGE, String.class))) {
            return;
        }
        List<BomConstraintLineModel> tempRangeResultList = JSON.parseArray(data.get(BomConstraintConstants.RESULT_RANGE, String.class), BomConstraintLineModel.class);
        for(BomConstraintLineModel line : tempRangeResultList) {
            if(CollectionUtils.isEmpty(line.getFormula())) {
                continue;
            }
            for (BomConstraintLineModel.FormulaInfo formula : line.getFormula()) {
                if(StringUtils.isNotBlank(formula.getExpression())) {
                    formula.setExpression(formula.getExpression().replaceAll("'", "''"));
                }
                if(StringUtils.isNotBlank(formula.getExpression_label())) {
                    formula.setExpression_label(formula.getExpression_label().replaceAll("'", "''"));
                }
            }
        }
        data.set(BomConstraintConstants.RESULT_RANGE, tempRangeResultList);
    }

}
