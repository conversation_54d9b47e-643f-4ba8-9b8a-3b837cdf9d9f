package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.treepath.impl.TreePathService;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021/11/29 16:05
 */
@Slf4j
public class KnowledgeClassAddAction extends StandardAddAction {
    private final TreePathService treePathService = SpringUtil.getContext().getBean(TreePathService.class);

    @Override
    protected void setDefaultSystemInfo(IObjectData objectData) {
        super.setDefaultSystemInfo(objectData);
        treePathService.initDataPath(actionContext.getUser(), objectData
                , SFAPreDefineObject.KnowledgeClass.getApiName(), "tree_path", "parent_id");
    }

}
