package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.ProductCategoryBizService;
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel;
import com.facishare.crm.sfa.predefine.service.objectbo.ProductCategoryBO;
import com.facishare.crm.sfa.predefine.service.resource.ProductCategoryServiceResource;
import com.facishare.crm.sfa.predefine.service.treepath.impl.TreePathService;
import com.facishare.crm.sfa.utilities.util.ProductCategoryUtils;
import com.facishare.crm.sfa.utilities.validator.ProductCategoryV2Validator;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Filed.PRODUCT_CATEGORY_PATH;
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Metadata.API_NAME;
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject.ORDER_FIELD;

/**
 * <AUTHOR>
 * @date 2021/12/1 16:56
 */
@Slf4j
public class KnowledgeClassEditAction extends StandardEditAction {
    private final TreePathService treePathService = SpringUtil.getContext().getBean(TreePathService.class);
    private boolean isNeedChangePath = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        IObjectData oldData = dataList.get(0);
        IObjectData newData = arg.getObjectData().toObjectData();
        isNeedChangePath = checkNeedChangePath(oldData, newData);
    }

    private boolean checkNeedChangePath(IObjectData oldData, IObjectData newData) {
        String newPid = ObjectDataUtils.getValueOrDefault(newData, ProductCategoryModel.Filed.PID, "");
        String oldPid = ObjectDataUtils.getValueOrDefault(oldData, ProductCategoryModel.Filed.PID, "");
        return !oldPid.equals(newPid);
    }


    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (isNeedChangePath) {
            String pid = ObjectDataUtils.getValueOrDefault(arg.getObjectData().toObjectData(), "parent_id", "");
            String id = objectData.getId();
            treePathService.changePathByParentId(actionContext.getUser(), id, pid, SFAPreDefineObject.KnowledgeClass.getApiName(), "tree_path");
        }

        return newResult;
    }
}
