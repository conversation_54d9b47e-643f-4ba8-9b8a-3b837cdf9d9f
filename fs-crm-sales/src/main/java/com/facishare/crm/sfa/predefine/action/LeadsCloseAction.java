package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.model.Enum.LeadsStatusEnum;
import com.facishare.crm.sfa.model.Enum.SessionBOCItemKeys;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.ObjectPoolService;
import com.facishare.crm.sfa.predefine.service.QiXinTodoService;
import com.facishare.crm.sfa.predefine.service.SFALogService;
import com.facishare.crm.sfa.predefine.service.model.SFALogModels;
import com.facishare.crm.sfa.predefine.service.task.LeadsOverTimeTaskService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.LeadsConstants;
import com.facishare.crm.sfa.utilities.proxy.PaasWorkFlowProxy;
import com.facishare.crm.sfa.utilities.proxy.model.FeedsModel;
import com.facishare.crm.sfa.utilities.proxy.model.PaasWorkFlowModel;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.predef.action.BaseObjectApprovalAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import joptsimple.internal.Strings;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

@Slf4j
public class LeadsCloseAction extends BaseObjectApprovalAction<LeadsCloseAction.Arg, LeadsCloseAction.Result> {
    private final LeadsOverTimeTaskService leadsOverTimeTaskService = SpringUtil.getContext().getBean(LeadsOverTimeTaskService.class);
    private final QiXinTodoService qiXinTodoService = SpringUtil.getContext().getBean(QiXinTodoService.class);
    private final ObjectPoolService objectPoolService = SpringUtil.getContext().getBean(ObjectPoolService.class);
    private final SFALogService sfaLogService = SpringUtil.getContext().getBean(SFALogService.class);
    private static final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private static final PaasWorkFlowProxy passWorkFlowProxy = SpringUtil.getContext().getBean(PaasWorkFlowProxy.class);

    protected List<IObjectData> dbObjectDataList;
    private boolean sendFeeds = true;
    private final List<String> systemButtonParamFormApiNameList = Lists.newArrayList("form_completed_result", "form_send_event", "form_close_reason", "form_close_reason__o");
    private static final String leadsI18nKey = GetI18nKeyUtil.getDescribeDisplayNameKey(SFAPreDefineObject.Leads.getApiName());
    private static final String leadsPoolI18nKey = GetI18nKeyUtil.getDescribeDisplayNameKey("LeadsPoolObj");

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.CLOSE.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getId());
    }

    @Override
    protected void doFunPrivilegeCheck() {
        if (arg.isSkipFunctionCheck()) {
            return;
        }
        super.doFunPrivilegeCheck();
    }

    @Override
    protected void doDataPrivilegeCheck() {
        if (arg.isSkipFunctionCheck()) {
            return;
        }
        super.doDataPrivilegeCheck();
    }

    @Override
    protected void before(LeadsCloseAction.Arg arg) {
        if(RequestUtil.isMobileRequest() && RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_710)) {
            sendFeeds = false;
            Map<String, String> headers = Maps.newHashMap();
            headers.put("Content-Type", "application/json");
            headers.put("x-tenant-id", actionContext.getTenantId());
            headers.put("x-user-id", actionContext.getUser().getUpstreamOwnerIdOrUserId());

            PaasWorkFlowModel.CheckWorkFlowExistArg checkWorkFlowExistArg = PaasWorkFlowModel.CheckWorkFlowExistArg.builder()
                    .entityId(actionContext.getObjectApiName()).triggerType(ApprovalFlowTriggerType.CLOSE.getTriggerTypeCode()).build();
            PaasWorkFlowModel.CheckWorkFlowExistResult rstResult = passWorkFlowProxy.checkWorkFlowExist(headers, checkWorkFlowExistArg);
            if(rstResult != null && rstResult.isData()) {
                throw new SFABusinessException(SFAErrorCode.CLIENT_UPGRADE_PROMPT);
            }
        }

        if(StringUtils.isBlank(arg.getId()) && CollectionUtils.notEmpty(arg.getDataIds())) {
            arg.setId(arg.getDataIds().get(0));
        }
        if(arg.getArgs() != null) {
            IObjectData argObjectData = arg.getArgs().toObjectData();
            arg.setDealResult(AccountUtil.getStringValue(argObjectData, "form_completed_result", ""));
            arg.setSendSaleEvent(AccountUtil.getBooleanValue(argObjectData, "form_send_event", false));
            arg.setCloseReason(AccountUtil.getStringValue(argObjectData, "form_close_reason", ""));
            arg.setCloseReason__o(AccountUtil.getStringValue(argObjectData, "form_close_reason__o", ""));
        } else {
            ObjectDataDocument objectDataDocument = new ObjectDataDocument();
            objectDataDocument.put("form_completed_result", arg.getDealResult());
            objectDataDocument.put("form_send_event", arg.isSendSaleEvent());
            objectDataDocument.put("form_close_reason", arg.getCloseReason());
            objectDataDocument.put("form_close_reason__o", arg.getCloseReason__o());
            arg.setArgs(objectDataDocument);
        }

        super.before(arg);

        IObjectData leadsData;
        if (CollectionUtils.empty(dataList)) {
            leadsData = serviceFacade.findObjectData(actionContext.getTenantId(), arg.getId(), objectDescribe);
        } else {
            leadsData = dataList.get(0);
        }
        if (leadsData == null) {
            throw new ValidateException(String.format(I18N.text(SFA_HAS_BEEN_DELETED), I18N.text("LeadsObj.attribute.self.display_name")));
        }
        String bizStatus = leadsData.get("biz_status", String.class);
        if (!LeadsBizStatusEnum.UN_PROCESSED.getCode().equals(bizStatus)
                && !LeadsBizStatusEnum.PROCESSED.getCode().equals(bizStatus)
                && !LeadsBizStatusEnum.CLOSED.getCode().equals(bizStatus)
                && !LeadsBizStatusEnum.UN_ASSIGNED.getCode().equals(bizStatus)) {
            throw new ValidateException(String.format(I18N.text(SFA_OBJECT_CANT_DO_THIS_JOB),
                    I18N.text("LeadsObj.attribute.self.display_name")));
        }
        IFieldDescribe field = objectDescribe.getFieldDescribe("close_reason");
        if (field != null && field.isActive() && StringUtils.isEmpty(arg.getCloseReason())) {
            throw new ValidateException(I18N.text(SFA_INVALID_REASON_CANT_BE_EMPTY));
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        log.info("leads close action needTriggerApprovalFlow: {}", needTriggerApprovalFlow());
        if (needTriggerApprovalFlow()) {
            Map<String, ApprovalFlowStartResult> resultMap = this.startApprovalFlow(this.dataList, ApprovalFlowTriggerType.CLOSE, approvalFlowTriggerMap(), approvalCallBackMap(), null);
            if (!ApprovalFlowStartResult.getNeedTriggerChangeOwnerAfterActionEnum().contains(resultMap.getOrDefault((this.dataList.get(0)).getId(), ApprovalFlowStartResult.APPROVAL_NOT_EXIST))) {
                return Result.builder().build();
            }
        }

        LeadsCloseAction.Result result = LeadsCloseAction.Result.builder().build();
        LeadsStatusEnum status = LeadsStatusEnum.CLOSED;
        LeadsBizStatusEnum bizStatus = LeadsBizStatusEnum.CLOSED;
        String closeReason = arg.getCloseReason();
        if (closeReason == null) {
            closeReason = "";
        } else if ("other".equals(closeReason) && !Strings.isNullOrEmpty(arg.getCloseReason__o())) {
            closeReason += arg.getCloseReason__o();
        }
        if (CollectionUtils.empty(dataList)) {
            dataList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), Lists.newArrayList(arg.getId()),
                    SFAPreDefineObject.Leads.getApiName());
        }

        dbObjectDataList = ObjectDataExt.copyList(dataList);

        if (CollectionUtils.notEmpty(dataList)) {
            List<ObjectDataDocument> dataDocumentList = ObjectDataDocument.ofList(dataList);
            String userId = actionContext.getUser().getUpstreamOwnerIdOrUserId();
            for (ObjectDataDocument objectData : dataDocumentList) {
                objectData.put("leads_status", status.getCode());
                objectData.put("biz_status", bizStatus.getCode());
                objectData.put("completed_result", arg.getDealResult());
                objectData.put("last_modified_by", Lists.newArrayList(userId));
                objectData.put("last_modified_time", System.currentTimeMillis());
                objectData.put("is_overtime", false);
                objectData.put("close_reason", closeReason);
                for (String field : LeadsUtils.exceptFields) {
                    objectData.remove(field);
                }
            }
            dataList = dataDocumentList.stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
        }
        List<String> updateFieldList = Lists.newArrayList("leads_status", "biz_status", "last_modified_by", "completed_result",
                "last_modified_time", "is_overtime", "close_reason");
        try {
            serviceFacade.batchUpdateByFields(actionContext.getUser(), dataList, updateFieldList);
        } catch (Exception e) {
            log.error("leads close error {}", actionContext.getTenantId(), e);
            throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
        }
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (isApprovalFlowStartSuccessOrAsynchronous(arg.getId())) {
            return result;
        }
        leadsOverTimeTaskService.deleteTask(actionContext.getTenantId(), arg.getId());
        // 新增加线索回收计算到期时间
//        recalculateTaskService.send(actionContext.getTenantId(), arg.getLeadsId(), "LeadsObj", ActionCodeEnum.DEAL);
        String leadsId = arg.getId();
        dataList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), Lists.newArrayList(leadsId), SFAPreDefineObject.Leads.getApiName());
        addFlowRecord();
        Optional<IObjectData> optionalData = dataList.stream().filter(d -> leadsId.equals(d.getId())).findFirst();
        // 发送【无效】的计算跟进时间的消息
        this.serviceFacade.sendActionMq(this.actionContext.getUser(), dataList, ObjectAction.CLOSE);
        if (optionalData.isPresent()) {
            IObjectData objectData = optionalData.get();
            String closeReasonCode = objectData.get("close_reason", String.class);
            String closeReason = "--";
            String closeReasonI18nKey = "--";
            if (!StringUtils.isEmpty(closeReasonCode)) {
                SelectOneFieldDescribe fieldDescribe = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe("close_reason");
                Optional<ISelectOption> option = fieldDescribe.getOption(closeReasonCode);
                if (option.isPresent()) {
                    closeReason = option.get().getLabel();
                    closeReasonI18nKey = closeReason;
                    String optionKey = "LeadsObj.field.close_reason.option." + closeReasonCode;
                    if (ObjectUtils.allNotEmpty(I18N.text(optionKey))){
                        closeReasonI18nKey = optionKey;
                    }
                    if ("other".equals(closeReasonCode) && !Strings.isNullOrEmpty(objectData.get("close_reason__o", String.class))) {
                        closeReason += ":" + objectData.get("close_reason__o", String.class);
                        closeReasonI18nKey = objectData.get("close_reason__o", String.class);
                    }
                }
            }
            String msg = String.format(I18N.text(SFA_INVALID_REASON_MSG), I18N.text(leadsI18nKey), objectData.getName(), closeReason);
            InternationalItem internationalItem = InternationalItem.builder()
                    .defaultInternationalValue(msg)
                    .internationalKey(SFA_INVALID_REASON_MSG_NEW)
                    .internationalParameters(Lists.newArrayList(leadsI18nKey, objectData.getName(), closeReasonI18nKey))
                    .build();
            serviceFacade.logWithInternationalCustomMessage(actionContext.getUser(), EventType.MODIFY, ActionType.Handle, objectDescribe, objectData,
                    msg,internationalItem);

            String leadsPoolId = objectData.get(LeadsConstants.Field.LEADS_POOL_ID.getApiName(), String.class);
            IObjectData leadsPool = objectPoolService.getObjectPoolById(SFAPreDefineObject.Leads.getApiName(), actionContext.getTenantId()
                    , leadsPoolId);
            if (leadsPool != null) {
                msg = String.format("%s %s，%s %s", I18N.text(leadsI18nKey), objectData.getName(), I18N.text(leadsPoolI18nKey), leadsPool.getName());
                SFALogModels.SFALogEntity logEntity = sfaLogService.buildLogEntity(leadsPool, msg, false);
                List<SFALogModels.SFALogTextMessage> textMessageList = Lists.newArrayList();
                sfaLogService.appendNameLinkLogTextMessage(actionContext.getObjectApiName(), objectData, textMessageList);

                msg = String.format("，%s %s", I18N.text(leadsPoolI18nKey), leadsPool.getName());
                sfaLogService.appendLogTextMessage(actionContext.getObjectApiName(), objectData, SFALogModels.LogLinkType.NO_LINK, msg, textMessageList);
                logEntity.setLogTextMessageList(textMessageList);

                InternationalItem poolItem = InternationalItem.builder()
                        .defaultInternationalValue(msg)
                        .internationalKey("sfa.leads.choose.pool.msg")
                        .internationalParameters(Lists.newArrayList(leadsPoolI18nKey, leadsPool.getName()))
                        .build();
                sfaLogService.addLog(actionContext.getUser(), logEntity, "SalesCluePoolLog", SFALogModels.LogOperationType.DEAL, poolItem);
            }
            String employeeId = actionContext.getUser().isOutUser() ? actionContext.getUser().getOutUserId() : actionContext.getUser().getUpstreamOwnerIdOrUserId();
            if (!LeadsUtils.isGraySendTodo(actionContext, dataList.get(0))) {
                qiXinTodoService.dealTodo(actionContext.getTenantId(), actionContext.getUser(), SFAPreDefineObject.Leads.getApiName(),
                        SessionBOCItemKeys.TobeProcessedSalesClue, leadsId);
            }

            LeadsUtils.insertCrmDealDataRelation(actionContext.getTenantId(), objectData.getId(), 2, employeeId);

            //更新自定义字段
            ButtonCustomFieldUtil.updateCustomField(actionContext.getUser(), arg.getArgs(), objectDescribe, systemButtonParamFormApiNameList,
                    udefButton, dataList, dbObjectDataList, ObjectAction.CLOSE.getActionLabel());
//            String actionParamter = ButtonCustomFieldUtil.getActionParamter(arg.getArgs(), this.objectDescribe, systemButtonParamFormApiNameList);
//            if (!StringUtils.isBlank(actionParamter)) {
//                ButtonExecutor.Arg buttonArg = ButtonExecutor.Arg.builder().args(arg.getArgs()).build();
//                IUdefAction udefAction = new UdefAction();
//                udefAction.setActionParamter(actionParamter);
//                Map<String, Object> fieldMap = getUpdateFieldMapByButtonAction(objectData, udefAction, buttonArg, actionContext.getUser());
//
//                //更新字段
//                metaDataService.batchUpdateWithMap(actionContext.getUser(), dataList, fieldMap);
//                LogUtil.recordLogs(actionContext.getUser(), dataList, dbObjectDataList, objectDescribe, ObjectAction.CLOSE.getActionLabel());
//            }
        }

        if(arg.isSendSaleEvent() && StringUtils.isNotBlank(arg.getDealResult())) {
            List<FeedsModel.FeedRelatedCrmObject> crmObjects = Lists.newArrayList();
            FeedsModel.FeedRelatedCrmObject crmObject = FeedsModel.FeedRelatedCrmObject.builder()
                    .apiName(SFAPreDefineObject.Leads.getApiName()).dataId(arg.getId()).build();
            crmObjects.add(crmObject);
            if(sendFeeds) {
                AccountUtil.publishFeeds(actionContext.getRequestContext(), arg.getDealResult(), 501, crmObjects, arg.getFeedsData());
            }
        }
        return super.after(arg, result);
    }

    @Override
    protected boolean needTriggerApprovalFlow() {
        return !arg.isSkipTriggerApprovalFlow();
    }

    protected Map<String, Map<String, Object>> approvalFlowTriggerMap() {
        Map<String, Map<String, Object>> result = Maps.newHashMap();
        for (IObjectData objectData : dataList) {
            Map<String, Object> fieldMap = Maps.newHashMap();
            if(arg.getArgs() != null) {
                result.put(objectData.getId(), arg.getArgs());
                fieldMap.put("params", JSON.toJSONString(arg.getArgs()));
            } else {
                Map<String, Object> params = Maps.newHashMap();
                params.put("close_reason", arg.getCloseReason());
                params.put("close_reason__o", arg.getCloseReason__o());
                params.put("deal_result", arg.getDealResult());
                fieldMap.put("params", JSON.toJSONString(params));
            }
            fieldMap.put("buttonName", I18N.text("sfa.leads.close.invalid")/*无效*/);
            fieldMap.put("buttonDescription", I18N.text("sfa.leads.close.invalid")/*无效*/);
            result.put(objectData.getId(), fieldMap);
        }
        return result;
    }

    protected Map<String, Map<String, Object>> approvalCallBackMap() {
        Map<String, Map<String, Object>> result = Maps.newHashMap();
        for (IObjectData objectData : dataList) {
            if(arg.getArgs() != null) {
                result.put(objectData.getId(), arg.getArgs());
            } else {
                Map<String, Object> fieldMap = Maps.newHashMap();
                fieldMap.put("close_reason", arg.getCloseReason());
                fieldMap.put("close_reason__o", arg.getCloseReason__o());
                fieldMap.put("deal_result", arg.getDealResult());
                fieldMap.put("send_sales_event", arg.isSendSaleEvent());
                result.put(objectData.getId(), fieldMap);
            }
        }
        return result;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.CLOSE.getButtonApiName();
    }

    @Override
    protected IObjectData getPreObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), Lists.newArrayList(arg.getId()), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected IObjectData getPostObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), Lists.newArrayList(arg.getId()), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected boolean skipPreFunction() {
        return arg.isSkipPreAction() || super.skipPreFunction();
    }

    @Override
    protected boolean skipCheckButtonConditions() {
        return arg.isSkipButtonConditions() || super.skipCheckButtonConditions();
    }

    @Override
    protected Map<String, Object> getArgs() {
        if(arg.getArgs() != null) {
            return ObjectDataExt.toMap(arg.getArgs().toObjectData());
        }
        return Maps.newHashMap();
    }

    private void addFlowRecord() {
        List<IObjectData> oldFlowRecordDataList = Lists.newArrayList();
        for (IObjectData leadsData : dataList) {
            String oldOwnerId = CommonBizUtils.getOwner(leadsData);
            if(StringUtils.isBlank(oldOwnerId)) {
                continue;
            }
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(1);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "leads_owner", oldOwnerId);
            SearchUtil.fillFilterEq(filters, "leads_id", leadsData.getId());
            searchTemplateQuery.setFilters(filters);
            List<OrderBy> orderByList = Lists.newArrayList();
            orderByList.add(new OrderBy("last_modified_time", false));
            searchTemplateQuery.setOrders(orderByList);
            searchTemplateQuery.setNeedReturnCountNum(false);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(actionContext.getUser(), SFAPreDefineObject.LeadsFlowRecord.getApiName(), searchTemplateQuery);
            if(queryResult != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(queryResult.getData())) {
                IObjectData oldFlowRecordData = queryResult.getData().get(0);
                oldFlowRecordData.set("leads_status", "closed");
                oldFlowRecordData.set("leads_status_changed_time", System.currentTimeMillis());
                oldFlowRecordData.set("last_modified_by", Lists.newArrayList(actionContext.getUser().getUpstreamOwnerIdOrUserId()));
                oldFlowRecordData.set("last_modified_time", System.currentTimeMillis());
                oldFlowRecordDataList.add(oldFlowRecordData);
            }
        }
        if(CollectionUtils.notEmpty(oldFlowRecordDataList)) {
            List<String> updateFieldList = Lists.newArrayList("leads_status", "leads_status_changed_time", "last_modified_by",
                    "last_modified_time");
            try {
				IActionContext context = ActionContextExt.of(actionContext.getUser(),
						RequestContextManager.getContext()).setNotValidate(true).getContext();
                serviceFacade.batchUpdateByFields(context, oldFlowRecordDataList, updateFieldList);
            } catch (Exception e) {
                log.error("addFlowRecord error", e);
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
            }
        }
    }

    @Data
    @NoArgsConstructor
    static class Arg {
        private String id;
        private String closeReason;
        private String closeReason__o;
        private String dealResult;
        private boolean skipTriggerApprovalFlow;
        private boolean skipFunctionCheck;
        private boolean skipPreAction;
        private boolean skipButtonConditions;
        private boolean sendSaleEvent;
        private List<String> dataIds;
        private ObjectDataDocument args;
        private ObjectDataDocument feedsData;
    }

    @Data
    @Builder
    @NoArgsConstructor
    public static class Result {
    }
}
