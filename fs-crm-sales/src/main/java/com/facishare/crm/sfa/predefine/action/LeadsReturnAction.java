package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.Enum.LeadsBackOperationTypeEnum;
import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.model.Enum.SessionBOCItemKeys;
import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.action.model.IPoolActionContentModels;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.LeadsPoolServiceImpl;
import com.facishare.crm.sfa.predefine.service.QiXinTodoService;
import com.facishare.crm.sfa.predefine.service.model.SFALogModels;
import com.facishare.crm.sfa.predefine.service.task.LeadsAllocateOverTimeTaskService;
import com.facishare.crm.sfa.predefine.service.task.LeadsAllocateTaskService;
import com.facishare.crm.sfa.predefine.service.task.LeadsOverTimeTaskService;
import com.facishare.crm.sfa.task.RelationOperationType;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.CommonBizUtils;
import com.facishare.crm.sfa.utilities.util.LeadsUtil;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;


@Slf4j
public class LeadsReturnAction extends BaseSFAReturnAction {
    LeadsOverTimeTaskService leadsOverTimeTaskService = SpringUtil.getContext().getBean(LeadsOverTimeTaskService.class);
    LeadsAllocateOverTimeTaskService leadsAllocateOverTimeTaskService = SpringUtil.getContext().getBean(LeadsAllocateOverTimeTaskService.class);
    LeadsAllocateTaskService leadsAllocateTaskService = SpringUtil.getContext().getBean(LeadsAllocateTaskService.class);
    private static final QiXinTodoService qiXinTodoService = SpringUtil.getContext().getBean(QiXinTodoService.class);
    LeadsPoolServiceImpl leadsPoolServiceImpl = SpringUtil.getContext().getBean(LeadsPoolServiceImpl.class);
    private static final LeadsUtil leadsUtil = SpringUtil.getContext().getBean(LeadsUtil.class);
    @Override
    protected boolean existNonPoolData(List<IObjectData> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return false;
        }
        return dataList.stream().filter(x -> StringUtils.isBlank(LeadsUtils.getPoolId(x))).findFirst().isPresent();
    }

    @Override
    protected String getObjectPoolId(BaseSFAReturnAction.Arg arg) {
        if (arg.getArgs() != null) {
            IObjectData argObjectData = arg.getArgs().toObjectData();
            String poolId = AccountUtil.getStringValue(argObjectData, "form_leads_pool_id", "");
            if (StringUtils.isNotBlank(poolId)) {
                arg.setObjectPoolId(poolId);
            }
        }
        return super.getObjectPoolId(arg);
    }

    @Override
    protected void before(BaseSFAReturnAction.Arg arg) {
        super.before(arg);
        if (arg.getArgs() != null) {
            if (!arg.getArgs().containsKey("form_leads_pool_id")) {
                arg.getArgs().put("form_leads_pool_id", getObjectPoolId(arg));
            }
        }
        if (arg.getOperationType() != LeadsBackOperationTypeEnum.TAKE_BACK.getValue()
                && arg.getOperationType() != LeadsBackOperationTypeEnum.RETURN.getValue()) {
            throw new ValidateException(String.format("type%s", I18N.text(SFA_CONFIG_PARAMETER_ERROR)));
        }
        IFieldDescribe backReasonField = objectDescribe.getFieldDescribe("back_reason");
        if (backReasonField.isActive() && backReasonField.isRequired() && arg.getOperationType() == LeadsBackOperationTypeEnum.RETURN.getValue()
                && Strings.isNullOrEmpty(arg.getBackReason())) {
            throw new ValidateException(I18N.text(SFA_MULTIIMPORTBUSINESS_850_1));
        }
        if (CollectionUtils.isEmpty(dataList)) {
            dataList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), arg.getObjectIDs(), SFAPreDefineObject.Leads.getApiName());
        }
        Map<String, LeadsBizStatusEnum> unOperateStatus = Maps.newHashMap();
        unOperateStatus.put(LeadsBizStatusEnum.UN_ASSIGNED.getCode(), LeadsBizStatusEnum.UN_ASSIGNED);
        Map<String, LeadsBizStatusEnum> canOperateStatus = Maps.newHashMap();
        canOperateStatus.put(LeadsBizStatusEnum.UN_PROCESSED.getCode(), LeadsBizStatusEnum.UN_PROCESSED);
        canOperateStatus.put(LeadsBizStatusEnum.TRANSFORMED.getCode(), LeadsBizStatusEnum.TRANSFORMED);
        canOperateStatus.put(LeadsBizStatusEnum.PROCESSED.getCode(), LeadsBizStatusEnum.PROCESSED);
        canOperateStatus.put(LeadsBizStatusEnum.CLOSED.getCode(), LeadsBizStatusEnum.CLOSED);
        List<String> tobeProcessIds = Lists.newArrayList();
        failedList = Lists.newArrayList();
        Iterator var1 = dataList.iterator();
        while (var1.hasNext()) {
            IObjectData objectData = (IObjectData) var1.next();
            String leadsId = objectData.getId();
            String bizStatus = objectData.get("biz_status", String.class);
            String lifeStatus = objectData.get("life_status", String.class);
            if (canOperateStatus.containsKey(bizStatus) && !lifeStatus.equals(ObjectLifeStatus.INVALID.getCode())) {
                tobeProcessIds.add(leadsId);
            } else if (unOperateStatus.containsKey(bizStatus)) {
                String failedMsg = String.format("%s %s %s，%s", I18N.text("LeadsObj.attribute.self.display_name"),
                        objectData.getName(), unOperateStatus.get(bizStatus).getText(), I18N.text(SFA_UNABEL_OPERATE));
                failedList.add(failedMsg);
                continue;
            } else if (lifeStatus.equals(ObjectLifeStatus.INVALID.getCode()) || lifeStatus.equals(ObjectLifeStatus.IN_CHANGE.getCode())) {
                String failedMsg = String.format("%s %s %s，%s",
                        I18N.text("LeadsObj.attribute.self.display_name"),
                        objectData.getName(),
                        I18N.text(SFA_HAS_BEEN_INVALID),
                        I18N.text(SFA_UNABEL_OPERATE));
                failedList.add(failedMsg);
                continue;
            } else {

                String failedMsg = String.format(I18N.text(SFA_OBJECT_STATUS_UNPROCESSED),
                        I18N.text("LeadsObj.attribute.self.display_name"),
                        objectData.getId(), bizStatus);
                failedList.add(failedMsg);
                continue;
            }
        }
        if (dataList.size() == 1) {
            if (CollectionUtils.isNotEmpty(failedList)) {
                throw new ValidateException(failedList.get(0));
            }
        } else {
            if (failedList.size() == dataList.size()) {
                throw new ValidateException(failedList.get(0));
            }
        }
        arg.setObjectIDs(tobeProcessIds);
    }

    @Override
    protected void doFunPrivilegeCheck() {
        if (arg.getOperationType() == LeadsBackOperationTypeEnum.RETURN.getValue()) {
            super.doFunPrivilegeCheck();
        }
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        String privilegeCode = "Return";
        if (arg.getOperationType() == LeadsBackOperationTypeEnum.TAKE_BACK.getValue()) {
            privilegeCode = "TakeBack";
        }
        if (arg.getOperationType() == LeadsBackOperationTypeEnum.RETURN.getValue()) {
            privilegeCode = "Return";
        }
        return Lists.newArrayList(privilegeCode);
    }

    @Override
    protected SFAObjectPoolCommon.Result doAct(Arg arg) {
        return super.doAct(arg);
    }

    @Override
    protected SFAObjectPoolCommon.Result after(Arg arg, SFAObjectPoolCommon.Result result) {
        result.setSuccessList(arg.getObjectIDs());
        result.setFailedList(failedList);
        if (CollectionUtils.isEmpty(arg.getObjectIDs())) {
            return result;
        }
        if (isApprovalFlowStartSuccessOrAsynchronous(arg.getObjectIDs().get(0))) {
            return result;
        }
        super.after(arg, result);
        //创建线索超时提醒任务
        addLeadsOverTimeTask(arg);
        //线索分配任务创建
        addLeadsAllocateTask(arg);
        addFlowRecord();
        if (!CollectionUtils.isEmpty(arg.getObjectIDs())) {
            for (String processedId : arg.getObjectIDs()) {
                Optional<IObjectData> optionalData = dataList.stream().filter(d -> processedId.equals(d.getId())).findFirst();
                if (!optionalData.isPresent()) {
                    continue;
                }
                IObjectData objectData = optionalData.get();
                addLog(objectData);
                //待办
                String leadsPoolId = arg.getObjectPoolId();
                if (!Strings.isNullOrEmpty(leadsPoolId)) {
                    boolean isNewNotifyAdmin = AccountUtil.getBooleanValue(objectPoolData, "is_new_to_notify_admin", false);
                    if(isNewNotifyAdmin) {
                        Map<String, List<String>> inAndOutPoolAdminList = leadsPoolServiceImpl.getInAndOutPoolAdminById(actionContext.getUser(), leadsPoolId);
                        if (!inAndOutPoolAdminList.isEmpty()) {
                            qiXinTodoService.sendInAndOutTodo(actionContext.getTenantId(), SessionBOCItemKeys.TOBE_ASSIGNED_SALES_CLUE,
                                    actionContext.getObjectApiName(), processedId, actionContext.getUser().getUpstreamOwnerIdOrUserId(),
                                    inAndOutPoolAdminList.containsKey("in") ? inAndOutPoolAdminList.get("in") : Lists.newArrayList(),
                                    inAndOutPoolAdminList.containsKey("out") ? inAndOutPoolAdminList.get("out") : Lists.newArrayList());
                        }
                    }
                }
            }
        }
        sendActionMq();
        leadsUtil.sendHandleRelationshiMsgByListByList(actionContext.getTenantId(), Lists.newArrayList(dataList.get(0).getId()), RelationOperationType.RESULT_ONE.getValue());
        return result;
    }

    protected void addLog(IObjectData objectData) {
        String company = objectData.get("company", String.class);
        if (Strings.isNullOrEmpty(company)) {
            company = "";
        }

        String backReasonCode = arg.getBackReason();
        String backReason = "--";
        String backReasonKey = "--";
        if (!Strings.isNullOrEmpty(backReasonCode)) {
            SelectOneFieldDescribe fieldDescribe = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe("back_reason");
            Optional<ISelectOption> option = fieldDescribe.getOption(backReasonCode);
            if (option.isPresent()) {
                backReason = option.get().getLabel();
                backReasonKey = backReason;
                String optionKey = "LeadsObj.field.back_reason.option." + backReasonCode;
                if (!ObjectUtils.isEmpty(I18N.text(optionKey))){
                    backReasonKey = optionKey;
                }
                if ("other".equals(arg.getBackReason()) && !Strings.isNullOrEmpty(arg.getBackReason__o())) {
                    backReason += ":" + arg.getBackReason__o();
                    if (!ObjectUtils.isEmpty(arg.getBackReason__o())) {
                        backReasonKey = arg.getBackReason__o();
                    }
                }
            }
        }
        String msg = String.format(I18N.text(SFA_LEADS_RETURN_REASON_LOG),
                I18N.text("LeadsObj.attribute.self.display_name"),
                objectData.getName(),
                company,
                I18N.text("LeadsPoolObj.attribute.self.display_name"),
                objectPoolData.getName(),
                backReason
        );

        InternationalItem internationalItem = InternationalItem.builder()
                .defaultInternationalValue(msg)
                .internationalKey(SFA_LEADS_RETURN_REASON_LOG_NEW)
                .internationalParameters(Lists.newArrayList(
                        "LeadsObj.attribute.self.display_name", objectData.getName(), company,
                        "LeadsPoolObj.attribute.self.display_name", objectPoolData.getName(), backReasonKey))
                .build();
        serviceFacade.logWithInternationalCustomMessage(actionContext.getUser(), EventType.MODIFY, ActionType.RETURN, objectDescribe, objectData, msg, internationalItem);
        LeadsUtils.addPoolReturnLog(actionContext.getUser(), objectData, objectPoolData, SFALogModels.LogOperationType.RETURN_BACK);
    }

    @Override
    protected void logAsync(List<IObjectData> dataList, EventType eventType, ActionType actionType) {
    }

    @Override
    protected boolean needTriggerApprovalFlow() {
        if (arg.getOperationType() == LeadsBackOperationTypeEnum.TAKE_BACK.getValue()) {
            return false;
        }
        return super.needTriggerApprovalFlow();
    }

    @Override
    protected Map<String, Map<String, Object>> approvalFlowTriggerMap() {
        Map<String, Map<String, Object>> result = Maps.newHashMap();
        for (IObjectData objectData : dataList) {
            Map<String, Object> fieldMap = Maps.newHashMap();
            if (arg.getArgs() != null) {
                result.put(objectData.getId(), arg.getArgs());
                fieldMap.put("params", JSON.toJSONString(arg.getArgs()));
                fieldMap.put("buttonName", ObjectAction.RETURN.getActionLabel());
                fieldMap.put("buttonDescription", ObjectAction.RETURN.getActionLabel());
            }
            //下面参数供流程，公海管理员做审批人时使用
            fieldMap = LeadsUtils.getApprovalFlowTriggerMap(objectDescribe, getObjectPoolId(arg), fieldMap);
            result.put(objectData.getId(), fieldMap);
        }
        return result;
    }

    private void addLeadsAllocateTask(Arg arg) {
        if (arg.getOperationType() == LeadsBackOperationTypeEnum.RETURN.getValue()) {
            Map<String, List<String>> maps = Maps.newHashMap();
            if (StringUtils.isBlank(arg.getObjectPoolId()) || CollectionUtils.isEmpty(arg.getObjectIDs())) return;
            maps.put(arg.getObjectPoolId(), arg.getObjectIDs());
            leadsAllocateTaskService.createOrUpdateTask(actionContext.getTenantId(),
                    maps, 2, 0);
        }
    }

    private void addLeadsOverTimeTask(Arg arg) {
        leadsOverTimeTaskService.deleteTask(actionContext.getTenantId(), arg.getObjectIDs());
        leadsAllocateOverTimeTaskService.createOrUpdateTask(actionContext.getTenantId(), arg.getObjectIDs(), objectPoolData);
    }

    private void addFlowRecord() {
        List<IObjectData> oldFlowRecordDataList = Lists.newArrayList();
        for (IObjectData leadsData : dataList) {
            String oldOwnerId = CommonBizUtils.getOwner(leadsData);
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(1);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "leads_owner", oldOwnerId);
            SearchUtil.fillFilterEq(filters, "leads_id", leadsData.getId());
            searchTemplateQuery.setFilters(filters);
            List<OrderBy> orderByList = Lists.newArrayList();
            orderByList.add(new OrderBy("last_modified_time", false));
            searchTemplateQuery.setOrders(orderByList);
            searchTemplateQuery.setNeedReturnCountNum(false);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(actionContext.getUser(), SFAPreDefineObject.LeadsFlowRecord.getApiName(), searchTemplateQuery);
            if (queryResult != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(queryResult.getData())) {
                IObjectData oldFlowRecordData = queryResult.getData().get(0);
                oldFlowRecordData.set("leads_status", "returned");
                oldFlowRecordData.set("leads_status_changed_time", System.currentTimeMillis());
                oldFlowRecordData.set("leads_back_reason", I18N.text("sfa.leads.return.employee.initiative")/*员工主动退回*/);
                oldFlowRecordData.set("last_modified_by", Lists.newArrayList(actionContext.getUser().getUpstreamOwnerIdOrUserId()));
                oldFlowRecordData.set("last_modified_time", System.currentTimeMillis());
                oldFlowRecordDataList.add(oldFlowRecordData);
            }
        }
        if (CollectionUtils.isNotEmpty(oldFlowRecordDataList)) {
            List<String> updateFieldList = Lists.newArrayList("leads_status", "leads_status_changed_time", "leads_back_reason", "last_modified_by",
                    "last_modified_time");
            try {
				IActionContext context = ActionContextExt.of(actionContext.getUser(),
						RequestContextManager.getContext()).setNotValidate(true).getContext();
                serviceFacade.batchUpdateByFields(context, oldFlowRecordDataList, updateFieldList);
            } catch (Exception e) {
                log.error("addFlowRecord error", e);
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
            }
        }
    }

    private void sendActionMq() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getObjectIDs(), actionContext.getObjectApiName());
        if (objectDataList.size() <= 0) {
            return;
        }
        IPoolActionContentModels.PoolActionContent actionContent = IPoolActionContentModels.PoolActionContent.builder()
                .poolName(objectPoolData.getName())
                .objectIds(arg.getObjectIDs()).build();

        if (objectDataList.size() > 1) {
            sfaOpenApiMqService.sendOpenApiMq(actionContext.getUser(), ObjectAction.RETURN.getActionCode(),
                    Utils.LEADS_POOL_API_NAME, arg.getObjectPoolId(), actionContent);
        } else {
            IObjectData objectData = objectDataList.get(0);
            actionContent.setName(objectData.getName());
            actionContent.setCompany(AccountUtil.getStringValue(objectData, "company", "--"));
            actionContent.setSource(AccountUtil.getStringValue(objectData, "source", "--"));
            String sourceDescription = LeadsUtils.getSelectOneLabel(objectDescribe, "source", AccountUtil.getStringValue(objectData, "source", ""));
            if (StringUtils.isBlank(sourceDescription)) {
                sourceDescription = "--";
            }
            actionContent.setSourceDescription(sourceDescription);
            actionContent.setBackReason(arg.getBackReason());
            String backReasonDescription = LeadsUtils.getSelectOneLabel(objectDescribe, "back_reason", arg.getBackReason());
            if (StringUtils.isBlank(backReasonDescription)) {
                backReasonDescription = "--";
            }
            actionContent.setBackReasonDescription(backReasonDescription);
            actionContent.setBackReason__o(arg.getBackReason__o());
            sfaOpenApiMqService.sendOpenApiMq(actionContext.getUser(), ObjectAction.RETURN.getActionCode(),
                    Utils.LEADS_POOL_API_NAME, arg.getObjectPoolId(), actionContent);
        }
    }
}