package com.facishare.crm.sfa.predefine.action;

import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.activity.producer.ActivityRocketProducer;
import com.facishare.crm.sfa.lto.utils.LicenseCheckUtil;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.bizvalidator.BizValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.ProductIsRepeatedValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.newopportunity.AvailableWorkflowValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.newopportunity.NewOpportunityOwnerShipValidator;
import com.facishare.crm.sfa.task.ForecastTaskProducer;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.proxy.NewOpportunityProxy;
import com.facishare.crm.sfa.utilities.proxy.model.NewOpportunityTriggerModel;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.DhtUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.NewOpportunityUtil;
import com.facishare.crm.sfa.utilities.validator.NewOpportunityValidator;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.rest.core.exception.RestProxyInvokeException;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@EqualsAndHashCode(callSuper = true)
public class NewOpportunityAddAction extends BaseAddSFAAction {

    private String dataFrom = "1";

    private final NewOpportunityProxy newOpportunityProxy = SpringUtil.getContext().getBean(NewOpportunityProxy.class);
    private final ForecastTaskProducer forecastTaskProducer = SpringUtil.getContext().getBean(ForecastTaskProducer.class);
    private final ActivityRocketProducer activityRocketProducer = SpringUtil.getContext().getBean(ActivityRocketProducer.class);

    boolean isFromLeadsTransfer = false;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        if (isFromLeadsTransfer) {
            return Lists.newArrayList("TransferAdd");
        } else if (dataFrom.equals(NewOpportunityAddAction.From.PROCUREMENT_ENTERPRISE_TRANSFER.getValue())) {
            return Lists.newArrayList();
        } else {
            return super.getFuncPrivilegeCodes();
        }
    }

    @Override
    protected void validateDetail(String detailApiName, List<IObjectData> detailDataList) {
        if (isFromLeadsTransfer) {
            if (Boolean.TRUE.equals(this.actionContext.getAttribute("leads_transfer_validate_details"))) {
                super.validateDetail(detailApiName, detailDataList);
            }
        } else {
            super.validateDetail(detailApiName, detailDataList);
        }
    }

    @Override
    protected void init() {
        super.init();
        objectData.set("is_from_leads_transfer", false);
        boolean isNotSpecifyTime = isNotSpecifyTime();
        if (isNotSpecifyTime || arg.getObjectData().get("last_followed_time") == null) {
            objectData.set("last_followed_time", System.currentTimeMillis());
        }
        if (isNotSpecifyTime || arg.getObjectData().get("stg_changed_time") == null) {
            objectData.set("stg_changed_time", System.currentTimeMillis());
        }
        if (DhtUtil.isFromSyncData(actionContext.getPeerName()) && !Objects.isNull(arg.getObjectData().get("stg_changed_time"))) {
            objectData.set("stg_changed_time", arg.getObjectData().get("stg_changed_time"));
        }
        setStageData();
    }

    private boolean isNotSpecifyTime() {
        return BooleanUtils.isNotTrue(actionContext.getAttribute(ActionContextExt.IS_SPECIFY_TIME));
    }

    private void setStageData() {
        Map<String, String> map = NewOpportunityValidator
                .salesStageToProbability(String.valueOf(arg.getObjectData().get("sales_stage")), objectDescribe);
        if (!map.isEmpty()) {
            objectData.set("sales_status", map.get("sales_status"));
            Object probability = objectData.get("probability");
            if (probability == null) {
                objectData.set("probability", map.get("probability"));
            }
            if (objectData.get("forecast_type") == null && map.get("forecast_type") != null) {
                objectData.set("forecast_type", map.get("forecast_type"));
            }
        }
    }

    @Override
    protected void before(Arg arg) {
        isFromLeadsTransfer = (boolean) arg.getObjectData().getOrDefault("is_from_leads_transfer", false);
        dataFrom = AccountUtil.getStringValue(arg.getObjectData(), AccountConstants.Field.FROM, "1");
        super.before(arg);
        ValidatorContext validatorContext = ValidatorContext.builder()
                .action(ObjectAction.CREATE)
                .user(actionContext.getUser())
                .actionContext(actionContext)
                .describeApiName(objectDescribe.getApiName())
                .objectDescribe(objectDescribe)
                .objectDescribes(objectDescribes)
                .objectData(objectData)
                .detailObjectData(detailObjectData)
                .build();
        BizValidator.build()
                .withContext(validatorContext)
//                .with(new PriceBookValidator())
//                .when(bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId())&& NewOpportunityUtil.hasAccount(objectData))
//                .with(new ProductRangeValidator())
//                .when(!bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId())&& NewOpportunityUtil.hasAccount(objectData))
                .with(new ProductIsRepeatedValidator())
                .with(new AvailableWorkflowValidator())
//                .with(new CurrencyValidator())
//                .when(bizConfigThreadLocalCacheService.isCurrencyEnabled(actionContext.getTenantId()))
                .with(new NewOpportunityOwnerShipValidator())
                .when(!actionContext.isFromOpenAPI())
                .doValidate();

        NewOpportunityUtil.setLeadsToNewOpportunity(actionContext.getUser(), Lists.newArrayList(objectData));
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        // 新建商机触发流程接口
        if (Boolean.TRUE.equals(GrayUtil.isGrayEnable(actionContext.getTenantId(), GrayUtil.NEW_OPPORTUNITY_DO_AFTER_ASYNC))) {
            try {
                ParallelUtils.createBackgroundTask().submit(() -> doAfter(arg, result)).run();
            } catch (Exception e) {
                log.warn("NewOpportunityAddAction after task error,tenantId " + actionContext.getTenantId(), e);
            }
        } else {
            doAfter(arg, result);
        }
        ParallelUtils.createBackgroundTask().submit(this::sendMQ).run();
        return result;
    }

    private void doAfter(Arg arg, Result result) {
        IObjectData data = result.getObjectData().toObjectData();
        if (shouldTriggerSalesProcess(data, actionContext.getUser())) {
            trigger(arg);
        }
        forecastTaskProducer.sendMessageWhenNewOpportunityCreate(actionContext.getTenantId(), result.getObjectData().getId());
    }

    private boolean shouldTriggerSalesProcess(IObjectData data, User user) {
        if (ObjectLifeStatus.UNDER_REVIEW.getCode().equals(data.get(ObjectLifeStatus.LIFE_STATUS_API_NAME))) {
            String triggerMode = serviceFacade.findTenantConfig(user, ConfigType.NEW_OPPORTUNITY_ADD_SALES_PROCESS_TRIGGER_MODE.getKey());
            return !"1".equals(triggerMode);
        }
        return true;
    }

    private void trigger(Arg arg) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json");
        headers.put("x-tenant-id", getActionContext().getTenantId());
        headers.put("x-user-id", getActionContext().getUser().getUpstreamOwnerIdOrUserId());

        NewOpportunityTriggerModel.Arg triggerArg = new NewOpportunityTriggerModel.Arg();
        triggerArg.setEntityId(SFAPreDefineObject.NewOpportunity.getApiName());
        triggerArg.setObjectId((String) arg.getObjectData().get(DBRecord.ID));
        triggerArg.setSourceWorkflowId(String.valueOf(arg.getObjectData().get("sales_process_id")));
        if (arg.getObjectData().get("sales_stage") != null) {
            triggerArg.setStageId(String.valueOf(arg.getObjectData().get("sales_stage")));
        }
        log.info("NewOpportunityTriggerModel.Arg {}", triggerArg);
        try {
            NewOpportunityTriggerModel.Result triggerResult = newOpportunityProxy.trigger(triggerArg, headers);
            if (triggerResult.getCode() > 0) {
                log.error("trigger error,headers:{},arg:{},result:{}",
                        JsonUtil.toJsonWithNull(headers), JsonUtil.toJsonWithNull(triggerArg), JsonUtil.toJsonWithNull(triggerResult)
                );
                throw new ValidateException(triggerResult.getMessage());
            }
        } catch (RestProxyInvokeException e) {
            log.error("/rest/instance/trigger error ,arg:{}", triggerArg, e);
            throw new RestProxyInvokeException(e);
        }
    }

    private void sendMQ() {
        try {
            if (!LicenseCheckUtil.checkAIExist(actionContext.getUser().getTenantId())) {
                return;
            }
            ActivityMessage activityMessage = ActivityMessage.builder()
                    .tenantId(actionContext.getUser().getTenantId())
                    .objectId(objectData.getId())
                    .objectApiName(objectData.getDescribeApiName())
                    .actionCode(actionContext.getActionCode())
                    .stage(actionContext.getActionCode())
                    .opId(actionContext.getUser().getUpstreamOwnerIdOrUserId())
                    .build();
            activityRocketProducer.sendActivityMessage("account-to-strategy", activityMessage);
        } catch (Exception e) {
            log.error("sendMQ error,tenantId:{}", actionContext.getTenantId(), e);
        }
    }

    public enum From {
        LIST("1", "商机2.0列表"), // ignoreI18n
        PROCUREMENT_ENTERPRISE_TRANSFER("6", "招投标主体转换"); // ignoreI18n

        private final String value;
        private final String label;

        From(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return value;
        }

        public String getLabel() {
            return label;
        }
    }

}
