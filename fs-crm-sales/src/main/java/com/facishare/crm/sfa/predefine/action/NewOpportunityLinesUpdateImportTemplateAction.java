package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.constant.NewOppportunityConstants;
import com.facishare.crm.sfa.utilities.util.imports.ImportSoUtil;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportTemplateAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;

import java.util.List;

public class NewOpportunityLinesUpdateImportTemplateAction extends StandardUpdateImportTemplateAction {
    private final List<String> removeFields = Lists.newArrayList(ObjectDataExt.EXTEND_OBJ_DATA_ID,
            NewOppportunityConstants.NewOpportunityLinesField.PRICEBOOKPRODUCTID.getApiName());
    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        headerFieldList.removeIf(f -> removeFields.contains(f.getApiName()));
        ImportSoUtil.removeFields(headerFieldList, ImportSoUtil.PERIODIC_PRODUCT_FILTER_FIELDS);
    }
}