package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.attribute.dao.AttributeRedisDao;
import com.facishare.crm.sfa.predefine.service.attribute.dao.AttributeValueRedisDao;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeValue;
import com.facishare.crm.sfa.predefine.service.task.AttributeTaskService;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants.ValueField;
import com.facishare.crm.sfa.utilities.validator.AttributeValidator;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by luxin on 2018/11/12.
 */
@Slf4j
public class NonstandardAttributeEditAction extends StandardEditAction {

}
