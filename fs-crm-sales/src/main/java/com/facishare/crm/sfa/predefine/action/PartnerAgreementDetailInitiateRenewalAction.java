package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.model.ChannelServiceModel;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.ChannelAgreementService;
import com.facishare.crm.sfa.predefine.service.ChannelTaskService;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel;
import com.facishare.crm.sfa.prm.api.channel.AgreementStatusRecordService;
import com.facishare.crm.sfa.prm.api.enums.AgreementStatus;
import com.facishare.crm.sfa.prm.core.service.AgreementStatusRecordServiceImpl;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.crm.sfa.prm.platform.utils.I18NUtils;
import com.facishare.crm.sfa.service.ChannelService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

import static com.facishare.crm.constants.PrmI18NConstants.*;
import static com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel.AGREEMENT_STATUS;

/**
 * Created by Sundy on 2024/10/21 16:26
 */
public class PartnerAgreementDetailInitiateRenewalAction extends AbstractStandardAction<PartnerAgreementDetailInitiateRenewalAction.Arg, PartnerAgreementDetailInitiateRenewalAction.Result> {
    private static final ChannelTaskService channelTaskService = SpringUtil.getContext().getBean(ChannelTaskService.class);
    private final ChannelService channelService = SpringUtil.getContext().getBean("channelServiceProvider", ChannelService.class);
    private final MetaDataFindServiceExt metaDataFindServiceExt = SpringUtil.getContext().getBean(MetaDataFindServiceExt.class);
    private final ChannelAgreementService channelAgreementService = SpringUtil.getContext().getBean(ChannelAgreementService.class);
    private final AgreementStatusRecordService agreementStatusRecordService = SpringUtil.getContext().getBean(AgreementStatusRecordServiceImpl.class);
    private String signSchemeId;
    private String belongDataId;
    private String admissionObject;
    private IObjectData belongData;


    private IObjectData objectData;

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.INITIATE_RENEWAL.getButtonApiName();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.INITIATE_RENEWAL.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }

    @Override
    protected Map<String, Object> getArgs() {
        return arg.getArgs();
    }

    @Override
    protected void before(PartnerAgreementDetailInitiateRenewalAction.Arg arg) {
        super.before(arg);
        if (StringUtils.isBlank(objectData.getOutTenantId())) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_SCHEME_OUTSIDE_COMPANY_EMPTY));
        }
        this.admissionObject = channelService.fetchChannelAdmissionObject(actionContext.getUser());
        this.belongDataId = channelService.getAdmissionObjectDataId(objectData, this.admissionObject);
        AgreementStatus originalAgreementStatus = AgreementStatus.find(DataUtils.getValue(objectData, AGREEMENT_STATUS, String.class, null), null);
        if (originalAgreementStatus != AgreementStatus.ACTIVE) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_AGREEMENT_STATUS_NOT_EQ_ACTIVATE));
        }
        this.belongData = metaDataFindServiceExt.findObjectByIdIgnoreAll(actionContext.getUser(), this.belongDataId, this.admissionObject);
        if (this.belongData == null) {
            String value = PartnerAgreementDetailModel.BELONG_ACCOUNT_ID;
            if (SFAPreDefineObject.Partner.getApiName().equals(admissionObject)) {
                value = PartnerAgreementDetailModel.BELONG_PARTNER_ID;
            }
            throw new ValidateException(I18N.text(PRM_CHANNEL_DATA_NOT_EXISTS, value));
        }
        ChannelServiceModel.MatchScheme matchScheme = channelService.matchSignScheme(actionContext.getUser(), this.admissionObject, this.belongData);
        if (StringUtils.isBlank(matchScheme.getSchemeId())) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_SCHEME_NOT_MATCH, I18NUtils.getDataI18nName(this.belongData)));
        }
        this.signSchemeId = matchScheme.getSchemeId();
    }


    @Override
    protected PartnerAgreementDetailInitiateRenewalAction.Result doAct(PartnerAgreementDetailInitiateRenewalAction.Arg arg) {
        IObjectData oldAgreementDetailData = metaDataFindServiceExt.findObjectData(actionContext.getUser(), arg.getObjectDataId(), SFAPreDefineObject.PartnerAgreementDetail.getApiName());
        IObjectData newAgreementDetailData = channelAgreementService.createAgreementDetailWhenInitiateRenewal(actionContext.getUser(), admissionObject, this.belongData, oldAgreementDetailData);
        channelTaskService.manualInitiateRenewal(actionContext.getUser(), this.belongDataId, this.signSchemeId, oldAgreementDetailData.getOutTenantId(), this.admissionObject);
        return PartnerAgreementDetailInitiateRenewalAction.Result.of(newAgreementDetailData);
    }

    @Data
    public static class Arg {
        private String objectDataId;
        private Map<String, Object> args;
    }

    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        public static PartnerAgreementDetailInitiateRenewalAction.Result of(IObjectData objectData) {
            PartnerAgreementDetailInitiateRenewalAction.Result result = new PartnerAgreementDetailInitiateRenewalAction.Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }
}
