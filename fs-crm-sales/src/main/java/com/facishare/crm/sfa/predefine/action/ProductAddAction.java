package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.bizvalidator.BizValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.product.ProductStockValidator;
import com.facishare.crm.sfa.predefine.service.ProductCategoryBizService;
import com.facishare.crm.sfa.predefine.service.SpuSkuService;
import com.facishare.crm.sfa.predefine.service.model.MultiUnitData;
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel;
import com.facishare.crm.sfa.predefine.service.model.SearchUsedSpecModel;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl;
import com.facishare.crm.sfa.utilities.common.convert.ConvertUtil;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.constant.SpuSkuConstants;
import com.facishare.crm.sfa.utilities.util.DhtUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.JsonUtil;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.validator.ProductCategoryV2Validator;
import com.facishare.crm.sfa.utilities.validator.ProductValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ObjectLockStatus;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.LicenseServiceImpl;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by luxin on 2018/1/16.
 */
@Slf4j
public class ProductAddAction extends StandardAddAction {
    private final SpuSkuService spuSkuService = SpringUtil.getContext().getBean(com.facishare.crm.sfa.predefine.service.SpuSkuService.class);
    private final com.facishare.crm.sfa.predefine.service.real.SpuSkuService bizSpuSkuService = SpringUtil
            .getContext().getBean(com.facishare.crm.sfa.predefine.service.real.SpuSkuServiceImpl.class);
    private final List<Map<String, Object>> spuSpecAndValueInfoList = Lists.newArrayList();
    private final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitServiceImpl.class);
    private List<MultiUnitData.MultiUnitItem> multiUnitItems = null;
    private boolean isSpuOpen;
    private final LicenseService licenseService = SpringUtil.getContext().getBean(LicenseServiceImpl.class);
    private final ProductCategoryBizService productCategoryBizService = SpringUtil.getContext().getBean(ProductCategoryBizService.class);
    private final ProductCategoryV2Validator productCategoryV2Validator = SpringUtil.getContext().getBean(ProductCategoryV2Validator.class);
    private IObjectData spuData;
    private IObjectData skuData;
    private boolean needGenerateSpu;
    private boolean hasSpec;
    private String generateSpuId;
    private List<IObjectData> generatedSpuList;
    private List<IObjectData> generatedSpecList;
    private List<IObjectData> generatedSpecValueList;
    private String specSet;

    @Override
    protected void before(Arg arg) {
        productCategoryBizService.handleCategoryMappingCategoryId(actionContext.getUser(), arg.getObjectData().toObjectData(), SFAPreDefineObject.Product.getApiName());
        log.info("ProductAddAction>before()>arg={}" + JsonUtil.toJsonWithNullValues(arg));
        isSpuOpen = SFAConfigUtil.isSpuOpen(actionContext.getTenantId());
        generateSpuAndSpec();
        try {
            super.before(arg);
            // 产品分类字段上不可是商城分类，商城分类字段上值不允许是产品分类
            productCategoryV2Validator.checkCategoryParamOfProduct(actionContext.getTenantId(), objectDescribe, arg.getObjectData());
            productCategoryV2Validator.checkCategoryIsLeafNode(actionContext.getUser(), arg.getObjectData());
            ProductValidator.validateSerialNumberAndMultiUnit(objectData);
            if (!Objects.isNull(objectData.get("is_multiple_unit")) &&
                    BooleanUtils.isTrue((Boolean) objectData.get("is_multiple_unit"))) {
                multiUnitItems = multiUnitService.preprocessMultiUnit(ObjectDataDocument.of(objectData));
                if (multiUnitItems == null) {
                    //throw new ValidateException("多单位信息必填");
                    throw new ValidateException(I18N.text("sfa.multi.info.isrequired"));
                } else {
                    Set<String> module = licenseService.getModule(actionContext.getTenantId());
                    if (multiUnitItems != null && module.contains("kx_peculiarity") && multiUnitItems.size() > 3) {
                        if (!GrayUtil.isGrayMultiUnitCount(actionContext.getTenantId())) {
                            //throw new ValidateException("开启快销企业多单位数量超过上限3个");
                            throw new ValidateException(I18N.text("sfa.open.kuaixiao.multidata.limit"));
                        }
                    }
                    if (multiUnitItems.size() > 20) {
                        //throw new ValidateException("多单位数量超过上限20个");
                        throw new ValidateException(I18N.text("sfa.multidata.limit"));
                    }
                    multiUnitService.checkMultiUnitAndGetUnitInfo(actionContext.getTenantId(), multiUnitItems, (SelectOne) objectDescribe.getFieldDescribe("unit"));
                }
            }
            ValidatorContext validatorContext = ValidatorContext.builder()
                    .action(ObjectAction.CREATE)
                    .user(actionContext.getUser())
                    .describeApiName(objectDescribe.getApiName())
                    .objectData(objectData)
                    .build();
            BizValidator.build().withContext(validatorContext).with(new ProductStockValidator()).doValidate();
            ProductValidator.handlePeriodicProduct(actionContext.getUser(), Lists.newArrayList(objectData));
        } catch (Exception e) {
            deleteGeneratedData();
            throw e;
        }
    }


    @Override
    protected void init() {
        super.init();

        String tenantId = actionContext.getTenantId();
        objectData.set(Tenantable.TENANT_ID, tenantId);
        arg.getDetails().forEach((k, v) -> {
            if (CollectionUtils.notEmpty(v)) {
                v.forEach(o -> o.put(Tenantable.TENANT_ID, tenantId));
            }
        });
        if (CollectionUtils.empty(objectData.getOwner())) {
            ArrayList<String> owners = Lists.newArrayList(actionContext.getUser().getUpstreamOwnerIdOrUserId());
            objectData.setOwner(owners);
            arg.getDetails().forEach((k, v) -> {
                if (CollectionUtils.notEmpty(v)) {
                    v.forEach(o -> o.put("owner", owners));
                }
            });
        }
        if (Objects.isNull(objectData.get("is_multiple_unit"))) {
            objectData.set("is_multiple_unit", false);
        }
    }


    @Override
    protected void validate() {
        if (!isSpuOpen || needSkipValidateSpec()) {
            super.validate();
            return;
        }

        validateSpec();
    }

    /**
     * 此处增加数据同步特殊处理，为了兼容无规格商品的数据同步
     *
     * @return boolean true 跳过校验 false 不跳过校验
     */
    private boolean needSkipValidateSpec() {
        if ("fs-sync-data-all".equals(RequestContextManager.getContext().getPeerName())) {
            List<Map<String, String>> inputSpecAndValueList = objectData.get("spec_and_value", List.class);
            // 数据同步，无规格值，不校验
            return CollectionUtils.empty(inputSpecAndValueList);
        }
        return false;
    }

    @Override
    protected void doSaveData() {
        // 特殊处理字段
        ConvertUtil.convertOrderFieldValue2Number(objectData);
        resetShelfTime();
        //特殊处理OpenApi调用,创建产品时候，创建一个与产品数据相同的商品。
        super.doSaveData();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (CollectionUtils.notEmpty(spuSpecAndValueInfoList)) {
            List<IObjectData> skuSpecRelateDataList = spuSpecAndValueInfoList.stream().map(o -> {
                o.put("sku_id", objectData.getId());
                return new ObjectData(o);
            }).collect(Collectors.toList());
            serviceFacade.bulkSaveObjectData(skuSpecRelateDataList, actionContext.getUser());
        }
        if (multiUnitItems != null) {
            multiUnitService.saveMultiUnit4SkuAdd(actionContext.getUser(), objectData, multiUnitItems);
        }
        // 同步生成价目表产品
        bizSpuSkuService.asynchronousCreatePriceBookProduct(actionContext.getUser(), Lists.newArrayList(objectData));
        return super.after(arg, result);
    }

    private void resetShelfTime() {
        long productStatusUpdateTime = System.currentTimeMillis();
        String productStatus = objectData.get("product_status", String.class);
        if (ProductConstants.Status.OFF.getStatus().equals(productStatus)) {
            objectData.set("off_shelves_time", productStatusUpdateTime);
        } else {
            objectData.set("on_shelves_time", productStatusUpdateTime);
        }
    }

    private void validateSpec() {
        @SuppressWarnings("unchecked")
        List<Map<String, String>> inputSpecAndValueList = objectData.get("spec_and_value", List.class);
        // 校验数据是否有规格值
        if (CollectionUtils.empty(inputSpecAndValueList)) {
            throw new ValidateException(I18N.text("product.specification.validate.msg"));
        }

        validateSpuSpec();
        List<SearchUsedSpecModel.SpecAndSpecValue> dataList = getSpuRelatedSpecAndValue(spuData.getId());

        if (dataList.size() < inputSpecAndValueList.size()) {
            throw new ValidateException(I18N.text("product.spec.param.error.msg"));
        }
        List<String> existSpecIds = dataList.stream().map(SearchUsedSpecModel.SpecAndSpecValue::getSpecId).collect(Collectors.toList());
        validateSpecIdValue(existSpecIds, inputSpecAndValueList);
        ProductValidator.validateSpecStatusActive(actionContext.getTenantId(), Lists.newArrayList(objectData));

        String rawSpecSpecValueSqlItem = "(r.spec_id='%s' and spec_value_id='%s')";
        List<String> specSpecValueSqlPart = Lists.newArrayListWithCapacity(inputSpecAndValueList.size());

        int expectedSize = dataList.size();
        List<String> productSpecStrList = initListWithExpectedSize(expectedSize);
        List<String> specValueStrList = initListWithExpectedSize(expectedSize);
        Set<String> specValueIds = Sets.newHashSetWithExpectedSize(expectedSize);

        // 顺序是否正确,规格值组合是否被使用
        for (int i = 0; i < dataList.size(); i++) {
            SearchUsedSpecModel.SpecAndSpecValue specAndValue = dataList.get(i);
            Optional<Map<String, String>> mapOptional = inputSpecAndValueList.stream()
                    .filter(r -> specAndValue.getSpecId().equals(r.get("spec_id")))
                    .findFirst();
            if (!mapOptional.isPresent()) {
                continue;
            }
            Map<String, String> inputSpecAndValue = mapOptional.get();
            String inputSpecValueId = inputSpecAndValue.get("spec_value_id");
            if (Strings.isNullOrEmpty(inputSpecValueId)) {
                continue;
            }
            inputSpecAndValue.put("order_field", String.valueOf(i));
            Map<String, String> specValueIdAndSpecValueNameMapping = Maps.newHashMap();
            specAndValue.getSpecValueList().forEach(o -> specValueIdAndSpecValueNameMapping.put(o.getSpecValueId(), o.getSpecValueName()));
            String specValueName = specValueIdAndSpecValueNameMapping.get(inputSpecValueId);
            if (specValueName == null) {
                throw new ValidateException(I18N.text("product.specification.value.not.exist.msg"));
            }
            specValueIds.add(inputSpecValueId);
            productSpecStrList.set(i, specAndValue.getSpecName() + ":" + specValueName);
            specValueStrList.set(i, specValueName);
            String specSpecValueSqlItem = String.format(rawSpecSpecValueSqlItem, specAndValue.getSpecId(), inputSpecValueId);
            specSpecValueSqlPart.add(specSpecValueSqlItem);
            Map<String, Object> skuSpecMapping = Maps.newHashMapWithExpectedSize(7);
            skuSpecMapping.put("spu_id", spuData.getId());
            skuSpecMapping.put("spec_id", inputSpecAndValue.get("spec_id"));
            skuSpecMapping.put("spec_value_id", inputSpecAndValue.get("spec_value_id"));
            skuSpecMapping.put("order_field", inputSpecAndValue.get("order_field"));
            skuSpecMapping.put("object_describe_api_name", "SpuSkuSpecValueRelateObj");
            skuSpecMapping.put("object_describe_id", "XXXX");
            skuSpecMapping.put(Tenantable.TENANT_ID, actionContext.getTenantId());
            spuSpecAndValueInfoList.add(skuSpecMapping);
        }
        productSpecStrList.removeIf(r -> Strings.isNullOrEmpty(r));
        specValueStrList.removeIf(r -> Strings.isNullOrEmpty(r));
        ProductValidator.validateSpecValueExistSubset(actionContext.getTenantId(), spuData.getId(), specValueIds);

        if (Strings.isNullOrEmpty(specSet)) {
            objectData.set("product_spec", Joiner.on(";").join(productSpecStrList));
        } else {
            objectData.set("product_spec", specSet);
        }
        if (Strings.isNullOrEmpty(objectData.getName()) || spuData.getName().equals(objectData.getName())) {
            objectData.set("name", String.format("%s[%s]", spuData.getName(), Strings.isNullOrEmpty(specSet) ? Joiner.on("-").join(specValueStrList) : specSet));
        }
        objectData.set("batch_sn", spuData.get("batch_sn"));
    }

    private List<String> initListWithExpectedSize(int expectedSize) {
        List<String> list = Lists.newArrayListWithExpectedSize(expectedSize);
        for (int i = 0; i < expectedSize; i++) {
            list.add("");
        }
        return list;
    }

    private void validateSpecIdValue(List<String> existSpecIds, List<Map<String, String>> inputSpecAndValueList) {
        boolean hasSpecValue = false;
        for (Map<String, String> map : inputSpecAndValueList) {
            String specId = map.get("spec_id");
            if (Strings.isNullOrEmpty(specId)) {
                throw new ValidateException(I18N.text("product.spec.param.error.msg"));
            }
            if (!existSpecIds.contains(specId)) {
                throw new ValidateException(I18N.text("product.spec.param.error.msg"));
            }
            if (!Strings.isNullOrEmpty(map.get("spec_value_id"))) {
                hasSpecValue = true;
            }
        }
        if (!hasSpecValue) {
            throw new ValidateException(I18N.text("product.spec.param.error.msg"));
        }
    }

    private void validateSpuSpec() {
        // 商品是否有规格
        String spuId = objectData.get("spu_id", String.class);
        if (Strings.isNullOrEmpty(spuId)) {
            throw new ValidateException(I18N.text("product.param.error.msg"));
        }
        spuData = serviceFacade.findObjectData(actionContext.getUser(), spuId, Utils.SPU_API_NAME);
        if (null == spuData || spuData.get("is_spec") == null || !(boolean) spuData.get("is_spec")) {
            throw new ValidateException(I18N.text("product.associated.spu.error.msg"));
        }
    }

    private List<SearchUsedSpecModel.SpecAndSpecValue> getSpuRelatedSpecAndValue(String spuId) {
        List<SearchUsedSpecModel.SpecAndSpecValue> specAndSpecValueList = Lists.newArrayList();
        // 规格&规格值是否存在
        SearchUsedSpecModel.Arg searchSpecAndSpecArg = SearchUsedSpecModel.Arg.builder().spuId(spuId).isIncludeAll(Boolean.TRUE).build();
        SearchUsedSpecModel.Result searchSpecAndSpecResult = spuSkuService.searchUsedSpecValuesBySpuId(
                searchSpecAndSpecArg,
                new ServiceContext(actionContext.getRequestContext(), null, null)
        );
        if (null != searchSpecAndSpecResult && CollectionUtils.notEmpty(searchSpecAndSpecResult.getDataList())) {
            return searchSpecAndSpecResult.getDataList();
        }
        return specAndSpecValueList;
    }

    /*
    ERP同步自动生成商品及规格
     */
    private void generateSpuAndSpec() {
        needGenerateSpu = isSpuOpen && GrayUtil.autoGenerateSpu(actionContext.getTenantId()) && DhtUtil.isFromErp(actionContext.getPeerName());
        if (!needGenerateSpu) {
            return;
        }
        skuData = arg.getObjectData().toObjectData();
        if (CollectionUtils.empty(skuData.getOwner())) {
            skuData.setOwner(Lists.newArrayList(actionContext.getUser().getUpstreamOwnerIdOrUserId()));
        }
        String spuName = skuData.getName();
        List<IObjectData> spuDataList = findDataByName(SFAPreDefineObject.SPU.getApiName(), Lists.newArrayList(spuName));
        specSet = skuData.get(ProductConstants.PRODUCT_SPEC, String.class);
        Map<String, String> specValueMap = Maps.newHashMap(); //规格-规格值
        Map<String, Integer> specOrderMap = Maps.newHashMap(); //规格顺序
        List<IObjectData> specDataList = Lists.newArrayList();
        List<IObjectData> specValueDataList = Lists.newArrayList();
        parseSpecAndValue(specSet, specValueMap, specOrderMap);
        if (CollectionUtils.notEmpty(specValueMap)) {
            specDataList = findDataByName(SFAPreDefineObject.Specification.getApiName(), Lists.newArrayList(specValueMap.keySet()));
            specValueDataList = findSpecValueByName(specValueMap, specDataList);
        }
        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(actionContext.getTenantId(),
                Lists.newArrayList(SFAPreDefineObject.SPU.getApiName(), SFAPreDefineObject.Specification.getApiName(), SFAPreDefineObject.SpecificationValue.getApiName()));
        if (CollectionUtils.empty(spuDataList)) {
            saveSpu(describeMap.get(SFAPreDefine.SPU.getApiName()), skuData, spuName);
        } else {
            IObjectData innerSpuData = spuDataList.get(0);
            Boolean isSpec = innerSpuData.get(SpuSkuConstants.MULTIUNITRELEATED_IS_SPEC, Boolean.class);
            if (!Boolean.TRUE.equals(isSpec)) {
                throw new ValidateException(I18N.text("product.associated.spu.error.msg"));
            }
            skuData.set(ProductConstants.PRODUCT_SPU_ID, innerSpuData.getId());
        }
        if (hasSpec) {
            List<String> owner = skuData.getOwner();
            checkAndSaveSpec(describeMap, owner, specValueMap, specDataList, specValueDataList, specOrderMap);
        }
    }

    @Nullable
    private List<IObjectData> findSpecValueByName(Map<String, String> specValueMap, List<IObjectData> specDataList) {
        //存在规格的情况，才去查找规格值，规格都没有，肯定没有规格值
        if (CollectionUtils.empty(specDataList)) {
            return Lists.newArrayList();
        }
        List<IObjectData> specValueDataList = findDataByName(SFAPreDefineObject.SpecificationValue.getApiName(), Lists.newArrayList(specValueMap.values()));
        if (CollectionUtils.notEmpty(specValueDataList)) {
            //去掉不是当前查询的规格值，规格值存在重名情况，需要用规格id过滤
            Set<String> specIdSet = specDataList.stream().map(DBRecord::getId).collect(Collectors.toSet());
            specValueDataList.removeIf(specValueData -> !specIdSet.contains(specValueData.get("specification_id", String.class)));
        }
        return specValueDataList;
    }

    private void deleteGeneratedData() {
        if (CollectionUtils.notEmpty(generatedSpuList)) {
            serviceFacade.bulkDeleteDirect(generatedSpuList, actionContext.getUser());
        }
        if (CollectionUtils.notEmpty(generatedSpecValueList)) {
            serviceFacade.bulkDeleteDirect(generatedSpecValueList, actionContext.getUser());
        }
        if (CollectionUtils.notEmpty(generatedSpecList)) {
            serviceFacade.bulkDeleteDirect(generatedSpecList, actionContext.getUser());
        }
    }

    private void modifySkuSpec(Map<String, String> specValueMap, Map<String, String> specIdMap,
                               HashBasedTable<String, String, String> specValueNameToSpecNameTable,
                               Map<String, Integer> specOrderMap) {
        List<Map<String, String>> specList = Lists.newArrayList();
        for (Map.Entry<String, String> entry : specValueMap.entrySet()) {
            Map<String, String> map = Maps.newHashMap();
            map.put("spec_id", specIdMap.get(entry.getKey()));
            String spedValueId = specValueNameToSpecNameTable.get(entry.getValue(), entry.getKey());
            map.put("spec_value_id", spedValueId);
            map.put("order_field", specOrderMap.get(entry.getKey()).toString());
            specList.add(map);
        }
        skuData.set("spec_and_value", specList);
    }

    private void saveSpuRelatedSpec(Map<String, String> specValueMap, Map<String, String> specIdMap,
                                    HashBasedTable<String, String, String> specValueNameToSpecNameTable,
                                    Map<String, Integer> specOrderMap) {
        List<IObjectData> specRelatedDataList = Lists.newArrayList();
        IObjectData data;
        for (Map.Entry<String, String> entry : specValueMap.entrySet()) {
            data = new ObjectData();
            data.setDescribeApiName("SpuSkuSpecValueRelateObj");
            data.set("spec_id", specIdMap.get(entry.getKey()));
            String spedValueId = specValueNameToSpecNameTable.get(entry.getValue(), entry.getKey());
            data.set("spec_value_id", spedValueId);
            data.set("spu_id", generateSpuId);
            data.set("order_field", specOrderMap.get(entry.getKey()).toString());
            data.setTenantId(actionContext.getTenantId());
            specRelatedDataList.add(data);
        }
        serviceFacade.bulkSaveObjectData(specRelatedDataList, actionContext.getUser());
    }

    private void checkAndSaveSpec(Map<String, IObjectDescribe> describeMap, List<String> owner, Map<String, String> specValueMap,
                                  List<IObjectData> specDataList, List<IObjectData> specValueDataList, Map<String, Integer> specOrderMap) {
        Map<String, String> specIdMap = Maps.newHashMap();
        Map<String, String> idToSpecNameMap = Maps.newHashMap();
        //规格值名称->规格名称->规格值Id
        HashBasedTable<String, String, String> specValueNameToSpecNameTable = HashBasedTable.create();
        List<String> specNameList = Lists.newArrayList();
        List<Tuple<String, String>> specValueNameList = Lists.newArrayList();
        for (IObjectData objectData : specDataList) {
            specIdMap.put(objectData.getName(), objectData.getId());
            idToSpecNameMap.put(objectData.getId(), objectData.getName());
        }
        for (IObjectData objectData : specValueDataList) {
            String specificationName = idToSpecNameMap.get(objectData.get("specification_id", String.class));
            specValueNameToSpecNameTable.put(objectData.getName(), specificationName, objectData.getId());
        }
        //循环入参的规格，如果规格和规格值不存在，则保存
        for (Map.Entry<String, String> entry : specValueMap.entrySet()) {
            if (Objects.isNull(specIdMap.get(entry.getKey()))) {
                specNameList.add(entry.getKey());
            }
            if (Objects.isNull(specValueNameToSpecNameTable.get(entry.getValue(), entry.getKey()))) {
                specValueNameList.add(new Tuple<>(entry.getValue(), entry.getKey()));
            }
        }
        saveSpec(describeMap.get(SFAPreDefine.Specification.getApiName()), owner, specNameList, specIdMap);
        saveSpecValue(describeMap.get(SFAPreDefine.SpecificationValue.getApiName()), owner, specIdMap, specValueNameList, specValueNameToSpecNameTable);
        if (!Strings.isNullOrEmpty(generateSpuId)) {
            saveSpuRelatedSpec(specValueMap, specIdMap, specValueNameToSpecNameTable, specOrderMap);
        }
        modifySkuSpec(specValueMap, specIdMap, specValueNameToSpecNameTable, specOrderMap);
    }

    private void saveSpec(IObjectDescribe describe, List<String> owner, List<String> specNameList, Map<String, String> specIdMap) {
        if (CollectionUtils.empty(specNameList)) {
            return;
        }
        List<IObjectData> specDataList = Lists.newArrayList();
        IObjectData data;
        String id;
        for (String name : specNameList) {
            data = new ObjectData();
            data.setName(name);
            id = serviceFacade.generateId();
            data.setId(id);
            data.setRecordType("default__c");
            data.setOwner(owner);
            data.setDescribeApiName(SFAPreDefineObject.Specification.getApiName());
            specIdMap.put(name, id);
            fillDefaultInfo(data, describe);
            specDataList.add(data);
        }
        generatedSpecList = serviceFacade.bulkSaveObjectData(specDataList, actionContext.getUser());
    }

    private void saveSpecValue(IObjectDescribe describe, List<String> owner,
                               Map<String, String> specIdMap, List<Tuple<String, String>> specValueNameList, HashBasedTable<String, String, String> specValueNameToSpecNameTable) {
        if (CollectionUtils.empty(specValueNameList)) {
            return;
        }
        List<IObjectData> specValueDataList = Lists.newArrayList();
        IObjectData data;
        String id;
        for (Tuple<String, String> specValueNameToSpecName : specValueNameList) {
            data = new ObjectData();
            data.setName(specValueNameToSpecName.getKey());
            id = serviceFacade.generateId();
            data.setId(id);
            data.setRecordType("default__c");
            data.setOwner(owner);
            data.setDescribeApiName(SFAPreDefineObject.SpecificationValue.getApiName());
            specValueNameToSpecNameTable.put(specValueNameToSpecName.getKey(), specValueNameToSpecName.getValue(), id);
            data.set("specification_id", specIdMap.getOrDefault(specValueNameToSpecName.getValue(), ""));
            fillDefaultInfo(data, describe);
            specValueDataList.add(data);
        }
        generatedSpecValueList = serviceFacade.bulkSaveObjectData(specValueDataList, actionContext.getUser());
    }

    @NotNull
    private String findSpecName(List<Tuple<String, String>> specValueReverseMap, String name) {
        return specValueReverseMap.stream()
                .filter(tuple -> tuple.getKey().equals(name))
                .findFirst()
                .map(Tuple::getValue)
                .orElse("");
    }

    private void saveSpu(IObjectDescribe describe, IObjectData skuData, String name) {
        IObjectData data = generateSpuData(skuData, name);
        fillDefaultInfo(data, describe);
        generatedSpuList = serviceFacade.bulkSaveObjectData(Lists.newArrayList(data), actionContext.getUser());
    }

    private void fillDefaultInfo(IObjectData data, IObjectDescribe describe) {
        fillSystemInfo(data, describe);
        setDefaultTeamMember(data);
    }

    private void fillSystemInfo(IObjectData data, IObjectDescribe describe) {
        data.setTenantId(actionContext.getTenantId());
        data.setCreatedBy(actionContext.getUser().getUserIdOrOutUserIdIfOutUser());
        data.setLastModifiedBy(actionContext.getUser().getUserIdOrOutUserIdIfOutUser());
        data.setCreateTime(System.currentTimeMillis());
        data.set(ObjectLockStatus.LOCK_STATUS_API_NAME, ObjectLockStatus.UNLOCK.getStatus());
        data.set(ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectLifeStatus.NORMAL.getCode());
    }

    private IObjectData generateSpuData(IObjectData skuData, String name) {
        IObjectData data = new ObjectData();
        generateSpuId = serviceFacade.generateId();
        data.setId(generateSpuId);
        skuData.set(ProductConstants.PRODUCT_SPU_ID, generateSpuId);
        data.setName(name);
        data.set(SpuSkuConstants.MULTIUNITRELEATED_IS_SPEC, hasSpec);
        data.set(SpuSkuConstants.MULTIUNITRELEATED_STANDARD_PRICE, skuData.get(ProductConstants.PRODUCT_PRICE));
        data.set(SpuSkuConstants.MULTIUNITRELEATED_SPU_UNIT, skuData.get(ProductConstants.UNIT));
        data.set(ProductConstants.CATEGORY, skuData.get(ProductConstants.CATEGORY));
        data.set(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID, skuData.get(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID));
        data.set(ProductCategoryModel.Filed.SHOP_CATEGORY_ID, skuData.get(ProductCategoryModel.Filed.SHOP_CATEGORY_ID));
        data.set("product_line", skuData.get("product_line"));
        data.set("batch_sn", skuData.get("batch_sn"));
        data.setRecordType(skuData.getRecordType());
        data.set(SpuSkuConstants.MULTIUNITRELEATED_IS_MULTIPLE_UNIT, skuData.get(SpuSkuConstants.MULTIUNITRELEATED_IS_MULTIPLE_UNIT));
        data.setOwner(skuData.getOwner());
        data.setDataOwnDepartment(skuData.getDataOwnDepartment());
        data.setDescribeApiName(SFAPreDefineObject.SPU.getApiName());
        return data;
    }

    /*
    拆分规格信息，示例：颜色:红色;内存:128G
     */
    private void parseSpecAndValue(String specSet, Map<String, String> specValueMap, Map<String, Integer> specOrderMap) {
        if (Strings.isNullOrEmpty(specSet)) {
            hasSpec = false;
            return;
        }
        String[] specArray = specSet.split(";");
        int i = 0;
        for (String spec : specArray) {
            String[] valueArray = spec.split(":");
            if (valueArray.length == 2) {
                specValueMap.put(valueArray[0], valueArray[1]);
                specOrderMap.put(valueArray[0], i);
                i++;
            }
        }
        hasSpec = true;
    }

    private List<IObjectData> findDataByName(String describeName, List<String> nameList) {
        SearchTemplateQuery query = buildSearchQuery(nameList);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(actionContext.getUser(), describeName, query);
        if (queryResult == null || CollectionUtils.empty(queryResult.getData())) {
            return Lists.newArrayList();
        } else {
            return queryResult.getData();
        }
    }

    private SearchTemplateQuery buildSearchQuery(List<String> nameList) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName(IObjectData.NAME);
        filter.setFieldValues(nameList);
        filter.setOperator(Operator.IN);
        filters.add(filter);
        query.setFilters(filters);
        query.setOffset(0);
        query.setPermissionType(0);
        query.setNeedReturnCountNum(Boolean.FALSE);
        query.setNeedReturnQuote(Boolean.FALSE);
        return query;
    }
}
