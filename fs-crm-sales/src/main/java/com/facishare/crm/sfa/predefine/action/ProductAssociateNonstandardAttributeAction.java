package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_PRODUCT_RELATED_NONSTANDARD_ATTRIBUTE_LIMIT;

/**
 * Created by luxin on 2017/12/7.
 */
@Slf4j
public class ProductAssociateNonstandardAttributeAction extends PreDefineAction<ProductAssociateNonstandardAttributeAction.Arg, ProductAssociateNonstandardAttributeAction.Result> {
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("AssociateNonstandardAttribute");
    }

    @Override
    protected List<String> getDataPrivilegeIds(ProductAssociateNonstandardAttributeAction.Arg arg) {
        return arg.getProductIds();
    }

    @Override
    protected ProductAssociateNonstandardAttributeAction.Result doAct(ProductAssociateNonstandardAttributeAction.Arg arg) {
        if (CollectionUtils.isNotEmpty(arg.productIds) && CollectionUtils.isNotEmpty(arg.nonstandardAttributeIds)) {
            List<String> updateFieldList = new ArrayList<>();
            for (IObjectData product : dataList) {
                List<String> relatedAttributeIds = product.get("nonstandard_attribute_ids", ArrayList.class);
                String dbDefaultValuesStr = product.get("non_attribute_values", String.class);
                Map<String, BigDecimal> dbDefaultValues = Strings.isNullOrEmpty(dbDefaultValuesStr) ? Maps.newHashMap() : JSON.parseObject(dbDefaultValuesStr, Map.class);
                //前端传过来的默认值，按产品取
                Map<String, BigDecimal> defaultValues = Optional.ofNullable(arg.getDefaultValues())
                    .map(values -> values.get(product.getId()))
                    .orElseGet(Maps::newHashMap);
                for (String nonstandardAttributeId : arg.getNonstandardAttributeIds()) {
                    if (relatedAttributeIds == null) {
                        relatedAttributeIds = new ArrayList<>();
                    }
                    if (!relatedAttributeIds.contains(nonstandardAttributeId)) {
                        relatedAttributeIds.add(nonstandardAttributeId);
                    }
                    if(defaultValues.get(nonstandardAttributeId) != null) {
                        dbDefaultValues.put(nonstandardAttributeId, defaultValues.get(nonstandardAttributeId));
                    } else {//清空的情况
                        dbDefaultValues.remove(nonstandardAttributeId);
                    }
                }
                if (relatedAttributeIds.size() > 50) {
                    throw new ValidateException(I18N.text(SFA_PRODUCT_RELATED_NONSTANDARD_ATTRIBUTE_LIMIT));
                }
                if (!relatedAttributeIds.isEmpty()) {
                    product.set("nonstandard_attribute_ids", relatedAttributeIds);
                    if (!updateFieldList.contains("nonstandard_attribute_ids")) {
                        updateFieldList.add("nonstandard_attribute_ids");
                    }
                }
                if(!dbDefaultValues.isEmpty()){
                    product.set("non_attribute_values", JSON.toJSONString(dbDefaultValues));
                    if (!updateFieldList.contains("non_attribute_values")) {
                        updateFieldList.add("non_attribute_values");
                    }
                }
            }

            if (!dataList.isEmpty()) {
                serviceFacade.batchUpdateByFields(actionContext.getUser(), dataList, updateFieldList);
            }
        }
        return ProductAssociateNonstandardAttributeAction.Result.builder().errorCode("0").value(true).build();
    }
    @Override
    protected ProductAssociateNonstandardAttributeAction.Result after(ProductAssociateNonstandardAttributeAction.Arg arg, ProductAssociateNonstandardAttributeAction.Result result){
        /*String logContent = String.format("状态，原状态: 禁用 ,被更改为: 启用 状态");
        serviceFacade.logWithCustomMessage(actionContext.getUser(), EventType.MODIFY, ActionType.MODIFY, objectDescribe, dataList, logContent);
        */return super.after(arg, result);
    }

    @Data
    public static class Arg {
        @JsonProperty("product_ids")
        private List<String> productIds;

        @JsonProperty("nonstandard_attribute_ids")
        private List<String> nonstandardAttributeIds;

        @JsonProperty("default_values")
        private Map<String, Map<String, BigDecimal>> defaultValues;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String errorDetail;
        private String errorCode;
        private String message;
        private Boolean value;

        public boolean isSuccess() {
            return "0".equals(errorCode) && value;
        }
    }
}
