package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.attribute.AttributeRangeService;
import com.facishare.crm.sfa.predefine.service.cpq.BomConstraintService;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by luxin on 2017/12/7.
 */
@Slf4j
public class ProductDisAssociateNonstandardAttributeAction extends PreDefineAction<ProductDisAssociateNonstandardAttributeAction.Arg, ProductDisAssociateNonstandardAttributeAction.Result> {
    private final BomConstraintService bomConstraintService = SpringUtil.getContext().getBean(BomConstraintService.class);
    private final AttributeRangeService attributeRangeService = SpringUtil.getContext().getBean(AttributeRangeService.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("DisAssociateNonstandardAttribute");
    }

    @Override
    protected List<String> getDataPrivilegeIds(ProductDisAssociateNonstandardAttributeAction.Arg arg) {
        return arg.getProductIds();
    }

    @Override
    protected ProductDisAssociateNonstandardAttributeAction.Result doAct(ProductDisAssociateNonstandardAttributeAction.Arg arg) {
        if (CollectionUtils.isNotEmpty(arg.productIds) && CollectionUtils.isNotEmpty(arg.nonstandardAttributeIds)) {
            ArrayListMultimap<String, String> dataMap = ArrayListMultimap.create();
            List<String> updateFieldList = new ArrayList<>();
            for (IObjectData product : dataList) {
                String dbDefaultValuesStr = product.get("non_attribute_values", String.class);
                Map<String, BigDecimal> dbDefaultValues = Strings.isNullOrEmpty(dbDefaultValuesStr) ? Maps.newHashMap() : JSON.parseObject(dbDefaultValuesStr, Map.class);

                List<String> relatedAttributeIds = product.get("nonstandard_attribute_ids", ArrayList.class);
                for (String attributeId : arg.getNonstandardAttributeIds()) {
                    if (relatedAttributeIds != null) {
                        boolean flag = relatedAttributeIds.remove(attributeId);
                        if (flag) {
                            dataMap.put(product.getId(),attributeId);
                            //移除默认值
                            dbDefaultValues.remove(attributeId);
                        }
                    }
                }
                if (relatedAttributeIds != null) {
                    product.set("nonstandard_attribute_ids", relatedAttributeIds.isEmpty() ? null : relatedAttributeIds);
                    if (!updateFieldList.contains("nonstandard_attribute_ids")) {
                        updateFieldList.add("nonstandard_attribute_ids");
                    }
                    product.set("non_attribute_values", JSON.toJSONString(dbDefaultValues));
                    if (!updateFieldList.contains("non_attribute_values")) {
                        updateFieldList.add("non_attribute_values");
                    }
                }
            }
            bomConstraintService.checkBomConstraintAttr(actionContext.getUser(),dataMap,false);
            if (!dataList.isEmpty()) {
                serviceFacade.batchUpdateByFields(actionContext.getUser(), dataList, updateFieldList);
            }
            attributeRangeService.deleteAttrRangeRelation(actionContext.getUser(),arg.getProductIds(),arg.getNonstandardAttributeIds(), Utils.NONSTANDARD_ATTRIBUTE_OBJ_API_NAME);
        }

        return ProductDisAssociateNonstandardAttributeAction.Result.builder().errorCode("0").value(true).build();
    }
    @Override
    protected ProductDisAssociateNonstandardAttributeAction.Result after(ProductDisAssociateNonstandardAttributeAction.Arg arg, ProductDisAssociateNonstandardAttributeAction.Result result){
        return super.after(arg, result);
    }

    @Data
    public static class Arg {
        @JsonProperty("product_ids")
        private List<String> productIds;

        @JsonProperty("nonstandard_attribute_ids")
        private List<String> nonstandardAttributeIds;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String errorDetail;
        private String errorCode;
        private String message;
        private Boolean value;

        public boolean isSuccess() {
            return "0".equals(errorCode) && value;
        }
    }
}
