package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.bizvalidator.BizValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.product.ProductStockValidator;
import com.facishare.crm.sfa.predefine.service.ProductCategoryBizService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreService;
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreServiceImpl;
import com.facishare.crm.sfa.predefine.service.model.MultiUnitData;
import com.facishare.crm.sfa.predefine.service.model.MultiUnitListData;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl;
import com.facishare.crm.sfa.predefine.service.real.SpuSkuService;
import com.facishare.crm.sfa.predefine.service.real.SpuSkuServiceImpl;
import com.facishare.crm.sfa.predefine.service.task.ProductStatusChangeTaskService;
import com.facishare.crm.sfa.utilities.common.convert.ConvertUtil;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.proxy.StockProxy;
import com.facishare.crm.sfa.utilities.proxy.model.CheckIsAllowModifySpuBatchSNRestModel;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.crm.sfa.utilities.validator.ProductCategoryV2Validator;
import com.facishare.crm.sfa.utilities.validator.ProductValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.LicenseServiceImpl;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * Created by luxin on 2018/1/16.
 */
@Slf4j
public class ProductEditAction extends StandardEditAction {
    private final SpuSkuService skuService = SpringUtil.getContext().getBean(SpuSkuServiceImpl.class);
    private final StockProxy stockProxy = SpringUtil.getContext().getBean(StockProxy.class);
    private final BizConfigThreadLocalCacheService configService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitServiceImpl.class);
    private final LicenseService licenseService = SpringUtil.getContext().getBean(LicenseServiceImpl.class);
    private final ProductStatusChangeTaskService productStatusChangeTaskService = SpringUtil.getContext().getBean(ProductStatusChangeTaskService.class);
    private final ProductCategoryBizService productCategoryBizService = SpringUtil.getContext().getBean(ProductCategoryBizService.class);
    private final BomCoreService bomCoreService = SpringUtil.getContext().getBean(BomCoreServiceImpl.class);
    private final ProductCategoryV2Validator productCategoryV2Validator = SpringUtil.getContext().getBean(ProductCategoryV2Validator.class);


    private IObjectData beforeUpdateProductData;
    private boolean isSpuOpen;
    private boolean isCpqOpen;
    private boolean isChangeToMulti = false;
    private List<MultiUnitData.MultiUnitItem> multiUnitData;
    MultiUnitListData multiUnitListData;

    @Override
    protected void prepareMasterObjectData() {
        super.prepareMasterObjectData();
        beforeUpdateProductData = serviceFacade.findObjectData(actionContext.getTenantId(), arg.getObjectData().getId(), objectDescribe);
        updateShelvesTime4ProductStatusChanged(beforeUpdateProductData);
    }

    @Override
    protected void before(Arg arg) {
        boolean isMultiUnit = AccountUtil.getBooleanValue(arg.getObjectData(), "is_multiple_unit", false);
        super.before(arg);
        if (Objects.nonNull(updatedFieldMap.get("is_multiple_unit"))) {
            if (!(Boolean) updatedFieldMap.get("is_multiple_unit") && beforeUpdateProductData.get("is_multiple_unit", Boolean.class, false)) {
                updatedFieldMap.remove("is_multiple_unit");
                objectData.set("is_multiple_unit", true);
            }
        }
        productCategoryBizService.handleCategoryMappingCategoryId(actionContext.getUser(), dbMasterData,
                arg.getObjectData().toObjectData(), SFAPreDefineObject.Product.getApiName());
        productCategoryV2Validator.checkCategoryIsLeafNode(actionContext.getUser(), arg.getObjectData(), dbMasterData);
        isSpuOpen = SFAConfigUtil.isSpuOpen(actionContext.getTenantId());
        isCpqOpen = SFAConfigUtil.isCPQ(actionContext.getTenantId());

        if (multiUnitService.isOpenMultiUnit(actionContext.getTenantId()) && isMultiUnit) {
            multiUnitData = multiUnitService.preprocessMultiUnit(ObjectDataDocument.of(objectData));
            List<IObjectData> dbMultiUnitData = multiUnitService
                    .getMultiUnitInfoListByProduct(actionContext.getTenantId(), Lists.newArrayList(objectData.getId()));
            multiUnitListData = multiUnitService.compareMultiUnitData(multiUnitData, dbMultiUnitData);
            if (configService.isOpenMultiUnitPriceBook(actionContext.getTenantId())) {
                isChangeToMulti = multiUnitService.checkIsUpdatePricingUnit(multiUnitData, dbMultiUnitData);
            }
            Set<String> module = licenseService.getModule(actionContext.getTenantId());
            if (multiUnitData != null && module.contains("kx_peculiarity") && multiUnitData.size() > 3) {
                if (!GrayUtil.isGrayMultiUnitCount(actionContext.getTenantId())) {
                    //throw new ValidateException("开启快销企业多单位数量超过上限3个");
                    throw new ValidateException(I18N.text("sfa.open.kuaixiao.multidata.limit"));
                }
            }
            if (multiUnitData != null && multiUnitData.size() > 20) {
                //throw new ValidateException("多单位数量超过上限20个");
                throw new ValidateException(I18N.text("sfa.multidata.limit"));
            }
            if (!isSpuOpen) {
                updatedFieldMap.put(ProductConstants.PRODUCT_MULTI_UNIT_CALL_BACK_KEY, multiUnitListData);
            }
        }

        if (isCpqOpen) {
            Boolean isPackage = MapUtils.getBoolean(arg.getObjectData(), ProductConstants.IS_PACKAGE, false);
            bomCoreService.checkExistChildNode(actionContext.getUser(),objectData.getId(),isPackage);
        }

        if (isSpuOpen) {
            objectData.set("batch_sn", dbMasterData.get("batch_sn"));
        }
        ProductValidator.validateSerialNumberAndMultiUnit(objectData);
        if (!isSpuOpen) {
            if (isMultiUnit()) {
                multiUnitService.checkEditSkuMultiUnit(actionContext.getTenantId(), objectData.getId(), multiUnitData,
                        (SelectOne) objectDescribe.getFieldDescribe("unit"));
            }
            batchSNFieldValidate();
        }
        doERPValidate();
        ProductValidator.handlePeriodicProduct(actionContext.getUser(), Lists.newArrayList(objectData));
        ProductValidator.checkUpdatePricingMode(actionContext.getUser(), objectData, dbMasterData);
    }

    private void doERPValidate() {
        if(isSpuOpen && GrayUtil.autoGenerateSpu(actionContext.getTenantId()) && DhtUtil.isFromErp(actionContext.getPeerName())){
            //不支持修改规格值
            Set<String> updatedFields = updatedFieldMap.keySet();
            if(updatedFields.contains(ProductConstants.PRODUCT_SPEC)){
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_SPU_SPEC_VALUE_NOT_SUPPORT_MODIFY));
            }
        }
    }

    @Override
    protected void init() {
        super.init();
        if (Objects.isNull(objectData.get("is_multiple_unit"))) {
            objectData.set("is_multiple_unit", false);
        }
    }

    @Override
    protected void validate() {
        super.validate();
        ValidatorContext validatorContext = ValidatorContext.builder()
                .action(ObjectAction.CREATE)
                .user(actionContext.getUser())
                .describeApiName(objectDescribe.getApiName())
                .objectData(objectData)
                .build();
        BizValidator.build().withContext(validatorContext).with(new ProductStockValidator()).doValidate();
    }


    @Override
    public Result doAct(Arg arg) {
        // 特殊处理order_field字段,如果存在字符,将整个字符替换成数字
        ConvertUtil.convertOrderFieldValue2Number(objectData);


        log.info("ProductEditAction>act()>arg={}" + JsonUtil.toJsonWithNullValues(arg));
        //特殊处理OpenApi调用,创建产品时候，更新商品数据与产品数据同步。
        String spuId = this.objectData.get("spu_id", String.class);
        log.info("ProductEditAction doAct spu_id {}", spuId);
        if (Objects.equals(actionContext.getPeerName(), "OpenAPI-V2.0")) {
            IObjectDescribe skuDesc = serviceFacade.findObject(actionContext.getTenantId(), Utils.PRODUCT_API_NAME);
            String id = objectData.getId();
            log.info("ProductEditAction doAct descID {}", skuDesc.getId());
            List<IFieldDescribe> fieldDescribes = skuDesc.getFieldDescribes();
            if (isSpuOpen) {
                fieldDescribes.stream()
                        .filter(k -> ProductConstants.CANT_SUPPORT_UPDATE_SKU_FIELDS.contains(k.getApiName()))
                        .forEach(k -> ((ObjectData) objectData).remove(k.getApiName()));
            }
            objectData.set(DBRecord.ID, id);
            objectData.set("object_describe_api_name", Utils.PRODUCT_API_NAME);
        }

        if (isSpuOpen && isPriceUpdated(beforeUpdateProductData)) {
            updateNotHaveSpecSpuData(spuId);
        }
        return super.doAct(arg);
    }

    @Override
    protected void doUpdateData() {
        if (objectData == null) {
            return;
        }
        if (isMultiUnit()) {
            if (isSpuOpen) {
                super.doUpdateData();
            } else {
                skuService.updateMultiUnitSku(actionContext.getUser(), objectData, multiUnitListData);
                updatedDataList.add(objectData);
            }
        } else {
            super.doUpdateData();
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);

        batchUpdateMultiUnitBarcode();
        Map<String, Object> tmpResult = Maps.newHashMap();
        tmpResult.put("success", result.getObjectData().get("CRMResponse"));
        tmpResult.put(DBRecord.ID, objectData.getId());
        result.setObjectData(ObjectDataDocument.of(tmpResult));
        // send delete constraint lines message

        if (isCpqOpen) {
            ObjectDataDocument objectData = result.getObjectData();
            boolean isSaleable = objectData.get("is_saleable") == null ? false : Boolean.valueOf(objectData.get("is_saleable").toString());
            if (!isSaleable) {
                productStatusChangeTaskService.createOrUpdateTask(actionContext.getTenantId(), Lists.newArrayList(result.getObjectData().getId()), Lists.newArrayList());
            }
        }
        if (isMultiUnit() && configService.isOpenMultiUnitPriceBook(actionContext.getTenantId())) {
            if (isChangeToMulti) {
                //更新对应标准价目表明细单位
                multiUnitService.updateStandPriceBookProductUnit(actionContext.getUser(), Lists.newArrayList(objectData.getId()), multiUnitData);
            }
            //非多单位产品改单位，异步更新引用的所有价目表明细单位字段
            if (!isMultiUnit() && updatedFieldMap.containsKey("unit")) {
                multiUnitService.updatePriceBookProductActualUnit(actionContext.getTenantId()
                        , String.valueOf(updatedFieldMap.get("unit"))
                        , Lists.newArrayList(objectData.getId()));
            }
        }

        return result;
    }

    /**
     * 批量更新多单位的barcode
     */
    private void batchUpdateMultiUnitBarcode() {
        if (isMultiUnit() && isSpuOpen) {
            Map<String, String> unitIdAndBarcodeMapping = Maps.newHashMap();
            Map<String, BigDecimal> unitIdAndQuantityMapping = Maps.newHashMap();
            for (MultiUnitData.MultiUnitItem multiUnitItem : multiUnitData) {
                unitIdAndBarcodeMapping.put(multiUnitItem.getUnitId(), multiUnitItem.getBarcode());
                unitIdAndQuantityMapping.put(multiUnitItem.getUnitId(), multiUnitItem.getMinimumOrderQuantity());
            }

            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();

            IFilter filter = new Filter();
            filter.setFieldName("product_id");
            filter.setOperator(Operator.EQ);
            filter.setFieldValues(Lists.newArrayList(objectData.getId()));
            searchTemplateQuery.setFilters(Lists.newArrayList(filter));
            searchTemplateQuery.setPermissionType(0);
            searchTemplateQuery.setLimit(200);

            IObjectDescribe multiUnitDescribe = serviceFacade.findObject(actionContext.getTenantId(), Utils.MULTI_UNIT_RELATED_API_NAME);
            QueryResult<IObjectData> multiUnitDataQueryResult = serviceFacade.findBySearchQueryWithDeleted(actionContext.getUser(), multiUnitDescribe, searchTemplateQuery);

            List<IObjectData> multiUnitDataList = multiUnitDataQueryResult.getData();

            for (IObjectData objectData : multiUnitDataList) {
                objectData.set("barcode", unitIdAndBarcodeMapping.get(objectData.get("unit_id")));
                objectData.set("minimum_order_quantity", unitIdAndQuantityMapping.get(objectData.get("unit_id")));
            }

            serviceFacade.batchUpdateByFields(actionContext.getUser(), multiUnitDataList, Lists.newArrayList("barcode", "minimum_order_quantity"));
        }
    }


    @Override
    protected void startCreateWorkFlow() {
        // do nothing
    }

    private void updateNotHaveSpecSpuData(String spuId) {
        List<IObjectData> spuData = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), Lists.newArrayList(spuId), Utils.SPU_API_NAME);
        spuData.stream().findFirst().ifPresent(o -> {
            if (isNotHaveSpec(o.get("is_spec"))) {
                o.set("standard_price", objectData.get("price"));
                serviceFacade.batchUpdateByFields(actionContext.getUser(), Lists.newArrayList(o), Lists.newArrayList("standard_price"));
            }
        });
    }

    /**
     * 是否无规格
     *
     * @param isSpec 商品的规格标注位
     */
    private boolean isNotHaveSpec(Object isSpec) {
        return Objects.equals(isSpec, false);
    }

    private boolean isPriceUpdated(final IObjectData beforeUpdateProductData) {
        Object beforeUpdateSkuPrice = beforeUpdateProductData.get("price");

        Object newPrice = objectData.get("price");
        return !Objects.equals(beforeUpdateSkuPrice, newPrice);
    }

    /**
     * 更新上下架时间为产品上下架状态改变的情况
     */
    private void updateShelvesTime4ProductStatusChanged(final IObjectData beforeUpdateProductData) {
        String productStatus = (String) objectData.get("product_status");
        String beforeUpdateProductStatus = beforeUpdateProductData.get("product_status", String.class);
        // 处理 "product_status":null 的情况，把值进行回填
        if (StringUtils.isBlank(productStatus)) {
            objectData.set("product_status", beforeUpdateProductStatus);
            return;
        }
        long productStatusUpdateTime = System.currentTimeMillis();
        if (!Objects.equals(productStatus, beforeUpdateProductStatus)) {
            if (productStatus.equals(ProductConstants.Status.OFF.getStatus())) {
                objectData.set("off_shelves_time", productStatusUpdateTime);
            } else {
                objectData.set("on_shelves_time", productStatusUpdateTime);
            }
        }
    }

    private boolean isMultiUnit() {
        return multiUnitData != null;
    }

    private void batchSNFieldValidate() {
        IFieldDescribe batchField = objectDescribe.getFieldDescribe("batch_sn");
        if (batchField != null && Boolean.TRUE.equals(batchField.isActive())) {

            CheckIsAllowModifySpuBatchSNRestModel.Arg snValidateArg = CheckIsAllowModifySpuBatchSNRestModel.Arg.builder()
                    .newValue(objectData.get("batch_sn", String.class))
                    .oldValue(dbMasterData.get("batch_sn", String.class))
                    .spuId(objectData.getId())
                    .tenantId(actionContext.getTenantId())
                    .build();


            CheckIsAllowModifySpuBatchSNRestModel.Result snValidateResult = stockProxy.checkIsAllowModifySpuBatchSN(snValidateArg,
                    SFAHeaderUtil.getHeaders(actionContext.getUser()));
            if (!snValidateResult.getData().getResult()) {
                throw new ValidateException(I18N.text("product.edit.batch_serial_verification_failed", snValidateResult.getData().getErrorMsg()));
            }
        }
    }
}
