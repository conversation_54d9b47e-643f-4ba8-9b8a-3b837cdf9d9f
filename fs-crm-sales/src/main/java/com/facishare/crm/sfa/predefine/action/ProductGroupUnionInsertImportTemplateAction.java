package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.describebuilder.SelectOneFieldDescribeBuilder;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportTemplateAction;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/7/2 6:03 下午
 * @illustration
 */
public class ProductGroupUnionInsertImportTemplateAction extends StandardUnionInsertImportTemplateAction {

    private final List<String> filterFields = Lists.newArrayList("parent_bom_id", "bom_path", "root_id", BomConstants.FIELD_NODE_BOM_CORE_VERSION, BomConstants.FIELD_NODE_BOM_CORE_TYPE, BomConstants.FIELD_IS_PACKAGE);

    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        super.customHeader(headerFieldList);
        headerFieldList.removeIf(field -> filterFields.contains(field.getApiName()));
        headerFieldList.stream().filter(x -> x.getApiName().equals("owner")).forEach(x -> x.setRequired(true));
    }

    @Override
    protected void customMasterHeader(List<IFieldDescribe> masterFieldList) {
        super.customMasterHeader(masterFieldList);
    }

    @Override
    protected String getFieldSampleValue(IFieldDescribe field) {
        return super.getFieldSampleValue(field);
    }

    @Override
    protected void supportRelatedMark(List<IFieldDescribe> sortedFieldList) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("label", I18N.text(BomI18NKeyUtil.SFA_BOM_PATH));
        IFieldDescribe field = ImportExportExt.createField(map, "RELATED_MARK", true);
        sortedFieldList.add(0, field);
    }

    @Override
    protected void supportID(List<IFieldDescribe> sortedFieldList) {

        super.supportID(sortedFieldList);
        sortedFieldList.removeIf(field -> Objects.equals(field.getLabel(), I18N.text("BOMObj.field.product_group_id.label") + "_" + I18N.text("paas.udobj.data_id")));
    }

    @Override
    protected void supportUniqueID(List<IFieldDescribe> sortedFieldList) {
        return;
    }

    @Override
    protected void customDetailHeader(List<IFieldDescribe> detailFieldList) {
        if (GrayUtil.bomMasterSlaveMode(actionContext.getTenantId())) {
            Map<String, Object> map = Maps.newHashMap();
            map.put("label", I18N.text("sfa.bom.parent.bom.name"));
            map.put("api_name", "parent_bom_name");
            IFieldDescribe field = ImportExportExt.createField(map, "text", false);
            detailFieldList.add(field);
            ISelectOption option1 = new SelectOption();
            option1.setValue("true");
            option1.setLabel(I18N.text("BOMObj.field.is_package.option.true"));
            ISelectOption option2 = new SelectOption();
            option2.setValue("false");
            option2.setLabel(I18N.text("BOMObj.field.is_package.option.false"));
            List<ISelectOption> selectOptions = Lists.newArrayList();
            selectOptions.add(option1);
            selectOptions.add(option2);
            SelectOneFieldDescribe selectOneField = SelectOneFieldDescribeBuilder.builder().apiName("root_node_flag").label(I18N.text("sfa.bom.root.node.flag")).selectOptions(selectOptions).defaultValud(false).build();
            detailFieldList.add(selectOneField);
        }
    }
}
