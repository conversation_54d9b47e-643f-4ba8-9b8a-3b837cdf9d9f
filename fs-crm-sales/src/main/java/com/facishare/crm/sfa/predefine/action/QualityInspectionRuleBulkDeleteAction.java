package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.QualityInspectionDBService;
import com.facishare.crm.sfa.predefine.service.QualityInspectionRuleNotifierProvider;
import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.Serializer;
import com.facishare.paas.appframework.core.predef.action.StandardBulkDeleteAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

/**
 * <AUTHOR>
 * @Date 2022/9/26 17:01
 * @Version 1.0
 **/
@Slf4j
@Idempotent(serializer = Serializer.Type.java)
public class QualityInspectionRuleBulkDeleteAction extends StandardBulkDeleteAction {
    private static final QualityInspectionDBService qualityInspectionDBService = SpringUtil.getContext().getBean(QualityInspectionDBService.class);
    private static final QualityInspectionRuleNotifierProvider qualityInspectionRuleNotifierProvider = SpringUtil.getContext().getBean(QualityInspectionRuleNotifierProvider.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected void doFunPrivilegeCheck() {
    }

    @Override
    protected Result doAct(Arg arg) {
        arg.setDirectDelete(true);
        return super.doAct(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result ret = super.after(arg, result);
        if (ret.getSuccess()){
            if (CollectionUtils.isNotEmpty(dataList)){
                qualityInspectionDBService.deleteRule(actionContext, dataList.get(0));
                qualityInspectionDBService.deleteRuleMember(actionContext, dataList.get(0).getId());

                for(IObjectData data : dataList){
                    Integer type = (Integer) data.get("type");
                    if(type != null && type == 1) {
                        qualityInspectionRuleNotifierProvider.sendDirtyModifyMsg(actionContext.getTenantId());
                    }
                }
            }
        }
        return ret;
    }
}