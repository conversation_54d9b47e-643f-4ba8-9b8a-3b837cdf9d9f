package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.utils.StringUtil;
import com.facishare.crm.sfa.predefine.service.QualityInspectionDBService;
import com.facishare.crm.sfa.predefine.service.QualityInspectionRuleNotifierProvider;
import com.facishare.crm.sfa.predefine.service.QualityInspectionRuleService;
import com.facishare.crm.sfa.predefine.service.model.quality.QIGetWechatConversionModel.QIUser;
import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.Serializer;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2022/9/26 17:01
 * @Version 1.0
 **/
@Slf4j
@Idempotent(serializer = Serializer.Type.java)
public class QualityInspectionRuleEditAction extends StandardEditAction {
    private static final QualityInspectionRuleService qualityInspectionRuleService = SpringUtil.getContext().getBean(QualityInspectionRuleService.class);
    private static final QualityInspectionDBService qualityInspectionDBService = SpringUtil.getContext().getBean(QualityInspectionDBService.class);

    private static final QualityInspectionRuleNotifierProvider qualityInspectionRuleNotifierProvider = SpringUtil.getContext().getBean(QualityInspectionRuleNotifierProvider.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }
    @Override
    protected void doFunPrivilegeCheck() {
    }

    @Override
    protected void before(Arg arg) {
        ObjectDataDocument data = arg.getObjectData();
        String record_type  = StringUtil.convertString(String.valueOf(data.get("record_type")), "");
        if(record_type.contains("dirtyword")){
            data.put("type", 1);
            data.put("session_feature", "1");
            data.put("session_action", "1");
            //data.put("record_type", record_dirtyword_layout);
            //data.put("layout_api_name", dirtyword_layout_api);
        }else if(record_type.contains("action")){
            data.put("type", 2);
            data.put("dirty_words", "{}");
            data.put("session_feature", "1");
            data.put("session_action", String.valueOf(data.get("session_action")));
            //data.put("record_type", record_action_layout);
            //data.put("layout_api_name", action_layout_api);
        }else if(record_type.contains("feature")){
            data.put("type", 3);
            data.put("dirty_words", "{}");
            data.put("session_feature", String.valueOf(data.get("session_feature")));
            data.put("session_action", "1");
            //data.put("record_type", record_feature_layout);
            //data.put("layout_api_name", feature_layout_api);
        }

        super.before(arg);
        data.put("owner", com.google.common.collect.Lists.newArrayList(actionContext.getUser().getUpstreamOwnerIdOrUserId()));
        log.warn("QualityInspectionRuleEditAction before {}", data);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);

        ObjectDataDocument data = arg.getObjectData();
        QIUser monitors = JSON.parseObject(String.valueOf(data.get("monitors")), QIUser.class);
        QIUser msgUser = JSON.parseObject(String.valueOf(data.get("msg_user")), QIUser.class);

        String ruleId = data.getId();
        qualityInspectionRuleService.updateRule(actionContext, ruleId, data);
        qualityInspectionDBService.deleteRuleMember(actionContext, ruleId);
        qualityInspectionRuleService.addRuleMember(actionContext, monitors, msgUser, ruleId);
        qualityInspectionRuleService.putRecordLayout((Integer) data.get("type"), data);

        Integer type = (Integer) data.get("type");
        if(type != null && type == 1) {
            qualityInspectionRuleNotifierProvider.sendDirtyModifyMsg(actionContext.getTenantId());
        }
        return result;
    }
}