package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.bizvalidator.BizValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.product.ProductStockValidator;
import com.facishare.crm.sfa.predefine.service.ProductCategoryBizService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreService;
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreServiceImpl;
import com.facishare.crm.sfa.predefine.service.model.MultiUnitData;
import com.facishare.crm.sfa.predefine.service.model.MultiUnitListData;
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel;
import com.facishare.crm.sfa.predefine.service.real.*;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.proxy.StockProxy;
import com.facishare.crm.sfa.utilities.proxy.model.CheckIsAllowModifySpuBatchSNRestModel;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.StringRegexEscapeUtils;
import com.facishare.crm.sfa.utilities.validator.ProductCategoryV2Validator;
import com.facishare.crm.sfa.utilities.validator.ProductValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ObjectLockStatus;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.LicenseServiceImpl;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Filed.SHOP_CATEGORY_ID;
import static com.facishare.crm.sfa.utilities.constant.ProductConstants.MAX_SKU_LIMIT_OF_SAME_GROUP;
import static com.facishare.crm.sfa.utilities.constant.SpuSkuConstants.SKU_RELATE_SPU_ID;

/**
 * Created by luxin on 2018/11/12.
 */
public class SPUEditAction extends StandardEditAction {

    private final SpuSkuService skuService = SpringUtil.getContext().getBean(SpuSkuServiceImpl.class);
    private final ProductService productService = SpringUtil.getContext().getBean(ProductService.class);
    private final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitServiceImpl.class);
    private final LicenseService licenseService = SpringUtil.getContext().getBean(LicenseServiceImpl.class);
    private final StockProxy stockProxy = SpringUtil.getContext().getBean(StockProxy.class);
    private final ProductCategoryBizService productCategoryBizService = SpringUtil.getContext().getBean(ProductCategoryBizService.class);
    private final BizConfigThreadLocalCacheService configService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private final BomCoreService bomCoreService = SpringUtil.getContext().getBean(BomCoreServiceImpl.class);
    private final ProductCategoryV2Validator productCategoryV2Validator = SpringUtil.getContext().getBean(ProductCategoryV2Validator.class);


    /**
     * 新增的sku数据
     */
    private final List<IObjectData> addSkuDataList = Lists.newArrayList();
    /**
     * 作废sku数据
     */
    private final List<IObjectData> invalidSkuDataList = Lists.newArrayList();
    /**
     * 被编辑的sku数据
     */
    private final List<IObjectData> editedSkuDataList = Lists.newArrayList();

    private final List<IObjectData> skuList = Lists.newArrayList();

    private List<MultiUnitData.MultiUnitItem> multiUnitData;
    MultiUnitListData multiUnitListData;
    private IObjectDescribe skuDescribe;
    private boolean isChangeToMulti = false;

    @Override
    protected void before(Arg arg) {
        if (!SFAConfigUtil.isSpuOpen(actionContext.getTenantId())) {
            //throw new ValidateException("商品对象已关闭，不允许更新商品数据");
            throw new ValidateException(I18N.text("sfa.spu.closed.cannot.add.spudata"));
        }
        if (!Objects.equals(actionContext.getPeerName(), "OpenAPI-V2.0")) {
            IObjectDescribe spuDescribe = serviceFacade.findObject(actionContext.getTenantId(), Utils.SPU_API_NAME);
            productService.modifyArg(spuDescribe, arg.getObjectData());
        }
        boolean isMultiUnit = AccountUtil.getBooleanValue(arg.getObjectData(), "is_multiple_unit", false);

        super.before(arg);
        if (Objects.nonNull(updatedFieldMap.get("is_multiple_unit"))) {
            if (!(Boolean) updatedFieldMap.get("is_multiple_unit") && dbMasterData.get("is_multiple_unit", Boolean.class, false)) {
                updatedFieldMap.remove("is_multiple_unit");
                objectData.set("is_multiple_unit", true);
                //throw new ValidateException(I18N.text("sfa.multi.cannot.close"));
            }
        }
        productCategoryBizService.handleCategoryMappingCategoryId(actionContext.getUser(), dbMasterData,
                arg.getObjectData().toObjectData(), SFAPreDefineObject.SPU.getApiName());
        productCategoryV2Validator.checkCategoryIsLeafNode(actionContext.getUser(), arg.getObjectData(), dbMasterData);
        if (multiUnitService.isOpenMultiUnit(actionContext.getTenantId()) && isMultiUnit) {
            multiUnitData = multiUnitService.preprocessMultiUnit(ObjectDataDocument.of(objectData));
            List<IObjectData> dbMultiUnitData = multiUnitService.getMultiUnitInfoListBySPU(actionContext.getTenantId(), objectData.getId());
            multiUnitListData = multiUnitService.compareMultiUnitData(multiUnitData, dbMultiUnitData);
            // 开启了多单位,校验多单位数据
            multiUnitService.checkEditSpuMultiUnit(objectData, multiUnitData, (SelectOne) objectDescribe.getFieldDescribe("unit"));

            if (configService.isOpenMultiUnitPriceBook(actionContext.getTenantId())) {
                isChangeToMulti = multiUnitService.checkIsUpdatePricingUnit(multiUnitData, dbMultiUnitData);
            }

            Set<String> module = licenseService.getModule(actionContext.getTenantId());
            if (multiUnitData != null && module.contains("kx_peculiarity") && multiUnitData.size() > 3) {
                if (!GrayUtil.isGrayMultiUnitCount(actionContext.getTenantId())) {
                    //throw new ValidateException("开启快销企业多单位数量超过上限3个");
                    throw new ValidateException(I18N.text("sfa.open.kuaixiao.multidata.limit"));
                }
            }
            if (multiUnitData != null && multiUnitData.size() > 20) {
                //throw new ValidateException("多单位数量超过上限20个");
                throw new ValidateException(I18N.text("sfa.multidata.limit"));
            }
        }
        ProductValidator.validateSerialNumberAndMultiUnit(objectData);

        if (!Objects.equals(actionContext.getPeerName(), "OpenAPI-V2.0")) {
            parseSkuDataForBefore();
            ValidatorContext validatorContext = ValidatorContext.builder().action(ObjectAction.CREATE).user(actionContext.getUser()).objectDataList(skuList).build();
            BizValidator.build().withContext(validatorContext).with(new ProductStockValidator()).doValidate();
            ProductValidator.handlePeriodicProduct(actionContext.getUser(), skuList);
        }
        resetSpecField();
        if (SFAConfigUtil.isCPQ(actionContext.getTenantId())) {
            List<Map<String, Object>> skuList = objectData.get("sku", List.class);
            if (CollectionUtils.nullToEmpty(skuList).size()==1) {
                Map<String, Object> skuMap = skuList.get(0);
                String skuId = ObjectDataExt.of(skuMap).getId();
                if(StringUtils.isNotBlank(skuId)){
                    Boolean isPackage = MapUtils.getBoolean(skuMap, ProductConstants.IS_PACKAGE, false);
                    bomCoreService.checkExistChildNode(actionContext.getUser(),skuId,isPackage);
                }
            }
        }
    }

    @Override
    protected void init() {
        super.init();
        if (Objects.isNull(objectData.get("is_multiple_unit"))) {
            objectData.set("is_multiple_unit", false);
        }
    }

    @Override
    protected void validate() {
        super.validate();
        batchSNFieldValidate();
    }

    @Override
    protected void startCreateWorkFlow() {
        // do nothing
    }

    @Override
    protected Result doAct(Arg arg) {
        if (!Objects.equals(actionContext.getPeerName(), "OpenAPI-V2.0")) {
            parseSkuData();
        } else {
            // TODO: 2018/12/25 针对OpenAPI做适配，获取公共字段：单位，产品分类，产品线字段，同步更新SPU下的sku
            String spuId = arg.getObjectData().getId();
            Object category = arg.getObjectData().get("category");
            Object productCategoryId = arg.getObjectData().get(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID);
            Object shopCategoryId = arg.getObjectData().get(SHOP_CATEGORY_ID);
            Object unit = arg.getObjectData().get("unit");
            Object productLine = arg.getObjectData().get("product_line");
            Object batchSN = arg.getObjectData().get("batch_sn");
            IFilter filter = new Filter();
            filter.setFieldName(SKU_RELATE_SPU_ID);
            filter.setFieldValues(Lists.newArrayList(spuId));
            filter.setOperator(Operator.EQ);
            List<IObjectData> skuDataList = serviceFacade.findDataWithWhere(actionContext.getUser(), Utils.PRODUCT_API_NAME, Lists.newArrayList(filter), Lists.newArrayList(), 0, 500);
            String spuNewName = arg.getObjectData().get("name").toString();
            IObjectData spuDataFromDB = serviceFacade.findObjectData(actionContext.getUser(), spuId, Utils.SPU_API_NAME);
            boolean isSpec = Objects.equals(Boolean.TRUE, spuDataFromDB.get("is_spec"));
            String spuNameFromDB = spuDataFromDB.getName();
            for (IObjectData skuData : skuDataList) {
                String skuOldName = skuData.getName();
                String skuNewName;
                if (isSpec) {
                    skuNewName = skuOldName.replaceFirst(StringRegexEscapeUtils.escape(spuNameFromDB), spuNewName);
                } else {
                    skuNewName = spuNewName;
                }
                skuData.setName(skuNewName);
                if (!Objects.isNull(category) && StringUtils.isNotEmpty(category.toString())) {
                    skuData.set("category", category);
                }
                if (!Objects.isNull(productCategoryId) && StringUtils.isNotEmpty(productCategoryId.toString())) {
                    skuData.set(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID, productCategoryId);
                }
                if (!Objects.isNull(shopCategoryId)){
                    skuData.set(SHOP_CATEGORY_ID, productCategoryId);
                }
                if (!Objects.isNull(unit) && StringUtils.isNotEmpty(unit.toString())) {
                    skuData.set("unit", unit);
                }
                if (!Objects.isNull(productLine) && StringUtils.isNotEmpty(productLine.toString())) {
                    skuData.set("product_line", productLine);
                }
                if (!Objects.isNull(batchSN) && StringUtils.isNotEmpty(batchSN.toString())) {
                    skuData.set("batch_sn", batchSN);
                }
            }
            serviceFacade.batchUpdate(skuDataList, actionContext.getUser());
        }
        return super.doAct(arg);
    }


    @Override
    protected boolean needTriggerApprovalFlow() {
        return false;
    }

    @Override
    /**
     * 商品产品保存走service里面的逻辑,不用父类的保存方法
     */ protected void doUpdateData() {
        if (Objects.equals(actionContext.getPeerName(), "OpenAPI-V2.0")) {
            super.batchUpdateObjectData(Lists.newArrayList(objectData));
            return;
        }
        if (objectData == null) {
            return;
        }

        processSkuData();

        if (isMultiUnit()) {
            skuService.updateMultiUnitSpuAndHandleSku(objectData, actionContext.getUser(), addSkuDataList, invalidSkuDataList, editedSkuDataList, multiUnitListData);
        } else {
            skuService.updateSpuAndHandleSku(objectData, actionContext.getUser(), addSkuDataList, invalidSkuDataList, editedSkuDataList);
        }
        updatedDataList.add(objectData);
    }


    @Override
    protected Result after(Arg arg, Result result) {
        updatedDataList.stream().findFirst().ifPresent(o -> o.set("sku", ""));

        Result newResult = super.after(arg, result);
        if (isMultiUnit() && configService.isOpenMultiUnitPriceBook(actionContext.getTenantId())) {
            List<IObjectData> skuDataList = skuService.findProductData(actionContext.getTenantId(), Lists.newArrayList(objectData.getId()));
            List<String> productIds = skuDataList.stream()
                    .map(DBRecord::getId)
                    .collect(Collectors.toList());
            if (isChangeToMulti && CollectionUtils.notEmpty(productIds)) {
                multiUnitService.updateStandPriceBookProductUnit(actionContext.getUser(), productIds, multiUnitData);
            }

            //非多单位产品改单位，异步更新引用的所有价目表明细单位字段
            if (!isMultiUnit() && updatedFieldMap.containsKey("unit")) {
                multiUnitService.updatePriceBookProductActualUnit(actionContext.getTenantId()
                        , String.valueOf(updatedFieldMap.get("unit"))
                        , productIds);
            }
        }

        return newResult;
    }

    @Override
    public void recordLog() {
        super.recordLog();
    }

    /**
     * 校验深圳的是否能编辑batch_sn字段,如果不能编辑抛出校验失败异常
     */
    private void batchSNFieldValidate() {
        IFieldDescribe batchField = objectDescribe.getFieldDescribe("batch_sn");
        if (batchField != null && Boolean.TRUE.equals(batchField.isActive())) {
            IObjectData beforeUpdateSpuData = serviceFacade.findObjectData(actionContext.getTenantId(), this.objectData.getId(), objectDescribe);


            CheckIsAllowModifySpuBatchSNRestModel.Arg snValidateArg = CheckIsAllowModifySpuBatchSNRestModel.Arg.builder().newValue(objectData.get("batch_sn", String.class)).oldValue(beforeUpdateSpuData.get("batch_sn", String.class)).spuId(objectData.getId()).tenantId(actionContext.getTenantId()).build();


            CheckIsAllowModifySpuBatchSNRestModel.Result snValidateResult = stockProxy.checkIsAllowModifySpuBatchSN(snValidateArg, SFAHeaderUtil.getHeaders(actionContext.getUser()));

            if (!snValidateResult.getData().getResult()) {
                throw new ValidateException(I18N.text("product.edit.batch_serial_verification_failed", snValidateResult.getData().getErrorMsg()));
            }
        }
    }

    /**
     * 处理 sku 新增的数据
     */
    private void processSkuData() {
        if (CollectionUtils.notEmpty(addSkuDataList)) {
            addSkuDataList.forEach(o -> {
                o.set("spu_id", objectData.getId());
                o.set("category", objectData.get("category"));
                o.set(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID, ObjectDataUtils.getValueOrDefault(objectData, ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID, ""));
                o.set(SHOP_CATEGORY_ID, objectData.get(SHOP_CATEGORY_ID));
                o.set("product_line", objectData.get("product_line"));
                o.set("unit", objectData.get("unit"));
                o.set("batch_sn", objectData.get("batch_sn"));
                setDefaultRecordType(o, skuDescribe);
                modifyObjectDataBeforeCreate(o, skuDescribe);
                convertPathForRichText(o, skuDescribe);
            });
        }
        if (CollectionUtils.notEmpty(editedSkuDataList)) {
            editedSkuDataList.forEach(d -> {
                d.setTenantId(actionContext.getTenantId());
                convertPathForRichText(d, skuDescribe);
            });
            serviceFacade.processData(skuDescribe, editedSkuDataList);
        }
    }


    /**
     * 从spu数据中解析出sku数据
     */
    private void parseSkuData() {
        Optional<List<Map<String, Object>>> tmpSkuListOptional = Optional.ofNullable(objectData.get("sku", List.class));

        if (!tmpSkuListOptional.isPresent() || tmpSkuListOptional.get().isEmpty()) {
            throw new ValidateException(I18N.text("product.sku_data_is_null"));
        }

        boolean isSpec = Objects.equals(objectData.get("is_spec"), true);
        //新增的规格产品
        List<IObjectData> toAddSkuList = Lists.newArrayList();
        //更换规格值或者新增规格的历史产品
        List<IObjectData> toEditSkuList = Lists.newArrayList();
        tmpSkuListOptional.get().forEach(o -> {
            Object statusFlag = o.get("status_flag");

            if (isMultiUnit()) {
                o.put("is_multiple_unit", true);
            }

            if (statusFlag == null) {
                throw new RuntimeException("status flag is null");
            }

            if (Objects.isNull(o.get("object_describe_id"))) {
                o.put(ProductConstants.DescribeField.FIELD_DESCRIBE_ID.getApiName(), skuDescribe.getId());
                o.put(ProductConstants.DescribeField.FIELD_DESCRIBE_API_NAME.getApiName(), skuDescribe.getApiName());
            }

            // 更新要编辑和要作废的产品
            if (statusFlag.equals(ProductConstants.SkuEditStatus.EDIT.getStatus()) || (statusFlag.equals(ProductConstants.SkuEditStatus.INVALID.getStatus())) || (statusFlag.equals(ProductConstants.SkuEditStatus.SKU_EDIT_STATUS.getStatus())) || statusFlag.equals(ProductConstants.SkuEditStatus.ADD_SPEC.getStatus())) {
                // ------全部数据更新 start
                ObjectDataExt dataExt = ObjectDataExt.of(Maps.newHashMap(ObjectDataExt.of(o).toMap()));
                dataExt.remove(DBRecord.VERSION);
                dataExt.remove(ObjectLockStatus.LOCK_STATUS_API_NAME);
                dataExt.remove(ObjectLifeStatus.LIFE_STATUS_API_NAME);
                dataExt.remove(ObjectLifeStatus.LIFE_STATUS_BEFORE_INVALID_API_NAME);

                dataExt.set("name", o.get("name"));
                dataExt.set("category", objectData.get("category"));
                dataExt.set(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID, ObjectDataUtils.getValueOrDefault(objectData, ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID, ""));
                dataExt.set(SHOP_CATEGORY_ID, objectData.get(SHOP_CATEGORY_ID));
                dataExt.set("product_line", objectData.get("product_line"));
                dataExt.set("unit", objectData.get("unit"));
                dataExt.set("batch_sn", objectData.get("batch_sn"));

                if (!(Objects.equals(objectData.get("is_spec"), true))) {
                    dataExt.set("price", objectData.get("standard_price"));
                }
                editedSkuDataList.add(dataExt.getObjectData());
                // ------全部数据更新 end
                if (isSpec && (statusFlag.equals(ProductConstants.SkuEditStatus.SKU_EDIT_STATUS.getStatus()) || statusFlag.equals(ProductConstants.SkuEditStatus.ADD_SPEC.getStatus()))) {
                    toEditSkuList.add(dataExt.getObjectData());
                }
            }

            if (statusFlag.equals(ProductConstants.SkuEditStatus.ADD.getStatus())) {
                o.put(ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectLifeStatus.NORMAL.getCode());
                o.put("owner", objectData.getOwner());
                IObjectData needAddSkuData = ObjectDataExt.of(o).getObjectData();
                modifyObjectDataBeforeCreate(needAddSkuData, skuDescribe);
                addSkuDataList.add(needAddSkuData);
                if (isSpec) {
                    toAddSkuList.add(needAddSkuData);
                }
            } else if (statusFlag.equals(ProductConstants.SkuEditStatus.INVALID.getStatus())) {
                invalidPrivilegeCheck();
                if (Objects.equals(o.get("is_deleted"), false)) {
                    invalidSkuDataList.add(ObjectDataExt.of(o).getObjectData());
                }
            }
        });
        if (CollectionUtils.notEmpty(toAddSkuList) || CollectionUtils.notEmpty(toEditSkuList)) {
            ProductValidator.validateSpecStatusActive(actionContext.getTenantId(), toAddSkuList, toEditSkuList);
        }
    }


    /**
     * 从spu数据中解析出sku数据
     */
    private void parseSkuDataForBefore() {
        skuDescribe = serviceFacade.findObject(actionContext.getTenantId(), Utils.PRODUCT_API_NAME);
        Optional<List<Map<String, Object>>> tmpSkuListOptional = Optional.ofNullable(objectData.get("sku", List.class));

        if (!tmpSkuListOptional.isPresent()) {
            throw new ValidateException(I18N.text("product.sku_data_is_null"));
        }
        if (tmpSkuListOptional.get().size() > MAX_SKU_LIMIT_OF_SAME_GROUP) {
            throw new ValidateException(I18N.text("product.sku_data_more_then_limit"));
        }

        tmpSkuListOptional.get().forEach(o -> {
            if (Objects.isNull(o.get("object_describe_id"))) {
                o.put(ProductConstants.DescribeField.FIELD_DESCRIBE_ID.getApiName(), skuDescribe.getId());
                o.put(ProductConstants.DescribeField.FIELD_DESCRIBE_API_NAME.getApiName(), skuDescribe.getApiName());
            }
            skuList.add(ObjectDataExt.of(o).getObjectData());
        });
    }

    // 如果有作废产品数据需要校验作废权限
    private void invalidPrivilegeCheck() {
        serviceFacade.doFunPrivilegeCheck(actionContext.getUser(), Utils.PRODUCT_API_NAME, Lists.newArrayList(ObjectAction.INVALID.getActionCode()));
    }

    private void resetSpecField() {
        objectData.set("is_spec", dbMasterData.get("is_spec"));
    }

    private boolean isMultiUnit() {
        return multiUnitData != null;
    }

}
