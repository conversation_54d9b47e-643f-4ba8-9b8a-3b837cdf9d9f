package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.ProductCategoryBizService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.ProductCategoryUtils;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.SpuProductImportUtil;
import com.facishare.crm.sfa.utilities.validator.ProductCategoryImportValidator;
import com.facishare.crm.sfa.utilities.validator.ProductCategoryV2Validator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Filed.SHOP_CATEGORY_ID;

/**
 * <AUTHOR>
 * @date 2018/11/9 16:11
 * @instruction
 */
@Slf4j
public class SPUUpdateImportDataAction extends StandardUpdateImportDataAction {
    private final ProductCategoryBizService productCategoryBizService = SpringUtil.getContext().getBean(ProductCategoryBizService.class);
    private final BizConfigThreadLocalCacheService cacheConfigService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private ProductCategoryV2Validator productCategoryV2Validator = SpringUtil.getContext().getBean(ProductCategoryV2Validator.class);
    private final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitServiceImpl.class);
    private ProductCategoryImportValidator productCategoryImportValidator = SpringUtil.getContext().getBean(ProductCategoryImportValidator.class);
    private final ProductCategoryUtils productCategoryUtils = SpringUtil.getContext().getBean(ProductCategoryUtils.class);


    public static String appName = ConfigHelper.getProcessInfo().getName();
    public static String serverIp = ConfigHelper.getProcessInfo().getIp();
    public static String profile = ConfigHelper.getProcessInfo().getProfile();
    public static String action = "SPUUpdateImport";
    private final Map<String, Boolean> spuIsSpecMap = Maps.newHashMap();
    private final Map<String, String> spuOldNameMap = Maps.newHashMap();

    @Override
    protected void customAfterImport(List<IObjectData> actualList) {
        super.customAfterImport(actualList);
        productCategoryBizService.fillCategoryField(actionContext.getUser(), actualList, fillCategoryData, SFAPreDefineObject.SPU.getApiName());
    }

    private Set<String> fillCategoryData = Sets.newHashSet();

    private final ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) SpringUtil.getContext().getBean("taskExecutor");

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        List<IObjectData> collect = dataList.stream().map(ImportData::getData).collect(Collectors.toList());
        fillCategoryData = productCategoryBizService.handleCategoryMappingCategoryIdOfImport(actionContext.getUser(), collect, "SPUObj");
    }

    @Override
    protected List<IObjectData> importData(List<IObjectData> validList) {

        List<IObjectData> objectDataList = super.importData(validList);
        Map<String, ObjectDataExt> dbSpuDataMap = getDataInStoreById();
        for (Map.Entry<String, ObjectDataExt> entry : dbSpuDataMap.entrySet()) {
            String spuId = entry.getKey();
            ObjectDataExt objectDataExt = entry.getValue();
            spuIsSpecMap.put(spuId, objectDataExt.get("is_spec", Boolean.class));
            spuOldNameMap.put(spuId, objectDataExt.getName());
        }

        Map<String, IObjectData> spuIdToName = objectDataList.stream().collect(Collectors.toMap(o -> o.getId(), v -> v, (v1, v2) -> v1));
        /**
         * 异步处理
         *  - 产品名称
         *  - 分类
         *  - 产品线
         *  - 单位
         */
        executor.execute(() -> {
            syncFieldToSku(actionContext.getTenantId(), actionContext.getUser(), spuIdToName);
        });
        return objectDataList;
    }

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);
        List<ImportError> importErrors = productCategoryImportValidator.checkInsertRelatedCategoryIsLeafNode(actionContext.getUser(), dataList);
        allErrorList.addAll(importErrors);
        dataList.forEach(data -> {
            try {
                productCategoryV2Validator.checkCategoryParamOfProduct(actionContext.getTenantId(), objectDescribe, ObjectDataDocument.of(data.getData()));
            } catch (ValidateException ex) {
                allErrorList.addAll(productCategoryUtils.getErrorList(Lists.newArrayList(data), ex.getMessage()));
            }
            //不支持更新是否多规格
            checkEditIsSpec(data);
        });
    }

    private void checkEditIsSpec(ImportData data) {
        Optional.ofNullable(data)
                .map(ImportData::getData)
                .ifPresent(spuData -> {
                    if (spuData.containsField("is_spec")) {
                        allErrorList.add(new BaseImportAction.ImportError(data.getRowNo(), I18N.text(SFAI18NKeyUtil.SFA_SPU_UPDATE_IMPORT_NOT_SUPPORT_MODIFY_SPEC)));
                    }
                });
    }

    private void syncFieldToSku(String tenantId, User user, Map<String, IObjectData> spuDataMap) {
        try {
            List<List<String>> spuIdGroup = Lists.partition(Lists.newArrayList(spuDataMap.keySet()), 5);

            spuIdGroup.forEach(spuIds -> {
                List<IObjectData> dbSkuDataList = findProductData(tenantId, spuIds);
                if (CollectionUtils.empty(dbSkuDataList)) {
                    return;
                }

                List<IObjectData> oldDataList = ObjectDataExt.copyList(dbSkuDataList);

                Map<String, List<IObjectData>> spuToSkuDataList = dbSkuDataList.stream().collect(Collectors.groupingBy(o -> o.get("spu_id", String.class)));

                List<IObjectData> updateSkuDataList = Lists.newArrayList();
                boolean isOpenMultiUnitPriceBook = cacheConfigService.isOpenMultiUnitPriceBook(actionContext.getTenantId());
                spuDataMap.forEach((spuId, spuData) -> {
                    if (isOpenMultiUnitPriceBook) {
                        updatePriceBookProductActualUnit(spuId, spuData, spuToSkuDataList);
                    }
                    setSyncFieldToSku(spuId, spuData, spuToSkuDataList, updateSkuDataList);
                });

                setNewSkuName(updateSkuDataList);

                List<List<IObjectData>> partition = Lists.partition(updateSkuDataList, 100);
                partition.forEach(dataList -> {
                    serviceFacade.batchUpdate(dataList, user);
                });
                recordImportDataLog(updateSkuDataList, oldDataList, serviceFacade.findObject(tenantId, Utils.PRODUCT_API_NAME));
            });
        } catch (Exception e) {
            // 不需要多语
            sendAuditLog(tenantId, user.getUpstreamOwnerIdOrUserId(), "商品更新导入，同步产品信息失败");// ignoreI18n
            log.error("SPUUpdateImportDataAction importData syncFieldToSku tenantId->{}", tenantId, e);
        }
    }

    private void recordImportDataLog(List<IObjectData> objectData, List<IObjectData> beforeUpdateDataList, IObjectDescribe describe) {
        if (CollectionUtils.empty(objectData)) {
            return;
        }
        Map<String, IObjectData> id2DataMap = Maps.newHashMap();
        beforeUpdateDataList.forEach(o -> id2DataMap.put(o.getId(), o));

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> serviceFacade.updateImportLog(actionContext.getUser(), EventType.MODIFY, ActionType.UpdateImport, describe, objectData, id2DataMap));
        parallelTask.run();
    }

    private void setNewSkuName(List<IObjectData> updateSkuDataList) {
        updateSkuDataList.forEach(data -> {
            String spuId = data.get("spu_id", String.class);
            String spuName = data.get("spu_name", String.class);
            if (Boolean.TRUE.equals(spuIsSpecMap.getOrDefault(spuId, Boolean.FALSE))) {
                String productSpec = data.get("product_spec", String.class);
                if (StringUtils.isNotEmpty(productSpec)) {
                    String oldSpuName = spuOldNameMap.getOrDefault(spuId, "");
                    String suffixName = SpuProductImportUtil.getSuffixName(productSpec);
                    String oldSkuName = oldSpuName.concat(suffixName);
                    if (Objects.equals(data.getName(), oldSkuName)) {
                        String productName = spuName.concat(suffixName);
                        data.setName(productName);
                    }
                } else {
                    data.setName(spuName);
                }
            } else {
                data.setName(spuName);
            }
        });
    }

    private void setSyncFieldToSku(String spuId, IObjectData spuData, Map<String, List<IObjectData>> spuToSkuDataList, List<IObjectData> updateSkuDataList) {
        List<IObjectData> skuDataList = spuToSkuDataList.get(spuId);
        if (CollectionUtils.empty(skuDataList)) {
            return;
        }
        String category = spuData.get("category", String.class);
        Object shopCategoryId = spuData.get(SHOP_CATEGORY_ID);
        String productCategoryId = spuData.get(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID, String.class);

        String productLine = spuData.get("product_line", String.class);
        String unit = spuData.get("unit", String.class);
        String name = spuData.getName();

        for (IObjectData skuData : skuDataList) {
            skuData.set("category", category);
            skuData.set(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID, productCategoryId);
            skuData.set(SHOP_CATEGORY_ID, shopCategoryId);
            skuData.set("product_line", productLine);
            skuData.set("unit", unit);
            skuData.set("spu_name", name);
            updateSkuDataList.add(skuData);
        }
    }

    //更新商品之后，更新产品之前
    private void updatePriceBookProductActualUnit(String spuId, IObjectData spuData, Map<String, List<IObjectData>> spuToSkuDataList) {
        List<IObjectData> skuDataList = spuToSkuDataList.get(spuId);
        if (CollectionUtils.empty(skuDataList)) {
            return;
        }
        List<String> productIds = new ArrayList<>();
        String unit = spuData.get("unit", String.class);
        for (IObjectData skuData : skuDataList) {
            String skuUnit = skuData.get("unit", String.class);
            if (!Objects.isNull(unit) && !unit.equals(skuUnit)) {
                productIds.add(skuData.getId());
            }
        }
        multiUnitService.updatePriceBookProductActualUnit(actionContext.getTenantId()
                , unit
                , productIds);
    }

    private List<IObjectData> findProductData(String tenantId, List<String> spuIdList) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, tenantId);
        SearchUtil.fillFilterIn(filters, "spu_id", spuIdList);
        SearchUtil.fillFilterIn(filters, "is_deleted", Lists.newArrayList("0", "1"));
        query.setFilters(filters);
        query.setLimit(2000);
        query.setOffset(0);
        QueryResult<IObjectData> productDataResult = serviceFacade.findBySearchQuery(new User(tenantId, "-10000"), Utils.PRODUCT_API_NAME, query);
        List<IObjectData> productDataList = productDataResult.getData();
        return productDataList;
    }

    private void sendAuditLog(String tenantId, String userId, String message) {
        AuditLogDTO dto = AuditLogDTO.builder().appName(appName).serverIp(serverIp).profile(profile).action(action).tenantId(tenantId).userId(userId).objectApiNames(Utils.SPU_API_NAME).message(message).build();
        BizLogClient.send("biz-audit-log", Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
    }
}
