package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.SaleContractImportUtil;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;

/**
 * 销售合同产品更新导入模板
 * jimzh
 * 2021/7/22 11:02
 */
public class SaleContractLineUpdateImportTemplateAction  extends StandardUpdateImportTemplateAction {

    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        SaleContractImportUtil.removeDetailImportFields(headerFieldList);
    }
}
