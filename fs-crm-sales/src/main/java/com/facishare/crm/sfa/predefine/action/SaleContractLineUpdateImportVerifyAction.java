package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.SaleContractImportUtil;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportVerifyAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;

/**
 * 销售合同产品更新导入校验
 * jimzh
 * 2021/7/22 11:03
 */
public class SaleContractLineUpdateImportVerifyAction extends StandardUpdateImportVerifyAction {
    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> fields = super.getValidImportFields();
        SaleContractImportUtil.removeDetailImportFields(fields);
        return fields;
    }
}
