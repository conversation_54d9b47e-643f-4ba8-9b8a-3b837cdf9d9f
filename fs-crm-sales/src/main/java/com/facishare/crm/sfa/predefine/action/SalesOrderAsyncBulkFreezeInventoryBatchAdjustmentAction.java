package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAsyncBulkAction;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/2/17 19:50
 */
public class SalesOrderAsyncBulkFreezeInventoryBatchAdjustmentAction extends AbstractStandardAsyncBulkAction<SalesOrderAsyncBulkFreezeInventoryBatchAdjustmentAction.Arg, SalesOrderFreezeInventoryBatchAdjustmentAction.Arg> {

    @Override
    protected String getDataIdByParam(SalesOrderFreezeInventoryBatchAdjustmentAction.Arg arg) {
        return arg.getDataId();
    }

    @Override
    protected List<SalesOrderFreezeInventoryBatchAdjustmentAction.Arg> getButtonParams() {
        return arg.getDataIds().stream()
                .map(data -> {
                    SalesOrderFreezeInventoryBatchAdjustmentAction.Arg salesOrderArg = new SalesOrderFreezeInventoryBatchAdjustmentAction.Arg();
                    salesOrderArg.setDataId(data);
                    return salesOrderArg;
                })
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.FREEZE_INVENTORY_BATCH_ADJUSTMENT.getActionCode();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.FREEZE_INVENTORY_BATCH_ADJUSTMENT.getActionCode();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.FREEZE_INVENTORY_BATCH_ADJUSTMENT.getActionCode());
    }


    @EqualsAndHashCode(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Arg extends SFAObjectPoolCommon.Arg implements Serializable {

        private List<String> dataIds;

    }
}
