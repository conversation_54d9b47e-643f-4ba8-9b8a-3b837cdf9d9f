package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.crm.sfa.predefine.handler.salesorder.freezeadjustment.FreezeInventoryBatchAdjustmentActionHandler;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/2/17 19:50
 */
public class SalesOrderFreezeInventoryBatchAdjustmentAction extends PreDefineAction<SalesOrderFreezeInventoryBatchAdjustmentAction.Arg, SalesOrderFreezeInventoryBatchAdjustmentAction.Result> {
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.FREEZE_INVENTORY_BATCH_ADJUSTMENT.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(SalesOrderFreezeInventoryBatchAdjustmentAction.Arg arg) {
        return null;
    }

    @Override
    protected Handler.Arg<SalesOrderFreezeInventoryBatchAdjustmentAction.Arg> buildHandlerArg(SimpleHandlerDescribe handlerDescribe) {
        FreezeInventoryBatchAdjustmentActionHandler.Arg handlerArg = new FreezeInventoryBatchAdjustmentActionHandler.Arg();
        handlerArg.setDataId(arg.getDataId());
        return handlerArg;
    }

    @Override
    protected SalesOrderFreezeInventoryBatchAdjustmentAction.Result doAct(SalesOrderFreezeInventoryBatchAdjustmentAction.Arg arg) {
        return new SalesOrderFreezeInventoryBatchAdjustmentAction.Result();
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Arg  extends SFAObjectPoolCommon.Arg {
        private String dataId;
    }

    @Data
    public static class Result implements Serializable {
        private String result = "success";

        private List<String> successList;
        private List<String> failedList;
        private List<String> errorList;
        private BaseObjectSaveAction.ValidationMessage validationMessage;
    }
}
