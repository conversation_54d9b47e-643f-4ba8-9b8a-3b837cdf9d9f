package com.facishare.crm.sfa.predefine.button;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.ui.layout.IButton;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class QualityInspectionRuleButtonProvider extends AbstractSfaSpecialButtonProvider {

    @Override
    public String getApiName() {
        return SFAPreDefineObject.QualityInspectionRule.getApiName();
    }

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = super.getSpecialButtons();
        buttons.add(ButtonUtils.buildButton(ObjectAction.UPDATE));
        buttons.add(ButtonUtils.buildButton(ObjectAction.DELETE));
        return buttons;
    }
}
