package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.QuoterModel;
import com.facishare.crm.sfa.predefine.service.AdvancedFormulaService;
import com.facishare.crm.sfa.predefine.service.NonstandardAttributeService;
import com.facishare.crm.sfa.predefine.service.attribute.AttributeCoreService;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class AdvancedFormulaRelatedListController extends StandardRelatedListController {
    private final AdvancedFormulaService advancedFormulaService = SpringUtil.getContext().getBean(AdvancedFormulaService.class);
    private final AttributeCoreService attributeCoreService = SpringUtil.getContext().getBean(AttributeCoreService.class);
    private final NonstandardAttributeService nonstandardAttributeService = SpringUtil.getContext().getBean(NonstandardAttributeService.class);
    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        QueryResult<IObjectData> queryResult = super.findData(query);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return queryResult;
        }
        List<IObjectData> dataList = queryResult.getData();
        Set<String> apiNames = new HashSet<>();
        Set<String> productIds = new HashSet<>();
        for (IObjectData iObjectData : dataList) {
            String apiName = iObjectData.get(QuoterModel.AdvancedFormulaModel.OBJECT_NAME, String.class);
            String productId = iObjectData.get(QuoterModel.AdvancedFormulaModel.PRODUCT_ID, String.class);
            if (StringUtils.isNotBlank(apiName)) {
                apiNames.add(apiName);
            }
            String formula = iObjectData.get(QuoterModel.AdvancedFormulaModel.FORMULA, String.class);
            if (StringUtils.isNotBlank(formula) && (formula.contains("EXT#NON_ATTR#") || formula.contains("EXT#ATTR#"))) {
                productIds.add(productId);
            }
        }
        if (CollectionUtils.isEmpty(apiNames)) {
            return queryResult;
        }
        Map<String, IObjectData> productMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(productIds)) {
            List<IObjectData> productList = serviceFacade.findObjectDataByIdsIgnoreAll(controllerContext.getTenantId(), Lists.newArrayList(productIds), Utils.PRODUCT_API_NAME);
            attributeCoreService.attachAttributeData(controllerContext.getUser()
                    , productList
                    , DBRecord.ID);
            nonstandardAttributeService.attachNonstandardAttributeData(controllerContext.getUser()
                    , productList, false);
            productMap = productList.stream().collect(Collectors.toMap(DBRecord::getId, Function.identity(), (k1, k2) -> k1));
        }
        Map<String, IObjectDescribe> descMap = serviceFacade.findObjects(controllerContext.getTenantId(), apiNames);
        Pattern pattern = Pattern.compile("\\$(.*?)\\$");
        Map<String, IObjectData> finalProductMap = productMap;
        dataList.forEach(data -> {
            String apiName = data.get(QuoterModel.AdvancedFormulaModel.OBJECT_NAME, String.class, "");
            String fieldName = data.get(QuoterModel.AdvancedFormulaModel.FIELD_NAME, String.class, "");
            IObjectDescribe describe = descMap.get(apiName);
            if (Objects.nonNull(describe)) {
                data.set(QuoterModel.AdvancedFormulaModel.OBJECT_NAME, describe.getDisplayName());
                Optional<String> fieldOpt = Optional.of(describe).map(x -> x.getFieldDescribe(fieldName)).map(IFieldDescribe::getLabel);
                fieldOpt.ifPresent(s -> data.set(QuoterModel.AdvancedFormulaModel.FIELD_NAME, s));
                String expression = advancedFormulaService.translateFormula(describe, pattern, finalProductMap, data, controllerContext.getTenantId());
                data.set(QuoterModel.AdvancedFormulaModel.FORMULA,StringUtils.replace(expression,"$",""));
            }
        });
        return queryResult;
    }
}
