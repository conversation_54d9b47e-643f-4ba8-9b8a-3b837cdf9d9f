package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.aggregatevalue.AggregateRuleService;
import com.facishare.crm.sfa.utilities.constant.AggregateRuleConstants;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 聚合规则ListHeader
 *
 * <AUTHOR>
 */
public class AggregateRuleListHeaderController extends StandardListHeaderController {
    AggregateRuleService aggregateRuleService = SpringUtil.getContext().getBean(AggregateRuleService.class);
    private final List<String> invisible_buttons = Lists.newArrayList("Import_button_default","Export_button_default","ExportFile_button_default","ExportFile","IntelligentForm_button_default");

    @Override
    protected List<IButton> getButtons() {
        List<IButton> buttons = super.getButtons();
        buttons.removeIf(x -> invisible_buttons.contains(x.getName()));
        return buttons;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result.getButtons().removeIf(x -> invisible_buttons.contains(x.get("api_name")));
        IFieldDescribe field = result.copyFieldToDescribeExt(AggregateRuleConstants.Field.AGGREGATE_OBJECT);
        if(field != null) {
            aggregateRuleService.buildAggregateObjectDescribe(controllerContext.getUser(), field);
            field.setIndex(false);
        }
        return super.after(arg, result);
    }

}
