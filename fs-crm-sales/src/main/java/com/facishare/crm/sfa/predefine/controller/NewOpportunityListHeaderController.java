package com.facishare.crm.sfa.predefine.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.crm.sfa.utilities.util.NewOpportunityUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class NewOpportunityListHeaderController extends SFAListHeaderController {
    //判断是移动端还是web端
    private String agentType;
    private static final String MOBILE= "mobile";

    @Override
    protected Result doService(Arg arg) {
        Result res = super.doService(arg);
        String tenantId = controllerContext.getTenantId();
        //移动端下发字段
        if (StringUtils.isNotEmpty(agentType)&&agentType.equals(MOBILE)){
            Boolean onlyDefaultTemplate = arg.getOnlyDefaultTemplate();
            if (ObjectUtils.isEmpty(arg.getExtraParams())||ObjectUtils.isEmpty(arg.getExtraParams().get("related_procument"))) {
                return res;
            }
            if(Boolean.TRUE==arg.getExtraParams().get("related_procument")){
                if (Boolean.TRUE==onlyDefaultTemplate) {
                    return res;
                }
                try {
                    ILayout iLayout = res.getLayout().toLayout();
                    LayoutExt layoutExt = LayoutExt.of(iLayout);
                    List<IComponent> componentList = layoutExt.getComponents();
                    if (CollectionUtil.isNotEmpty(componentList)) {
                        Optional<IComponent> component = componentList.stream().filter(x -> x.get("api_name").equals("table_component")).findFirst();
                        if (component.isPresent()) {
                            List<Map<String,Object>> fieldSection = (List<Map<String,Object>>)component.get().get("include_fields");
                            dealData(fieldSection);

                        }
                    }
                } catch (MetadataServiceException e) {
                    log.error("移动端布局,error:{},{}",tenantId,e);
                }
            }

        }
        return res;
    }

    public void dealData(List<Map<String, Object>> form_fields){
        form_fields.clear();
        Map<String,Object> field =new HashMap<>();
        field.put("api_name","name");
        field.put("render_type","text");
        field.put("field_name","name");
        field.put("is_show_label",true);
        field.put("label", I18N.text("NewOpportunityObj.field.name.label")/*商机名称*/);
        form_fields.add(field);
        Map<String,Object> account_id =new HashMap<>();
        account_id.put("api_name","account_id");
        account_id.put("render_type","object_reference");
        account_id.put("field_name","account_id");
        account_id.put("is_show_label",true);
        account_id.put("label",I18N.text("AccountObj.field.name.label")/*客户名称*/);
        form_fields.add(account_id);
        Map<String,Object> sales_status =new HashMap<>();
        sales_status.put("is_readonly",false);
        sales_status.put("is_required",false);
        sales_status.put("render_type","select_one");
        sales_status.put("field_name","sales_status");
        sales_status.put("is_show_label",true);
        sales_status.put("label",I18N.text("NewOpportunityObj.field.sales_status.label")/*阶段状态*/);
        form_fields.add(sales_status);
        Map<String,Object> amount =new HashMap<>();
        amount.put("api_name","amount");
        amount.put("render_type","currency");
        amount.put("field_name","amount");
        amount.put("is_show_label",true);
        amount.put("label",I18N.text("NewOpportunityObj.field.amount.label")/*商机金额*/);
        form_fields.add(amount);
        Map<String,Object> probability =new HashMap<>();
        probability.put("api_name","probability");
        probability.put("render_type","percentile");
        probability.put("field_name","probability");
        probability.put("is_show_label",true);
        probability.put("label",I18N.text("NewOpportunityObj.field.probability.label")/*赢率*/);
        form_fields.add(probability);
        Map<String,Object> close_date =new HashMap<>();
        close_date.put("api_name","close_date");
        close_date.put("render_type","date");
        close_date.put("field_name","close_date");
        close_date.put("is_show_label",true);
        close_date.put("label",I18N.text("NewOpportunityObj.field.close_date.label")/*预计成交日期*/);
        form_fields.add(close_date);
        Map<String,Object> owner =new HashMap<>();
        owner.put("api_name","owner");
        owner.put("render_type","employee");
        owner.put("field_name","owner");
        owner.put("is_show_label",true);
        owner.put("label",I18N.text("NewOpportunityObj.field.owner.label")/*负责人*/);
        form_fields.add(owner);
    }

    @Override
    protected String getAgentType() {
        agentType=super.getAgentType();
        return super.getAgentType();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result res = super.after(arg, result);
        ILayout listLayout = serviceFacade.getListLayoutWitchComponents(controllerContext.getUser(), objectDescribeExt.getObjectDescribe(), PageType.ListLayout, arg.getRecordTypeAPIName());
        LayoutExt.of(listLayout).getComponentByApiName(ComponentExt.LIST_COMPONENT_NAME).ifPresent(iComponent -> LayoutExt.of(res.getLayout()).addComponent(iComponent));
        ILayout layout = serviceFacade.getListLayoutWitchComponents(controllerContext.getUser(), objectDescribeExt.getObjectDescribe(), PageType.ListLayout, arg.getRecordTypeAPIName());
        NewOpportunityUtil.findListComponent(layout).ifPresent(iComponent -> LayoutExt.of(res.getLayout()).addComponent(iComponent));
        //修改 button
        ButtonUtils.editMergeButton(res);
        return res;
    }

}

