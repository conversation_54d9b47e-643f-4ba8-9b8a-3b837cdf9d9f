package com.facishare.crm.sfa.predefine.controller;


import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.InfraServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectCluster;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

@Slf4j
public class NewOpportunityWebDetailController extends SFAWebDetailController {
    private static final InfraServiceFacade INFRA_SERVICE_FACADE = SpringUtil.getContext().getBean(InfraServiceFacadeImpl.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null) {
            return newResult;
        }
        ILayout layout = newResult.getLayout().toLayout();

        WebDetailLayout webDetailLayout = WebDetailLayout.of(layout);
        webDetailLayout.removeComponents(Lists.newArrayList("LeadsObj_new_opportunity_id_related_list"));
        webDetailLayout.removeButtonsByActionCode(Lists.newArrayList("Recover"));
        if (!GrayUtil.isAllowNewOpportunityContactAtlasComponent(controllerContext.getTenantId()) && (RequestUtil.isMobileRequest() || RequestUtil.isH5MobileRequest())) {
            webDetailLayout.removeComponents(Lists.newArrayList("new_opportunity_contact_atlas"));
        }
//        if (RequestUtil.isMobileOrH5Request() || RequestUtil.isWXMiniProgram() || AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(controllerContext.getAppId())) {
//            WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("fishbone_diagram_view"));
//        }
        setStageInstanceLabel(result);
        newResult.setLayout(LayoutDocument.of(layout));
        return newResult;
    }

    @Override
    protected boolean defaultEnableQixinGroup() {
        IObjectCluster cluster = INFRA_SERVICE_FACADE.find(controllerContext.getUser(), arg.getObjectDescribeApiName());
        if (Objects.isNull(cluster) || cluster.getIsActive()) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }

    private void setStageInstanceLabel(Result result) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt ext = SearchTemplateQueryExt.of(query);
        ext.addFilter(Operator.EQ, "object_data_id", data.getId());
        ext.addFilter(Operator.EQ, "object_api_name", data.getDescribeApiName());
        ext.addFilter(Operator.IN, "state", Lists.newArrayList("in_progress", "pass", "error"));
        ext.addFilter(Operator.EQ, DBRecord.IS_DELETED, "0");
        List<IObjectData> instances = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(controllerContext.getUser(), "StageInstanceObj", query, Lists.newArrayList("name", "workflow_id", "source_workflow_id")).getData();
        if (instances.isEmpty()) {
            return;
        }
        IObjectData instance = instances.get(0);
        String workflowId = instance.get("workflow_id", String.class);
        String sourceWorkflowId = instance.get("source_workflow_id", String.class);
        if (workflowId == null || sourceWorkflowId == null) {
            return;
        }
        String lang = I18N.getContext().getLanguage();
        String tenantId = controllerContext.getTenantId();
        String label = I18NExt.getDesignatedLanguage(String.format("flow.%s.name", workflowId), tenantId, lang);
        if (label == null) {
            label = I18NExt.getDesignatedLanguage(String.format("flow.%s.name", sourceWorkflowId), tenantId, lang);
        }
        if (label != null) {
            result.getData().put("stage_instance_label", label);
        }
    }
}
