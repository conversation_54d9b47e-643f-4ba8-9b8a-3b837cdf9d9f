package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.model.Enum.DealDataTypeEnum;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crm.sfa.utilities.util.SalesOrderUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by renlb on 2019/3/25.
 */
public class OrderTodoListController extends SFATodoListController {

    protected StopWatch orderTodoListStopWatch = StopWatch.create("Order_TodoList_Controller");
    private boolean hasDeliveryRole = false;


    @Override
    protected void buildUnProcessedQuery(SearchTemplateQuery query) {
        super.buildUnProcessedQuery(query);
    }

    @Override
    protected void buildCustomUnProcessedQuery(SearchTemplateQuery query) {
        if (arg.getSessionBOCItemKey() == DealDataTypeEnum.TOBE_DELIVERY_CUSTOMER_ORDER.getSessionKey()) {
            checkRole();
            orderTodoListStopWatch.lap("OrderTodoListController buildUnProcessedQuery getDeliveryFilters  begin");
            List<IFilter> filters = SalesOrderUtil.getDeliveryFilters(controllerContext.getUser(), hasDeliveryRole);
            query.setFilters(filters);
            orderTodoListStopWatch.lap("OrderTodoListController buildUnProcessedQuery getDeliveryFilters  end");
        } else {
            List<IFilter> filters = Lists.newArrayList();
            IFilter filter = new Filter();
            filter.setFieldName("life_status");
            List<String> fieldValues = Lists.newArrayList();
            fieldValues.add(ObjectLifeStatus.UNDER_REVIEW.getCode());
            filter.setFieldValues(fieldValues);
            filter.setOperator(Operator.EQ);
            filters.add(filter);
            SearchUtil.fillFilterIn(filters, IObjectData.ID, Lists.newArrayList(""));
            query.setFilters(filters);
        }
    }

    private void checkRole() {
        List<String> roleCodes = serviceFacade.getUserRole(controllerContext.getUser());
        if (CollectionUtils.notEmpty(roleCodes) && roleCodes.contains(SalesOrderConstants.GOODS_SENDING_PERSON_ROLE)) {
            hasDeliveryRole = true;
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        orderTodoListStopWatch.logSlow(100);
        return super.after(arg, result);
    }
}
