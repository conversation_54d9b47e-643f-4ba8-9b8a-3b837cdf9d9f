package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by Sundy on 2024/11/14 15:27
 */
public class PartnerAgreementDetailDescribeLayoutController extends StandardDescribeLayoutController {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        if (!Boolean.TRUE.equals(arg.getInclude_layout())) {
            return result;
        }
        if (StringUtils.isBlank(arg.getLayout_type()) || result.getLayout() == null) {
            return result;
        }
        LayoutExt layoutExt = LayoutExt.of(result.getLayout());
        for (String onlyReadField : PartnerAgreementDetailModel.ONLY_READ_FIELDS) {
            layoutExt.getField(onlyReadField).ifPresent(field -> field.setReadOnly(true));
        }
        return result;
    }
}
