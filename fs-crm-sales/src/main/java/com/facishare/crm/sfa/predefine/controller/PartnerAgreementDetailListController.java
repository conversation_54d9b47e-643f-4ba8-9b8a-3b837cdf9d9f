package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.prm.api.enums.AgreementStatus;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.crm.sfa.service.ChannelService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel.AGREEMENT_STATUS;

/**
 * Created by Sundy on 2024/10/23 17:32
 */
public class PartnerAgreementDetailListController extends StandardListController {
    private final ChannelService channelService = SpringUtil.getContext().getBean("channelServiceProvider", ChannelService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        handleDataButton(after);
        return after;
    }

    private void handleDataButton(Result after) {
        List<ObjectDataDocument> dataList = after.getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        ButtonInfo buttonInfo = after.getButtonInfo();
        if (buttonInfo == null) {
            return;
        }
        Map<String, List<String>> buttonMap = buttonInfo.getButtonMap();
        if (buttonMap == null || buttonMap.isEmpty()) {
            return;
        }
        String admissionObject = channelService.fetchChannelAdmissionObject(controllerContext.getUser());
        Set<String> admissionDataIds = dataList.stream().map(d -> getAdmissionDataId(d.toObjectData())).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Set<String> allowInitiateRenewalDataIds = channelService.allowInitiateRenewal(controllerContext.getUser(), admissionDataIds, admissionObject);
        List<String> allowInitiateRenewalIds = dataList.stream()
                .map(ObjectDataDocument::toObjectData)
                .filter(data -> AgreementStatus.find(DataUtils.getValue(data, AGREEMENT_STATUS, String.class, null)) == AgreementStatus.ACTIVE)
                .filter(d -> StringUtils.isNotBlank(getAdmissionDataId(d)) && allowInitiateRenewalDataIds.contains(getAdmissionDataId(d)))
                .map(DBRecord::getId)
                .collect(Collectors.toList());
        buttonMap.forEach((id, buttons) -> {
            if (allowInitiateRenewalIds.contains(id) || CollectionUtils.isEmpty(buttons)) {
                return;
            }
            buttons.removeIf(b -> ObjectAction.INITIATE_RENEWAL.getButtonApiName().equals(b));
        });
    }

    private String getAdmissionDataId(IObjectData objectData) {
        return channelService.fetchAdmissionDataId(controllerContext.getUser(), objectData);
    }
}
