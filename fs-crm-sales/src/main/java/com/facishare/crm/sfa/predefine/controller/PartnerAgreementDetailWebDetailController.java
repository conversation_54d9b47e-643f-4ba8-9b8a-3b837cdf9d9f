package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.prm.api.enums.AgreementStatus;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.crm.sfa.service.ChannelService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import static com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel.AGREEMENT_STATUS;

/**
 * Created by Sundy on 2024/10/21 16:03
 */
public class PartnerAgreementDetailWebDetailController extends StandardWebDetailController {
    private final ChannelService channelService = SpringUtil.getContext().getBean("channelServiceProvider", ChannelService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        String admissionObject = channelService.fetchChannelAdmissionObject(controllerContext.getUser());
        String belongDataId = channelService.getAdmissionObjectDataId(result.getData().toObjectData(), admissionObject);
        AgreementStatus agreementStatus = AgreementStatus.find(DataUtils.getValue(data, AGREEMENT_STATUS, String.class, null));
        if (!channelService.allowInitiateRenewal(controllerContext.getUser(), admissionObject, belongDataId) ||
                agreementStatus != AgreementStatus.ACTIVE) {
            // 移除发起签约按钮
            WebDetailLayout.of(result.getLayout().toLayout()).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.INITIATE_RENEWAL.getActionCode()));
        }
        return after;
    }
}
