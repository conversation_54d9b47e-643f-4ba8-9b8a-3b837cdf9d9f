package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.action.PartnerRenewExpirationAction;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.service.IPrmConfigService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.*;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by quzf on 2018/4/12.
 */
@Slf4j
public class PartnerDescribeLayoutController extends SFADescribeLayoutController {
    private static final BizConfigThreadLocalCacheService configCache = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private static final IPrmConfigService prmConfigService = SpringUtil.getContext().getBean(IPrmConfigService.class);
    private static final String LEADS_ID = "leads_id";

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        if (!Boolean.TRUE.equals(arg.getInclude_layout())) {
            return result;
        }
        if (StringUtils.isBlank(arg.getLayout_type()) || result.getLayout() == null) {
            return result;
        }
        LayoutExt layoutExt = LayoutExt.of(result.getLayout());
        switch (arg.getLayout_type()) {
            case LayoutTypes.ADD:
                addSubmitAndAddContactButton(controllerContext.getUser(), layoutExt);
                layoutExt.removeFields(Lists.newArrayList(LEADS_ID, PartnerRenewExpirationAction.EXPIRED_TIME));
                layoutHandler(result.getLayout().toLayout());
                break;
            case LayoutTypes.EDIT:
                if (!prmConfigService.allowEditPartnerName(controllerContext.getUser())) {
                    setReadOnly(layoutExt, IObjectData.NAME, true);
                }
                layoutExt.removeFields(Lists.newArrayList(PartnerRenewExpirationAction.EXPIRED_TIME));
                break;
            default:
                break;
        }
        if (configCache.isOpenPartnerAddress(controllerContext.getTenantId())) {
            try {
                removeAreaLocation(layoutExt);
            } catch (Exception e) {
                log.warn("removeAreaLocation ex, tenant:{}", controllerContext.getTenantId(), e);
            }
        }
        return result;
    }

    private void addSubmitAndAddContactButton(User user, LayoutExt layoutExt) {
        ObjectAction submitAndAddContact = ObjectAction.CREATE_SAVE_CREATE_CONTACT;
        boolean havaFunc = serviceFacade.funPrivilegeCheck(user, SFAPreDefine.Partner.getApiName(), "SubmitAndAddContact");
        if (serviceFacade.isEditLayoutEnable(controllerContext.getTenantId(), controllerContext.getObjectApiName(), true)) {
            if (havaFunc) {
                return;
            }
            List<IButton> buttonList = layoutExt.getButtons().stream().filter(b -> !submitAndAddContact.getButtonApiName().equals(b.getName())).collect(Collectors.toList());
            layoutExt.setButtons(buttonList);
            return;
        }
        if (!havaFunc) {
            return;
        }
        IButton button = new Button();
        button.setName(submitAndAddContact.getButtonApiName());
        button.setAction(submitAndAddContact.getActionCode());
        button.setActionType(IButton.ACTION_TYPE_DEFAULT);
        button.setLabel(submitAndAddContact.getActionLabel());
        layoutExt.addButtons(button);
    }

    private void removeAreaLocation(LayoutExt layoutExt) throws MetadataServiceException {
        ILayout layout = layoutExt.getLayout();
        List<IComponent> componentList = layout.getComponents();
        for (IComponent iComponent : componentList) {
            if (iComponent instanceof IFormComponent) {
                List<IFieldSection> fieldSections = ((IFormComponent) iComponent).getFieldSections();
                boolean success = fieldSections.removeIf(x -> "area_location".equals(x.getName()));
                if (success) {
                    ((IFormComponent) iComponent).setFieldSections(fieldSections);
                    break;
                }
            }
        }
        layout.setComponents(componentList);
    }

    private void removeFields(LayoutExt layoutExt, String fieldApiName) {
        layoutExt.removeFields(Lists.newArrayList(fieldApiName));
    }

    private void setReadOnly(LayoutExt layoutExt, String fieldApiName, boolean readOnly) {
        Optional<IFormField> nameField = layoutExt.getField(fieldApiName);
        nameField.ifPresent(iFormField -> iFormField.setReadOnly(readOnly));
    }

    /**
     * 处理布局：
     * 新建可编辑计划和默认积分池
     */
    private void layoutHandler(ILayout layout) {
        if (!"layout_store__c".equals(layout.getName())) {
            return;
        }
        LayoutExt layoutExt = LayoutExt.of(layout);
        setReadOnly(layoutExt, "create_default_org", false);
        setReadOnly(layoutExt, "create_default_point_pool", false);
        setReadOnly(layoutExt, "default_point_type", false);
    }
}
