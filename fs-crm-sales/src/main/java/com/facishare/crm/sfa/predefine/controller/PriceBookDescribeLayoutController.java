package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSONArray;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.PriceBookCommonService;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.TabSection;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by luxin on 2018/6/5.
 */
public class PriceBookDescribeLayoutController extends StandardDescribeLayoutController {

    private PriceBookCommonService priceBookCommonService = SpringUtil.getContext().getBean(PriceBookCommonService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);

        ILayout layout = result.getLayout().toLayout();


        String layoutType = arg.getLayout_type();
        removeFieldsFromLayout(result, layout, layoutType);
        removeDetailObjectDescribe(result, controllerContext.getUser());
        return result;
    }

    private void removeFieldsFromLayout(Result result, ILayout layout, String layoutType) {
        if (Objects.equals(LayoutExt.Edit_LAYOUT_TYPE, layoutType) || Objects.equals(LayoutExt.Add_LAYOUT_TYPE, layoutType)) {
            LayoutExt layoutExt = LayoutExt.of(layout);
            try {
                List<IComponent> components = layoutExt.getComponents();
                List<Map<Object, Object>> fieldSections;
                JSONArray formFields;
                for (IComponent component : components) {
                    fieldSections = component.get("field_section", List.class);
                    if (CollectionUtils.isEmpty(fieldSections)) {
                        continue;
                    }
                    for (Map<Object, Object> fieldSection : fieldSections) {
                        if (fieldSection.get("form_fields") instanceof JSONArray) {
                            formFields = (JSONArray) fieldSection.get("form_fields");
                            Iterator<Object> formFieldIterator = formFields.iterator();
                            while (formFieldIterator.hasNext()) {
                                Object formField = formFieldIterator.next();
                                if (formField instanceof Map) {
                                    if ("is_standard".equals(((Map) formField).get("field_name"))) {
                                        formFieldIterator.remove();
                                    }
                                }
                            }
                            fieldSection.put("form_fields", formFields);
                        }
                    }
                    component.set("field_section", fieldSections);
                }
                layoutExt.setComponents(components);
            } catch (MetadataServiceException e) {
                log.error("layoutExt.getComponents error. tenantId {},apiName {}", controllerContext.getTenantId(), arg.getApiname(), e);
            }
            result.setLayout(LayoutDocument.of(layoutExt));
        }
        if (GrayUtil.isPriceBookReform(controllerContext.getTenantId()) && Objects.equals(LayoutExt.Edit_LAYOUT_TYPE, layoutType)) {
            String standardId = priceBookCommonService.getStandardPriceBookId(controllerContext.getUser());
            LayoutExt.of(layout).getFormComponent()
                    .map(FormComponentExt::getFormComponent)
                    .map(FormComponent.class::cast).ifPresent(formComponent -> {
                if (StringUtils.isNotEmpty(standardId) && standardId.equals(arg.getData_id())) {
                    PreDefLayoutUtil.setFormComponentFieldsReadOnly(formComponent, Lists.newArrayList("apply_org_range", "apply_partner_range", "apply_account_range"));
                }
            });
        }
    }

    private void removeDetailObjectDescribe(Result result, User user) {
        if (!GrayUtil.isPriceBookReform(user.getTenantId())) {
            return;
        }
        List<DetailObjectListResult> detailObjectListResultList = result.getDetailObjectList();
        if (CollectionUtils.isEmpty(detailObjectListResultList)) {
            return;
        }
        ILayout resultLayout = result.getLayout().toLayout();
        ILayout layout = serviceFacade.findObjectLayoutByType(user, arg.getRecordType_apiName(), SFAPreDefineObject.PriceBook.getApiName(), resultLayout.getLayoutType());
        if (layout == null) {
            return;
        }
        AtomicBoolean isApplyAccountRange = new AtomicBoolean(false);
        LayoutExt layoutExt = LayoutExt.of(layout);
        layoutExt.getFormComponent().ifPresent(formComponent -> {
            for (IFieldSection fieldSection : formComponent.getFieldSections()) {
                List<IFormField> fields = fieldSection.getFields();
                for (IFormField formField : fields) {
                    if ("apply_account_range".equals(formField.getFieldName())) {
                        isApplyAccountRange.set(true);
                        break;
                    }
                }
            }
        });
        if (!isApplyAccountRange.get()) {
            detailObjectListResultList.removeIf(detailObjectListResult -> "PriceBookAccountObj".equals(detailObjectListResult.getObjectApiName()));
            try {
                result.getLayout().toLayout().getComponents().stream().filter(x -> ComponentExt.of(x).isTabs()).map(x -> (ITabsComponent) x).forEach(x -> {
                    List<TabSection> tabSections = x.getTabs();
                    Iterator<TabSection> tabSectionIterator = tabSections.iterator();
                    Iterator<List<String>> childComponents = nullToEmpty(x.getComponents()).iterator();
                    while (childComponents.hasNext()) {
                        tabSectionIterator.next();
                        List<String> tabChild = nullToEmpty(childComponents.next());
                        if (tabChild.contains("PriceBookAccountObj_md_group_component")) {
                            tabSectionIterator.remove();
                            childComponents.remove();
                        }
                    }
                    x.setTabs(tabSections);
                });
            } catch (MetadataServiceException e) {
                log.error("getComponents error msg is{}", e.getMessage());
            }
        }
    }

    public <T> List<T> nullToEmpty(List<T> list) {
        return Objects.isNull(list) ? Lists.newArrayList() : list;
    }
}
