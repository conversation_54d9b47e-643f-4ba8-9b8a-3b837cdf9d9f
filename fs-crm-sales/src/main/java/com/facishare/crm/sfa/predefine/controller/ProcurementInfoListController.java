package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.procurement.qianlima.QlmProcurementService;
import com.facishare.crm.sfa.predefine.service.Procurement.ProcurementService;
import com.facishare.crm.sfa.predefine.service.ProcurementInfoService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.util.*;

import static com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementConstants.ProcurementInfo.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/9/27 15:29
 */
@Slf4j
public class ProcurementInfoListController extends StandardListController {

    private static final String TRANSFER_OPPORTUNITY_COUNT = "new_opportunity_count";
    private static final String TRANSFER_LEADS_COUNT = "leads_count";
    private static final String TRANSFER_ACCOUNT_COUNT = "account_count";
    private static final String TRANSFER_OPPORTUNITY_BUTTON = "button_transferNewOpportunity__c";
    private static final String TRANSFER_LEADS_BUTTON = "button_transferLeads__c";
    private static final String TRANSFER_ACCOUNT_BUTTON = "button_transferAccount_procurementInfo__c";
    private static final List<String> buttonLists = Lists.newArrayList(TRANSFER_OPPORTUNITY_BUTTON, TRANSFER_LEADS_BUTTON, TRANSFER_ACCOUNT_BUTTON);

    private static final ProcurementService procurementService = SpringUtil.getContext().getBean(ProcurementService.class);
    private static final ObjectDataServiceImpl objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private static final ProcurementInfoService procurementInfoService = SpringUtil.getContext().getBean(ProcurementInfoService.class);

    @Override
    protected void before(Arg arg) {
        replaceSearchQueryInfoJson(arg);
        super.before(arg);
    }

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        return callerBidStatusSearchQuery(query);
    }

    /**
     * 根据千里马订阅器条件设置订阅器条件
     */
    private void replaceSearchQueryInfoJson(Arg arg) {
        String searchQueryInfoJson = arg.getSearchQueryInfo();
        JSONObject searchQueryInfo = JSON.parseObject(searchQueryInfoJson);
        JSONArray filters = searchQueryInfo.getJSONArray("filters");
        for (int i = filters.size() - 1; i >= 0; i--) {
            JSONObject filter = filters.getJSONObject(i);
            if ("qlm_subscriber".equals(filter.getString(Filter.FIELD_NAME))) {
                filter.put(Filter.FIELD_NAME, "subscriber");
                arg.setSearchQueryInfo(searchQueryInfo.toJSONString());
                break;
            }
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        String qlmToken = procurementInfoService.getQlmToken(controllerContext.getTenantId(), false);
        for (ObjectDataDocument document : result.getDataList()) {
            document.put(AREA, procurementService.getArea(document.get(PROVINCE), document.get(CITY)));
            if ("2".equals(document.get("data_source")) && ObjectUtils.isNotEmpty(document.get(BID_ID))) {
                String url = QlmProcurementService.getWEB_HOST() + "/open-pc/#/affiche-detail/" + document.get(BID_ID) + "?token=" + qlmToken;
                document.put("source_url", url);
            }
            document.put(BID_ID, document.getId());
            document.put(BIDING_DAYS, procurementService.getDaysByMillis(document.get(BIDING_END_TIME) == null ? "" : document.get(BIDING_END_TIME).toString()));
            document.put(TENDER_DAYS, procurementService.getDaysByMillis(document.get(TENDER_END_TIME) == null ? "" : document.get(TENDER_END_TIME).toString()));
            if ("1".equals(document.get(BID_TYPE))) {
                document.put("caller_bid_status", procurementService.getCallerStatus(document.get(BIDING_END_TIME) == null ? null : Long.valueOf(document.get(BIDING_END_TIME).toString()),
                        document.get(TENDER_END_TIME) == null ? null : Long.valueOf(document.get(TENDER_END_TIME).toString())));
            }
        }
        result = super.after(arg, result);

        // 查询转换情况
        List<String> isTransferNewOpportunity = Lists.newArrayList();
        List<String> isTransferLeads = Lists.newArrayList();
        List<String> isTransferAccount = getIsTransferAccount(result);
        List<String> notHaveEnterprise = Lists.newArrayList();
        result.getDataList().forEach(o -> {
            Object newOpportunityCount = o.get(TRANSFER_OPPORTUNITY_COUNT);
            Object leadsCount = o.get(TRANSFER_LEADS_COUNT);
            Object callerEnterprise = o.get("caller_enterprise");
            if (ObjectUtils.isNotEmpty(newOpportunityCount) && !"0".equals(newOpportunityCount.toString())) {
                isTransferNewOpportunity.add(o.getId());
            }
            if (ObjectUtils.isNotEmpty(leadsCount) && !"0".equals(leadsCount.toString())) {
                isTransferLeads.add(o.getId());
            }
            o.put(TRANSFER_ACCOUNT_COUNT, "0");
            if (isTransferAccount.contains(o.getId())) {
                o.put(TRANSFER_ACCOUNT_COUNT, "1");
            }
            if (ObjectUtils.isEmpty(callerEnterprise)) {
                notHaveEnterprise.add(o.getId());
            }
        });
        // 屏蔽转换按钮
        ButtonInfo buttonInfo = result.getButtonInfo();
        if (ObjectUtils.isNotEmpty(buttonInfo) && ObjectUtils.isNotEmpty(buttonInfo.getButtonMap())) {
            Map<String, List<String>> buttonMapList = buttonInfo.getButtonMap();
            buttonMapList.forEach((k, v) -> {
                buttonMapList.get(k).removeIf(x -> !buttonLists.contains(x));
                // 屏蔽转商机按钮
                if (isTransferNewOpportunity.contains(k)) {
                    v.remove(TRANSFER_OPPORTUNITY_BUTTON);
                }
                // 屏蔽转线索按钮
                if (isTransferLeads.contains(k)) {
                    v.remove(TRANSFER_LEADS_BUTTON);
                }
                // 屏蔽转客户按钮
                if (isTransferAccount.contains(k) || notHaveEnterprise.contains(k)) {
                    v.remove(TRANSFER_ACCOUNT_BUTTON);
                }
            });
        }
        return result;
    }

    public List<String> getIsTransferAccount(Result result) {
        // 汇总主体和标讯公告一对多关系
        Map<String, List<String>> enterpriseIds = new HashMap<>();
        result.getDataList().forEach(o -> {
            Object callerEnterprise = o.get("caller_enterprise");
            if (ObjectUtils.isEmpty(callerEnterprise)) {
                return;
            }
            String eId = ((List) callerEnterprise).get(0).toString();
            if (ObjectUtils.isEmpty(enterpriseIds.get(eId))) {
                enterpriseIds.put(eId, Lists.newArrayList(o.getId()));
            } else {
                enterpriseIds.get(eId).add(o.getId());
            }
        });
        // 查主体的转换记录
        Set<String> eIds = enterpriseIds.keySet();
        if (ObjectUtils.isEmpty(eIds)){
            return Lists.newArrayList();
        }
        StringBuilder resSql = new StringBuilder("select procurement_enterprise_id, account_id from biz_procurement_transfer_log where procurement_enterprise_id in ('");
        resSql.append(String.join("','", eIds));
        resSql.append("') and tenant_id = '");
        resSql.append(SqlEscaper.pg_escape(controllerContext.getTenantId()));
        resSql.append("' and is_deleted = 0 and account_id is not null");
        List<Map> accountTransformResult = null;
        try {
            accountTransformResult = objectDataService.findBySql(controllerContext.getTenantId(), resSql.toString());
        } catch (MetadataServiceException e) {
            log.error("sql:{}", resSql, e);
        }
        if (ObjectUtils.isEmpty(accountTransformResult)){
            return Lists.newArrayList();
        }
        // 转换记录结果汇总
        List<String> ret = Lists.newArrayList();
        accountTransformResult.forEach(o -> {
            String pId = o.get("procurement_enterprise_id").toString();
            ret.addAll(enterpriseIds.get(pId));
        });
        return ret;
    }

    /**
     * 支持招标状态字段进行筛选，招标状态是虚拟字段 <br/>
     * 招标状态计算的方式: {@link com.facishare.crm.sfa.predefine.service.Procurement.ProcurementService#getCallerStatus(Long, Long)} <br/>
     */
    public SearchTemplateQuery callerBidStatusSearchQuery(SearchTemplateQuery searchTemplateQuery) {
        List<IFilter> callerBidStatusIFilterList = Lists.newArrayList();
        List<IFilter> standardFilterFilterList = Lists.newArrayList();
        searchTemplateQuery.getFilters().forEach(x -> {
            if ("caller_bid_status".equals(x.getFieldName())) {
                callerBidStatusIFilterList.add(x);
            } else {
                standardFilterFilterList.add(x);
            }
        });
        // 不包含招标状态
        if (callerBidStatusIFilterList.isEmpty()) {
            return searchTemplateQuery;
        }
        List<Wheres> iFilterWheres = Lists.newArrayList();
        Date nowDate = new Date();
        // 处理招标状态字段
        callerBidStatusIFilterList.forEach(x -> {
            switch (x.getOperator()) {
                case IS:
                    // 为空 一级公告类型不等于1
                    IFilter isFilter = new Filter();
                    isFilter.setFieldName("bid_type");
                    isFilter.setFieldValues(Lists.newArrayList("1"));
                    isFilter.setOperator(Operator.N);
                    standardFilterFilterList.add(isFilter);
                    break;
                case ISN:
                    // 不为空 一级公告类型等于1
                    IFilter isnFilter = new Filter();
                    isnFilter.setFieldName("bid_type");
                    isnFilter.setFieldValues(Lists.newArrayList("1"));
                    isnFilter.setOperator(Operator.EQ);
                    standardFilterFilterList.add(isnFilter);
                    break;
                case EQ:
                    // 等于 一级公告类型等于1
                    IFilter eqIsnFilter = new Filter();
                    eqIsnFilter.setFieldName("bid_type");
                    eqIsnFilter.setFieldValues(Lists.newArrayList("1"));
                    eqIsnFilter.setOperator(Operator.EQ);
                    standardFilterFilterList.add(eqIsnFilter);
                    if ("1".equals(x.getFieldValues().get(0))) {
                        // 标书获取中：获取标书截止日期 不为空 且 获取标书截止日期 大于等于现在
                        IFilter tenderEndTimeNotNullFilter = new Filter();
                        tenderEndTimeNotNullFilter.setFieldName("tender_end_time");
                        tenderEndTimeNotNullFilter.setFieldValues(Lists.newArrayList());
                        tenderEndTimeNotNullFilter.setOperator(Operator.ISN);
                        IFilter tenderEndTimeGteFilter = new Filter();
                        tenderEndTimeGteFilter.setFieldName("tender_end_time");
                        tenderEndTimeGteFilter.setFieldValues(Lists.newArrayList(String.valueOf(nowDate.getTime())));
                        tenderEndTimeGteFilter.setOperator(Operator.GTE);
                        standardFilterFilterList.add(tenderEndTimeNotNullFilter);
                        standardFilterFilterList.add(tenderEndTimeGteFilter);
                    } else if ("2".equals(x.getFieldValues().get(0))) {
                        // 投标中： （投标截止日期 不为空 且 投标截止日期 大于等于现在） 且 （获取标书截止日期 为空 或 获取标书截止日期 小于现在）
                        IFilter bidingEndTimeNotNullFilter = new Filter();
                        bidingEndTimeNotNullFilter.setFieldName(BIDING_END_TIME);
                        bidingEndTimeNotNullFilter.setFieldValues(Lists.newArrayList());
                        bidingEndTimeNotNullFilter.setOperator(Operator.ISN);
                        IFilter bidingEndTimeGteFilter = new Filter();
                        bidingEndTimeGteFilter.setFieldName(BIDING_END_TIME);
                        bidingEndTimeGteFilter.setFieldValues(Lists.newArrayList(String.valueOf(nowDate.getTime())));
                        bidingEndTimeGteFilter.setOperator(Operator.GTE);
                        standardFilterFilterList.add(bidingEndTimeNotNullFilter);
                        standardFilterFilterList.add(bidingEndTimeGteFilter);

                        IFilter tenderEndTimeNullFilter = new Filter();
                        tenderEndTimeNullFilter.setFieldName("tender_end_time");
                        tenderEndTimeNullFilter.setFieldValues(Lists.newArrayList());
                        tenderEndTimeNullFilter.setOperator(Operator.IS);
                        Wheres tenderEndTimeNotNullWheres = new Wheres();
                        tenderEndTimeNotNullWheres.setFilters(Lists.newArrayList(tenderEndTimeNullFilter));
                        tenderEndTimeNotNullWheres.setConnector("OR");
                        iFilterWheres.add(tenderEndTimeNotNullWheres);

                        IFilter tenderEndTimeLtFilter = new Filter();
                        tenderEndTimeLtFilter.setFieldName("tender_end_time");
                        tenderEndTimeLtFilter.setFieldValues(Lists.newArrayList(String.valueOf(nowDate.getTime())));
                        tenderEndTimeLtFilter.setOperator(Operator.LT);
                        Wheres tenderEndTimeLtWheres = new Wheres();
                        tenderEndTimeLtWheres.setFilters(Lists.newArrayList(tenderEndTimeLtFilter));
                        tenderEndTimeLtWheres.setConnector("OR");
                        iFilterWheres.add(tenderEndTimeLtWheres);
                    } else if ("3".equals(x.getFieldValues().get(0))) {
                        // 投标已截止： 投标截止日期 不为空 且 投标截止日期 小于现在
                        IFilter bidingEndTimeNotNullFilter = new Filter();
                        bidingEndTimeNotNullFilter.setFieldName(BIDING_END_TIME);
                        bidingEndTimeNotNullFilter.setFieldValues(Lists.newArrayList());
                        bidingEndTimeNotNullFilter.setOperator(Operator.ISN);
                        IFilter bidingEndTimeGteFilter = new Filter();
                        bidingEndTimeGteFilter.setFieldName(BIDING_END_TIME);
                        bidingEndTimeGteFilter.setFieldValues(Lists.newArrayList(String.valueOf(nowDate.getTime())));
                        bidingEndTimeGteFilter.setOperator(Operator.LT);
                        standardFilterFilterList.add(bidingEndTimeNotNullFilter);
                        standardFilterFilterList.add(bidingEndTimeGteFilter);
                    } else {
                        // 其他 两个时间都为空
                        IFilter bidingEndTimeNullFilter = new Filter();
                        bidingEndTimeNullFilter.setFieldName(BIDING_END_TIME);
                        bidingEndTimeNullFilter.setFieldValues(Lists.newArrayList());
                        bidingEndTimeNullFilter.setOperator(Operator.IS);
                        standardFilterFilterList.add(bidingEndTimeNullFilter);
                        IFilter tenderEndTimeNullFilter = new Filter();
                        tenderEndTimeNullFilter.setFieldName("tender_end_time");
                        tenderEndTimeNullFilter.setFieldValues(Lists.newArrayList());
                        tenderEndTimeNullFilter.setOperator(Operator.IS);
                        standardFilterFilterList.add(tenderEndTimeNullFilter);
                    }
                    break;
                case N:
                    if ("1".equals(x.getFieldValues().get(0))) {
                        // 不等于 标书获取中：获取标书截止日期 为空 或 获取标书截止日期 小于现在
                        IFilter tenderEndTimeNullFilter = new Filter();
                        tenderEndTimeNullFilter.setFieldName("tender_end_time");
                        tenderEndTimeNullFilter.setFieldValues(Lists.newArrayList());
                        tenderEndTimeNullFilter.setOperator(Operator.IS);
                        Wheres tenderEndTimeNotNullWheres = new Wheres();
                        tenderEndTimeNotNullWheres.setFilters(Lists.newArrayList(tenderEndTimeNullFilter));
                        tenderEndTimeNotNullWheres.setConnector("OR");
                        iFilterWheres.add(tenderEndTimeNotNullWheres);
                        IFilter tenderEndTimeLtFilter = new Filter();
                        tenderEndTimeLtFilter.setFieldName("tender_end_time");
                        tenderEndTimeLtFilter.setFieldValues(Lists.newArrayList(String.valueOf(nowDate.getTime())));
                        tenderEndTimeLtFilter.setOperator(Operator.LT);
                        Wheres tenderEndTimeLtWheres = new Wheres();
                        tenderEndTimeLtWheres.setFilters(Lists.newArrayList(tenderEndTimeLtFilter));
                        tenderEndTimeLtWheres.setConnector("OR");
                        iFilterWheres.add(tenderEndTimeLtWheres);
                    } else if ("2".equals(x.getFieldValues().get(0))) {
                        // 不等于 投标中：投标截止日期 为空 或 投标截止日期 小于现在
                        IFilter bidingEndTimeNullFilter = new Filter();
                        bidingEndTimeNullFilter.setFieldName(BIDING_END_TIME);
                        bidingEndTimeNullFilter.setFieldValues(Lists.newArrayList());
                        bidingEndTimeNullFilter.setOperator(Operator.IS);
                        Wheres bidingEndTimeNotNullWheres = new Wheres();
                        bidingEndTimeNotNullWheres.setFilters(Lists.newArrayList(bidingEndTimeNullFilter));
                        bidingEndTimeNotNullWheres.setConnector("OR");
                        iFilterWheres.add(bidingEndTimeNotNullWheres);
                        IFilter bidingEndTimeLtFilter = new Filter();
                        bidingEndTimeLtFilter.setFieldName(BIDING_END_TIME);
                        bidingEndTimeLtFilter.setFieldValues(Lists.newArrayList(String.valueOf(nowDate.getTime())));
                        bidingEndTimeLtFilter.setOperator(Operator.LT);
                        Wheres bidingEndTimeLtWheres = new Wheres();
                        bidingEndTimeLtWheres.setFilters(Lists.newArrayList(bidingEndTimeLtFilter));
                        bidingEndTimeLtWheres.setConnector("OR");
                        iFilterWheres.add(bidingEndTimeLtWheres);
                    } else if ("3".equals(x.getFieldValues().get(0))) {
                        // 不等于 投标已截止：投标截止日期 为空 或 投标截止日期 大于等于现在
                        IFilter bidingEndTimeNullFilter = new Filter();
                        bidingEndTimeNullFilter.setFieldName(BIDING_END_TIME);
                        bidingEndTimeNullFilter.setFieldValues(Lists.newArrayList());
                        bidingEndTimeNullFilter.setOperator(Operator.IS);
                        Wheres bidingEndTimeNotNullWheres = new Wheres();
                        bidingEndTimeNotNullWheres.setFilters(Lists.newArrayList(bidingEndTimeNullFilter));
                        bidingEndTimeNotNullWheres.setConnector("OR");
                        iFilterWheres.add(bidingEndTimeNotNullWheres);
                        IFilter bidingEndTimeGteFilter = new Filter();
                        bidingEndTimeGteFilter.setFieldName(BIDING_END_TIME);
                        bidingEndTimeGteFilter.setFieldValues(Lists.newArrayList(String.valueOf(nowDate.getTime())));
                        bidingEndTimeGteFilter.setOperator(Operator.GTE);
                        Wheres bidingEndTimeGteWheres = new Wheres();
                        bidingEndTimeGteWheres.setFilters(Lists.newArrayList(bidingEndTimeGteFilter));
                        bidingEndTimeGteWheres.setConnector("OR");
                        iFilterWheres.add(bidingEndTimeGteWheres);
                    } else {
                        // 其他 两个时间都不为空
                        IFilter bidingEndTimeNotNullFilter = new Filter();
                        bidingEndTimeNotNullFilter.setFieldName(BIDING_END_TIME);
                        bidingEndTimeNotNullFilter.setFieldValues(Lists.newArrayList());
                        bidingEndTimeNotNullFilter.setOperator(Operator.ISN);
                        standardFilterFilterList.add(bidingEndTimeNotNullFilter);
                        IFilter tenderEndTimeNotNullFilter = new Filter();
                        tenderEndTimeNotNullFilter.setFieldName("tender_end_time");
                        tenderEndTimeNotNullFilter.setFieldValues(Lists.newArrayList());
                        tenderEndTimeNotNullFilter.setOperator(Operator.ISN);
                        standardFilterFilterList.add(tenderEndTimeNotNullFilter);
                    }
                    break;
                default:
                    break;
            }
        });
        searchTemplateQuery.setWheres(iFilterWheres);
        searchTemplateQuery.getFilters().clear();
        searchTemplateQuery.setFilters(standardFilterFilterList);
        return searchTemplateQuery;
    }


}
