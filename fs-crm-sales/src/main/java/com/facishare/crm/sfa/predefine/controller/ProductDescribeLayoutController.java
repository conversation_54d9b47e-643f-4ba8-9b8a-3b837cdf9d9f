package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.ProductCategoryBizService;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.util.LayoutUtils;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Created by luxin on 2018/1/16.
 */
// TODO 产品组 在新建的时候是可以编辑的,在编辑的时候 只读
public class ProductDescribeLayoutController extends SODescribeLayoutController {
    private ProductCategoryBizService productCategoryBizService = SpringUtil.getContext().getBean(ProductCategoryBizService.class);

    @Override
    protected void before(Arg arg) {
        if (RequestUtil.isMobileOrH5Request()) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MOBILE_NOT_SUPPORT_PRODUCT));
        }
        super.before(arg);

    }

    @Override
    protected Result after(Arg arg, Result result) {
        boolean isSpuOpen = SFAConfigUtil.isSpuOpen(controllerContext.getTenantId());
        LayoutDocument layout = result.getLayout();
        LayoutExt layoutExt = LayoutExt.of(layout.toLayout());
        Optional<FormComponentExt> formComponentExtOptional = layoutExt.getFormComponent();
        Set<String> fieldApiNames = Sets.newHashSet("on_shelves_time", "off_shelves_time");
        List<String> removeFields = Lists.newArrayList("owner", "product_spec");
        if (UdobjConstants.LAYOUT_TYPE_ADD.equals(arg.getLayout_type())) {
            formComponentExtOptional.ifPresent(o -> {
                LayoutUtils.removeFormComponentAddAndEditButton4MobileOrH5(o);
                LayoutUtils.removeFormComponentSystemSection(o);
                if (isSpuOpen) {
                    LayoutUtils.modifyFormComponentFieldsProperty(o, LayoutUtils.PRODUCT_NEED_MODIFY_FIELD_INFO);
                    fieldApiNames.addAll(removeFields);
                }
                fieldApiNames.add(ProductConstants.IS_PACKAGE);
                LayoutUtils.removeFormComponentFields(o, fieldApiNames);
            });
            // 保存并添加单价目表 由前端自定义
            // issuePriceBookAddButton(layoutExt);
        } else if (UdobjConstants.LAYOUT_TYPE_EDIT.equals(arg.getLayout_type())) {
            formComponentExtOptional.ifPresent(o -> {
                LayoutUtils.removeFormComponentAddAndEditButton4MobileOrH5(o);
                LayoutUtils.removeFormComponentSystemSection(o);
                // 会修改产品 只读/必填 等属性
                if (isSpuOpen) {
                    LayoutUtils.modifyFormComponentFieldsProperty(o, LayoutUtils.PRODUCT_EDIT_NEED_MODIFY_FIELD_INFO);
                    fieldApiNames.addAll(removeFields);
                }
                LayoutUtils.removeFormComponentFields(o, fieldApiNames);
            });
        }
        productCategoryBizService.removeLayoutCategoryField(controllerContext.getUser(), layout.toLayout());
        productCategoryBizService.buildCategorySelectOptions(controllerContext.getUser(), result.getObjectDescribe());
        return super.after(arg, result);
    }

    @Override
    protected boolean supportSaveAndCreate() {
        return true;
    }

    @Override
    protected boolean supportSaveDraft() {
        return true;
    }

}
