package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.*;
import com.facishare.crm.sfa.predefine.service.attribute.AttributeCoreService;
import com.facishare.crm.sfa.predefine.service.attribute.AttributeRangeService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyCoreService;
import com.facishare.crm.sfa.predefine.service.real.BlljmyGrayService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.CouponProduct;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.AvailableConstants;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.crm.sfa.utilities.constant.PriceBookProductConstants;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.constant.dmConstants.DmDefineConstants;
import com.facishare.crm.sfa.utilities.constant.dmConstants.RealPriceConstants;
import com.facishare.crm.sfa.utilities.proxy.model.GetPromotionProductsByAccountIdModel;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.crm.sfa.utilities.util.Price.RealPriceModel;
import com.facishare.crm.sfa.utilities.util.i18n.SalesOrderI18NKeyUtil;
import com.facishare.crm.util.CommonConfigCenter;
import com.facishare.crm.util.MtCurrentUtil;
import com.facishare.crm.util.UnitUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.MultiCurrencyLogicServiceImpl;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.CurrencyFieldDescribe;
import com.facishare.paas.metadata.impl.describe.FormulaFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.Where;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import joptsimple.internal.Strings;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID;
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID__R;
import static com.facishare.crm.sfa.utilities.util.ButtonUtils.removeButtons;

/**
 * Created by luxin on 2018/3/12.
 */
public class ProductRelatedListController extends StandardRelatedListController {
    private final ProductCoreService productCoreService = SpringUtil.getContext().getBean(ProductCoreService.class);
    private final BlljmyGrayService blljmyGrayService = SpringUtil.getContext().getBean(BlljmyGrayService.class);
    private final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitServiceImpl.class);
    private final AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);
    private final PromotionService promotionService = SpringUtil.getContext().getBean(PromotionService.class);
    private final PricePolicyCoreService pricePolicyCoreService = SpringUtil.getContext().getBean(PricePolicyCoreService.class);
    private final AttributeCoreService attributeCoreService = SpringUtil.getContext().getBean(AttributeCoreService.class);
    private final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private final UnitCoreService unitCoreService = SpringUtil.getContext().getBean(UnitCoreServiceImpl.class);
    private final AvailableRangeCoreService availableRangeCoreService = SpringUtil.getContext().getBean(AvailableRangeCoreService.class);
    private final ProductCategoryBizService productCategoryBizService = SpringUtil.getContext().getBean(ProductCategoryBizService.class);
    private final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private final MultiCurrencyLogicServiceImpl multiCurrencyLogicService = SpringUtil.getContext().getBean(MultiCurrencyLogicServiceImpl.class);
    private final NonstandardAttributeService nonstandardAttributeService = SpringUtil.getContext().getBean(NonstandardAttributeService.class);
    private final AttributeRangeService attributeRangeService = SpringUtil.getContext().getBean(AttributeRangeService.class);
    private final QuoterCommonController quoterCommonController = SpringUtil.getContext().getBean(QuoterCommonController.class);
    private final CouponProduct couponProduct = SpringUtil.getContext().getBean("couponProduct", CouponProduct.class);

    private static final int MAX_LIMIT = 100;
    private static final long THIRTY_DAYS_IN_MILLION_SECONDS = 30L * 24L * 60L * 60L * 1000L;
    private DomainPluginParam priceDomainPluginParam;
    private boolean isSkuEdit;
    protected String accountId = null;
    protected String partnerId = null;
    private boolean fromChannelMyProduct;
    List<GetPromotionProductsByAccountIdModel.WrapProductPromotion> productPromotionList = null;
    Map<String, List<String>> pricePolicyProductListMap = null;
    String masterObjectApiName = Strings.EMPTY;
    List<String> multiSupportObjects = Lists.newArrayList(Utils.SALES_ORDER_API_NAME, Utils.QUOTE_API_NAME, Utils.SALE_CONTRACT_API_NAME);
    List<String> getRealPriceSupportObjects = Lists.newArrayList(Utils.SALES_ORDER_API_NAME, Utils.QUOTE_API_NAME, Utils.NEW_OPPORTUNITY_API_NAME);
    List<String> realPricePluginObjects = Lists.newArrayList(Utils.SALES_ORDER_API_NAME, Utils.QUOTE_API_NAME, Utils.SALE_CONTRACT_API_NAME, Utils.NEW_OPPORTUNITY_API_NAME);
    protected static final List<String> RELATED_FIELD_API_NAME =
            Lists.newArrayList("salesorderproduct_product_list"
                    , "product_quote_lines_list"
                    , "product_new_opportunity_lines_list"
                    , "spu_sku_list");

    private boolean isCurrencyEnabled = false;
    private String mcCurrency = null;
    List<String> productIdList = null;
    private boolean priceDomainFlag = false;

    private boolean canUseQuoter = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        isSkuEdit = arg.getObjectData() != null && Objects.equals(arg.getObjectData().get("under_spu_skus_edit"), true);
        //是否灰度了报价器功能
        canUseQuoter = Boolean.TRUE.equals(GrayUtil.isGrayEnable(controllerContext.getTenantId(), GrayUtil.QUOTER_OPEN_TENANT_ID));
        //是否灰度了报价器功能
        if(canUseQuoter && quoterCommonController.isFromQuoter(arg.getExtraData())) {
            //处理从报价器来的标准属性、非标属性参数
            SearchTemplateQuery tempQuery = quoterCommonController.processAttrFilter(arg.getExtraData(), null, ProductConstants.NONSTANDARD_ATTRIBUTE_IDS, null);
            SearchTemplateQuery searchTemplateQuery = (SearchTemplateQuery)SearchTemplateQuery.fromJsonString(arg.getSearchQueryInfo());

            List<Wheres> wheres = Lists.newArrayList();
            if(CollectionUtils.isNotEmpty(tempQuery.getFilters())) {
                Wheres where2 = new Wheres();
                where2.setConnector(Where.CONN.OR.toString());
                where2.setFilters(tempQuery.getFilters());
                wheres.add(where2);
            }

            List<String> productIds = quoterCommonController.findProductsByAttr(controllerContext.getUser(), arg.getExtraData());
            if(CollectionUtils.isNotEmpty(productIds)) {
                List<IFilter> filters = Lists.newArrayList();
                IFilter productIdFilter = new Filter();
                productIdFilter.setFieldName(IObjectData.ID);
                productIdFilter.setOperator(Operator.IN);
                productIdFilter.setFieldValues(productIds);
                filters.add(productIdFilter);

                Wheres where = new Wheres();
                where.setConnector(Where.CONN.OR.toString());
                where.setFilters(filters);
                wheres.add(where);
            }
            searchTemplateQuery.setWheres(wheres);
            arg.setSearchQueryInfo(JsonUtil.toJson(searchTemplateQuery));
        }

    }

    private void priceDomainEnable() {
        if (StringUtils.isBlank(masterObjectApiName)) {
            return;
        }
        priceDomainPluginParam = infraServiceFacade.findPluginParam(controllerContext.getTenantId(), masterObjectApiName, DmDefineConstants.REAL_PRICE);
        priceDomainFlag = priceDomainPluginParam != null;
    }

    @Override
    protected void init() {
        if (arg.getMasterData() != null && arg.getMasterData().get("object_describe_api_name") != null) {
            masterObjectApiName = arg.getMasterData().get("object_describe_api_name").toString();
        }
        //没传币种，默认用本位币
        isCurrencyEnabled = bizConfigThreadLocalCacheService.isCurrencyEnabled(controllerContext.getTenantId());

        if (DhtUtil.isDhtOrAccessoriesMallRequest(controllerContext.getPeerName(), controllerContext.getAppId())) {
            if (null == arg.getObjectData()) {
                arg.setObjectData(ObjectDataDocument.of(new HashMap<>()));
            }
            //营销通+订货通一体化，游客角色过来的请求，已经带了account_id，也没有outTenantId、outUserId，不用查account_id
            if (arg.getObjectData().get("account_id") == null) {
                accountId = AccountUtil.getAccountIdByOutTenantId(controllerContext.getTenantId(), controllerContext.getUser().getOutTenantId(), controllerContext.getUser().getOutUserId());
                arg.getObjectData().put("account_id", accountId);
            }
        }
        if ((GrayUtil.isSpuRelatedListOptimize(controllerContext.getTenantId()) && !realPricePluginObjects.contains(masterObjectApiName)) || !GrayUtil.isSpuRelatedListOptimize(controllerContext.getTenantId())) {
            priceDomainEnable();
        }
        if (priceDomainFlag) {
            Map<String, String> fieldMapping = priceDomainPluginParam.getFieldMapping();
            if (Objects.nonNull(fieldMapping)) {
                if (StringUtils.isBlank(accountId)) {
                    arg.getObjectData().put("account_id", arg.getObjectData().get(fieldMapping.getOrDefault(RealPriceConstants.MasterField.ACCOUNT_ID, "")));
                }
                arg.getObjectData().put("partner_id", arg.getObjectData().get(fieldMapping.getOrDefault(RealPriceConstants.MasterField.PARTNER_ID, "")));
            }
        }

        if (arg.getObjectData() != null) {
            if (arg.getObjectData().containsKey("account_id")) {
                accountId = Optional.ofNullable(arg.getObjectData().get("account_id")).orElse("").toString();
            }
            if (arg.getObjectData().containsKey("partner_id")) {
                partnerId = Optional.ofNullable(arg.getObjectData().get("partner_id")).orElse("").toString();
            }
            if (arg.getObjectData().containsKey("from_channel_my_product")) {
                fromChannelMyProduct = (boolean) arg.getObjectData().get("from_channel_my_product");
            }
        }
        if (isCurrencyEnabled) {
            if (null == arg.getMasterData()) {
                if (null != arg.getObjectData() && arg.getObjectData().get("mc_currency") != null) {
                    mcCurrency = (String) arg.getObjectData().get("mc_currency");
                }
            } else {
                if (arg.getMasterData().get("mc_currency") != null) {
                    mcCurrency = (String) arg.getMasterData().get("mc_currency");
                }
            }
            if (Strings.isNullOrEmpty(mcCurrency)) {
                mcCurrency = MtCurrentUtil.findFunctionalCurrency(controllerContext.getUser());
            }
        }
        super.init();
    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        SearchTemplateQuery query = super.customSearchTemplate(searchQuery);
        resetPermission4Dht(query);
        if (CollectionUtils.isNotEmpty(arg.getFilterProductIds())) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.NIN, DBRecord.ID, arg.getFilterProductIds());
        }
        MultiKeyWordSearchUtil.handleSearchQuery(controllerContext.getUser(), query);
        IObjectData sourceData = Objects.isNull(arg.getMasterData()) ? null : arg.getMasterData().toObjectData();
        availableRangeUtils.buildRangeSearchQueryForProduct(controllerContext.getUser(), query,
                arg.getRelatedListName(), accountId, partnerId, "", DBRecord.ID, stopWatch, sourceData, availableRangeUtils.domainQueryAvailableRangeEnable(arg,priceDomainFlag,priceDomainPluginParam,controllerContext.getTenantId(),RealPriceConstants.DetailField.PRODUCT_ID));
        availableRangeUtils.buildRangeForMerchantRange(controllerContext.getUser(), partnerId, query, sourceData, fromChannelMyProduct);
        // 如果是 选择商品下产品 用于编辑的话,不用数据权限
        if (isSkuEdit) {
            getSearchTemplateQuery4SpuEdit(query);
        } else {
            getSearchTemplateQuery4Normal(query);
        }

        if (SFAConfigUtil.isCPQ(controllerContext.getTenantId())
                && RequestUtil.isMobileOrH5Request()
                && !GrayUtil.isGrayMobileProductPackage(controllerContext.getTenantId())) {
            if (Objects.equals(arg.getRelatedListName(), "salesorderproduct_product_list")
                    || Objects.equals(arg.getRelatedListName(), "product_quote_lines_list")) {
                IFilter filter = new Filter();
                filter.setFieldName("is_package");
                filter.setOperator(Operator.EQ);
                filter.setFieldValues(Lists.newArrayList("false"));
                query.setFilters(Lists.newArrayList(filter));
            }
        }

        //叠加促销信息
        if (GrayUtil.isGrayOrderPromotion(controllerContext.getTenantId())) {
            if (PromotionUtil.getIsPromotionEnable(controllerContext.getUser(), controllerContext.getClientInfo())) {
                if (arg.getObjectData() != null && arg.getObjectData().get("account_id") != null) {
                    productPromotionList = promotionService.getProductPromotionList(controllerContext.getTenantId(), controllerContext.getUser(), accountId);
                    if (Objects.equals(arg.getObjectData().get("is_promotion_list"), true)) {
                        promotionService.handPromotionQuery(productPromotionList, query, DBRecord.ID);
                    }
                }
            }
        }
        //叠加价格政策信息
        if (bizConfigThreadLocalCacheService.isOpenPricePolicy(controllerContext.getTenantId(),masterObjectApiName)) {
            if (arg.getMasterData() != null && !Objects.isNull(arg.getMasterData().get("object_describe_api_name"))) {
                pricePolicyProductListMap = pricePolicyCoreService.GetPricePolicyProductsByAccountId(controllerContext.getUser(), masterObjectApiName, accountId);
                if (arg.getObjectData().get("tab_price_policy_id") != null) {
                    String pricePolicyId = arg.getObjectData().get("tab_price_policy_id").toString();
                    List<String> pricePolicyProductIds = new ArrayList<>();
                    if (!Objects.isNull(pricePolicyProductListMap)) {
                        pricePolicyProductIds = pricePolicyProductListMap.get(pricePolicyId);
                    }
                    if (CollectionUtils.isNotEmpty(pricePolicyProductIds)) {
                        if (!pricePolicyProductIds.contains(PricePolicyConstants.ALL_PRODUCT)) {
                            com.facishare.crm.sfa.utilities.common.convert
                                    .SearchUtil.fillFilterIn(query.getFilters(), DBRecord.ID, pricePolicyProductIds);
                        }
                    } else {
                        com.facishare.crm.sfa.utilities.common.convert
                                .SearchUtil.fillFilterIn(query.getFilters(), DBRecord.ID, Lists.newArrayList("null"));
                    }
                }
            }
        }

        String relatedListName = arg.getRelatedListName();
        if (Objects.equals(relatedListName, "bom_product_constraint_list")) {
            handConstraintFilter(query,Utils.PRODUCT_CONSTRAINT_API_NAME);
        }
        if (Objects.equals(relatedListName, "bom_attribute_constraint_list")) {
            handConstraintFilter(query,SFAPreDefineObject.BomAttributeConstraint.getApiName());
        }
        handlePriceBookProductFilter(query);
        //促销产品页签处理过滤条件
        handlePromotionTabFilter(query);
        //最近订购页签处理过滤条件
        handleRecentlyOrderedTabFilter(query);

        // 根据优惠券查询适用产品过滤
        if (arg.getObjectData() != null && arg.getObjectData().get("couponInstanceId") != null && bizConfigThreadLocalCacheService.isOpenCoupon(controllerContext.getTenantId())){
            couponProduct.getCouponProductList(controllerContext.getTenantId(), controllerContext.getUser(), arg.getObjectData().get("couponInstanceId").toString(), query);
        }
        if (DhtUtil.isDhtOrAccessoriesMallRequest(controllerContext.getPeerName(), controllerContext.getAppId()) && query.getLimit() > MAX_QUERY_LIMIT) {
            query.setLimit(20);
        }
        return query;
    }

    private void handlePriceBookProductFilter(SearchTemplateQuery query) {
        if (arg.getObjectData() == null) {
            return;
        }
        if (bizConfigThreadLocalCacheService.isOpenWhetherFilterPriceBookSelectProduct(controllerContext.getTenantId())) {
            return;
        }
        IObjectData objectData = arg.getObjectData().toObjectData();
        if (Utils.PRICE_BOOK_API_NAME.equals(objectData.get("source_object_api_name", String.class)) && StringUtils.isNotEmpty(objectData.get("price_book_id", String.class))) {
            SearchTemplateQuery searchQuery = new SearchTemplateQuery();
            searchQuery.setLimit(0);
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, PriceBookProductConstants.PRICEBOOK_ID, objectData.get("price_book_id", String.class));
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, DBRecord.IS_DELETED, Lists.newArrayList("0","1"));
            SearchTemplateQueryExt.of(query).addFilter(Operator.NIN, DBRecord.ID, Lists.newArrayList(JSON.toJSONString(searchQuery), SFAPreDefineObject.PriceBookProduct.getApiName(), AvailableConstants.ProductResultField.PRODUCT_ID), 10);
        }
    }

    private void handConstraintFilter(SearchTemplateQuery query, String objectApiName) {
        SearchTemplateQuery searchTemplateQuery = SoCommonUtils.buildSearchTemplateQuery(0, 5000);
        SearchUtil.fillFilterIn(searchTemplateQuery.getFilters(), DBRecord.IS_DELETED, Lists.newArrayList("0","1"));
        SearchUtil.fillFilterIsNotNull(searchTemplateQuery.getFilters(), "bom_id");
        SearchUtil.fillFilterIsNotNull(searchTemplateQuery.getFilters(), "product_id");
        List<String> fieldValues = Lists.newArrayList(JSON.toJSONString(searchTemplateQuery), objectApiName , "product_id");
        SearchUtil.fillFilterBySubQuery(query.getFilters(), DBRecord.ID, Operator.NIN, fieldValues);
    }

    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        QueryResult<IObjectData> queryResult;
        if (isSkuEdit) {
            queryResult = serviceFacade.findBySearchQueryWithDeleted(controllerContext.getUser(), objectDescribe, query);
        } else {
            queryResult = super.findData(query);

            if (blljmyGrayService.checkNeedSearchAgainAndHandleFieldValues(controllerContext.getTenantId(), query, queryResult)) {
                queryResult = super.findData(query);
            }
        }

        // 价目表明细选择产品,需要给数据更换成定价单位的数据
        if (multiSupportObjects.contains(masterObjectApiName) ||
                (arg.getObjectData() != null && Utils.PRICE_BOOK_PRODUCT_API_NAME.equals(arg.getObjectData().get("object_describe_api_name")))) {
            multiUnitService.handleUnitAndPrice(queryResult.getData(), controllerContext.getTenantId());
        }
        if (multiUnitService.isOpenMultiUnit(controllerContext.getTenantId())) {
            unitCoreService.handCommonUnitInfo(controllerContext.getTenantId(), queryResult.getData(), Utils.PRODUCT_API_NAME, masterObjectApiName);
        }

        attributeCoreService.attachAttributeData(controllerContext.getUser()
                , queryResult.getData()
                , IObjectData.ID);
        nonstandardAttributeService.attachNonstandardAttributeData(controllerContext.getUser()
                , queryResult.getData(), false);
        //开启价目表强制优先级产品列表叠加
        if (GrayUtil.isGetRealPriceProductList(controllerContext.getTenantId())) {
            //插件不用处理，灰度全网后，下面的代码就没用了
            if (getRealPriceSupportObjects.contains(masterObjectApiName)) {
                if (RELATED_FIELD_API_NAME.contains(arg.getRelatedListName())) {
                    if (bizConfigThreadLocalCacheService.isEnforcePriority(controllerContext.getTenantId())) {
                        fillRealPrice(queryResult.getData());
                    }
                }
            }
        }

        return queryResult;
    }

    @Override
    protected ILayout findLayout() {
        return removeButtons(super.findLayout());
    }

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        QueryResult<IObjectData> result = super.getQueryResult(query);
        //叠加价格政策信息
        if (bizConfigThreadLocalCacheService.isOpenPricePolicy(controllerContext.getTenantId(),masterObjectApiName)) {
            pricePolicyCoreService.HasPricePolicyForProduct(controllerContext.getUser(), result.getData(), pricePolicyProductListMap);
        }
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result realResult = super.after(arg, result);
        nonstandardAttributeService.fillNonstandardAttributeDefaultValue(controllerContext.getTenantId(), realResult.getDataList(), false);
         //将非标属性的值重新赋值到产品行上、重置默认选中的属性值
        if (canUseQuoter && quoterCommonController.isFromQuoter(arg.getExtraData()) && CollectionUtils.isNotEmpty(result.getDataList())) {
            quoterCommonController.resetDefaultAttributeValue(result.getDataList(), arg.getExtraData());
        }

        if (arg.getObjectData() != null && Objects.equals(arg.getObjectData().get("is_spec"), true)) {
            // 有规格的数据<sku_id,data>
            Map<String, ObjectDataDocument> haveSpecObjectIdDataMapping = realResult.getDataList()
                    .stream().collect(Collectors.toMap(ObjectDataDocument::getId, o -> o));
            productCoreService.fillSpecAndValue4Skus(controllerContext.getTenantId(), haveSpecObjectIdDataMapping);
        }
        // 是否检测返回的产品数据的只读权限
        if (isSkuEdit) {
            fillDataRightInfo(result);
        }
        // 叠加促销信息
        if (GrayUtil.isGrayOrderPromotion(controllerContext.getTenantId())) {
            if (productPromotionList != null && !productPromotionList.isEmpty()) {
                promotionService.fillPromotionInfo(controllerContext.getUser(), result.getDataList(), productPromotionList);
            }
        }
        //补充虚拟字段
        fillVirtualField();
        fillUnitLabel();

        //属性范围
        attributeRangeService.queryAttrRange(controllerContext.getUser(),ObjectDataDocument.ofDataList(realResult.getDataList()),Utils.PRODUCT_API_NAME);

        productCategoryBizService.handleCategoryName(controllerContext.getUser(), realResult, PRODUCT_CATEGORY_ID, PRODUCT_CATEGORY_ID__R);
        //最近订购重新排序
        reorder(realResult);
        return realResult;
    }


    @Override
    protected void modifyQueryByRefFieldName(SearchTemplateQuery query) {
        if (!(Utils.OPPORTUNITY_API_NAME.equals(arg.getTargetObjectApiName()) && "opportunity_product_list".equals(arg.getRelatedListName())
                || Utils.VISITING_API_NAME.equals(arg.getTargetObjectApiName()) && "target_related_list_product".equals(arg.getRelatedListName()))) {
            super.modifyQueryByRefFieldName(query);
        }
    }

    private void resetPermission4Dht(SearchTemplateQuery query) {
        if (DhtUtil.isDhtOrAccessoriesMallRequest(controllerContext.getPeerName(), controllerContext.getAppId())) {
            query.setPermissionType(0);
            query.setDataRightsParameter(null);
        }
    }
    private void getSearchTemplateQuery4Normal(SearchTemplateQuery query) {
        // 处理query里面的filters
        productCategoryBizService.handleNewCategoryListFilters(controllerContext.getUser(), getSearchQueryInfo(), query);
        productCategoryBizService.oldCategoryTransferNewCategoryListFilters(controllerContext.getUser(), query, PRODUCT_CATEGORY_ID);
    }

    private void getSearchTemplateQuery4SpuEdit(SearchTemplateQuery query) {
        // 添加过滤条件要正常的和作废的数据
        SearchUtil.fillFilterIn(query.getFilters(), "is_deleted", Lists.newArrayList("0", "1"));
        query.setDataRightsParameter(null);
        query.setPermissionType(0);

    }

    /**
     * 添加数据权限的信息
     */
    private void fillDataRightInfo(Result result) {
        List<String> productIds = Lists.newArrayList();
        Map<String, ObjectDataDocument> productIdAndDataMapping = Maps.newHashMap();

        result.getDataList().forEach(o -> {
            String productId = o.getId();
            productIds.add(productId);
            productIdAndDataMapping.put(productId, o);
        });

        Map<String, Permissions> productIdDataRightMap = serviceFacade.checkDataPrivilege(controllerContext.getUser(), productIds, objectDescribe);

        productIdDataRightMap.forEach((k, v) -> {
            ObjectDataDocument productData = productIdAndDataMapping.get(k);

            // 未作废的数据才做标记
            if (!(Boolean) productData.get("is_deleted")) {
                if (Permissions.READ_WRITE.equals(v)) {
                    productData.put("data_right_flag", "write");
                } else if (Permissions.READ_ONLY.equals(v)) {
                    productData.put("data_right_flag", "readonly");
                } else if (Permissions.NO_PERMISSION.equals(v)) {
                    productData.put("data_right_flag", "invisible");
                }
            }
        });
    }

    //全网虚拟字段后，该方法删除
    @Deprecated
    private void fillRealPrice(List<IObjectData> productList) {
        if (CollectionUtils.isNotEmpty(productList) && !Strings.isNullOrEmpty(accountId)) {
            List<String> productIds = productList.stream().map(DBRecord::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(productIds)) {
                RealPriceModel.Arg arg = new RealPriceModel.Arg();
                arg.setAccountId(accountId);
                List<RealPriceModel.FullProduct> fullProductList = productIds.stream()
                        .map(productId -> RealPriceModel.FullProduct.builder().productId(productId).build())
                        .collect(Collectors.toList());
                arg.setFullProductList(fullProductList);
                arg.setObjectApiName(masterObjectApiName);

                Map<String, ObjectDataDocument> availablePrice = availableRangeCoreService.getRealPrice(controllerContext.getUser(), arg).getRst();
                productList.forEach(productData -> {
                    if (!availablePrice.containsKey(productData.getId())) {
                        return;
                    }
                    IObjectData priceBookData = availablePrice.get(productData.getId()).toObjectData();
                    Map<String, Object> priceBookMap = Maps.newHashMap();

                    priceBookMap.put(PriceBookConstants.ProductField.PRICEBOOKID.getApiName(),
                            priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName()));
                    priceBookMap.put(PriceBookConstants.ProductField.PRICEBOOKID__R.getApiName(),
                            priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKID__R.getApiName()));
                    priceBookMap.put(PriceBookConstants.ProductField.PRICEBOOKPRICE.getApiName(),
                            priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKPRICE.getApiName()));
                    priceBookMap.put(PriceBookConstants.ProductField.DISCOUNT.getApiName(),
                            priceBookData.get(PriceBookConstants.ProductField.DISCOUNT.getApiName()));
                    priceBookMap.put(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName(),
                            priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName()));
                    fillExtendInfo(productData, "realPriceInfo", priceBookMap);
                });
            }
        }
    }

    private void fillExtendInfo(IObjectData productData, String key, Object value) {
        Map<String, Object> extendInfo = productData.get(PriceBookConstants.EXTEND_INFO, Map.class);
        if (Objects.isNull(extendInfo)) {
            extendInfo = new HashMap<>();
        }
        extendInfo.put(key, value);
        productData.set(PriceBookConstants.EXTEND_INFO, extendInfo);
    }

    private Map<String, ObjectDataDocument> getAvailablePrice(List<String> productIds, Map<String, String> productId2unitIdMap, List<IObjectData> productDataList) {
        boolean isDhtRequest = DhtUtil.isDhtOrAccessoriesMallRequest(controllerContext.getPeerName(), controllerContext.getAppId());
        boolean isEnforcePriority = bizConfigThreadLocalCacheService.isEnforcePriority(controllerContext.getTenantId());
        Map<String, List<ObjectDataDocument>> details = availableRangeUtils.filterPresetObjectData(arg.getDetails(), arg.getObjectData());
        List<String> priceBookProductIds = Lists.newArrayList();
        if (!isDhtRequest && !isEnforcePriority) {
            List wheres = PriceBookProductUtil.getPriceBookProductsWheres(controllerContext.getUser());
            log.info("getAvailablePrice wheres[{}]", wheres);
            if (!CollectionUtils.isEmpty(wheres)) {
                IObjectData masterData = arg.getMasterData() == null ? null : arg.getMasterData().toObjectData();
                List<IObjectData> availablePriceBookDataList = this.availableRangeUtils.getAvailablePriceBookList(this.controllerContext.getUser(), this.accountId, this.partnerId,
                        masterData, details, PriceBookConstants.PriceBookRangeType.DETAIL.getRangeType());
                List<String> priceBookIds = availablePriceBookDataList.stream().map(DBRecord::getId).collect(Collectors.toList());

                List<IObjectData> priceBookProducts = PriceBookProductUtil.getPriceBookProductsFilterByWheres(controllerContext.getUser(), wheres, productIds, priceBookIds);
                if (CollectionUtils.isEmpty(priceBookProducts)) {
                    return new HashMap<>();
                }
                priceBookProductIds = priceBookProducts.stream().map(IObjectData::getId).distinct().collect(Collectors.toList());
            }
        }

        List<ObjectDataDocument> availablePriceList = availableRangeCoreService.getAvailablePrice(controllerContext.getUser(), accountId, partnerId,
                productIds, productId2unitIdMap, masterObjectApiName, this.arg.getMasterData(), null,
                priceBookProductIds, details, true, false, true, productDataList);

        return availablePriceList.stream().collect(Collectors.toMap(d -> d.toObjectData().get("product_id", String.class), d -> d));
    }

    /**
     * 补充虚拟字段的值
     */
    private void fillVirtualField() {
        if (Strings.isNullOrEmpty(accountId) || CollectionUtils.isEmpty(result.getDataList())) {
            return;
        }

        //虚拟字段开关
        boolean isVirtualExtensionEnabled = bizConfigThreadLocalCacheService.isVirtualExtensionEnabled(controllerContext.getTenantId());
        log.info("isVirtualExtensionEnabled[{}]", isVirtualExtensionEnabled);
        if (!isVirtualExtensionEnabled) {
            return;
        }

        Map<String, String> toMcCurrencyExchangeRateMap = null;
        Map<String, String> toFunctionalCurrencyExchangeRateMap = null;
        String functionalCurrency = null;
        Map<String, String> currencySymbolMap = new HashMap<>();
        if (isCurrencyEnabled) {
            functionalCurrency = MtCurrentUtil.findFunctionalCurrency(controllerContext.getUser());

            toMcCurrencyExchangeRateMap = MtCurrentUtil.getExchangeRateMap(controllerContext.getUser(), mcCurrency);
            toFunctionalCurrencyExchangeRateMap = MtCurrentUtil.getExchangeRateMap(controllerContext.getUser(), functionalCurrency);

            //币种符号
            List<String> currencyCodes = Lists.newArrayList(Sets.newHashSet(mcCurrency, functionalCurrency));
            currencySymbolMap = multiCurrencyLogicService.findCurrencySymbolByCurrencyCodes(currencyCodes);

            fillMcCurrencyField(functionalCurrency, currencySymbolMap);
        }
        fillVirtualProductField(functionalCurrency, toMcCurrencyExchangeRateMap);
        fillVirtualPriceBookField(functionalCurrency, currencySymbolMap, toMcCurrencyExchangeRateMap, toFunctionalCurrencyExchangeRateMap);

        //像新建商机2.0，不需要返回库存信息
        boolean needFillStock = false;
        if (Objects.equals(arg.getRelatedListName(), "salesorderproduct_product_list")) {
            needFillStock = true;
        } else if (Objects.equals(arg.getRelatedListName(), "spu_sku_list")) {
            if (Objects.equals(masterObjectApiName, "SalesOrderObj")) {
                needFillStock = true;
            }
        }
        if (needFillStock) {
            String shippingWarehouseId = arg.getObjectData() == null ? null : (String) arg.getObjectData().get("shipping_warehouse_id");
            boolean isDhtMultiLevelOrder = bizConfigThreadLocalCacheService.isDhtMultiLevelOrder(controllerContext.getTenantId());
            //开启渠道多级订货且record_type=distribution__c，取库存才按partnerId查
            String recordType = "";
            if (arg.getMasterData() != null) {
                recordType = Optional.ofNullable(arg.getMasterData().get(SystemConstants.Field.RecordType.apiName)).orElse("").toString();
            }
            StockUtil.fillVirtualStockField(controllerContext.getUser(), result.getDataList(), shippingWarehouseId, accountId, partnerId, recordType);
        }
    }

    /**
     * 多币种 符号
     */
    private void fillMcCurrencyField(String functionalCurrency, Map<String, String> currencySymbolMap) {
        if (!isCurrencyEnabled) {
            return;
        }

        for (ObjectDataDocument data : result.getDataList()) {
            String mcCurrencyR = currencySymbolMap.get(mcCurrency);
            String mcFunctionalCurrencyR = currencySymbolMap.get(functionalCurrency);

            data.put(ProductConstants.MC_CURRENCY__R, mcCurrencyR);
            data.put(ProductConstants.MC_FUNCTIONAL_CURRENCY__R, mcFunctionalCurrencyR);
        }
    }

    private void fillVirtualProductField(String functionalCurrency, Map<String, String> toMcCurrencyExchangeRateMap) {
        //价格精度
        Integer priceDecimalPlaces = 2;
        BigDecimal toMcCurrencyExchangeRate = null;
        if (isCurrencyEnabled && !Objects.equals(functionalCurrency, mcCurrency)) {
            CurrencyFieldDescribe priceField = (CurrencyFieldDescribe) objectDescribe.getFieldDescribe(ProductConstants.PRODUCT_PRICE);
            priceDecimalPlaces = priceField.getDecimalPlaces();

            toMcCurrencyExchangeRate = MtCurrentUtil.getExchangeRate(toMcCurrencyExchangeRateMap, functionalCurrency, mcCurrency);
        }

        for (ObjectDataDocument data : result.getDataList()) {
            if (!data.containsKey("price") || data.get("price") == null) {
                continue;
            }
            BigDecimal price = new BigDecimal(data.get("price").toString());

            if (isCurrencyEnabled && !Objects.equals(functionalCurrency, mcCurrency)) {
                price = price.multiply(toMcCurrencyExchangeRate).setScale(priceDecimalPlaces, BigDecimal.ROUND_HALF_EVEN);
            }
            data.put(ProductConstants.VIRTUAL_PRODUCT_PRICE, price);
        }
    }

    /**
     * 补充虚拟字段上价目表相关的字段
     */
    private void fillVirtualPriceBookField(String functionalCurrency, Map<String, String> currencySymbolMap,
                                           Map<String, String> toMcCurrencyExchangeRateMap, Map<String, String> toFunctionalCurrencyExchangeRateMap) {
        boolean isPriceBookEnabled = bizConfigThreadLocalCacheService.isPriceBookEnabled(controllerContext.getTenantId());
        if (!isPriceBookEnabled) {
            return;
        }

        int size = result.getDataList().size();
        List<String> productIds = Lists.newArrayListWithExpectedSize(size);
        Map<String, String> productId2unitIdMap = Maps.newHashMapWithExpectedSize(size);
        List<IObjectData> productList = Lists.newArrayListWithExpectedSize(size);
        for (ObjectDataDocument objectDataDocument : result.getDataList()) {
            productIds.add(objectDataDocument.getId());
            if (!Objects.isNull(objectDataDocument.get("unit"))) {
                productId2unitIdMap.put(objectDataDocument.getId(), objectDataDocument.get("unit").toString());
            }
            productList.add(objectDataDocument.toObjectData());
        }
        Map<String, ObjectDataDocument> availablePrice = getAvailablePrice(productIds, productId2unitIdMap, productList);

        Map<String, String> priceBookMcCurrencyMap = null;
        int priceBookPriceDecimalPlaces = 0;
        int sellingPriceDecimalPlaces = 0;
        if (isCurrencyEnabled) {
            //价目表上的币种
            List<String> priceBookIds = availablePrice.values().stream().map(d -> (String) d.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName())).distinct().collect(Collectors.toList());
            priceBookMcCurrencyMap = PriceBookUtil.getPriceBookMcCurrencyMap(controllerContext.getTenantId(), priceBookIds);

            //价目表价格……精度
            IObjectDescribe detailDescribe = serviceFacade.findObject(controllerContext.getTenantId(), Utils.PRICE_BOOK_PRODUCT_API_NAME);
            CurrencyFieldDescribe sellingPriceField = (CurrencyFieldDescribe) detailDescribe.getFieldDescribe(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName());
            FormulaFieldDescribe priceBookPriceField = (FormulaFieldDescribe) detailDescribe.getFieldDescribe(PriceBookConstants.ProductField.PRICEBOOKPRICE.getApiName());
            sellingPriceDecimalPlaces = sellingPriceField.getDecimalPlaces();
            priceBookPriceDecimalPlaces = priceBookPriceField.getDecimalPlaces();
        }

        boolean isDhtRequest = DhtUtil.isDhtOrAccessoriesMallRequest(controllerContext.getPeerName(), controllerContext.getAppId());
        boolean isYiPinWenHua = CommonConfigCenter.isYiPinWenHua(controllerContext.getTenantId());

        for (ObjectDataDocument data : result.getDataList()) {
            String productId = data.getId();
            if (!availablePrice.containsKey(productId)) {
                log.warn("availablePrice not contain productId[{}]", productId);
                continue;
            }
            IObjectData priceBookData = availablePrice.get(productId).toObjectData();

            String priceBookId = (String) priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName());
            data.put(ProductConstants.PRICE_BOOK_ID, priceBookId);
            data.put(ProductConstants.PRICE_BOOK_PRODUCT_ID, priceBookData.get(DBRecord.ID));
            data.put(ProductConstants.PRICE_BOOK_DISCOUNT, priceBookData.get(PriceBookConstants.ProductField.DISCOUNT.getApiName()));

            //一品文化显示【最小起订量】
            if (isYiPinWenHua) {
                String minOrderQuantityFieldApiName = CommonConfigCenter.getMinOrderQuantityFieldApiName(controllerContext.getTenantId());
                if (minOrderQuantityFieldApiName != null) {
                    data.put(ProductConstants.VIRTUAL_MIN_ORDER_QUANTITY, priceBookData.get(minOrderQuantityFieldApiName));
                }
            }

            /**
             * 订货通:价目表售价、价目表价格都没值才报错，其中一个为null，把另外一个值赋值过去
             * 其他：只是相关的虚拟字段不赋值
             * */
            if (isDhtRequest) {
                data.put(ProductConstants.PRICE_BOOK_ID__R, priceBookData.get("pricebook_id__r"));
                data.put(ProductConstants.PRICE_BOOK_PRODUCT_ID__R, priceBookData.getName());
                if (priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName()) == null &&
                        priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKPRICE.getApiName()) == null) {
                    String name = priceBookData.get("name", String.class);
                    String msg = String.format(I18N.text(SalesOrderI18NKeyUtil.SFA_PRICEBOOKPRICE_SELLINGPRICE_AND_PRICEBOOKPRICE_IS_NULL), name);
                    throw new ValidateException(msg);
                }

                if (priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName()) == null) {
                    priceBookData.set(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName(), priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKPRICE.getApiName()));
                }

                if (priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKPRICE.getApiName()) == null) {
                    priceBookData.set(PriceBookConstants.ProductField.PRICEBOOKPRICE.getApiName(), priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName()));
                }
            }

            if (!isCurrencyEnabled) {
                data.put(ProductConstants.VIRTUAL_PRICE_BOOK_SELLING_PRICE, priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName()));
                data.put(ProductConstants.VIRTUAL_PRICE_BOOK_PRICE, priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKPRICE.getApiName()));
                continue;
            }

            //开了多币种，需要换成用户选择的币种
            String priceBookCurrentCode = priceBookMcCurrencyMap.get(priceBookId);
            BigDecimal toMcCurrencyExchangeRate = MtCurrentUtil.getExchangeRate(toMcCurrencyExchangeRateMap, priceBookCurrentCode, mcCurrency);
            BigDecimal toFunctionalCurrencyExchangeRate = MtCurrentUtil.getExchangeRate(toFunctionalCurrencyExchangeRateMap, priceBookCurrentCode, functionalCurrency);

            String mcCurrencyR = currencySymbolMap.get(mcCurrency);
            String mcFunctionalCurrencyR = currencySymbolMap.get(functionalCurrency);

            if (priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName()) != null) {
                BigDecimal sellingPrice = new BigDecimal(priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName(), String.class));
                if (!Objects.equals(priceBookCurrentCode, mcCurrency)) {
                    sellingPrice = sellingPrice.multiply(toMcCurrencyExchangeRate).setScale(sellingPriceDecimalPlaces, BigDecimal.ROUND_HALF_EVEN);
                }

                data.put(ProductConstants.VIRTUAL_PRICE_BOOK_SELLING_PRICE, sellingPrice);
                data.put(ProductConstants.VIRTUAL_PRICE_BOOK_SELLING_PRICE__N, mcCurrencyR + sellingPrice);
            }

            if (priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKPRICE.getApiName()) != null) {
                BigDecimal priceBookPrice = new BigDecimal(priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKPRICE.getApiName(), String.class));
                if (!Objects.equals(priceBookCurrentCode, mcCurrency)) {
                    priceBookPrice = priceBookPrice.multiply(toMcCurrencyExchangeRate).setScale(priceBookPriceDecimalPlaces, BigDecimal.ROUND_HALF_EVEN);
                }

                BigDecimal originPriceBookPrice = new BigDecimal(priceBookData.get(PriceBookConstants.ProductField.PRICEBOOKPRICE.getApiName(), String.class));
                BigDecimal basePriceBookPrice = Objects.equals(priceBookCurrentCode, functionalCurrency) ?
                        originPriceBookPrice : originPriceBookPrice.multiply(toFunctionalCurrencyExchangeRate).setScale(priceBookPriceDecimalPlaces, BigDecimal.ROUND_HALF_EVEN);

                data.put(ProductConstants.VIRTUAL_PRICE_BOOK_PRICE, priceBookPrice);
                data.put(ProductConstants.VIRTUAL_BASE_PRICE_BOOK_PRICE, basePriceBookPrice);
                data.put(ProductConstants.VIRTUAL_PRICE_BOOK_PRICE__N, mcCurrencyR + priceBookPrice);
                data.put(ProductConstants.VIRTUAL_BASE_PRICE_BOOK_PRICE__N, mcFunctionalCurrencyR + basePriceBookPrice);
            }
        }
    }

    private void fillUnitLabel() {
        if (CollectionUtils.isEmpty(result.getDataList())) {
            return;
        }

        if (!DhtUtil.isDhtOrAccessoriesMallRequest(controllerContext.getPeerName(), controllerContext.getAppId())) {
            return;
        }

        Map<String, String> unit2Label = UnitUtil.getUnitOption(controllerContext.getUser(), objectDescribe, Utils.PRODUCT_API_NAME, ProductConstants.UNIT);

        for (ObjectDataDocument data : result.getDataList()) {
            String unit = (String) data.get("unit");
            data.put("unit__r", unit2Label.get(unit));
        }
    }

    //最近订购
    private void handleRecentlyOrderedTabFilter(SearchTemplateQuery templateQuery) {
        if (null == arg.getObjectData() || !Objects.equals(true, arg.getObjectData().get(ProductConstants.TAB_RECENTLY_ORDERED))) {
            return;
        }
        if (Strings.isNullOrEmpty(accountId)) {
            SearchTemplateQueryExt.of(templateQuery).addFilter(Operator.EQ, DBRecord.ID, "-99");
            return;
        }
        //最近订购强制limit 100
        templateQuery.setLimit(MAX_LIMIT);
        long createTime = System.currentTimeMillis() - THIRTY_DAYS_IN_MILLION_SECONDS;
        String sql = ConcatenateSqlUtils.getRecentlyOrderedProduct(controllerContext.getTenantId(), accountId, createTime);
        List<Map> rst = null;
        try {
            rst = objectDataService.findBySql(controllerContext.getTenantId(), sql);
        } catch (MetadataServiceException e) {
            log.error("handleRecentlyOrderedTab error. sql:{} ", sql, e);
        }
        if (CollectionUtils.isEmpty(rst)) {
            SearchTemplateQueryExt.of(templateQuery).addFilter(Operator.EQ, DBRecord.ID, "-99");
            return;
        }
        productIdList = Lists.newArrayListWithExpectedSize(rst.size());
        for (Map map : rst) {
            productIdList.add(map.getOrDefault("product_id", "").toString());
        }
        SearchTemplateQueryExt.of(templateQuery).addFilter(Operator.IN, DBRecord.ID, productIdList);
    }

    private void reorder(Result realResult) {
        if (null == realResult || CollectionUtils.isEmpty(realResult.getDataList()) || CollectionUtils.isEmpty(productIdList)) {
            return;
        }
        realResult.getDataList().sort((x, y) ->
                productIdList.indexOf(x.getId()) == productIdList.indexOf(y.getId()) ? 0
                        : productIdList.indexOf(x.getId()) > productIdList.indexOf(y.getId()) ? 1 : -1);
    }

    //促销产品页签过滤促销产品
    private void handlePromotionTabFilter(SearchTemplateQuery templateQuery) {
        if (null == arg.getObjectData() || !Objects.equals(true, arg.getObjectData().get(ProductConstants.TAB_PROMOTION))) {
            return;
        }
        if (Strings.isNullOrEmpty(accountId) || Strings.isNullOrEmpty(masterObjectApiName)
                || !bizConfigThreadLocalCacheService.isOpenPricePolicy(controllerContext.getTenantId(),masterObjectApiName)) {
            SearchTemplateQueryExt.of(templateQuery).addFilter(Operator.EQ, DBRecord.ID, "-99");
            return;
        }
        List<String> pricePolicyIds = pricePolicyCoreService.GetPricePolicyIdsByAccountId(controllerContext.getUser(), masterObjectApiName, accountId);
        if (CollectionUtils.isEmpty(pricePolicyIds)) {
            SearchTemplateQueryExt.of(templateQuery).addFilter(Operator.EQ, DBRecord.ID, "-99");
            return;
        }
        List<String> pricePolicyProducts = buildSubQuery(pricePolicyIds);
        SearchTemplateQueryExt.of(templateQuery).addFilter(Operator.IN, DBRecord.ID, pricePolicyProducts, 10);
    }

    private List<String> buildSubQuery(List<String> pricePolicyIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filterList = Lists.newArrayList();
        filterList.add(buildFilter(PricePolicyConstants.PRICE_POLICY_ID, pricePolicyIds, Operator.IN));
        filterList.add(buildFilter(DBRecord.IS_DELETED, Lists.newArrayList("0"), Operator.EQ));
        query.setFilters(filterList);
        return Lists.newArrayList(JSON.toJSONString(query), SFAPreDefineObject.PricePolicyProduct.getApiName(), PricePolicyConstants.PRODUCT_ID);
    }

    private IFilter buildFilter(String fieldName, List<String> fieldValues, Operator operator) {
        IFilter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setFieldValues(fieldValues);
        filter.setOperator(operator);
        return filter;
    }
}
