package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.QualityInspectionRuleService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/02/16 18:04
 * @Version 1.0
 **/
@Slf4j
public class QualityInspectionRuleListController extends StandardListController {
    private static final QualityInspectionRuleService QUALITY_INSPECTION_RULE_SERVICE = SpringUtil.getContext().getBean(QualityInspectionRuleService.class);

    @Override
    protected void doFunPrivilegeCheck() {
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result doService(Arg arg) {
        result = super.doService(arg);
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        List<ObjectDataDocument> objectDataDocumentList = result.getDataList();

        QUALITY_INSPECTION_RULE_SERVICE.rewriteListDoc(controllerContext, objectDataDocumentList);

        buildButtons(result);
        return result;
    }

    private void buildButtons(Result result) {
        if (result.getButtonInfo() != null && CollectionUtils.notEmpty(result.getButtonInfo().getButtons())) {
            return;
        }
        List<IUdefButton> udefButtons = serviceFacade.findCustomButtonByUsePage(objectDescribe.getApiName(), getUsePageType(),
                controllerContext.getUser());
        if (CollectionUtils.notEmpty(udefButtons)) {
            udefButtons.forEach(x -> {
                ButtonExt buttonExt = ButtonExt.of(x);
                String actionCode = buttonExt.toButton().getAction();
                buttonExt.setAction(actionCode);
            });
        }
        Map<String, List<String>> buttonMap = Maps.newHashMap();
        Map<String, List<Wheres>> wheresMap = Maps.newHashMap();
        udefButtons.forEach(x -> wheresMap.put(x.getApiName(), ButtonExt.of(x).handleWheres(objectDescribe)));

        for (IObjectData data : queryResult.getData()) {
            List<IUdefButton> resultButtons = this.filterByLockAndWheres(data, udefButtons, objectDescribe, wheresMap);
            buttonMap.put(data.getId(), resultButtons.stream().map(IUdefButton::getApiName).collect(Collectors.toList()));
        }
        result.setButtonInfo(ButtonInfo.builder()
                .buttons(ButtonDocument.ofList(udefButtons))
                .buttonMap(buttonMap)
                .build());
    }

    private List<IUdefButton> filterByLockAndWheres(IObjectData data, List<IUdefButton> buttonList, IObjectDescribe describe,
                                                    Map<String, List<Wheres>> wheresMap) {
        if (CollectionUtils.empty(buttonList)) {
            return Collections.emptyList();
        }

        // 锁定数据是否下发按钮更具按钮配置来处理
        List<IUdefButton> buttons = buttonList.stream()
                .filter(x -> !ObjectDataExt.of(data).isLock() || ButtonExt.of(x).isShowWhenLock())
                .collect(Collectors.toList());
        return ButtonExt.filterButtonsWheres(data, describe, buttons, wheresMap);
    }
}