package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.QualityInspectionRuleService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.*;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/02/20 18:04
 * @Version 1.0
 **/
public class QualityInspectionRuleWebDetailController extends StandardWebDetailController {
    private static final QualityInspectionRuleService qualityInspectionRuleService = SpringUtil.getContext().getBean(QualityInspectionRuleService.class);

    @Override
    protected void doFunPrivilegeCheck() {
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result doService(Arg arg) {
        result = super.doService(arg);
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        ObjectDataDocument dataDocument = result.getData();
        qualityInspectionRuleService.rewriteDoc(controllerContext, Lists.newArrayList(dataDocument.getId()), dataDocument);

        removeComponentField(result);
        buildButtons(result);
        return result;
    }

    private void buildButtons(Result result) {
        List<IUdefButton> udefButtons = new ArrayList<>();
        udefButtons.add(serviceFacade.findButtonByApiName(controllerContext.getUser(), ObjectAction.DELETE.getButtonApiName(), describe));//Delete_button_default
        udefButtons.add(serviceFacade.findButtonByApiName(controllerContext.getUser(), ObjectAction.UPDATE.getButtonApiName(), describe));//Edit_button_default
        List<IButton> customButtonList = udefButtons.stream().map((x) -> ButtonExt.of(x).toButton()).collect(Collectors.toList());
        try {
            ILayout layout = new Layout((result.getLayout()));
            for (IComponent component : layout.getComponents()) {
                if ("head_info".equals(component.getName())) {
                    List<IButton> buttonList = component.getButtons();
                    for (IButton button : customButtonList) {
                        if (buttonList.stream().noneMatch(e -> e.getName().equals(button.getName()))) {
                            buttonList.add(button);
                        }
                    }
                    component.setButtons(buttonList);
                    break;
                }
            }
        } catch (Exception e) {
            log.error("质检规则按钮解析失败", e);
        }
    }

    private void removeComponentField(Result result) {
        ILayout dlayout = new Layout(result.getLayout());
        try {
            List<IComponent> components1 = dlayout.getComponents();
            for (IComponent component : components1) {
                if (component.getName().equals("form_component")) {
                    removeNewWebSomeFields(component);
                }
                if (component.getName().equals("head_info")) {
                    removeButtons(component);
                }
                log.warn("api name:{}", component.getName());
            }
        } catch (MetadataServiceException ex) {
            log.warn("removeComponent", ex);
        }
    }

    private final Set<String> buttonSet = Sets.newHashSet("SaleRecord_button_default", "Dial_button_default", "SendMail_button_default", "Discuss_button_default", "Remind_button_default", "Schedule_button_default");

    private void removeButtons(IComponent component) {
        List<IButton> iButtons = component.getButtons();
        if (CollectionUtils.isNotEmpty(iButtons)) {
            log.warn("dlayout buttons:{}", iButtons);
            List<IButton> newButtons = Lists.newArrayList();
            for (IButton button : iButtons) {
                if (!buttonSet.contains(button.getName())) {
                    newButtons.add(button);
                }
            }
            component.setButtons(newButtons);
        }
    }

    private final Set<String> specialFieldNameToRemoveSet = Sets.newHashSet("monitors", "msg_user");

    private void removeNewWebSomeFields(IComponent component) {
        IFormComponent formComponent = (IFormComponent) component;
        List<IFieldSection> fieldSections = formComponent.getFieldSections();
        List<IFormField> fields;
        for (IFieldSection fieldSection : fieldSections) {
            fields = fieldSection.getFields();
            for (IFormField formField : fieldSection.getFields()) {
                if (specialFieldNameToRemoveSet.contains(formField.getFieldName())) {
                    fields.remove(formField);
                }
            }
            fieldSection.setFields(fields);
        }
        formComponent.setFieldSections(fieldSections);
    }
}