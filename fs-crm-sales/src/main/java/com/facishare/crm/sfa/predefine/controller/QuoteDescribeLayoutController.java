package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.constant.QuoteConstants;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.VersionUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.appframework.metadata.dto.RecordTypeLayoutStructure;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_ADD;
import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_EDIT;

@Slf4j
public class QuoteDescribeLayoutController extends SFADescribeLayoutController {

    @Override
    protected void promptUpgrade(Arg arg, Result result) {
        if (CollectionUtils.notEmpty(result.getDetailObjectList())) {
            Optional<DetailObjectListResult> quoteLinesResult = result.getDetailObjectList().stream()
                    .filter(r -> "quote_id".equals(r.getFieldApiName())).findFirst();
            if (quoteLinesResult.isPresent()) {
                IObjectDescribe linesDescribe = ObjectDescribeExt.of(quoteLinesResult.get().getObjectDescribe());
                IFieldDescribe totalField = linesDescribe.getFieldDescribe(QuoteConstants.QuoteLinesField.TOTAL_DISCOUNT.getApiName());
                if (totalField != null) {
                    if (VersionUtil.isVersionEarlierEqualThan675(controllerContext.getRequestContext())) {
                        throw new ValidateException(I18N.text("sfa.CommonUtil.358.1"));
                    }
                }
                if (linesDescribe.getFieldDescribe(QuoteConstants.QuoteLinesField.TIERED_PRICE_BOOK_ID.getApiName()) != null
                        && VersionUtil.isVersionEarlierEqualThan680(controllerContext.getRequestContext())) {
                    throw new ValidateException(I18N.text("sfa.CommonUtil.358.1"));
                }
            }
        }
        if (VersionUtil.isVersionEarlierEqualThan715(controllerContext.getRequestContext())) {
            throw new ValidateException(I18N.text("sfa.CommonUtil.358.1"));
        }
    }

    @Override
    protected void handelDescribe(Arg arg, Result result) {
        super.handelDescribe(arg, result);
        IObjectDescribe describe = result.getObjectDescribe().toObjectDescribe();
        setCascadeParentApiName(describe);
    }

    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (arg.getLayout_type() == null) {
            return;
        }
        switch (arg.getLayout_type()) {
            case LAYOUT_TYPE_EDIT:
                PreDefLayoutUtil.setFormComponentFieldReadOnly(formComponent, Arrays.asList("account_id"));
                break;
            default:
                break;
        }
        if (SFAConfigUtil.isCPQ(controllerContext.getTenantId())) {
            removeDetailFields();
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (LAYOUT_TYPE_ADD.equals(arg.getLayout_type()) || LAYOUT_TYPE_EDIT.equals(arg.getLayout_type())) {
            handleReadOnlyFieldsForDetailLayout(newResult, QuoteConstants.QuoteLinesField.QUOTE_ID.getApiName());
        }
        if (isFlowEditLayout()) {
            removeDetailButtons(newResult, SFAPreDefineObject.QuoteLines.getApiName());
        }
        return newResult;
    }

    @Override
    protected boolean supportSaveDraft() {
        /*if (bizConfigThreadLocalCacheService.isOpenPricePolicy(controllerContext.getTenantId())) {
            return false;
        }*/
        return true;
    }

    @Override
    protected boolean supportSaveAndCreate() {
        return true;
    }

    @Override
    protected List<String> getReadOnlyFields(IObjectDescribe detailDescribe) {
        List<String> readOnlyFields = super.getReadOnlyFields(detailDescribe);
        readOnlyFields.addAll(getReadOnlyFieldsForQuote(detailDescribe));
        if (bizConfigThreadLocalCacheService.isCPQEnabled(controllerContext.getTenantId())) {
            readOnlyFields.add("node_price");
        }
        return readOnlyFields;
    }

    /**
     * 将价目表ID和商机ID的cascade_parent_api_name设置成account_id
     *
     * @param describe
     */
    private void setCascadeParentApiName(IObjectDescribe describe) {
        List<IFieldDescribe> fields = ObjectDescribeExt.of(describe)
                .getFieldDescribesSilently().stream().filter(field ->
                        field.getApiName().equals(QuoteConstants.QuoteField.PRICEBOOKID.getApiName()) ||
                                field.getApiName().equals(QuoteConstants.QuoteField.OPPORTUNITYID.getApiName()))
                .collect(Collectors.toList());

        for (IFieldDescribe field : fields) {
            field.set("cascade_parent_api_name", Arrays.asList("account_id"));
        }
    }

    private void removeDetailFields() {

        if (arg.getInclude_detail_describe()) {
            List<DetailObjectListResult> detailObjectList = result.getDetailObjectList();
            if (CollectionUtils.empty(detailObjectList)) {
                return;
            }
            detailObjectList.forEach(result -> {
                List<RecordTypeLayoutStructure> layoutList = result.getLayoutList();
                if (CollectionUtils.notEmpty(layoutList)) {
                    layoutList.forEach(layout -> {
                        Map detailLayout = layout.getDetail_layout();
                        LayoutExt layoutExt = LayoutExt.of(detailLayout);
                        formComponent = (FormComponent) layoutExt.getFormComponent().get().getFormComponent();
                        List<IFieldSection> fieldSectionsResult = Lists.newArrayList();
                        for (IFieldSection fieldSection : formComponent.getFieldSections()) {
                            List<IFormField> fields = fieldSection.getFields();
                            for (IFormField formField : fieldSection.getFields()) {
                                if (Objects.equals(formField.getFieldName(), "parent_prod_package_id")
                                        || Objects.equals(formField.getFieldName(), "bom_id")) {
                                    formField.setReadOnly(true);
                                }
                                if (BomConstants.ORDER_QUOTE_READONLY_BOM_FIELDS.contains(formField.getFieldName())) {
                                    formField.setReadOnly(true);
                                }
                                if(BomConstants.ORDER_QUOTE_REMOVE_BOM_FIELDS.contains(formField.getFieldName())){
                                    fields.remove(formField);
                                }
                            }
                            fieldSection.setFields(fields);
                            fieldSectionsResult.add(fieldSection);
                        }
                        formComponent.setFieldSections(fieldSectionsResult);
                    });
                }
            });
        }
    }
}
