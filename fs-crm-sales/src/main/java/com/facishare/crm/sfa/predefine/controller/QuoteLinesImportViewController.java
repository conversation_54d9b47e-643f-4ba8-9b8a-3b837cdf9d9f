package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.utilities.util.QuoteImportUtil;
import com.facishare.crm.sfa.utilities.util.imports.ImportSoUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardImportViewController;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class QuoteLinesImportViewController extends StandardImportViewController {
    private static final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitService.class);

    @Override
    protected void customFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {
        super.customFields(describe, fieldDescribes);
        if (!StringUtils.equals(describe.getApiName(), Utils.QUOTE_LINES_API_NAME)) {
            return;
        }
        if (arg.getImportType() == IMPORT_TYPE_ADD) {
            QuoteImportUtil.removeQuoteLinesImportFields(fieldDescribes, controllerContext.getTenantId());
        }
        if (arg.getImportType() == IMPORT_TYPE_EDIT) {
            ImportSoUtil.removeFields(fieldDescribes, ImportSoUtil.DETAIL_REMOVE_FIELD_BASE);
            if (multiUnitService.isOpenMultiUnit(controllerContext.getTenantId())) {
                ImportSoUtil.removeFields(fieldDescribes, ImportSoUtil.SALES_ORDER_PRODUCT_FILTER_FIELDS);
            }
            ImportSoUtil.removeFields(fieldDescribes, ImportSoUtil.ATTRIBUTE_FILTER_FIELDS);
            ImportSoUtil.removeFields(fieldDescribes, ImportSoUtil.PERIODIC_PRODUCT_FILTER_FIELDS);
        }
    }
}
