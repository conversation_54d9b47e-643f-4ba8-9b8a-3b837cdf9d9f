package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.ListHeaderUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.google.common.collect.Lists;

import java.util.List;

public class RebatePolicyListHeaderController extends StandardListHeaderController {
    private final List<String> removeActions = Lists.newArrayList(ObjectAction.BATCH_IMPORT.getActionCode(), ObjectAction.INTELLIGENTFORM.getActionCode());
    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        ListHeaderUtils.buttonConsumer(result, buttonList ->{
            buttonList.removeIf(button -> removeActions.contains(button.getAction()));
            //移动端移除新建按钮
            if (!RequestUtil.isWebRequest()) {
                buttonList.removeIf(button -> ObjectAction.CREATE.getActionCode().contains(button.getAction()));
            }
        });
        return result;
    }
}
