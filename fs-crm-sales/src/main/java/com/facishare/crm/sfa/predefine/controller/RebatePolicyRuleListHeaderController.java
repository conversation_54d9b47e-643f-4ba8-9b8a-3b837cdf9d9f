package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.RebatePolicyConstants;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.ListHeaderUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.layout.component.IComponentInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Set;

public class RebatePolicyRuleListHeaderController extends StandardListHeaderController {
    private final Set<String> removeFields = Sets.newHashSet(
            RebatePolicyConstants.RebatePolicyRuleField.RULE_CONDITION,
            RebatePolicyConstants.RebatePolicyRuleField.FUNCTION_INFO,
            RebatePolicyConstants.RebatePolicyRuleField.REBATE_USED_DATE,
            RebatePolicyConstants.RebatePolicyRuleField.PRODUCT_CONDITION_CONTENT,
            RebatePolicyConstants.RebatePolicyRuleField.PRODUCT_RANGE,
            RebatePolicyConstants.RebatePolicyRuleField.EXECUTION_RESULT);
    private final List<String> removeActions = Lists.newArrayList(ObjectAction.BATCH_IMPORT.getActionCode(), ObjectAction.INTELLIGENTFORM.getActionCode()
            , ObjectAction.BATCH_EXPORT.getActionCode());
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        //只有只web端  大列表的情况才进行移除
        if (IComponentInfo.PAGE_TYPE_LIST.equals(arg.getListType()) && RequestUtil.isWebRequest()) {
            removeFields(result);
        }
        ListHeaderUtils.buttonConsumer(result, buttonList -> {
            buttonList.removeIf(button -> removeActions.contains(button.getAction()));
            //移动端移除新建按钮
            if (!RequestUtil.isWebRequest()) {
                buttonList.removeIf(button -> ObjectAction.CREATE.getActionCode().contains(button.getAction()));
            }
        });
        return result;
    }

    /**
     * 移除规则条件，执行量字段
     *
     * @param result 结果
     */
    private void removeFields(Result result) {
        ListHeaderUtils.removerField(result, removeFields);
    }
}
