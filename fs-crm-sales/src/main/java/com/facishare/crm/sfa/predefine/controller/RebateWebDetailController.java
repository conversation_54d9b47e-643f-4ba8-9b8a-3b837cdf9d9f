package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.RebateConstants;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.translate.TranslateManager;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.CouponUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.RebateUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.WebDetailUtils;
import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.TabSection;
import com.facishare.paas.metadata.impl.ui.layout.component.TabsComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class RebateWebDetailController extends SFAWebDetailController {
    private static final AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);
    private final TranslateManager translateManager = SpringUtil.getContext().getBean(TranslateManager.class);
    /**
     * 删除action
     */
    private final ImmutableSet<String> removeAction = ImmutableSet.of(ObjectAction.INVALID.getActionCode());

    private static final String REBATE_TO_REBATE_DETAIL_JSON = "{\n" +
            "                    \"field_section\": [],\n" +
            "                    \"buttons\": [],\n" +
            "                    \"api_name\": \"related_what_detail\",\n" +
            "                    \"nameI18nKey\": \"sfa.rebate.detail.sourceRelatedObject\",\n" +
            "                    \"related_list_name\": \"\",\n" +
            "                    \"ref_object_api_name\": \"RebateDetailObj\",\n" +
            "                    \"header\": \"返利使用明细\",\n" +
            "                    \"type\": \"related_what\",\n" +
            "                    \"field_api_name\": \"sourceRelatedObject\",\n" +
            "                    \"order\": 999,\n" +
            "                    \"is_hidden\": false,\n" +
            "                    \"_id\": \"related_what_detail\"" +
            "                }";
    public static final String REMOVE_API_NAME = "SalesOrderProductObj_rebate_coupon_id_related_list";

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        Optional.ofNullable(result).map(Result::getLayout)
                .ifPresent(x -> {
                    ILayout layout = new Layout(x);
                    WebDetailLayout of = WebDetailLayout.of(layout);
                    //移除打印按钮
                    //of.removeButtonsByActionCode(Lists.newArrayList(ObjectAction.PRINT.getActionCode()));
                    //已经有明细记录，则不移除编辑按钮，和作废按钮
                    if (RebateUtils.hasRebateDetail(data, controllerContext.getUser())) {
                        of.removeButtonsByActionCode(Lists.newArrayList(removeAction));
                    }
                });

        render(result);
        issueWhatTab(result);
        //CouponUtils.issueConditionTab(result);
        removeSalesOrderProductTab(result);
        removeTab(result, "related_rule_condition");
        Map<String, String> sectionNameMap = Maps.newHashMapWithExpectedSize(2);
        sectionNameMap.put("product_condition_section__c", "paas.metadata.detail.layout.rebate.product_condition_section");
        sectionNameMap.put("product_range_section__c", "paas.metadata.detail.layout.rebate.product_range_section");
        WebDetailUtils.dealFormI18N(result, sectionNameMap);
        return result;
    }

    /**
     * 删除销售订单产品标签
     *
     * @param result 结果
     */
    public static void removeSalesOrderProductTab(Result result) {
        //产品返利的时候不去除
        if (result == null || RebateConstants.RebateType.PRODUCT.getValue().equals(result.getData().get(RebateConstants.RebateField.REBATE_TYPE.getApiName()))) {
            return;
        }
        removeTab(result, REMOVE_API_NAME);
    }

    private static void removeTab(Result result, String apiName) {
        if (result == null) {
            return;
        }

        Optional.ofNullable(result.getLayout()).ifPresent(x -> {
            ILayout layout = new Layout(x);
            LayoutExt layoutExt = LayoutExt.of(layout);
            try {
                //web端显示tab按钮
                List<IComponent> components = layoutExt.getComponents();
                components.stream()
                        .filter(z -> "tabs".equals(z.getType()))
                        .findFirst().ifPresent(component -> {
                            TabsComponent tabsComponent = (TabsComponent) component;
                            List<TabSection> tabs = tabsComponent.getTabs();
                            tabs.removeIf(t -> t.getApiName().contains(apiName));
                            tabsComponent.setTabs(tabs);
                            if (REMOVE_API_NAME.equals(apiName)) {
                                tabsComponent.getComponents().removeIf(tabComponentList -> tabComponentList.contains(apiName));
                            } else {
                                tabsComponent.getComponents().forEach(comList -> comList.removeIf(com -> apiName.equals(com)));
                                tabsComponent.getComponents().removeIf(comList -> CollectionUtils.isEmpty(comList));
                            }
                        });
                components.removeIf(iComponent -> apiName.equals(iComponent.getName()));
                layoutExt.setComponents(components);
            } catch (MetadataServiceException e) {
                log.error("getComponents error");
            }
        });
    }

    public void issueWhatTab(Result result) {
        TabSection tabSection = new TabSection();
        tabSection.setApiName("tab_related_what_detail");
        String i18nKey = "RebateObj.tab_related_what_detail.header";
        tabSection.setHeader(I18N.text(i18nKey));
        tabSection.set("nameI18nKey", i18nKey);
        List<String> whatComponents = Lists.newArrayList("related_what_detail");
        CouponUtils.issueWhatTab(result, tabSection, whatComponents, REBATE_TO_REBATE_DETAIL_JSON);
    }


    private void render(Result result) {
        Optional.ofNullable(result)
                .map(Result::getData)
                .map(ObjectDataDocument::toObjectData)
                .ifPresent(data -> {
                            translateManager.getTranslateService(data.get(RebateConstants.RebateField.PRODUCT_CONDITION_TYPE.getApiName(), String.class),
                                            RebateConstants.RebateType.MONEY.getValue())
                                    .render(data, controllerContext.getUser());
                            translateManager.getTranslateService(data.get(RebateConstants.RebateField.PRODUCT_RANGE_TYPE.getApiName(), String.class),
                                            RebateConstants.RebateType.PRODUCT.getValue())
                                    .render(data, controllerContext.getUser());
                        }
                );
    }


    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        if (RequestUtil.isMobileRequest() || RequestUtil.isH5Request()) {
            availableRangeUtils.removeMobileButton(layout);
        }
        return layout;
    }


}
