package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.*;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyCoreService;
import com.facishare.crm.sfa.predefine.service.real.HandleSearchQueryService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl;
import com.facishare.crm.sfa.predefine.service.real.TransformData4ViewService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.*;
import com.facishare.crm.sfa.utilities.proxy.model.GetPromotionProductsByAccountIdModel;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.crm.util.MtCurrentUtil;
import com.facishare.crm.util.UnitUtil;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.MultiCurrencyLogicServiceImpl;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IDataRightsParameter;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.CurrencyFieldDescribe;
import com.facishare.paas.metadata.impl.describe.FormulaFieldDescribe;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.support.GrayHelper;
import com.facishare.paas.metadata.support.SchemaHelper;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID;
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID__R;

/**
 * Created by luxin on 2018/10/27.
 */
public class SPURelatedListController extends StandardRelatedListController {
    private final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private final HandleSearchQueryService handleSearchQueryService = SpringUtil.getContext().getBean(HandleSearchQueryService.class);
    private final TransformData4ViewService transformData4ViewService = SpringUtil.getContext().getBean(TransformData4ViewService.class);
    private final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitServiceImpl.class);
    private final SchemaHelper schemaHelper = SpringUtil.getContext().getBean(SchemaHelper.class);
    private final AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);
    private final PromotionService promotionService = SpringUtil.getContext().getBean(PromotionService.class);
    private final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private final PricePolicyCoreService pricePolicyCoreService = SpringUtil.getContext().getBean(PricePolicyCoreService.class);
    private final UnitCoreService unitCoreService = SpringUtil.getContext().getBean(UnitCoreServiceImpl.class);
    private final SFAStockService sfaStockService = SpringUtil.getContext().getBean(SFAStockService.class);
    private final ProductCategoryBizService productCategoryBizService = SpringUtil.getContext().getBean(ProductCategoryBizService.class);
    private final MultiCurrencyLogicServiceImpl multiCurrencyLogicService = SpringUtil.getContext().getBean(MultiCurrencyLogicServiceImpl.class);
    private final MetaDataFindServiceExt metaDataFindServiceExt = SpringUtil.getContext().getBean(MetaDataFindServiceExt.class);
    private final GrayHelper grayHelper = SpringUtil.getContext().getBean(GrayHelper.class);
    private final FunctionUtils functionUtils = SpringUtil.getContext().getBean(FunctionUtils.class);

    private List<OrderBy> orders;
    private IObjectDescribe targetObjectDescribe;
    private boolean isRealNotLookSpu;
    private String priceBookId;
    private boolean isReturnNull = false;
    private boolean isPriceBookOpen;
    private List<IFilter> spuFilters;

    private String dataRightInnerJoinerSqlPart;
    private String dataRightWhereSqlPart;
    List<GetPromotionProductsByAccountIdModel.WrapProductPromotion> productPromotionList = null;
    protected String accountId = "";
    protected String partnerId = "";
    private boolean fromChannelMyProduct;
    Map<String, List<String>> pricePolicyProductListMap = null;
    String masterObjectApiName = StringUtils.EMPTY;
    List<String> multiSupportObjects = Lists.newArrayList(Utils.SALES_ORDER_API_NAME, Utils.QUOTE_API_NAME, Utils.SALE_CONTRACT_API_NAME);
    private boolean isDht = false;
    private boolean isCurrencyEnabled = false;
    private String mcCurrency = null;
    private List<String> spuIdList = null;
    private static final String NONE_FILTER_SQL = " and 1=2 ";
    private static final long THIRTY_DAYS_IN_MILLION_SECONDS = 30L * 24L * 60L * 60L * 1000L;
    private List<String> rangeProductList = null;
    private List<Wheres> targetWheres = Lists.newArrayList();
    private ObjectDescribeExt sourceDescribeExt;

    @Override
    protected void before(Arg arg) {
        ObjectDataDocument objectData = arg.getObjectData();
        isRealNotLookSpu = objectData != null && Objects.equals(objectData.get("is_real_lookup"), false);

        if (isRealNotLookSpu) {
            priceBookId = objectData.get("pricebook_id") == null ? null : objectData.get("pricebook_id").toString();
            isPriceBookOpen = Objects.equals(objectData.get("pricebook_open"), true);

            if (isPriceBookOpen) {
                if (priceBookId == null) {
                    isReturnNull = true;
                } else {
                    targetObjectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), Utils.PRICE_BOOK_PRODUCT_API_NAME);
                }
            } else {
                targetObjectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), Utils.PRODUCT_API_NAME);
            }
        }
        if (arg.getMasterData() != null && arg.getMasterData().get("object_describe_api_name") != null) {
            masterObjectApiName = arg.getMasterData().get("object_describe_api_name").toString();
        }
        super.before(arg);
        if (isRealNotLookSpu && !isReturnNull) {
            SearchTemplateQuery searchTemplateQuery = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(arg.getSearchQueryInfo());
            targetWheres = searchTemplateQuery.getWheres();
            searchTemplateQuery.setWheres(null);
            arg.setSearchQueryInfo(searchTemplateQuery.toJsonString());
            sourceDescribeExt = getSourceDescribe();
        }
    }

    @Override
    protected void init() {
        isCurrencyEnabled = bizConfigThreadLocalCacheService.isCurrencyEnabled(controllerContext.getTenantId());

        if (DhtUtil.isDhtOrAccessoriesMallRequest(controllerContext.getPeerName(), controllerContext.getAppId())) {
            isDht = true;
            if (null == arg.getObjectData()) {
                arg.setObjectData(ObjectDataDocument.of(new HashMap<>()));
            }
            //营销通+订货通一体化，游客角色过来的请求，已经带了account_id，也没有outTenantId、outUserId，不用查account_id
            if (arg.getObjectData().get("account_id") == null) {
                accountId = AccountUtil.getAccountIdByOutTenantId(controllerContext.getTenantId(), controllerContext.getUser().getOutTenantId(), controllerContext.getUser().getOutUserId());
            }
            partnerId = Optional.ofNullable(arg.getObjectData().get("partner_id")).orElse("").toString();
        }

        if (arg.getObjectData() != null) {
            if (arg.getObjectData().containsKey("account_id")) {
                accountId = Optional.ofNullable(arg.getObjectData().get("account_id")).orElse("").toString();
            }
            if (arg.getObjectData().containsKey("partner_id")) {
                partnerId = Optional.ofNullable(arg.getObjectData().get("partner_id")).orElse("").toString();
            }
            if (arg.getObjectData().containsKey("from_channel_my_product")) {
                fromChannelMyProduct = (boolean) arg.getObjectData().get("from_channel_my_product");
            }
            // 支持下游身份 获取客户信息并自动回填
            if (isDht) {
                arg.getObjectData().put("account_id", accountId);
            }
        }
        //没传币种，默认用本位币
        if (isCurrencyEnabled) {
            if (null == arg.getMasterData()) {
                if (null != arg.getObjectData() && arg.getObjectData().get("mc_currency") != null) {
                    mcCurrency = (String) arg.getObjectData().get("mc_currency");
                }
            } else {
                if (arg.getMasterData().get("mc_currency") != null) {
                    mcCurrency = (String) arg.getMasterData().get("mc_currency");
                }
            }
            if (Strings.isNullOrEmpty(mcCurrency)) {
                mcCurrency = MtCurrentUtil.findFunctionalCurrency(controllerContext.getUser());
            }
        }
        super.init();
    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        SearchTemplateQuery query;
        if (isRealNotLookSpu && !isReturnNull) {
            String searchQueryInfo = getSearchQueryInfo();
            SearchTemplateQuery searchTemplateQuery = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(searchQueryInfo);

            orders = searchTemplateQuery.getOrders();
            if (GrayUtil.isSpuRelatedListOptimize(controllerContext.getTenantId())) {
                query = searchQuery;
            } else {
                query = serviceFacade.getSearchTemplateQuery(
                        controllerContext.getUser(),
                        objectDescribe,
                        getSearchTemplateId(),
                        getSearchQueryInfo(),
                        true
                );
            }

            query.setPermissionType(1);
            IDataRightsParameter dataRightsParameter = new DataRightsParameter();
            dataRightsParameter.setRoleType("1");
            dataRightsParameter.setSceneType("all");
            dataRightsParameter.setCascadeDept(true);
            dataRightsParameter.setCascadeSubordinates(true);
            query.setDataRightsParameter(dataRightsParameter);

            handleFilters(query);
            modifyQueryByRefFieldName(query);
            handleDataRights(query);

            if (isPriceBookOpen || isDht) {
                dataRightInnerJoinerSqlPart = "";
                dataRightWhereSqlPart = "";
            } else {
                // 数据权限----------------------开始
                dataRightInnerJoinerSqlPart = handleSearchQueryService.getDataInnerJoinerSqlPart(controllerContext.getUser(), targetObjectDescribe, query);
                dataRightWhereSqlPart = handleSearchQueryService.getAuthPartSqlPart(controllerContext.getUser(), targetObjectDescribe, query, controllerContext);
                // 数据权限----------------------结束
            }

            if (CollectionUtils.isEmpty(orders)) {
                OrderBy orderBy = new OrderBy();
                orderBy.setFieldName("order_field");
                orderBy.setIsAsc(true);
                orderBy.setIndexName("order_field");
                this.orders = Lists.newArrayList(orderBy);
            }
        } else {
            query = super.customSearchTemplate(searchQuery);
        }

        productCategoryBizService.handleNewCategoryListFilters(controllerContext.getUser(), getSearchQueryInfo(), query);
        productCategoryBizService.oldCategoryTransferNewCategoryListFilters(controllerContext.getUser(), query, PRODUCT_CATEGORY_ID);

        functionUtils.handleFunctionFilter(controllerContext.getUser(), arg.getObjectData(), arg.getMasterData(), arg.getDetails(), query);
        spuFilters = query.getFilters();

        //叠加价格政策信息
        if (!Strings.isNullOrEmpty(masterObjectApiName) && bizConfigThreadLocalCacheService.isOpenPricePolicy(controllerContext.getTenantId(),masterObjectApiName)) {
            pricePolicyProductListMap = pricePolicyCoreService.GetPricePolicyProductsByAccountId(controllerContext.getUser(), masterObjectApiName, accountId);
            if (arg.getObjectData().get("tab_price_policy_id") != null) {
                String pricePolicyId = arg.getObjectData().get("tab_price_policy_id").toString();
                List<String> pricePolicySPUIdList = getSPUIdsFromPricePolicy(pricePolicyId);
                if (CollectionUtils.isNotEmpty(pricePolicySPUIdList)) {
                    if (!pricePolicySPUIdList.contains(PricePolicyConstants.ALL_PRODUCT)) {
                        com.facishare.crm.sfa.utilities.common.convert
                                .SearchUtil.fillFilterIn(query.getFilters(), DBRecord.ID, pricePolicySPUIdList);
                    }
                } else {
                    com.facishare.crm.sfa.utilities.common.convert
                            .SearchUtil.fillFilterIn(query.getFilters(), DBRecord.ID, Lists.newArrayList("null"));
                }
            }
        }
        return query;
    }


    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        if (isRealNotLookSpu && !isReturnNull) {

            String countSql;
            String getSpuListSql;

            boolean isSpecialSchema = schemaHelper.isSpecialSchema(controllerContext.getTenantId());

            String orderInfo = ConcatenateSqlUtils.getOrderPart(orders, "SPUObj",
                    isSpecialSchema ? "SPUObj" : "ext", objectDescribe);
            String pageInfo = ConcatenateSqlUtils.getPagePart(query);

            String spuDataRightInnerJoinerSqlPart, spuDataRightWhereSqlPart;
            if (!serviceFacade.isAdmin(controllerContext.getUser()) && !isPriceBookOpen && !isDht) {

                SearchTemplateQuery spuSearchTemplateQuery = new SearchTemplateQuery();
                spuSearchTemplateQuery.setPermissionType(1);

                IDataRightsParameter dataRightsParameter = new DataRightsParameter();
                dataRightsParameter.setRoleType("1");
                dataRightsParameter.setSceneType("all");
                dataRightsParameter.setCascadeDept(true);
                dataRightsParameter.setCascadeSubordinates(true);
                query.setDataRightsParameter(dataRightsParameter);
                spuSearchTemplateQuery.setDataRightsParameter(dataRightsParameter);

                spuDataRightInnerJoinerSqlPart = handleSearchQueryService.getDataInnerJoinerSqlPart(controllerContext.getUser(), objectDescribe.getObjectDescribe(), spuSearchTemplateQuery);
                spuDataRightWhereSqlPart = handleSearchQueryService.getAuthPartSqlPart(controllerContext.getUser(), objectDescribe.getObjectDescribe(), spuSearchTemplateQuery, controllerContext);

            } else {
                spuDataRightInnerJoinerSqlPart = "";
                spuDataRightWhereSqlPart = "";

            }
            targetWheres = handleSearchQueryService.processTargetWheres(controllerContext.getUser(), targetWheres,
                    arg.getObjectData(), arg.getMasterData(), arg.getDetails(), sourceDescribeExt, targetObjectDescribe);
            StringBuilder spuTagJoinSqlPart = new StringBuilder();
            StringBuilder spuTagWhereSqlPart = new StringBuilder();
            handleSearchQueryService.getSpuTagSqlPart(objectDescribe.getObjectDescribe(), spuFilters, spuTagJoinSqlPart, spuTagWhereSqlPart);
            String spuFilterSqlPart = handleSearchQueryService.getFilterSqlPart(objectDescribe.getObjectDescribe(), spuFilters, "SPUObj");
            String availableRangeJoinSqlPart = "";
            String availableRangeFilterSqlPart = "";
            String priceBookProductValidPeriodFilterSqlPart = "";
            //增加可售范围的校验
            if (!StringUtils.isEmpty(accountId)) {
                IObjectData sourceData = Objects.isNull(arg.getMasterData()) ? null : arg.getMasterData().toObjectData();
                rangeProductList = availableRangeUtils.getAvailableProductList4Spu(controllerContext.getUser(),
                        accountId, partnerId, sourceData);
                if (CollectionUtils.isEmpty(rangeProductList)) {
                    //给一个不存在的值,保证查不出任何数据
                    availableRangeFilterSqlPart = " and k.id='-99' ";
                } else if (rangeProductList.contains(AvailableConstants.PublicConstants.RANGE_ALL)) {
                    availableRangeFilterSqlPart = "";
                } else {
                    if (!isPriceBookOpen && GrayUtil.isSpuRelatedListOptimize(controllerContext.getTenantId())) {
                        //子查询改为关联查询
                        availableRangeJoinSqlPart = " inner join biz_available_product availableproduct\n" +
                                " on k.id = availableproduct.product_id\n";
                        availableRangeFilterSqlPart = String.format(" and availableproduct.tenant_id = '%s'\n" +
                                " and availableproduct.is_deleted = 0\n" +
                                " and availableproduct.available_range_id = %s", SqlEscaper.pg_escape(controllerContext.getTenantId()), SqlEscaper.any_clause(rangeProductList));
                    } else {
                        availableRangeFilterSqlPart = ConcatenateSqlUtils.availableRangeFilterSqlPart(controllerContext.getTenantId(), rangeProductList);
                    }
                }
                boolean isOpenValidPeriod = bizConfigThreadLocalCacheService.isOpenPriceBookProductValidPeriod(controllerContext.getTenantId());
                if (isOpenValidPeriod) {
                    priceBookProductValidPeriodFilterSqlPart = handlePriceBookProductValidPeriodFilter(sourceData);
                }
            }

            String policyProductFilterSqlPart = handlePromotionTabFilter();
            String recentlyOrderedFilterSqlPart = handleRecentlyTabFilter();
            String replaceValue = "ProductObj_product_id\\.\"product_status\"";
            String tableAlias = "ProductObj_product_id";
            if (grayHelper.fixTargetTableAlias(controllerContext.getTenantId())) {
                replaceValue = "PriceBookProductObj_ProductObj_product_id\\.\"product_status\"";
                tableAlias = "PriceBookProductObj_ProductObj_product_id";
            }

            // 滤价目表
            if (isPriceBookOpen) {

                String wheresFilterSqlPart = handleSearchQueryService.getWheresSqlPart(
                        ObjectDescribeExt.of(targetObjectDescribe),
                        targetWheres,
                        "PriceBookProductObj")
                        .replaceAll("PriceBookProductObj\\.\"product_status\"", "k.product_status")
                        .replaceAll(replaceValue, "k.product_status");

                // TODO 自定义字段的 left join 处理
                countSql = ConcatenateSqlUtils.priceBookCountSql(
                        controllerContext.getTenantId(),
                        spuDataRightInnerJoinerSqlPart,
                        spuDataRightWhereSqlPart,
                        priceBookId,
                        dataRightInnerJoinerSqlPart,
                        dataRightWhereSqlPart,
                        wheresFilterSqlPart,
                        spuFilterSqlPart,
                        availableRangeFilterSqlPart,
                        policyProductFilterSqlPart,
                        recentlyOrderedFilterSqlPart,
                        priceBookProductValidPeriodFilterSqlPart,
                        spuTagJoinSqlPart.toString(),
                        spuTagWhereSqlPart.toString(),
                        isSpecialSchema
                );
                getSpuListSql = ConcatenateSqlUtils.priceBookChooseSql(
                        controllerContext.getTenantId(),
                        spuDataRightInnerJoinerSqlPart,
                        spuDataRightWhereSqlPart,
                        priceBookId,
                        dataRightInnerJoinerSqlPart,
                        dataRightWhereSqlPart,
                        wheresFilterSqlPart,
                        spuFilterSqlPart,
                        orderInfo,
                        pageInfo,
                        availableRangeFilterSqlPart,
                        policyProductFilterSqlPart,
                        recentlyOrderedFilterSqlPart,
                        priceBookProductValidPeriodFilterSqlPart,
                        spuTagJoinSqlPart.toString(),
                        spuTagWhereSqlPart.toString(),
                        isSpecialSchema
                );
            } else {
                availableRangeJoinSqlPart = availableRangeJoinSqlPart.replaceAll("k.id", "ProductObj.id");
                availableRangeFilterSqlPart = availableRangeFilterSqlPart.replaceAll("k.id", "ProductObj.id");
                policyProductFilterSqlPart = policyProductFilterSqlPart.replaceAll("k.id", "ProductObj.id");
                String spuCountSql = isSpecialSchema
                        ? ConcatenateSqlUtils.GET_UNDER_SPU_SKU_LIST_SQL_COUNT_SCHEMA
                        : ConcatenateSqlUtils.GET_UNDER_SPU_SKU_LIST_SQL_COUNT;
                String spuSql = isSpecialSchema
                        ? ConcatenateSqlUtils.GET_UNDER_SPU_SKU_LIST_SQL_SCHEMA
                        : ConcatenateSqlUtils.GET_UNDER_SPU_SKU_LIST_SQL;
                String whereSql = handleSearchQueryService.getWheresSqlPart(ObjectDescribeExt.of(targetObjectDescribe),
                        targetWheres, "ProductObj");
                String priceBookProductFilterSql = handlePriceBookProductFilter();
                String merchantProductRangeFilterSql = handleMerchantProductRangeFilterSql();
                countSql = ConcatenateSqlUtils.getUnderSpuSkuListCountSql(
                        spuCountSql,
                        controllerContext.getTenantId(),
                        spuDataRightInnerJoinerSqlPart,
                        availableRangeJoinSqlPart,
                        availableRangeFilterSqlPart,
                        policyProductFilterSqlPart,
                        priceBookProductFilterSql,
                        merchantProductRangeFilterSql,
                        spuDataRightWhereSqlPart,
                        dataRightInnerJoinerSqlPart,
                        dataRightWhereSqlPart,
                        whereSql,
                        spuFilterSqlPart,
                        recentlyOrderedFilterSqlPart,
                        spuTagJoinSqlPart.toString(),
                        spuTagWhereSqlPart.toString()
                );
                getSpuListSql = ConcatenateSqlUtils.getUnderSpuSkuListSql(
                        spuSql,
                        controllerContext.getTenantId(),
                        spuDataRightInnerJoinerSqlPart,
                        availableRangeJoinSqlPart,
                        availableRangeFilterSqlPart,
                        policyProductFilterSqlPart,
                        priceBookProductFilterSql,
                        merchantProductRangeFilterSql,
                        spuDataRightWhereSqlPart,
                        dataRightInnerJoinerSqlPart,
                        dataRightWhereSqlPart,
                        whereSql,
                        spuFilterSqlPart,
                        recentlyOrderedFilterSqlPart,
                        orderInfo,
                        pageInfo,
                        spuTagJoinSqlPart.toString(),
                        spuTagWhereSqlPart.toString()
                );
            }

            List<Map> countInfo;
            if (simplifyCount()) {
                Map<String,Object> map = Maps.newHashMap();
                map.put("count", 20);
                countInfo = Lists.newArrayList(map);
            } else {
                try {
                    countSql = countSql.replaceAll(tableAlias, "k");
                    countInfo = objectDataService.findBySql(controllerContext.getTenantId(), countSql);
                } catch (MetadataServiceException e) {
                    throw new RuntimeException();
                }
            }

            QueryResult<IObjectData> result = new QueryResult<>();
            Optional<Map> icountInfoMap = Objects.isNull(countInfo) ? Optional.empty() : countInfo.stream().findFirst();

            if (!icountInfoMap.isPresent()) {
                result.setData(Lists.newArrayListWithCapacity(0));

                result.setTotalNumber(0);
                return result;
            } else if (icountInfoMap.get().get("count") == null) {
                result.setData(Lists.newArrayListWithCapacity(0));
                return result;
            } else {
                Integer count = Integer.parseInt(icountInfoMap.get().get("count").toString());
                result.setTotalNumber(count);
            }
            try {
                getSpuListSql = getSpuListSql.replaceAll(tableAlias, "k");
                List<Map> neededSpuIdsResult = objectDataService.findBySql(controllerContext.getTenantId(), getSpuListSql);
                List<String> spuIds = neededSpuIdsResult.stream().map(o -> o.get(DBRecord.ID).toString()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(spuIds)) {
                    List<IObjectData> tmpSpuDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), spuIds, Utils.SPU_API_NAME);
                    Map<String, IObjectData> spuIdDataMap = tmpSpuDataList.stream().collect(Collectors.toMap(IObjectData::getId, o -> o));

                    List<IObjectData> spuDataList = Lists.newArrayListWithCapacity(spuIds.size());
                    spuIds.forEach(o -> spuDataList.add(spuIdDataMap.get(o)));
                    result.setData(spuDataList);
                } else {
                    result.setData(Lists.newArrayListWithCapacity(0));
                }
                return result;
            } catch (MetadataServiceException e) {
                throw new MetaDataException(SystemErrorCode.METADATA_ERROR, e);
            }
        } else if (isRealNotLookSpu) {
            QueryResult<IObjectData> objectDataQueryResult = new QueryResult<>();
            objectDataQueryResult.setData(Lists.newArrayListWithCapacity(0));
            return objectDataQueryResult;
        } else {
            return super.findData(query);

        }
    }

    private String handlePriceBookProductFilter() {
        String priceBookProductFilterSql = "";
        if (arg.getObjectData() == null) {
            return priceBookProductFilterSql;
        }
        if (bizConfigThreadLocalCacheService.isOpenWhetherFilterPriceBookSelectProduct(controllerContext.getTenantId())) {
            return priceBookProductFilterSql;
        }
        IObjectData objectData = arg.getObjectData().toObjectData();
        if (Utils.PRICE_BOOK_API_NAME.equals(objectData.get("source_object_api_name", String.class)) && StringUtils.isNotEmpty(objectData.get("price_book_id", String.class))) {
            priceBookProductFilterSql = String.format("and ProductObj.id not in (select distinct product_id\n" +
                    "from price_book_product where tenant_id='%s'\n" +
                    "and is_deleted in (0, 1) and pricebook_id = '%s')", SqlEscaper.pg_escape(controllerContext.getTenantId()), SqlEscaper.pg_escape(objectData.get("price_book_id", String.class)));
        }
        return priceBookProductFilterSql;
    }

    private String handleMerchantProductRangeFilterSql() {
        String merchantProductRangeFilterSql = "";
        if (!bizConfigThreadLocalCacheService.isDhtMultiLevelOrder(controllerContext.getTenantId())) {
            return merchantProductRangeFilterSql;
        }
        IObjectData sourceData = Objects.isNull(arg.getMasterData()) ? null : arg.getMasterData().toObjectData();
        if (Objects.isNull(sourceData) || !MultiLevelOrderConstants.RECORD_TYPE.equals(sourceData.getRecordType()) || (!SFAPreDefine.PriceBook.getApiName().equals(sourceData.getDescribeApiName()) && !fromChannelMyProduct)) {
            return merchantProductRangeFilterSql;
        }
        String fixPartnerId;
        if (SFAPreDefine.PriceBook.getApiName().equals(sourceData.getDescribeApiName())) {
            fixPartnerId = availableRangeUtils.getPartnerIdByPartnerRange(sourceData);
        } else {
            fixPartnerId = partnerId;
        }
        if (StringUtils.isEmpty(fixPartnerId)) {
            return merchantProductRangeFilterSql;
        }
        IObjectData partnerData;
        try {
            IActionContext actionContext = ActionContextExt.of(controllerContext.getUser()).skipRelevantTeam().getContext();
            partnerData = objectDataService.findById(fixPartnerId, controllerContext.getTenantId(), actionContext, SFAPreDefine.Partner.getApiName());
        } catch (MetadataServiceException e) {
            log.error("findById error:",e);
            return merchantProductRangeFilterSql;
        }
        if (Objects.isNull(partnerData) || !Boolean.TRUE.equals(partnerData.get("product_range_control", Boolean.class))) {
            return merchantProductRangeFilterSql;
        }
        merchantProductRangeFilterSql = String.format("and ProductObj.id = any(SELECT distinct a.product_id\n" +
                "FROM merchant_product_lines a \n" +
                "INNER JOIN merchant_product_range b ON a.merchant_product_range_id = b.id\n" +
                "AND a.tenant_id = b.tenant_id \n" +
                "WHERE a.is_deleted = 0 AND b.is_deleted = 0 \n" +
                "AND a.tenant_id = '%s' AND b.partner_id = '%s')", SqlEscaper.pg_escape(controllerContext.getTenantId()), SqlEscaper.pg_escape(fixPartnerId));
        return merchantProductRangeFilterSql;
    }

    private String handlePriceBookProductValidPeriodFilter(IObjectData sourceData) {
        Long businessDate = ValidDateUtils.getValidDate(controllerContext.getTenantId(), null, ObjectDataDocument.of(sourceData));
        Long nowDateTime = null == businessDate ? Long.valueOf(System.currentTimeMillis()) : businessDate;
        String priceBookProductValidPeriodFilterSqlPart = "AND (PriceBookProductObj.start_date is null or PriceBookProductObj.start_date <= " + nowDateTime + ") AND (PriceBookProductObj.end_date is null or PriceBookProductObj.end_date >= " + nowDateTime + ")";
        return priceBookProductValidPeriodFilterSqlPart;
    }

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        QueryResult<IObjectData> result = super.getQueryResult(query);
        //叠加价格政策信息
        if (bizConfigThreadLocalCacheService.isOpenPricePolicy(controllerContext.getTenantId(),masterObjectApiName)) {
            if (pricePolicyProductListMap != null && !pricePolicyProductListMap.isEmpty()) {
                pricePolicyCoreService.HasPricePolicyForSPU(controllerContext.getUser(), result.getData(), pricePolicyProductListMap);
            }
        }
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        stopWatch.lap("after");

        // TODO 看看是否是必须的
        LayoutUtils.removeListLayoutUpdateTypeButtons4MobileOrH5(result.getListLayouts());

        stopWatch.lap("remove");
        if (isRealNotLookSpu) {
            // 无规格的数据<spuId,data>
            Map<String, ObjectDataDocument> noHaveSpecSpuIdDataMapping = Maps.newHashMap();

            result.getDataList().forEach(o -> {
                if (!Objects.equals(o.get("is_spec"), true)) {
                    noHaveSpecSpuIdDataMapping.put(o.getId(), o);
                }
            });
            if (MapUtils.isNotEmpty(noHaveSpecSpuIdDataMapping)) {
                fillProductData2NoSpecSpuSkuData(noHaveSpecSpuIdDataMapping);
            }

            fillFloorPriceSku();
            stopWatch.lap("fillFloorPriceSku");

            //订货通，单位返回unit__r
            fillUnitLabel();
        }
        if (GrayUtil.isGrayOrderPromotion(controllerContext.getTenantId())) {
            if (PromotionUtil.getIsPromotionEnable(controllerContext.getUser(), controllerContext.getClientInfo())) {
                //叠加商品促销信息
                if (arg.getObjectData() != null && arg.getObjectData().get("account_id") != null) {
                    String accountId = arg.getObjectData().get("account_id").toString();
                    productPromotionList = promotionService.getProductPromotionList(controllerContext.getTenantId(), controllerContext.getUser(), accountId);
                    if (productPromotionList != null) {
                        List<String> productIds = new ArrayList<>();
                        productPromotionList.forEach(o -> {
                            String productId = o.getProductId();
                            if (!productIds.contains(productId)) {
                                productIds.add(productId);
                            }
                        });
                        if (!productIds.isEmpty()) {
                            List<IObjectData> productList = serviceFacade.findObjectDataByIdsIncludeDeleted(controllerContext.getUser(), productIds, Utils.PRODUCT_API_NAME);
                            if (productList != null && !productList.isEmpty()) {
                                result.getDataList().forEach(o -> {
                                    if (productList.stream().filter(r -> r.get("spu_id").equals(o.getId())).count() > 0) {
                                        o.put("have_promotion", true);
                                    }
                                });
                            }
                        }
                    }
                }
            }
        }

        stopWatch.logSlow(1000);
        productCategoryBizService.handleCategoryName(controllerContext.getUser(), result, PRODUCT_CATEGORY_ID, PRODUCT_CATEGORY_ID__R);
        reorder(result);
        return result;
    }


    /**
     * 将sku信息填充到无规格的spu上
     *
     * @param noHaveSpecSpuIdDataMapping key:spuId, value: data
     */
    private void fillProductData2NoSpecSpuSkuData(Map<String, ObjectDataDocument> noHaveSpecSpuIdDataMapping) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        IFilter filter = SearchUtil.filter("spu_id", Operator.IN, noHaveSpecSpuIdDataMapping.keySet());
        searchTemplateQuery.setFilters(Lists.newArrayList(filter));

        // limit 和 offset 都为0代表要取全部数据
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setOffset(0);

        // 获取无规格的sku数据列表
        IActionContext actionContext = ActionContextExt.of(controllerContext.getUser()).skipRelevantTeam().getContext();
        searchTemplateQuery.setNeedReturnCountNum(false);
        QueryResult<IObjectData> skuDataQueryResult = serviceFacade.findBySearchQuery(actionContext, Utils.PRODUCT_API_NAME, searchTemplateQuery);

        stopWatch.lap("find sku");

        Map<String, IObjectData> skuIdDataMapping = Maps.newHashMap();
        Set<String> spuIds = Sets.newHashSet();

        Map<String, IObjectDescribe> apiNameDescribeMap = serviceFacade.findObjects(controllerContext.getTenantId(), Lists.newArrayList(Utils.PRODUCT_API_NAME, Utils.PRICE_BOOK_PRODUCT_API_NAME));

        stopWatch.lap("find describes");

        for (IObjectData skuData : skuDataQueryResult.getData()) {
            skuIdDataMapping.put(skuData.getId(), skuData);
            spuIds.add(skuData.get("spu_id", String.class));
        }

        // 价目表选择产品,处理单位为定价单位 价格处理成基准单位*定价单位转换比例
        ObjectDataDocument objectData1 = arg.getObjectData();
        if (multiSupportObjects.contains(masterObjectApiName) ||
                (objectData1 != null && Utils.PRICE_BOOK_API_NAME.equals(objectData1.get("source_object_api_name")) || isPriceBookOpen)) {
            if (multiUnitService.isOpenMultiUnit(controllerContext.getTenantId())) {
                multiUnitService.handleUnitAndPrice(skuDataQueryResult.getData(), controllerContext.getTenantId());
                unitCoreService.handCommonUnitInfo(controllerContext.getTenantId()
                        , skuDataQueryResult.getData()
                        , Utils.PRODUCT_API_NAME, masterObjectApiName);
            }
        }
        transformData4ViewService.batchTransformDataForView(
                controllerContext.getUser(),
                apiNameDescribeMap.get(Utils.PRODUCT_API_NAME),
                skuDataQueryResult.getData()
        );

        stopWatch.lap("transform");

        if (isPriceBookOpen && isRealNotLookSpu) {
            Map<String, String> priceBookProductIds = getPriceBookProductIdAndSpuIdMapping(spuIds);

            List<IObjectData> priceBookProductDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), Lists.newArrayList(priceBookProductIds.keySet()), Utils.PRICE_BOOK_PRODUCT_API_NAME);
            //填充pricebook_id__r
            List<INameCache> recordName = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(priceBookProductDataList)) {
                Set<String> priceBookIdList = priceBookProductDataList.stream().map(x -> x.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName(), String.class)).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(priceBookIdList)) {
                    recordName.addAll(serviceFacade.findRecordName(ActionContextExt.of(controllerContext.getUser()).getContext()
                            , SFAPreDefineObject.PriceBook.getApiName(), Lists.newArrayList(priceBookIdList)));
                }
            }
            priceBookProductDataList.forEach(o -> {
                ObjectDataDocument objectDataDocument = noHaveSpecSpuIdDataMapping.get(priceBookProductIds.get(o.getId()));
                if (objectDataDocument != null) {
                    IObjectData spuData = skuIdDataMapping.get(o.get("product_id", String.class));
                    Optional<INameCache> nameCache = recordName.stream().filter(x -> o.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName(), String.class).equals(x.getId())).findFirst();
                    o.set("product_id__ro", ObjectDataDocument.of(spuData));
                    o.set("pricebook_id__r", nameCache.isPresent() ? nameCache.get().getName() : "");
                    objectDataDocument.put("pricebook_product_id__ro", ObjectDataExt.of(o).toMap());
                }
            });

            stopWatch.lap("transform pricebookProduct");

        } else {
            skuDataQueryResult.getData().forEach(objectData -> {
                ObjectDataDocument objectDataDocument = noHaveSpecSpuIdDataMapping.get(objectData.get("spu_id", String.class));
                if (objectDataDocument != null) {
                    objectDataDocument.put("product_id__ro", ObjectDataExt.of(objectData).toMap());
                }
            });
        }
    }

    private void fillFloorPriceSku() {
        if (Strings.isNullOrEmpty(accountId) || CollectionUtils.isEmpty(result.getDataList())) {
            return;
        }

        //虚拟字段开关
        boolean isVirtualExtensionEnabled = bizConfigThreadLocalCacheService.isVirtualExtensionEnabled(controllerContext.getTenantId());
        log.info("isListPage[{}], isVirtualExtensionEnabled[{}]", isListPage(), isVirtualExtensionEnabled);
        if (!isListPage() || !isVirtualExtensionEnabled) {
            return;
        }

        //最低sku
        List<String> spuIds = result.getDataList().stream().map(ObjectDataDocument::getId).collect(Collectors.toList());
        List<String> excludeSkuIds = getExcludeSkuIds();
        Map<String, IObjectData> spuId2FloorPriceSkuMap = FloorPriceProductUtil.getFloorPriceSku(controllerContext.getUser(), spuIds, rangeProductList, excludeSkuIds, arg.getRelatedListName(), accountId, partnerId, priceBookId, masterObjectApiName, arg.getMasterData(), stopWatch, isDht, arg.getObjectData(), arg.getDetails());
        stopWatch.lap("getFloorPriceSku");
        if (spuId2FloorPriceSkuMap.isEmpty()) {
            log.warn("spuId2FloorPriceSkuMap isEmpty");
            return;
        }

        fillFloorPriceSku(spuId2FloorPriceSkuMap);

        List<String> floorPriceProductIds = spuId2FloorPriceSkuMap.values().stream().map(IObjectData::getId).collect(Collectors.toList());
        fillVirtualStockField(floorPriceProductIds, spuId2FloorPriceSkuMap);
        stopWatch.lap("fillVirtualStockField");
    }

    private void fillFloorPriceSku(Map<String, IObjectData> spuId2FloorPriceSkuMap) {
        boolean isPriceBookEnabled = bizConfigThreadLocalCacheService.isPriceBookEnabled(controllerContext.getTenantId());

        String functionalCurrency = null;                         //本位币
        Map<String, String> toMcCurrencyExchangeRateMap = null;
        Map<String, String> currencySymbolMap = null;             //币种符号
        if (isCurrencyEnabled) {
            functionalCurrency = MtCurrentUtil.findFunctionalCurrency(controllerContext.getUser());
            toMcCurrencyExchangeRateMap = MtCurrentUtil.getExchangeRateMap(controllerContext.getUser(), mcCurrency);

            //币种符号
            List<String> currencyCodes = Lists.newArrayList(Sets.newHashSet(mcCurrency, functionalCurrency));
            currencySymbolMap = multiCurrencyLogicService.findCurrencySymbolByCurrencyCodes(currencyCodes);
        }

        //产品价格精度
        IObjectDescribe productDescribe = serviceFacade.findObject(controllerContext.getTenantId(), Utils.PRODUCT_API_NAME);
        CurrencyFieldDescribe productPriceField = (CurrencyFieldDescribe) productDescribe.getFieldDescribe(ProductConstants.PRODUCT_PRICE);
        Integer productPriceDecimalPlaces = productPriceField.getDecimalPlaces();

        //价目表价格……精度
        Integer priceBookPriceDecimalPlaces = null;
        Integer sellingPriceDecimalPlaces = null;
        if (isPriceBookEnabled) {
            IObjectDescribe priceBookProductDescribe = serviceFacade.findObject(controllerContext.getTenantId(), Utils.PRICE_BOOK_PRODUCT_API_NAME);
            CurrencyFieldDescribe sellingPriceField = (CurrencyFieldDescribe) priceBookProductDescribe.getFieldDescribe(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName());
            FormulaFieldDescribe priceBookPriceField = (FormulaFieldDescribe) priceBookProductDescribe.getFieldDescribe(PriceBookConstants.ProductField.PRICEBOOKPRICE.getApiName());
            sellingPriceDecimalPlaces = sellingPriceField.getDecimalPlaces();
            priceBookPriceDecimalPlaces = priceBookPriceField.getDecimalPlaces();
        }

        for (ObjectDataDocument data : result.getDataList()) {
            String spuId = (String) data.get(DBRecord.ID);
            IObjectData floorPriceSku = spuId2FloorPriceSkuMap.get(spuId);
            if (floorPriceSku == null) {
                continue;
            }
            data.put(SpuConstants.FLOOR_PRICE_PRODUCT_ID, floorPriceSku.get(DBRecord.ID));
            data.put(SpuConstants.VIRTUAL_PRODUCT_SPEC, floorPriceSku.get(ProductConstants.PRODUCT_SPEC));
            data.put(SpuConstants.VIRTUAL_PRODUCT_CODE, floorPriceSku.get(ProductConstants.PRODUCT_CODE));
            data.put(SpuConstants.VIRTUAL_PRODUCT_BARCODE, floorPriceSku.get(ProductConstants.BARCODE));
            if (isDht) {
                data.put(SpuConstants.UNIT, floorPriceSku.get("unit"));
            }

            if (!isCurrencyEnabled) {
                data.put(SpuConstants.VIRTUAL_PRODUCT_PRICE, floorPriceSku.get(ProductConstants.PRODUCT_PRICE));

                if (isPriceBookEnabled) {
                    String sellingPriceStr = floorPriceSku.get("pricebook_sellingprice", String.class);
                    if (!Strings.isNullOrEmpty(sellingPriceStr)) {
                        BigDecimal sellingPrice = new BigDecimal(sellingPriceStr);
                        data.put(SpuConstants.VIRTUAL_PRICE_BOOK_SELLING_PRICE, sellingPrice.setScale(sellingPriceDecimalPlaces, BigDecimal.ROUND_HALF_EVEN));
                    }

                    String priceBookPriceStr = floorPriceSku.get("pricebook_price", String.class);
                    if (!Strings.isNullOrEmpty(priceBookPriceStr)) {
                        BigDecimal priceBookPrice = new BigDecimal(priceBookPriceStr);
                        data.put(SpuConstants.VIRTUAL_PRICE_BOOK_PRICE, priceBookPrice.setScale(priceBookPriceDecimalPlaces, BigDecimal.ROUND_HALF_EVEN));
                    }

                    data.put(SpuConstants.PRICE_BOOK_ID, floorPriceSku.get("pricebook_id", String.class));
                    data.put(SpuConstants.PRICE_BOOK_PRODUCT_ID, floorPriceSku.get("pricebook_product_id", String.class));
                }
                continue;
            }

            //开了多币种，价格换成目标币种
            BigDecimal toMcCurrencyExchangeRate = MtCurrentUtil.getExchangeRate(toMcCurrencyExchangeRateMap, functionalCurrency, mcCurrency);
            BigDecimal productPrice = new BigDecimal((String) floorPriceSku.get(ProductConstants.PRODUCT_PRICE));
            productPrice = productPrice.multiply(toMcCurrencyExchangeRate).setScale(productPriceDecimalPlaces, BigDecimal.ROUND_HALF_EVEN);
            data.put(SpuConstants.VIRTUAL_PRODUCT_PRICE, productPrice);

            String mcCurrencyR = currencySymbolMap.get(mcCurrency);
            String mcFunctionalCurrencyR = currencySymbolMap.get(functionalCurrency);

            data.put(SpuConstants.MC_CURRENCY__R, mcCurrencyR);
            data.put(SpuConstants.MC_FUNCTIONAL_CURRENCY__R, mcFunctionalCurrencyR);

            if (isPriceBookEnabled) {
                String sellingPriceStr = floorPriceSku.get("pricebook_sellingprice", String.class);
                if (!Strings.isNullOrEmpty(sellingPriceStr)) {
                    BigDecimal sellingPrice = new BigDecimal(sellingPriceStr);
                    sellingPrice = sellingPrice.multiply(toMcCurrencyExchangeRate).setScale(sellingPriceDecimalPlaces, BigDecimal.ROUND_HALF_EVEN);
                    data.put(SpuConstants.VIRTUAL_PRICE_BOOK_SELLING_PRICE, sellingPrice);
                    data.put(SpuConstants.VIRTUAL_PRICE_BOOK_SELLING_PRICE__N, mcCurrencyR + sellingPrice);
                }

                String priceBookPriceStr = floorPriceSku.get("pricebook_price", String.class);
                if (!Strings.isNullOrEmpty(priceBookPriceStr)) {
                    BigDecimal basePriceBookPrice = new BigDecimal(priceBookPriceStr).setScale(priceBookPriceDecimalPlaces, BigDecimal.ROUND_HALF_EVEN);
                    BigDecimal priceBookPrice = new BigDecimal(priceBookPriceStr);
                    priceBookPrice = priceBookPrice.multiply(toMcCurrencyExchangeRate).setScale(priceBookPriceDecimalPlaces, BigDecimal.ROUND_HALF_EVEN);
                    data.put(SpuConstants.VIRTUAL_PRICE_BOOK_PRICE, priceBookPrice);
                    data.put(SpuConstants.VIRTUAL_PRICE_BOOK_PRICE__N, mcCurrencyR + priceBookPrice);

                    data.put(SpuConstants.VIRTUAL_BASE_PRICE_BOOK_PRICE, basePriceBookPrice);
                    data.put(SpuConstants.VIRTUAL_BASE_PRICE_BOOK_PRICE__N, mcFunctionalCurrencyR + basePriceBookPrice);
                }

                data.put(SpuConstants.PRICE_BOOK_ID, floorPriceSku.get("pricebook_id", String.class));
                data.put(SpuConstants.PRICE_BOOK_PRODUCT_ID, floorPriceSku.get("pricebook_product_id", String.class));
            }
        }
    }

    /**
     * 排除已选产品
     */
    private List<String> getExcludeSkuIds() {
        SearchTemplateQuery query = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(arg.getSearchQueryInfo());

        List<Wheres> wheres = query.getWheres();

        for (Wheres where : wheres) {
            List<IFilter> filters = where.getFilters();
            for (IFilter filter : filters) {
                if (Objects.equals(filter.getFieldName(), "product_id") && Objects.equals(filter.getOperator().name(), "NIN")) {
                    return filter.getFieldValues();
                }
            }
        }
        return Lists.newArrayList();
    }

    private void fillVirtualStockField(List<String> floorPriceProductIds, Map<String, IObjectData> spuId2FloorPriceSku) {
        if (Strings.isNullOrEmpty(masterObjectApiName)
                || !Objects.equals(masterObjectApiName, "SalesOrderObj")
                || !Objects.equals(arg.getRelatedListName(), "SPUObj_salesorderproduct_list")) {
            return;
        }
        boolean isStockEnabled = bizConfigThreadLocalCacheService.isStockEnabled(controllerContext.getTenantId());
        if (!isStockEnabled) {
            return;
        }

        //最低价格sku的库存
        String shippingWarehouseId = arg.getObjectData() == null ? null : (String) arg.getObjectData().get("shipping_warehouse_id");
        boolean isDhtMultiLevelOrder = bizConfigThreadLocalCacheService.isDhtMultiLevelOrder(controllerContext.getTenantId());
        //开启渠道多级订货且record_type=distribution__c，取库存才按partnerId查
        String recordType = "";
        if (arg.getMasterData() != null) {
            recordType = Optional.ofNullable(arg.getMasterData().get(SystemConstants.Field.RecordType.apiName)).orElse("").toString();
        }
        Map<String, String> productId2StockMap = sfaStockService.queryStockMap(controllerContext.getUser(), accountId, partnerId, shippingWarehouseId, floorPriceProductIds, recordType);
        if (productId2StockMap.isEmpty()) {
            return;
        }

        for (ObjectDataDocument data : result.getDataList()) {
            String spuId = (String) data.get(IObjectData.ID);
            IObjectData floorPriceSku = spuId2FloorPriceSku.get(spuId);
            if (floorPriceSku == null) {
                continue;
            }

            data.put(SpuConstants.VIRTUAL_AVAILABLE_STOCK, productId2StockMap.get(floorPriceSku.getId()));
        }
    }

    private void fillUnitLabel() {
        if (!isDht || CollectionUtils.isEmpty(result.getDataList())) {
            return;
        }

        Map<String, String> unit2Label = UnitUtil.getUnitOption(controllerContext.getUser(), objectDescribe, Utils.SPU_API_NAME, SpuConstants.UNIT);

        for (ObjectDataDocument data : result.getDataList()) {
            String unit = (String) data.get("unit");
            data.put("unit__r", unit2Label.get(unit));
        }
    }

    private Map<String, String> getPriceBookProductIdAndSpuIdMapping(Set<String> spuIds) {
        String findPriceBookProductSql = "select pb.id as _id,k.spu_id\n" +
                "from price_book_product as pb,\n" +
                "     biz_product as k\n" +
                "where k.spu_id = %s \n" +
                "  and pb.tenant_id = k.tenant_id\n" +
                "  and k.id = pb.product_id\n" +
                "  and pb.tenant_id = '%s'\n" +
                "  and pb.pricebook_id = '%s';";
        String sql = String.format(findPriceBookProductSql, SqlEscaper.any_clause(spuIds), SqlEscaper.pg_escape(controllerContext.getTenantId()), SqlEscaper.pg_escape(priceBookId));
        try {
            return objectDataService.findBySql(controllerContext.getTenantId(), sql)
                    .stream()
                    .collect(Collectors.toMap(o -> o.get(DBRecord.ID).toString(), o -> o.get("spu_id").toString()));
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    private List<String> getSPUIdsFromPricePolicy(String pricePolicyId) {
        List<String> spuIds = new ArrayList<>();
        if (!Objects.isNull(pricePolicyProductListMap)) {
            List<String> pricePolicyProductIds = pricePolicyProductListMap.get(pricePolicyId);
            if (pricePolicyProductIds.contains(PricePolicyConstants.ALL_PRODUCT)) {
                spuIds = Lists.newArrayList("ALL");
            } else {
                List<IObjectData> productList = serviceFacade.findObjectDataByIdsIgnoreAll(controllerContext.getTenantId(), pricePolicyProductIds, Utils.PRODUCT_API_NAME);
                if (productList != null && !productList.isEmpty()) {
                    spuIds = productList.stream().map(o -> o.get("spu_id", String.class)).collect(Collectors.toList());
                }
            }
        }
        return spuIds;
    }

    private String handlePromotionTabFilter() {
        if (null == arg.getObjectData() || !Objects.equals(true, arg.getObjectData().get(ProductConstants.TAB_PROMOTION))) {
            return "";
        }
        if (Strings.isNullOrEmpty(accountId) || Strings.isNullOrEmpty(masterObjectApiName)
                || !bizConfigThreadLocalCacheService.isOpenPricePolicy(controllerContext.getTenantId(),masterObjectApiName)) {
            return NONE_FILTER_SQL;
        }
        List<String> pricePolicyIds = pricePolicyCoreService.GetPricePolicyIdsByAccountId(controllerContext.getUser(), masterObjectApiName, accountId);
        if (CollectionUtils.isEmpty(pricePolicyIds)) {
            return NONE_FILTER_SQL;
        }
        Set<String> productIds = getPolicyProducts(controllerContext.getUser(), pricePolicyIds);
        if (CollectionUtils.isEmpty(productIds)) {
            return NONE_FILTER_SQL;
        }
        return String.format(" and k.id=any (array ['%s']) ", Joiner.on("','").join(productIds));
    }

    private Set<String> getPolicyProducts(User user, List<String> pricePolicyIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(0);
        List<IFilter> filterList = Lists.newArrayList();
        filterList.add(buildFilter(PricePolicyConstants.PRICE_POLICY_ID, pricePolicyIds, Operator.IN));
        filterList.add(buildFilter(ObjectData.IS_DELETED, Lists.newArrayList("0"), Operator.EQ));
        query.setFilters(filterList);
        QueryResult<IObjectData> queryResult = metaDataFindServiceExt.findBySearchQueryWithFields(user,
                SFAPreDefine.PricePolicyProduct.getApiName(), query, Lists.newArrayList(PricePolicyConstants.PRODUCT_ID), true);
        if (CollectionUtils.isEmpty(queryResult.getData())) {
            return Sets.newHashSet();
        }
        return queryResult.getData().stream().map(x -> x.get(PricePolicyConstants.PRODUCT_ID, String.class)).collect(Collectors.toSet());
    }

    private String handleRecentlyTabFilter() {
        if (null == arg.getObjectData() || !Objects.equals(true, arg.getObjectData().get(ProductConstants.TAB_RECENTLY_ORDERED))) {
            return "";
        }
        if (Strings.isNullOrEmpty(accountId)) {
            return NONE_FILTER_SQL;
        }
        long createTime = System.currentTimeMillis() - THIRTY_DAYS_IN_MILLION_SECONDS;
        String sql = ConcatenateSqlUtils.getRecentlyOrderedSpu(controllerContext.getTenantId(), accountId, createTime);
        List<Map> rst = null;
        try {
            rst = objectDataService.findBySql(controllerContext.getTenantId(), sql);
        } catch (MetadataServiceException e) {
            log.error("handleRecentlyOrderedTab error. sql:{} ", sql, e);
        }
        if (CollectionUtils.isEmpty(rst)) {
            return NONE_FILTER_SQL;
        }
        spuIdList = Lists.newArrayListWithExpectedSize(rst.size());
        for (Map map : rst) {
            spuIdList.add(map.getOrDefault("spu_id", "").toString());
        }
        return String.format(" and SPUObj.id=any (array ['%s']) ", Joiner.on("','").join(spuIdList));
    }

    private IFilter buildFilter(String fieldName, List<String> fieldValues, Operator operator) {
        IFilter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setFieldValues(fieldValues);
        filter.setOperator(operator);
        return filter;
    }

    private void reorder(Result realResult) {
        if (null == realResult || CollectionUtils.isEmpty(realResult.getDataList()) || CollectionUtils.isEmpty(spuIdList)) {
            return;
        }
        realResult.getDataList().sort((x, y) ->
                spuIdList.indexOf(x.getId()) == spuIdList.indexOf(y.getId()) ? 0
                        : spuIdList.indexOf(x.getId()) > spuIdList.indexOf(y.getId()) ? 1 : -1);
    }

    private boolean simplifyCount() {
        return isDht && GrayUtil.simplifySpuPaging(controllerContext.getTenantId());
    }
}
