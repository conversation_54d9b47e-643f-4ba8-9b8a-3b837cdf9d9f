package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.model.QueryOrderModeModel;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.pricepolicy.BlackMdEaConfig;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crm.sfa.utilities.proxy.SalesOrderBizProxy;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.DescribeDetailResult;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.appframework.metadata.dto.RecordTypeLayoutStructure;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_ADD;
import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_EDIT;

/**
 * Created by lilei on 2017/11/9.
 */

@Slf4j
public class SalesOrderDescribeLayoutController extends SFADescribeLayoutController {

    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private static final SalesOrderBizProxy salesOrderBizProxy = SpringUtil.getContext().getBean(SalesOrderBizProxy.class);
    private static final String SHIP_TO_PARTY = "ship_to_party";

    private boolean newInvoice;
    private boolean openCPQ;
    private boolean haveTemporaryAddAuth = false;
    private List<String> removeFields = Lists.newArrayList();
    private List<String> settleFilterFields = Lists.newArrayList("coupon", "rebate", "pricePolicy");


    public SalesOrderDescribeLayoutController() {
        super();
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        initOpenStatus();

        // 特殊逻辑：灰度报价单转订单功能，人员又报价单转订单的权限，没有订单新建的权限，
        // 特殊处理用-10000 请求从对象描述，跳过权限校验
        /**
         * 特殊逻辑
         *  1 灰度报价单转订单功能
         *  2 人员有报价单转订单的权限
         *  3 人员没有订单新建的权限
         *  4 -10000 请求描述，跳过权限
         */
        if (SoCommonUtils.quoteConvertOrderWithOutAddAuth(controllerContext.getTenantId())) {
            Map<String, Boolean> orderAddAuth = serviceFacade.funPrivilegeCheck(controllerContext.getUser(),
                    Utils.SALES_ORDER_API_NAME, Lists.newArrayList("Add"));
            Map<String, Boolean> quoteConvertOrderAuth = serviceFacade.funPrivilegeCheck(controllerContext.getUser(),
                    Utils.QUOTE_API_NAME, Lists.newArrayList("button_quoteobj2salesorderobj__c"));
            haveTemporaryAddAuth = quoteConvertOrderAuth.get("button_quoteobj2salesorderobj__c") && !orderAddAuth.get("Add");
        }
    }

    private void initOpenStatus() {
        newInvoice = bizConfigThreadLocalCacheService.isNewInvoice(controllerContext.getTenantId());
        openCPQ = bizConfigThreadLocalCacheService.isCPQEnabled(controllerContext.getTenantId());
    }


    private void needRemoveDetailFields() {
        if (newInvoice) {
            removeFields.addAll(SalesOrderConstants.newInvoiceSalesOrderProductFields);
        }

        if (openCPQ) {
            removeFields.addAll(BomConstants.ORDER_QUOTE_REMOVE_BOM_FIELDS);
        }
    }


    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (arg.getLayout_type() == null) {
            return;
        }
        List<String> readonlyFieldNames = getMasterReadOnlyFields();
        switch (arg.getLayout_type()) {
            case LAYOUT_TYPE_EDIT:
                readonlyFieldNames.add("account_id");
                PreDefLayoutUtil.removeSomeFields(formComponent, SalesOrderConstants.newInvoiceSalesOrderFields);
                PreDefLayoutUtil.removeSomeFields(formComponent, SalesOrderConstants.newArSalesOrderFields);
                break;
            case LAYOUT_TYPE_ADD:
                PreDefLayoutUtil.removeSomeFields(formComponent, SalesOrderConstants.newInvoiceSalesOrderFields);
                PreDefLayoutUtil.removeSomeFields(formComponent, SalesOrderConstants.newArSalesOrderFields);
                if (haveTemporaryAddAuth) {
                    if (Objects.nonNull(result.getLayout())) {
                        ILayout layout = new Layout(result.getLayout());
                        List<IButton> buttons = Lists.newArrayList();
                        buttons.add(PreDefLayoutUtil.createButton(ObjectAction.CREATE_SAVE));
                        if (supportSaveDraft()) {
                            buttons.add(PreDefLayoutUtil.createButton(ObjectAction.CREATE_SAVE_DRAFT));
                        }
                        layout.setButtons(buttons);
                    }
                }
                break;
        }
        PreDefLayoutUtil.setFormComponentFieldsReadOnly(formComponent, readonlyFieldNames);
        needRemoveDetailFields();
        result.setDetailObjectList(removeDetailFields(removeFields));
        addManualButton(result);
        handleLayoutForStock(result); // 下面那个逻辑，会把formComponent 变成从对象的，所以放在之前执行
        //将三个虚拟字段设置成只读
        setDetailFieldsReadonly(BomConstants.ORDER_QUOTE_READONLY_BOM_FIELDS);
        dealOrderSettlement(result, settleFilterFields, Utils.SALES_ORDER_API_NAME);
    }

    /**
     * 只处理开启库存企业
     */
    private void handleLayoutForStock(Result result) {
        // 布局错误忽略，不影响流程
        try {
            String tenantId = controllerContext.getTenantId();
            if (!bizConfigThreadLocalCacheService.isStockEnabled(tenantId)) {
                return;
            }
            // 大部分企业不会开启多仓库订货，所以订单产品描述中，没有订货仓库字段，就没必要处理多仓库逻辑，可以减少很多无效的rpc调用
            if (!needHandleDetailLayoutForMultiWarehouse()) {
                return;
            }
            LayoutDocument layoutDoc = result.getLayout();
            if (Objects.isNull(layoutDoc)) {
                return;
            }
            ILayout layout = new Layout(layoutDoc);
            if (com.facishare.paas.appframework.common.util.CollectionUtils.empty(layout.getComponents())) {
                return;
            }
            LayoutExt layoutExt = LayoutExt.of(layout);
            Optional<FormComponentExt> formComponentOption = layoutExt.getFormComponent();
            if (!formComponentOption.isPresent()) {
                return;
            }
            FormComponentExt formComponentExt = formComponentOption.get();
            IFormComponent formComponent = formComponentExt.getFormComponent();

            User user = controllerContext.getUser();
            QueryOrderModeModel.Arg queryOrderModelArg = new QueryOrderModeModel.Arg();
            String recordType = arg.getRecordType_apiName();
            queryOrderModelArg.setRecordType(recordType);
            QueryOrderModeModel.Result orderRelatedMode = salesOrderBizProxy.findOrderRelatedMode(queryOrderModelArg, SFARestHeaderUtil.getCrmHeader(tenantId, user));
            QueryOrderModeModel.ResultData resultData = orderRelatedMode.getData();
            if (Objects.isNull(resultData)) {
                return;
            }
            // 方便库存那边做灰度控制，如果返回空，就不处理布局，可以用作处理特殊情况
            if (StringUtils.isEmpty(resultData.getOrderMode())) {
                return;
            }
            //多仓库订货则移除主对象订货仓库，非多仓库订货则移除从对象多仓库字段, 新建编辑页逻辑一直，不考虑生命状态
            if (resultData.isMultiWarehouseMode()) {
                PreDefLayoutUtil.removeSomeFields((FormComponent) formComponent, Sets.newHashSet("shipping_warehouse_id"));
            } else {
                removeDetailFields(Lists.newArrayList("shipping_warehouse_id"));
            }
        } catch (Exception e) {
            log.warn("handleLayoutForStock fail", e);
        }
    }

    private boolean needHandleDetailLayoutForMultiWarehouse() {
        if (!arg.getInclude_detail_describe()) {
            return false;
        }
        List<DetailObjectListResult> detailObjectList = result.getDetailObjectList();
        if (CollectionUtils.isEmpty(detailObjectList)) {
            return false;
        }
        for (DetailObjectListResult detail : detailObjectList) {
            if (!StringUtils.equals(detail.getObjectApiName(), SystemConstants.SalesOrderProductApiName)) {
                continue;
            }
            Map objectDescribe = detail.getObjectDescribe();
            if (Objects.isNull(objectDescribe)) {
                return false;
            }
            ObjectDescribeExt of = ObjectDescribeExt.of(objectDescribe);
            return of.containsField("shipping_warehouse_id");
        }
        return false;

    }

    private void addManualButton(Result result) {
        if (CollectionUtils.isEmpty(result.getDetailObjectList()) || null == result.getLayout() || !bizConfigThreadLocalCacheService.isOpenManualGift(controllerContext.getTenantId())) {
            return;
        }
        ILayout ilayout = new Layout(result.getLayout());
        List<IComponent> lComponents = null;
        try {
            lComponents = ilayout.getComponents();
        } catch (MetadataServiceException e) {
            log.warn("addManualButton ", e);
        }
        if (CollectionUtils.isEmpty(lComponents)) {
            return;
        }

        for (IComponent component : lComponents) {
            if ("SalesOrderProductObj_md_group_component".equals(component.getName())) {
                List<Map> buttonInfos = (List<Map>) component.get("button_info");
                for (Map buttonInfo : buttonInfos) {
                    if (("list_normal").equals(String.valueOf((buttonInfo).get("render_type")))) {
                        if (((List) (buttonInfo).get("hidden")).contains("Batch_Lookup_Add_button_manual_gift")) {
                            // 布局隐藏了按钮
                            return;
                        }
                    }
                }
                break;
            }
        }

        for (DetailObjectListResult detailObject : result.getDetailObjectList()) {
            if (Utils.SALES_ORDER_PRODUCT_API_NAME.equals(detailObject.getObjectApiName())) {
                List<RecordTypeLayoutStructure> layouts = detailObject.getLayoutList();
                if (!CollectionUtils.isEmpty(layouts)) {
                    for (RecordTypeLayoutStructure layout : layouts) {
                        List buttons = (List) layout.getDetail_layout().get("buttons");
                        if (!CollectionUtils.isEmpty(buttons)) {
                            IButton batchAddButton = ObjectAction.BATCH_LOOKUP_CREATE.createButton();
                            batchAddButton.setName("Batch_Lookup_Add_button_manual_gift");
                            batchAddButton.set("lookup_field_name", "field_manual_gifts");
                            batchAddButton.setLabel(I18N.text("sfa.manual.gift.button.name"));
                            JSONObject button = (JSONObject) JSONObject.toJSON(batchAddButton);
                            buttons.add(button.get("containerDocument"));
                        }
                    }
                }
                break;
            }

        }
    }

    @Override
    protected void promptUpgrade(Arg arg, Result result) {
        super.promptUpgrade(arg, result);
        if (VersionUtil.isVersionEarlierEqualThan715(controllerContext.getRequestContext())) {
            throw new ValidateException(I18N.text("sfa.CommonUtil.358.1"));
        }
        if (bizConfigThreadLocalCacheService.isOpenPricePolicySalesOrderObj(controllerContext.getTenantId()) &&
                VersionUtil.isVersionEarlierEqualThan760(controllerContext.getRequestContext())) {
            throw new ValidateException(I18N.text("sfa.CommonUtil.358.1"));
        }
    }

    @Override
    protected boolean supportSaveDraft() {
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_740)
                || bizConfigThreadLocalCacheService.isPromotionEnabled(controllerContext.getTenantId())) {
            return false;
        }
        return true;
    }

    @Override
    protected boolean supportSaveAndCreate() {
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_740)) {
            return false;
        }
        return true;
    }

    @Override
    protected DescribeDetailResult findDescribeDetailResult() {
        DescribeDetailResult result = super.findDescribeDetailResult();
        return result;
    }

    @Override
    protected List<IObjectDescribe> getDetailDescribes(IObjectDescribe masterDescribe) {
        if (haveTemporaryAddAuth) {
            List<IObjectDescribe> detailDescribes = this.serviceFacade.findDetailDescribes(masterDescribe.getTenantId(), masterDescribe.getApiName());
            detailDescribes = (List) detailDescribes.stream().filter((x) -> {
                return x.isActive();
            }).collect(Collectors.toList());
            return detailDescribes;
        }
        return super.getDetailDescribes(masterDescribe);
    }

    protected DescribeDetailResult findSpecialDescribeDetailResult() {
        User user = new User(controllerContext.getTenantId(), "-10000");
        DescribeDetailResult describeDetailResult = serviceFacade.findDescribeByApiName(
                user,
                arg.getApiname(),
                arg.getInclude_layout(),
                arg.getLayout_type(),
                arg.getRecordType_apiName(),
                arg.getInclude_ref_describe(),
                arg.getInclude_detail_describe(),
                false,
                objectData
        );
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describeDetailResult.getObjectDescribe());
        describe = describeExt.getObjectDescribe();
        //清空从对象布局中的events
        if (describeExt.isCreateWithMaster()) {
            describeDetailResult.getLayout().remove(Layout.EVENTS);
        }
        // 替换邮箱字段的 pattern
        describeExt.handelEmailFields();
        return describeDetailResult;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (LAYOUT_TYPE_ADD.equals(arg.getLayout_type()) || LAYOUT_TYPE_EDIT.equals(arg.getLayout_type())) {
            handleReadOnlyFieldsForDetailLayout(newResult, SalesOrderConstants.SalesOrderProductField.ORDER_ID.getApiName());
        }

        removeFieldsIfHasDhtOrderDeliveryAddress(newResult);

        if (isFlowEditLayout()) {
            removeDetailButtons(newResult, SFAPreDefineObject.SalesOrderProduct.getApiName());
        }

        return newResult;
    }

    @Override
    protected List<String> getReadOnlyFields(IObjectDescribe detailDescribe) {
        List<String> readOnlyFields = super.getReadOnlyFields(detailDescribe);
        readOnlyFields.addAll(SalesOrderConstants.SALES_ORDER_PRODUCT_READONLY_FIELDS);
        boolean md20Black = BlackMdEaConfig.isEiBlackOrIndirect(Utils.SALES_ORDER_API_NAME, controllerContext.getTenantId(), controllerContext.getEa());
        boolean openReadOnly = false;
        if (bizConfigThreadLocalCacheService.isOpenPricePolicySalesOrderObj(controllerContext.getTenantId())) {
            readOnlyFields.addAll(Lists.newArrayList(
                    SalesOrderConstants.SalesOrderProductField.PRODUCT_PRICE.getApiName(),
                    SalesOrderConstants.SalesOrderProductField.IS_GIVEAWAY.getApiName()));
            openReadOnly = true;
        }
        if (bizConfigThreadLocalCacheService.isOpenCoupon(controllerContext.getTenantId()) || bizConfigThreadLocalCacheService.isOpenRebate(controllerContext.getTenantId())) {
            readOnlyFields.addAll(Lists.newArrayList(
                    SalesOrderConstants.SalesOrderProductField.PRODUCT_PRICE.getApiName(),
                    CouponConstants.PluginDetailField.REBATE_AMORTIZE_AMOUNT,
                    CouponConstants.PluginDetailField.REBATE_DYNAMIC_AMOUNT,
                    CouponConstants.PluginDetailField.COUPON_AMORTIZE_AMOUNT,
                    CouponConstants.PluginDetailField.COUPON_DYNAMIC_AMOUNT,
                    SalesOrderConstants.SalesOrderProductField.IS_GIVEAWAY.getApiName())
            );
            openReadOnly = true;
        }
        if (md20Black && openReadOnly) {
            readOnlyFields.addAll(Lists.newArrayList(SalesOrderConstants.SalesOrderProductField.SALES_PRICE.getApiName(),
                    SalesOrderConstants.SalesOrderProductField.DISCOUNT.getApiName(),
                    SalesOrderConstants.SalesOrderProductField.SUBTOTAL.getApiName()));
        }

        if (bizConfigThreadLocalCacheService.isCPQEnabled(controllerContext.getTenantId())) {
            readOnlyFields.addAll(Lists.newArrayList(
                    SalesOrderConstants.SalesOrderProductField.NODE_PRICE.getApiName(),
                    SalesOrderConstants.SalesOrderProductField.NODE_SUBTOTAL.getApiName(),
                    SalesOrderConstants.SalesOrderProductField.SHARE_RATE.getApiName()));
            readOnlyFields.addAll(BomConstants.FILTER_READ_ONlY_FIELD);
        }
        if (bizConfigThreadLocalCacheService.isOpenManualGift(controllerContext.getTenantId())) {
            readOnlyFields.add(SalesOrderConstants.SalesOrderProductField.GIFT_TYPE.getApiName());
        }
        return readOnlyFields;
    }

    private List<DetailObjectListResult> removeDetailFields(List<String> removeFields) {
        List<DetailObjectListResult> detailObjectList = Lists.newArrayList();
        if (arg.getInclude_detail_describe() && (detailObjectList = result.getDetailObjectList()) != null) {
            return OrderUtils.removeDetailFields(removeFields, detailObjectList);
        }
        return detailObjectList;
    }

    private void setDetailFieldsReadonly(Set<String> fieldsSet) {
        if (arg.getInclude_detail_describe()) {
            List<DetailObjectListResult> detailObjectList = result.getDetailObjectList();
            if (CollectionUtils.isEmpty(detailObjectList) || CollectionUtils.isEmpty(fieldsSet)) {
                return;
            }
            detailObjectList.forEach(result -> {
                List<RecordTypeLayoutStructure> layoutList = result.getLayoutList();
                if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(layoutList)) {
                    layoutList.forEach(layout -> {
                        Map detailLayout = layout.getDetail_layout();
                        LayoutExt layoutExt = LayoutExt.of(detailLayout);
                        formComponent = (FormComponent) layoutExt.getFormComponent().get().getFormComponent();
                        List<IFieldSection> fieldSectionsResult = Lists.newArrayList();
                        for (IFieldSection fieldSection : formComponent.getFieldSections()) {
                            List<IFormField> fields = fieldSection.getFields();
                            for (IFormField formField : fieldSection.getFields()) {
                                if (fieldsSet.contains(formField.getFieldName())) {
                                    formField.setReadOnly(true);
                                }
                            }
                            fieldSection.setFields(fields);
                            fieldSectionsResult.add(fieldSection);
                        }
                        formComponent.setFieldSections(fieldSectionsResult);
                    });
                }
            });
        }
    }

    private List<String> getMasterReadOnlyFields() {
        List<String> readOnlyFields = Lists.newArrayList();
        readOnlyFields.addAll(SalesOrderConstants.SALES_ORDER_READONLY_FIELDS);
        readOnlyFields.add(CouponConstants.PluginField.COUPON_AMOUNT);
        readOnlyFields.add(CouponConstants.PluginField.REBATE_AMOUNT);
        if ((bizConfigThreadLocalCacheService.isOpenPricePolicySalesOrderObj(controllerContext.getTenantId())
                || bizConfigThreadLocalCacheService.isOpenCoupon(controllerContext.getTenantId())
                || bizConfigThreadLocalCacheService.isOpenRebate(controllerContext.getTenantId()))
                && BlackMdEaConfig.isEiBlackOrIndirect(Utils.SALES_ORDER_API_NAME, controllerContext.getTenantId(), controllerContext.getEa())) {
            readOnlyFields.addAll(Lists.newArrayList(SalesOrderConstants.SalesOrderField.ORDER_AMOUNT.getApiName(),
                    SalesOrderConstants.SalesOrderField.DISCOUNT.getApiName()));
        }
        return readOnlyFields;
    }

    /**
     * 布局中如果有 收货地址 组件，则表单布局中去掉三个（ship_to_add、ship_to_tel、ship_to_id）
     */
    private void removeFieldsIfHasDhtOrderDeliveryAddress(Result result) {
        if (!hasDhtOrderDeliveryAddress(result)) {
            return;
        }

        List<String> removeFieldApiNames = Lists.newArrayList("ship_to_add", "ship_to_tel", "ship_to_id");

        Layout layout = new Layout(result.getLayout());
        try {
            List<IComponent> components = layout.getComponents();
            if (CollectionUtils.isEmpty(components)) {
                return;
            }

            for (IComponent component : components) {
                if (Objects.equals(component.getName(), "form_component")) {

                    FormComponent fc = (FormComponent) component;
                    List<IFieldSection> fields = fc.getFieldSections();
                    List<IFieldSection> newFields = Lists.newArrayList();

                    for (IFieldSection fieldSection : fields) {
                        boolean hasRemove = false;
                        List<IFormField> formFields = fieldSection.getFields();
                        Iterator iterator = formFields.iterator();
                        while (iterator.hasNext()) {
                            IFormField formField = (IFormField) iterator.next();
                            if (removeFieldApiNames.contains(formField.getFieldName())) {
                                iterator.remove();
                                hasRemove = true;
                            }
                        }
                        if (hasRemove) {
                            fieldSection.setFields(formFields);
                        }

                        /**
                         * 用户自建的，只有这3个字段，删完"form_fields": []是空的，把show_header设为FALSE 没效果，所以删了
                         {
                         "show_header": true,
                         "form_fields": [],
                         "api_name": "group_2Ns29__c",
                         "tab_index": "ltr",
                         "column": 2,
                         "header": "收货信息"
                         },
                         */
                        if (!CollectionUtils.isEmpty(formFields) || Objects.equals(fieldSection.getName(), "base_field_section__c")) {
                            newFields.add(fieldSection);
                        }
                    }
                    fc.setFieldSections(newFields);

                    return;
                }
            }
        } catch (MetadataServiceException e) {
            log.warn("removeFieldsIfHasDhtOrderDeliveryAddress ", e);
        }
    }

    /**
     * 是否有收货地址组件
     */
    private boolean hasDhtOrderDeliveryAddress(Result result) {
        Layout layout = new Layout(result.getLayout());
        try {
            List<IComponent> components = layout.getComponents();
            if (CollectionUtils.isEmpty(components)) {
                return false;
            }

            for (IComponent component : components) {
                if (Objects.equals(component.getName(), "dht_order_delivery_address")) {
                    return true;
                }
            }
        } catch (MetadataServiceException e) {
            log.warn("hasDhtOrderDeliveryAddress ", e);
        }

        return false;
    }
}