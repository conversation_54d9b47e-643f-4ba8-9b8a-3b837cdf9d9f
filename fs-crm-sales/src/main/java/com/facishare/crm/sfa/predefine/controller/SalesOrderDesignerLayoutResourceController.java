package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardDesignerLayoutResourceController;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;


public class SalesOrderDesignerLayoutResourceController extends StandardDesignerLayoutResourceController {
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    //加工主从一起新建编辑页从对象的通用按钮
    @Override
    protected List<IButton> processDetailObjectButtons(List<IButton> detailObjButtons, IObjectDescribe detailDescribe, IObjectDescribe masterDescribe) {

        if (Utils.SALES_ORDER_PRODUCT_API_NAME.equals(detailDescribe.getApiName()) && bizConfigThreadLocalCacheService.isOpenManualGift(controllerContext.getTenantId())) {
            IButton batchAddButton = ObjectAction.BATCH_LOOKUP_CREATE.createButton();
            batchAddButton.setName("Batch_Lookup_Add_button_manual_gift");
            batchAddButton.setLabel(I18N.text("sfa.manual.gift.button.name"));
            detailObjButtons.add(batchAddButton);
        }

        return detailObjButtons;
    }

}
