package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.QuoterModel;
import com.facishare.crm.sfa.predefine.service.attribute.model.Attribute;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeValue;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants;
import com.facishare.crm.sfa.utilities.constant.dmConstants.DmDefineConstants;
import com.facishare.crm.sfa.utilities.util.AttributeUtils;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.util.DomainPluginDescribeExt;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.expression.Expression;
import com.facishare.paas.appframework.metadata.expression.ExpressionFactory;
import com.facishare.paas.appframework.metadata.expression.ExpressionVariableFactory;
import com.facishare.paas.appframework.metadata.expression.ExtVariableType;
import com.facishare.paas.appframework.metadata.expression.SimpleExpression;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.MasterDetail;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdvancedFormulaServiceImpl implements AdvancedFormulaService {
    private static final String API_NAME = "AdvancedFormulaObj";
    private static final String PRICE_PER_SET = "price_per_set";
    @Autowired
    private FieldRelationCalculateService fieldRelationCalculateService;
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private AdvancedFormulaCalculateService advancedFormulaCalculateService;
    @Autowired
    private EnterpriseInitService enterpriseInitService;
    @Autowired
    private RedissonService redissonService;
    private static final String REF_OBJECT_API_NAME = "ref_object_api_name";
    private static final String REF_FIELD_NAME = "ref_field_name";

    private static final String ADF_BOM_ID = "{\"is_index\":true,\"is_active\":true,\"is_unique\":false,\"label\":\"产品选配明细编号\",\"target_api_name\":\"BOMObj\",\"type\":\"object_reference\",\"target_related_list_name\":\"advanced_formula_related_list_bom_id\",\"is_abstract\":null,\"target_related_list_label\":\"产品选配明细\",\"action_on_target_delete\":\"cascade_delete\",\"is_required\":false,\"wheres\":[],\"api_name\":\"bom_id\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"help_text\":\"\",\"status\":\"released\"}";// ignoreI18n

    @Override
    public void validateByField(IObjectDescribe describe, IFieldDescribe fieldDescribe) {
        JSONObject exprObject = JSON.parseObject(fieldDescribe.getDefaultValue() + "");
        SimpleExpression simpleExpression = SimpleExpression.builder()
                .id(fieldDescribe.getApiName())
                .expression(exprObject.getString("expression"))
                .returnType(fieldDescribe.getType())
                .nullAsZero(exprObject.getBooleanValue("default_to_zero"))
                .decimalPlaces(exprObject.getIntValue("decimal_places"))
                .build();
        //4、构造Expression并解析自定义变量
        Expression expression = ExpressionFactory.createExpression(describe, simpleExpression);
        List<ExpressionVariableFactory.ExtVariable> attrVariables = expression.getExtVariablesByType(ExtVariableType.ATTRIBUTE.getCode());
        List<ExpressionVariableFactory.ExtVariable> nonAttrVariables = expression.getExtVariablesByType(ExtVariableType.NON_ATTRIBUTE.getCode());
        List<SimpleExpression.VariableInfo> extVariableInfos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(attrVariables)) {
            List<SimpleExpression.VariableInfo> attrExtVariableInfos = attrVariables.stream().map(d -> SimpleExpression.VariableInfo.of(d.getName(), IFieldType.TEXT)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(attrExtVariableInfos)) {
                extVariableInfos.addAll(attrExtVariableInfos);
            }
        }
        if (CollectionUtils.isNotEmpty(nonAttrVariables)) {
            List<SimpleExpression.VariableInfo> nonExtVariableInfos = nonAttrVariables.stream().map(d -> SimpleExpression.VariableInfo.of(d.getName(), exprObject.getString("return_type"))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(nonExtVariableInfos)) {
                extVariableInfos.addAll(nonExtVariableInfos);
            }
        }
        fieldDescribe.setDefaultValue(exprObject.getString("expression"));
        fieldRelationCalculateService.validateByFields(describe, Lists.newArrayList(fieldDescribe), true, extVariableInfos);
    }

    @Override
    public List<ObjectDescribeDocument> domainList(User user, String pluginApiName) {
        List<ObjectDescribeDocument> list = new ArrayList<>();
        List<DomainPluginInstance> domainPluginInstances = queryDomain(user, pluginApiName);
        List<DomainPluginInstance> periodProductInstances = queryDomain(user, DmDefineConstants.PERIOD_PRODUCT);

        if (CollectionUtils.isEmpty(domainPluginInstances)) {
            return list;
        }
        Map<String, Map<String, String>> periodProductMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(periodProductInstances)) {
            periodProductInstances.forEach(x -> com.facishare.paas.appframework.common.util.CollectionUtils.nullToEmpty(x.getPluginParam().getDetails()).forEach(d -> {
                periodProductMap.put(d.getObjectApiName(), d.getFieldMapping());
            }));
        }
        Map<String, List<String>> removeMap = Maps.newHashMap();
        Set<String> apiNames = Sets.newHashSet();
        Map<String, String> apiMap = Maps.newHashMap();
        domainPluginInstances.forEach(pluginInstance -> com.facishare.paas.appframework.common.util.CollectionUtils.nullToEmpty(pluginInstance.getPluginParam().getDetails()).forEach(x -> {
            apiNames.add(x.getObjectApiName());
            apiMap.put(x.getObjectApiName(), pluginInstance.getRefObjectApiName());
            removeMap.computeIfAbsent(x.getObjectApiName(), k -> new ArrayList<>()).add("order_by");
            removeMap.computeIfAbsent(x.getObjectApiName(), k -> new ArrayList<>()).add("version");
            if (Objects.equals(x.getObjectApiName(), Utils.SALES_ORDER_PRODUCT_API_NAME)) {
                removeMap.computeIfAbsent(x.getObjectApiName(), k -> new ArrayList<>()).add("close_amount");
                removeMap.computeIfAbsent(x.getObjectApiName(), k -> new ArrayList<>()).add("close_quantity");
            }
        }));

        if (CollectionUtils.isEmpty(apiNames)) {
            return list;
        }
        List<IObjectDescribe> detailDescribes = serviceFacade.findObjectList(user.getTenantId(), apiNames);
        if (CollectionUtils.isEmpty(detailDescribes)) {
            return list;
        }
        for (IObjectDescribe iObjectDescribe : detailDescribes) {
            IObjectDescribe copyDescribe = ObjectDescribeExt.of(iObjectDescribe).copyOnWrite();
            List<String> removeFields = removeMap.getOrDefault(copyDescribe.getApiName(), Lists.newArrayList());
            Map<String, String> map = periodProductMap.getOrDefault(iObjectDescribe.getApiName(), Maps.newHashMap());
            String pricePerSet = map.get(PRICE_PER_SET);
            if (StringUtils.isNotBlank(pricePerSet)) {
                removeFields.add(pricePerSet);
            }
            copyDescribe.set("removeFields", removeFields);
            copyDescribe.set("masterObjectApiName", apiMap.get(copyDescribe.getApiName()));
            ObjectDescribeDocument of = ObjectDescribeDocument.of(copyDescribe);
            list.add(of);
        }
        return list;
    }

    private List<DomainPluginInstance> queryDomain(User user, String pluginApiName) {
        if (StringUtils.isBlank(pluginApiName)) {
            return Collections.emptyList();
        }
        Query query = Query.builder().needReturnCountNum(false).needReturnQuote(false).build();
        query.and(FilterExt.of(Operator.EQ, "plugin_api_name", pluginApiName).getFilter());
        query.and(FilterExt.of(Operator.EQ, DBRecord.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter());
        query.and(FilterExt.of(Operator.EQ, DomainPluginInstance.IS_ACTIVE, String.valueOf(Boolean.TRUE)).getFilter());
        return Optional.ofNullable(infraServiceFacade.findPluginInstanceByQuery(user, query)).map(QueryResult::getData).orElse(Lists.newArrayList());
    }

    @Override
    public List<ObjectDataDocument> formulaCalculate(QuoterModel.CalculateDataParam arg, ServiceContext serviceContext) {
        if (StringUtils.isNotBlank(arg.getSeriesId())) {
            String lockKey = buildSeriesKey(arg.getSeriesId(), serviceContext.getTenantId());
            RLock lock = redissonService.tryLockWithErrorMsg(0, 30, TimeUnit.SECONDS, lockKey,
                    I18NExt.text(I18NKey.PREVIOUS_REQUEST_PROCESSING_ALERT), AppFrameworkErrorCode.PREVIOUS_REQUEST_PROCESSING_ALERT.getCode());
            try {
                return advancedFormulaCalculateService.formulaCalculate(arg, serviceContext);
            } finally {
                redissonService.unlock(lock);
            }
        }
        return advancedFormulaCalculateService.formulaCalculate(arg, serviceContext);
    }

    private String buildSeriesKey(String seriesId, String tenantId) {
        return "series|" + seriesId + "|" + tenantId;
    }

    @Override
    public Map<String, Map<String, Map<String, CalculateRelation>>> queryAdvancedFormulaList(User user, QuoterModel.Arg arg) {
        Map<String, Map<String, Map<String, CalculateRelation>>> resultMap = Maps.newHashMap();
        List<IObjectData> dataList = queryFormulaList(user, arg);
        if (CollectionUtils.isEmpty(dataList)) {
            return resultMap;
        }
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), arg.getApiName());
        if (describe == null) {
            return resultMap;
        }
        IObjectDescribe masterDescribe = null;
        Optional<String> mdFieldOptional = ObjectDescribeExt.of(describe).getMasterDetailField().map(MasterDetail::getTargetApiName);
        if (mdFieldOptional.isPresent()) {
            masterDescribe = serviceFacade.findObject(user.getTenantId(), mdFieldOptional.get());
        }
        Map<String, List<IObjectData>> dataMap = Maps.newHashMap();
        Map<String, List<String>> dataFieldMap = Maps.newHashMap();
        dataList.forEach(x -> {
            String dataId = MoreObjects.firstNonNull(x.get(QuoterModel.AdvancedFormulaModel.BOM_ID, String.class), x.get(QuoterModel.AdvancedFormulaModel.PRODUCT_ID, String.class));
            dataMap.computeIfAbsent(dataId, k -> new ArrayList<>()).add(x);
            dataFieldMap.computeIfAbsent(dataId, k -> new ArrayList<>()).add(x.get(QuoterModel.AdvancedFormulaModel.FIELD_NAME, String.class));
        });
        Pattern pattern = Pattern.compile("\\$(.*?)\\$");
        IObjectDescribe finalMasterDescribe = masterDescribe;
        dataMap.forEach((dataId, objectDataList) -> {
            List<IFieldDescribe> masterFields = Lists.newArrayList();
            IObjectDescribe objectDescribe = ObjectDescribeExt.of(describe).copy();
            IObjectDescribe masterDescribeCopy = ObjectDescribeExt.of(finalMasterDescribe).copy();
            resultMap.putIfAbsent(dataId, Maps.newHashMap());
            resultMap.get(dataId).putIfAbsent(objectDescribe.getApiName(), Maps.newHashMap());
            resultMap.get(dataId).putIfAbsent(masterDescribeCopy.getApiName(), Maps.newHashMap());
            List<IFieldDescribe> newFieldDescribe = Lists.newArrayList();
            IFieldDescribe masterDetailFieldDescribe = null;
            Set<String> filterIds = Sets.newHashSet();
            for (IObjectData iObjectData : objectDataList) {
                String fieldName = iObjectData.get(QuoterModel.AdvancedFormulaModel.FIELD_NAME, String.class);
                String formula = iObjectData.get(QuoterModel.AdvancedFormulaModel.FORMULA, String.class);
                IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
                if (Objects.nonNull(fieldDescribe)) {
                    if (Objects.equals(IFieldType.FORMULA, fieldDescribe.getType())) {
                        fieldDescribe.setExpression(formula);
                    } else {
                        if (StringUtils.isNotBlank(formula)) {
                            JSONObject jsonObject = JSON.parseObject(formula);
                            fieldDescribe.setDefaultValue(jsonObject.getString("expression"));
                        } else {
                            fieldDescribe.setDefaultValue(formula);
                        }
                        fieldDescribe.setDefaultIsExpression(true);
                    }
                    if (filterIds.add(fieldDescribe.getId())) {
                        newFieldDescribe.add(fieldDescribe);
                    }
                    if (StringUtils.isNotBlank(formula)) {
                        boolean flag = true ;
                        if (formula.contains("#ATTR#")) {
                            CalculateRelation calculateRelation = resultMap.get(dataId).get(objectDescribe.getApiName()).getOrDefault("ATTR", new CalculateRelation());
                            Map<String, Set<String>> cMap = calculateRelation.getCalculateFields();
                            cMap.computeIfAbsent(objectDescribe.getApiName(), var -> new HashSet<>()).add(fieldName);
                            resultMap.get(dataId).get(objectDescribe.getApiName()).put("ATTR", calculateRelation);
                            flag = false;
                        }
                        if (formula.contains("#NON_ATTR#")) {
                            CalculateRelation calculateRelation = resultMap.get(dataId).get(objectDescribe.getApiName()).getOrDefault("NON_ATTR", new CalculateRelation());
                            Map<String, Set<String>> cMap = calculateRelation.getCalculateFields();
                            cMap.computeIfAbsent(objectDescribe.getApiName(), var -> new HashSet<>()).add(fieldName);
                            resultMap.get(dataId).get(objectDescribe.getApiName()).put("NON_ATTR", calculateRelation);
                            flag = false;
                        }
                        Matcher matcher = pattern.matcher(formula);
                        while (matcher.find()) {
                            String match = matcher.group(1);
                            String field = StringUtils.substringBefore(match, "__r.");
                            IFieldDescribe exprFieldDescribe = objectDescribe.getFieldDescribe(field);
                            if (exprFieldDescribe != null) {
                                if (Objects.equals(exprFieldDescribe.getType(), IFieldType.MASTER_DETAIL)) {
                                    masterDetailFieldDescribe = exprFieldDescribe;
                                    String masterField = StringUtils.substringAfterLast(match, "__r.");
                                    if (StringUtils.isNotBlank(masterField) && masterDescribeCopy.getFieldDescribe(masterField) != null) {
                                        IFieldDescribe masterFieldDescribe = masterDescribeCopy.getFieldDescribe(masterField);
                                        masterFields.add(masterFieldDescribe);
                                        if (Objects.equals("formula", masterFieldDescribe.getType())) {
                                            masterFieldDescribe.setExpression(null);
                                        } else {
                                            masterFieldDescribe.setDefaultValue(null);
                                        }
                                    }
                                } else {
                                    if (filterIds.add(exprFieldDescribe.getId())) {
                                        if (Objects.equals("formula", exprFieldDescribe.getType())) {
                                            exprFieldDescribe.setExpression(null);
                                        } else {
                                            exprFieldDescribe.setDefaultValue(null);
                                        }
                                        newFieldDescribe.add(exprFieldDescribe);
                                    }
                                }
                                flag = false;
                            }
                        }
                        if(flag){
                            Map<String, CalculateRelation> relationMap = resultMap.get(dataId).getOrDefault(objectDescribe.getApiName(),Maps.newHashMap());
                            CalculateRelation calculateRelation = relationMap.getOrDefault("advanced_constant_fields", new CalculateRelation());
                            calculateRelation.addCalculateField(objectDescribe.getApiName(),fieldDescribe.getApiName());
                            resultMap.get(dataId).get(objectDescribe.getApiName()).put("advanced_constant_fields", calculateRelation);
                        }
                    }
                }
            }
            if (masterDetailFieldDescribe != null) {
                newFieldDescribe.add(masterDetailFieldDescribe);
            }
            objectDescribe.setFieldDescribes(newFieldDescribe);
            masterDescribeCopy.setFieldDescribes(masterFields);
            fieldRelationCalculateService.computeCalculateRelationWithExt(masterDescribeCopy, Lists.newArrayList(objectDescribe), null);
            createResult(resultMap, dataId, objectDescribe, objectDescribe.getApiName());
            createResult(resultMap, dataId, masterDescribeCopy, objectDescribe.getApiName());
        });
        return resultMap;
    }

    @NotNull
    private List<IObjectData> queryFormulaList(User user, QuoterModel.Arg arg) {
        List<IObjectData> dataList = Lists.newArrayList();
        SearchTemplateQuery searchTemplateQuery = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(arg.getQueryInfo());
        searchTemplateQuery.setLimit(2000);
        List<Wheres> wheres = searchTemplateQuery.getWheres();
        if (CollectionUtils.isEmpty(wheres)) {
            return dataList;
        }
        Wheres addWhere = null;
        for (Wheres where : wheres) {
            List<IFilter> filters = where.getFilters();
            if (CollectionUtils.isNotEmpty(filters)) {
                for (IFilter f : filters) {
                    if (Objects.equals(f.getFieldName(), QuoterModel.AdvancedFormulaModel.PRODUCT_ID)) {
                        addWhere = where;
                    }
                }
            }
        }
        if (addWhere != null) {
            List<IFilter> filters = addWhere.getFilters();
            SearchUtil.fillFilterIsNull(filters, QuoterModel.AdvancedFormulaModel.BOM_ID);
        }
        int offset = 0;
        int loopCnt = 0;
        QueryResult<IObjectData> queryResult;
        while (loopCnt < 10) {
            searchTemplateQuery.setOffset(offset);
            queryResult = serviceFacade.findBySearchQueryIgnoreAll(user, API_NAME, searchTemplateQuery);
            List resultData = queryResult == null ? Lists.newArrayList() : queryResult.getData();
            if (CollectionUtils.isNotEmpty(resultData)) {
                dataList.addAll(resultData);
            }
            if (CollectionUtils.isEmpty(resultData) || resultData.size() < 2000) {
                break;
            }
            offset += 2000;
            loopCnt++;
        }
        return dataList;
    }

    private void createResult(Map<String, Map<String, Map<String, CalculateRelation>>> resultMap, String dataId, IObjectDescribe objectDescribe, String apiName) {
        objectDescribe.getFieldDescribeMap().forEach((name, dataField) -> {
            if (!Objects.equals(dataField.getType(), IFieldType.MASTER_DETAIL) && FieldDescribeExt.of(dataField).getCalculateRelation() != null && MapUtils.isNotEmpty(FieldDescribeExt.of(dataField).getCalculateRelation().getCalculateFields())) {
                resultMap.get(dataId).get(objectDescribe.getApiName()).put(name, FieldDescribeExt.of(dataField).getCalculateRelation());
            }
        });
    }

    @Override
    public void init(String tenantId) {
        List<String> apiNames = Lists.newArrayList(API_NAME);
        ObjectDescribe describe = enterpriseInitService.getDescribeFromLocalResource(API_NAME);
        List<IFormField> formFieldList = Lists.newArrayList();
        if (SFAConfigUtil.isCPQ(tenantId)) {
            IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(ADF_BOM_ID);
            if (describe.getFieldDescribe(fieldDescribe.getApiName()) == null) {
                describe.addFieldDescribe(fieldDescribe);
            }
            IFormField formField = new FormField();
            formField.setFieldName(fieldDescribe.getApiName());
            formField.setRequired(false);
            formField.setReadOnly(false);
            formField.setRenderType(fieldDescribe.getType());
            formFieldList.add(formField);
        }
        enterpriseInitService.initDescribeForTenant(tenantId, describe);
        enterpriseInitService.initMultiLayoutForOneTenant(apiNames, tenantId, formFieldList);
        enterpriseInitService.initPrivilegeRelate(apiNames, User.systemUser(tenantId), null, null, null);
    }

    @Override
    public String translateFormula(IObjectDescribe describe, Pattern pattern, Map<String, IObjectData> finalProductMap, IObjectData data, String tenantId) {
        String formula = data.get(QuoterModel.AdvancedFormulaModel.FORMULA, String.class, "");
        if (StringUtils.isNotBlank(formula)) {
            JSONObject jsonObject = JSON.parseObject(formula);
            String expression = jsonObject.getString("expression");
            if (StringUtils.isNotBlank(expression)) {
                Matcher matcher = pattern.matcher(expression);
                while (matcher.find()) {
                    String match = matcher.group(1);
                    if (match.contains("__r.")) {
                        String prefield = StringUtils.substringBefore(match, "__r.");
                        IFieldDescribe exprFieldDescribe = describe.getFieldDescribe(prefield);
                        if (exprFieldDescribe != null) {
                            String suxField = StringUtils.substringAfterLast(match, "__r.");
                            if (StringUtils.isNotBlank(suxField)) {
                                try {
                                    IObjectDescribe targetObj = serviceFacade.findObject(tenantId, exprFieldDescribe.get("target_api_name", String.class));
                                    IFieldDescribe targetField = targetObj.getFieldDescribe(suxField);
                                    expression = StringUtils.replace(expression, match, exprFieldDescribe.getLabel() + "." + Optional.ofNullable(targetField).map(IFieldDescribe::getLabel).orElse(suxField));
                                } catch (Exception e) {
                                    log.warn(e.getMessage());
                                }
                            }
                        }
                    } else {
                        IFieldDescribe exprFieldDescribe = describe.getFieldDescribe(match);
                        if (exprFieldDescribe != null) {
                            expression = StringUtils.replace(expression, match, exprFieldDescribe.getLabel());
                        }
                        if (match.contains("EXT#ATTR#")) {
                            String suxField = StringUtils.substringAfterLast(match, "EXT#ATTR#");
                            IObjectData product = finalProductMap.get(data.get(QuoterModel.AdvancedFormulaModel.PRODUCT_ID, String.class));
                            if (product != null) {
                                List<Attribute> standardAttribute = product.get(AttributeConstants.ATTRIBUTE, List.class);
                                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(standardAttribute)) {
                                    for (Attribute attribute : standardAttribute) {
                                        if (Objects.equals(attribute.getId(), suxField)) {
                                            expression = StringUtils.replace(expression, match, attribute.getName());
                                            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(attribute.getAttributeValues())) {
                                                for (AttributeValue a : attribute.getAttributeValues()) {
                                                    if (expression.contains(a.getId())) {
                                                        expression = StringUtils.replace(expression, a.getId(), a.getName());
                                                    }
                                                }
                                            }
                                            break;
                                        }

                                    }
                                }
                            }
                        }
                        if (match.contains("EXT#NON_ATTR#")) {
                            String suxField = StringUtils.substringAfterLast(match, "EXT#NON_ATTR#");
                            IObjectData product = finalProductMap.get(data.get(QuoterModel.AdvancedFormulaModel.PRODUCT_ID, String.class));
                            if (product != null) {
                                List<IObjectData> nonstandardAttribute = product.get("nonstandardAttribute", List.class);
                                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(nonstandardAttribute)) {
                                    String name = nonstandardAttribute.stream().filter(attr -> Objects.equals(attr.getId(), suxField)).map(AttributeUtils::getI18nName).findFirst().orElse(match);
                                    expression = StringUtils.replace(expression, match, name);
                                }
                            }

                        }

                    }
                }
            }
            return expression;
        }
        return "";
    }

    @Override
    public void checkRepeat(IObjectData objectData, User user) {
        String productId = objectData.get(QuoterModel.AdvancedFormulaModel.PRODUCT_ID, String.class);
        String bomId = objectData.get(QuoterModel.AdvancedFormulaModel.BOM_ID, String.class);
        String id = objectData.getId();
        if (StringUtils.isAllBlank(productId, bomId)) {
            throw new ValidateException(I18N.text(I18NKey.ERRORCODE_PARAM_WRONG));
        }
        String objectName = objectData.get(REF_OBJECT_API_NAME, String.class);
        String fieldName = objectData.get(REF_FIELD_NAME, String.class);
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, REF_OBJECT_API_NAME, objectName);
        SearchUtil.fillFilterEq(filters, REF_FIELD_NAME, fieldName);
        if (StringUtils.isNotBlank(bomId)) {
            SearchUtil.fillFilterEq(filters, QuoterModel.AdvancedFormulaModel.BOM_ID, bomId);
        } else {
            if (StringUtils.isNotBlank(productId)) {
                SearchUtil.fillFilterEq(filters, QuoterModel.AdvancedFormulaModel.PRODUCT_ID, productId);
                SearchUtil.fillFilterIsNull(filters, QuoterModel.AdvancedFormulaModel.BOM_ID);
            }
        }
        if (StringUtils.isNotBlank(id)) {
            SearchUtil.fillFilterNotEq(filters, DBRecord.ID, id);
        }
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, "0");
        query.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(user, "AdvancedFormulaObj", query);
        if (Objects.nonNull(queryResult) && com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(queryResult.getData())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ADD_ADVANCE_FORMULA_EXIST_WARN));
        }
    }

    @Override
    public void checkFieldFormula(IObjectDescribe objectDescribe, IObjectData objectData, List<IObjectData> dataList, User user) {
        String bomId = objectData.get(QuoterModel.AdvancedFormulaModel.BOM_ID, String.class);
        if (StringUtils.isBlank(bomId)) {
            return;
        }
        IObjectDescribe orderProductDescribe = serviceFacade.findObject(user.getTenantId(), objectData.get(QuoterModel.AdvancedFormulaModel.OBJECT_NAME, String.class));
        Optional<String> mdFieldOptional = ObjectDescribeExt.of(orderProductDescribe).getMasterDetailField().map(MasterDetail::getTargetApiName);
        if (!mdFieldOptional.isPresent()) {
            return;
        }
        Query query = Query.builder().needReturnCountNum(false).needReturnQuote(false).build();
        query.and(FilterExt.of(Operator.EQ, "plugin_api_name", "quoter").getFilter());
        query.and(FilterExt.of(Operator.EQ, DomainPluginInstance.REF_OBJECT_API_NAME, mdFieldOptional.get()).getFilter());
        query.and(FilterExt.of(Operator.EQ, DBRecord.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter());
        query.and(FilterExt.of(Operator.EQ, DomainPluginInstance.IS_ACTIVE, String.valueOf(Boolean.TRUE)).getFilter());
        List<DomainPluginInstance> domainPluginInstances = Optional.ofNullable(infraServiceFacade.findPluginInstanceByQuery(user, query)).map(QueryResult::getData).orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(domainPluginInstances)) {
            return;
        }
        domainPluginInstances.forEach(x -> {
            DomainPluginDescribeExt domain = DomainPluginDescribeExt.of(mdFieldOptional.get(), x.getPluginParam());
            String price = domain.getDefaultDetailFieldApiName("price");
            String quantity = domain.getDefaultDetailFieldApiName("quantity");
            for (IObjectData d : dataList) {
                if (Objects.equals(d.getId(), objectData.getId())) {
                    continue;
                }
                String field = d.get(QuoterModel.AdvancedFormulaModel.FIELD_NAME, String.class);
                if (StringUtils.equalsAny(field, price, quantity)) {
                    String formula = objectData.get(QuoterModel.AdvancedFormulaModel.FORMULA, String.class);
                    if (StringUtils.contains(formula, "$" + price + "$")) {
                        IFieldDescribe fieldDescribe = orderProductDescribe.getFieldDescribe(price);
                        if (Objects.nonNull(fieldDescribe)) {
                            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ADD_ADVANCE_FORMULA_FIELD_FORMULA_WARN, fieldDescribe.getLabel()));
                        }
                    }
                    if (StringUtils.contains(formula, "$" + quantity + "$")) {
                        IFieldDescribe fieldDescribe = orderProductDescribe.getFieldDescribe(quantity);
                        if (Objects.nonNull(fieldDescribe)) {
                            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ADD_ADVANCE_FORMULA_FIELD_FORMULA_WARN, fieldDescribe.getLabel()));
                        }
                    }
                } else {
                    String refField = objectData.get(QuoterModel.AdvancedFormulaModel.FIELD_NAME, String.class);
                    if (StringUtils.equalsAny(refField, price, quantity)) {
                        String formula = d.get(QuoterModel.AdvancedFormulaModel.FORMULA, String.class);
                        String objectField = d.get(QuoterModel.AdvancedFormulaModel.FIELD_NAME, String.class);
                        if (StringUtils.contains(formula, "$" + price + "$")) {
                            IFieldDescribe fieldDescribe = orderProductDescribe.getFieldDescribe(price);
                            IFieldDescribe objectFieldDescribe = orderProductDescribe.getFieldDescribe(objectField);
                            if (Objects.nonNull(fieldDescribe) && Objects.nonNull(objectFieldDescribe)) {
                                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ADD_ADVANCE_FORMULA_FIELD_FORMULA_QUOTE_WARN, fieldDescribe.getLabel(), objectFieldDescribe.getLabel()));
                            }
                        }
                        if (StringUtils.contains(formula, "$" + quantity + "$")) {
                            IFieldDescribe fieldDescribe = orderProductDescribe.getFieldDescribe(quantity);
                            IFieldDescribe objectFieldDescribe = orderProductDescribe.getFieldDescribe(objectField);
                            if (Objects.nonNull(fieldDescribe) && Objects.nonNull(objectFieldDescribe)) {
                                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ADD_ADVANCE_FORMULA_FIELD_FORMULA_QUOTE_WARN, fieldDescribe.getLabel(), objectFieldDescribe.getLabel()));
                            }
                        }
                    }
                }
            }
        });

    }
}
