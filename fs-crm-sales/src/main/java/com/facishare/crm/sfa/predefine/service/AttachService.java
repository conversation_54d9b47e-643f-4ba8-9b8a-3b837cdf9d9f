package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.privilege.util.EmployeeUtil;
import com.facishare.crm.sfa.predefine.service.model.*;
import com.facishare.crm.sfa.utilities.constant.AttachContants;
import com.facishare.crm.sfa.utilities.util.SFABizLogUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.impl.ui.layout.component.RelatedObjectList;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018/11/27 14:55
 */
@ServiceModule("biz_obj_attach")
@Component
@Slf4j
public class AttachService {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private GDSHandler gdsService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;


    @ServiceMethod("attach_list")
    public AttachResult.Result getAttachListByDataIdAndApiName(AttachResult.Arg arg, ServiceContext context) {
        String objectApiName = arg.getObjectApiName();
        String dataId = arg.getObjectDataId();
        if (StringUtils.isBlank(objectApiName) || StringUtils.isBlank(dataId)) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        QueryResult<IObjectData> queryResult = null;

        IObjectDescribe objectDescribe = serviceFacade.findObject(context.getTenantId(), arg.getObjectApiName());
        if (objectDescribe == null) {
            throw new ValidateException("describe not find");
        }
        IObjectData objectData = serviceFacade.findObjectDataIncludeDeleted(context.getUser(), arg.getObjectDataId(), arg.getObjectApiName());
        if (objectData == null) {
            throw new ValidateException("object data not find");
        }
        if (StringUtils.isBlank(objectApiName) || StringUtils.isBlank(dataId)) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        SearchTemplateQueryExt ext = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        ext.setOffset(arg.getOffset());
        ext.setLimit(arg.getLimit());
        ext.addFilter(Operator.EQ, AttachContants.AttachField.OBJECTDATAID.getApiName(), Lists.newArrayList(dataId));
        ext.addFilter(Operator.EQ, AttachContants.AttachField.OBJECTAPINAME.getApiName(), Lists.newArrayList(objectApiName));
        OrderBy orderBy = new OrderBy();
        orderBy.setIsAsc(false);
        orderBy.setFieldName("create_time");
        ext.setOrders(Lists.newArrayList(orderBy));
        queryResult = serviceFacade.findBySearchQuery(context.getUser(), AttachContants.API_NAME, (SearchTemplateQuery) ext.getQuery());
        List<IObjectData> dataList = queryResult.getData();
        dataList.forEach(data -> {
            setValueForCanpreview(data);
            String userName = EmployeeUtil.getUserName(data.getCreatedBy(), data.getTenantId());
            data.set("created_by__r", userName);
        });

        List<IButton> buttonList = Lists.newArrayList();
        if (!objectData.isDeleted()) {
            buttonList = getButtons(objectData, objectDescribe, context);
        }
        return AttachResult.Result.builder()
                .attachList(ObjectDataDocument.ofList(dataList))
                .total(queryResult.getTotalNumber())
                .limit(ext.getLimit())
                .offset(ext.getOffset())
                .buttons(buttonList)
                .build();
    }


    @ServiceMethod("add_attach")
    public AttachUpLoadResult.Result batchSaveAttach(AttachUpLoadResult.Arg arg, ServiceContext context) {
        String objectApiName = arg.getObjectApiName();
        String objectDataId = arg.getObjectDataId();
        List<AttachUpLoadResult.AttachInfoList> attachInfoList = arg.getAttachInfoList();
        if (StringUtils.isBlank(objectApiName) || StringUtils.isBlank(objectDataId) || CollectionUtils.isEmpty(attachInfoList)) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }

        IObjectDescribe desc = describeLogicService.findObject(context.getTenantId(), AttachContants.API_NAME);
        IObjectDescribe objectDesc = describeLogicService.findObject(context.getTenantId(), objectApiName);
        IObjectData objData = serviceFacade.findObjectData(context.getTenantId(), objectDataId, objectDesc);
        if (!getAttachPrivilege(context.getUser(), objectApiName)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_UPLOAD_NOT_HAVE_PERMISSION));
        }
        attachInfoList.forEach(k -> {
            ObjectData objectData = new ObjectData();
            objectData.setName(k.getName());
            objectData.setTenantId(context.getTenantId());
            objectData.setOwner(Lists.newArrayList(context.getUser().getUpstreamOwnerIdOrUserId()));
            objectData.setDescribeApiName(AttachContants.API_NAME);
            objectData.set(AttachContants.AttachField.HEIGHT.getApiName(), 0);
            objectData.set(AttachContants.AttachField.WIDTH.getApiName(), 0);
            objectData.set(AttachContants.AttachField.ORIGINALHEIGHT.getApiName(), 0);
            objectData.set(AttachContants.AttachField.ORIGINALWIDTH.getApiName(), 0);
            objectData.set(AttachContants.AttachField.FIELDNAME.getApiName(), "");
            objectData.set(AttachContants.AttachField.ATTACHTYPE.getApiName(), 3);
            objectData.set(AttachContants.AttachField.OBJECTDESCRIBE_ID.getApiName(), desc.getId());
            objectData.set(AttachContants.AttachField.OBJECTDATAID.getApiName(), objectDataId);
            objectData.set(AttachContants.AttachField.OBJECTAPINAME.getApiName(), objectApiName);
            objectData.set(AttachContants.AttachField.ATTACHSIZE.getApiName(), k.getSize());
            String finalAPath = gdsService.NsaveFileFromTempFile(k.getTempFileName(), context.getTenantId(), k.getFileExtension(), context.getUser().getUpstreamOwnerIdOrUserId());
            objectData.set(AttachContants.AttachField.ATTACHPATH.getApiName(), finalAPath);
            serviceFacade.saveObjectData(context.getUser(), objectData);
            serviceFacade.logCustomMessageOnly(context.getUser(), EventType.ADD, ActionType.UNLOAD, objectDesc, objData,
                    k.getName());
        });
        log.info("添加附件发送MQ:{}", ObjectAction.ADD_ATTACH);
        serviceFacade.sendActionMq(context.getUser(), Lists.newArrayList(objData), ObjectAction.ADD_ATTACH);
        return AttachUpLoadResult.Result.builder().build();

    }


    @ServiceMethod("delete_attach")
    public AttachDeleteResult.Result batchDeleteAttach(AttachDeleteResult.Arg arg, ServiceContext context) {
        String objectApiName = arg.getObjectApiName();
        List<String> ids = arg.getIds();
        if (StringUtils.isBlank(objectApiName) || CollectionUtils.isEmpty(ids)) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        List<IObjectData> includeDeletedDatas = serviceFacade.findObjectDataByIdsIncludeDeleted(context.getUser(), ids, AttachContants.API_NAME);
        if (CollectionUtils.isEmpty(includeDeletedDatas)) {
            throw new ValidateException("delete attach failed");
        }
        if (!getAttachPrivilege(context.getUser(), objectApiName)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_UPLOAD_NOT_HAVE_PERMISSION));
        }
        List<IObjectData> invalided = serviceFacade.bulkInvalid(includeDeletedDatas, context.getUser());
        invalided.forEach(data -> {
            IObjectDescribe objectDesc = describeLogicService.findObject(context.getTenantId(), objectApiName);
            IObjectData objectData = serviceFacade.findObjectData(context.getTenantId(), data.get(AttachContants.AttachField.OBJECTDATAID.getApiName(), String.class), objectDesc);
            serviceFacade.bulkDelete(Lists.newArrayList(data), context.getUser());
            serviceFacade.logCustomMessageOnly(context.getUser(), EventType.DELETE, ActionType.Delete, objectDesc, objectData,
                    data.getName());

        });
        return AttachDeleteResult.Result.builder().build();
    }


    @ServiceMethod("rename_attach")
    public AttachReNameResult.Result reNameAttach(AttachReNameResult.Arg arg, ServiceContext context) {
        String objectApiName = arg.getObjectApiName();
        String id = arg.getId();
        String name = arg.getName();
        if (StringUtils.isBlank(objectApiName) || StringUtils.isBlank(id) || StringUtils.isBlank(name)) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        IObjectData objectData = serviceFacade.findObjectData(context.getUser(), id, AttachContants.API_NAME);
        if (Objects.isNull(objectData)) {
            throw new ValidateException("rename attach failed");
        }
        String endWithName = "";
        if (StringUtils.isNotBlank(name)) {
            int index = name.lastIndexOf(".");
            if (index >= 0) {
                endWithName = name.substring(index);
            }
        }
        String fileType = getFileType(objectData);
        if (StringUtils.isNotBlank(endWithName) && StringUtils.isNotBlank(fileType) && fileType.equals(endWithName)) {
            objectData.setName(name);
        } else {
            objectData.setName(name + fileType);
        }
        serviceFacade.updateObjectData(context.getUser(), objectData);
        return AttachReNameResult.Result.builder().build();
    }

    public RelatedObjectList getProductAttrComponent(User user) {
        Map<String, Map<String, Boolean>> objApiNameAndActionCodePrivilegeMapping = functionPrivilegeService.batchFunPrivilegeCheck(user,
                Lists.newArrayList(Utils.PRODUCT_API_NAME),
                Lists.newArrayList("ViewAttach", "UploadDeleteAttach"));
        if (!objApiNameAndActionCodePrivilegeMapping.get(Utils.PRODUCT_API_NAME).get("ViewAttach")) {
            return null;
        }
        RelatedObjectList component = new RelatedObjectList();
        component.setName("ProductAttObj_related_list");
        component.setHeader(I18N.text(I18NKey.ANNEX));
        component.setRefObjectApiName("AttachObj");
        component.setRelatedListName("AttachObj");
        component.setOrder(2);
        if (objApiNameAndActionCodePrivilegeMapping.get(Utils.PRODUCT_API_NAME).get("UploadDeleteAttach")) {
            List<IButton> buttons = getButtons();
            component.setButtons(buttons);
        }
        return component;
    }

    private List<IButton> getButtons() {
        List<IButton> buttons = Lists.newArrayList();
        IButton addButton = new Button();
        addButton.setAction("Add");
        addButton.setActionType("default");
        addButton.setLabel(I18N.text(I18NKey.action_upload));
        addButton.setName("ProductAttObj_Add_button_default");
        buttons.add(addButton);
        IButton deleteButton = new Button();
        deleteButton.setAction("Delete");
        deleteButton.setActionType("default");
        deleteButton.setLabel(I18N.text(I18NKey.action_delete));
        deleteButton.setName("ProductAttObj_Delete_button_default");
        buttons.add(deleteButton);
        return buttons;
    }

    private void setValueForCanpreview(IObjectData data) {
        String fileType = getFileType(data);
        if (StringUtils.isBlank(fileType)) {
            data.set("can_preview", false);
        } else {
            fileType = fileType.toLowerCase();
            boolean isCanPreview = isCanPreview(fileType);
            if (isCanPreview) {
                data.set("can_preview", true);
            } else {
                AttachContants.FileTypeMime typeMime = AttachContants.FileTypeMime.fromExtension(fileType);
                if (typeMime == null) {
                    data.set("can_preview", false);
                } else {
                    String mime = typeMime.getMimeType();
                    if (mime.startsWith("text") ||
                            "image/bmp".equals(mime) ||
                            "image/gif".equals(mime) ||
                            "image/jpeg".equals(mime) ||
                            "image/png".equals(mime)) {
                        data.set("can_preview", true);
                    } else {
                        data.set("can_preview", false);
                    }
                }
            }
        }
    }


    private String getFileType(IObjectData data) {
        String attachPath = data.get(AttachContants.AttachField.ATTACHPATH.getApiName(), String.class);
        String fileType = "";
        if (StringUtils.isNotBlank(attachPath)) {
            int index = attachPath.lastIndexOf(".");
            if (index >= 0) {
                fileType = attachPath.substring(index);
            }
        }
        return fileType;
    }


    private boolean isCanPreview(String fileType) {
        switch (fileType) {
            case AttachContants.XLSX:
            case AttachContants.TXT:
            case AttachContants.PPT:
            case AttachContants.DOCX:
            case AttachContants.PPTX:
            case AttachContants.PDF:
            case AttachContants.DOC:
            case AttachContants.XLS:
            case AttachContants.CSV:
                return true;
            default:
                return false;

        }
    }

    public AttachServiceModels.MergeAttachResult mergeAttach(User user, AttachServiceModels.MergeAttachArg arg) {
        log.info("merge attach begin,arg {}", arg);
        AttachServiceModels.MergeAttachResult result = AttachServiceModels.MergeAttachResult.builder().success(true)
                .build();
        String objectApiName = arg.getApiName();
        if (StringUtils.isBlank(objectApiName) || StringUtils.isBlank(arg.getTargetDataId())
                || CollectionUtils.isEmpty(arg.getSourceDataIds())) {
            return result;
        }
        QueryResult<IObjectData> queryResult = null;
        try {
            SearchTemplateQueryExt ext = SearchTemplateQueryExt.of(new SearchTemplateQuery());
            ext.setOffset(0);
            ext.setLimit(20);
            ext.addFilter(Operator.IN, AttachContants.AttachField.OBJECTDATAID.getApiName(), Lists.newArrayList(arg.getSourceDataIds()));
            ext.addFilter(Operator.EQ, AttachContants.AttachField.OBJECTAPINAME.getApiName(), Lists.newArrayList(objectApiName));

            for(int executeCount = 0, maxExecuteCount = 1000 ; executeCount < maxExecuteCount ; executeCount++){
                if (executeCount >= maxExecuteCount - 1) {
                    log.warn("AttachService#mergeAttach reaches loop limit, limit:{}", executeCount); //日志打印
                    //上报audit_log
                    SFABizLogUtil.sendAuditLog(SFABizLogUtil.Arg.builder()
                        .action("sfa_loop_limit")
                        .objectApiNames(objectApiName)
                        .message("AttachService.mergeAttach").build(), user);
                    break;
                }

                queryResult = serviceFacade.findBySearchQuery(user, AttachContants.API_NAME, (SearchTemplateQuery) ext.getQuery());
                List<IObjectData> dataList = queryResult.getData();
                if (CollectionUtils.isEmpty(dataList)) {
                    break;
                }
                dataList.forEach(data -> {
                    data.set(AttachContants.AttachField.OBJECTDATAID.getApiName(), arg.getTargetDataId());
                });
                serviceFacade.batchUpdateByFields(user, dataList, Lists.newArrayList(AttachContants.AttachField.OBJECTDATAID.getApiName()));
            }
        } catch (Exception e) {
            log.warn("merge attach error", e);
            result.setSuccess(false);
        }
        return result;
    }

    private List<IButton> getButtons(IObjectData data, IObjectDescribe describe, ServiceContext context) {
        List<IButton> buttons = Lists.newArrayList();
        //增加功能权限校验
        if (serviceFacade.funPrivilegeCheck(context.getUser(), describe.getApiName(), ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode())) {
            Map<String, Map<String, Permissions>> privilege = metaDataService.checkDataPrivilege(
                    context.getUser(), Lists.newArrayList(data), ObjectDescribeExt.of(describe),
                    Lists.newArrayList(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode()));

            if (privilege.containsKey(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode())) {
                Map<String, Permissions> permissions = privilege.get(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode());
                if (permissions.containsKey(data.getId())
                        && permissions.get(data.getId()).equals(Permissions.READ_WRITE)) {
                    IButton addButton = new Button();
                    addButton.setAction("Add");
                    addButton.setActionType("default");
                    addButton.setLabel(I18N.text(I18NKey.action_upload));
                    addButton.setName("AccountAttObj_Add_button_default");
                    buttons.add(addButton);
                    IButton deleteButton = new Button();
                    deleteButton.setAction("Delete");
                    deleteButton.setActionType("default");
                    deleteButton.setLabel(I18N.text(I18NKey.action_delete));
                    deleteButton.setName("AccountAttObj_Delete_button_default");
                    buttons.add(deleteButton);
                }
            }
        }
        return buttons;
    }


    private boolean getAttachPrivilege(User user, String objectApiName) {
        return serviceFacade.funPrivilegeCheck(user, objectApiName, ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode());
    }
}
