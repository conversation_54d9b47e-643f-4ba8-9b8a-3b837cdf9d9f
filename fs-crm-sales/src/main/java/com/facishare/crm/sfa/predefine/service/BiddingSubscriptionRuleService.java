package com.facishare.crm.sfa.predefine.service;

import com.facishare.paas.license.Result.ModuleInfoResult;
import com.facishare.paas.license.arg.QueryModuleArg;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import org.springframework.util.ObjectUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.constants.CommonConstants;
import com.facishare.crm.sfa.lto.procurement.qianlima.QlmProcurementService;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.Procurement.module.ProcurementUtils;
import com.facishare.crm.sfa.predefine.service.model.BiddingSubscriptionRulesModel;
import com.facishare.crm.sfa.predefine.service.model.procurement.BiddingSubscriptionRulesConstants;
import com.facishare.crm.sfa.predefine.service.task.TaskService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.license.Result.ParaInfoResult;
import com.facishare.paas.license.arg.QueryModuleParaArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ModuleParaPojo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.functions.utils.Maps;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.facishare.crm.sfa.predefine.service.model.procurement.BiddingSubscriptionRulesConstants.*;

@Service
@Slf4j
@ServiceModule("biddingSubscriptionRule")
public class BiddingSubscriptionRuleService {

    @Autowired
    private LicenseClient licenseClient;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private QlmProcurementService qlmProcurementService;
    @Autowired
    private BiddingSubscriptionService biddingSubscriptionService;
    @Autowired
    private TaskService taskService;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-sales-config", config -> {
            buildMap(KEYWORDS_MATCH_MODE_MAP, config.get("qlm_subscriber_keywords_match_mode_map", "[]"));
            buildMap(KEYWORDS_MATCH_FIELD_MAP, config.get("qlm_subscriber_keywords_match_field_map", "[]"));
            buildMap(CALLER_TYPE_MAP, config.get("qlm_subscriber_caller_type_map", "[]"));
            buildMap(BID_SUB_TYPE_MAP, config.get("qlm_subscriber_bid_sub_type_map", "[]"));
        });
    }

    public static <T> void buildMap(Map<String, T> map, String json) {
        JSONArray options = JSON.parseArray(json);
        for (int i = 0; i < options.size(); i++) {
            JSONObject option = options.getJSONObject(i);
            map.put(option.getString("value"),
                    option.getObject("thirdParty", new TypeReference<T>() {
                            }
                    )
            );
        }
    }

    public void delRuleToQlm(IObjectData data) {
        String tenantId = data.getTenantId();
        JSONObject request = new JSONObject();
        QlmProcurementService.Account account = qlmProcurementService.getAccount(tenantId);
        closeQlmAiRule(data, account);
        request.put("accountKey", account.getAccountKey());
        request.put("id", data.get(BiddingSubscriptionRulesConstants.QLM_ID, Long.class));
        QlmProcurementService.Result result = qlmProcurementService.commonPost("/open/subscriber/delete", request);
        if (!result.isSuccess()) {
            throw new ValidateException(result.getMsg());
        }
        data.set(BiddingSubscriptionRulesConstants.QLM_ID, null);
        data.set(BiddingSubscriptionRulesConstants.PROCUREMENT_ANALYSIS_INIT_STATUS, null);
        data.set(BiddingSubscriptionRulesConstants.QLM_BUY_DATA, null);
        data.set(BiddingSubscriptionRulesConstants.QLM_BUY_DATA_STATUS, null);
    }

    public void saveRuleToQlm(IObjectData data) {
        String tenantId = data.getTenantId();
        JSONObject request = ProcurementUtils.toQueryParam(data);
        String qlmId = data.get(BiddingSubscriptionRulesConstants.QLM_ID, String.class);
        if (!StringUtils.isEmpty(qlmId)) {
            request.put("id", qlmId);
        }
        QlmProcurementService.Account account = qlmProcurementService.getAccount(tenantId);
        if (account == null){
            throw new ValidateException(I18N.text("sfa.no.qianlima.account.found"));
        }
        request.put("accountKey", account.getAccountKey());
        QlmProcurementService.Result result = qlmProcurementService.commonPost("/open/subscriber/createOrUpdate", request);
        if (!result.isSuccess()) {
            throw new ValidateException(result.getMsg());
        }
        data.set(BiddingSubscriptionRulesConstants.QLM_ID, result.getData(String.class));
        openQlmAiRule(data, account);
    }

    public void openQlmAiRule(IObjectData data, QlmProcurementService.Account account){
        // 判断 description_of_subject_matter 字段
        if (ObjectUtils.isEmpty(data.get("description_of_subject_matter"))){
            return;
        }
        // 判断License
        if (!getLicense(data.getTenantId(), "bidding_info_manage_qianlima_subscription_ai_plugin_app")) {
            return;
        }
        Set<String> keywords = new HashSet<>();
        try {
            JSON.parseArray(data.get("keywords", String.class)).forEach(
                    keyword -> keywords.addAll(Arrays.asList(keyword.toString().split(" ")))
            );
        } catch (Exception e) {
            log.warn("openQlmAiRule keywords erroe,keywords:{}", data.get("keywords", String.class));
        }
        JSONObject request = new JSONObject();
        request.put("accountKey", account.getAccountKey());
        request.put("name", data.getName());
        request.put("dyIds", Lists.newArrayList(Long.parseLong(data.get(BiddingSubscriptionRulesConstants.QLM_ID, String.class))));
        request.put("businessContent", data.get("description_of_subject_matter", String.class));
        request.put("excludeBusinessContent", data.get("description_of_not_subject_matter", String.class));
        request.put("precautions", data.get("subscription_requirements", String.class));
        request.put("keywords", String.join(",", keywords));
        request.put("disjunctor", 1);
        try {
            QlmProcurementService.Result result = qlmProcurementService.commonPost("/open/bigmodel/template/related/create", request);
            if (!result.isSuccess()) {
                log.error("openQlmAiRule error,msg:{}", result.getMsg());
                delRuleToQlm(data);
            }
            data.set("qlm_ai_template_related_id", result.getData(String.class));
        } catch (Exception e) {
            log.error("openQlmAiRule error", e);
            delRuleToQlm(data);
        }
    }

    private boolean getLicense(String tenantId, String appId) {
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId(appId);
        licenseContext.setTenantId(tenantId);
        licenseContext.setUserId(User.SUPPER_ADMIN_USER_ID);
        QueryModuleArg arg = new QueryModuleArg();
        arg.setLicenseContext(licenseContext);
        ModuleInfoResult result = licenseClient.queryModule(arg);
        List<ModuleInfoPojo> modules = result.getResult();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(modules) && modules.stream().anyMatch(module -> Objects.equals(module.getModuleCode(), appId))) {
            return true;
        }
        return false;
    }

    public void closeQlmAiRule(IObjectData data, QlmProcurementService.Account account) {
        if (ObjectUtils.isEmpty(data.get("qlm_ai_template_related_id"))){
            return;
        }
        JSONObject request = new JSONObject();
        request.put("accountKey", account.getAccountKey());
        request.put("id", Long.parseLong(data.get("qlm_ai_template_related_id", String.class)));
        QlmProcurementService.Result result = qlmProcurementService.commonPost("/open/bigmodel/template/related/delete", request);
        if (!result.isSuccess()) {
            throw new ValidateException(result.getMsg());
        }
        data.set("qlm_ai_template_related_id", null);
    }

    private void checkLicensePara(String tenantId, String recordType) {
        int maxCount = getMaxCount(tenantId, recordType);
        if (maxCount == 0) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_IMPORT_RULE_LICENSE_NOT_ENOUGH));
        }
        int count = getUsingCount(tenantId, recordType);
        if (count >= maxCount) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_BIDDING_RULE_COUNT_REACH_LIMITED, maxCount));
        }
    }

    public int getMaxCount(String tenantId, String recordType) {
        return selectModuleParaMaxCount(tenantId, BiddingSubscriptionRulesConstants.PARA_KEY);
    }

    public int getUsingCount(String tenantId, String recordType) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, tenantId);
        SearchUtil.fillFilterEq(filters, CommonConstants.IS_DELETED, 0);
        SearchUtil.fillFilterEq(filters, BiddingSubscriptionRulesConstants.IS_ENABLED, "true");
        SearchUtil.fillFilterEq(filters, CommonConstants.RECORD_TYPE, recordType);
        query.setFilters(filters);
        Integer count = serviceFacade.countObjectDataFromDB(tenantId, SFAPreDefineObject.BiddingSubscriptionRules.getApiName(), query);
        if (count == null) {
            count = 0;
        }
        return count;
    }

    public int getBuyHistoricalDataUsingCount(String tenantId) {
        int useCount = 0;
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IObjectData.TENANT_ID, tenantId);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setNeedReturnQuote(false);
        searchTemplateQuery.setLimit(1);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setOrders(Lists.newArrayList(new OrderBy("create_time", false)));
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), "ProcurementQlmBuyDataBillObj", searchTemplateQuery);
        if (ObjectUtils.isEmpty(queryResult) || ObjectUtils.isEmpty(queryResult.getData()) || ObjectUtils.isEmpty(queryResult.getData().get(0).get("cumulative_number"))) {
            return useCount;
        }
        return queryResult.getData().get(0).get("cumulative_number", Integer.class);
    }

    public int selectModuleParaMaxCount(String tenantId, String paraKey) {
        QueryModuleParaArg queryModuleParaArg = new QueryModuleParaArg();
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setTenantId(tenantId);
        licenseContext.setAppId("CRM");
        queryModuleParaArg.setContext(licenseContext);
        queryModuleParaArg.setParaKeys(Sets.newHashSet(paraKey));
        ParaInfoResult licenseResult = licenseClient.queryModulePara(queryModuleParaArg);
        int maxCount = 0;
        if (licenseResult == null || CollectionUtils.isEmpty(licenseResult.getResult())) {
            return maxCount;
        }
        for (ModuleParaPojo para : licenseResult.getResult()) {
            if (paraKey.equals(para.getParaKey())) {
                maxCount = Integer.parseInt(para.getParaValue());
            }
        }
        return maxCount;
    }

    public void checkPara(String tenantId, String recordType) {
        if (QLM.equals(recordType)) {
            checkLicensePara(tenantId, recordType);
        } else if (ZL.equals(recordType)) {
            biddingSubscriptionService.validateLicense(tenantId, TABLE_BIZ_BIDDING_RULE);
        }
    }

    @ServiceMethod("getBuyHistoricalDataEstimatedCount")
    public Object getBuyHistoricalDataEstimatedCount(ServiceContext context, BiddingSubscriptionRulesModel.BuyHistoricalDataArg arg) {
        QlmProcurementService.Result result = qlmHistoricalDataRequest(context, arg, "/open/subscriber/export/num");
        return Maps.of("count", result.getData(Long.class));
    }

    @ServiceMethod("buyHistoricalData")
    public BiddingSubscriptionRulesModel.Result buyHistoricalData(ServiceContext context, BiddingSubscriptionRulesModel.BuyHistoricalDataArg arg) {
        // 查验资源余量
        int allCount = selectModuleParaMaxCount(context.getTenantId(), BUY_DATA);
        int buyHistoricalDataUsingCount = allCount - getBuyHistoricalDataUsingCount(context.getTenantId());
        if (buyHistoricalDataUsingCount <= 0){
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_BIDDING_NO_RESOURCE_MSG));
        }
        // 发送请求并标记任务状态
        QlmProcurementService.Result result = qlmHistoricalDataRequest(context, arg, "/open/subscriber/export/start");
        String recordId = result.getData();
        IObjectData rulesObjectData = serviceFacade.findObjectData(context.getUser(), arg.getRulesId(), SFAPreDefineObject.BiddingSubscriptionRules.getApiName());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("qlm_buy_data_id", recordId);
        jsonObject.put("startTime", arg.getStartTime());
        jsonObject.put("endTime", arg.getEndTime());
        jsonObject.put("task_time", new Date());
        rulesObjectData.set(QLM_BUY_DATA, jsonObject.toJSONString());
        rulesObjectData.set(QLM_BUY_DATA_STATUS, "101");
        serviceFacade.batchUpdateByFields(context.getUser(), Lists.newArrayList(rulesObjectData), Lists.newArrayList(QLM_BUY_DATA,QLM_BUY_DATA_STATUS));
        // 发送MQ
        String mqData = String.format("%s %s", context.getTenantId(), rulesObjectData.getId());
        taskService.createOrUpdateTask("qlm_buy_data", context.getTenantId(), rulesObjectData.getId(), new Date(), mqData);
        return BiddingSubscriptionRulesModel.Result.builder().code(0).build();
    }

    private QlmProcurementService.Result qlmHistoricalDataRequest(ServiceContext context, BiddingSubscriptionRulesModel.BuyHistoricalDataArg arg,String url){
        if (ObjectUtils.isEmpty(arg.getEndTime()) || ObjectUtils.isEmpty(arg.getStartTime()) || ObjectUtils.isEmpty(arg.getRulesId())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MISS_REQUIRED_PARAMETERS));
        }
        // 查询订阅器
        IObjectData rulesObjectData = serviceFacade.findObjectData(context.getUser(), arg.getRulesId(), SFAPreDefineObject.BiddingSubscriptionRules.getApiName());
        if (ObjectUtils.isEmpty(rulesObjectData) || ObjectUtils.isEmpty(rulesObjectData.get(BiddingSubscriptionRulesConstants.QLM_ID))) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_BIDDING_RULE_NOT_START));
        }
        // 查询参数构建
        JSONObject request = new JSONObject();
        request.put("dyId", rulesObjectData.get(BiddingSubscriptionRulesConstants.QLM_ID, Long.class));
        request.put("startTime", dateFormat.format(arg.getStartTime()));
        request.put("endTime", dateFormat.format(arg.getEndTime()));
        QlmProcurementService.Account account = qlmProcurementService.getAccount(context.getTenantId());
        request.put("accountKey", account.getAccountKey());
        QlmProcurementService.Result result = qlmProcurementService.commonPost(url, request);
        if (!result.isSuccess()) {
            throw new ValidateException(String.format("%s：%s", I18N.text("ProcurementInfoObj.field.data_source.option.2"), result.getMsg()));
        }
        return result;
    }

}
