package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreService;
import com.facishare.crm.sfa.predefine.service.cpq.BomService;
import com.facishare.crm.sfa.predefine.service.cpq.model.CheckProductBomModel;
import com.facishare.crm.sfa.predefine.service.cpq.model.CreateBomModel;
import com.facishare.crm.sfa.predefine.service.cpq.model.GetAllParentProdListModel;
import com.facishare.crm.sfa.predefine.service.cpq.model.QueryAllSubBomModel;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.constant.BomCoreConstants;
import com.facishare.crm.sfa.utilities.constant.DetailProductConstants;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crm.sfa.utilities.enums.EnumUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.ProductConstraintUtil;
import com.facishare.crm.sfa.utilities.util.SFABizLogUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.BomUtil.canExactDivisor;

@Component
@Slf4j
public class BomServiceImpl implements BomService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private BomCoreService bomCoreService;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Autowired
    private ConfigService configService;

    private static final Integer MinValue = 0;
    private static final Integer MaxValue = 999999999;

    @Autowired
    private ObjectDataServiceImpl objectDataService;

    @Override
    public List<ObjectDataDocument> getProductGroupByIds(ServiceContext context, List<String> ids) {
        List<IObjectData> productGroupList = serviceFacade.findObjectDataByIds(context.getTenantId(), ids, Utils.PRODUCT_GROUP_API_NAME);
        return ObjectDataDocument.ofList(productGroupList);
    }

    @Override
    public GetAllParentProdListModel.Result getAllParentProdList(ServiceContext context, GetAllParentProdListModel.Arg arg) {
        if (StringUtils.isBlank(arg.getProductId())) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        GetAllParentProdListModel.Result result = new GetAllParentProdListModel.Result(Lists.newArrayList());
        SearchTemplateQuery bomQuery = new SearchTemplateQuery();
        bomQuery.setNeedReturnCountNum(false);
        bomQuery.setPermissionType(0);
        SearchTemplateQueryExt.of(bomQuery).addFilter(Operator.EQ, BomConstants.FIELD_PRODUCT_ID, arg.getProductId());
        List<IObjectData> dataList = serviceFacade.findBySearchQuery(context.getUser(), Utils.BOM_API_NAME, bomQuery).getData();
        if (CollectionUtils.isEmpty(dataList)) {
            return result;
        }
        bomQuery = new SearchTemplateQuery();
        bomQuery.setNeedReturnCountNum(false);
        bomQuery.setPermissionType(0);
        String matchValue = String.format("*.%s", Joiner.on("|").join(dataList.stream().map(DBRecord::getId).collect(Collectors.toList())));
        SearchTemplateQueryExt.of(bomQuery).addFilter(Operator.MATCH, BomConstants.FIELD_BOM_PATH, matchValue);
        QueryResult<IObjectData> bomList = serviceFacade.findBySearchQuery(context.getUser(), Utils.BOM_API_NAME, bomQuery);
        if (CollectionUtils.isEmpty(bomList.getData())) {
            return result;
        }
        Set<String> productIdSet = Sets.newHashSet();
        productIdSet.addAll(bomList.getData().stream().map(x -> x.get(BomConstants.FIELD_PRODUCT_ID).toString()).collect(Collectors.toSet()));
        List<String> pathList = bomList.getData().stream().map(x -> x.get(BomConstants.FIELD_BOM_PATH).toString()).collect(Collectors.toList());
        Set<String> bomIdSet = Sets.newHashSet();
        pathList.forEach(x -> {
                    String[] strArr = x.split("\\.");
                    for (String s : strArr) {
                        if (StringUtils.isNotBlank(s)) {
                            bomIdSet.add(s);
                        }
                    }
                }
        );
        List<IObjectData> parentBoms = serviceFacade.findObjectDataByIds(context.getTenantId(), Lists.newArrayList(bomIdSet), Utils.BOM_API_NAME);
        if (CollectionUtils.isNotEmpty(parentBoms)) {
            productIdSet.addAll(parentBoms.stream().map(x -> x.get(BomConstants.FIELD_PRODUCT_ID, String.class)).collect(Collectors.toSet()));
        }
        result.setParentProdIdList(Lists.newArrayList(productIdSet));
        return result;
    }

    @Override
    public CheckProductBomModel.Result checkBom(User user, CheckProductBomModel.Arg arg) {
        boolean isBomMasterSlaveMode = GrayUtil.bomMasterSlaveMode(user.getTenantId());
        boolean isBigBomTenants = GrayUtil.isBigBomTenant(user.getTenantId());
        Map<String, CheckProductBomModel.SingleResult> subResult = Maps.newHashMap();

        if (GrayUtil.isGrayBomPriceBookProduct(user.getTenantId()) && RequestUtil.isWebRequest()) {
            return new CheckProductBomModel.Result(subResult, Maps.newHashMap());
        }
        List<CheckProductBomModel.ProductBom> bomList = arg.getProductBomList();
        if (CollectionUtils.isEmpty(bomList)) {
            return new CheckProductBomModel.Result(subResult, Maps.newHashMap());
        }
        Set<String> rootProductIdList = bomList.stream().map(CheckProductBomModel.ProductBom::getRootProductId).collect(Collectors.toSet());
        Map<String, String> rootBomIdToRootProductId = Maps.newHashMap();

        bomList.forEach(x -> {
            rootBomIdToRootProductId.put(x.getRootBomId(), x.getRootProductId());
            x.getSubBomList().forEach(y -> {
                if (y.get("product_group_id__v") != null && StringUtils.isNotBlank(y.get("product_group_id__v").toString())) {
                    y.put(BomConstants.FIELD_PRODUCT_GROUP_ID, y.get("product_group_id__v").toString());
                }
            });
        });
        List<IObjectData> rootProductDataList = getProductDataList(user.getTenantId(), Lists.newArrayList(rootProductIdList));
        Map<String, String> productIdToName = rootProductDataList.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName, (v1, v2) -> v1));

        Map<String, String> rootBomIdToProductName = Maps.newHashMap();
        rootBomIdToRootProductId.forEach((rootBomId, rootProductId) -> {
            String productName = productIdToName.get(rootProductId);
            rootBomIdToProductName.put(rootBomId, productName);
        });
        List<String> rootBomIdList = Lists.newArrayList();
        List<String> coreIdList = Lists.newArrayList();
        Map<String, String> dataMap = Maps.newHashMap();
        boolean paramFlag = false;
        for (CheckProductBomModel.ProductBom productBom : bomList) {
            if (StringUtils.isNotBlank(productBom.getRootBomId())) {
                rootBomIdList.add(productBom.getRootBomId());
                if (CollectionUtils.isNotEmpty(productBom.getSubBomList())) {
                    List<IObjectData> subDataList = ObjectDataDocument.ofDataList(productBom.getSubBomList());
                    subDataList.forEach(x -> {
                        String relatedCoreId = x.get(BomConstants.FIELD_RELATED_CORE_ID, String.class);
                        if (StringUtils.isNotBlank(relatedCoreId)) {
                            coreIdList.add(relatedCoreId);
                            dataMap.put(relatedCoreId, "");
                        }
                    });
                }
            }
            if (StringUtils.isBlank(productBom.getCoreId())) {
                paramFlag = true;
            }
        }
        if (paramFlag) {
            SFABizLogUtil.Arg dto = SFABizLogUtil.Arg.builder()
                    .createTime(System.currentTimeMillis())
                    .objectApiNames(Utils.BOM_API_NAME)
                    .message("bom_core_id param is missing")
                    .action("check_bom_param")
                    .build();
            SFABizLogUtil.sendAuditLog(dto, user);
        }
        if (CollectionUtils.isEmpty(rootBomIdList)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }

        //将所有复用BOM数据取出并做new_bom_path处理
        List<IObjectData> dbBomList = queryBomByCond(user, rootBomIdList, isBomMasterSlaveMode, coreIdList);
        Map<String, List<IObjectData>> rootIdMap = new HashMap<>();
        Map<String, IObjectData> idMap = new HashMap<>();

        for (IObjectData iObjectData : dbBomList) {
            rootIdMap.computeIfAbsent(iObjectData.get(BomConstants.FIELD_ROOT_ID, String.class), k -> new ArrayList<>()).add(iObjectData);
            idMap.put(iObjectData.getId(), iObjectData);
        }
        Map<String, IObjectData> groupMap = Maps.newHashMap();
        List<String> groupIdList = dbBomList.stream().map(g -> g.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 校验分组
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            List<IObjectData> groupDataList = findProductCatalogList(user, groupIdList);
            if(isBigBomTenants) {
                checkGroupRequiredNew(dbBomList, bomList, rootBomIdToProductName, groupDataList);
            } else {
                checkGroupRequired(dbBomList, bomList, rootBomIdToProductName, groupDataList);
            }
            groupMap = groupDataList.stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (x1, x2) -> x2));
            if(isBigBomTenants) {
                checkGroupCountNew(bomList, groupMap, idMap);
            } else {
                checkGroupCount(bomList, groupMap, idMap);
            }
        }

        if (CollectionUtils.isEmpty(rootProductDataList)) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_INVALID_OBJ_PRODUCT));
        }
        Map<String, String> dataMaps = Maps.newHashMap();
        CheckProductBomModel.Result result = new CheckProductBomModel.Result();
        result.setResult(Maps.newHashMap());
        for (CheckProductBomModel.ProductBom bom : bomList) {
            //1.获取根产品的信息
            Optional<IObjectData> rootProduct = rootProductDataList.stream().filter(x -> x.getId().equals(bom.getRootProductId())).findFirst();
            if (!rootProduct.isPresent()) {
                throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
            }
            CheckProductBomModel.SingleResult singleResult = checkSingleBom(user, bom, groupMap, rootIdMap, arg.getSelectionPage(), idMap);
            if (StringUtils.isNotBlank(singleResult.getCheckSingleResult())) {
                String key = arg.getResultKeyIsVirtual() ? bom.getRootProdKey() : String.format("%s_%s", bom.getRootProductId(), bom.getRootProdKey());
                result.getResult().put(key, singleResult);
                if (MapUtils.isNotEmpty(singleResult.getBomResultList())) {
                    singleResult.setCheckSingleResult(Joiner.on(";").join(singleResult.getBomResultList().values()));
                }
            }
            List<IObjectData> tmpBomList = rootIdMap.getOrDefault(bom.getRootBomId(), Lists.newArrayList());
            if (CollectionUtils.isNotEmpty(tmpBomList)) {
                dataMaps.put(bom.getRootProdKey(), tmpBomList.get(0).get(BomConstants.FIELD_CORE_ID, String.class));
            }

        }
        result.setDataMap(dataMaps);
        return result;
    }

    @Override
    public void checkGroupCount(List<CheckProductBomModel.ProductBom> bomList, Map<String, IObjectData> groupMap, Map<String, IObjectData> dbMap) {
        bomList.forEach(x -> {
            BigDecimal rootNodeAmount = Objects.isNull(x.getRootNodeAmount()) ? BigDecimal.ONE : x.getRootNodeAmount();
            Multimap<String, String> multiMap = ArrayListMultimap.create();
            Map<String, BigDecimal> groupCount = Maps.newHashMap();
            List<IObjectData> dataList = ObjectDataDocument.ofDataList(x.getSubBomList());
            dataList.forEach(b -> {
                if (StringUtils.isNotBlank(b.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class))
                        && groupMap.containsKey(b.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class))) {
                    IObjectData bomData = dbMap.get(b.getId());
                    if (Objects.nonNull(bomData) && StringUtils.isNotBlank(bomData.get(BomConstants.FIELD_BOM_PATH, String.class))) {
                        multiMap.put(b.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class), bomData.get(BomConstants.FIELD_BOM_PATH, String.class).concat("."));
                        BigDecimal count = groupCount.getOrDefault(b.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class), BigDecimal.ZERO);
                        count = count.add(b.get("amount", BigDecimal.class, BigDecimal.ZERO));
                        groupCount.put(b.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class), count);
                    }
                }
            });
            if (!multiMap.isEmpty()) {
                for (String key : multiMap.keySet()) {
                    Collection<String> pathList = multiMap.get(key);
                    pathList.forEach(path -> dataList.forEach(b -> {
                        IObjectData bomData = dbMap.get(b.getId());
                        if (Objects.nonNull(bomData) && StringUtils.isNotBlank(bomData.get(BomConstants.FIELD_BOM_PATH, String.class))) {
                            if (StringUtils.startsWith(bomData.get(BomConstants.FIELD_BOM_PATH, String.class), path)) {
                                BigDecimal count = groupCount.getOrDefault(b.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class), BigDecimal.ZERO);
                                count = count.add(b.get("amount", BigDecimal.class, BigDecimal.ZERO));
                                groupCount.put(b.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class), count);
                            }
                        }
                    }));
                }
            }
            if (!groupCount.isEmpty()) {
                groupCount.forEach((groupId, count) -> {
                    IObjectData groupData = groupMap.get(groupId);
                    if (Objects.nonNull(groupData)) {
                        BigDecimal min = groupData.get(BomConstants.MIN_AMOUNT_COUNT, BigDecimal.class);
                        BigDecimal max = groupData.get(BomConstants.MAX_AMOUNT_COUNT, BigDecimal.class);
                        if (Objects.nonNull(min)) {
                            if (count.compareTo(min.multiply(rootNodeAmount)) < 0) {
                                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_MIN_AMOUNT_WARN, groupData.getName()));
                            }
                        }
                        if (Objects.nonNull(max)) {
                            if (count.compareTo(max.multiply(rootNodeAmount)) > 0) {
                                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_MAX_AMOUNT_WARN, groupData.getName()));
                            }
                        }
                    }
                });
            }
        });
    }
    
    /**
     * 性能优化版本的分组数量校验方法
     */
    public void checkGroupCountNew(List<CheckProductBomModel.ProductBom> bomList, Map<String, IObjectData> groupMap, Map<String, IObjectData> dbMap) {
        for (CheckProductBomModel.ProductBom bom : bomList) {
            BigDecimal rootNodeAmount = Objects.isNull(bom.getRootNodeAmount()) ? BigDecimal.ONE : bom.getRootNodeAmount();
            List<IObjectData> dataList = ObjectDataDocument.ofDataList(bom.getSubBomList());
            if (CollectionUtils.isEmpty(dataList)) {
                continue;
            }
            
            // 预处理：创建BOM路径索引和分组ID到BOM路径的映射
            Map<String, String> bomPathMap = new HashMap<>();
            Map<String, Set<String>> groupToBomPathsMap = new HashMap<>();
            
            // 第一次遍历：收集所有BOM路径和初始分组计数
            Map<String, BigDecimal> groupCount = new HashMap<>();
            
            for (IObjectData data : dataList) {
                String id = data.getId();
                IObjectData bomData = dbMap.get(id);
                if (bomData == null || StringUtils.isBlank(bomData.get(BomConstants.FIELD_BOM_PATH, String.class))) {
                    continue;
                }
                
                String bomPath = bomData.get(BomConstants.FIELD_BOM_PATH, String.class);
                bomPathMap.put(id, bomPath);
                
                String groupId = data.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class);
                if (StringUtils.isBlank(groupId) || !groupMap.containsKey(groupId)) {
                    continue;
                }
                
                // 添加到分组到路径的映射
                groupToBomPathsMap.computeIfAbsent(groupId, k -> new HashSet<>())
                    .add(bomPath + ".");
                
                // 计算初始分组数量
                BigDecimal amount = data.get("amount", BigDecimal.class, BigDecimal.ZERO);
                groupCount.merge(groupId, amount, BigDecimal::add);
            }
            
            // 第二次遍历：处理子路径的计数
            for (Map.Entry<String, Set<String>> entry : groupToBomPathsMap.entrySet()) {
                String groupId = entry.getKey();
                Set<String> parentPaths = entry.getValue();
                
                for (IObjectData data : dataList) {
                    String id = data.getId();
                    String bomPath = bomPathMap.get(id);
                    if (bomPath == null) {
                        continue;
                    }
                    
                    // 检查当前BOM是否是某个分组的子路径
                    for (String parentPath : parentPaths) {
                        if (StringUtils.startsWith(bomPath, parentPath)) {
                            String dataGroupId = data.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class);
                            BigDecimal amount = data.get("amount", BigDecimal.class, BigDecimal.ZERO);
                            groupCount.merge(groupId, amount, BigDecimal::add);
                            break; // 一旦找到匹配的父路径，就不需要继续检查其他父路径
                        }
                    }
                }
            }
            
            // 验证分组数量约束
            for (Map.Entry<String, BigDecimal> entry : groupCount.entrySet()) {
                String groupId = entry.getKey();
                BigDecimal count = entry.getValue();
                IObjectData groupData = groupMap.get(groupId);
                
                if (groupData != null) {
                    BigDecimal min = groupData.get(BomConstants.MIN_AMOUNT_COUNT, BigDecimal.class);
                    BigDecimal max = groupData.get(BomConstants.MAX_AMOUNT_COUNT, BigDecimal.class);
                    
                    if (min != null && count.compareTo(min.multiply(rootNodeAmount)) < 0) {
                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_MIN_AMOUNT_WARN, groupData.getName()));
                    }
                    
                    if (max != null && count.compareTo(max.multiply(rootNodeAmount)) > 0) {
                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_MAX_AMOUNT_WARN, groupData.getName()));
                    }
                }
            }
        }
    }

    private void checkGroupRequired(List<IObjectData> boms, List<CheckProductBomModel.ProductBom> bomList, Map<String, String> rootBomIdToProductName, List<IObjectData> groupDataList) {
        List<String> rootIds = bomList.stream().filter(x -> StringUtils.isNotEmpty(x.getRootBomId()))
                .map(CheckProductBomModel.ProductBom::getRootBomId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rootIds)) {
            return;
        }
        List<IObjectData> groupMinBomCount = queryRequiredGroup(bomList, rootIds, boms, groupDataList);

        Map<String, String> bomIdToRootId = boms.stream()
                .collect(Collectors.toMap(IObjectData::getId, o -> o.get(BomConstants.FIELD_ROOT_ID, String.class), (v1, v2) -> v1));
        rootBomIdToProductName.forEach((k, v) -> bomIdToRootId.put(k, k));
        if (CollectionUtils.isEmpty(groupMinBomCount)) {
            return;
        }
        Map<String, String> bomGroupName = groupMinBomCount.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName));
        Map<String, String> bomGroupIdToProductName = Maps.newHashMap();
        groupMinBomCount.forEach(o -> {
            String groupId = o.getId();
            String groupName = o.getName();
            bomGroupName.put(groupId, groupName);

            String parentBomId = o.get(BomConstants.FIELD_PARENT_BOM_ID, String.class);
            String rootBomId = bomIdToRootId.get(parentBomId);
            String rootBomProductName = rootBomIdToProductName.get(rootBomId);
            bomGroupIdToProductName.put(groupId, rootBomProductName);
        });

        // 获取 分组对应的bom数量
        Map<String, Long> groupBomMap = Maps.newHashMap();
        for (CheckProductBomModel.ProductBom bom : bomList) {
            List<IObjectData> objectData = ObjectDataDocument.ofDataList(bom.getSubBomList());
            groupBomMap.putAll(objectData.stream()
                    .filter(o -> StringUtils.isNotEmpty(o.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)))
                    .collect(Collectors.groupingBy(x -> x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)
                            , Collectors.counting())));
        }
        Map<String, List<IObjectData>> groupBomListMap = boms.stream()
                .filter(o -> StringUtils.isNotEmpty(o.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)))
                .collect(Collectors.groupingBy(x -> x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)));
        // 只校验是否大于0。不校验具体数量的对比
        List<String> requiredGroupList = Lists.newArrayList();
        StringJoiner emptyGroupMsg = new StringJoiner(";");
        for (IObjectData data : groupMinBomCount) {
            int bomSize = groupBomMap.getOrDefault(data.getId(), 0L).intValue();
            int bomC = groupBomMap.getOrDefault(data.getName(), 0L).intValue();
            if (bomSize == 0 && bomC == 0) {
                List<IObjectData> tmpList = groupBomListMap.get(data.getId());
                boolean flag = false;
                if (CollectionUtils.isNotEmpty(tmpList)) {
                    for (IObjectData d : tmpList) {
                        if (d.get(BomConstants.FIELD_ENABLED_STATUS, Boolean.class, true) && ProductConstants.Status.ON.getStatus().equals(d.get(ProductConstants.PRODUCT_STATUS, String.class, "1"))) {
                            flag = true;
                            break;
                        }
                    }
                    if (!flag) {
                        if (StringUtils.isBlank(bomGroupIdToProductName.get(data.getId()))) {
                            emptyGroupMsg.add(bomGroupName.get(data.getId()));
                        } else {
                            emptyGroupMsg.add(bomGroupIdToProductName.get(data.getId()).concat(".")
                                    .concat(bomGroupName.get(data.getId())));
                        }
                    }
                }
                if (flag) {
                    String message = "";
                    if (StringUtils.isBlank(bomGroupIdToProductName.get(data.getId()))) {
                        message = bomGroupName.get(data.getId());
                    } else {
                        message = bomGroupIdToProductName.get(data.getId()).concat(".")
                                .concat(bomGroupName.get(data.getId()));
                    }
                    requiredGroupList.add(message);
                }
            }
        }
        if (StringUtils.isNotBlank(emptyGroupMsg.toString())) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_EMPTY_GROUP_REQUIRED_PRODUCT, emptyGroupMsg.toString()));
        }
        String errorMessage = Joiner.on(";").join(requiredGroupList);
        if (CollectionUtils.isNotEmpty(requiredGroupList)) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_REQUIRED_PRODUCT, errorMessage));
        }

    }
    
    private void checkGroupRequiredNew(List<IObjectData> boms, List<CheckProductBomModel.ProductBom> bomList, Map<String, String> rootBomIdToProductName, List<IObjectData> groupDataList) {
        List<String> rootIds = Lists.newArrayList();
        List<IObjectData> allSubBoms = Lists.newArrayList();
        // 只遍历一次bomList，收集所有子BOM
        for (CheckProductBomModel.ProductBom bom : bomList) {
            allSubBoms.addAll(ObjectDataDocument.ofDataList(bom.getSubBomList()));
            if(StringUtils.isNotEmpty(bom.getRootBomId())) {
                rootIds.add(bom.getRootBomId());
            }
        }

        if (CollectionUtils.isEmpty(rootIds)) {
            return;
        }
        List<IObjectData> groupMinBomCount = queryRequiredGroup(bomList, rootIds, boms, groupDataList);

        Map<String, String> bomIdToRootId = boms.stream()
                .collect(Collectors.toMap(IObjectData::getId, o -> o.get(BomConstants.FIELD_ROOT_ID, String.class), (v1, v2) -> v1));
        rootBomIdToProductName.forEach((k, v) -> bomIdToRootId.put(k, k));
        if (CollectionUtils.isEmpty(groupMinBomCount)) {
            return;
        }
        
        // 预处理分组名称映射
        Map<String, String> bomGroupName = groupMinBomCount.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName));
        Map<String, String> bomGroupIdToProductName = Maps.newHashMap();
        
        // 一次性处理所有分组的产品名称映射
        for (IObjectData groupData : groupMinBomCount) {
            String groupId = groupData.getId();
            String parentBomId = groupData.get(BomConstants.FIELD_PARENT_BOM_ID, String.class);
            String rootBomId = bomIdToRootId.get(parentBomId);
            String rootBomProductName = rootBomIdToProductName.get(rootBomId);
            bomGroupIdToProductName.put(groupId, rootBomProductName);
        }

        // 一次性处理所有bomList，构建分组对应的bom数量映射
        Map<String, Long> groupBomMap = new HashMap<>();

        
        // 一次性处理所有子BOM的分组统计
        groupBomMap.putAll(allSubBoms.stream()
                .filter(o -> StringUtils.isNotEmpty(o.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)))
                .collect(Collectors.groupingBy(x -> x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class), 
                        Collectors.counting())));
        
        // 一次性构建分组ID到BOM列表的映射
        Map<String, List<IObjectData>> groupBomListMap = boms.stream()
                .filter(o -> StringUtils.isNotEmpty(o.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)))
                .collect(Collectors.groupingBy(x -> x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)));
        
        // 校验逻辑
        List<String> requiredGroupList = new ArrayList<>();
        StringJoiner emptyGroupMsg = new StringJoiner(";");
        
        for (IObjectData data : groupMinBomCount) {
            String groupId = data.getId();
            int bomSize = groupBomMap.getOrDefault(groupId, 0L).intValue();
            int bomC = groupBomMap.getOrDefault(data.getName(), 0L).intValue();
            
            if (bomSize == 0 && bomC == 0) {
                List<IObjectData> tmpList = groupBomListMap.get(groupId);
                boolean flag = false;
                
                if (CollectionUtils.isNotEmpty(tmpList)) {
                    // 使用流操作替代循环，提高性能
                    flag = tmpList.stream().anyMatch(d -> 
                        d.get(BomConstants.FIELD_ENABLED_STATUS, Boolean.class, true) && 
                        ProductConstants.Status.ON.getStatus().equals(d.get(ProductConstants.PRODUCT_STATUS, String.class, "1"))
                    );
                    
                    if (!flag) {
                        String groupName = bomGroupName.get(groupId);
                        String productName = bomGroupIdToProductName.get(groupId);
                        
                        if (StringUtils.isBlank(productName)) {
                            emptyGroupMsg.add(groupName);
                        } else {
                            emptyGroupMsg.add(productName + "." + groupName);
                        }
                    }
                }
                
                if (flag) {
                    String groupName = bomGroupName.get(groupId);
                    String productName = bomGroupIdToProductName.get(groupId);
                    String message = StringUtils.isBlank(productName) ? groupName : productName + "." + groupName;
                    requiredGroupList.add(message);
                }
            }
        }
        
        // 抛出异常处理
        if (StringUtils.isNotBlank(emptyGroupMsg.toString())) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_EMPTY_GROUP_REQUIRED_PRODUCT, emptyGroupMsg.toString()));
        }
        
        if (CollectionUtils.isNotEmpty(requiredGroupList)) {
            String errorMessage = Joiner.on(";").join(requiredGroupList);
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_REQUIRED_PRODUCT, errorMessage));
        }
    }

    /**
     * @param bomList       前端传过来的数据
     * @param rootIds       根bomId
     * @param boms          数据库查出来的数据
     * @param groupDataList 分组数据
     * @return
     */
    @Nullable
    private List<IObjectData> queryRequiredGroup(List<CheckProductBomModel.ProductBom> bomList, List<String> rootIds, List<IObjectData> boms, List<IObjectData> groupDataList) {
        List<IObjectData> subList = Lists.newArrayList();
        bomList.forEach(x -> x.getSubBomList().forEach(y -> subList.add(y.toObjectData())));
        List<IObjectData> requiredGroups = groupDataList.stream().filter(x -> {
            Integer minProdCount = x.get("min_prod_count", Integer.class);
            return checkGroupNum(minProdCount);
        }).collect(Collectors.toList());
        //过滤出需要校验的分组
        List<String> requiredGroupIds = requiredGroups.stream().map(DBRecord::getId).collect(Collectors.toList());

        boms.stream().filter(x -> requiredGroupIds.contains(x.get(BomConstants.FIELD_PRODUCT_GROUP_ID))).forEach(x -> x.set("group_required", true));
        Set<String> groupIds = Sets.newHashSet();
        //查找根节点下的必填
        List<IObjectData> bomGroupRequired = boms.stream().filter(x -> rootIds.contains(x.get(BomConstants.FIELD_PARENT_BOM_ID)))
                .filter(x -> BooleanUtils.isTrue(x.get("group_required", Boolean.class))).collect(Collectors.toList());
        bomGroupRequired.forEach(x -> groupIds.add(x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)));
        //查找子节点下的必填
        subList.stream().filter(x -> StringUtils.isNotBlank(x.getId())).map(DBRecord::getId).forEach(x -> recursiveGetSubBoms(boms, x, groupIds));
        if (groupIds.isEmpty()) {
            return null;
        }
        return groupDataList.stream().filter(x -> groupIds.contains(x.getId())).collect(Collectors.toList());
    }

    private List<IObjectData> queryBomByCond(User user, List<String> rootIds, boolean isBomMasterSlaveMode, List<String> coreIdList) {
        List<IObjectData> dataList = Lists.newArrayList();
        IActionContext context = ActionContextExt.of(user).allowUpdateInvalid(false).setSkipRelevantTeam(true).getContext();
        context.setDoCalculate(true);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setLimit(2000);
        List<IFilter> filters = Lists.newArrayList();
        if (rootIds.size() == 1) {
            SearchUtil.fillFilterEq(filters, BomConstants.FIELD_ROOT_ID, rootIds.get(0));
        } else {
            SearchUtil.fillFilterIn(filters, BomConstants.FIELD_ROOT_ID, rootIds);
        }
        if (isBomMasterSlaveMode) {
            SearchUtil.fillFilterIsNotNull(filters, BomConstants.FIELD_PARENT_BOM_ID);
        }
        SearchUtil.fillFilterEq(filters, "is_deleted", 0);
        searchTemplateQuery.setFilters(filters);
        int offset = 0;
        int loopCnt = 0;
        QueryResult<IObjectData> queryResult;
        while (loopCnt < 10) {
            searchTemplateQuery.setOffset(offset);
            queryResult = serviceFacade.findBySearchQuery(context
                    , Utils.BOM_API_NAME, searchTemplateQuery);
            List resultData = queryResult == null ? Lists.newArrayList() : queryResult.getData();
            if (CollectionUtils.isNotEmpty(resultData)) {
                dataList.addAll(resultData);
            }
            if (CollectionUtils.isEmpty(resultData) || resultData.size() < 2000) {
                break;
            }
            offset += 2000;
            loopCnt++;
        }
        if (isBomMasterSlaveMode && CollectionUtils.isNotEmpty(coreIdList)) {
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setPermissionType(0);
            query.setNeedReturnCountNum(false);
            query.setLimit(2000);
            List<IFilter> filterList = Lists.newArrayList();
            if (coreIdList.size() == 1) {
                SearchUtil.fillFilterEq(filterList, BomConstants.FIELD_CORE_ID, coreIdList.get(0));
            } else {
                SearchUtil.fillFilterIn(filterList, BomConstants.FIELD_CORE_ID, coreIdList);
            }
            SearchUtil.fillFilterIsNotNull(filterList, BomConstants.FIELD_PARENT_BOM_ID);
            SearchUtil.fillFilterEq(filterList, "is_deleted", 0);
            query.setFilters(filterList);
            int offsetNew = 0;
            int loopCntNew = 0;
            QueryResult<IObjectData> result;
            while (loopCntNew < 10) {
                query.setOffset(offsetNew);
                result = serviceFacade.findBySearchQuery(context
                        , Utils.BOM_API_NAME, query);
                List resultData = result == null ? Lists.newArrayList() : result.getData();
                if (CollectionUtils.isNotEmpty(resultData)) {
                    dataList.addAll(resultData);
                }
                if (CollectionUtils.isEmpty(resultData) || resultData.size() < 2000) {
                    break;
                }
                offsetNew += 2000;
                loopCntNew++;
            }
        }
        return dataList;
    }


    public String getNewBomPath(IObjectData bomData, String newBomPath) {
        String toReplaceBomPath = bomData.get(BomConstants.FIELD_BOM_PATH, String.class, "");
        if (StringUtils.isNotBlank(newBomPath) && StringUtils.isNotBlank(toReplaceBomPath)) {
            int index = toReplaceBomPath.indexOf(".");
            if (index > 0) {
                toReplaceBomPath = newBomPath + toReplaceBomPath.substring(index);
            } else {
                toReplaceBomPath = newBomPath + "." + toReplaceBomPath;
            }
        }
        return toReplaceBomPath;
    }

    /**
     * 根据coreId查询Bom数据,不包括虚拟的那条BOM（BomCore主表的数据也会存入BomObj里，这条数据要过滤掉）
     *
     * @param bomCoreId
     * @param user
     * @param filterRelatedCoreBom 是否过滤掉复用BOM的数据
     * @param needCalculate        是否需要计算引用关系
     * @return
     */
    public List<IObjectData> getBomListByCoreId(String bomCoreId, User user, boolean filterRelatedCoreBom, boolean needCalculate) {
        IActionContext context = ActionContextExt.of(user).allowUpdateInvalid(false).setSkipRelevantTeam(true).getContext();
        context.setDoCalculate(needCalculate);
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setPermissionType(0);
        query.setNeedReturnCountNum(false);
        query.setLimit(2000);

        //根据BomCoreId查询对应Bom数据（BomCore 存储时会将主对象也存储到Bom表里，确保根原来的逻辑一致）
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "life_status", "normal");
        SearchUtil.fillFilterEq(filters, "is_deleted", "0");

        SearchUtil.fillFilterEq(filters, BomConstants.FIELD_CORE_ID, bomCoreId);

        SearchUtil.fillFilterIsNotNull(filters, BomConstants.FIELD_PARENT_BOM_ID);
        //只查询复用BOM的数据
        if (filterRelatedCoreBom) {
            SearchUtil.fillFilterIsNotNull(filters, BomConstants.FIELD_RELATED_CORE_ID);
        }
        query.setFilters(filters);
        QueryResult<IObjectData> rootBomList = serviceFacade.findBySearchQuery(context, Utils.BOM_API_NAME, query);
        return rootBomList.getData();
    }

    @Override
    public IObjectData getRootBomByCoreId(String bomCoreId, User user, boolean needCalculate) {
        IActionContext context = ActionContextExt.of(user).allowUpdateInvalid(false).setSkipRelevantTeam(true).getContext();
        context.setDoCalculate(needCalculate);
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setPermissionType(0);
        query.setNeedReturnCountNum(false);
        query.setLimit(1);

        //根据BomCoreId查询对应Bom数据（BomCore 存储时会将主对象也存储到Bom表里，确保根原来的逻辑一致）
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "life_status", "normal");
        SearchUtil.fillFilterEq(filters, "is_deleted", "0");

        SearchUtil.fillFilterEq(filters, BomConstants.FIELD_CORE_ID, bomCoreId);

        SearchUtil.fillFilterIsNull(filters, BomConstants.FIELD_PARENT_BOM_ID);

        query.setFilters(filters);
        QueryResult<IObjectData> rootBomList = serviceFacade.findBySearchQuery(context, Utils.BOM_API_NAME, query);
        if (CollectionUtils.isNotEmpty(rootBomList.getData())) {
            return rootBomList.getData().get(0);
        } else {
            return null;
        }
    }

    private boolean checkGroupNum(Integer minProdCount) {
        return minProdCount != null && minProdCount != 0;
    }


    private void recursiveGetSubBoms(List<IObjectData> boms, String parentBomId, Set<String> groupIds) {
        List<IObjectData> subBoms = boms.stream().filter(y -> parentBomId.equals(y.get(BomConstants.FIELD_PARENT_BOM_ID, String.class))).collect(Collectors.toList());
        subBoms.stream().filter(x -> StringUtils.isNotBlank(x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)) && BooleanUtils.isTrue(x.get("group_required", Boolean.class))).forEach(x -> groupIds.add(x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)));
    }


    /**
     * @param user
     * @param bomDetail 前端传过来的BOM数据
     * @param groupMap  数据库查出来的产品组数据数据
     * @param dbBomMap  根据root_id查询出来的bom数据
     * @param idMap
     * @return
     */
    private CheckProductBomModel.SingleResult checkSingleBom(User user, CheckProductBomModel.ProductBom bomDetail
            , Map<String, IObjectData> groupMap, Map<String, List<IObjectData>> dbBomMap, Boolean selectionPage, Map<String, IObjectData> idMap) {
        if (CollectionUtils.isEmpty(bomDetail.getSubBomList())) {
            return new CheckProductBomModel.SingleResult();
        }
        if (StringUtils.isAnyBlank(bomDetail.getRootProductId(), bomDetail.getRootProdKey(), bomDetail.getRootBomId())) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        CheckProductBomModel.SingleResult result = new CheckProductBomModel.SingleResult();
        String rootBomId = bomDetail.getRootBomId();
        String rootProductKey = bomDetail.getRootProdKey();
        List<IObjectData> bomList = ObjectDataDocument.ofDataList(bomDetail.getSubBomList());

        List<IObjectData> dbBomList = dbBomMap.getOrDefault(rootBomId, Lists.newArrayList());

        checkTreeStructure(rootProductKey, bomList);

        fillBomListByDB(user.getTenantId(), result, bomList, dbBomList, selectionPage, idMap);

        //2.获取根产品下默认勾选的子bom

        List<IObjectData> dbAndNewSubBomList = findDefaultSelectedBomList(dbBomList);

        if (CollectionUtils.isNotEmpty(dbAndNewSubBomList)) {
            if (GrayUtil.bomMasterSlaveMode(user.getTenantId())) {
                Map<String, IObjectData> bomMap = bomList.stream().collect(Collectors.toMap(x -> x.get(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, String.class), Function.identity(), (v1, v2) -> v1));
                dbAndNewSubBomList.forEach(x -> {
                    IObjectData tmpData = bomMap.get(x.get(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, String.class));
                    if (Objects.nonNull(tmpData)) {
                        x.set("amount", tmpData.get("amount", String.class));
                    }
                });
            } else {
                Map<String, IObjectData> bomMap = bomList.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity(), (v1, v2) -> v1));
                dbAndNewSubBomList.forEach(x -> {
                    IObjectData tmpData = bomMap.get(x.getId());
                    if (Objects.nonNull(tmpData)) {
                        x.set("amount", tmpData.get("amount", String.class));
                    }
                });
            }
        }
        //加入其它非正常状态的数据
        dbAndNewSubBomList.addAll(bomList.stream().filter(o -> !ProductConstants.Status.ON.getStatus().equals(o.get("product_status", String.class))
                || !"normal".equals(o.get("product_life_status", String.class)) || !toBoolean(o.get("enabled_status"), Boolean.FALSE)).collect(Collectors.toList()));
        //校验层级完整性
        checkBomTree(bomList, dbBomList, rootBomId, result);
        //1.校验子产品明细的数量：最小数量+幅度*n=数量，数量要在最小最大之间
        checkSubBomAmount(user, bomList, result, selectionPage);

        //2.校验子产品分组的数量和选项控制
        if(GrayUtil.isBigBomTenant(user.getTenantId())) {
            checkGroupRuleNew(groupMap, bomList, result);
        } else {
            checkGroupRule(groupMap, bomList, result);
        }
        //3.校验子产品明细的必填
        checkSubBomIsRequired(bomList, dbAndNewSubBomList, rootBomId, result, dbBomList);

        //5.校验子产品明细是否是上架、正常
        checkSubProductStatus(bomList, result, rootBomId);

        //6.校验主子件价目表是否一致
        checkNodePriceBookRange(bomDetail.getPriceBookId(), bomList, user.getTenantId(), result);

        //入参的子产品明细和默认选中的子产品明细集合
        Map<String, IObjectData> allSubPros = Maps.newHashMap();
        bomList.forEach(o -> allSubPros.put(o.get(BomConstants.FIELD_PRODUCT_ID).toString(), o));
        dbAndNewSubBomList.forEach(o -> allSubPros.put(o.get(BomConstants.FIELD_PRODUCT_ID, String.class), o));
        fillResultData(user, bomDetail.getRootProductId(), result, groupMap, allSubPros);

        return result;
    }

    private void checkNodePriceBookRange(String priceBookId, List<IObjectData> bomList, String tenantId, CheckProductBomModel.SingleResult result) {
        if (!GrayUtil.filterBomAccordingPriceBook(tenantId) || CollectionUtils.isEmpty(bomList) || StringUtils.isBlank(priceBookId)) {
            return;
        }
        bomList.forEach(x -> {
            if (!Objects.equals(x.get(BomConstants.FIELD_PRICE_BOOK_ID, String.class), priceBookId) &&
                    StringUtils.isNotBlank(x.get(BomConstants.FIELD_PRICE_MODE, String.class)) &&
                    x.get(BomConstants.FIELD_PRICE_MODE, Integer.class, 0) == EnumUtil.PriceMode.PRICE_BOOK.getValue()) {
                buildErrorList(result, true, x.get(BomConstants.FIELD_PRODUCT_ID, String.class), I18N.text(BomI18NKeyUtil.SFA_BOM_PRICE_RANGE_WARN));
                result.setCheckSingleResult(I18N.text(BomI18NKeyUtil.SFA_BOM_PRICE_RANGE_WARN));
            }
        });
    }


    private void checkBomTree(List<IObjectData> bomList, List<IObjectData> dbBomList, String rootBomId, CheckProductBomModel.SingleResult result) {
        List<String> bomIds = bomList.stream()
                .filter(x -> !Objects.equals(EnumUtil.nodeType.temp.getValue(), x.get(BomConstants.FIELD_NODE_TYPE, String.class)))
                .map(IObjectData::getId).distinct().collect(Collectors.toList());
        Map<String, IObjectData> dbBomMap = dbBomList.stream()
                .filter(x -> StringUtils.isNotBlank(x.get(BomConstants.FIELD_BOM_PATH, String.class)))
                .collect(Collectors.toMap(IObjectData::getId, Function.identity(), (v1, v2) -> v1));
        List<String> bomIdList = Lists.newArrayList();
        bomIds.forEach(x -> {
            IObjectData bom = dbBomMap.get(x);
            if (Objects.nonNull(bom)) {
                String bomPath = bom.get(BomConstants.FIELD_BOM_PATH, String.class);
                if (StringUtils.isNotBlank(bomPath)) {
                    List<String> tmpList = Splitter.on(".").splitToList(bomPath);
                    if (CollectionUtils.isNotEmpty(tmpList)) {
                        bomIdList.addAll(tmpList);
                    }
                }
            }
        });
        List<String> ids = bomIdList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        ids.removeIf(x -> bomIds.contains(x) || Objects.equals(x, rootBomId));
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        ids.forEach(x -> {
            IObjectData bom = dbBomMap.get(x);
            if (Objects.nonNull(bom)) {
                buildErrorList(result, true, bom.get(BomConstants.FIELD_PRODUCT_ID, String.class), I18N.text(BomI18NKeyUtil.SFA_BOM_INVALID_TREE_STRUCTURE));
                result.setCheckSingleResult(I18N.text(BomI18NKeyUtil.SFA_BOM_INVALID_TREE_STRUCTURE));
            }
        });


    }

    private void checkTreeStructure(String rootProductKey, List<IObjectData> bomList) {
        //校验树形的完整性
        Set<String> parentKey = bomList.stream().filter(x -> x.get(DetailProductConstants.FIELD_PARENT_PROD_PKG_KEY) != null && StringUtils.isNotBlank(x.get(DetailProductConstants.FIELD_PARENT_PROD_PKG_KEY, String.class)))
                .map(x -> x.get(DetailProductConstants.FIELD_PARENT_PROD_PKG_KEY, String.class)).collect(Collectors.toSet());
        parentKey.forEach(x -> {
            if (!x.equals(rootProductKey) && bomList.stream().filter(y -> y.get(DetailProductConstants.FIELD_PROD_PKG_KEY).toString().equals(x)).count() == 0) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_INVALID_TREE_STRUCTURE));
            }
        });
    }

    //根据修正补全数据
    private void fillBomListByDB(String tenantId, CheckProductBomModel.SingleResult result, List<IObjectData> subBomList, List<IObjectData> dbBomDataList, Boolean selectionPage, Map<String, IObjectData> idMap) {
        if (CollectionUtils.isEmpty(subBomList)) {
            return;
        }
        Map<String, IObjectData> productMap = Maps.newHashMap();
        Map<String, IObjectData> dbBomDataMap =
                dbBomDataList.stream().collect(Collectors.toMap(DBRecord::getId, x -> x, (x1, x2) -> x1));

        List<String> productIds = subBomList.stream().filter(x -> Objects.equals(EnumUtil.nodeType.temp.getValue(), x.get(BomConstants.FIELD_NODE_TYPE, String.class)))
                .map(x -> x.get(BomConstants.FIELD_PRODUCT_ID, String.class)).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(productIds)) {
            List<IObjectData> productList = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, productIds, Utils.PRODUCT_API_NAME);
            if (CollectionUtils.isNotEmpty(productList)) {
                productMap = productList.stream().collect(Collectors.toMap(DBRecord::getId, Function.identity(), (k1, k2) -> k1));
            }
        }
        log.info("fillBomListByDB subBomList:{}", JSON.toJSONString(subBomList));
        log.info("fillBomListByDB dbBomDataMap:{}", JSON.toJSONString(dbBomDataMap));
        IObjectDescribe bomDescribe = serviceFacade.findObject(tenantId, Utils.BOM_API_NAME);
        for (IObjectData data : subBomList) {
            String id = data.getId();
            if (data.get(DetailProductConstants.FIELD_PARENT_PROD_PKG_KEY) == null) {
                data.set(DetailProductConstants.FIELD_PARENT_PROD_PKG_KEY, "");
            }
            IObjectData product = productMap.get(data.get(BomConstants.FIELD_PRODUCT_ID));
            IObjectData bomObj = Objects.nonNull(dbBomDataMap.get(id)) ? dbBomDataMap.get(id) : idMap.get(id);
            if (Objects.nonNull(bomObj)) {
                if (bomObj.get(BomConstants.FIELD_IS_PACKAGE, Boolean.class, false)
                        && StringUtils.isBlank(data.get(BomConstants.FIELD_RELATED_CORE_ID, String.class))
                        && GrayUtil.bomMasterSlaveMode(tenantId) && BooleanUtils.isTrue(selectionPage)) {
                    buildErrorList(result, true, data.get(BomConstants.FIELD_PRODUCT_ID).toString(), I18N.text(BomI18NKeyUtil.SFA_BOM_NODE_VERSION_WARN));
                    result.setCheckSingleResult(I18N.text(BomI18NKeyUtil.SFA_BOM_NODE_VERSION_WARN));
                }
                data.set("amountDB", bomObj.get("amount"));
                data.set("product_status", bomObj.get("product_status"));
                data.set("product_life_status", bomObj.get("product_life_status"));
                if (StringUtils.isEmpty(data.get("price_book_id", String.class))) {
                    data.set("adjust_price", bomObj.get("adjust_price"));
                }
                data.set(BomConstants.FIELD_AMOUNT_ANY, bomObj.get(BomConstants.FIELD_AMOUNT_ANY));
                data.set("is_required", bomObj.get("is_required"));
                data.set("selected_by_default", bomObj.get("selected_by_default"));
                data.set("price_editable", bomObj.get("price_editable"));
                data.set("amount_editable", bomObj.get("amount_editable"));
                data.set("min_amount", bomObj.get("min_amount"));
                data.set("max_amount", bomObj.get("max_amount"));
                data.set("increment", bomObj.get("increment"));
                data.set("enabled_status", bomObj.get("enabled_status"));
                data.set(BomConstants.FIELD_PRICE_MODE, bomObj.get(BomConstants.FIELD_PRICE_MODE));
                data.set(BomConstants.FIELD_PARENT_BOM_ID, bomObj.get(BomConstants.FIELD_PARENT_BOM_ID));
                data.set(BomConstants.FIELD_CORE_ID, bomObj.get(BomConstants.FIELD_CORE_ID));
            } else if (Objects.equals(EnumUtil.nodeType.temp.getValue(), data.get(BomConstants.FIELD_NODE_TYPE, String.class))) {
                if (Objects.isNull(product)) {
                    buildErrorList(result, true, data.get(BomConstants.FIELD_PRODUCT_ID).toString(), I18N.text(BomI18NKeyUtil.SFA_BOM_NODE_DELETED));
                    result.setCheckSingleResult(I18N.text(BomI18NKeyUtil.SFA_BOM_NODE_DELETED));
                } else {
                    if (Objects.nonNull(bomDescribe)&&Objects.nonNull(bomDescribe.getFieldDescribe(BomConstants.FIELD_AMOUNT_ANY))) {
                        data.set(BomConstants.FIELD_AMOUNT_ANY, bomDescribe.getFieldDescribe(BomConstants.FIELD_AMOUNT_ANY).getDefaultValue());
                    }
                    data.set("product_status", product.get("product_status"));
                    data.set("product_life_status", product.get("life_status"));
                    data.set("enabled_status", true);
                    data.set("is_required", false);
                    data.set("selected_by_default", false);
                    data.set("amountDB", 1);
                    data.set("price_editable", true);
                    data.set("amount_editable", true);
                }
            } else {
                buildErrorList(result, true, data.get(BomConstants.FIELD_PRODUCT_ID).toString(), I18N.text(BomI18NKeyUtil.SFA_BOM_NODE_DELETED));
                result.setCheckSingleResult(I18N.text(BomI18NKeyUtil.SFA_BOM_NODE_DELETED));
            }
        }
    }

    private List<IObjectData> findDefaultSelectedBomList(List<IObjectData> bomRootList) {
        if (CollectionUtils.isEmpty(bomRootList)) {
            return Lists.newArrayList();
        }
        return bomRootList.stream().filter(x ->
                BooleanUtils.isTrue(x.get("selected_by_default", Boolean.class, false)) &&
                        Objects.equals("1", x.get("product_status", String.class)) &&
                        Objects.equals("normal", x.get("product_life_status", String.class)) &&
                        BooleanUtils.isTrue(x.get("enabled_status", Boolean.class, false))
        ).collect(Collectors.toList());
    }

    private List<IObjectData> findProductCatalogList(User user, List<String> productGoupIds) {
        if (CollectionUtils.isEmpty(productGoupIds)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchQuery = bomCoreService.getNewTemplateQuery();
        List<IFilter> filters = searchQuery.getFilters();
        SearchUtil.fillFilterIn(filters, DBRecord.ID, productGoupIds);
        QueryResult<IObjectData> result = serviceFacade.findBySearchQueryIgnoreAll(user, Utils.PRODUCT_GROUP_API_NAME, searchQuery);
        return result.getData();
    }

    private void checkSubBomAmount(User user, List<IObjectData> subBomList, CheckProductBomModel.SingleResult result, Boolean selectionPage) {
        if (Objects.equals("1", configService.findTenantConfig(user, ConfigType.SKIP_BOM_NUMBER.getKey()))) {
            return;
        }
        Map<String, String> relateMap = new HashMap<>();
        if (BooleanUtils.isNotTrue(selectionPage)) {
            for (IObjectData x : subBomList) {
                if (StringUtils.isNotBlank(x.get(BomConstants.FIELD_BOM_CORE_ID, String.class))) {
                    relateMap.put(x.get(BomConstants.FIELD_BOM_CORE_ID, String.class), x.get(BomConstants.FIELD_AMOUNT, String.class, "1"));
                }
            }
        }
        for (IObjectData data : subBomList) {
            if (StringUtils.isBlank(data.get("amount", String.class))) {
                continue;
            }
            boolean amountAny = data.get(BomConstants.FIELD_AMOUNT_ANY, Boolean.class, false);
            Double amount = toDouble(data.get("amount"), 1);
            String rootAmount = relateMap.get(data.get(BomConstants.FIELD_CORE_ID, String.class));
            if (!amountAny) {
                if (StringUtils.isNotBlank(rootAmount)) {
                    if (!canExactDivisor(amount, new BigDecimal(rootAmount).doubleValue(), data.get(BomConstants.ROOT_BOM_QUANTITY_PLACES, Integer.class, 0))) {
                        buildErrorList(result, true, data.get(BomConstants.FIELD_PRODUCT_ID).toString(), I18N.text(BomI18NKeyUtil.SFA_BOM_NUMBER_LIMIT));
                        result.setCheckSingleResult(I18N.text(BomI18NKeyUtil.SFA_BOM_NUMBER_LIMIT));
                        continue;
                    } else {
                        amount = (new BigDecimal(data.get("amount", String.class)).divide(new BigDecimal(rootAmount), 14, BigDecimal.ROUND_HALF_UP)).doubleValue();
                    }
                } else {
                    String rootQuantity = data.get(BomConstants.ROOT_BOM_QUANTITY, String.class);
                    if (StringUtils.isNotBlank(rootQuantity)) {
                        if (!canExactDivisor(new BigDecimal(data.get("amount", String.class)).doubleValue(), new BigDecimal(rootQuantity).doubleValue(), data.get(BomConstants.ROOT_BOM_QUANTITY_PLACES, Integer.class, 0))) {
                            buildErrorList(result, true, data.get(BomConstants.FIELD_PRODUCT_ID).toString(), I18N.text(BomI18NKeyUtil.SFA_BOM_NUMBER_LIMIT));
                            result.setCheckSingleResult(I18N.text(BomI18NKeyUtil.SFA_BOM_NUMBER_LIMIT));
                            continue;
                        } else {
                            amount = (new BigDecimal(data.get("amount", String.class)).divide(new BigDecimal(rootQuantity), 14, BigDecimal.ROUND_HALF_UP)).doubleValue();
                        }
                    }
                }
            }
            Double minAmount = toDouble(data.get("min_amount"), MinValue);
            Double maxAmount = toDouble(data.get("max_amount"), MaxValue);
            Object objIncrement = data.get("increment");
            Double increment = toDouble(objIncrement, 1);


            //1.最小数量存在的校验
            if (minAmount > MinValue && amount < minAmount) {
                buildErrorList(result, true, data.get(BomConstants.FIELD_PRODUCT_ID).toString(), I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_COUNT));
                result.setCheckSingleResult(I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_COUNT));
            }
            if (maxAmount < MaxValue && amount > maxAmount) {
                buildErrorList(result, true, data.get(BomConstants.FIELD_PRODUCT_ID).toString(), I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_COUNT_MAX));
                result.setCheckSingleResult(I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_COUNT_MAX));
            }

            if (!Objects.isNull(objIncrement) && Strings.isNotBlank(objIncrement.toString()) && increment > MinValue && !canExactDivisor(amount, increment)) {
                buildErrorList(result, true, data.get(BomConstants.FIELD_PRODUCT_ID).toString(), I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_COUNT_FORMULA));
                result.setCheckSingleResult(I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_COUNT_FORMULA));
            }
        }
    }

    private void checkGroupRule(Map<String, IObjectData> groupMap, List<IObjectData> subBomList, CheckProductBomModel.SingleResult result) {
        if (MapUtils.isEmpty(groupMap)) {
            return;
        }
        //跟进每个父节点,独立判断
        Map<String, List<IObjectData>> bomListByParent = subBomList.stream().collect(Collectors.groupingBy(o -> o.get(DetailProductConstants.FIELD_PARENT_PROD_PKG_KEY, String.class)));
        bomListByParent.forEach((parentId, dataList) -> {
            List<String> groupId = dataList.stream().filter(x -> x.get(BomConstants.FIELD_PRODUCT_GROUP_ID) != null && StringUtils.isNotBlank(x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)))
                    .map(x -> x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(groupId)) {
                return;
            }

            groupId.forEach(x -> {

                if (!groupMap.containsKey(x)) {
                    return;
                }
                IObjectData groupData = groupMap.get(x);
                Integer minCount = toInt(groupData.get("min_prod_count"), MinValue);
                Integer maxCount = toInt(groupData.get("max_prod_count"), MaxValue);
                String name = groupData.getName();

                long count = dataList.stream().filter(y -> x.equals(y.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class))).count();
                //1.判断分组内选项控制
                if (toBoolean(groupData.get("group_options_control"), Boolean.FALSE) && count > 1) {
                    buildErrorList(result, false, groupData.getId(), name + "," + I18N.text(BomI18NKeyUtil.SO_CHECK_PRO_PCK_GROUP_CONTROL));
                    result.setCheckSingleResult(name + "," + I18N.text(BomI18NKeyUtil.SO_CHECK_PRO_PCK_GROUP_CONTROL));
                }
                //2.分组内数量
                if (minCount > MinValue && count < minCount) {
                    buildErrorList(result, false, groupData.getId(), name + "," + I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_PRODUCT_COUNT_MIN, minCount));
                    result.setCheckSingleResult(name + "," + I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_PRODUCT_COUNT_MIN, minCount));
                }
                if (maxCount < MaxValue && count > maxCount) {
                    buildErrorList(result, false, groupData.getId(), name + "," + I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_PRODUCT_COUNT_MAX, maxCount));
                    result.setCheckSingleResult(name + "," + I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_PRODUCT_COUNT_MAX, maxCount));
                }
            });
        });
    }
    
    private void checkGroupRuleNew(Map<String, IObjectData> groupMap, List<IObjectData> subBomList, CheckProductBomModel.SingleResult result) {
        if (MapUtils.isEmpty(groupMap)) {
            return;
        }
        
        // 预先构建父节点到子节点的映射，避免重复遍历
        Map<String, Map<String, List<IObjectData>>> parentToGroupDataMap = new HashMap<>();
        
        // 一次性遍历所有subBomList，按父节点和分组ID进行分类
        for (IObjectData data : subBomList) {
            String parentId = data.get(DetailProductConstants.FIELD_PARENT_PROD_PKG_KEY, String.class);
            String groupId = data.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class);
            
            if (StringUtils.isBlank(groupId) || !groupMap.containsKey(groupId)) {
                continue;
            }
            
            parentToGroupDataMap
                .computeIfAbsent(parentId, k -> new HashMap<>())
                .computeIfAbsent(groupId, k -> new ArrayList<>())
                .add(data);
        }
        
        // 处理每个父节点下的分组
        for (Map.Entry<String, Map<String, List<IObjectData>>> parentEntry : parentToGroupDataMap.entrySet()) {
            Map<String, List<IObjectData>> groupToDataMap = parentEntry.getValue();
            
            // 处理每个分组
            for (Map.Entry<String, List<IObjectData>> groupEntry : groupToDataMap.entrySet()) {
                String groupId = groupEntry.getKey();
                List<IObjectData> groupDataList = groupEntry.getValue();
                int count = groupDataList.size();
                
                IObjectData groupData = groupMap.get(groupId);
                Integer minCount = toInt(groupData.get("min_prod_count"), MinValue);
                Integer maxCount = toInt(groupData.get("max_prod_count"), MaxValue);
                String name = groupData.getName();
                
                // 1.判断分组内选项控制
                if (toBoolean(groupData.get("group_options_control"), Boolean.FALSE) && count > 1) {
                    buildErrorList(result, false, groupData.getId(), name + "," + I18N.text(BomI18NKeyUtil.SO_CHECK_PRO_PCK_GROUP_CONTROL));
                    result.setCheckSingleResult(name + "," + I18N.text(BomI18NKeyUtil.SO_CHECK_PRO_PCK_GROUP_CONTROL));
                }
                
                // 2.分组内数量
                if (minCount > MinValue && count < minCount) {
                    buildErrorList(result, false, groupData.getId(), name + "," + I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_PRODUCT_COUNT_MIN, minCount));
                    result.setCheckSingleResult(name + "," + I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_PRODUCT_COUNT_MIN, minCount));
                }
                
                if (maxCount < MaxValue && count > maxCount) {
                    buildErrorList(result, false, groupData.getId(), name + "," + I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_PRODUCT_COUNT_MAX, maxCount));
                    result.setCheckSingleResult(name + "," + I18N.text(BomI18NKeyUtil.SFA_BOM_GROUP_PRODUCT_COUNT_MAX, maxCount));
                }
            }
        }
    }

    private void checkSubBomIsRequired(List<IObjectData> subBomList, List<IObjectData> dbSubBomList, String rootProductId, CheckProductBomModel.SingleResult result, List<IObjectData> dbBomList) {
        if (CollectionUtils.isEmpty(subBomList) || CollectionUtils.isEmpty(dbSubBomList)) {
            return;
        }
        List<String> subBomId = subBomList.stream().map(DBRecord::getId).collect(Collectors.toList());

        List<String> parentProductIds = dbBomList.stream().filter(x -> subBomId.contains(x.getId())).map(x -> x.get(BomConstants.FIELD_PARENT_BOM_ID, String.class)).collect(Collectors.toList());

        List<IObjectData> requiredDataList = dbSubBomList.stream().filter(x -> toBoolean(x.get("is_required"), Boolean.FALSE)).collect(Collectors.toList());

        for (IObjectData requiredData : requiredDataList) {
            String isRequiredParentProductId = requiredData.get(BomConstants.FIELD_PARENT_BOM_ID, String.class);
            if (!Objects.equals(isRequiredParentProductId, rootProductId)) {
                /**
                 * 父节点不是顶级根节点时
                 *  必选的父节点是否被选中，如果被选中
                 *      校验节点是否在bomList里面
                 *          不在提示异常
                 *  如果父节点未被选中
                 *      校验节点是否在bomList里面
                 *          在 提示异常
                 */
                if (parentProductIds.contains(isRequiredParentProductId) || subBomId.contains(isRequiredParentProductId)) {
                    if (!subBomId.contains(requiredData.getId())) {
                        buildErrorList(result, true, requiredData.get(BomConstants.FIELD_PRODUCT_ID).toString(), I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_IS_REQUIRED));
                        result.setCheckSingleResult(I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_IS_REQUIRED));
                    }
                }
            } else {
                if (!subBomId.contains(requiredData.getId())) {
                    buildErrorList(result, true, requiredData.get(BomConstants.FIELD_PRODUCT_ID).toString(), I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_IS_REQUIRED));
                    result.setCheckSingleResult(I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_IS_REQUIRED));
                }
            }
        }
    }


    private void checkSubProductStatus(List<IObjectData> subBomList, CheckProductBomModel.SingleResult result, String rootBomId) {
        for (IObjectData data : subBomList) {
            if (StringUtils.isNotBlank(rootBomId) && StringUtils.equalsAny(rootBomId, data.getId(), data.get(BomConstants.FIELD_BOM_ID, String.class))) {
                continue;
            }
            String productStatus = data.get("product_status", String.class);
            String productLifeStatus = data.get("product_life_status", String.class);

            if (!ProductConstants.Status.ON.getStatus().equals(productStatus) || !"normal".equals(productLifeStatus)) {
                log.warn("id: {}, coreId: {}, rootBomId: {}, productStatus: {}, productLifeStatus: {}", data.getId(), data.get(BomConstants.FIELD_CORE_ID, String.class), rootBomId, productStatus, productLifeStatus);
                buildErrorList(result, true, data.get(BomConstants.FIELD_PRODUCT_ID).toString(), I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_STATUS));
                result.setCheckSingleResult(I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_STATUS));
            }
        }
    }

    private List<IObjectData> getProductDataList(String tenantId, List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        return serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, idList, Utils.PRODUCT_API_NAME);
    }

    private void fillResultData(User user, String rootProductId, CheckProductBomModel.SingleResult result, Map<String, IObjectData> groupMap, Map<String, IObjectData> allSubPros) {
        Set<String> productIdList = Sets.newHashSet();
        productIdList.add(rootProductId);
        Map<String, String> subBomResultMap = result.getBomResultList();
        if (MapUtils.isNotEmpty(subBomResultMap)) {
            subBomResultMap.forEach((k, v) -> productIdList.add(k));
        }
        Map<String, String> productNameCacheMap = ProductConstraintUtil.getObjectName(user, productIdList, Utils.PRODUCT_API_NAME);
        String rootProductName = productNameCacheMap.containsKey(rootProductId) ? productNameCacheMap.get(rootProductId) : rootProductId;
        String strResult = result.getCheckSingleResult();
        if (StringUtils.isNotBlank(strResult)) {
            result.setCheckSingleResult(rootProductName + ":" + strResult);
        }

        if (MapUtils.isNotEmpty(subBomResultMap)) {
            Map<String, String> newSubBomResultMap = Maps.newHashMap();
            subBomResultMap.forEach((id, resultMessage) -> {
                String subProductName = productNameCacheMap.containsKey(id) ? productNameCacheMap.get(id) : id;
                String groupName = "";
                String groupId = allSubPros.get(id) != null ? allSubPros.get(id).get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class) : null;
                if (StringUtils.isNotBlank(groupId)) {
                    groupName = groupMap.containsKey(groupId) ? groupMap.get(groupId).getName() : groupId;
                    groupName = groupName + "->";
                }
                newSubBomResultMap.put(id, rootProductName + "->" + groupName + subProductName + ":" + resultMessage);
            });
            subBomResultMap.putAll(newSubBomResultMap);
        }

        Map<String, String> groupResultMap = result.getGroupResultList();
        if (MapUtils.isNotEmpty(groupResultMap)) {
            Map<String, String> newGroupResultMap = Maps.newHashMap();
            groupResultMap.forEach((id, resultMessage) -> {
                String groupName = groupMap.containsKey(id) ? groupMap.get(id).getName() : id;
                newGroupResultMap.put(id, rootProductName + "->" + groupName + ":" + resultMessage);
            });
            groupResultMap.putAll(newGroupResultMap);
        }
    }

    @Override
    public void checkSubProducts(ObjectDataDocument objectDataDocument) {
        Double maxAmount = toDouble(objectDataDocument.get("max_amount"), Double.MAX_VALUE);

        Object minAmount = objectDataDocument.get("min_amount");

        Double increment = toDouble(objectDataDocument.get("increment"), 1);

        Double amount = toDouble(objectDataDocument.get("amount"), 1);

        Boolean isRequired = toBoolean(objectDataDocument.get("is_required"), Boolean.FALSE);
        Boolean selectedByDefault = toBoolean(objectDataDocument.get("selected_by_default"), Boolean.FALSE);

        if (increment < 0) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_FIELD_INCREMENT));
        }
        if (amount < 0) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_FIELD_COUNT));
        }
        if (maxAmount < 0) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_FIELD_MAX_COUNT));
        }

        if (isRequired) {
            if (maxAmount == 0) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_FIELD_IS_REQUIRED));
            }
        }

        if (isRequired && !selectedByDefault) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_FIELD_IS_REQUIRED_DEFAULT_SELECTED));
        }

        if (minAmount != null && Strings.isNotBlank(minAmount.toString())) {
            double intMinAmount = Double.parseDouble(minAmount.toString());
            if (amount < intMinAmount) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_COUNT));
            }
            if (amount > maxAmount) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_COUNT_MAX));
            }
        }
        if (StringUtils.isNotBlank(MapUtils.getString(objectDataDocument, BomConstants.FIELD_INCREMENT)) && !canExactDivisor(amount, increment)) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SO_CHECK_SUB_PRO_COUNT_FORMULA));
        }
    }

    private Boolean toBoolean(Object value, Object defaultValue) {
        boolean result;
        if (Objects.isNull(value) || Strings.isBlank(value.toString())) {
            result = Objects.isNull(defaultValue) ? Boolean.FALSE : Boolean.valueOf(defaultValue.toString());
        } else {
            result = Boolean.parseBoolean(value.toString());
        }
        return result;
    }

    private Double toDouble(Object value, Object defaultValue) {
        double result;
        if (Objects.isNull(value) || Strings.isBlank(value.toString())) {
            result = Objects.isNull(defaultValue) ? Double.valueOf(1) : Double.valueOf(defaultValue.toString());
        } else {
            result = Double.parseDouble(value.toString());
        }
        return result;
    }

    private Integer toInt(Object value, Object defaultValue) {
        int result;
        if (Objects.isNull(value) || Strings.isBlank(value.toString())) {
            result = Objects.isNull(defaultValue) ? 0 : Integer.parseInt(defaultValue.toString());
        } else {
            result = Integer.parseInt(value.toString());
        }
        return result;
    }

    private void buildErrorList(CheckProductBomModel.SingleResult result, Boolean isBom, String dataId, String errMsg) {
        Map<String, String> list = isBom ? result.getBomResultList() : result.getGroupResultList();
        list.computeIfPresent(dataId, (oldK, oldV) -> oldV.concat("," + errMsg));
        list.putIfAbsent(dataId, errMsg);
    }

    @Override
    public List<IObjectData> getBomListByIds(User user, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), ids, Utils.BOM_API_NAME);
    }


    /**
     * 根据指定的core_id查询所有层级下的的复用bom
     *
     * @param user
     * @param arg
     * @return
     */
    public QueryAllSubBomModel.Result queryAllSubBomCore(User user, QueryAllSubBomModel.Arg arg) {
        List<QueryAllSubBomModel.SubBomInfo> list = Lists.newArrayList();
        recursiveSubCoreBom(1, user, arg.getBomCoreId(), arg.getNewBomPath(), list);
        //过滤删除、作废的bomCore
        Set<String> coreIds = list.stream().map(QueryAllSubBomModel.SubBomInfo::getBomCoreId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(coreIds)) {
            IActionContext context = ActionContextExt.of(user).allowUpdateInvalid(false).setSkipRelevantTeam(true).getContext();
            context.setDoCalculate(false);
            List<IObjectData> coreList = serviceFacade.findObjectDataByIdsExcludeInvalid(context, Lists.newArrayList(coreIds), Utils.BOM_CORE_API_NAME);
            if (CollectionUtils.isNotEmpty(coreList)) {
                List<String> normalBomCoreIds = coreList.stream().map(IObjectData::getId).collect(Collectors.toList());
                list = list.stream().filter(subBomInfo -> normalBomCoreIds.contains(subBomInfo.getBomCoreId())).collect(Collectors.toList());
            }
        }
        return QueryAllSubBomModel.Result.builder().allSubBomList(list).build();
    }


    /**
     * 根据指定的core_id查询第一层的复用bom
     *
     * @param deep
     * @param user
     * @param bomCoreId        当前bom的coreId
     * @param newBomPath       当前bom的path
     * @param toAppendDataList
     */
    private void recursiveSubCoreBom(int deep, User user, String bomCoreId, String newBomPath, List<QueryAllSubBomModel.SubBomInfo> toAppendDataList) {
        if (deep > 10) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_LEVEL_CAN_NOT_EXCEED_TEN_LEVELS));
        }
        //取出当前bomcore下所有的复用bom
        List<IObjectData> bomList = getBomListByCoreId(bomCoreId, user, true, false);

        if (CollectionUtils.isEmpty(bomList)) {
            return;
        }
        //遍历所有的复用bom,并且递归查询此复用bom下的复用bom
        bomList.stream().forEach(x -> {
            //根节点时可能没传newBomPath
            String toReplaceBomPath = getNewBomPath(x, newBomPath);
            QueryAllSubBomModel.SubBomInfo subBomInfo = QueryAllSubBomModel.SubBomInfo.builder()
                    .bomCoreId(x.get(BomConstants.FIELD_RELATED_CORE_ID, String.class))
                    .newBomId(x.get(DBRecord.ID, String.class))
                    .newBomPath(toReplaceBomPath)//拼接此复用bom的path
                    .build();
            toAppendDataList.add(subBomInfo);
            //为了解决相同复用BOM，只能牺牲性能，一个一个查询
            recursiveSubCoreBom(deep + 1, user, subBomInfo.getBomCoreId(), subBomInfo.getNewBomPath(), toAppendDataList);
        });
    }

    /**
     * 根据指定的productId查询对应的bom
     *
     * @param productId
     * @param user
     * @return
     */
    @Override
    public List<IObjectData> getBomsByProductId(String productId, User user) {
        IActionContext context = ActionContextExt.of(user).allowUpdateInvalid(false).setSkipRelevantTeam(true).getContext();
        context.setDoCalculate(false);
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setPermissionType(0);
        query.setNeedReturnCountNum(false);

        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "life_status", "normal");
        SearchUtil.fillFilterEq(filters, "is_deleted", "0");

        SearchUtil.fillFilterEq(filters, BomConstants.FIELD_PRODUCT_ID, productId);
        //parent_bom_id为空的为根节点
        SearchUtil.fillFilterIsNull(filters, BomConstants.FIELD_PARENT_BOM_ID);
        //core_id不为空
        SearchUtil.fillFilterIsNotNull(filters, BomConstants.FIELD_CORE_ID);

        query.setFilters(filters);
        QueryResult<IObjectData> rootBomList = serviceFacade.findBySearchQuery(context, Utils.BOM_API_NAME, query);
        return rootBomList.getData();
    }


    /**
     * 构建BomCoreObj对象的参数
     *
     * @param user
     * @param arg
     * @return
     */
    @Override
    public CreateBomModel.CoreArg buildCoreArg(User user, CreateBomModel.Arg arg) {
        if (CollectionUtil.isEmpty(arg.getBomList())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAM_BLANK_CHECK_MSG, ""));
        }
        ObjectDataDocument objectData = buildBomCore(user, arg);
        Map<String, List<ObjectDataDocument>> map = buildBomList(user, arg);
        CreateBomModel.CoreArg coreArg = CreateBomModel.CoreArg.builder().objectData(objectData).details(map).build();
        return coreArg;
    }

    @Transactional
    public void updateSourceObjInfo(User user, String bomCoreId, CreateBomModel.Arg arg) {
        List<String> fromIdList = arg.getBomList().stream().map(CreateBomModel.CreateBomInfo::getFromId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fromIdList)) {
            return;
        }
        Map<String, String> id2bomIdMap = arg.getBomList().stream().collect(Collectors.toMap(CreateBomModel.CreateBomInfo::getFromId, CreateBomModel.CreateBomInfo::getTempBomId, (ov, nv) -> nv));

        String slaveApiName = arg.getBomList().get(0).getObjectApiName();
        List<IObjectData> datas = Lists.newArrayList();
        id2bomIdMap.keySet().stream().forEach(id -> {
            IObjectData data = new ObjectData();
            data.setId(id);
            data.setDescribeApiName(slaveApiName);
            data.setTenantId(user.getTenantId());
            data.set(SalesOrderConstants.SalesOrderProductField.STANDARD_BOM_ID.getApiName(), bomCoreId);
            data.set(SalesOrderConstants.SalesOrderProductField.STANDARD_BOM_LINE_ID.getApiName(), id2bomIdMap.get(id));
            datas.add(data);
        });
        serviceFacade.batchUpdateByFields(user, datas, Lists.newArrayList(
                SalesOrderConstants.SalesOrderProductField.STANDARD_BOM_ID.getApiName(),
                SalesOrderConstants.SalesOrderProductField.STANDARD_BOM_LINE_ID.getApiName())
        );

        IObjectData masterData = new ObjectData();
        masterData.setId(arg.getMasterDataId());
        masterData.setDescribeApiName(arg.getMasterApiName());
        masterData.setTenantId(user.getTenantId());
        masterData.set(SalesOrderConstants.SalesOrderField.BOM_CREATED_STATUS.getApiName(), true);
        serviceFacade.batchUpdateByFields(user, Lists.newArrayList(masterData), Lists.newArrayList(SalesOrderConstants.SalesOrderField.BOM_CREATED_STATUS.getApiName()));
    }

    @Override
    public void existedCreateBomCheck(User user, String objectApiName, String fromId) {
        List<IObjectData> datas = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(fromId), objectApiName);
        if (CollectionUtils.isEmpty(datas)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        String standardBomId = datas.get(0).get(SalesOrderConstants.SalesOrderProductField.STANDARD_BOM_ID.getApiName(), String.class);
        //已经生成过
        if (StringUtils.isNotBlank(standardBomId)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_BILL_EXIST_STANDARD_BOM_WARN));
        }
    }

    @Transactional
    public boolean standardBomHandler(User user, CreateBomModel.CreateBomInfo rootBom, CreateBomModel.Arg arg) {
        List<IObjectData> bomCores = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(rootBom.getBomCoreId()), Utils.BOM_CORE_API_NAME);
        if (CollectionUtils.isEmpty(bomCores) || CollectionUtils.isEmpty(arg.getBomList())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        IObjectData bomCore = bomCores.get(0);
        //如果当前单据是从标准BOM创建的，则直接给对应的标准BOM字段设置为对应的值
        if (BomCoreConstants.category.standard.getValue().equals(bomCore.get(BomCoreConstants.FIELD_CATEGORY, String.class))) {
            arg.getBomList().stream().forEach(x -> x.setTempBomId(x.getBomId()));
            updateSourceObjInfo(user, bomCore.getId(), arg);
            return false;
        } else {
            return true;
        }
    }

    /**
     * 预处理数量
     *
     * @param user
     * @param arg
     */
    @Override
    public void preHandlerCreateBom(User user, CreateBomModel.Arg arg, CreateBomModel.CreateBomInfo root) {
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), Utils.BOM_API_NAME);
        if (describe == null) {
            return;
        }
        IFieldDescribe amount = describe.getFieldDescribe(BomConstants.FIELD_AMOUNT);
        Integer decimalPlaces = amount.get("decimal_places", Integer.class, 2);
        BigDecimal base = root.getQuantity() == null ? BigDecimal.ONE : root.getQuantity();
        arg.getBomList().stream().forEach(x -> {
            //临时子件和子件的数量是否为任意值==true的，保留原值
            if (Objects.equals(EnumUtil.nodeType.temp.getValue(), x.getNodeType()) || x.isAmountAny()) {
                return;
            }
            if (x.isRoot() || x.getQuantity() == null) {
                x.setQuantity(BigDecimal.ONE);
            } else {
                x.setQuantity(x.getQuantity().divide(base, decimalPlaces, BigDecimal.ROUND_HALF_UP));
            }
        });
    }

    @Override
    @Transactional
    public void checkDuplicateBom(User user, CreateBomModel.Arg arg) {
        if (bizConfigThreadLocalCacheService.isOpenBomDuplicateCheck(user.getTenantId())
                && !bizConfigThreadLocalCacheService.isGenerateStandardBomNone(user.getTenantId())
                && CollectionUtils.isNotEmpty(arg.getBomList())) {
            log.info("createBom:创建标准BOM时，走了查重逻辑");
            CreateBomModel.CreateBomInfo rootNode = arg.getBomList().stream().filter(x -> x.isRoot()).findFirst().orElse(null);

            if (rootNode == null) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
            }
            //limit  200 一个产品最多支持200个版本
            String checkSql = "select bom.core_id as core_id, array_agg(bom.product_id||'-'||bom.amount::int) as product_ids\n" +
                    "                    from biz_bom bom\n" +
                    "                    where bom.tenant_id = '" + SqlEscaper.pg_escape(user.getTenantId()) + "'\n" +
                    "                      and bom.core_id in (select c.id\n" +
                    "                                      from biz_bom_core c\n" +
                    "                                      where c.product_id = '" + SqlEscaper.pg_escape(rootNode.getProductId()) + "'\n" +
                    "                                        and c.tenant_id = '" + SqlEscaper.pg_escape(user.getTenantId()) + "'\n" +
                    "                                        and c.life_status = 'normal'\n" +
                    "                                        and c.is_deleted = 0\n" +
                    "                                        and c.category='standard')\n" +
                    "                    group by bom.core_id having count(1)=" + arg.getBomList().size() + "\n" +
                    "                    limit 200";
            try {
                List<Map> list = objectDataService.findBySql(user.getTenantId(), checkSql);
                //没查询到数据，则说明没有重复数据
                if (CollectionUtils.isEmpty(list)) {
                    return;
                }
                List<String> productLinkers = arg.getBomList().stream().map(x -> x.getProductId() + "-" + (x.getQuantity() == null ? 1 : x.getQuantity().intValue())).collect(Collectors.toList());
                for (int i = 0; i < list.size(); i++) {
                    Map<String, Object> map = list.get(i);
                    List<String> dbList = Arrays.asList((String[]) map.get("product_ids"));
                    String bomCoreId = (String) map.get("core_id");
                    //只要有一组相同，则认为已经存在重复数据
                    if (productLinkers.size() == dbList.size() && productLinkers.containsAll(dbList)) {
                        //有重复的标准BOM存在，先将标准BOM的ID更新到当前对象的stand_bom_id字段中，再抛出提示信息
                        List<IObjectData> dbBomList = getBomListByCoreId(bomCoreId, user, false, false);
                        Map<String, Integer> product2Amount = Maps.newHashMap();
                        //有可能一个产品组合下存在多个相同的子件，按顺序进行匹配，并不是非常准确，没有特别好的方法
                        Map<String, List<String>> product2BomIds = dbBomList.stream().collect(Collectors.groupingBy(x -> x.get("product_id", String.class), Collectors.mapping(IObjectData::getId, Collectors.toList())));
                        arg.getBomList().stream().forEach(x -> {
                            List<String> bomIds = product2BomIds.get(x.getProductId());
                            if (CollectionUtils.isNotEmpty(bomIds)) {
                                int amount = product2Amount.getOrDefault(x.getProductId(), 0).intValue();
                                if (amount >= bomIds.size()) {
                                    amount = bomIds.size() - 1;
                                }
                                x.setTempBomId(bomIds.get(amount));
                                product2Amount.put(x.getProductId(), Integer.valueOf(amount + 1));
                            } else {
                                x.setTempBomId("");
                            }
                        });
                        updateSourceObjInfo(user, bomCoreId, arg);
                        throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_BOM_DUPLICATE_EXIST_ERROR));
                    }
                }
            } catch (MetadataServiceException e) {
                log.error("createBom:checkDuplicateBom error by sql:" + checkSql);
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_BOM_DUPLICATE_SQL_CHECK_ERROR));
            }
        } else {
            log.info("createBom:创建标准BOM时，没走查重逻辑，单据名称：" + arg.getMasterApiName() + ",单据ID：" + arg.getMasterDataId());
        }
    }


    private Map<String, List<ObjectDataDocument>> buildBomList(User user, CreateBomModel.Arg arg) {
        //过滤出bom_id不为空、或是临时子件节点的数据
        Predicate<CreateBomModel.CreateBomInfo> predicate = x -> StringUtils.isNotBlank(x.getBomId())
                || Objects.equals(EnumUtil.nodeType.temp.getValue(), x.getNodeType());
        List<CreateBomModel.CreateBomInfo> bomList = arg.getBomList().stream().filter(Objects::nonNull).filter(predicate)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bomList)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        List<ObjectDataDocument> result = bomList.stream().map(x -> buildBom(user, x)).collect(Collectors.toList());
        return buildBomTree(user, result);
    }

    private ObjectDataDocument buildBomCore(User user, CreateBomModel.Arg arg) {
        Optional<CreateBomModel.CreateBomInfo> bomInfoOptional = arg.getBomList().stream().filter(x -> x.isRoot()).findFirst();
        if (!bomInfoOptional.isPresent()) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        ObjectDataDocument objectDataDocument = buildBom(user, bomInfoOptional.get());
        objectDataDocument.put(SystemConstants.ObjectDescribeApiName, BomCoreConstants.DESC_API_NAME);
        objectDataDocument.put(SystemConstants.Field.Id.apiName, serviceFacade.generateId());
        objectDataDocument.put("category", "standard");
        objectDataDocument.put("purpose", "sale");
        if (arg.isAutoCreate()) {
            objectDataDocument.put(BomCoreConstants.FIELD_REMARK, I18N.text(SFAI18NKeyUtil.SFA_BILL_STANDARD_BOM_CREATE_AUTO));
        } else {
            objectDataDocument.put(BomCoreConstants.FIELD_REMARK, I18N.text(SFAI18NKeyUtil.SFA_BILL_STANDARD_BOM_CREATE_MANUAL));
        }
        return objectDataDocument;
    }

    private ObjectDataDocument buildBom(User user, CreateBomModel.CreateBomInfo createBomInfo) {
        ObjectDataDocument data = new ObjectDataDocument();
        String tempBomId = serviceFacade.generateId();
        createBomInfo.setTempBomId(tempBomId);
        data.put(SystemConstants.Field.Id.apiName, tempBomId);
        data.put("action_type", "create");
        data.put(SystemConstants.ObjectDescribeApiName, BomConstants.DESC_BOM_API_NAME);
        data.put(BomConstants.FIELD_ROOT_PROD_PKG_KEY, createBomInfo.getRootProdPkgKey());
        data.put(BomConstants.FIELD_PARENT_PROD_PKG_KEY, createBomInfo.getParentProdPkgKey());
        data.put(BomConstants.FIELD_PROD_PKG_KEY, createBomInfo.getProdPkgKey());
        data.put(BomConstants.FIELD_TEMP_NODE_BOM_ID, createBomInfo.getTempNodeBomId());
        data.put(BomConstants.FIELD_BOM_ID, createBomInfo.getBomId());
        data.put(BomConstants.FIELD_BOM_CORE_ID, createBomInfo.getBomCoreId());
        data.put(BomConstants.VIRTUAL_FIELD_QUANTITY, createBomInfo.getQuantity());
        data.put(BomConstants.FIELD_PRODUCT_ID, createBomInfo.getProductId());
        data.put(BomConstants.FIELD_NODE_TYPE, createBomInfo.getNodeType());
        if (Objects.equals(EnumUtil.nodeType.temp.getValue(), createBomInfo.getNodeType())) {
            data.put(BomConstants.FIELD_PRODUCT_GROUP_ID, createBomInfo.getTempNodeGroupId());
            //临时子件使用价目表价格
            createBomInfo.setPriceMode(EnumUtil.PriceMode.PRICE_BOOK.getValue() + "");
        } else {
            data.put(BomConstants.FIELD_PRODUCT_GROUP_ID, createBomInfo.getProductGroupId());
        }
        data.put(BomConstants.FIELD_ORDER_FIELD, createBomInfo.getNodeNo());
        data.put(BomConstants.FIELD_AMOUNT, createBomInfo.getQuantity());
        data.put(BomConstants.FIELD_SELECTED_BY_DEFAULT, true);
        data.put(BomConstants.FIELD_INCREMENT, "");
        data.put(BomConstants.FIELD_IS_REQUIRED, true);
        data.put(BomConstants.FIELD_AMOUNT_EDITABLE, false);
        data.put(BomConstants.FIELD_MAX_AMOUNT, createBomInfo.getMaxAmount());
        data.put(BomConstants.FIELD_MIN_AMOUNT, createBomInfo.getMinAmount());
        data.put(BomConstants.FIELD_PRICE_EDITABLE, false);
        data.put(BomConstants.FIELD_PRICE_MODE, createBomInfo.getPriceMode());
        data.put(BomConstants.FIELD_ENABLED_STATUS, true);
        data.put(BomConstants.FIELD_NODE_BOM_CORE_TYPE, EnumUtil.nodeBomCoreType.product.getValue());
        //1 表示"配置价格"
        if ("1".equals(createBomInfo.getPriceMode())) {
            //“配置价格”定价，取配置bom上的价格	如果是"价目表价格"，此字段是空值
            data.put(BomConstants.FIELD_ADJUST_PRICE, createBomInfo.getAdjustPrice());
        }
        if (StringUtils.isNotBlank(user.getUserId())) {
            data.put("tenant_id", user.getTenantId());
            data.put(SystemConstants.Field.Owner.apiName, Lists.newArrayList(user.getUserId()));
        }
        if (StringUtils.isNotBlank(user.getOutUserId())) {
            data.put(SystemConstants.Field.OutOwner.apiName, Lists.newArrayList(user.getOutUserId()));
            data.put(SystemConstants.Field.OutTenantId.apiName, user.getOutTenantId());
        }
        //本次新增
        data.put(SystemConstants.Field.RecordType.apiName, "default__c");
        return data;
    }

    /**
     * 构建产品组
     *
     * @return
     */
    private ObjectDataDocument buildProductGroup(IObjectData old) {
        //历史分组不存在，则自动创建一个分组数据
        if (old == null) {
            ObjectDataDocument o = new ObjectDataDocument();
            o.put(SystemConstants.ObjectDescribeApiName, Utils.PRODUCT_GROUP_API_NAME);
            o.put("name", "默认分组"); // ignoreI18n
            o.put(SystemConstants.Field.RecordType.apiName, "default__c");
            return o;
        } else {
            IObjectData newGroup = ObjectDataExt.of(old).copy();
            newGroup.set(DBRecord.ID, null);
            newGroup.set(BomConstants.FIELD_PARENT_BOM_ID, null);
            return ObjectDataDocument.of(newGroup);
        }
    }

    /**
     * 构建bom树，按BomCore新增时的格式构建
     *
     * @param user
     * @param bomList
     * @return
     */
    private Map<String, List<ObjectDataDocument>> buildBomTree(User user, List<ObjectDataDocument> bomList) {
        if (CollectionUtils.isEmpty(bomList)) {
            return Maps.newHashMap();
        }
        //过滤出所有分组
        Set<String> groupIds = bomList.stream().map(x -> x.toObjectData().get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class)).filter(Objects::nonNull).collect(Collectors.toSet());
        //重数据库中取出分组数据
        List<IObjectData> dbGroupList = queryGroupListByCond(user, Lists.newArrayList(groupIds));
        ;
        //将临时子件的分组id，赋值到product_group_id，以便下面统一处理
        bomList.stream().filter(x -> Objects.equals(EnumUtil.nodeType.temp.getValue(), x.toObjectData().get(BomConstants.FIELD_NODE_TYPE, String.class)))
                .forEach(x -> {
                    if (StringUtils.isNotBlank(x.toObjectData().get(BomConstants.FIELD_TEMP_NODE_GROUP_ID, String.class))) {
                        x.toObjectData().set(BomConstants.FIELD_PRODUCT_GROUP_ID, x.toObjectData().get(BomConstants.FIELD_TEMP_NODE_GROUP_ID));
                    }
                });
        //将数据转成Map
        Map<String, IObjectData> dbGroupMap = dbGroupList.stream().collect(Collectors.toMap(DBRecord::getId, o -> o, (ov, nv) -> nv));

        Map<String, ObjectDataDocument> map = bomList.stream()
                .collect(Collectors.toMap(x -> x.toObjectData().get(BomConstants.FIELD_PROD_PKG_KEY, String.class), v -> v, (x1, x2) -> x2));
        //创建出来的,用于存储分组节点
        Map<String, ObjectDataDocument> groupMap = Maps.newHashMap();
        //只可能存在一个根节点
        List<ObjectDataDocument> tree = Lists.newArrayList();
        bomList.stream().forEach(x -> {
            if (StringUtils.isBlank(x.toObjectData().get(BomConstants.FIELD_PARENT_PROD_PKG_KEY, String.class))) {
                tree.add(x);
            } else {
                String groupId = x.toObjectData().get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class);
                //如果节点带有分组ID，则需要添加到分组中
                if (StringUtils.isNotBlank(groupId)) {
                    ObjectDataDocument group = groupMap.get(groupId);
                    if (group == null) {
                        group = buildProductGroup(dbGroupMap.get(groupId));
                        groupMap.put(groupId, group);
                        //首次创建分组，需要将分组加入到树中
                        ObjectDataDocument parent = map.get(x.toObjectData().get(BomConstants.FIELD_PARENT_PROD_PKG_KEY, String.class));
                        if (parent != null) {
                            List<ObjectDataDocument> childrenList = parent.toObjectData().get("children", List.class, Lists.newArrayList());
                            childrenList.add(group);
                            parent.toObjectData().set("children", childrenList);
                        }
                    }
                    //将当前节点接入到分组中
                    List<ObjectDataDocument> childrenList = group.toObjectData().get("children", List.class, Lists.newArrayList());
                    childrenList.add(x);
                    group.toObjectData().set("children", childrenList);
                } else {
                    ObjectDataDocument parent = map.get(x.toObjectData().get(BomConstants.FIELD_PARENT_PROD_PKG_KEY, String.class));
                    if (parent != null) {
                        List<ObjectDataDocument> childrenList = parent.toObjectData().get("children", List.class, Lists.newArrayList());
                        childrenList.add(x);
                        parent.toObjectData().set("children", childrenList);
                    }
                }
            }
        });
        if (tree.size() != 1) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        Map<String, List<ObjectDataDocument>> result = Maps.newHashMap();
        result.put(BomConstants.DESC_BOM_API_NAME, tree.get(0).toObjectData().get("children", List.class, Lists.newArrayList()));
        return result;
    }

    private List<IObjectData> queryGroupListByCond(User user, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(500);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, DBRecord.ID, ids);
        searchTemplateQuery.setFilters(filters);
        return serviceFacade.findBySearchQueryIgnoreAll(user, Utils.PRODUCT_GROUP_API_NAME, searchTemplateQuery).getData();
    }
}
