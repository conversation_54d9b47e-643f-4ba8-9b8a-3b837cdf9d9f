package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.model.ChannelServiceModel;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel;
import com.facishare.crm.sfa.prm.api.channel.AgreementStatusRecordService;
import com.facishare.crm.sfa.prm.api.enums.AgreementStatus;
import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.service.ChannelService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.support.GDSHandler;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.facishare.crm.constants.PrmI18NConstants.PRM_CHANNEL_INITIATE_RENEWAL_ACTION;
import static com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel.ABLE_RENEWAL;
import static com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel.AGREEMENT_ID;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-04
 * ============================================================
 */
@Service
@Slf4j
public class ChannelAgreementService {
    @Resource
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Resource
    private ServiceFacade serviceFacade;
    @Autowired
    private GDSHandler gdsHandler;
    @Resource
    private MappingRuleService mappingRuleService;
    @Resource
    private ChannelService channelService;
    @Resource
    private AgreementStatusRecordService agreementStatusRecordService;

    private static final String RULE_PARTNER_2_PARTNER_AGREEMENT_DETAIL = "rule_partnerobj2partneragreementdetail__c";
    private static final String RULE_ACCOUNT_2_PARTNER_AGREEMENT_DETAIL = "rule_accountobj2partneragreementdetail__c";

    public IObjectData fetchPartnerAgreementDetailData(User user, String admissionDataId, String admissionObject, AgreementStatus agreementStatus) {
        String field;
        if (SFAPreDefineObject.Account.getApiName().equals(admissionObject)) {
            field = PartnerAgreementDetailModel.BELONG_ACCOUNT_ID;
        } else if (SFAPreDefineObject.Partner.getApiName().equals(admissionObject)) {
            field = PartnerAgreementDetailModel.BELONG_PARTNER_ID;
        } else {
            log.warn("ChannelAgreementService#fetchPartnerAgreementDetailData admissionObject is not support, admissionObject:{}", admissionObject);
            return null;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        SearchUtil.fillFilterEq(query.getFilters(), field, admissionDataId);
        if (agreementStatus == null) {
            SearchUtil.fillFilterIsNull(query.getFilters(), PartnerAgreementDetailModel.AGREEMENT_STATUS);
        } else {
            SearchUtil.fillFilterEq(query.getFilters(), PartnerAgreementDetailModel.AGREEMENT_STATUS, agreementStatus.getStatus());
        }
        query.setOrders(Lists.newArrayList(new OrderBy(IObjectData.CREATE_TIME, false)));
        List<IObjectData> dataList = serviceFacade.findBySearchQuery(user, SFAPreDefineObject.PartnerAgreementDetail.getApiName(), query).getData();
        if (CollectionUtils.isNotEmpty(dataList)) {
            return dataList.get(0);
        }
        return null;
    }

    public IObjectData createAgreementDetailWhenInitiateRenewal(User user, String admissionObject, IObjectData admissionData, IObjectData oldAgreementDetailData) {
        IObjectData originalAgreementDetail = fetchPartnerAgreementDetailData(user, admissionData.getId(), admissionObject, null);
        if (originalAgreementDetail != null) {
            return originalAgreementDetail;
        }
        ChannelServiceModel.MatchScheme matchScheme = channelService.matchSignScheme(user, admissionObject, admissionData);
        if (!matchScheme.matchSchemeSuccess()) {
            return null;
        }
        IObjectData partnerAgreementData = metaDataFindServiceExt.findObjectByIdWithHtml(user, matchScheme.getMatch(), SFAPreDefineObject.PartnerAgreement.getApiName());
        String dataId = createPartnerAgreementDetailData(user, partnerAgreementData, admissionData, admissionObject, SignStatus.PENDING_RENEWAL);
        IObjectData newAgreementDetailData = metaDataFindServiceExt.findObjectData(user, dataId, SFAPreDefineObject.PartnerAgreementDetail.getApiName());
        if (newAgreementDetailData == null) {
            return null;
        }
        newAgreementDetailData.set(ABLE_RENEWAL, true);
        newAgreementDetailData.setOutOwner(oldAgreementDetailData.getOutOwner());
        newAgreementDetailData.setOutTenantId(oldAgreementDetailData.getOutTenantId());
        metaDataFindServiceExt.bulkUpdateByFields(user, Lists.newArrayList(newAgreementDetailData), Lists.newArrayList("able_renewal", "out_owner", "out_tenant_id"));
        newAgreementDetailData = copyRelevantTeam(oldAgreementDetailData, newAgreementDetailData);
        serviceFacade.batchUpdateRelevantTeam(user, Lists.newArrayList(newAgreementDetailData), true);
        agreementStatusRecordService.changeSignStatusWithRecord(user, I18N.text(PRM_CHANNEL_INITIATE_RENEWAL_ACTION), admissionObject, admissionData, SignStatus.PENDING_RENEWAL);
        return newAgreementDetailData;
    }

    private IObjectData copyRelevantTeam(IObjectData sourceData, IObjectData targetData) {
        List<TeamMember> sourceTeamMembers = ObjectDataExt.of(sourceData).getTeamMembers();
        ObjectDataExt targetDataExt = ObjectDataExt.of(targetData);
        List<TeamMember> targetMembers = targetDataExt.getTeamMembers();
        sourceTeamMembers.forEach(teamMember -> {
            if (targetMembers.stream().anyMatch(a -> a.getEmployee().equals(teamMember.getEmployee()))) {
                return;
            }
            targetMembers.add(teamMember);
        });
        targetDataExt.setTeamMembers(targetMembers);
        return targetDataExt.getObjectData();
    }

    public String upsertPartnerAgreementDetailData(User user, String partnerAgreementId, String crmMapperId, String admissionObject) {
        IObjectData admissionData = metaDataFindServiceExt.findObjectByIdWithHtml(user, crmMapperId, admissionObject);
        if (admissionData == null) {
            log.warn("PrmChannelService#upsertPartnerAgreementDetailData admissionData is null, user:{}, admissionObject:{}", user.getTenantId(), admissionObject);
            return null;
        }
        IObjectData partnerAgreementData = metaDataFindServiceExt.findObjectByIdWithHtml(user, partnerAgreementId, SFAPreDefineObject.PartnerAgreement.getApiName());
        if (partnerAgreementData == null) {
            return null;
        }
        IObjectData partnerAgreementDetailData = fetchPartnerAgreementDetailData(user, crmMapperId, admissionObject, null);
        if (partnerAgreementDetailData == null) {
            return createPartnerAgreementDetailData(user, partnerAgreementData, admissionData, admissionObject, SignStatus.PENDING_SIGNATURE);
        } else {
            return updatePartnerAgreementDetailData(user, partnerAgreementData, partnerAgreementDetailData);
        }
    }

    private String updatePartnerAgreementDetailData(User user, IObjectData partnerAgreementData, IObjectData partnerAgreementDetailData) {
        String agreementContent = ObjectDataUtils.getValue(partnerAgreementData, PartnerAgreementDetailModel.AGREEMENT_CONTENT, String.class, null);
        partnerAgreementDetailData.set(PartnerAgreementDetailModel.AGREEMENT_CONTENT, agreementContent);
        User systemUser = new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        metaDataFindServiceExt.bulkUpdateByFields(systemUser, Lists.newArrayList(partnerAgreementDetailData), Lists.newArrayList(PartnerAgreementDetailModel.AGREEMENT_CONTENT));
        return partnerAgreementDetailData.getId();
    }

    private String createPartnerAgreementDetailData(User user, IObjectData partnerAgreementData, IObjectData admissionData, String admissionObject, SignStatus signStatus) {
        if (partnerAgreementData == null || admissionData == null) {
            return null;
        }
        User systemUser = new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(user.getTenantId())
                .appId("fs-crm-sfa")
                .contentType(RequestContext.ContentType.FULL_JSON)
                .ea(gdsHandler.getEAByEI(user.getTenantId()))
                .peerName("fs-crm-sfa")
                .user(systemUser)
                .build();
        ActionContext actionContext = new ActionContext(requestContext, SFAPreDefineObject.PartnerAgreementDetail.getApiName(), SystemConstants.ActionCode.Add.getActionCode());
        String agreementContent = ObjectDataUtils.getValue(partnerAgreementData, PartnerAgreementDetailModel.AGREEMENT_CONTENT, String.class, null);
        String ruleApiName;
        if (SFAPreDefineObject.Partner.getApiName().equals(admissionObject)) {
            ruleApiName = RULE_PARTNER_2_PARTNER_AGREEMENT_DETAIL;
        } else if (SFAPreDefineObject.Account.getApiName().equals(admissionObject)) {
            ruleApiName = RULE_ACCOUNT_2_PARTNER_AGREEMENT_DETAIL;
        } else {
            return null;
        }
        IObjectData partnerAgreementDetailData = mappingRuleService.generateDataByMappingRule(user, ruleApiName, admissionData, SFAPreDefineObject.PartnerAgreementDetail.getApiName());
        partnerAgreementDetailData.set(AGREEMENT_ID, partnerAgreementData.getId());
        if (SFAPreDefineObject.Partner.getApiName().equals(admissionObject)) {
            partnerAgreementDetailData.set(PartnerAgreementDetailModel.BELONG_PARTNER_ID, admissionData.getId());
        } else if (SFAPreDefineObject.Account.getApiName().equals(admissionObject)) {
            partnerAgreementDetailData.set(PartnerAgreementDetailModel.BELONG_ACCOUNT_ID, admissionData.getId());
        } else {
            //ignore
        }
        partnerAgreementDetailData.set(PartnerAgreementDetailModel.START_DATE, null);
        partnerAgreementDetailData.set(PartnerAgreementDetailModel.END_DATE, null);
        partnerAgreementDetailData.set(PartnerAgreementDetailModel.SIGNING_TIME, null);
        partnerAgreementDetailData.set(PartnerAgreementDetailModel.AGREEMENT_CONTENT, agreementContent);
        partnerAgreementDetailData.set(PartnerAgreementDetailModel.SIGN_STATUS, signStatus.getStatus());
        if (CollectionUtils.isEmpty(partnerAgreementDetailData.getOwner())) {
            partnerAgreementDetailData.setOwner(Lists.newArrayList(User.SUPPER_ADMIN_USER_ID));
        }
        if (StringUtils.isBlank(partnerAgreementDetailData.getRecordType())) {
            partnerAgreementDetailData.setRecordType("default__c");
        }
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(ObjectDataDocument.of(partnerAgreementDetailData));
        BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
        optionInfo.setSkipFuncValidate(true);
        arg.setOptionInfo(optionInfo);
        BaseObjectSaveAction.Result result = serviceFacade.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
        return result.getObjectData().getId();
    }
}
