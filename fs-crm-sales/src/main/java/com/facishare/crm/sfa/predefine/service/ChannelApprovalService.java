package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.model.ChannelEventModel;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.model.ChannelFlowInitVO;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.prm.api.channel.ChannelAdmissionService;
import com.facishare.crm.sfa.prm.api.dto.AdmissionConfigDTO;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.crm.sfa.service.ChannelService;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @time 2024-07-09 10:46
 * @Description
 */
@Service
@Slf4j
public class ChannelApprovalService {
    @Resource
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Resource
    private ChannelTaskService channelTaskService;
    @Resource
    private ChannelAdmissionService channelAdmissionService;
    @Resource(name = "channelServiceProvider")
    private ChannelService channelService;


    private static final String APPROVAL_INSTANCE_OBJ = "ApprovalInstanceObj";
    private static final String SOURCE_WORKFLOW_ID = "source_workflow_id";
    private static final String APPROVAL_FLOW = "approvalflow";

    public void channelFlowHandler(User user, StandardFlowCompletedAction.Arg arg, RequestContext.BizInfo bizInfo) {
        try {
            // 存在 license
            if (!channelService.notExistsModule(user)) {
                sendApprovalMessage(user, arg, bizInfo);
            }
        } catch (Exception e) {
            log.warn("ChannelApprovalService#channelFlowHandler failed, tenant:{}, objectApiName:{}", user.getTenantId(), arg.getDescribeApiName(), e);
        }
    }

    public void sendApprovalMessage(User user, StandardFlowCompletedAction.Arg arg, RequestContext.BizInfo bizInfo) {
        String sourceWorkFlowId = queryWorkFlowId(user, bizInfo);
        log.warn("合作伙伴审批流：tenant:{}, approvalId:{}, sourceWorkFlowId:{}", user.getTenantId(), bizInfo.getOtherBizId(), sourceWorkFlowId);
        if (StringUtils.isBlank(sourceWorkFlowId)) {
            return;
        }
        String approvalStatus;
        if (arg.isPass()) {
            approvalStatus = StandardFlowCompletedAction.Arg.PASS;
        } else if (arg.isReject()) {
            approvalStatus = StandardFlowCompletedAction.Arg.REJECT;
        } else {
            log.warn("审批流：审批流状态未知, tenant:{}, approvalId:{}, sourceWorkFlowId:{}", user.getTenantId(), bizInfo.getOtherBizId(), sourceWorkFlowId);
            return;
        }
        AdmissionConfigDTO admissionConfigDTO = channelAdmissionService.fetchChannelAdmissionConfig(user);
        String applyToApp = admissionConfigDTO.getApplyToApp();
        String scene = ChannelFlowInitVO.getSceneByFlowId(arg.getDescribeApiName(), sourceWorkFlowId);
        if (ChannelFlowInitVO.UNKNOWN.equals(scene)) {
            return;
        }
        IObjectData admissionData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, arg.getDataId(), admissionConfigDTO.getRelatedObjectApiName());
        if (notFromSelfRegistration(user, approvalStatus, admissionData)) {
            log.warn("admissionData notFromSelfRegistration");
            return;
        }
        if (ChannelFlowInitVO.REGISTER.equals(scene)) {
            // 合作伙伴注册审批
            ChannelEventModel.RegisterApprovalEvent registerApprovalEvent = ChannelEventModel.RegisterApprovalEvent.builder()
                    .tenantId(user.getTenantId())
                    .approvalOperator(arg.getUserId())
                    .dataId(arg.getDataId())
                    .objectApiName(arg.getDescribeApiName())
                    .approvalStatus(approvalStatus)
                    .app(applyToApp)
                    .build();
            channelTaskService.registerCreateLinkRelationEventHandler(user, registerApprovalEvent);
        } else if (ChannelFlowInitVO.SIGN.equals(scene)) {
            // 伙伴资质及协议审批
            ChannelEventModel.SignApprovalEvent registerApprovalEvent = ChannelEventModel.SignApprovalEvent
                    .builder()
                    .tenantId(user.getTenantId())
                    .approvalOperator(arg.getUserId())
                    .dataId(arg.getDataId())
                    .objectApiName(arg.getDescribeApiName())
                    .approvalStatus(approvalStatus)
                    .app(applyToApp)
                    .build();
            channelTaskService.signApprovalEventHandler(user, registerApprovalEvent);
        }
    }

    private boolean notFromSelfRegistration(User user, String approvalStatus, IObjectData objectData) {
        if (objectData == null) {
            log.warn("notFromSelfRegistration objectData is null, tenant:{}, approvalStatus:{}", user.getTenantId(), approvalStatus);
            return true;
        }
        if (SFAPreDefineObject.Account.getApiName().equals(objectData.getDescribeApiName())) {
            return false;
        }
        // 来源不是自注册
        String outResources = DataUtils.getValue(objectData, "out_resources", String.class, null);
        if (!"self_registration".equals(outResources)) {
            log.warn("notFromSelfRegistration is not self_registration, tenant:{}, status:{}",
                    user.getTenantId(), approvalStatus);
            return true;
        }
        return false;
    }

    public void renewalSign(User user, StandardFlowCompletedAction.Arg arg, RequestContext.BizInfo bizInfo) {
        String sourceWorkFlowId = queryWorkFlowId(user, bizInfo);
        log.warn("续签审批流：tenant:{}, approvalId:{}, sourceWorkFlowId:{}", user.getTenantId(), bizInfo.getOtherBizId(), sourceWorkFlowId);
        if (StringUtils.isBlank(sourceWorkFlowId)) {
            return;
        }
        if (!arg.isPass()) {
            return;
        }
        channelTaskService.renewalSignApproval(user, sourceWorkFlowId, arg.getDataId(), arg.getUserId());
    }

    public String queryWorkFlowId(User user, RequestContext.BizInfo bizInfo) {
        if (bizInfo == null) {
            log.warn("审批流：bizInfo == null, tenant:{}", user.getTenantId());
            return null;
        }
        String biz = bizInfo.getBiz();
        String approvalId = bizInfo.getOtherBizId();
        if (!APPROVAL_FLOW.equals(biz) || StringUtils.isBlank(approvalId)) {
            return null;
        }
        IObjectData data = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, approvalId, APPROVAL_INSTANCE_OBJ);
        if (data == null) {
            log.warn("审批流：审批流数据为空, tenant:{}, approvalId:{}", user.getTenantId(), approvalId);
            return null;
        }
        return ObjectDataUtils.getValueOrDefault(data, SOURCE_WORKFLOW_ID, "");
    }
}
