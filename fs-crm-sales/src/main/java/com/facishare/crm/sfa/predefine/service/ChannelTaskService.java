package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.enums.ReminderTriggerEnums;
import com.facishare.crm.model.*;
import com.facishare.crm.platform.async.executor.AsyncBootstrap;
import com.facishare.crm.platform.converter.Converter;
import com.facishare.crm.sfa.model.ChannelFlowInitVO;
import com.facishare.crm.sfa.model.ChannelServiceModel;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel;
import com.facishare.crm.sfa.prm.api.client.EnterpriseRelationServiceAdapter;
import com.facishare.crm.sfa.prm.platform.utils.I18NUtils;
import com.facishare.crm.sfa.service.ChannelService;
import com.facishare.crm.sfa.task.AsyncTaskProducer;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.facishare.crm.constants.PrmI18NConstants.*;

/**
 * <AUTHOR>
 * @time 2023-11-07 14:39
 * @Description
 */
@Service
@Slf4j
public class ChannelTaskService {
    @Resource
    private AsyncTaskProducer asyncTaskProducer;
    @Resource
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource(name = "channelServiceProvider")
    private ChannelService channelService;
    @Resource(name = "enterpriseRelationServiceAdapterImpl")
    private EnterpriseRelationServiceAdapter enterpriseRelationServiceAdapter;
    @Resource(name = "objectConverter")
    private Converter converter;

    private static final String CHANNEL_REGISTER_CREATE_ENTERPRISE = "channel_register_create_enterprise";
    private static final String RENEW_EXPIRATION_BUTTON = "renew_expiration_button";
    private static final String CHANNEL_SIGNING_APPROVAL = "channel_signing_approval";
    private static final String MANUAL_INITIATE_RENEWAL_BUTTON = "manual_initiate_renewal_button";
    private static final String CHANNEL_RENEWAL_SIGN_SUCCESSFUL = "channel_renewal_sign_successful";


    private void sendCreateLinkRelationMsg(User user, ChannelMessageModel.RegisterChannelApproval registerChannelApproval) {
        String messageBody = JSON.toJSONString(registerChannelApproval);
        String messageKey = user.getTenantId()
                .concat("@")
                .concat("CreateLinkRelation")
                .concat("_")
                .concat(registerChannelApproval.getObjectApiName())
                .concat(registerChannelApproval.getDataId())
                .concat("_")
                .concat(registerChannelApproval.getApprovalStatus());
        log.warn("ChannelTaskService#sendCreateLinkRelationMsg by send rocketmq message {}", messageBody);
        asyncTaskProducer.create(CHANNEL_REGISTER_CREATE_ENTERPRISE, messageBody, messageKey);
    }

    public void renewExpiration(User user, String objectApiName, String partnerId, Long renewTimestamp) {
        User adminUser = new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        PartnerMsgModel.RenewExpiration renewExpiration = new PartnerMsgModel.RenewExpiration();
        renewExpiration.setTenantId(user.getTenantId());
        renewExpiration.setDataId(partnerId);
        renewExpiration.setObjectApiName(objectApiName);
        renewExpiration.setRenewTimestamp(renewTimestamp);
        AsyncBootstrap.runAsyncTask(() -> asyncSendRenewExpiration(adminUser, renewExpiration));
    }

    private void asyncSendRenewExpiration(User user, PartnerMsgModel.RenewExpiration renewExpiration) {
        String messageKey = user.getTenantId()
                .concat("@")
                .concat("RenewExpiration_")
                .concat(renewExpiration.getDataId());
        String messageBody = JSON.toJSONString(renewExpiration);
        log.warn("PartnerTaskService#asyncSendRenewExpiration by send rocketmq message {}", messageBody);
        asyncTaskProducer.create(RENEW_EXPIRATION_BUTTON, messageBody, messageKey, Integer.valueOf(user.getTenantId()), null);
    }

    private boolean notFromSelfRegistration(User user, String approvalStatus, IObjectData partnerData) {
        if (partnerData == null) {
            log.warn("PartnerTaskService#isChannelPartner, tenant:{}, approvalStatus:{}", user.getTenantId(), approvalStatus);
            return true;
        }
        // 来源不是自注册
        if (!"self_registration".equals(partnerData.get("out_resources", String.class))) {
            log.warn("PartnerTaskService#isChannelPartner is not self_registration, tenant:{}, status:{}",
                    user.getTenantId(), approvalStatus);
            return true;
        }
        return false;
    }

    private PartnerMsgModel.ChannelApproval buildChannelApprovalArg(String tenantId, String approvalStatus, String partnerId, String approvalOperator, String schemeId) {
        PartnerMsgModel.ChannelApproval channelApproval = new PartnerMsgModel.ChannelApproval();
        channelApproval.setTenantId(tenantId);
        channelApproval.setPartnerId(partnerId);
        channelApproval.setApprovalStatus(approvalStatus);
        channelApproval.setApprovalOperator(approvalOperator);
        channelApproval.setSignSchemeId(schemeId);
        return channelApproval;
    }

    private void sendSigningApprovalMsg(User user, ChannelMessageModel.ChannelSignApproval channelSignApproval) {
        String messageBody = JSON.toJSONString(channelSignApproval);
        String messageKey = user.getTenantId()
                .concat("@")
                .concat("SigningApproval")
                .concat("_")
                .concat(channelSignApproval.getObjectApiName())
                .concat(channelSignApproval.getDataId())
                .concat("_")
                .concat(channelSignApproval.getApprovalStatus());
        log.warn("ChannelTaskService#sendSigningApprovalMsg by send rocketmq message {}", messageBody);
        asyncTaskProducer.create(CHANNEL_SIGNING_APPROVAL, messageBody, messageKey);
    }

    public void manualInitiateRenewal(User user, String admissionDataId, String signSchemeId, String outTenantId, String admissionObject) {
        User adminUser = new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        AsyncBootstrap.runAsyncTask(() -> asyncManualInitiateRenewal(adminUser, admissionDataId, signSchemeId, outTenantId, admissionObject));
    }

    private void asyncManualInitiateRenewal(User user, String admissionDataId, String signSchemeId, String outTenantId, String admissionObject) {
        if (StringUtils.isAnyBlank(admissionDataId, signSchemeId, outTenantId, admissionObject)) {
            log.warn("PartnerTaskService#manualInitiateRenewal, tenant:{}, admissionDataId:{}, signSchemeId:{}, outTenantId:{}, admissionObject:{}",
                    user.getTenantId(), admissionDataId, signSchemeId, outTenantId, admissionObject);
            return;
        }
        // 查询审批激活
        ChannelRpcModel.SignReminderInfoResult signReminderInfoResult = channelService.querySignReminderInfo(user, signSchemeId, ReminderTriggerEnums.MANUAL);
        if (!signReminderInfoResult.getEffective()) {
            return;
        }
        List<PartnerChannelManage.ReminderType> reminderTypes = signReminderInfoResult.getReminderTypes();
        if (CollectionUtils.isEmpty(reminderTypes)) {
            return;
        }
        reminderTypes.forEach(reminderType -> sendManualInitiateRenewalMQ(user, admissionDataId, signSchemeId, outTenantId, reminderType, admissionObject));
    }

    private void sendManualInitiateRenewalMQ(User user, String admissionDataId, String signSchemeId, String outTenantId, PartnerChannelManage.ReminderType reminderType, String admissionObject) {
        if (!Boolean.TRUE.equals(reminderType.getActivated())) {
            return;
        }
        PartnerChannelManage.ExpireReminderTask reminderTask = PartnerChannelManage.ExpireReminderTask.builder()
                .tenantId(user.getTenantId())
                .signSchemeId(signSchemeId)
                .dataId(admissionDataId)
                .objectApiName(admissionObject)
                .outTenantId(Long.valueOf(outTenantId))
                .reminderMethod(reminderType.getReminderMethod())
                .build();
        String messageBody = JSON.toJSONString(reminderTask);
        String messageKey = user.getTenantId()
                .concat("@")
                .concat("manualInitiateRenewalEvent")
                .concat("@")
                .concat(reminderType.getReminderMethod());
        log.warn("PartnerTaskService#manualInitiateRenewalEvent by send rocketmq message {}", messageBody);
        asyncTaskProducer.create(MANUAL_INITIATE_RENEWAL_BUTTON, messageBody, messageKey);
    }

    public void renewalSignApproval(User user, String sourceWorkFlowId, String partnerAgreementDetailId, String userId) {
        String admissionObject = channelService.fetchChannelAdmissionObject(user);
        String renewalSignFlowId = ChannelFlowInitVO.getWorkFlowIdByScene(SFAPreDefineObject.PartnerAgreementDetail.getApiName(), ChannelFlowInitVO.RENEWAL_SIGN);
        if (!sourceWorkFlowId.equals(renewalSignFlowId)) {
            return;
        }
        IObjectData partnerAgreementDetailData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, partnerAgreementDetailId, PartnerAgreementDetailModel.OBJ_API_NAME);
        if (partnerAgreementDetailData == null) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_DATA_NOT_EXISTS, "partnerAgreementDetailId"));
        }
        String belongDataId = channelService.getAdmissionObjectDataId(partnerAgreementDetailData, admissionObject);
        if (StringUtils.isBlank(belongDataId)) {
            //todo 抛异常提示是不是更好？
            log.warn("ChannelTaskService#renewalSignApproval, partnerAgreementDetailData:{} belongDataId is null", partnerAgreementDetailData);
            return;
        }
        String outTenantId = enterpriseRelationServiceAdapter.fetchOutTenantId(user, admissionObject, belongDataId);
        if (StringUtils.isBlank(outTenantId)) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_SCHEME_NO_DOWNSTREAM_COMPANY));
        }

        IObjectData admissionData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, belongDataId, admissionObject);
        if (admissionData == null) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_DATA_NOT_EXISTS, "belongDataId"));
        }
        ChannelServiceModel.MatchScheme matchScheme = channelService.matchSignScheme(user, admissionObject, admissionData);
        if (StringUtils.isBlank(matchScheme.getSchemeId())) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_SCHEME_NOT_MATCH, I18NUtils.getDataI18nName(admissionData)));
        }
        updateAbleRenewalFalse(user, partnerAgreementDetailData);
        PartnerChannelManage.RenewalSuccessful renewalSuccessful = PartnerChannelManage.RenewalSuccessful.builder()
                .userId(userId)
                .tenantId(user.getTenantId())
                .partnerAgreementDetailId(partnerAgreementDetailId)
                .objectDataId(belongDataId)
                .objectApiName(admissionObject)
                .outTenantId(Long.valueOf(outTenantId))
                .signSchemeId(matchScheme.getSchemeId())
                .language(I18NUtils.getLanguage())
                .build();
        String messageBody = JSON.toJSONString(renewalSuccessful);
        String messageKey = user.getTenantId()
                .concat("@")
                .concat("channelRenewalSuccessfulEvent")
                .concat("@")
                .concat(admissionObject)
                .concat("_")
                .concat(belongDataId);
        log.warn("ChannelTaskService#renewalSignApproval by send rocketmq message {}", messageBody);
        asyncTaskProducer.create(CHANNEL_RENEWAL_SIGN_SUCCESSFUL, messageBody, messageKey);

    }

    private void updateAbleRenewalFalse(User user, IObjectData partnerAgreementDetailData) {
        Map<String, Object> updateMap = Maps.newHashMap();
        updateMap.put("able_renewal", false);
        log.info("续签提醒:更新 able_renewal = false");
        serviceFacade.updateWithMap(user, partnerAgreementDetailData, updateMap);
    }

    public void registerCreateLinkRelationEventHandler(User user, ChannelEventModel.RegisterApprovalEvent registerApprovalEvent) {
        log.warn("ChannelTaskService#registerCreateLinkRelationEventHandler by registerCreateLinkRelationEventHandler registerApprovalEvent:{}", registerApprovalEvent);
        if (registerApprovalEvent == null) {
            log.warn("ChannelTaskService#registerCreateLinkRelationEventHandler, registerApprovalEvent is null");
            return;
        }
        String channelAdmissionObject = channelService.fetchChannelAdmissionObject(user);
        if (StringUtils.isBlank(channelAdmissionObject)) {
            return;
        }
        if (!channelAdmissionObject.equals(registerApprovalEvent.getObjectApiName())) {
            log.warn("ChannelTaskService#registerApprovalEvent, registerApprovalEvent.getObjectApiName:{} is not equals channelAdmissionObject:{}",
                    registerApprovalEvent.getObjectApiName(), channelAdmissionObject);
            return;
        }
        ChannelMessageModel.RegisterChannelApproval registerChannelApproval = converter.convertDTO(registerApprovalEvent, ChannelMessageModel.RegisterChannelApproval.class);
        registerChannelApproval.setLanguage(I18NUtils.getLanguage());
        sendCreateLinkRelationMsg(user, registerChannelApproval);
    }

    public void signApprovalEventHandler(User user, ChannelEventModel.SignApprovalEvent signApprovalEvent) {
        log.warn("ChannelTaskService#signApprovalEventHandler, signApprovalEvent:{}", signApprovalEvent);
        if (signApprovalEvent == null) {
            log.warn("ChannelTaskService#signApprovalEventHandler, signApprovalEvent is null");
            return;
        }
        String channelAdmissionObject = channelService.fetchChannelAdmissionObject(user);
        if (StringUtils.isBlank(channelAdmissionObject)) {
            return;
        }
        if (!channelAdmissionObject.equals(signApprovalEvent.getObjectApiName())) {
            log.warn("ChannelTaskService#signApprovalEventHandler, signApprovalEvent.getObjectApiName:{} is not equals channelAdmissionObject:{}",
                    signApprovalEvent.getObjectApiName(), channelAdmissionObject);
            return;
        }
        IObjectData objectData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, signApprovalEvent.getDataId(), signApprovalEvent.getObjectApiName());
        ChannelServiceModel.MatchScheme matchScheme = channelService.matchSignScheme(user, channelAdmissionObject, objectData);
        ChannelMessageModel.ChannelSignApproval channelSignApproval = converter.convertDTO(signApprovalEvent, ChannelMessageModel.ChannelSignApproval.class);
        channelSignApproval.setSignSchemeId(matchScheme.getSchemeId());
        channelSignApproval.setLanguage(I18NUtils.getLanguage());
        sendSigningApprovalMsg(user, channelSignApproval);
    }
}
