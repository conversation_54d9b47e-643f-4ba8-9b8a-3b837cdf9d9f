package com.facishare.crm.sfa.predefine.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.CrmMenuInitFindMenuDataList;
import com.facishare.crm.sfa.predefine.service.model.MutipleUnitInfo;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@ServiceModule("common_unit")
@Service
@Slf4j
public class CommonUnitService {
//    @Autowired
//    OriginalValueRedisDao redisDao;
    @Autowired
    private EnterpriseInitService enterpriseInitService;
//    @Autowired
//    NewOpportunityProxy newOpportunityProxy;
//    @Autowired
//    ModuleCtrlConfigService moduleCtrlConfigService;
//    @Autowired
//    InitObjectsPermissionsAndLayoutProxy initObjectsPermissionsAndLayoutProxy;
    @Autowired
    LazyLoadOptionsService lazyLoadOptionsService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    IObjectDescribeService objectDescribeService;
    @Resource(name = "crmMenuInitServiceSFA")
    CrmMenuInitService crmMenuInitService;
    @Autowired
    UnitCoreService unitCoreService;

    @ServiceMethod("new_init_common_unit")
    public Map newOpenService(List<Integer> tenantIds) {
        Map map = Maps.newHashMap();
        for (Integer tenantId : tenantIds) {
            try {
                init(User.builder().tenantId(tenantId.toString()).userId(User.SUPPER_ADMIN_USER_ID).build(), tenantId.toString());
                map.put("tenantId:" + tenantId, "开启成功"); // ignoreI18n
            } catch (Exception ex) {
                map.put("tenantId:" + tenantId, "开启失败：" + ex.getMessage()); // ignoreI18n
                //log.error(ex.getMessage(), ex);
            }
        }

        return map;
    }

    @ServiceMethod("init_amortizeInfo_info")
    public Map openAmortizeInfo(ServiceContext context, List<Integer> tenantIds) {
        Map map = Maps.newHashMap();
        for (Integer tenantId : tenantIds) {
            try {
                this.initAmortizeInfo(User.builder().tenantId(tenantId.toString()).userId(User.SUPPER_ADMIN_USER_ID).build(), tenantId.toString());
                map.put("tenantId:" + tenantId, "开启成功"); // ignoreI18n
            } catch (Exception ex) {
                map.put("tenantId:" + tenantId, "开启失败：" + ex.getMessage()); // ignoreI18n
                //log.error(ex.getMessage(), ex);
            }
        }

        return map;
    }

    public String init(User user, String tenantId) throws MetadataServiceException {

        String commonUnitApiName = SFAPreDefineObject.CommonUnit.getApiName();
        StringBuilder sb = new StringBuilder();
        IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, commonUnitApiName);
        if (describe == null) {
            enterpriseInitService.initDescribeForTenant(tenantId, commonUnitApiName);
        }
        //先刷功能权限
        String initPrivilegeMsg =enterpriseInitService.initPrivilegeRelate(Lists.newArrayList(commonUnitApiName), user, null, null,null);
        sb.append(" \n initPrivilegeRelate result:" + initPrivilegeMsg);
        //刷布局
        List<ILayout> layouts = serviceFacade.findLayoutByObjectApiName(user.getTenantId(), commonUnitApiName);
        if (layouts == null||layouts.isEmpty()) {
            enterpriseInitService.initMultiLayoutForOneTenant(Lists.newArrayList(commonUnitApiName), tenantId);
        }

        //默认CRM菜单中新增商机2.0菜单
//        menuCommonService.createMenuItem(user, commonUnitApiName);
        addCommonInfoObjToMenu(user.getTenantId(), user);
        sb.append(" \n createMenuItem success");

        //log.warn("commonUnit init all success,tenantId {}，msg {}", tenantId, sb.toString());
        return sb.toString();
    }

    @ServiceMethod("init_payment")
    public Map openPayment(ServiceContext context, List<Integer> tenantIds) {
        Map map = Maps.newHashMap();
        for (Integer tenantId : tenantIds) {
            try {
                this.initPaymentObj(User.builder().tenantId(tenantId.toString()).userId(User.SUPPER_ADMIN_USER_ID).build(), tenantId.toString());
                map.put("tenantId:" + tenantId, "开启成功"); // ignoreI18n
            } catch (Exception ex) {
                map.put("tenantId:" + tenantId, "开启失败：" + ex.getMessage()); // ignoreI18n
                log.error(ex.getMessage(), ex);
            }
        }

        return map;
    }
    public void initPaymentObj(User user, String tenantId) throws MetadataServiceException {
        List<String> apiNames = Lists.newArrayList(Utils.CUSTOMER_PAYMENT_API_NAME, Utils.ORDER_PAYMENT_API_NAME);
        for (String apiname : apiNames) {
            IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, apiname);
            if (Objects.isNull(describe)) {
                enterpriseInitService.initDescribeForTenant(tenantId, apiname);
            }

            List<ILayout> layouts = serviceFacade.findLayoutByObjectApiName(user.getTenantId(), apiname);
            if (layouts == null||layouts.isEmpty()) {
                enterpriseInitService.initMultiLayoutForOneTenant(Lists.newArrayList(apiname), tenantId);
            }
        }
    }

    public String initAmortizeInfo(User user, String tenantId) throws MetadataServiceException {

        String apiName = SFAPreDefineObject.AmortizeInfo.getApiName();
        StringBuilder sb = new StringBuilder();
        IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, apiName);
        if (describe == null) {
            enterpriseInitService.initDescribeForTenant(tenantId, apiName);
        }
        //先刷功能权限
        String initPrivilegeMsg =enterpriseInitService.initPrivilegeRelate(Lists.newArrayList(apiName), user, null, null,null);
        sb.append(" \n initPrivilegeRelate result:" + initPrivilegeMsg);
        //刷布局
        List<ILayout> layouts = serviceFacade.findLayoutByObjectApiName(user.getTenantId(), apiName);
        if (layouts == null||layouts.isEmpty()) {
            enterpriseInitService.initMultiLayoutForOneTenant(Lists.newArrayList(apiName), tenantId);
        }

        //log.warn("AmortizeInfo init all success,tenantId {}，msg {}", tenantId, sb.toString());
        return sb.toString();
    }
    private void addCommonInfoObjToMenu(String tenantId, User user) {

        CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListArg arg = new CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListArg();
        arg.setApiNames(Lists.newArrayList(SFAPreDefineObject.CommonUnit.getApiName()));
        CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListResult menuItemByApiName = crmMenuInitService.innerFindMenuDataListByApiNames(arg, tenantId);
        if (Objects.isNull(menuItemByApiName) || org.apache.commons.collections4.CollectionUtils.isNotEmpty(menuItemByApiName.getMenuDataList())) {
            return;
        }
        crmMenuInitService.createMenuItem(user,Lists.newArrayList("CommonUnitObj"),"");
    }

    public void checkProductCommonUnit(User user, String tenantId, String productId, String commonUnit) {
        if (StringUtils.isEmpty(productId)) {
            throw new ValidateException(I18N.text("product.param.error.msg"));
        }
        if (StringUtils.isEmpty(commonUnit)) {
            throw new ValidateException(I18N.text("sfa.common.unit.cannot.empty"));
        }
        List<MutipleUnitInfo.Option> options =
                lazyLoadOptionsService.getLazyLoadOptionList(tenantId,user, productId);
        if (options == null || options.isEmpty()) {
            throw new ValidateException(I18N.text("sfa.unit.option.under.product.cannot.empty"));
        }

        boolean result = false;
        for (MutipleUnitInfo.Option option : options) {
            if (option != null) {
                if (option.getValue().equals(commonUnit)) {
                    result = true;
                    break;
                }
            }
        }
        if (!result) {
            throw new ValidateException(I18N.text("sfa.common.unit.product.does.not.match"));
        }
    }

    public List<BaseImportAction.ImportError> checkImportProductCommonUnit(String tenantId,User user, List<BaseImportDataAction.ImportData> dataList) {
        List<BaseImportAction.ImportError> errorList = Lists.newArrayList();
        List<String> productIds = new ArrayList<>();
        dataList.forEach(data -> {
            if (data.getData().get("product_id") != null) {
                productIds.add(data.getData().get("product_id").toString());
            }
        });
        QueryResult<IObjectData> multiUnitRelateResult = serviceFacade.findBySearchQuery(user,
                Utils.MULTI_UNIT_RELATED_API_NAME,
                unitCoreService.getTemplateQuery(tenantId, productIds));

        dataList.forEach(data -> {
            if (data.getData().get("product_id") != null&&data.getData().get("common_unit") != null) {
                String commonUnit = data.getData().get("common_unit").toString();
                List<IObjectData> multiUnitRelateList = multiUnitRelateResult.getData().stream().filter(r -> r.get("unit_id").equals(commonUnit))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(multiUnitRelateList)) {
                    errorList.add(new BaseImportAction.ImportError(data.getRowNo(), I18N.text("sfa.common.unit.import.0")));
                }
            }
        });
        return errorList;
    }
}
