package com.facishare.crm.sfa.predefine.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.enums.ActionCodeEnum;
import com.facishare.crm.sfa.predefine.service.model.UpdateFollowModel;
import com.facishare.crm.sfa.predefine.service.task.RecalculateTaskService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.facishare.crm.sfa.utilities.constant.AccountConstants.Field.LAST_FOLLOWED_TIME;
import static com.facishare.crm.sfa.utilities.constant.LeadsConstants.LAST_FOLLOW_TIME;

@ServiceModule("follow")
@Component
@Slf4j
public class FunUpdateFollowService {

    @Autowired
    @Qualifier("objectDataPgService")
    private IObjectDataService objectDataService;

    @Autowired
    private RecalculateTaskService recalculateTaskService;

    @ServiceMethod("calculateExpireTime")
    public UpdateFollowModel.Result calculateExpireTime(ServiceContext context, UpdateFollowModel.Arg arg) {
        UpdateFollowModel.Result result = UpdateFollowModel.Result.builder().build();
        if (arg.getApiName() == null
                || (!Utils.ACCOUNT_API_NAME.equals(arg.getApiName()) && !Utils.LEADS_API_NAME.equals(arg.getApiName()))) {
            result.setMsg("apiName is illegal, only support AccountObj and LeadsObj");
            return result;
        }
        if (CollectionUtils.isEmpty(arg.getObjectIds())){
            result.setMsg("objectIds is empty");
            return result;
        }

        List<String> objectIds = arg.getObjectIds();
        List<IObjectData> objectDataList = Lists.newArrayList();
        ArrayList<String> updateFields = Lists.newArrayList();
        objectIds.forEach(
                objectId -> {
                    IObjectData data = new ObjectData();
                    data.setTenantId(context.getTenantId());
                    data.setDescribeApiName(arg.getApiName());
                    if (Utils.LEADS_API_NAME.equals(arg.getApiName())) {
                        data.set(LAST_FOLLOW_TIME, System.currentTimeMillis());
                        updateFields.add(LAST_FOLLOW_TIME);
                    } else {
                        data.set(LAST_FOLLOWED_TIME, System.currentTimeMillis());
                        updateFields.add(LAST_FOLLOWED_TIME);
                    }
                    data.setId(objectId);
                    objectDataList.add(data);
                }
        );

        try {
            objectDataService.batchUpdateIgnoreOther(objectDataList, updateFields, ActionContextExt.of(context.getUser()).getContext());
        } catch (MetadataServiceException e) {
            log.error("calculateExpireTime error", e);
        }
        for (String objectId : objectIds) {
            recalculateTaskService.send(context.getTenantId(), objectId, arg.getApiName(), ActionCodeEnum.FOLLOW);
        }
        return result;
    }
}
