package com.facishare.crm.sfa.predefine.service.GoalValue.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.common.exception.CrmCheckedException;
import com.facishare.crm.sfa.lto.integral.core.service.dto.EnableRule;
import com.facishare.crm.sfa.predefine.enums.GoalEnum;
import com.facishare.crm.sfa.predefine.service.GoalValue.dto.*;
import com.facishare.crm.sfa.predefine.service.GoalValue.rpc.proxy.FsBiStatProxy;
import com.facishare.crm.sfa.predefine.service.GoalValue.rpc.proxy.QueryDetailGoalRuleStatusArg;
import com.facishare.crm.sfa.predefine.service.GoalValue.rpc.proxy.QueryDetailGoalRuleStatusResult;
import com.facishare.crm.sfa.predefine.service.GoalValue.utilities.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.restdriver.dto.FindEnumsByFieldName;
import com.facishare.paas.license.Result.ParaInfoResult;
import com.facishare.paas.license.arg.QueryModuleParaArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.rest.core.exception.RestProxyInvokeException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 目标规则配置服务类
 */
@ServiceModule("goal_rule")
@Component
@Slf4j
public class GoalRuleOperatorService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private IObjectDataProxyService proxyService;
    @Autowired
    private GoalRuleSaveActionService goalRuleActionService;
    @Autowired
    private GoalRuleCommonService goalRuleCommonService;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private GoalValueCommonService goalValueCommonService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private FsBiStatProxy fsBiStatProxy;

    @Resource()
    private LicenseClient licenseClient;
    @Autowired
    private EIEAConverter eieaConverter;

    /**
     * 获取目标规则列表(管理后台)
     *
     * @param context
     * @param arg
     * @return
     */
    @ServiceMethod("find_Rule_List")
    public FindRuleList.Result findRuleList(ServiceContext context, FindRuleList.Arg arg) {

        goalRuleActionService.initPresetRule(context.getUser());
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(context.getUser(),
                "GoalValueObj", arg.buildSearchQuery());

        List<IObjectData> objectDataList = queryResult.getData();

        fillOthersForRuleList(objectDataList, context);

        return FindRuleList.Result.builder()

                .pageCount(arg.getPageSize())
                .totalCount(queryResult.getTotalNumber())
                .dataList(ObjectDataDocument.ofList(objectDataList))
                .build();
    }

    /**
     * 获取目标规则列表(前端应用)
     *
     * @param context
     * @param arg
     * @return
     */
    @ServiceMethod("find_List")
    public FindRuleList.Result findList(ServiceContext context, FindRuleList.Arg arg) throws
            MetadataServiceException, CrmCheckedException {
        FindRuleList.Result rst = FindRuleList.Result.builder()
                .pageCount(arg.getPageSize())
                .dataList(Lists.newArrayList())
                .totalCount(0).build();
        List<String> userRoleList = serviceFacade.getUserRole(context.getUser());
        boolean isGoalRuleManager = CollectionUtils.empty(userRoleList)? false : userRoleList.contains(GoalRoleConstants.GOAL_MANAGER_ROLE_CODE);
        boolean isCrmManager = serviceFacade.isAdmin(context.getUser());
        List<String> visibleDeptIds = Lists.newArrayList();
        if(!isGoalRuleManager && !isCrmManager){
            VisibleDeptModel visibleDeptModel =  goalValueCommonService.getVisibleDeptIdsContainsShareData(context.getUser(), Boolean.FALSE);
            visibleDeptIds = visibleDeptModel.getVisibleDeptIds();
        }
        if (CollectionUtils.empty(visibleDeptIds) && !isGoalRuleManager && !isCrmManager) {
            return rst;
        }
        List<IObjectData> accountGoalValueData = goalValueCommonService.getAllValueByThemeApiName(context.getUser(), GoalEnum.ThemeTypeValue.ACCOUNT.getValue());
        List<IObjectData> productGoalValueData = goalValueCommonService.getAllValueByThemeApiName(context.getUser(), GoalEnum.ThemeTypeValue.PRODUCT.getValue());
        List<String> accountIdsForGoalValue = accountGoalValueData.stream().map(data-> data.get(GoalValueConstants.CHECK_OBJECT_ID, String.class)).distinct().collect(Collectors.toList());
        List<String> productIdsForGoalValue = productGoalValueData.stream().map(data-> data.get(GoalValueConstants.CHECK_OBJECT_ID, String.class)).distinct().collect(Collectors.toList());
        List<IObjectData> accounts = new ArrayList<>();
        List<IObjectData> products = new ArrayList<>();
        List<String> visibleRuleIds = new ArrayList<>();
        if(!isGoalRuleManager && !isCrmManager){
          accounts = goalValueCommonService.getAccountsByNameAndIds(context.getUser(), null, accountIdsForGoalValue);
          products = goalValueCommonService.getProductsByNameAndIds(context.getUser(), null, productIdsForGoalValue);
          visibleRuleIds = goalValueCommonService.findAllRuleIdByApplyCircleIds(context.getUser(), visibleDeptIds);
        }
        List<String> accountIds = accounts.stream().map(accountData->accountData.get("id", String.class)).collect(Collectors.toList());
        List<String> productIds = products.stream().map(productData->productData.get("id", String.class)).collect(Collectors.toList());

        String sqlWherePartion = "";
        String deptSql = (isGoalRuleManager || isCrmManager) ?
          "" :
          String.format(" AND ((d.apply_circle_id IN (%s) and coalesce(a.theme_api_name,'PersonnelObj')='PersonnelObj') " +
            "OR (d.apply_circle_id IN (%s) or ((a.theme_api_name='AccountObj' and a.id IN (select goal_rule_id from goal_value av where  av.check_object_id in (%s) and av.is_deleted=0 and av.goal_type='AccountObj'))  or  (a.theme_api_name='ProductObj' and a.id IN (select goal_rule_id from goal_value pv where  pv.check_object_id in (%s) and pv.is_deleted=0 and pv.goal_type='ProductObj')))))", SearchUtil
            .convertToSqlInConditionValues(visibleDeptIds), SearchUtil
            .convertToSqlInConditionValues(visibleDeptIds), SearchUtil.convertToSqlInConditionValues(accountIds), SearchUtil
            .convertToSqlInConditionValues(productIds));
        String sqlOrder = " ORDER BY a.last_modified_time DESC ";
        String sqlLimit = String.format("LIMIT %d OFFSET %d", arg.getPageSize(), arg.buildSearchQuery().getOffset());
        if (!arg.getGoalRuleId().isEmpty()) {
            sqlWherePartion += String.format(" AND a.id = '%s' ", StringEscapeUtils.escapeSql(arg.getGoalRuleId()));
        }
        if (!arg.getGoalRuleDetailId().isEmpty()) {
            sqlWherePartion = String.format(" AND b.id = '%s'", StringEscapeUtils.escapeSql(arg.getGoalRuleDetailId()));
        }
        //拼装搜索关键字findList
        if (!Strings.isNullOrEmpty(arg.getName())) {
            sqlWherePartion += String.format(" AND a.name LIKE '%%%s%%'", StringEscapeUtils.escapeSql(arg.getName()));
        }

        //拼装时区条件
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(arg.getTimeZoneList())) {
            sqlWherePartion += String.format(" AND a.time_zone in (%s)", SearchUtil
              .convertToSqlInConditionValues(arg.getTimeZoneList()));
        }

        String dataSql = MessageFormat.format(SpecialSql.getQuerySql("GetGoalRuleListSql")
                , deptSql
                , addQuotationMarks(context.getTenantId())
                , sqlWherePartion, sqlOrder, sqlLimit);

        QueryResult<IObjectData> dataQueryResult = objectDataService.findBySql(dataSql, context.getTenantId(),
                "GoalValueObj");

        if (dataQueryResult == null || CollectionUtils.empty(dataQueryResult.getData())) {
            return rst;
        }
        handleData(dataQueryResult.getData(), context.getUser());
        Optional<IObjectData> data = dataQueryResult.getData().stream().findFirst();
        handleSpencialFields(dataQueryResult.getData(), context);
        Integer totalNumber = (Integer) data.map(x -> x.get("total_number")).orElse("0");
        return FindRuleList.Result.builder()
                .pageCount(arg.getPageSize())
                .totalCount(totalNumber)
                .dataList(ObjectDataDocument.ofList(dataQueryResult.getData()))
                .build();
    }

    private void handleData(List<IObjectData> data, User user) {
        if(CollectionUtils.empty(data)){
            return;
        }
        try {
            for (IObjectData objData: data) {
                List<String> checkCircleIds = goalRuleCommonService.findGoalRuleApplyCircle(user, objData.get(GoalRuleObj.GOAL_RULE_ID, String.class))
                                                                   .stream().map(d -> String.valueOf(d.get(GoalRuleApplyCircleObj.FIELD_APPLY_CIRCLE_ID)))
                                                                   .collect(Collectors.toList());
                objData.set(GoalRuleApplyCircleObj.FIELD_APPLY_CIRCLE_ID, checkCircleIds);
            }
        } catch (Exception e) {
            log.error("handleData error", e);
        }
    }

    private void handleSpencialFields(List<IObjectData> objectDataList, ServiceContext context) {
        List<String> ruleAndDetailRuleIds = objectDataList.stream()
                                                          .map(data -> getDetailRuleId(data))
                                                          .collect(Collectors.toList());

        Map<String, Integer> ruleIdAndStatusMap = new HashMap<>();
        try {
            if(CollectionUtils.notEmpty(ruleAndDetailRuleIds)){
                QueryDetailGoalRuleStatusArg arg = new QueryDetailGoalRuleStatusArg(ruleAndDetailRuleIds, context.getTenantId());
                String ea = getEa(context);
                QueryDetailGoalRuleStatusResult result = fsBiStatProxy.getStatus(arg, HttpUtils.header(context.getUser().getUserId(), context.getTenantId(), ea));
                if(result !=null && CollectionUtils.notEmpty(result.getData())){
                  ruleIdAndStatusMap = result.getData();
                }
            }
        } catch (Exception e) {
            log.error("query aggRuleStatus error", e);
        }
        //填充考核对象
      Map<String, Integer> finalRuleIdAndStatusMap = ruleIdAndStatusMap;
      Map<String, IObjectDescribe> describeListPresetObject =  getThemeApiNameDescribe(context.getTenantId(), objectDataList);
      objectDataList.forEach(data -> {
            String check_level_field_type = "";
            String themeApiName = data.get(GoalRuleObj.THEME_API_NAME, String.class);
            themeApiName = Strings.isNullOrEmpty(themeApiName) ? GoalEnum.ThemeTypeValue.PERSON.getValue() : themeApiName;
            String checkLevelFieldApiName = data.get(GoalRuleObj.CHECK_LEVEL_FIELD_API_NAME, String.class);
            String checkLevelType = data.get(GoalRuleObj.CHECK_LEVEL_TYPE, String.class);

            if(StringUtils.isNotEmpty(checkLevelFieldApiName) && (GoalRuleConstants.LEVEL_TREE_API_NAME_LIST.contains(checkLevelFieldApiName)|| GoalRuleConstants.LEVEL_TREE_API_TYPE_LIST.contains(check_level_field_type))){
                data.set(GoalRuleObj.IS_LEVEL_TREE, true);
            }else {
                data.set(GoalRuleObj.IS_LEVEL_TREE, false);
            }
            String key = getDetailRuleId(data);
          if (finalRuleIdAndStatusMap.containsKey(key) && finalRuleIdAndStatusMap.get(key) != null ) {
              data.set(GoalRuleObj.STATUS, finalRuleIdAndStatusMap.get(key));
            }
        });
    }

    private String getEa(ServiceContext context) {
        if(StringUtils.isNotEmpty(context.getEa())){
            return context.getEa();
        }
        return eieaConverter.enterpriseIdToAccount(Integer.parseInt(context.getTenantId()));
    }

    private Map<String, IObjectDescribe> getThemeApiNameDescribe(String tenantId, List<IObjectData> objectDataList) {
        List<String> themeApiNameList = objectDataList.stream().map(o -> getThemeApiName(o)).distinct().collect(Collectors.toList());
        Map<String, IObjectDescribe> describeListPresetObject = describeLogicService.findObjects(tenantId, themeApiNameList);
        return describeListPresetObject == null ? Maps.newHashMap() : describeListPresetObject;
    }

    private String getThemeApiName(IObjectData o) {
        String themeApiName = o.get(GoalRuleObj.THEME_API_NAME, String.class);
        themeApiName = StringUtils.isEmpty(themeApiName) ? GoalEnum.ThemeTypeValue.PERSON.getValue() : themeApiName;
        return themeApiName;
    }

    private String getDetailRuleId(IObjectData data) {
        String detailRuleId = data.get(GoalRuleObj.GOAL_RULE_DETAIL_ID, String.class);
        detailRuleId = StringUtils.isEmpty(detailRuleId) ? GoalRuleConstants.NO_GOAL_DETAIL : detailRuleId;
        return String.format("%s|%s", data.get(GoalRuleObj.GOAL_RULE_ID, String.class), detailRuleId);
    }

    private String addQuotationMarks(String str) {
        if (str == null || str.isEmpty())
            return str;
        return String.format("'%s'", str);
    }

    private void fillOthersForRuleList(List<IObjectData> objectDataList, ServiceContext context) {
        Set<String> checkFieldApiNameList = objectDataList.stream()
                .map(x -> String.valueOf(x.get(GoalRuleObj.CHECK_OBJECT_API_NAME)))
                .distinct()
                .collect(Collectors.toSet());
        Map<String, IObjectDescribe> describeListPresetObject = describeLogicService.findObjects(context.getTenantId(), checkFieldApiNameList);
        objectDataList.forEach(data -> {
            describeListPresetObject.forEach((apiName, describe) -> {
                if (data.get(GoalRuleObj.CHECK_OBJECT_API_NAME).equals(apiName)) {
                    IFieldDescribe fields = describe.getFieldDescribe(String.valueOf(data.get("check_field_api_name")));
                    if (fields != null) {
                        data.set("check_field_label", fields.getLabel());
                    }
                }
            });

        });


        //填充考核对象
        objectDataList.forEach(data -> {
            String themeApiName = data.get(GoalRuleObj.THEME_API_NAME, String.class);
            if(StringUtils.isEmpty(themeApiName)){
                data.set(GoalRuleObj.THEME_API_NAME,
                        GoalEnum.ThemeTypeValue.PERSON.getLabel()//考核对象默认值
                );
            }else {
                data.set(GoalRuleObj.THEME_API_NAME, GoalEnum.ThemeTypeValue.getEnumByValue(themeApiName).getLabel());
            }
        });
        //TODO 填充status 2：初始化中  调用付杰的接口
    }

    /**
     * 获取目标考核指标(预设对象加自定义对象)
     *
     * @return
     */
    @ServiceMethod("find_Goal_Check_Index")
    public FindGoalCheckIndex.Result findGoalCheckIndex(ServiceContext context) {

        List<IObjectDescribe> describeListCustomObject = describeLogicService.findDescribeByPrivilegeAndModule(context.getUser(),
                ObjectAction.VIEW_LIST.getActionCode(), false,
                true, false, false);
//        Map<String,IObjectDescribe> describeListPresetObject= describeLogicService.findObjects(context.getTenantId(), Arrays.asList(GoalRulePresetObject.presetObjectApiName));
//        describeListPresetObject.keySet().forEach(k->{
//            describeListCustomObject.add(describeListPresetObject.get(k));
//        });
        Map<String, String> apiNameObject = new HashMap<>();
        describeListCustomObject.forEach(describe -> {
            apiNameObject.put(describe.getApiName(), describe.getDisplayName());
        });

        return FindGoalCheckIndex.Result.builder().apiName(apiNameObject).build();
    }


    /**
     * 对象/字段模糊搜索
     *
     * @return
     */
    @ServiceMethod("find_Search_Object_OR_Field")
    public FindSearchObjectORField.Result findSearchObjectORField(ServiceContext context, FindSearchObjectORField.Arg arg) {

        if (arg.getSearchField() != null) {
            List<IObjectDescribe> describeListCustomObject;

            boolean searchAllDescribe = CollectionUtils.empty(arg.getSearchApiName());
            //匹配时间会传apiname
            if (searchAllDescribe) {
                describeListCustomObject = describeLogicService.findDescribeByPrivilegeAndModule(context.getUser(),
                        ObjectAction.VIEW_LIST.getActionCode(), false,
                        true, false, false);
            } else {
                Map<String, IObjectDescribe> describe = describeLogicService.findObjects(context.getTenantId(), arg.getSearchApiName());
                describeListCustomObject = Lists.newArrayList(describe.values());
            }

            //返回格式
            List<List<Map<String, Map<String, String>>>> searchObject = new ArrayList<>();
            describeListCustomObject.forEach(oneObject -> {
                //ApiName匹配
                Map<String, Map<String, String>> object = new HashMap<>();
                Boolean apiNameFlag = false;
                Map<String, String> fieldApiName = new HashMap<>();
                fieldApiName.put("label", oneObject.getDisplayName());
                fieldApiName.put("is_active", String.valueOf(oneObject.isActive()));
                object.put(oneObject.getApiName(), fieldApiName);
                if (oneObject.getDisplayName().contains(arg.getSearchField())) {
                    apiNameFlag = true;
                }
                //字段匹配
                Boolean finalApiNameFlag = apiNameFlag;
                List<Map<String, Map<String, String>>> fieldsDescribe = new ArrayList<>();
                Map<String, Map<String, String>> fieldSimple = new HashMap<>();
                //对象名匹配，那么字段则全部展示
                if (apiNameFlag) {
                    oneObject.getFieldDescribes().forEach(field -> {
                        Map<String, String> fieldType = new HashMap<>();
                        fieldType.put("type", field.getType());
                        fieldType.put("label", field.getLabel());
                        fieldType.put("is_active", String.valueOf(field.isActive()));
                        fieldSimple.put(field.getApiName(), fieldType);
                    });
                    fieldsDescribe.add(object);
                    fieldsDescribe.add(fieldSimple);
                    searchObject.add(fieldsDescribe);
                } else {
                    //没有传searchApiName只支持搜索预设对象的字段，因为根据企业id查询describe列表是不包含字段描述的，所以根据apiName重查一遍。
                    if (searchAllDescribe && !IObjectDescribe.DEFINE_TYPE_CUSTOM.equals(oneObject.getDefineType())) {
                        oneObject = describeLogicService.findObject(context.getTenantId(), oneObject.getApiName());
                    }

                    //对象名不匹配，过滤字段是否有匹配的
                    oneObject.getFieldDescribes().forEach(field -> {
                        if (field.getLabel().contains(arg.getSearchField())) {
                            Map<String, String> fieldTypeFilter = new HashMap<>();
                            fieldTypeFilter.put("type", field.getType());
                            fieldTypeFilter.put("label", field.getLabel());
                            fieldTypeFilter.put("is_active", String.valueOf(field.isActive()));
                            fieldSimple.put(field.getApiName(), fieldTypeFilter);
                        }
                    });
                    if (fieldSimple.size() > 0) {
                        fieldsDescribe.add(object);
                        fieldsDescribe.add(fieldSimple);
                        searchObject.add(fieldsDescribe);
                    }

                }
            });
            return FindSearchObjectORField.Result.builder().searchobject(searchObject).build();
        } else {
            return null;
        }
    }


    /**
     * 新增或修改目标规则
     *
     * @param context
     * @param arg
     * @return
     */
    @ServiceMethod("create_Or_Update_Rule")
    public CreateOrUpdateRule.Result createOrUpdateRule(ServiceContext context, CreateOrUpdateRule.Arg arg) {

        IObjectData goalRule = arg.getGoalRule().toObjectData();
        sortYear(goalRule);
        List<IObjectData> goalRuleDetails = arg.getGoalRuleDetails() != null ?
                arg.getGoalRuleDetails().stream()
                        .map(ObjectDataDocument::toObjectData)
                        .collect(Collectors.toList()) : Lists.newArrayList();

//      boolean isNeedInitGoalValue = isNeedInitGoalValue(context.getUser(), goalRule, goalRuleDetails);
        String ruleId = goalRuleActionService.createOrUpdateRule(
                context.getUser(),
                goalRule, goalRuleDetails,
                arg.getCheckCircleIds());
//      if(isNeedInitGoalValue){
//          EnableRule.Arg enableRuleArg = new EnableRule.Arg();
//          enableRuleArg.setRuleId(ruleId);
//          this.enableRule(context, enableRuleArg);
//      }

        return CreateOrUpdateRule.Result.builder().ruleId(ruleId).build();
    }

    /**
     * 年份排序
     *
     * @param goalRule
     */
    private void sortYear(IObjectData goalRule) {
        List<String> year = goalRule.get(GoalRuleObj.COUNT_FISCAL_YEAR, ArrayList.class);
        List<Integer> iYear = year.stream()
                .map(x -> Integer.parseInt(x))
                .collect(Collectors.toList());
        Collections.sort(iYear);
        year = iYear.stream().map(x -> String.valueOf(x)).collect(Collectors.toList());
        goalRule.set(GoalRuleObj.COUNT_FISCAL_YEAR, year);
    }


    /**
     * 获取规则详情
     *
     * @param context
     * @param arg
     * @return
     */
    @ServiceMethod("find_By_Id")
    public FindById.Result findById(ServiceContext context, FindById.Arg arg) {

        IObjectData goalRuleData = goalRuleCommonService.findGoalRule(context.getUser(), arg.getRuleId());

        List<IObjectData> goalRuleDetailDatas = goalRuleCommonService.findGoalRules(context.getUser(), arg.getRuleId());

        List<String> checkCircleIds = goalRuleCommonService.findGoalRuleApplyCircle(context.getUser(), arg.getRuleId())
                .stream().map(data -> String.valueOf(data.get(GoalRuleApplyCircleObj.FIELD_APPLY_CIRCLE_ID)))
                .collect(Collectors.toList());

        fillGoalRuleSpecialFields(goalRuleData);
        return FindById.Result.builder()
                .goalRule(ObjectDataDocument.of(goalRuleData))
                .goalRuleDetails(ObjectDataDocument.ofList(goalRuleDetailDatas))
                .checkCircleIds(checkCircleIds)
                .subGoalValueNames(buildSubGoalValueName(context, goalRuleData, goalRuleDetailDatas))
                .deletedOptions(buildDeletedOptions(context, goalRuleData))
                .build();
    }

    private void fillGoalRuleSpecialFields(IObjectData goalRuleData) {
        String themeApiName = goalRuleData.get(GoalRuleObj.THEME_API_NAME, String.class);
        String checkLevelFieldApiName = goalRuleData.get(GoalRuleObj.CHECK_LEVEL_FIELD_API_NAME, String.class);
        String checkLevelType =  goalRuleData.get(GoalRuleObj.CHECK_LEVEL_TYPE, String.class);
        String deptFieldApiName =  goalRuleData.get(GoalRuleObj.DEPT_FIELD_API_NAME, String.class);

        if(StringUtils.isEmpty(themeApiName)){
            themeApiName = GoalEnum.ThemeTypeValue.PERSON.getValue();
            goalRuleData.set(GoalRuleObj.THEME_API_NAME, themeApiName);
        }
        if(Objects.equals(GoalEnum.ThemeTypeValue.PERSON.getValue(), themeApiName) && StringUtils.isEmpty(deptFieldApiName)){
            deptFieldApiName = GoalRuleConstants.OWNER_DEPARTMENT;
            goalRuleData.set(GoalRuleObj.DEPT_FIELD_API_NAME, deptFieldApiName);
        }
        String personnelRelationApiName = goalRuleData.get(GoalRuleObj.PERSONNEL_RELATION_API_NAME, String.class);
        if(StringUtils.isEmpty(personnelRelationApiName) && Objects.equals(themeApiName, GoalEnum.ThemeTypeValue.PERSON.getValue())){
            goalRuleData.set(GoalRuleObj.PERSONNEL_RELATION_API_NAME, GoalRuleConstants.OWNER);
        }
        if(StringUtils.isNotEmpty(checkLevelFieldApiName)){
            return;
        }
        if(GoalEnum.ThemeTypeValue.ACCOUNT.getValue().equals(themeApiName) || GoalEnum.ThemeTypeValue.PRODUCT.getValue().equals(themeApiName)){
            goalRuleData.set(GoalRuleObj.CHECK_LEVEL_FIELD_API_NAME, GoalRuleConstants.NAME);
            goalRuleData.set(GoalRuleObj.CHECK_LEVEL_TYPE, GoalRuleConstants.NAME_LEVEL_TYPE);
        }else {
            goalRuleData.set(GoalRuleObj.CHECK_LEVEL_FIELD_API_NAME, GoalRuleConstants.PERSONEL_CHECK_LEVEL_FIELD_API_NAME);
            goalRuleData.set(GoalRuleObj.CHECK_LEVEL_TYPE, GoalRuleConstants.DEPT_USERID_LEVEL_TYPE);
        }
    }


    /**
     * 当考核维度是客户名称或者产品名称时，返回子目标维度值对应的主属性
     *
     * @param context
     * @param goalRuleData
     * @param goalRuleDetailDatas
     * @return
     */
    @Nullable
    private Map buildSubGoalValueName(ServiceContext context,
                                      IObjectData goalRuleData,
                                      List<IObjectData> goalRuleDetailDatas) {
        String subgoalObjectApiName = goalRuleData.get(GoalRuleObj.SUBGOAL_OBJECT_API_NAME, String.class);
        if (!Strings.isNullOrEmpty(subgoalObjectApiName)) {
            String subgoalFieldApiName = goalRuleData.get(GoalRuleObj.SUBGOAL_FIELD_API_NAME, String.class);
            IObjectDescribe objectDescribe = serviceFacade.findObject(context.getTenantId(), subgoalObjectApiName);
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(subgoalFieldApiName);
            if (fieldDescribe != null && IFieldType.OBJECT_REFERENCE.equals(fieldDescribe.getType())) {
                Set<String> ids = Sets.newHashSet();
                goalRuleDetailDatas.forEach(goalRuleDetail -> {
                    if (goalRuleDetail.get(GoalRuleDetailObj.SUBGOAL_VALUE) != null) {
                        ids.addAll((Collection<? extends String>) goalRuleDetail.get(GoalRuleDetailObj.SUBGOAL_VALUE));
                    }
                });

                try {
                    log.debug("buildSubGoalValueName,fieldDescribe{},ids{},context{}", fieldDescribe, ids, context);
                    List<INameCache> nameCaches = proxyService.findRecordName(
                            ActionContextExt.of(context.getUser()).getContext(),
                            ((ObjectReferenceFieldDescribe) fieldDescribe).getTargetApiName(), new ArrayList<>(ids));
                    List<INameCache> nameCachesNew = new ArrayList<>();
                    nameCaches.forEach(name -> {
                        if (name.getName() == null) {
                            nameCachesNew.add(name);
                        }
                    });
                    nameCaches.removeAll(nameCachesNew);
                    if (CollectionUtils.notEmpty(nameCaches) && nameCaches.get(0).getName() != null) {
                        return nameCaches.stream().collect(Collectors.toMap(p -> p.getId(), INameCache::getName));
                    }

                } catch (MetadataServiceException e) {
                    log.error("fillSubGoalValueName->findRecordName error", e);
                }
            }
        }

        return null;
    }

    /**
     * 获取已经删除的选项
     *
     * @param context
     * @param goalRuleData
     * @return
     */
    private Map buildDeletedOptions(ServiceContext context,
                                    IObjectData goalRuleData) {
//        String subgoalObjectApiName = goalRuleData.get(GoalRuleObj.SUBGOAL_OBJECT_API_NAME,String.class);
//        if(!Strings.isNullOrEmpty(subgoalObjectApiName)){
//            String subgoalFieldApiName = goalRuleData.get(GoalRuleObj.SUBGOAL_FIELD_API_NAME,String.class);
//            if (!Strings.isEmpty(subgoalObjectApiName)) {
//                IObjectDescribe objectDescribe = serviceFacade.findObject(context.getTenantId(), subgoalObjectApiName);
//                IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(subgoalFieldApiName);
//                if (fieldDescribe != null && IFieldType.SELECT_ONE.equals(fieldDescribe.getType())) {
//                    List<FindEnumsByFieldName.EnumInfo> enumInfos = crmRemoteService.findEnumsByFieldName(
//                            context.getTenantId(), subgoalObjectApiName, subgoalFieldApiName, true);
//                    if (CollectionUtils.notEmpty(enumInfos)) {
//                        return getDeletedOptions(enumInfos);
//                    }
//
//                }
//            }
//        }
        return null;
    }

    private Map getDeletedOptions(List<FindEnumsByFieldName.EnumInfo> enumInfos) {
        Map deleteOptions = Maps.newHashMap();
        for (FindEnumsByFieldName.EnumInfo enumInfo : enumInfos) {
            if (enumInfo.getIsDeleted()) {
                deleteOptions.put(enumInfo.getItemCode(), enumInfo.getItemName());
            }
            if (CollectionUtils.notEmpty(enumInfo.getChildren())) {
                deleteOptions.putAll(getDeletedOptions(enumInfo.getChildren()));
            }
        }

        return deleteOptions;
    }

    /**
     * 启用规则
     *
     * @param context
     * @param arg
     * @return
     */
    @ServiceMethod("enableRule")
    public EnableRule.Result enableRule(ServiceContext context, EnableRule.Arg arg) {
        IObjectData goalRuleData = goalRuleCommonService.findGoalRule(context.getUser(),"ruleId");
        int limitNumber = versionLimitNumber(context);
        List<IObjectData> goalRulesNumber = goalRuleCommonService.findGoalRulesNumber(context.getUser());
        if (goalRulesNumber.size() >= limitNumber) {
            throw new ValidateException(I18N.text(GoalRuleI18NKeyUtils.GOAL_RULE_OPPENING_EXCEED_UPPER_LIMIT) + limitNumber);
        }

        enableRule(context, goalRuleData);
        return EnableRule.Result.builder().success(true).build();
    }

    private void enableRule(ServiceContext context, IObjectData goalRuleData) {
        List<IObjectData> goalRuleDetailDatas = goalRuleCommonService.findGoalRules(context.getUser(), goalRuleData.getId());
        Map updateMap = Maps.newHashMap();
        updateMap.put(GoalRuleObj.STATUS, "1");
        updateMap.put(GoalRuleObj.DISABLE_REASON, "");
        serviceFacade.updateWithMap(context.getUser(), goalRuleData, updateMap);
        goalRuleData.set("status", "1");
        String themeApiName = goalRuleData.get(GoalRuleObj.THEME_API_NAME, String.class);
        if(!GoalEnum.GoalTypeValue.ACCOUNT.getValue().equals(themeApiName)
          &&!GoalEnum.GoalTypeValue.PRODUCT.getValue().equals(themeApiName)
            &&!GoalEnum.GoalTypeValue.PartnerObj.getValue().equals(themeApiName)){
            syncGoalValue(context.getUser(), goalRuleData.getId());
        }

        goalRuleActionService.sendMessage(ObjectAction.RECOVER, goalRuleData, goalRuleDetailDatas);
    }

    /**
     * 已开启的规则数量/启用规则数量
     *
     * @param context
     * @return
     */
    @ServiceMethod("enable_Number")
    public EnableNumber.Result enableNumber(ServiceContext context) {
        int limitNumber = versionLimitNumber(context);
        List<IObjectData> goalRulesNumber = goalRuleCommonService.findGoalRulesNumber(context.getUser());
        return EnableNumber.Result.builder()
                .enableNumber(String.valueOf(goalRulesNumber.size()))
                .totalNumber(String.valueOf(limitNumber))
                .build();
    }


    public int versionLimitNumber(ServiceContext context) {
        //获取数量
        QueryModuleParaArg getModuleParaLicensesArg = new QueryModuleParaArg();
        int limitNumber = 0;
        if (context.getTenantId() != null && GoalRuleConstants.vipLimitCount.containsKey(context.getTenantId())) {
            limitNumber =  GoalRuleConstants.vipLimitCount.get(context.getTenantId());
            return limitNumber;
        }
        Set<String> paraKeys = new HashSet<>();
        paraKeys.add("target_rules_limit");
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setUserId(context.getUser().getUserId());
        licenseContext.setTenantId(context.getTenantId());
        licenseContext.setAppId("CRM");

        getModuleParaLicensesArg.setContext(licenseContext);
        getModuleParaLicensesArg.setModuleCode("target_rules");
        getModuleParaLicensesArg.setParaKeys(paraKeys);

        ParaInfoResult getModuleParaLicensesResult = new ParaInfoResult();
        try {
            getModuleParaLicensesResult = licenseClient.queryModulePara(getModuleParaLicensesArg);
        } catch (RestProxyInvokeException e) {
            log.error("paas versionService getVersion error.", e);
        }
        if (getModuleParaLicensesResult.getResult().size() > 0) {
            limitNumber = Integer.parseInt(getModuleParaLicensesResult.getResult().get(0).getParaValue());
        }
        return limitNumber;
    }

    public void syncGoalValue(User user, String ruleId) {
        try {
          ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
          task.submit(() -> goalValueCommonService.syncGoalValue(user, ruleId));
          task.run();
        } catch (Exception e) {
          log.error("syncGoalValue error", e);
        }

    }


    /**
     * 编辑时校验当前规则是否配置目标值
     *
     * @param context
     * @param arg
     * @return
     */
    @ServiceMethod("check_Edit")
    public CheckEdit.Result checkEdit(ServiceContext context, CheckEdit.Arg arg) {
        goalRuleCommonService.findGoalRule(context.getUser(), arg.getRuleId());
        Boolean flag = goalValueCommonService.existGoalValue(context.getUser(), arg.getRuleId());
        return CheckEdit.Result.builder().value(flag).build();
    }

    /**
     * 禁用规则
     *
     * @param context
     * @param arg
     * @return
     */
    @ServiceMethod("disable_Rule")
    public DisableRule.Result disableRule(ServiceContext context, DisableRule.Arg arg) {
        IObjectData goalRuleData = goalRuleCommonService.findGoalRule(context.getUser(), arg.getRuleId());
        List<IObjectData> goalRuleDetailDatas = goalRuleCommonService.findGoalRules(context.getUser(), arg.getRuleId());
        Map updateMap = Maps.newHashMap();
        updateMap.put(GoalRuleObj.STATUS, "0");
        updateMap.put(GoalRuleObj.DISABLE_REASON, "员工停用");// ignoreI18n
        //发送MQ时，状态手动改过来，不再查库
        goalRuleData.set("status", "0");
        serviceFacade.updateWithMap(context.getUser(), goalRuleData, updateMap);
        goalRuleActionService.sendMessage(ObjectAction.INVALID, goalRuleData, goalRuleDetailDatas);
        return DisableRule.Result.builder().success(true).build();
    }

    /**
     * 删除规则
     *
     * @param context
     * @param arg
     * @return
     */
    @ServiceMethod("delete_Rule")
    public DeleteRule.Result deleteRule(ServiceContext context, DeleteRule.Arg arg) {
        goalRuleActionService.deleteRule(context.getUser(), arg.getRuleId());

        return DeleteRule.Result.builder().success(true).build();
    }

    /**
     * 重新触发目标规则MQ给BI(一个或者多个目标规则ID)
     *
     * @param context
     * @param arg
     * @return
     */
    @ServiceMethod("second_Send_Message")
    public SecondSendMessage.Result secondSendMessage(ServiceContext context, SecondSendMessage.Arg arg) {
        arg.getRuleIdAndtenantId().forEach((k, v) -> {
            User user = User.builder().tenantId(v).userId(User.SUPPER_ADMIN_USER_ID).build();
            List<IObjectData> goalRuleDataList = goalRuleCommonService.findGoalRuleByIds(user, Collections.singletonList(k));
            List<IObjectData> goalRuleDetailDataList = goalRuleCommonService.findGoalRulesByIds(user, Collections.singletonList(k));

            List<IObjectData> goalRuleDetailData = goalRuleDetailDataList.stream()
                    .filter(detailData -> detailData.get(GoalRuleObj.GOAL_RULE_ID, String.class).equals(goalRuleDataList.get(0).getId()))
                    .collect(Collectors.toList());
            goalRuleActionService.sendMessage(ObjectAction.CREATE, goalRuleDataList.get(0), goalRuleDetailData);
        });
        return SecondSendMessage.Result.builder().success(true).build();
    }

    /**
     * 企业注册后初始化默认规则
     *
     * @param context
     * @return
     */
    @ServiceMethod("init_Default_Rule")
    public InitDefaultRule.Result initDefaultRule(ServiceContext context) {
        goalRuleActionService.initDefaultRule(context.getUser());
        return InitDefaultRule.Result.builder().success(true).build();
    }

    /**
     * 初始化目标规则
     *
     * @param context
     * @param arg
     * @return
     */
    @ServiceMethod("init_Goal_Rule")
    public InitGoalRule.Result initGoalRule(ServiceContext context, InitGoalRule.Arg arg) {
        arg.getApiNames().forEach(apiName -> {
            IObjectData goalRule = goalRuleActionService.initGoalRule(context.getUser(), apiName);
            if (arg.getEnableRule()) {
                enableRule(context, goalRule);
            }
        });
        return InitGoalRule.Result.builder().success(true).build();
    }

    /**
     * 6.5灰度时把老数据同步给BI
     *
     * @param context
     * @return
     */
    @ServiceMethod("init_Goal_Data")
    public InitGoalData.Result initGoalData(ServiceContext context) {
        log.debug("TenantId: ", context.getTenantId());
        int total = 1;
        String dataSql = "select count(1) as number from goal_rule where is_deleted = '0' and tenant_id = " + "'" + context.getTenantId() + "'";
        QueryResult<IObjectData> dataQueryResult = null;
        try {
            dataQueryResult = objectDataService.findBySql(dataSql, context.getTenantId(),
                    "GoalValueObj");
        } catch (MetadataServiceException e) {
            log.error("goal_rule->initGoalData error", e);
        }

        if (dataQueryResult == null || CollectionUtils.empty(dataQueryResult.getData())) {
            total = 1;
        }
        IObjectData count = null;
        //sonar处理
        if(dataQueryResult != null && CollectionUtils.notEmpty(dataQueryResult.getData())) {
            count = dataQueryResult.getData().get(0);
        }

        Integer size = count == null ? Integer.valueOf(0) : (Integer) count.get("number");
        int sizeInt = size;
        double sizeDouble = (double) sizeInt;
        double sizefinal = sizeDouble / 100.0;
        total = (int) Math.ceil(sizeDouble / 100.0);

        InitGoalData.Arg arg = new InitGoalData.Arg();
        QueryResult<IObjectData> queryResult = new QueryResult();
        for (int i = 1; i <= total; i++) {
            arg.setPageNumber(i);
            queryResult = serviceFacade.findBySearchQuery(context.getUser(),
                    "GoalValueObj", arg.buildSearchQuery());
            List<IObjectData> goalRuleDataList = queryResult.getData();
            List<String> ids = goalRuleDataList.stream().map(x -> String.valueOf(x.get(GoalRuleObj.ID))).distinct().collect(Collectors.toList());

            List<IObjectData> goalRuleDetailDataList = goalRuleCommonService.findGoalRulesByIds(context.getUser(), ids);
            goalRuleDataList.forEach(data -> {
                List<IObjectData> goalRuleDetails = new ArrayList<>();
                goalRuleDetailDataList.forEach(dataDetail -> {
                    if (data.getId().equals(dataDetail.get(GoalRuleObj.GOAL_RULE_ID))) {
                        goalRuleDetails.add(dataDetail);
                    }
                });
                goalRuleActionService.sendMessage(ObjectAction.CREATE, data, goalRuleDetails);

            });
        }
        log.debug("initGoalData finish");
        return InitGoalData.Result.builder().success(true).build();
    }

  private boolean isNeedInitGoalValue(User user,
                                      IObjectData goalRule,
                                      List<IObjectData> goalRuleDetails) {
        if(StringUtils.isEmpty(goalRule.getId())){
            return false;
        }
      IObjectDescribe goalRuleDetaildescribe = serviceFacade.findObject(user.getTenantId(),"GoalValueObj");
      IObjectData oldGoalRule = serviceFacade.findObjectData(user, goalRule.getId(), "GoalValueObj");
      List<IObjectData> oldDetailDataList = serviceFacade.findDetailObjectDataList(goalRuleDetaildescribe, goalRule, user);
    return (!Objects.equals(oldGoalRule.get("check_object_api_name"), goalRule.get("check_object_api_name"))
      || !Objects.equals(oldGoalRule.get("check_field_api_name"), goalRule.get("check_field_api_name"))
      || !Objects.equals(oldGoalRule.get("count_fiscal_year"), goalRule.get("count_fiscal_year"))
      || !Objects.equals(oldGoalRule.get("start_month"), goalRule.get("start_month"))
      || !CollectionUtils.isEqual(goalRuleDetails, oldDetailDataList)
    ) && "1".equals(oldGoalRule.get("status"));

  }

    /**
     * 同步目标值
     *
     * @param arg
     * @return
     */
    @ServiceMethod("sync_Goal_Value")
    public SyncGoalValue.Result syncGoalValue(SyncGoalValue.Arg arg) {
        if(StringUtils.isEmpty(arg.getTenantId())){
            log.error("syncGoalValue 参数错误。arg:{}", arg);
            return SyncGoalValue.Result.builder().success(false).build();
        }
        List<String> ruleIds = new ArrayList<>();
        if(CollectionUtils.empty(arg.getRuleIds())){
           List<IObjectData> datas = getPersonnelGoalRuleIds(arg);
            ruleIds = datas.stream().map(o -> o.get(GoalRuleObj.GOAL_RULE_ID, String.class)).collect(Collectors.toList());
        }else {
            ruleIds = arg.getRuleIds();
        }
        log.info("syncGoalValue task, arg:{}", arg);
        log.info("syncGoalValue start, tenant_id:{}, ruleIds:{}", arg.getTenantId(), ruleIds);

        User user = User.builder().tenantId(arg.getTenantId()).userId(User.SUPPER_ADMIN_USER_ID).build();
        ruleIds.forEach(ruleId->{
            this.syncGoalValue(user, ruleId);
        });
        log.info("syncGoalValue end。");
        return SyncGoalValue.Result.builder().success(true).build();
    }

    private List<IObjectData> getPersonnelGoalRuleIds(SyncGoalValue.Arg arg) {
        String dataSql = MessageFormat.format(SpecialSql.getQuerySql("GetGoalRuleIdForPersonnelSql")
          , addQuotationMarks(arg.getTenantId()));

        QueryResult<IObjectData> dataQueryResult = null;
        try {
            dataQueryResult = objectDataService.findBySql(dataSql, arg.getTenantId(),"GoalValueObj");
        } catch (Exception e) {
            log.error("getPersonnelGoalRuleIds error.");
        }

        if (dataQueryResult == null || CollectionUtils.empty(dataQueryResult.getData())) {
            return Lists.newArrayList();
        }
        return dataQueryResult.getData();
    }
}
