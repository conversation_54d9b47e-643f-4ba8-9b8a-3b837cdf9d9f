package com.facishare.crm.sfa.predefine.service.GoalValue.service;

import com.facishare.crm.common.exception.CrmCheckedException;
import com.facishare.crm.sfa.predefine.enums.GoalEnum;
import com.facishare.crm.sfa.predefine.service.GoalValue.dto.*;
import com.facishare.crm.sfa.predefine.service.GoalValue.utilities.*;
import com.facishare.crm.sfa.predefine.service.GoalValue.utilities.SearchUtil;
import com.facishare.crm.sfa.predefine.service.GoalValue.dto.*;
import com.facishare.organization.api.exception.OrganizationException;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.BatchGetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.BatchGetDepartmentDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.QueryDeptByName;
import com.facishare.paas.appframework.common.service.dto.QueryMemberInfosByDeptIds;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectLockStatus;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.google.common.base.Charsets;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.hash.Hashing;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.GoalValue.utilities.I18NTransformationUtil.transformRuleFilterEntityList;

@ServiceModule("goal_value")
@Component
@Slf4j
public class GoalValueService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private GoalRuleCommonService goalRuleCommonService;
    @Autowired
    private GoalValueCommonService goalValueCommonService;
    @Autowired
    private DepartmentProviderService departmentProviderService;

    @ServiceMethod("rule_filter")
    public GetRuleFilterModel.Result getRuleFilter(ServiceContext context, GetRuleFilterModel.Arg arg)
            throws MetadataServiceException, CrmCheckedException {
        List<String> userRoleList = serviceFacade.getUserRole(context.getUser());
        boolean isGoalRuleManager =
                !CollectionUtils.empty(userRoleList) && userRoleList.contains(GoalRoleConstants.GOAL_MANAGER_ROLE_CODE);
        boolean isCrmManager = serviceFacade.isAdmin(context.getUser());
        boolean isSuperManager = isGoalRuleManager || isCrmManager;
        List<String> visibleDeptIds = Lists.newArrayList();

        if (CollectionUtils.empty(visibleDeptIds) && !isSuperManager) {
            return GetRuleFilterModel.Result
                    .builder()
                    .ruleFilterList(Lists.newArrayList())
                    .build();
        }
        String themeApINameSql = "";
        String themeApiName = "";

        if (GoalEnum.ThemeTypeValue.PERSON.getValue().equals(themeApiName) || GoalEnum.ThemeTypeValue.DEPT.getValue().equals(themeApiName)) {
            themeApINameSql = " AND (M.theme_api_name is null or M.theme_api_name='PersonnelObj')";
            if (GoalEnum.ThemeTypeValue.DEPT.getValue().equals(themeApiName)) {
                themeApINameSql = themeApINameSql + " AND (M.dept_field_api_name IS NOT NULL AND M.dept_field_api_name NOT IN ('main_department','owner_department'))";
            }
        } else if (StringUtils.isNotEmpty(themeApiName)) {
            themeApINameSql = String.format(" AND M.theme_api_name ='%s' ", StringEscapeUtils.escapeSql(themeApiName));
        }
        List<String> accountIds = new ArrayList<>();
        List<String> productIds = new ArrayList<>();
        if (GoalEnum.ThemeTypeValue.ACCOUNT.getValue().equals(themeApiName) && !isSuperManager) {
            List<IObjectData> accountGoalValueData = goalValueCommonService.getAllValueByThemeApiName(context.getUser(), GoalEnum.ThemeTypeValue.ACCOUNT.getValue());
            List<String> accountIdsForGoalValue = accountGoalValueData.stream().map(data -> data.get(GoalValueConstants.CHECK_OBJECT_ID, String.class)).distinct().collect(Collectors.toList());
            List<IObjectData> accounts = goalValueCommonService.getAccountsByNameAndIds(context.getUser(), null, accountIdsForGoalValue);
            accountIds = accounts.stream().map(accountData -> accountData.getId()).collect(Collectors.toList());
        }
        if (GoalEnum.ThemeTypeValue.PRODUCT.getValue().equals(themeApiName) && !isSuperManager) {
            List<IObjectData> productGoalValueData = goalValueCommonService.getAllValueByThemeApiName(context.getUser(), GoalEnum.ThemeTypeValue.PRODUCT.getValue());
            List<String> productIdsForGoalValue = productGoalValueData.stream().map(data -> data.get(GoalValueConstants.CHECK_OBJECT_ID, String.class)).distinct().collect(Collectors.toList());
            List<IObjectData> products = goalValueCommonService.getProductsByNameAndIds(context.getUser(), null, productIdsForGoalValue);
            productIds = products.stream().map(productData -> productData.getId()).collect(Collectors.toList());
        }
        String sqlWherePartion = "";
        String deptSql = (isSuperManager) ? "" : String.format(" AND ((C.apply_circle_id IN (%s)  and coalesce(M.theme_api_name,'PersonnelObj')='PersonnelObj') OR (C.apply_circle_id IN (%s) or (" +
                        " (M.theme_api_name='AccountObj' and M.id IN (select goal_rule_id from goal_value av where  av.is_deleted=0 %s and av.goal_type='AccountObj'))  or  (M.theme_api_name='ProductObj' and M.id IN (select goal_rule_id from goal_value pv where  pv.is_deleted=0 %s and pv.goal_type='ProductObj')))))", SearchUtil
                        .convertToSqlInConditionValues(visibleDeptIds), SearchUtil
                        .convertToSqlInConditionValues(visibleDeptIds),
                CollectionUtils.empty(accountIds) ? " and 1=0 " : String.format("and av.check_object_id in (%s)", SearchUtil.convertToSqlInConditionValues(accountIds)),
                CollectionUtils.empty(productIds) ? " and 1=0 " : String.format("and pv.check_object_id in (%s)", SearchUtil.convertToSqlInConditionValues(productIds)));


        String sql = SpecialSql.getQuerySql("RuleFilter");
        String selSql = MessageFormat.format(sql,
                String.format("'%s'", StringEscapeUtils.escapeSql(context.getTenantId())), deptSql, themeApINameSql);

        log.info("rule_filter_sql:{}", selSql);
        QueryResult<IObjectData> queryResult = objectDataService.findBySql(selSql,
                context.getTenantId(),
                "GoalValueObj");

        List<GetRuleFilterModel.RuleFilterEntity> ruleFilterEntities = handleGoalRules(queryResult.getData());
        transformRuleFilterEntityList(context, ruleFilterEntities);
        return GetRuleFilterModel.Result
                .builder()
                .ruleFilterList(ruleFilterEntities)
                .themeApiName(themeApiName)
                .build();
    }

    private List<GetOrganizationModel.OrganizationEntity> getSubordinateGoalValues(User user,
                                                                                   GetOrganizationModel.Arg arg,
                                                                                   List<String> applyCircleIds,
                                                                                   List<String> existUserIds,
                                                                                   List<String> allSubordinateIds,
                                                                                   boolean isSupperRole,
                                                                                   List<IObjectData> valueDatas) {
        List<GetOrganizationModel.OrganizationEntity> organizationEntities = Lists.newArrayList();
        List<String> subordinates = serviceFacade.getSubordinateIdsByUserId(user.getTenantId(),
                user.getUserId(),
                user.getUserId(),
                false);
        List<String> userIds = Lists.newArrayList(user.getUserId());
        List<UserInfo> shareUserInfos = new ArrayList<>();
        if (CollectionUtils.notEmpty(subordinates)) {
            userIds.addAll(subordinates);
        }
        if (CollectionUtils.notEmpty(allSubordinateIds)) {
            allSubordinateIds.removeAll(userIds);
            if (CollectionUtils.notEmpty(allSubordinateIds)) {
                userIds.addAll(allSubordinateIds);
                shareUserInfos = serviceFacade.getUserNameByIds(user.getTenantId(),
                        user.getUserId(),
                        allSubordinateIds);
            }
        }
        Map<String, List<String>> userIdAndDeptIds = goalValueCommonService.getBelong2DeptIds(user.getTenantIdInt(), userIds);
        if (!isSupperRole) {
            valueDatas = valueDatas.stream().filter(o -> userIds.contains(o.get(GoalValueConstants.CHECK_OBJECT_ID, String.class))).collect(Collectors.toList());
        }
        Map<String, IObjectData> checkIdAndGoalValueMap = valueDatas.stream().collect(Collectors.toMap(obj -> obj.get(GoalValueConstants.CHECK_OBJECT_ID, String.class), obj -> obj, (v1, v2) -> v1));
        if (CollectionUtils.notEmpty(valueDatas)) {
            if (!existUserIds.contains(user.getUserId())) {
                Optional<IObjectData> valueData = Optional.ofNullable(checkIdAndGoalValueMap.get(user.getUserId()));
                if (valueData.isPresent()) {
                    GetOrganizationModel.OrganizationEntity organizationEntity = handlePersonalValue(user,
                            arg,
                            applyCircleIds,
                            Boolean.TRUE,
                            null,
                            valueData.get(),
                            userIdAndDeptIds);
                    if (organizationEntity != null) {
                        organizationEntities.add(organizationEntity);
                    }
                }

            }

            if (CollectionUtils.notEmpty(subordinates)) {
                for (String subordinate : subordinates) {
                    if (!existUserIds.contains(subordinate)) {
                        Optional<IObjectData> valueData = Optional.ofNullable(checkIdAndGoalValueMap.get(subordinate));
                        if (valueData.isPresent()) {
                            User subUser = new User(user.getTenantId(), subordinate);
                            subUser.setUserName("");
                            GetOrganizationModel.OrganizationEntity orgEntity = handlePersonalValue(subUser,
                                    arg,
                                    applyCircleIds,
                                    Boolean.FALSE,
                                    user.getUserId(),
                                    valueData.get(),
                                    userIdAndDeptIds);
                            if (orgEntity != null) {
                                organizationEntities.add(orgEntity);
                            }
                        }
                    }
                }
            }

            for (UserInfo shareUserInfo : shareUserInfos) {
                if (!existUserIds.contains(shareUserInfo.getId())) {
                    Optional<IObjectData> valueData = Optional.ofNullable(checkIdAndGoalValueMap.get(shareUserInfo.getId()));
//                    Optional<IObjectData> valueData = valueDatas.stream()
//                                                                .filter(data -> String.valueOf(data.get(GoalValueConstants.CHECK_OBJECT_ID)).equals(shareUserInfo.getId()))
//                                                                .findFirst();
                    if (valueData.isPresent()) {
                        User subUser = new User(user.getTenantId(), shareUserInfo.getId());
                        subUser.setUserName(shareUserInfo.getNickname());
                        GetOrganizationModel.OrganizationEntity orgEntity = handlePersonalValue(subUser,
                                arg,
                                applyCircleIds,
                                Boolean.TRUE,
                                null,
                                valueData.get(),
                                userIdAndDeptIds);
                        if (orgEntity != null) {
                            organizationEntities.add(orgEntity);
                        }
                    }
                }
            }
        }

        return organizationEntities;
    }

    private List<GetOrganizationModel.OrganizationEntity> getResponsibleDeptGoalValues(User user,
                                                                                       GetOrganizationModel.Arg arg,
                                                                                       List<String> applyCircleIds,
                                                                                       List<String> responsibleDeptIds,
                                                                                       boolean isSupperRole,
                                                                                       List<IObjectData> allDeptGoalValues,
                                                                                       List<IObjectData> allMemberGoalValues) {
        List<GetOrganizationModel.OrganizationEntity> organizationEntities = Lists.newArrayList();

        List<QueryDeptByName.DeptInfo> allSubResponsibleDeptsWithSelf = Lists.newArrayList();

        allSubResponsibleDeptsWithSelf.addAll(convert(getBatchGetDepartmentDto(user, responsibleDeptIds)));
//        subDeptsMap.values().forEach(depts -> allSubResponsibleDeptsWithSelf.addAll(depts));
        List<QueryDeptByName.DeptInfo> distinctSubResponsibleDeptsWithSelf = allSubResponsibleDeptsWithSelf.stream()
                .distinct()
                .collect(Collectors.toList());
        List<String> allSubResponsibleDeptIds = distinctSubResponsibleDeptsWithSelf.stream()
                .map(deptInfo -> deptInfo.getId())
                .distinct()
                .collect(Collectors.toList());
        allSubResponsibleDeptIds.retainAll(applyCircleIds);
        if (CollectionUtils.notEmpty(allSubResponsibleDeptIds)) {
            List<GetOrganizationModel.OrganizationEntity> entities = handleOrgTree(user,
                    distinctSubResponsibleDeptsWithSelf,
                    allSubResponsibleDeptIds,
                    arg, isSupperRole, allDeptGoalValues, allMemberGoalValues);
            if (CollectionUtils.notEmpty(entities)) {
                organizationEntities.addAll(entities);
            }
        }

        return organizationEntities;
    }

    private List<DepartmentDto> getBatchGetDepartmentDto(User user, List<String> responsibleDeptIds) {
        List<DepartmentDto> departmentDtos = new ArrayList<>();
        try {
            BatchGetDepartmentDtoArg batchGetDepartmentDtoArg = new BatchGetDepartmentDtoArg();
            batchGetDepartmentDtoArg.setDepartmentIds(responsibleDeptIds.stream().map(Integer::valueOf).collect(Collectors.toList()));
            batchGetDepartmentDtoArg.setRunStatus(RunStatus.ACTIVE);
            batchGetDepartmentDtoArg.setEnterpriseId(user.getTenantIdInt());
            BatchGetDepartmentDtoResult batchGetDepartmentDtoResult = departmentProviderService.batchGetDepartmentDto(batchGetDepartmentDtoArg);
            departmentDtos = batchGetDepartmentDtoResult.getDepartments();
        } catch (OrganizationException e) {
            log.error("getBatchGetDepartmentDto error", e);
        }
        return departmentDtos;
    }

    private Collection<? extends QueryDeptByName.DeptInfo> convert(List<DepartmentDto> depts) {
        List<QueryDeptByName.DeptInfo> deptInfos = new ArrayList<>();
        if (CollectionUtils.empty(depts)) {
            return deptInfos;
        }
        for (DepartmentDto dept : depts) {
            QueryDeptByName.DeptInfo deptInfo = new QueryDeptByName.DeptInfo();
            List<String> ancestors = new ArrayList<>();
            if (CollectionUtils.notEmpty(dept.getAncestors())) {
                dept.getAncestors().forEach(obj -> ancestors.add(String.valueOf(obj)));
            }
            deptInfo.setId(String.valueOf(dept.getDepartmentId()));
            deptInfo.setName(dept.getName());
            deptInfo.setParentId(dept.parentId());
            deptInfo.setAncestors(ancestors);
            deptInfos.add(deptInfo);
        }

        return deptInfos;
    }

    private void setCurrentUserName(User user) {
        List<UserInfo> userInfos = serviceFacade.getUserNameByIds(user.getTenantId(),
                user.getUserId(),
                Lists.newArrayList(user.getUserId()));
        if (CollectionUtils.notEmpty(userInfos)) {
            user.setUserName(userInfos.get(0).getName());
        }
    }

    private List<GetOrganizationModel.OrganizationEntity> getVisibleGoalValues(User user,
                                                                               GetOrganizationModel.Arg arg,
                                                                               List<String> applyCircleIds,
                                                                               List<String> responsibleDeptIds,
                                                                               List<String> allSubordinateIds,
                                                                               List<IObjectData> allDeptGoalValues,
                                                                               List<IObjectData> allMemberGoalValues) {
        List<GetOrganizationModel.OrganizationEntity> organizationEntities = Lists.newArrayList();
        List<String> existUserIds = Lists.newArrayList();
        List<String> userRoleList = serviceFacade.getUserRole(user);
        boolean isGoalRuleManager = CollectionUtils.empty(userRoleList) ? false : userRoleList.contains(GoalRoleConstants.GOAL_MANAGER_ROLE_CODE);
        boolean isCrmManager = serviceFacade.isAdmin(user);
        boolean isSupperRole = isGoalRuleManager || isCrmManager;

        if (CollectionUtils.notEmpty(responsibleDeptIds)) {
            List<GetOrganizationModel.OrganizationEntity> deptEntities = getResponsibleDeptGoalValues(user,
                    arg,
                    applyCircleIds,
                    responsibleDeptIds,
                    isSupperRole,
                    allDeptGoalValues, allMemberGoalValues);
            if (CollectionUtils.notEmpty(deptEntities)) {
                organizationEntities.addAll(deptEntities);
                for (GetOrganizationModel.OrganizationEntity organizationEntity : deptEntities) {
                    if (organizationEntity.getGoalType().equals(GoalEnum.GoalTypeValue.EMPLOYEE.getValue())) {
                        existUserIds.add(organizationEntity.getCheckObjectId());
                    }
                }
            }
        }

        setCurrentUserName(user);
        List<GetOrganizationModel.OrganizationEntity> subordinateEntities = getSubordinateGoalValues(user,
                arg,
                applyCircleIds,
                existUserIds,
                allSubordinateIds, isSupperRole, allMemberGoalValues);
        if (CollectionUtils.notEmpty(subordinateEntities)) {
            organizationEntities.addAll(subordinateEntities);
        }
        return organizationEntities.stream().distinct().collect(Collectors.toList());
    }

    private GetOrganizationModel.OrganizationEntity getSpecifiedPersonalGoalValue(User user,
                                                                                  GetOrganizationModel.Arg arg,
                                                                                  List<String> applyCircleIds) {
        List<UserInfo> userInofs = serviceFacade.getUserNameByIds(user.getTenantId(),
                user.getUserId(),
                Lists.newArrayList(arg.getCheckObjectId()));
        User newUser = new User(user.getTenantId(), arg.getCheckObjectId());
        if (CollectionUtils.notEmpty(userInofs)) {
            newUser.setUserName(userInofs.get(0).getName());
        }
        List<IObjectData> valueDatas = goalValueCommonService.findGoalValues(user,
                arg.getGoalRuleId(),
                arg.getGoalRuleDetailId(),
                arg.getFiscalYear(),
                GoalEnum.GoalTypeValue.EMPLOYEE.getValue(),
                Lists.newArrayList(newUser.getUserId()));
        if (CollectionUtils.notEmpty(valueDatas)) {
            return handlePersonalValue(newUser, arg, applyCircleIds, Boolean.TRUE, null, valueDatas.get(0));
        }
        return null;
    }

    @ServiceMethod("organization_tree")
    public GetOrganizationModel.Result getOrganizationTree(ServiceContext context, GetOrganizationModel.Arg arg)
            throws CrmCheckedException {
        List<GetOrganizationModel.OrganizationEntity> organizationEntities = Lists.newArrayList();

        IObjectData ruleData = validateRule(context.getUser(),
                arg.getGoalRuleId(),
                arg.getFiscalYear(),
                arg.getContainGoalValue());

        List<String> checkCircleIds = getApplyCircleIds(context.getUser(), arg.getGoalRuleId());
        validateDetailRule(context.getUser(), arg.getGoalRuleId(), arg.getGoalRuleDetailId());
        //TODO 当前用户可见的部门
//        VisibleDeptModel visibleDeptModel = goalValueCommonService.getVisibleDeptIdsContainsShareData(context.getUser(), Boolean.FALSE);
        VisibleDeptModel visibleDeptModel = goalValueCommonService.getVisibleDeptIdsContainsShareDataForOrgTree(context.getUser());
        validateInApplyCircle(checkCircleIds, visibleDeptModel.getVisibleDeptIds());
        List<IObjectData> allValueDatas = goalValueCommonService.findAllGoalValuesWithSql(context.getUser(),
                arg.getGoalRuleId(),
                arg.getGoalRuleDetailId(),
                arg.getFiscalYear());
        List<IObjectData> allDeptGoalValues = allValueDatas.stream().filter(value -> GoalEnum.GoalTypeValue.CIRCLE.getValue().equals(value.get(GoalValueConstants.GOAL_TYPE, String.class))).collect(Collectors.toList());
        List<IObjectData> allMemberGoalValues = allValueDatas.stream().filter(value -> GoalEnum.GoalTypeValue.EMPLOYEE.getValue().equals(value.get(GoalValueConstants.GOAL_TYPE, String.class))).collect(Collectors.toList());

        if (Strings.isNullOrEmpty(arg.getGoalType()) || Strings.isNullOrEmpty(arg.getCheckObjectId())) {
            List<GetOrganizationModel.OrganizationEntity> visibleGoalValues = getVisibleGoalValues(context.getUser(),
                    arg,
                    checkCircleIds,
                    visibleDeptModel.getResponsibleDeptIds(),
                    visibleDeptModel.getAllSubordinateIds(),
                    allDeptGoalValues,
                    allMemberGoalValues
            );
            if (CollectionUtils.notEmpty(visibleGoalValues)) {
                organizationEntities.addAll(visibleGoalValues);
            }
        } else {//TODO 什么情况走这里
            validateInResponsible(visibleDeptModel.getResponsibleDeptIds(),
                    visibleDeptModel.getAllSubordinateIds(),
                    arg.getGoalType(),
                    arg.getCheckObjectId());
            if (arg.getGoalType().equals(GoalEnum.GoalTypeValue.EMPLOYEE.getValue())) {
                GetOrganizationModel.OrganizationEntity organizationEntity = getSpecifiedPersonalGoalValue(context.getUser(),
                        arg,
                        checkCircleIds);
                if (organizationEntity != null) {
                    organizationEntities.add(organizationEntity);
                }
            } else if (arg.getGoalType().equals(GoalEnum.GoalTypeValue.CIRCLE.getValue())) {
                List<GetOrganizationModel.OrganizationEntity> deptGoalValues = getResponsibleDeptGoalValues(context.getUser(),
                        arg,
                        checkCircleIds,
                        Lists.newArrayList(arg.getCheckObjectId()), false, allDeptGoalValues, allMemberGoalValues);
                if (CollectionUtils.notEmpty(deptGoalValues)) {
                    organizationEntities.addAll(deptGoalValues);
                }
            }
        }

        Boolean lock = Boolean.FALSE;
        Boolean unlock = Boolean.FALSE;
        if (serviceFacade.isAdmin(context.getUser())) {
            boolean isLock = goalValueCommonService.isLock(context.getUser(),
                    arg.getGoalRuleId(),
                    arg.getGoalRuleDetailId(),
                    arg.getFiscalYear(),
                    GoalEnum.GoalTypeValue.CIRCLE.getValue(),
                    checkCircleIds.get(0));
            lock = !isLock;
            unlock = isLock;
        }
        Boolean allowPersonalModify = String.valueOf(ruleData.get(GoalRuleObj.ALLOW_PERSONAL_MODIFY_GOAL)).equals("1");
        return GetOrganizationModel.Result
                .builder()
                .result(organizationEntities)
                .lockable(lock)
                .unlockable(unlock)
                .allowPersonalModify(allowPersonalModify)
                .build();
    }

    @ServiceMethod("goal_value_list")
    public GetOrganizationModel.Result getGoalValueListForTerminal(ServiceContext context, GetOrganizationModel.Arg arg)
            throws CrmCheckedException {
        List<GetOrganizationModel.OrganizationEntity> organizationEntities = Lists.newArrayList();

        IObjectData ruleData = validateRule(context.getUser(),
                arg.getGoalRuleId(),
                arg.getFiscalYear(),
                arg.getContainGoalValue());

        List<String> checkCircleIds = getApplyCircleIds(context.getUser(), arg.getGoalRuleId());
        validateDetailRule(context.getUser(), arg.getGoalRuleId(), arg.getGoalRuleDetailId());
        List<String> userRoleList = serviceFacade.getUserRole(context.getUser());
        boolean isGoalRuleManager = CollectionUtils.empty(userRoleList) ? false : userRoleList.contains(GoalRoleConstants.GOAL_MANAGER_ROLE_CODE);
        boolean isCrmManager = serviceFacade.isAdmin(context.getUser());
        boolean isSuperRole = isCrmManager && isGoalRuleManager;
        List<IObjectData> accounts = new ArrayList<>();
        List<IObjectData> products = new ArrayList<>();
        List<IObjectData> partners = new ArrayList<>();
        List<String> checkObjIds = new ArrayList<>();
        List<IObjectData> goalValues = new ArrayList<>();
        List<String> dbCheckObjIds = new ArrayList<>();
        Map checkIdAndObjMap = new HashedMap();
        String checkLevelFieldApiName = ruleData.get(GoalRuleObj.CHECK_LEVEL_FIELD_API_NAME, String.class);
        String checkLevelType = ruleData.get(GoalRuleObj.CHECK_LEVEL_TYPE, String.class);
        String checkCycle = ruleData.get(GoalRuleObj.CHECK_CYCLE, String.class);

        boolean isCheckForLevel = GoalRuleConstants.OPTIONAL_LEVEL_TYPE.equals(checkLevelType) || GoalRuleConstants.PROVINCE_CITY_LEVEL_TYPE.equals(checkLevelType);
        String checkObjectId = arg.getCheckObjectId();
        Boolean lock = Boolean.FALSE;
        Boolean unlock = Boolean.FALSE;
        if (serviceFacade.isAdmin(context.getUser())) {
            boolean isLock = goalValueCommonService.isLock(context.getUser(),
                    arg.getGoalRuleId(),
                    arg.getGoalRuleDetailId(),
                    arg.getFiscalYear(),
                    arg.getGoalType(),
                    "");
            lock = !isLock;
            unlock = isLock;
        }
        Boolean allowPersonalModify = String.valueOf(ruleData.get(GoalRuleObj.ALLOW_PERSONAL_MODIFY_GOAL)).equals("1");
        return GetOrganizationModel.Result
                .builder()
                .result(organizationEntities)
                .lockable(lock)
                .unlockable(unlock)
                .allowPersonalModify(allowPersonalModify)
                .build();
    }


    private IObjectData validateRule(User user, String ruleId, String fiscalYear, boolean validateFiscalYear) {
        IObjectData ruleData = goalRuleCommonService.findGoalRule(user, ruleId);
        if (ruleData.isDeleted()) {
            throw new ValidateException(I18N.text(GoalRuleI18NKeyUtils.GOAL_RULE_HAS_BEEN_DELETED));
        }
        if (!String.valueOf(ruleData.get(GoalRuleObj.STATUS)).equals("1")) {
            throw new ValidateException(I18N.text(GoalRuleI18NKeyUtils.GOAL_RULE_IS_NOT_ENABLED));
        }
        if (validateFiscalYear) {
            if (!ruleData.get(GoalRuleObj.COUNT_FISCAL_YEAR).toString().contains(fiscalYear)) {
                throw new ValidateException(I18N.text(GoalRuleI18NKeyUtils.GOAL_RULE_FISCAL_YEAR_BEYOND_CURRENT_));
            }
        }

        return ruleData;
    }

    private void validateDetailRule(User user, String ruleId, String detailId) {
        List<IObjectData> detailRules = goalRuleCommonService.findGoalRules(user, ruleId);
        if (CollectionUtils.empty(detailRules)) {
            if (!Strings.isNullOrEmpty(detailId)) {
                throw new ValidateException("子目标不属于当前目标规则！");// ignoreI18n
            }
        } else {
            if (Strings.isNullOrEmpty(detailId)) {
                throw new ValidateException("子目标不能为空！");// ignoreI18n
            }
            Optional<IObjectData> dataOptional = detailRules.stream()
                    .filter(detailRule -> detailRule.getId().equals(detailId))
                    .findFirst();
            if (!dataOptional.isPresent()) {
                throw new ValidateException("子目标不属于当前目标规则！");// ignoreI18n
            }
        }
    }

    private List<String> getApplyCircleIds(User user, String ruleId) {
        List<String> applyCircleIds = Lists.newArrayList();
        List<IObjectData> circleDatas = goalRuleCommonService.findGoalRuleApplyCircle(user, ruleId);
        if (CollectionUtils.empty(circleDatas)) {
            throw new ValidateException("目标无适用考核部门！");// ignoreI18n
        }
        applyCircleIds = circleDatas.stream()
                .map(data -> String.valueOf(data.get(GoalRuleApplyCircleObj.FIELD_APPLY_CIRCLE_ID)))
                .collect(Collectors.toList());
        return applyCircleIds;
    }

    private void validateInApplyCircle(List<String> applyCircleIds, List<String> visibleDeptIds) {
        if (CollectionUtils.empty(visibleDeptIds)) {
            throw new ValidateException("不在目标适用考核部门！");// ignoreI18n
        }
        visibleDeptIds.retainAll(applyCircleIds);

        if (CollectionUtils.empty(visibleDeptIds)) {
            throw new ValidateException("不在目标适用考核部门！");// ignoreI18n
        }
    }

    private void validateInResponsible(List<String> responsibleDeptIds, List<String> subordianteIds, String goalType,
                                       String checkObjectId) {
        if (GoalEnum.GoalTypeValue.CIRCLE.getValue().equals(goalType)) {
            if (CollectionUtils.empty(responsibleDeptIds) || !responsibleDeptIds.contains(checkObjectId)) {
                throw new ValidateException("部门超出负责范围！");// ignoreI18n
            }
        }

        if (GoalEnum.GoalTypeValue.EMPLOYEE.getValue().equals(goalType)) {
            if (CollectionUtils.empty(subordianteIds) || !subordianteIds.contains(checkObjectId)) {
                throw new ValidateException("人员超出负责范围！");// ignoreI18n
            }
        }
    }

    private void validateVisibleScope(User user, String ruleId, String goalType, String checkObjectId) {
        if (GoalEnum.ThemeTypeValue.ACCOUNT.getValue().equals(goalType) || GoalEnum.ThemeTypeValue.PRODUCT.getValue().equals(goalType)) {
            return;
        }
        List<String> applyCircleIds = getApplyCircleIds(user, ruleId);
        VisibleDeptModel visibleDeptModel = new VisibleDeptModel();
        try {
            visibleDeptModel = goalValueCommonService.getVisibleDeptIds(user, Boolean.FALSE);
        } catch (Exception e) {
            log.error("goal_value->validateVisibleScope error", e);
        }
        validateInApplyCircle(applyCircleIds, visibleDeptModel.getVisibleDeptIds());
        validateInResponsible(visibleDeptModel.getResponsibleDeptIds(),
                visibleDeptModel.getAllSubordinateIds(),
                goalType,
                checkObjectId);
    }

    @ServiceMethod("detail")
    public GetDetailModel.Result getDetail(ServiceContext context, GetDetailModel.Arg arg) {
        IObjectData ruleData = validateRule(context.getUser(), arg.getGoalRuleId(), arg.getFiscalYear(), true);
        validateDetailRule(context.getUser(), arg.getGoalRuleId(), arg.getGoalRuleDetailId());
        validateVisibleScope(context.getUser(), arg.getGoalRuleId(), arg.getGoalType(), arg.getCheckObjectId());
        List<String> userIds = StringUtils.isNotEmpty(arg.getCheckObjectId()) ? Lists.newArrayList(arg.getCheckObjectId()) : new ArrayList<>();
        IObjectData data = new ObjectData();
        Integer startMonth = 1;
        String firstMonth = "";
        boolean editable = false;
        List<IObjectData> objectDataList = goalValueCommonService.findGoalValues(context.getUser(),
                arg.getGoalRuleId(),
                arg.getGoalRuleDetailId(),
                arg.getFiscalYear(),
                arg.getGoalType(),
                userIds);
        if (CollectionUtils.notEmpty(objectDataList)) {
            IObjectData objectData = objectDataList.get(0);
            Object oMonth = ruleData.get(GoalRuleObj.START_MONTH);
            String checkCycle = ruleData.get(GoalRuleObj.CHECK_CYCLE, String.class);
            if (oMonth != null) {
                startMonth = Integer.valueOf(oMonth.toString());
            }
            Map<Integer, String> monthMapping = goalRuleCommonService.getMonthData(checkCycle);

            data.setId(objectData.getId());
            Object oAnnualValue = objectData.get(GoalValueConstants.ANNUAL_VALUE);
            if (oAnnualValue != null) {
                data.set(GoalValueConstants.ANNUAL_VALUE, oAnnualValue.toString());
            } else {
                data.set(GoalValueConstants.ANNUAL_VALUE, "");
            }
            int cycleCount;
            if (Objects.equals(checkCycle, GoalEnum.GoalCheckCycle.WEEK.getValue())) {
                cycleCount = 53;
            } else if (Objects.equals(checkCycle, GoalEnum.GoalCheckCycle.QUARTER.getValue())) {
                cycleCount = 4;
            } else if (Objects.equals(checkCycle, GoalEnum.GoalCheckCycle.YEAR.getValue())) {
                cycleCount = 1;
            } else {
                cycleCount = 12;
            }
            for (int i = 0; i < cycleCount; i++) {
                int month = i + 1;
                Object oMonthValue = objectData.get(monthMapping.get(month));
                if (oMonthValue != null) {
                    data.set(monthMapping.get(month), oMonthValue.toString());
                } else {
                    data.set(monthMapping.get(month), "");
                }
            }

            firstMonth = monthMapping.get(startMonth);
            String lockStatus = String.valueOf(objectData.get("lock_status"));
            if (lockStatus.equals(ObjectLockStatus.LOCK.getStatus())) {
                editable = serviceFacade.isAdmin(context.getUser());
            } else {
                editable = true;
            }
        }

        return GetDetailModel.Result
                .builder()
                .data(ObjectDataDocument.of(data))
                .startMonth(firstMonth)
                .editable(editable)
                .build();
    }

    @ServiceMethod("lock")
    public LockGoalRuleModel.Result lockGoalRule(ServiceContext context, LockGoalRuleModel.Arg arg) {
        List<String> userRoleList = serviceFacade.getUserRole(context.getUser());
        boolean isGoalRuleManager =
                !CollectionUtils.empty(userRoleList) && userRoleList.contains(GoalRoleConstants.GOAL_MANAGER_ROLE_CODE);
        if (!serviceFacade.isAdmin(context.getUser()) && !isGoalRuleManager) {
            throw new ValidateException("无权操作！");// ignoreI18n
        }
        IObjectData ruleData = validateRule(context.getUser(), arg.getGoalRuleId(), arg.getFiscalYear(), true);
        List<String> applyCircleIds = getApplyCircleIds(context.getUser(), arg.getGoalRuleId());
        validateDetailRule(context.getUser(), arg.getGoalRuleId(), arg.getGoalRuleDetailId());
        String goalValueType = "";
        String themeApiName = ruleData.get(GoalRuleObj.THEME_API_NAME, String.class);
        String checkId = "";
        if (org.springframework.util.StringUtils.isEmpty(themeApiName) || GoalEnum.ThemeTypeValue.PERSON.getValue().equals(themeApiName)) {
            goalValueType = GoalEnum.GoalTypeValue.CIRCLE.getValue();
            checkId = applyCircleIds.get(0);
        } else {
            goalValueType = themeApiName;
        }
        boolean isLock = goalValueCommonService.isLock(context.getUser(),
                arg.getGoalRuleId(),
                arg.getGoalRuleDetailId(),
                arg.getFiscalYear(),
                goalValueType,
                checkId);

        if (isLock == arg.getLock()) {
            if (arg.getLock()) {
                throw new ValidateException("目标值已被锁定！");// ignoreI18n
            } else {
                throw new ValidateException("目标值已被解锁！");// ignoreI18n
            }
        }

        Boolean lockable = Boolean.FALSE;
        Boolean unlockable = Boolean.FALSE;
        goalValueCommonService.lockGoalValue(context.getUser(),
                arg.getGoalRuleId(),
                arg.getGoalRuleDetailId(),
                arg.getFiscalYear(),
                arg.getLock());
        if (arg.getLock()) {
            unlockable = Boolean.TRUE;
        } else {
            lockable = Boolean.TRUE;
        }

        return LockGoalRuleModel.Result.builder()
                .success(Boolean.TRUE)
                .lockable(lockable)
                .unlockable(unlockable)
                .build();
    }

    private GetOrganizationModel.OrganizationEntity handlePersonalValue(User user,
                                                                        GetOrganizationModel.Arg arg,
                                                                        List<String> checkCircleIds,
                                                                        Boolean parentLeaf,
                                                                        String parentId,
                                                                        IObjectData valueData) {
        GetOrganizationModel.OrganizationEntity organizationEntity = null;
        IObjectData ruleData = goalRuleCommonService.findGoalRule(user, arg.getGoalRuleId());
        String checkCycle = ruleData.get(GoalRuleObj.CHECK_CYCLE, String.class);
        List<String> mainDeptIds = goalValueCommonService.getBelong2DeptIds(user, Lists.newArrayList(user.getUserId()));
        if (CollectionUtils.notEmpty(mainDeptIds)) {
            mainDeptIds.retainAll(checkCircleIds);
            if (CollectionUtils.notEmpty(mainDeptIds)) {
                organizationEntity = new GetOrganizationModel.OrganizationEntity();
                organizationEntity.setId(valueData == null ? "" : valueData.getId());
                organizationEntity.setCheckObjectId(user.getUserId());
                organizationEntity.setGoalType(GoalEnum.GoalTypeValue.EMPLOYEE.getValue());
                if (arg.getContainGoalValue()) {
                    organizationEntity.setGoalValue(valueData == null ? "" : getMonthlyValue(valueData, arg.getMonth(), checkCycle));
                    if (!Strings.isNullOrEmpty(parentId)) {
                        organizationEntity.setParentId(parentId);
                    }
                } else {
                    organizationEntity.setParentId(mainDeptIds.get(0));
                }
                organizationEntity.setCheckObjectName(user.getUserName());
                organizationEntity.setParentLeaf(parentLeaf);
            }
        }

        return organizationEntity;
    }

    private GetOrganizationModel.OrganizationEntity handlePersonalValue(User user,
                                                                        GetOrganizationModel.Arg arg,
                                                                        List<String> checkCircleIds,
                                                                        Boolean parentLeaf,
                                                                        String parentId,
                                                                        IObjectData valueData,
                                                                        Map<String, List<String>> userIdAndDeptids) {
        GetOrganizationModel.OrganizationEntity organizationEntity = null;
//        List<String> mainDeptIds = goalValueCommonService.getBelong2DeptIds(user, Lists.newArrayList(user.getUserId()));
        List<String> mainDeptIds = userIdAndDeptids.get(user.getUserId());
        IObjectData ruleData = goalRuleCommonService.findGoalRule(user, arg.getGoalRuleId());
        String checkCycle = ruleData.get(GoalRuleObj.CHECK_CYCLE, String.class);
        if (CollectionUtils.notEmpty(mainDeptIds)) {
            mainDeptIds.retainAll(checkCircleIds);
            if (CollectionUtils.notEmpty(mainDeptIds)) {
                organizationEntity = new GetOrganizationModel.OrganizationEntity();
                organizationEntity.setId(valueData == null ? "" : valueData.getId());
                organizationEntity.setCheckObjectId(user.getUserId());
                organizationEntity.setGoalType(GoalEnum.GoalTypeValue.EMPLOYEE.getValue());
                if (arg.getContainGoalValue()) {
                    organizationEntity.setGoalValue(valueData == null ? "" : getMonthlyValue(valueData, arg.getMonth(), checkCycle));
                    if (!Strings.isNullOrEmpty(parentId)) {
                        organizationEntity.setParentId(parentId);
                    }
                } else {
                    organizationEntity.setParentId(mainDeptIds.get(0));
                }
                organizationEntity.setCheckObjectName(user.getUserName());
                organizationEntity.setParentLeaf(parentLeaf);
            }
        }

        return organizationEntity;
    }

    private List<GetOrganizationModel.OrganizationEntity> handleOrgTree(User user,
                                                                        List<QueryDeptByName.DeptInfo> allDeptInfos,
                                                                        List<String> checkDeptIds,
                                                                        GetOrganizationModel.Arg arg,
                                                                        boolean isSupperRole,
                                                                        List<IObjectData> allDeptGoalValues,
                                                                        List<IObjectData> allMemberGoalValues) {
        List<QueryDeptByName.DeptInfo> checkDeptInofs = allDeptInfos.stream()
                .filter(deptInfo -> checkDeptIds.contains(deptInfo.getId()))
                .collect(Collectors.toList());
//        List<String> highestDeptIds = getHighestDeptIds(checkDeptInofs);
        Map<String, List<QueryMemberInfosByDeptIds.Member>> memberMap = serviceFacade.getMemberInfoMapByDeptIds(user,
                checkDeptIds,
                Boolean.FALSE,
                null,
                1);
        List<String> deptMemberIds = Lists.newArrayList();
        if (CollectionUtils.notEmpty(memberMap)) {
            memberMap.values().forEach(members -> members.forEach(member -> deptMemberIds.add(member.getId())));
        }
        List<String> memberIds = deptMemberIds.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.notEmpty(memberIds)) {
            Map<String, List<String>> userIdAndDeptIds = goalValueCommonService.getBelong2DeptIds(user.getTenantIdInt(), memberIds);
            memberIds = memberIds.stream().filter(id -> userIdAndDeptIds.containsKey(id)).collect(Collectors.toList());
        }

//        List<IObjectData> allDeptGoalValues = Lists.newArrayList();
//        List<IObjectData> allMemberGoalValues = Lists.newArrayList();
//        allDeptGoalValues = goalValueCommonService.findAllGoalValues(user,
//                arg.getGoalRuleId(),
//                arg.getGoalRuleDetailId(),
//                arg.getFiscalYear(),
//                GoalEnum.GoalTypeValue.CIRCLE.getValue(),
//                null);
        allDeptGoalValues = allDeptGoalValues.stream().filter(o -> checkDeptIds.contains(o.get(GoalValueConstants.CHECK_OBJECT_ID, String.class))).collect(Collectors.toList());
//        allMemberGoalValues = goalValueCommonService.findAllGoalValues(user,
//                arg.getGoalRuleId(),
//                arg.getGoalRuleDetailId(),
//                arg.getFiscalYear(),
//                GoalEnum.GoalTypeValue.EMPLOYEE.getValue(),
//                null);
        List<String> finalMemberIds = memberIds;
        allMemberGoalValues = allMemberGoalValues.stream().filter(o -> finalMemberIds.contains(o.get(GoalValueConstants.CHECK_OBJECT_ID, String.class))).collect(Collectors.toList());
        //过滤停用员工被删除的目标值
        if (CollectionUtils.notEmpty(allMemberGoalValues)) {
            allMemberGoalValues = allMemberGoalValues.stream().filter(data -> !data.isDeleted()).collect(Collectors.toList());
        }
        List<GetOrganizationModel.OrganizationEntity> organizationEntities = Lists.newArrayList();
        List<IObjectData> finalAllDeptGoalValues = allDeptGoalValues;
        List<IObjectData> finalAllMemberGoalValues = allMemberGoalValues;
        IObjectData ruleData = goalRuleCommonService.findGoalRule(user, arg.getGoalRuleId());
        String checkCycle = ruleData.get(GoalRuleObj.CHECK_CYCLE, String.class);
        List<GetOrganizationModel.OrganizationEntity> entities = handleDeptValue(checkDeptInofs,
                memberMap, finalAllDeptGoalValues, finalAllMemberGoalValues,
                arg.getContainGoalValue(),
                arg.getMonth(), checkCycle);
        if (CollectionUtils.notEmpty(entities)) {
            organizationEntities.addAll(entities);
        }
//        ParallelStreamSupport.parallelStream(highestDeptIds, forkJoinPool).forEach(deptId -> {
//            try {
//                List<GetOrganizationModel.OrganizationEntity> entities = handleDeptValue(allDeptInfos,
//                  deptId,
//                  memberMap, finalAllDeptGoalValues, finalAllMemberGoalValues,
//                  arg.getContainGoalValue(),
//                  arg.getMonth(),
//                  Boolean.TRUE);
//                if (CollectionUtils.notEmpty(entities)) {
//                    organizationEntities.addAll(entities);
//                }
//            } catch (Exception e) {
//                log.error("handleDeptValue error", e);
//            }
//        });

        return organizationEntities;
    }

    private List<GetOrganizationModel.OrganizationEntity> handleDeptValue(List<QueryDeptByName.DeptInfo> allDeptInfos,
                                                                          Map<String, List<QueryMemberInfosByDeptIds.Member>> deptMemberMap,
                                                                          List<IObjectData> deptValues,
                                                                          List<IObjectData> memberValues,
                                                                          Boolean containValue,
                                                                          String month, String checkCycle) {
        List<GetOrganizationModel.OrganizationEntity> organizationEntities = Lists.newArrayList();
        //遍历deptValues
        Map<String, IObjectData> checkIdAndDeptValueMap = deptValues.stream().collect(Collectors.toMap(obj -> obj.get(GoalValueConstants.CHECK_OBJECT_ID, String.class), obj -> obj, (oldValue, newValue) -> newValue));
        Map<String, IObjectData> checkIdAndMemberValueMap = memberValues.stream().collect(Collectors.toMap(obj -> obj.get(GoalValueConstants.CHECK_OBJECT_ID, String.class), obj -> obj, (oldValue, newValue) -> newValue));
        if (CollectionUtils.empty(allDeptInfos)) {
            return organizationEntities;
        }
        for (QueryDeptByName.DeptInfo deptInfo : allDeptInfos) {
            Optional<IObjectData> valueData = Optional.ofNullable(checkIdAndDeptValueMap.get(deptInfo.getId()));
            if (!valueData.isPresent()) {
                continue;
            }
            GetOrganizationModel.OrganizationEntity entity = new GetOrganizationModel.OrganizationEntity();
            IObjectData deptValue = valueData.get();
            entity.setId(deptValue == null ? "" : deptValue.getId());
            entity.setCheckObjectId(deptInfo.getId());
            entity.setGoalType(GoalEnum.GoalTypeValue.CIRCLE.getValue());
            if (containValue) {
                entity.setGoalValue(deptValue == null ? "" : getMonthlyValue(deptValue, month, checkCycle));
            }
            entity.setParentId(String.valueOf(deptInfo.getParentId()));
            entity.setCheckObjectName(deptInfo.getName());
//            entity.setParentLeaf(parentLeaf);

            organizationEntities.add(entity);
        }

        if (CollectionUtils.empty(deptMemberMap)) {
            return organizationEntities;
        }
        deptMemberMap.forEach((key, value) -> {
            value.forEach(member -> {
                Optional<IObjectData> memberData = Optional.ofNullable(checkIdAndMemberValueMap.get(member.getId()));
                if (memberData.isPresent()) {
                    GetOrganizationModel.OrganizationEntity entity = new GetOrganizationModel.OrganizationEntity();
                    IObjectData memberValue = memberData.get();
                    entity.setId(memberValue == null ? "" : memberValue.getId());
                    entity.setCheckObjectId(member.getId());
                    entity.setGoalType(GoalEnum.GoalTypeValue.EMPLOYEE.getValue());
                    if (containValue) {
                        entity.setGoalValue(memberValue == null ? "" : getMonthlyValue(memberValue, month, checkCycle));
                    }
                    entity.setParentId(key);
                    entity.setCheckObjectName(member.getNickname());
                    //                entity.setParentLeaf(Boolean.FALSE);
                    organizationEntities.add(entity);
                }
            });
        });

        return organizationEntities;
    }

    private List<GetOrganizationModel.OrganizationEntity> handleDeptValue(List<QueryDeptByName.DeptInfo> allDeptInfos,
                                                                          String deptId,
                                                                          Map<String, List<QueryMemberInfosByDeptIds.Member>> deptMemberMap,
                                                                          List<IObjectData> deptValues,
                                                                          List<IObjectData> memberValues,
                                                                          Boolean containValue,
                                                                          String month,
                                                                          Boolean parentLeaf) {
        List<GetOrganizationModel.OrganizationEntity> organizationEntities = Lists.newArrayList();

        Optional<IObjectData> deptGoalValue = deptValues.stream()
                .filter(deptValue -> String.valueOf(deptValue.get(GoalValueConstants.CHECK_OBJECT_ID)).equals(deptId))
                .findFirst();
        if (!deptGoalValue.isPresent()) {
            return organizationEntities;
        }


        GetOrganizationModel.OrganizationEntity organizationEntity = new GetOrganizationModel.OrganizationEntity();
        IObjectData deptValue = deptGoalValue.get();
        organizationEntity.setId(deptValue == null ? "" : deptValue.getId());
        organizationEntity.setCheckObjectId(deptId);
        organizationEntity.setGoalType(GoalEnum.GoalTypeValue.CIRCLE.getValue());
        if (containValue) {
            organizationEntity.setGoalValue(deptValue == null ? "" : getMonthlyValue(deptValue, month, null));
        }
        List<QueryDeptByName.DeptInfo> deptInfos = allDeptInfos.stream()
                .filter(deptInfo -> deptInfo.getId().equals(deptId))
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(deptInfos)) {
            organizationEntity.setParentId(String.valueOf(deptInfos.get(0).getParentId()));
            organizationEntity.setCheckObjectName(deptInfos.get(0).getName());
        } else {
            organizationEntity.setParentId("");
        }
        organizationEntity.setParentLeaf(parentLeaf);

        organizationEntities.add(organizationEntity);

        List<QueryMemberInfosByDeptIds.Member> members = deptMemberMap.get(deptId);
        if (CollectionUtils.notEmpty(members)) {
            for (QueryMemberInfosByDeptIds.Member member : members) {
                Optional<IObjectData> memberGoalVlue = memberValues.stream()
                        .filter(memberValue ->
                                String.valueOf(memberValue.get(GoalValueConstants.CHECK_OBJECT_ID)).equals(member.getId()))
                        .findFirst();
                if (!memberGoalVlue.isPresent()) {
                    continue;
                }
                GetOrganizationModel.OrganizationEntity entity = new GetOrganizationModel.OrganizationEntity();
                IObjectData memberValue = memberGoalVlue.get();
                entity.setId(memberValue == null ? "" : memberValue.getId());
                entity.setCheckObjectId(member.getId());
                entity.setGoalType(GoalEnum.GoalTypeValue.EMPLOYEE.getValue());
                if (containValue) {
                    entity.setGoalValue(memberValue == null ? "" : getMonthlyValue(memberValue, month, null));
                }
                entity.setParentId(deptId);
                entity.setCheckObjectName(member.getNickname());
                entity.setParentLeaf(Boolean.FALSE);
                organizationEntities.add(entity);
            }
        }

        List<QueryDeptByName.DeptInfo> subDepts = allDeptInfos.stream()
                .filter(deptInfo ->
                        deptInfo.getAncestors().size() > 0
                                && deptInfo.getAncestors().indexOf(deptId) == deptInfo.getAncestors().size() - 1)
                .collect(Collectors.toList());
        //子部门排序
//        subDepts.sort((QueryDeptByName.DeptInfo d1, QueryDeptByName.DeptInfo d2) -> d1.getOrder() - d2.getOrder());
        for (QueryDeptByName.DeptInfo subDeptInfo : subDepts) {
            organizationEntities.addAll(handleDeptValue(allDeptInfos,
                    subDeptInfo.getId(),
                    deptMemberMap,
                    deptValues,
                    memberValues,
                    containValue,
                    month,
                    Boolean.FALSE));
        }

        return organizationEntities;
    }

    private String getMonthlyValue(IObjectData objectData, String month, String checkCycle) {
        if (Strings.isNullOrEmpty(month) || Strings.isNullOrEmpty(checkCycle)) {
            Object oValue = objectData.get(GoalValueConstants.ANNUAL_VALUE);
            return oValue == null ? "" : oValue.toString();
        }

        Map<Integer, String> monthMapping = goalRuleCommonService.getMonthData(checkCycle);

        Object monthValue = objectData.get(monthMapping.get(Integer.valueOf(month)));
        return monthValue == null ? "" : monthValue.toString();
    }

    private List<String> getHighestDeptIds(List<QueryDeptByName.DeptInfo> deptInfos) {
        if (CollectionUtils.empty(deptInfos)) {
            return Lists.newArrayList();
        }

        List<String> highestDeptIds = Lists.newArrayList();
        if (deptInfos.stream().anyMatch(deptInfo -> deptInfo.getId().equals("999999"))) {
            highestDeptIds.add("999999");
            return highestDeptIds;
        }

        deptInfos.sort((QueryDeptByName.DeptInfo d1, QueryDeptByName.DeptInfo d2) -> d1.getOrder() - d2.getOrder());
        List<String> allDeptIds = deptInfos.stream().map(dept -> dept.getId()).collect(Collectors.toList());
        for (QueryDeptByName.DeptInfo deptInfo : deptInfos) {
            if (deptInfo.getAncestors().stream().anyMatch(d -> allDeptIds.contains(d))) {
                allDeptIds.remove(deptInfo.getId());
            }
        }

        return allDeptIds;
    }

    private List<GetRuleFilterModel.RuleFilterEntity> handleGoalRules(List<IObjectData> objectDataList) {
        List<GetRuleFilterModel.RuleFilterEntity> ruleFilterEntities = Lists.newArrayList();

        if (CollectionUtils.notEmpty(objectDataList)) {
            List<String> ruleIds = objectDataList.stream()
                    .map(rule -> rule.get(GoalValueConstants.GOAL_RULE_ID).toString())
                    .distinct()
                    .collect(Collectors.toList());

            for (String id : ruleIds) {
                GetRuleFilterModel.RuleFilterEntity entity = new GetRuleFilterModel.RuleFilterEntity();
                entity.setValue(id);
                List<GetRuleFilterModel.RuleFilterEntity> children = Lists.newArrayList();
                List<IObjectData> details = objectDataList.stream()
                        .filter(rule -> rule.get(GoalValueConstants.GOAL_RULE_ID).toString().equals(id))
                        .collect(Collectors.toList());
                for (int i = 0; i < details.size(); i++) {
                    String themeApiName = details.get(i).get(GoalRuleObj.THEME_API_NAME, String.class);
                    themeApiName = Strings.isNullOrEmpty(themeApiName) ? GoalEnum.ThemeTypeValue.PERSON.getValue() : themeApiName;
                    String checkLevelType = details.get(i).get(GoalRuleObj.CHECK_LEVEL_TYPE, String.class);
                    String checkLevelApiName = details.get(i).get(GoalRuleObj.CHECK_LEVEL_FIELD_API_NAME, String.class);
                    String checkCycle = Objects.toString(details.get(i).get(GoalRuleObj.CHECK_CYCLE, String.class), "month");
                    String timeZone = Objects.toString(details.get(i).get(GoalRuleObj.GOAL_RULE_TIME_ZONE, String.class), "Asia/Shanghai");

                    String tag = null;
                    if (!Strings.isNullOrEmpty(themeApiName) && !Strings.isNullOrEmpty(checkLevelType) && !Strings.isNullOrEmpty(checkLevelApiName)) {
                        //tag固定使用month生成，如果使用checkCycle生成，同一类的规则不同考核周期tag会不一致，导致uitype返回目标规则数据时只会返回周期月的规则
                        tag = Joiner.on('$').join(themeApiName, checkLevelType, checkLevelApiName, "month");
                    }
                    if (i == 0) {
                        entity.setLabel(details.get(i).get("goal_rule_name").toString());
                        entity.setFiscalYear(details.get(i).get(GoalValueConstants.FISCAL_YEAR).toString());
                        entity.setThemeApiName(themeApiName);
                        entity.setCheckLevelType(checkLevelType);
                        entity.setTag(tag);
                        entity.setCheckCycle(checkCycle);
                        entity.setTimeZone(timeZone);
                    }

                    Object oDetailId = details.get(i).get(GoalValueConstants.GOAL_RULE_DETAIL_ID);
                    if (oDetailId != null && !Strings.isNullOrEmpty(oDetailId.toString())) {
                        GetRuleFilterModel.RuleFilterEntity child = new GetRuleFilterModel.RuleFilterEntity();
                        child.setValue(oDetailId.toString());
                        child.setLabel(details.get(i).get("goal_rule_detail_name").toString());
                        child.setThemeApiName(themeApiName);
                        child.setCheckLevelType(checkLevelType);
                        child.setTag(tag);
                        children.add(child);
                    }
                }
                entity.setChildren(children);
                ruleFilterEntities.add(entity);
            }
        }

        return ruleFilterEntities;
    }

    /**
     * 删除目标值
     *
     * @param context
     * @param arg
     * @return
     */
    @ServiceMethod("deleteGoalValue")
    public DeleteGoalValue.Result deleteGoalValue(ServiceContext context, DeleteGoalValue.Arg arg) {
        goalValueCommonService.deleteGoalValue(context.getUser(), arg.getId());

        return DeleteGoalValue.Result.builder().success(true).build();
    }

    @ServiceMethod("findLevelCodeList")
    public FindLevelCode.Result findLevelCodeList(ServiceContext context, FindLevelCode.Arg arg) {

        IObjectData goalRuleData = goalRuleCommonService.findGoalRule(context.getUser(), arg.getGoalRuleId());
        String checkCycle = goalRuleData.get(GoalRuleObj.CHECK_CYCLE, String.class);
        List<IObjectData> objectDataList = goalValueCommonService.findGoalValues(context.getUser(),
                arg.getGoalRuleId(),
                arg.getGoalRuleDetailId(),
                arg.getFiscalYear(),
                null,
                null);
        return FindLevelCode.Result
                .builder()
                .dataList(buildLevelData(objectDataList, false, null, checkCycle))
                .build();
    }

    private List<FindLevelCode.LevelValue> buildLevelData(List<IObjectData> objectDataList,
                                                          boolean isNeedGoalValue,
                                                          String month,
                                                          String checkCycle) {
        if (CollectionUtils.empty(objectDataList)) {
            return Lists.newArrayList();
        }
        List<FindLevelCode.LevelValue> datas = Lists.newArrayList();
        for (IObjectData data : objectDataList) {
            if(data != null) {
                FindLevelCode.LevelValue levelValue = new FindLevelCode.LevelValue();
                levelValue.setCheckObjectId(data.get(GoalValueConstants.CHECK_OBJECT_ID, String.class));
                levelValue.setId(data.get(GoalValueConstants.ID_, String.class));
                if (isNeedGoalValue) {
                    levelValue.setGoalValue(getMonthlyValue(data, month, checkCycle));
                }
                datas.add(levelValue);
            }
        }
        return datas;
    }

    @ServiceMethod("findLevelValueList")
    public FindLevelCode.Result findLevelValueList(ServiceContext context, GetOrganizationModel.Arg arg) {
        IObjectData goalRuleData = validateRule(context.getUser(),
                arg.getGoalRuleId(),
                arg.getFiscalYear(),
                arg.getContainGoalValue());
        validateDetailRule(context.getUser(), arg.getGoalRuleId(), arg.getGoalRuleDetailId());
        String checkCycle = goalRuleData.get(GoalRuleObj.CHECK_CYCLE, String.class);
        List<String> checkObjIds = new ArrayList<>();
        List<IObjectData> goalValues = new ArrayList<>();
        String checkLevelFieldApiName = goalRuleData.get(GoalRuleObj.CHECK_LEVEL_FIELD_API_NAME, String.class);

        goalValues = goalValueCommonService.findGoalValues(context.getUser(), arg.getGoalRuleId(), arg.getGoalRuleDetailId(), arg.getFiscalYear(), arg.getGoalType(), checkObjIds);
        Boolean lock = Boolean.FALSE;
        Boolean unlock = Boolean.FALSE;
        if (serviceFacade.isAdmin(context.getUser())) {
            boolean isLock = goalValueCommonService.isLock(context.getUser(),
                    arg.getGoalRuleId(),
                    arg.getGoalRuleDetailId(),
                    arg.getFiscalYear(),
                    arg.getGoalType(),
                    "");
            lock = !isLock;
            unlock = isLock;
        }
        Boolean allowPersonalModify = String.valueOf(goalRuleData.get(GoalRuleObj.ALLOW_PERSONAL_MODIFY_GOAL)).equals("1");
        return FindLevelCode.Result
                .builder()
                .dataList(buildLevelData(goalValues, true, arg.getMonth(), checkCycle))
                .lockable(lock)
                .unlockable(unlock)
                .allowPersonalModify(allowPersonalModify)
                .build();
    }


    @ServiceMethod("checkGoalValueExist")
    public CheckGoalValueExist.Result checkGoalValueExist(ServiceContext context, CheckGoalValueExist.Arg arg) {
        if (CollectionUtils.empty(arg.getObjectDatas())) {
            return CheckGoalValueExist.Result
                    .builder()
                    .dataList(Lists.newArrayList())
                    .build();
        }
        List<IObjectData> objectDatas = (List) ((CheckGoalValueExist.Arg) arg).objectDatas.stream().map((x) -> {
            return x.toObjectData();
        }).collect(Collectors.toList());
        String goalRuleId = objectDatas.get(0).get(GoalValueConstants.GOAL_RULE_ID, String.class);
        String goalRuleDetailId = objectDatas.get(0).get(GoalValueConstants.GOAL_RULE_DETAIL_ID, String.class);
        String fiscalYear = objectDatas.get(0).get(GoalValueConstants.FISCAL_YEAR, String.class);
        String gaolType = objectDatas.get(0).get(GoalValueConstants.GOAL_TYPE, String.class);
        List<String> checkObjIds = objectDatas.stream().map(o -> o.get(GoalValueConstants.CHECK_OBJECT_ID, String.class)).collect(Collectors.toList());

        List<IObjectData> dbData = goalValueCommonService.findGoalValues(context.getUser(), goalRuleId, goalRuleDetailId, fiscalYear, gaolType, checkObjIds);
        List<String> existCheckObjIdList = CollectionUtils.empty(dbData) ? Lists.newArrayList() : dbData.stream().map(o -> o.get(GoalValueConstants.CHECK_OBJECT_ID, String.class)).collect(Collectors.toList());

        return CheckGoalValueExist.Result
                .builder()
                .dataList(existCheckObjIdList)
                .build();
    }


}
