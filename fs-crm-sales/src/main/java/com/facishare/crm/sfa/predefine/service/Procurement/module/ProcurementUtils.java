package com.facishare.crm.sfa.predefine.service.Procurement.module;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.action.model.ProcurementDetail;
import com.facishare.crm.sfa.predefine.service.model.procurement.BiddingSubscriptionRulesConstants;
import com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementConstants;
import com.facishare.crm.sfa.utilities.common.UseRangeFieldDataRender;
import com.facishare.crm.sfa.utilities.util.DateUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.HtmlUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fxiaoke.bi.industry.entity.BidDetailInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_BIDDING_KEYWORDS_FORMAT_ERR;


@Slf4j
public class ProcurementUtils {

    /**
     * 千里马业务类型
     */
    public static final String QLM = "record_qlm__c";
    private static final JsonMapper jsonMapper = new JsonMapper();

    public static String buildContact(String unitName, String contactName, String tel) {
        StringBuilder s = new StringBuilder();
        if (StringUtils.isEmpty(contactName) && StringUtils.isEmpty(tel)) {
            return s.toString();
        }
        if (StringUtils.isNotEmpty(unitName)) {
            s.append(unitName);
            s.append("：");
        }
        if (StringUtils.isNotEmpty(contactName)) {
            s.append(contactName);
        }
        if (StringUtils.isNotEmpty(contactName) && StringUtils.isNotEmpty(tel)) {
            s.append("，");
        }
        if (StringUtils.isNotEmpty(tel)) {
            s.append(tel);
        }
        s.append("；");
        return s.toString();
    }

    /**
     * 知了搜索数据 转 公告查询对象
     */
    public static ObjectDataDocument toProcurementSearch(BidDetailInfo result, String dataFrom) {
        ObjectDataDocument document = new ObjectDataDocument();
        if (result == null) {
            return document;
        }
        document.put("title", result.getTitle());
        document.put("name", result.getTitle());
        document.put("bid_type", result.getBidType());
        document.put("bid_id", result.getBidId());
        document.put("bid_sub_type", result.getBidSubType());
        document.put("publish_time", DateUtils.getTime(result.getPublishTime(), "yyyy-MM-dd"));
        document.put("area", BidDetailInfoExt.getArea(result));
        document.put("project_name", result.getProjectName());
        document.put("project_number", result.getProjectNumber());
        document.put("biding_end_time", result.getBidingEndTime());
        document.put("tender_end_time", result.getTenderEndTime());
        document.put("biding_days", DateUtils.getDays(result.getBidingEndTime()));
        document.put("tender_days", DateUtils.getDays(result.getTenderEndTime()));
        document.put("caller_budget", result.getBudget());
        document.put("winner_amount", result.getWinnerAmount());
        document.put("winner_each_amount", result.getWinnerAmount());

        // 主体信息
        document.put("caller_names", result.getCallerName());
        document.put("caller_contact", BidDetailInfoExt.getContact(result.getCallerContacts()));
        BidDetailInfoExt.flatWinners(document, result.getWinners(), "winner_names", "winner_contact");
        BidDetailInfoExt.flatWinners(document, result.getAgents(), "agent_names", "agent_contact");
        BidDetailInfoExt.flatWinners(document, result.getTenders(), "winner_candidate", "tender_candidate");
        document.put("amount", BidDetailInfoExt.flatWinnerAmount(result.getWinners()));
        document.put("product_names", BidDetailInfoExt.splitNames(result.getProducts()));
        document.put("caller_credit_code", result.getCallerCreditCode());
        if (StringUtils.isNotBlank(result.getContent())) {
            document.put("info_content", result.getContent());
            document.put("info_content__o", HtmlUtils.parseText(result.getContent()));
        } else {
            document.put("info_content", "");
            document.put("info_content__o", "");
        }
        document.put("caller_method", result.getBidMethod());
        document.put("jury", BidDetailInfoExt.getJury(result.getJuries()));
        document.put("caller_type", BidDetailInfoExt.getCallerTypes(result.getCallerNameType()));
        document.put("source_url", result.getBidUrl());
        document.put("object_describe_api_name", SFAPreDefineObject.ProcurementSearch.getApiName());
        document.put("is_show_related_button", false);
        if (result.getBidType() == 1) {
            document.put("caller_bid_status", BidDetailInfoExt.getCallerStatus(result.getBidingEndTime(), result.getTenderEndTime()));
        }
        document.put("data_from", dataFrom);
        document.put("record_type", ProcurementUtils.QLM);
        return document;
    }

    /**
     * 千里马搜索数据 转 公告查询对象
     */
    public static ObjectDataDocument toProcurementSearch(JSONObject data, String sourceUrl) {
        ObjectDataDocument document = new ObjectDataDocument();
        document.put("bid_id", data.getString("dataId"));
        document.put("title", data.getString("title"));
        document.put("name", data.getString("title"));
        //项目编号
        document.put("project_number", data.getString("projectNo"));
        //字典项
        document.put("caller_type", data.getString("zhaobiaoUnitFirstNature"));
        if (data.containsKey("zhaoBiaoType")) {
            String type = data.getString("zhaoBiaoType");
            String callerMethod = null;
            if ("0".equals(type)) {
                callerMethod = "公开招标"; // ignoreI18n
            } else if ("1".equals(type)) {
                callerMethod = "邀请招标"; // ignoreI18n
            } else if ("2".equals(type)) {
                callerMethod = "竞争性谈判或竞争性磋商"; // ignoreI18n
            } else if ("3".equals(type)) {
                callerMethod = "单一来源采购"; // ignoreI18n
            } else if ("4".equals(type)) {
                callerMethod = "询价"; // ignoreI18n
            } else if ("5".equals(type)) {
                callerMethod = "国务院政府采购监督管理部门认定的其他采购方式"; // ignoreI18n
            } else if ("6".equals(type)) {
                callerMethod = "电子卖场"; // ignoreI18n
            }
            document.put("caller_method", callerMethod);
        }
        document.put("bid_type", ProcurementConstants.BID_TYPE_MAP.get(data.getString("messageType")));
        document.put("bid_sub_type", BiddingSubscriptionRulesConstants.BID_SUB_TYPE_MAP.get(data.getString("noticeSegmentName")));
        document.put("call_sub_type", data.getString("zhaobiaoUnitSecondNature"));
        //招标
        document.put("caller_names", data.getString("zhaoBiaoUnit"));
        document.put("caller_budget", data.getString("budgetAmountNumber"));
        document.put("caller_contact", ProcurementUtils.buildContact(null, data.getString("zhaoRelationName"), data.getString("zhaoRelationWay")));
        //中标
        if (data.containsKey("zhongBiaoAmount")) {
            if (data.getString("zhongBiaoAmount") != null && data.getString("zhongBiaoAmount").contains("%")) {
                document.put("winner_amount_pct", data.getString("zhongBiaoAmount"));
            } else {
                document.put("amount", data.getString("zhongBiaoAmount"));
                document.put("winner_amount", data.getString("zhongBiaoAmount"));
            }
        }
        document.put("winner_names", data.getString("zhongBiaoUnit"));
        document.put("winner_contact", ProcurementUtils.buildContact(data.getString("zhongBiaoUnit"), data.getString("zhongRelationName"), data.getString("zhongRelationWay")));
        //代理
        document.put("agent_names", data.getString("agent"));
        //产品
        if (data.containsKey("bdKeywords")) {
            document.put("product_names", BidDetailInfoExt.splitNames(data.getJSONArray("bdKeywords").toJavaList(String.class)));
        }
        //地区
        document.put("area", data.getString("area"));
        //开标时间
        document.put("open_biding_time", data.getLong("openBidingTime"));
        //发布时间
        document.put("publish_time", data.getLong("publishTime"));
        //标书截至日期
        if (data.containsKey("registrationEndTime")){
            document.put("tender_end_date_time", data.getLong("registrationEndTime"));
            document.put("tender_end_time", DateUtils.getTimeByTimeStamp(data.getLong("registrationEndTime")));
            document.put("tender_days", ProcurementUtils.getRemainder(data.getLong("registrationEndTime")));
        }
        //投标截止日期
        if (data.containsKey("tenderEndTime")) {
            document.put("biding_end_date_time", data.getLong("tenderEndTime"));
            document.put("biding_end_time", DateUtils.getTimeByTimeStamp(data.getLong("tenderEndTime")));
            document.put("biding_days", ProcurementUtils.getRemainder(data.getLong("tenderEndTime")));
        }
        // 招标状态
        if ("1".equals(document.get("bid_type"))) {
            document.put("caller_bid_status", BidDetailInfoExt.getCallerStatus(
                    document.get("biding_end_time") == null ? "" : document.get("biding_end_time").toString(),
                    document.get("tender_end_time") == null ? "" : document.get("tender_end_time").toString()
            ));
        }
        //公告地址
        document.put("source_url", sourceUrl);
        //公告详情
        document.put("info_content", data.getString("content"));
        document.put("object_describe_api_name", SFAPreDefineObject.ProcurementSearch.getApiName());
        document.put("is_show_related_button", false);
        return document;
    }

    public static List<ProcurementDetail.UnitEntity> change(List<com.fxiaoke.bi.industry.entity.UnitEntity> originUnitList) {
        List<ProcurementDetail.UnitEntity> res = new ArrayList<>();
        if (CollectionUtils.isEmpty(originUnitList)) {
            return res;
        }
        for (com.fxiaoke.bi.industry.entity.UnitEntity originUnit : originUnitList) {
            ProcurementDetail.UnitEntity targetUnit = new ProcurementDetail.UnitEntity();
            targetUnit.setName(originUnit.getName());
            targetUnit.setAmount(originUnit.getAmount());
            targetUnit.setCreditCode(originUnit.getCreditCode());
            targetUnit.setContactPersons(new ArrayList<>());
            if (CollectionUtils.isNotEmpty(originUnit.getContactPersons())) {
                for (com.fxiaoke.bi.industry.entity.ContactEntity originContact : originUnit.getContactPersons()) {
                    ProcurementDetail.ContactEntity targetContact = new ProcurementDetail.ContactEntity();
                    targetContact.setName(originContact.getName());
                    targetContact.setPhone(originContact.getPhone());
                    targetUnit.getContactPersons().add(targetContact);
                }
            }
            res.add(targetUnit);
        }
        return res;
    }

    /**
     * 知了数据转 公告
     */
    public static ProcurementDetail toProcurementDetail(BidDetailInfo bidDetailInfo, String dataName) {
        ProcurementDetail detailInfo = new ProcurementDetail();
        detailInfo.setBidId(bidDetailInfo.getBidId());
        detailInfo.setTitle(bidDetailInfo.getTitle());
        detailInfo.setContent(bidDetailInfo.getContent());
        detailInfo.setSource(bidDetailInfo.getSource());
        detailInfo.setPartnerUrl(bidDetailInfo.getPartnerUrl());
        detailInfo.setBidUrl(bidDetailInfo.getBidUrl());
        if (bidDetailInfo.getBidType() != null) {
            detailInfo.setBidType(bidDetailInfo.getBidType().toString());
        }
        if (bidDetailInfo.getBidSubType() != null) {
            detailInfo.setBidSubType(bidDetailInfo.getBidSubType().toString());
        }
        detailInfo.setBidMethod(bidDetailInfo.getBidMethod());
        detailInfo.setProducts(bidDetailInfo.getProducts());
        //招标单位
        ProcurementDetail.UnitEntity caller = new ProcurementDetail.UnitEntity();
        caller.setName(bidDetailInfo.getCallerName());
        caller.setAmount(bidDetailInfo.getBudget());
        caller.setCreditCode(bidDetailInfo.getCallerCreditCode());
        caller.setContactPersons(new ArrayList<>());
        if (CollectionUtils.isNotEmpty(bidDetailInfo.getCallerContacts())) {
            for (com.fxiaoke.bi.industry.entity.ContactEntity originContact : bidDetailInfo.getCallerContacts()) {
                ProcurementDetail.ContactEntity targetContact = new ProcurementDetail.ContactEntity();
                targetContact.setName(originContact.getName());
                targetContact.setPhone(originContact.getPhone());
                caller.getContactPersons().add(targetContact);
            }
        }
        detailInfo.setCaller(caller);
        //中标单位
        detailInfo.setWinners(change(bidDetailInfo.getWinners()));
        //代理单位
        detailInfo.setAgents(change(bidDetailInfo.getAgents()));
        //中标候选人
        detailInfo.setTenders(change(bidDetailInfo.getTenders()));

        detailInfo.setWinnerAmount(bidDetailInfo.getWinnerAmount());
        detailInfo.setPublishTime(DateUtils.getTime(bidDetailInfo.getPublishTime(), "yyyy-MM-dd"));
        detailInfo.setCity(bidDetailInfo.getCity());
        detailInfo.setProvince(bidDetailInfo.getProvince());
        detailInfo.setBidingEndTime(DateUtils.getTime(bidDetailInfo.getBidingEndTime(), "yyyy-MM-dd HH:mm:ss"));
        detailInfo.setTenderEndTime(DateUtils.getTime(bidDetailInfo.getTenderEndTime(), "yyyy-MM-dd HH:mm:ss"));
        detailInfo.setProjectName(bidDetailInfo.getProjectName());
        detailInfo.setProjectNumber(bidDetailInfo.getProjectNumber());
        detailInfo.setCallerNameType(bidDetailInfo.getCallerNameType());
        //专家
        detailInfo.setJuries(new ArrayList<>());
        if (CollectionUtils.isNotEmpty(bidDetailInfo.getJuries())) {
            for (com.fxiaoke.bi.industry.entity.JuryEntity originJury : bidDetailInfo.getJuries()) {
                ProcurementDetail.JuryEntity targetJury = new ProcurementDetail.JuryEntity();
                targetJury.setJuryName(originJury.getJuryName());
                targetJury.setJuryPosition(originJury.getJuryPosition());
                detailInfo.getJuries().add(targetJury);
            }
        }
        // 增加订阅来源的名称
        detailInfo.put(ProcurementConstants.ProcurementInfo.DATA_SOURCE, ProcurementConstants.ProcurementInfo.DATA_SOURCE_ZL);
        detailInfo.put("subscription_name", dataName);
        return detailInfo;
    }

    /**
     * 千里马搜索数 据转 公告
     */
    public static ProcurementDetail toProcurementDetail(JSONObject data) {
        ProcurementDetail detailInfo = new ProcurementDetail();
        detailInfo.setTitle(data.getString("title"));
        String callerNameTypeStr = data.getString("zhaobiaoUnitFirstNature");
        if (!StringUtils.isEmpty(callerNameTypeStr)) {
            detailInfo.setCallerNameType(Arrays.stream(callerNameTypeStr.split(",")).map(ProcurementConstants.CALLER_TYPE_MAP::get).collect(Collectors.toList()));
        }
        detailInfo.setBidType(ProcurementConstants.BID_TYPE_MAP.get(data.getString("messageType")));
        detailInfo.setBidSubType(ProcurementConstants.BID_SUB_TYPE_MAP.get(data.getString("noticeSegmentName")));
        String[] area = data.getString("area").split("-");
        if (area.length > 0) {
            detailInfo.setProvince(area[0]);
        }
        if (area.length > 1) {
            detailInfo.setCity(area[1]);
        }
        detailInfo.setProjectNumber(data.getString("projectNo"));
        detailInfo.setPublishTime(data.getLong("publishTime"));
        detailInfo.setBidingEndTime(data.getLong("tenderEndTime"));
        detailInfo.setTenderEndTime(data.getLong("registrationEndTime"));
        detailInfo.setBidId(data.getString("dataId"));
        //以下是查找关联类型
        JSONArray products = data.getJSONArray("bdKeywords");
        if (products != null && !products.isEmpty()) {
            detailInfo.setProducts(products.toJavaList(String.class));
        }
        //招标单位
        ProcurementDetail.UnitEntity caller = ProcurementDetail.buildUnitEntity(
                data.getString("zhaoBiaoUnit"),
                data.getString("budgetAmountNumber"),
                data.getString("zhaoRelationName"),
                data.getString("zhaoRelationWay")
        );
        if (caller != null) {
            detailInfo.setCaller(caller);
        }
        //代理单位
        ProcurementDetail.UnitEntity agent = ProcurementDetail.buildUnitEntity(data.getString("agent"), null, null, null);
        if (agent != null) {
            detailInfo.setAgents(Lists.newArrayList(agent));
        }
        //中标候选人
        if (Objects.equals(detailInfo.getBidSubType(), "21")) {//候选人公示
            ProcurementDetail.UnitEntity tender = ProcurementDetail.buildUnitEntity(
                    data.getString("zhongBiaoUnit"),
                    data.getString("zhongBiaoAmount"),
                    data.getString("zhongRelationName"),
                    data.getString("zhongRelationWay")
            );
            if (tender != null) {
                detailInfo.setTenders(Lists.newArrayList(tender));
            }
        } else {
            //中标单位
            ProcurementDetail.UnitEntity winner = ProcurementDetail.buildUnitEntity(
                    data.getString("zhongBiaoUnit"),
                    data.getString("zhongBiaoAmount"),
                    data.getString("zhongRelationName"),
                    data.getString("zhongRelationWay")
            );
            if (winner != null) {
                detailInfo.setWinners(Lists.newArrayList(winner));
                detailInfo.setWinnerAmount(winner.getAmount());
                detailInfo.setWinnerAmountPct(winner.getAmountPct());
            }
        }
        detailInfo.put(ProcurementConstants.ProcurementInfo.DATA_SOURCE, ProcurementConstants.ProcurementInfo.DATA_SOURCE_QLM);
        //开标时间
        String openBidingTime = data.getString("openBidingTime");
        try {
            if (!StringUtils.isEmpty(openBidingTime)) {
                long obt = Long.parseLong(openBidingTime);
                if (obt > 0L && obt < 17040384000000L) {
                    detailInfo.put("open_biding_time", openBidingTime);
                }
            }
        } catch (Exception e) {
            log.warn("千里马标讯[openBidingTime][{}]解析异常", openBidingTime);
        }
        //招标方式
        detailInfo.put("caller_method", data.getString("zhaoBiaoType"));
        detailInfo.put("group_id", data.getString("groupId"));
        String callerSubTypeStr = data.getString("zhaobiaoUnitSecondNature");
        // 公告详情
        detailInfo.setContent(data.getString("content"));
        if (!StringUtils.isEmpty(callerSubTypeStr)) {
            detailInfo.put("call_sub_type", Arrays.stream(data.getString("zhaobiaoUnitSecondNature").split(",")).map(ProcurementConstants.CALLER_SUB_TYPE_MAP::get).collect(Collectors.toList()));
        }
        return detailInfo;
    }

    /**
     * 订阅器数据转千里马查询参数
     */
    public static JSONObject toQueryParam(IObjectData data) {
        JSONObject request = new JSONObject();
        request.put("subscribeName", data.getName());
        String keywords = String.valueOf(data.get(BiddingSubscriptionRulesConstants.KEYWORDS));
        if (!org.springframework.util.StringUtils.isEmpty(keywords)) {
            List<List<String>> rule = new ArrayList<>();
            // json格式 ["key1 key2","key3 key4",...]
            try {
                ArrayNode arrayNode = (ArrayNode)jsonMapper.readTree(keywords);
                for (JsonNode jsonNode : arrayNode){
                    rule.add(Arrays.stream(jsonNode.asText().split(" +")).collect(Collectors.toList()));
                }
            } catch (JsonProcessingException e) {
                log.warn("千里马关键词参数转换错误：{}",keywords,e);
                throw new ValidateException(I18N.text(SFA_BIDDING_KEYWORDS_FORMAT_ERR));
            }
            request.put("ruleList", rule);
        }
        try {
            ArrayNode secondCompanyTypeJsonNode = null;
            JsonNode conditionsJsonNode = jsonMapper.readTree(String.valueOf(data.get("conditions")));
            ArrayNode valueJsonNode = (ArrayNode)conditionsJsonNode.get("value");
            all: for (JsonNode node : valueJsonNode){
                ArrayNode filters = (ArrayNode)node.get("filters");
                for (JsonNode filtersNode : filters){
                    if (BiddingSubscriptionRulesConstants.ONE_LEVEL_TWO.equals(filtersNode.get("field_name").asText())){
                        secondCompanyTypeJsonNode = (ArrayNode)filtersNode.get("field_values");
                        break all;
                    }
                }
            }
            List<Integer> secondCompanyTypeList = new ArrayList<>();
            if (secondCompanyTypeJsonNode != null) {
                secondCompanyTypeJsonNode.forEach(x -> secondCompanyTypeList.add(x.get(1).asInt()));
            }
            if (!secondCompanyTypeList.isEmpty()) {
                request.put("secondCompanyType", secondCompanyTypeList);
            }
        } catch (Exception e) {
            log.warn("二级行业条件解析失败：", e);
        }

        String keywords_match_mode = data.get(BiddingSubscriptionRulesConstants.KEYWORDS_MATCH_MODE, String.class);
        request.put("searchMode", BiddingSubscriptionRulesConstants.KEYWORDS_MATCH_MODE_MAP.get(keywords_match_mode));

        String keywords_match_field = data.get(BiddingSubscriptionRulesConstants.KEYWORDS_MATCH_FIELD, String.class);
        request.put("searchRange", BiddingSubscriptionRulesConstants.KEYWORDS_MATCH_FIELD_MAP.get(keywords_match_field));

        String exclusion_words = String.valueOf(data.get(BiddingSubscriptionRulesConstants.EXCLUSION_WORDS));
        if (!org.springframework.util.StringUtils.isEmpty(exclusion_words)) {
            request.put("excludeWordList", Arrays.stream(exclusion_words.split(" +")).collect(Collectors.toList()));
        }
        request.put("amountType", data.get("amount_type", String.class));
        Long amountContentMin = data.get("amount_content_min", Long.class);
        if (amountContentMin != null) {
            request.put("amountContentMin", amountContentMin * 10000L);
        }
        Long amountContentMax = data.get("amount_content_max", Long.class);
        if (amountContentMin != null) {
            request.put("amountContentMax", amountContentMax * 10000L);
        }
        String conditions = data.get(BiddingSubscriptionRulesConstants.CONDITIONS, String.class);
        if (!org.springframework.util.StringUtils.isEmpty(conditions)) {
            parseConditions(JSON.parseObject(conditions), request);
        }
        return request;
    }

    public static void parseConditions(JSONObject json, JSONObject request) {
        //所属省 城市
        List<String> areaIdList = new LinkedList<>();
        //招标公司类型
        List<String> callerTypeList = new LinkedList<>();
        //二级公告类型
        List<String> bidSubTypeList = new LinkedList<>();
        //采购类型
        List<String> purchaseTypeList = new LinkedList<>();
        UseRangeFieldDataRender.UseRangeInfo useRangeInfo = JSON.parseObject(json.toString(), UseRangeFieldDataRender.UseRangeInfo.class);
        if (useRangeInfo.getValue().equalsIgnoreCase(UseRangeFieldDataRender.UseRangeType.ALL.toString())) {
            return;
        }
        List<UseRangeFieldDataRender.FilterGroup> filterGroups = JSON.parseArray(useRangeInfo.getValue(), UseRangeFieldDataRender.FilterGroup.class);
        if (org.springframework.util.CollectionUtils.isEmpty(filterGroups)) {
            return;
        }
        for (UseRangeFieldDataRender.FilterGroup filterGroup : filterGroups) {
            JSONArray andFilters = filterGroup.getFilters();
            for (int i = 0; i < andFilters.size(); i++) {
                JSONObject filter = andFilters.getJSONObject(i);
                List<String> values = filter.getJSONArray(IFilter.FIELD_VALUES).toJavaList(String.class);
                String fieldName = filter.getString(IFilter.FIELD_NAME);
                if (BiddingSubscriptionRulesConstants.CALLER_TYPE.equals(fieldName)) {
                    callerTypeList.addAll(values);
                } else if (BiddingSubscriptionRulesConstants.BID_SUB_TYPE.equals(fieldName)) {
                    bidSubTypeList.addAll(values);
                } else if (BiddingSubscriptionRulesConstants.PURCHASE_TYPE.equals(fieldName)) {
                    purchaseTypeList.addAll(values);
                } else if (BiddingSubscriptionRulesConstants.AREA_PROVINCE.equals(fieldName)) {
                    areaIdList.addAll(values);
                } else if (BiddingSubscriptionRulesConstants.AREA_CITY.equals(fieldName)) {
                    areaIdList.addAll(values);
                }
            }
        }
        request.put("companyType", callerTypeList.stream().flatMap(e -> BiddingSubscriptionRulesConstants.CALLER_TYPE_MAP.get(e).stream()).collect(Collectors.toList()));
        request.put("infoTypeList", bidSubTypeList.stream().flatMap(e -> BiddingSubscriptionRulesConstants.BID_SUB_TYPE_MAP.get(e).stream()).collect(Collectors.toList()));
        request.put("purchaseType", purchaseTypeList);
        request.put("areaIds", String.join(",", areaIdList));
    }

    public static long getRemainder(Long targetTime) {
        return Math.max(0, (targetTime - System.currentTimeMillis()) / 86400000L);
    }

    public static String encodeURL(String url) {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        try {
            URI uri = new URI(url);
            return uri.toASCIIString();
        } catch (Exception e) {
            log.warn("标讯url[{}]解析异常", url, e);
            return url;
        }
    }
}
