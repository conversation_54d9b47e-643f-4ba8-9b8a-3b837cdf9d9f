package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.sfa.predefine.service.cpq.constraint.AbstractProductConstraintTemplate;
import com.facishare.crm.sfa.predefine.service.cpq.constraint.BomProductConstraintList;
import com.facishare.crm.sfa.predefine.service.cpq.constraint.NormalProductConstraintList;
import com.facishare.crm.sfa.predefine.service.cpq.model.ProductConstraintModel;
import com.facishare.crm.sfa.predefine.service.task.ProductStatusChangeTaskService;
import com.facishare.crm.sfa.utilities.constant.CPQConstraintConstants;
import com.facishare.crm.sfa.utilities.util.ProductConstraintUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/8/24 3:03 下午
 * @illustration
 */
@ServiceModule("constraint")
@Component
@Slf4j
public class ProductConstraintCommonService {

    @Autowired
    private SpecialTableMapper specialTableMapper;
    @Autowired
    private ProductStatusChangeTaskService productStatusChangeTaskService;


    @ServiceMethod("check_product_constraint")
    public ProductConstraintModel.CheckProductConstraintResult checkProductConstraint(ServiceContext serviceContext, ProductConstraintModel.CheckProductConstraintArg arg) {
        if (CollectionUtils.empty(arg.getProductIds())) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BASIC_SETTING_BUSINESS_4519_2));
        }
        List<Map> productConstraintLinesList = specialTableMapper.setTenantId(serviceContext.getTenantId())
                .findBySql(String.format(CPQConstraintConstants.QUERY_PRODUCT_CONSTRAINT_LINES_BY_ACTIVE_STATUS_TRUE, serviceContext.getTenantId()));

        if(CollectionUtils.empty(productConstraintLinesList)){
            return ProductConstraintModel.CheckProductConstraintResult.builder().errorMessage(Maps.newHashMap()).build();
        }
        Map<String, List<Map>> productIdToConstraintLines = Maps.newHashMap();
        Set<String> needFindNameProduct = Sets.newHashSet();
        Set<String> exclusionsProductIds = Sets.newHashSet();
        for (String productId : arg.getProductIds()) {
            int depth = 1;
            if (CollectionUtils.notEmpty(productIdToConstraintLines.get(productId))) {
                continue;
            } else {
                recursionFindDownProductIds(depth, productId, productConstraintLinesList, needFindNameProduct, productIdToConstraintLines, exclusionsProductIds);
            }
            // 校验下互斥关系
            validateHC(productId, arg.getProductIds(), productConstraintLinesList, productIdToConstraintLines);
        }
        Map<String, String> productIdToName = ProductConstraintUtil.getProductName(serviceContext.getUser(), needFindNameProduct);
        if(CollectionUtils.empty(productIdToName)){
            return ProductConstraintModel.CheckProductConstraintResult.builder().errorMessage(Maps.newHashMap()).build();
        }
        Map<String, ProductConstraintModel.NormalConstraintLines> result = ProductConstraintUtil.getErrorMessage(arg.getProductIds(), productIdToConstraintLines, productIdToName);
        return ProductConstraintModel.CheckProductConstraintResult.builder().errorMessage(result).build();
    }

    private void validateHC(String productId, List<String> arg, List<Map> data, Map<String, List<Map>> productIdToConstraintLines){
        List<String> downProductIds = data.stream().filter(o -> Objects.equals(o.get("up_product_id").toString(),
                productId)).filter(o->Objects.equals("2", o.get("constraint_type").toString()))
                .map(o -> o.get("down_product_id").toString()).collect(Collectors.toList());

        List<Map> downProductIdList = data.stream()
                .filter(o -> Objects.equals(o.get("up_product_id").toString(), productId))
                .filter(o->Objects.equals("2", o.get("constraint_type").toString())).collect(Collectors.toList());

        downProductIds.forEach(id->{
            if(arg.contains(id)){
                // 表示存在了互斥关系
                List<Map> maps = productIdToConstraintLines.get(productId);
                if(maps != null){
                    maps.addAll(downProductIdList);
                    productIdToConstraintLines.put(productId, maps);
                }else{
                    productIdToConstraintLines.put(productId, Lists.newArrayList(downProductIdList));
                }
            }
        });
    }


    private void recursionFindDownProductIds(int depth, String productId, List<Map> data, Set<String> needFindNameProduct,
                                             Map<String, List<Map>> productIdToConstraintLines, Set<String> exclusionsProductIds) {
        // TODO: 2020/9/28 限制深度100
        if(depth > CPQConstraintConstants.MAX_DEPTH){
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SO_CHECK_CONSTRAINT_MAX_DEPTH));
        }
        depth++;
        needFindNameProduct.add(productId);
        exclusionsProductIds.add(productId);
        List<Map> nullData = Lists.newArrayList();

        List<String> downProductIds = data.stream()
                .filter(x -> Objects.equals(x.get(CPQConstraintConstants.UP_PRODUCT).toString(), productId))
                .map(o -> o.get(CPQConstraintConstants.DOWN_PRODUCT).toString()).collect(Collectors.toList());

        List<Map> downMaps = data.stream()
                .filter(x -> Objects.equals(x.get(CPQConstraintConstants.UP_PRODUCT).toString(), productId))
                .filter(x->Objects.equals("1", x.get("constraint_type").toString()))
                .collect(Collectors.toList());

        for (String prId : downProductIds) {
            if(exclusionsProductIds.contains(prId)){
                break;
            }
            recursionFindDownProductIds(depth, prId, data, needFindNameProduct, productIdToConstraintLines, exclusionsProductIds);
        }

        if (CollectionUtils.empty(downProductIds)) {
            return;
        }

        nullData.addAll(downMaps);
        productIdToConstraintLines.put(productId, Lists.newArrayList(nullData));
        nullData.clear();
    }


    /**
     * 查询普通产品的约束列表
     *
     * @param serviceContext
     * @param arg
     * @return 依赖列表，互斥列表
     */
    @ServiceMethod("get_normal_product_constraint")
    @Deprecated
    public ProductConstraintModel.ConstraintProductResult getNormalProductConstraintLines(ServiceContext serviceContext, ProductConstraintModel.ConstraintProductArg arg) {
        if (CollectionUtils.empty(arg.getDataInfo())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONTACT_PARAM_EXCEPTION));
        }

        AbstractProductConstraintTemplate abstractProductConstraintTemplate = new NormalProductConstraintList(arg, serviceContext.getUser());
        return abstractProductConstraintTemplate.generateResult();
    }


    /**
     * bom组合的约束列表
     *
     * @param serviceContext
     * @param arg
     * @return 依赖列表，互斥列表
     */
    @ServiceMethod("get_bom_product_constraint")
    @Deprecated
    public ProductConstraintModel.ConstraintProductResult getBomProductConstraint(ServiceContext serviceContext, ProductConstraintModel.ConstraintProductArg arg) {
        if (StringUtils.isEmpty(arg.getRootBomId()) || CollectionUtils.empty(arg.getBomIds())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONTACT_PARAM_EXCEPTION));
        }

        AbstractProductConstraintTemplate abstractProductConstraintTemplate = new BomProductConstraintList(arg, serviceContext.getUser());
        return abstractProductConstraintTemplate.generateResult();
    }

    @ServiceMethod("send_mq_message")
    @Deprecated
    public void getBomProductConstraint(ServiceContext serviceContext, ProductConstraintModel.ProductStatusChangeArg arg) {
        productStatusChangeTaskService.createOrUpdateTask(serviceContext.getTenantId(), arg.getProductIds(), arg.getBomIds());
    }


}
