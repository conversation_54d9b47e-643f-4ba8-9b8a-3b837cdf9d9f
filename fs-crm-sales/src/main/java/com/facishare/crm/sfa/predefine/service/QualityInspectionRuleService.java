package com.facishare.crm.sfa.predefine.service;

import static com.facishare.crm.sfa.predefine.service.model.quality.QIGetWechatConversionModel.action_layout_api;
import static com.facishare.crm.sfa.predefine.service.model.quality.QIGetWechatConversionModel.dirtyword_layout_api;
import static com.facishare.crm.sfa.predefine.service.model.quality.QIGetWechatConversionModel.feature_layout_api;
import static com.facishare.crm.sfa.predefine.service.model.quality.QIGetWechatConversionModel.record_action_layout;
import static com.facishare.crm.sfa.predefine.service.model.quality.QIGetWechatConversionModel.record_dirtyword_layout;
import static com.facishare.crm.sfa.predefine.service.model.quality.QIGetWechatConversionModel.record_feature_layout;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_PROCUREMENT_RULE_OP_LOG_MSG;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.quality.models.QualityInspectionConstants.CommonConstants;
import com.facishare.crm.sfa.lto.quality.models.QualityInspectionConstants.QualityInspectionConf;
import com.facishare.crm.sfa.lto.quality.models.QualityInspectionConstants.RuleMember;
import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.sfa.lto.utils.StringUtil;
import com.facishare.crm.sfa.predefine.service.model.quality.QIGetWechatConversionModel.QIUser;
import com.facishare.crm.sfa.utilities.util.LogUtil;
import com.facishare.crm.util.CommonBizOrgUtils;
import com.facishare.crm.util.Safes;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.UserInfoExt;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.Tenantable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023/2/15 17:11
 * @Version 1.0
 **/
@Slf4j
@Component
public class QualityInspectionRuleService {
    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    QualityInspectionDBService qualityInspectionDBService;
    public void addRuleMember(ActionContext context, QIUser monitors, QIUser msgUser, String ruleId) {
        List<Map<String, Object>> insertMemberData = Lists.newArrayList();
        if(monitors != null) {
            memberToDb(context, distinctList(monitors.getMember()), ruleId, 1, insertMemberData);
            memberToDb(context, distinctList(monitors.getGroup()), ruleId, 3, insertMemberData);
            memberToDb(context, distinctList(monitors.getRole()), ruleId, 5, insertMemberData);
        }
        if(msgUser != null) {
            memberToDb(context, distinctList(msgUser.getMember()), ruleId, 2, insertMemberData);
            memberToDb(context, distinctList(msgUser.getGroup()), ruleId, 4, insertMemberData);
            memberToDb(context, distinctList(msgUser.getRole()), ruleId, 6, insertMemberData);
        }
        qualityInspectionDBService.insertRuleMember(context, insertMemberData);
    }

    private List<String> distinctList(List<String> list){
        if(CollectionUtil.isNotEmpty(list)){
            return list.stream().distinct().collect(Collectors.toList());
        }
        return null;
    }

    private void mergeMembers(ControllerContext context, List<Object> ids, ObjectDataDocument dataDocument) {
        List<Map> mapList = qualityInspectionDBService.findMembers(context, ids);
        if(CollectionUtil.isNotEmpty(mapList)){
            List<String> monitorMember = Lists.newArrayList();
            List<String> monitorGroup = Lists.newArrayList();
            List<String> monitorRole = Lists.newArrayList();

            List<String> msgUserMember = Lists.newArrayList();
            List<String> msgUserGroup = Lists.newArrayList();
            List<String> msgUserRole = Lists.newArrayList();

            for(Map map : mapList){
                int member_type = (Integer) map.get("member_type");
                String member_id = StringUtil.convertString((String)map.get("member_id"), "");
                if(member_type == 1){
                    monitorMember.add(member_id);
                }else if(member_type == 2 ){
                    msgUserMember.add(member_id);
                }else if(member_type  == 3 ){
                    monitorGroup.add(member_id);
                }else if(member_type == 4 ){
                    msgUserGroup.add(member_id);
                }else if(member_type  == 5 ){
                    monitorRole.add(member_id);
                }else if(member_type == 6 ){
                    msgUserRole.add(member_id);
                }
            }
            dataDocument.put("monitors", QIUser.builder().member(monitorMember).group(monitorGroup).role(monitorRole).build());
            dataDocument.put("msg_user", QIUser.builder().member(msgUserMember).group(msgUserGroup).role(msgUserRole).build());

            //补齐monitor和msgUser的名称
            dataDocument.put("monitors__label", userGroupRoleName(context, monitorMember, monitorGroup, monitorRole));
            dataDocument.put("msg_user__label", userGroupRoleName(context, msgUserMember, msgUserGroup, msgUserRole));
        }
    }

    private String userGroupRoleName(ControllerContext controllerContext, List<String> members, List<String> groups, List<String> roles){
        log.warn("userGroupRoleName members:{}, groups:{}, roles:{}", members, groups, roles);
        List<String> names = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(members)) {
            List<UserInfoExt> userInfos = serviceFacade.getUserExtByIds(controllerContext.getTenantId(), controllerContext.getUser().getUpstreamOwnerIdOrUserId(), members);
            log.warn("List<UserInfoExt> userInfos:{}", userInfos);
            userInfos.forEach(u -> {
                names.add(u.getName());
            });
        }
        if (CollectionUtil.isNotEmpty(groups)) {
            if(groups.contains("999999")){
                //names.add("全公司");
                names.add(I18N.text("sfa.BasicSettingBusiness.2840.1"));
            }
            Map<String, String> tempUserGroupNameMap = CommonBizOrgUtils.getUserGroupName(controllerContext.getTenantId(), groups);
            log.warn("Map<String, String> tempUserGroupNameMap:{}", tempUserGroupNameMap);
            tempUserGroupNameMap.keySet().forEach(x -> {
                names.add(tempUserGroupNameMap.get(x));

            });
        }
        if (CollectionUtil.isNotEmpty(roles)) {
            Map<String, String> tempUserRoleNameMap = CommonBizOrgUtils.getUserRoleName(controllerContext.getTenantId(), Lists.newArrayList(roles));
            log.warn("Map<String, String>  tempUserRoleNameMap:{}", tempUserRoleNameMap);
            tempUserRoleNameMap.keySet().forEach(x -> {
                names.add(tempUserRoleNameMap.get(x));
            });
        }
        return trimTag(names.toString());
    }

    private void memberToDb(ActionContext context, List<String> monitors, String ruleId, int type, List<Map<String, Object>> insertMemberData) {
        long currentTimeMillis = System.currentTimeMillis();
        if(CollectionUtil.isNotEmpty(monitors) ){
            for( String user:  monitors ) {
                String _id = serviceFacade.generateId();
                Map<String, Object> memberItem = Maps.newHashMap();
                memberItem.put(CommonConstants.ID, _id);
                memberItem.put(Tenantable.TENANT_ID, context.getTenantId());
                memberItem.put(CommonConstants.OBJECT_DESCRIBE_API_NAME, QualityInspectionConf.OBJ_QUALITY_INSPECTION_RULE);
                memberItem.put(RuleMember.MEMBER_ID, user);
                memberItem.put(RuleMember.MEMBER_TYPE, type);
                memberItem.put(RuleMember.RULE_ID, ruleId);
                memberItem.put(CommonConstants.IS_DELETED, 0);
                memberItem.put(CommonConstants.CREATED_BY, context.getUser().getUpstreamOwnerIdOrUserId());
                memberItem.put(CommonConstants.CREATE_TIME, currentTimeMillis);
                memberItem.put(CommonConstants.LAST_MODIFIED_BY,  context.getUser().getUpstreamOwnerIdOrUserId());
                memberItem.put(CommonConstants.LAST_MODIFIED_TIME, currentTimeMillis);
                insertMemberData.add(memberItem);
            }
        }
    }

    public void rewriteListDoc(ControllerContext controllerContext, List<ObjectDataDocument> dataDocumentList){
        if(CollectionUtil.isNotEmpty(dataDocumentList)) {
            List<Object> ruleIds = dataDocumentList.stream().map(ObjectDataDocument::getId)
                .collect(Collectors.toList());
            List<Map> mapList = qualityInspectionDBService.findRule(controllerContext, ruleIds);
            if(CollectionUtil.isNotEmpty(mapList)){
                Map<String , Map> dbMap = Maps.newHashMap();
                for(Map map : mapList){
                    dbMap.put(String.valueOf(map.get("id")), map);
                }
                for(ObjectDataDocument dataDocument : dataDocumentList){
                    reloadDoc(dbMap.get(dataDocument.getId()), dataDocument);
                    //补齐monitor和msgUser
                    mergeMembers(controllerContext, Lists.newArrayList(dataDocument.getId()), dataDocument);
                }
            }
        }
    }

    private void reloadDoc(Map ruleMap, ObjectDataDocument dataDocument){
        if(ruleMap.containsKey("type")) {
            dataDocument.put("type", ruleMap.get("type"));
        }
        String dirty_words = StringUtil.convertString((String)ruleMap.get("dirty_words"), "");
        dataDocument.put("dirty_words", dirty_words);

        String session_feature = trimTag(StringUtil.convertString((String)ruleMap.get("session_feature"), ""));
        dataDocument.put("session_feature", session_feature);

        String session_feature_rule = StringUtil.convertString((String)ruleMap.get("session_feature_rule"), "");
        dataDocument.put("session_feature_rule", session_feature_rule);

        String session_action = trimTag(StringUtil.convertString((String)ruleMap.get("session_action"), ""));
        dataDocument.put("session_action", session_action);

        List<String> target_type = str2List(ruleMap, "target_type");
        dataDocument.put("target_type", target_type);

        List<String> session_type = str2List(ruleMap, "session_type");
        dataDocument.put("session_type", session_type);

        String msg_push = StringUtil.convertString((String)ruleMap.get("msg_push"), "");
        dataDocument.put("msg_push", msg_push);

        putRecordLayout((Integer) ruleMap.get("type"), dataDocument);
    }

    public void putRecordLayout(Integer type , ObjectDataDocument dataDocument){
        if(type != null) {
            if (type == 1) {
                dataDocument.put("record_type", record_dirtyword_layout);
                dataDocument.put("layout_api_name", dirtyword_layout_api);
                dataDocument.remove("session_feature");
                dataDocument.remove("session_action");
            } else if (type == 2) {
                dataDocument.put("record_type", record_action_layout);
                dataDocument.put("layout_api_name", action_layout_api);
                dataDocument.remove("session_feature");
                dataDocument.remove("dirty_words");
            } else if (type == 3) {
                dataDocument.put("record_type", record_feature_layout);
                dataDocument.put("layout_api_name", feature_layout_api);
                dataDocument.remove("dirty_words");
                dataDocument.remove("session_action");
            } else {
                dataDocument.put("record_type", record_dirtyword_layout);
                dataDocument.put("layout_api_name", dirtyword_layout_api);
            }
        }
    }

    public void rewriteDoc(ControllerContext controllerContext, List<Object> ruleIds, ObjectDataDocument dataDocument){
        if(dataDocument == null) return;
        Map rulemap = Safes.first(qualityInspectionDBService.findRule(controllerContext, ruleIds));
        log.warn("QualityInspectionRule findRule{}",rulemap);
        if(CollectionUtil.isNotEmpty(rulemap)){
            reloadDoc(rulemap, dataDocument);
        }
        //补齐monitor和msgUser
        mergeMembers(controllerContext, ruleIds, dataDocument);
    }

    public void updateRule(ActionContext context, String ruleId, ObjectDataDocument data) {
        Map<String, Object> dataMap = updateValue(context, data);
        qualityInspectionDBService.updateRule(context, ruleId, dataMap);
    }

    private Map<String, Object> updateValue(ActionContext context, ObjectDataDocument data){
        Map<String, Object> updateValue = Maps.newHashMap();
        int type = (Integer) data.get("type");
        if(type > 0){
            updateValue.put("type", type);
        }
        String dirty_words = StringUtil.convertString((String)data.get("dirty_words"), "");
        if(StringUtil.isNotEmpty(dirty_words) && !dirty_words.equalsIgnoreCase("null")){
            updateValue.put("dirty_words", dirty_words);
        }
        List<String> target_type = JSON.parseArray(JSON.toJSONString(data.get("target_type")), String.class);
        if(CollectionUtil.isNotEmpty(target_type)){
            updateValue.put("target_type", target_type.toString());
        }

        List<String> session_type = JSON.parseArray(JSON.toJSONString(data.get("session_type")), String.class);
        if(CollectionUtil.isNotEmpty(session_type)){
            updateValue.put("session_type", session_type.toString());
        }

        String msg_push = StringUtil.convertString((String)data.get("msg_push"), "");
        if(StringUtil.isNotEmpty(msg_push) && !msg_push.equalsIgnoreCase("null")){
            updateValue.put("msg_push", msg_push);
        }

        String session_feature = StringUtil.convertString((String)data.get("session_feature"), "");
        if(StringUtil.isNotEmpty(session_feature)){
            updateValue.put("session_feature", session_feature);
        }

        String session_feature_rule = StringUtil.convertString((String)data.get("session_feature_rule"), "");
        if(StringUtil.isNotEmpty(session_feature_rule) && !session_feature_rule.equalsIgnoreCase("null")){
            updateValue.put("session_feature_rule", session_feature_rule);
        }

        String session_action = StringUtil.convertString((String)data.get("session_action"), "");
        if(StringUtil.isNotEmpty(session_action) ){
            updateValue.put("session_action", session_action);
        }

        updateValue.put(CommonConstants.LAST_MODIFIED_TIME, System.currentTimeMillis());
        updateValue.put(CommonConstants.LAST_MODIFIED_BY, context.getUser().getUpstreamOwnerIdOrUserId());
        return updateValue;
    }

    public void addRuleOpLog(
        ServiceContext serviceContext, IObjectData objectData, ActionType actionType){
        addRuleOperateLog(serviceContext, I18N.text(SFA_PROCUREMENT_RULE_OP_LOG_MSG, I18N.text(actionType.getName()),objectData.getName()), actionType.getId());
    }

    public void addRuleOperateLog(ServiceContext context, String message, String operateType) {
        LogUtil.addSettingLog(context, message, "35", "35", operateType);
    }

    private List<String> str2List(Map ruleMap, String key){
        if(ruleMap.containsKey(key)){
            String src = (String)ruleMap.get(key);
            if(StringUtil.isNotEmpty(src)){
                return StringUtil.split(StringUtil.removeAll(StringUtil.removeAll(StringUtil.removeAll(src, " "), "["), "]").trim(), ",");
            }
        }
        return null;
    }

    private String trimTag(String key){
        if(StringUtil.isNotEmpty(key)){
            return StringUtil.removeAll(StringUtil.removeAll(StringUtil.removeAll(key, " "), "["), "]").trim();
        }
        return "";
    }
}
