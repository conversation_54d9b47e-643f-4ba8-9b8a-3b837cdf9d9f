package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule;
import com.facishare.crm.sfa.predefine.service.model.ListLastProduct;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.QuoteConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectMappingServiceImpl;
import com.facishare.paas.appframework.metadata.TableComponentExt;
import com.facishare.paas.appframework.metadata.layout.TableComponentRender;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IObjectMappingRuleDetailInfo;
import com.facishare.paas.metadata.api.IObjectMappingRuleEnumInfo;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IDataRightsParameter;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.DataRightsParameter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@ServiceModule("quote")
@Component
@Slf4j
public class QuoteService {
    @Autowired
    private ModuleCtrlConfigService moduleCtrlConfigService;
    @Autowired
    ServiceFacade serviceFacade;
	@Autowired
	private InfraServiceFacade infraServiceFacade;
    @Autowired
    private ObjectMappingServiceImpl objectMappingService;
    @Autowired
    private UserRoleInfoService userRoleInfoService;

    private static final int MAX_SUB_BATCH_SIZE = 1000;
    private static Integer limit;

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-sales-config",
                config -> limit = config.getInt("quote_lines_history_limit", 1));
    }

    /*
     * 校验是否开启CPQ,1标志就是开启的状态
     * */
    public String checkCpqStatus(User user, ActionContext actionContext) {
        return checkCpqStatus(user, actionContext.getRequestContext());
    }

    public String checkCpqStatus(User user, RequestContext requestContext) {
        ServiceContext serviceContext = new ServiceContext(requestContext, "", "");
        ConfigCtrlModule.Arg configCtrlModule = new ConfigCtrlModule.Arg();
        configCtrlModule.setModuleCode("cpq");
        configCtrlModule.setTenantId(user.getTenantId());
        ConfigCtrlModule.Result configCtrlModuleResult = moduleCtrlConfigService.checkModuleStatus(configCtrlModule, serviceContext);
        return configCtrlModuleResult.getValue().getOpenStatus();
    }

    @ServiceMethod("list_last_product")
    public ListLastProduct.Result listLastProduct(ServiceContext serviceContext, ListLastProduct.Arg arg) {
        Map<String, ListLastProduct.LayoutData> map = Maps.newHashMap();
        Boolean isPackage = isPackageProduct(serviceContext.getUser(), arg.getProductId());
        ListLastProduct.LayoutData orderProductInfo = getOrderProductInfo(serviceContext.getUser(), arg.getAccountId(),
                arg.getPriceBookId(), arg.getProductId(), arg.getRecordType(), isPackage);
        map.put(SFAPreDefineObject.SalesOrderProduct.getApiName(), orderProductInfo);
        ListLastProduct.LayoutData quoteLinesInfo = getQuoteLinesInfo(serviceContext.getUser(), arg.getAccountId(),
                arg.getPriceBookId(), arg.getProductId(), arg.getRecordType(), isPackage);
        map.put(SFAPreDefineObject.QuoteLines.getApiName(), quoteLinesInfo);
        return ListLastProduct.Result.builder().data(map).build();
    }

    private ListLastProduct.LayoutData getQuoteLinesInfo(User user, String accountId, String priceBookId,
                                                         String productId, String recordType, Boolean isPackage) {
        ObjectDescribeExt objectDescribe = findObject(user.getTenantId(), SFAPreDefineObject.QuoteLines.getApiName());
        List<ILayout> listLayouts = findListLayouts(user, objectDescribe);
        List<IObjectData> objectDataList = getLastData(user, objectDescribe, accountId, priceBookId, productId,
                recordType, isPackage);
        asyncFillFieldInfo(user, objectDescribe, objectDataList);
        return ListLastProduct.LayoutData.builder()
                .dataList(ObjectDataDocument.ofList(objectDataList))
                .objectDescribe(ObjectDescribeDocument.of(objectDescribe))
                .listLayouts(LayoutDocument.ofList(listLayouts))
                .build();
    }

    private ListLastProduct.LayoutData getOrderProductInfo(User user, String accountId, String priceBookId,
                                                           String productId, String recordType, Boolean isPackage) {
        ObjectDescribeExt objectDescribe = findObject(user.getTenantId(), SFAPreDefineObject.SalesOrderProduct.getApiName());
        List<ILayout> listLayouts = findListLayouts(user, objectDescribe);
        String sourceRecordType = getSourceRecordType(user, recordType);
        List<IObjectData> objectDataList = getLastData(user, objectDescribe, accountId, priceBookId, productId,
                sourceRecordType, isPackage);
        asyncFillFieldInfo(user, objectDescribe, objectDataList);
        return ListLastProduct.LayoutData.builder()
                .dataList(ObjectDataDocument.ofList(objectDataList))
                .objectDescribe(ObjectDescribeDocument.of(objectDescribe))
                .listLayouts(LayoutDocument.ofList(listLayouts))
                .build();
    }

    private void asyncFillFieldInfo(User user, IObjectDescribe describe, List<IObjectData> dataList) {
        List<IObjectData> synchronizedDataList = ObjectDataExt.synchronize(dataList);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            fillQuoteFieldValue(user, describe, synchronizedDataList, null);
        });
        parallelTask.submit(() -> {
            fillRefObjectName(user, describe, synchronizedDataList, null);
        });
        parallelTask.submit(() -> {
            fillInfo(user, describe, synchronizedDataList);
        });
        try {
            parallelTask.await(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Error in fill info of list_last_product, ei:{}, object:{}", user.getTenantId(), describe.getApiName(), e);
        }
    }

    private void fillQuoteFieldValue(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList,
                                     Map<String, List<IObjectData>> refObjectDataMap) {
        infraServiceFacade.fillQuoteFieldValue(user, dataList, objectDescribe, refObjectDataMap, false);
    }

    private void fillRefObjectName(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList,
                                   Map<String, List<IObjectData>> refObjectDataMap) {
        serviceFacade.fillObjectDataWithRefObject(objectDescribe, dataList, user, refObjectDataMap);
    }

    private void fillInfo(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        serviceFacade.fillUserInfo(objectDescribe, dataList, user);
        serviceFacade.fillDepartmentInfo(objectDescribe, dataList, user);
    }

    private String getSourceRecordType(User user, String targetRecordType) {
        String sourceRecordType = null;
        if (Strings.isNullOrEmpty(targetRecordType)) {
            return sourceRecordType;
        }
		String ruleApiName = "rule_salesorderprodobj2quotelinesobj__c";
        List<IObjectMappingRuleInfo> mappingRuleInfoList = objectMappingService.findByApiName(user,
                ruleApiName);
        if (CollectionUtils.empty(mappingRuleInfoList)) {
            return sourceRecordType;
        }
		IObjectMappingRuleInfo ruleInfo = mappingRuleInfoList.stream()
				.filter(r -> ruleApiName.equals(r.getRuleApiName())).findFirst().orElseThrow(IllegalArgumentException::new);
		List<IObjectMappingRuleDetailInfo> fieldMappingList = ruleInfo.getFieldMapping();
        if (CollectionUtils.empty(fieldMappingList)) {
            return sourceRecordType;
        }
        Optional<IObjectMappingRuleDetailInfo> recordTypeMappingInfo = fieldMappingList.stream()
                .filter(rule -> IFieldType.RECORD_TYPE.equals(rule.getSourceFieldName()))
                .findFirst();
        List<IObjectMappingRuleEnumInfo> enumMappingList = Lists.newArrayList();
        if (recordTypeMappingInfo.isPresent()) {
            enumMappingList = recordTypeMappingInfo.get().getOptionMapping();
        }
        if (CollectionUtils.empty(enumMappingList)) {
            return sourceRecordType;
        }
        Optional<IObjectMappingRuleEnumInfo> enumInfoOptional = enumMappingList.stream()
                .filter(enumInfo -> targetRecordType.equals(enumInfo.getTargetEnumCode()))
                .findFirst();
        if (enumInfoOptional.isPresent()) {
            sourceRecordType = enumInfoOptional.get().getSourceEnumCode();
        }
        return sourceRecordType;
    }

    private List<ILayout> findListLayouts(User user, ObjectDescribeExt objectDescribe) {
        List<ILayout> listLayouts = serviceFacade.findMobileListLayout(user, objectDescribe, true);
        listLayouts.forEach(layout -> doRender(layout, user, objectDescribe));
        return listLayouts;
    }

    private Boolean isPackageProduct(User user, String productId) {
        if (!Strings.isNullOrEmpty(productId)) {
            IObjectData productData = serviceFacade.findObjectData(user, productId, SFAPreDefineObject.Product.getApiName());
            if (productData != null) {
                return productData.get("is_package", Boolean.class);
            }
        }
        return Boolean.FALSE;
    }

    private List<IObjectData> getLastData(User user, ObjectDescribeExt objectDescribe, String accountId,
                                          String priceBookId, String productId, String recordType, Boolean isPackage) {
        List<IObjectData> objectDataList = Lists.newArrayList();
        if (Strings.isNullOrEmpty(accountId) || Strings.isNullOrEmpty(productId) || Strings.isNullOrEmpty(recordType)) {
            return objectDataList;
        }
        SearchTemplateQuery query = buildSearchQuery(user, accountId, priceBookId, productId, recordType, isPackage,
                objectDescribe.getApiName());
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, objectDescribe,
                objectDescribe.getApiName(), query);
        if (queryResult != null) {
            objectDataList = queryResult.getData();
            if (CollectionUtils.notEmpty(objectDataList) && Boolean.TRUE.equals(isPackage)) {
                fillSubProduct(user, objectDescribe, objectDataList);
            }
            if (CollectionUtils.notEmpty(objectDataList)) {
                return objectDataList.stream()
                        .sorted(Comparator.comparing(o -> o.get("name", String.class)))
                        .collect(Collectors.toList());
            }
            return objectDataList;
        } else {
            return objectDataList;
        }
    }

    private SearchTemplateQuery buildSearchQuery(User user, String accountId, String priceBookId, String productId,
                                                 String recordType, Boolean isPackage, String apiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        filters.add(getEqualFilter("product_id", productId, Boolean.FALSE));
        filters.add(getEqualFilter("record_type", recordType, Boolean.FALSE));
        filters.add(getEqualFilter("account_id", accountId, Boolean.TRUE));
        if (!Strings.isNullOrEmpty(priceBookId)) {
            filters.add(getEqualFilter("price_book_id", priceBookId, Boolean.FALSE));
        }
        IFilter filter = new Filter();
        filter.setFieldName("prod_pkg_key");
        filter.setFieldValues(Lists.newArrayList());
        if (Boolean.TRUE.equals(isPackage)) {
            filter.setOperator(Operator.ISN);
        } else {
            filter.setOperator(Operator.IS);
        }
        filters.add(filter);
        query.setFilters(filters);
        query.setOffset(0);
        if (Objects.nonNull(limit)) {
            query.setLimit(limit);
        }

        if (isAdmin(user)) {
            query.setPermissionType(0);
        } else {
            query.setPermissionType(1);
            query.setDataRightsParameter(getDataRightsParameter(apiName));
        }
        return query;
    }

    private boolean isAdmin(User user) {
        return user.isSupperAdmin() || userRoleInfoService.isAdmin(user);
    }

    private String getMasterId(String apiName, IObjectData objectData) {
        String masterId = "";
        if (SFAPreDefineObject.QuoteLines.getApiName().equals(apiName)) {
            masterId = objectData.get("quote_id", String.class);
        } else if (SFAPreDefineObject.SalesOrderProduct.getApiName().equals(apiName)) {
            masterId = objectData.get("order_id", String.class);
        }
        return masterId;
    }

    public void fillSubProduct(User user, ObjectDescribeExt objectDescribe, List<IObjectData> objectDataList) {
        int offset = 0;
        int loopCnt = 0;
        String packageKey = objectDataList.get(0).get("prod_pkg_key", String.class);
        String masterId = getMasterId(objectDescribe.getApiName(), objectDataList.get(0));
        if (Strings.isNullOrEmpty(packageKey) || Strings.isNullOrEmpty(masterId)) {
            return;
        }
        SearchTemplateQuery templateQuery = buildSubProductQuery(objectDescribe.getApiName(), packageKey, masterId);
        objectDataList.clear();
        while (loopCnt < 10) {
            templateQuery.setOffset(offset);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, objectDescribe,
                    objectDescribe.getApiName(), templateQuery);
            if (queryResult != null && CollectionUtils.notEmpty(queryResult.getData())) {
                objectDataList.addAll(queryResult.getData());
            }
            if (queryResult == null || queryResult.getTotalNumber() < MAX_SUB_BATCH_SIZE) {
                break;
            }
            offset += MAX_SUB_BATCH_SIZE;
            loopCnt++;
        }
    }

    private SearchTemplateQuery buildSubProductQuery(String apiName, String packageKey, String masterId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        filters.add(getEqualFilter("root_prod_pkg_key", packageKey, Boolean.FALSE));
        if (SFAPreDefineObject.QuoteLines.getApiName().equals(apiName)) {
            filters.add(getEqualFilter("quote_id", masterId, Boolean.FALSE));
        } else if (SFAPreDefineObject.SalesOrderProduct.getApiName().equals(apiName)) {
            filters.add(getEqualFilter("order_id", masterId, Boolean.FALSE));
        }
        query.setFilters(filters);
        query.setOffset(0);
        query.setPermissionType(0);
        query.setLimit(MAX_SUB_BATCH_SIZE);
        return query;
    }

    public IDataRightsParameter getDataRightsParameter(String apiName) {
        IDataRightsParameter dataRightsParameter = new DataRightsParameter();
        dataRightsParameter.setRoleType("1");
        dataRightsParameter.setSceneType("all");
        dataRightsParameter.setCascadeDept(true);
        dataRightsParameter.setCascadeSubordinates(true);
        dataRightsParameter.setIsDetailObject(true);
        if (SFAPreDefineObject.QuoteLines.getApiName().equals(apiName)) {
            dataRightsParameter.setMasterObjectApiName(SFAPreDefineObject.Quote.getApiName());
            dataRightsParameter.setMasterIdFieldApiName(QuoteConstants.QuoteLinesField.QUOTE_ID.getApiName());
        } else {
            dataRightsParameter.setMasterObjectApiName(SFAPreDefineObject.SalesOrder.getApiName());
            dataRightsParameter.setMasterIdFieldApiName(SalesOrderConstants.SalesOrderProductField.ORDER_ID.getApiName());
        }
        return dataRightsParameter;
    }

    private IFilter getEqualFilter(String fieldName, String filterValue, Boolean isMasterField) {
        IFilter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setFieldValues(Lists.newArrayList(filterValue));
        filter.setOperator(Operator.EQ);
        if (Boolean.TRUE.equals(isMasterField)) {
            filter.setIsMasterField(isMasterField);
        }
        return filter;
    }

    private ObjectDescribeExt findObject(String tenantId, String apiName) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, apiName);
        ObjectDescribeExt ret = ObjectDescribeExt.copy(objectDescribe);
        ret.removeFieldDescribe("extend_obj_data_id");
        return ret;
    }

    private void doRender(ILayout x, User user, ObjectDescribeExt objectDescribe) {
        this.getComponentRender(x, user, objectDescribe).render();
    }

    public TableComponentRender getComponentRender(ILayout x, User user, ObjectDescribeExt objectDescribe) {
        Optional<TableComponent> tableComponent = LayoutExt.of(x).getTableComponent();
        if(tableComponent.isPresent()){
            return TableComponentRender.builder()
                    .functionPrivilegeService(serviceFacade)
                    .user(user)
                    .describeExt(objectDescribe)
                    .tableComponentExt(TableComponentExt.of(tableComponent.get()))
                    .build();
        }else{
            return TableComponentRender.builder()
                    .functionPrivilegeService(serviceFacade)
                    .user(user)
                    .describeExt(objectDescribe)
                    .build();
        }
    }

    //选中导出时，增加导出被选中产品包的子产品
    public void fillSubProduct(User user, List<IObjectData> dataList, IObjectDescribe objectDescribe, Set<String> idList) {
        if (CollectionUtils.notEmpty(dataList)) {
            Map<String/*root_prod_pkg_key*/, List<IObjectData>> dataMap = dataList.stream().filter(x-> StringUtils.isNotBlank(x.get(QuoteConstants.QuoteLinesField.ROOT_PROD_PKG.getApiName(), String.class))&&
                    StringUtils.isNotBlank(x.get(QuoteConstants.QuoteLinesField.QUOTE_ID.getApiName(), String.class)))
                    .collect(Collectors.groupingBy(x->x.get(QuoteConstants.QuoteLinesField.ROOT_PROD_PKG.getApiName(), String.class)));
            Map<String,List<String>> condMap = Maps.newHashMap();
            IObjectData objectData;
            for (Map.Entry<String, List<IObjectData>> entry : dataMap.entrySet()) {
                List<IObjectData> tmpList = entry.getValue();
                if(CollectionUtils.notEmpty(tmpList)&&tmpList.size()==1){
                    objectData = tmpList.get(0);
                    condMap.putIfAbsent(objectData.get(QuoteConstants.QuoteLinesField.QUOTE_ID.getApiName(),String.class), Lists.newArrayList());
                    condMap.get(objectData.get(QuoteConstants.QuoteLinesField.QUOTE_ID.getApiName(),String.class)).add(tmpList.get(0).get(QuoteConstants.QuoteLinesField.ROOT_PROD_PKG.getApiName(), String.class));
                }
            }
            Iterator<IObjectData> iterator = dataList.iterator();
            while(iterator.hasNext()){
                objectData = iterator.next();
                if (idList.contains(objectData.getId())) {
                    iterator.remove();
                }else {
                    idList.add(objectData.getId());
                }
            }
            if (MapUtils.isNotEmpty(condMap)) {
                int offset = 0;
                int loopCnt = 0;
                SearchTemplateQuery templateQuery = buildSubProductQuery(condMap);
                while (loopCnt < 10) {
                    templateQuery.setOffset(offset);
                    QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, objectDescribe.getApiName(), templateQuery);
                    if (queryResult != null && CollectionUtils.notEmpty(queryResult.getData())) {
                        queryResult.getData().stream().forEach(x->{
                            if (!idList.contains(x.getId())) {
                                dataList.add(x);
                                idList.add(x.getId());
                            }
                        });
                    }
                    if (queryResult == null || queryResult.getTotalNumber() < MAX_SUB_BATCH_SIZE) {
                        break;
                    }
                    offset += MAX_SUB_BATCH_SIZE;
                    loopCnt++;
                }
                dataList.sort((x, y) -> y.getLastModifiedTime().compareTo(x.getLastModifiedTime()));
            }
        }
    }

    private SearchTemplateQuery buildSubProductQuery(Map<String,List<String>> condMap) {
        Set<String> rootIds = condMap.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, QuoteConstants.QuoteLinesField.ROOT_PROD_PKG.getApiName(), rootIds);
        SearchUtil.fillFilterIn(filters, QuoteConstants.QuoteLinesField.QUOTE_ID.getApiName(), condMap.keySet());
        query.setFilters(filters);
        query.setOffset(0);
        query.setLimit(MAX_SUB_BATCH_SIZE);
        return query;
    }
}

