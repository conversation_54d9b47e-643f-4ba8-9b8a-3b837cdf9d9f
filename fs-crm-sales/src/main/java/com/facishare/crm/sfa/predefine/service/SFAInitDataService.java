package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.predefine.service.model.CategoryObject;
import com.facishare.crm.sfa.predefine.service.model.CheckDeleteCategoryModel;
import com.facishare.crm.sfa.predefine.service.model.SFAInitDataModel;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.ProductCategoryUtils;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.validator.ProductCategoryValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject.API_NAME;
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject.CODE;
import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_CHECK_CATEGORY_DELETE;

@ServiceModule("sfa_init_data_service")
@Component
@Slf4j
public class SFAInitDataService {
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Autowired
    private ProductCategoryBizService productCategoryBizService;
    @Autowired
    private ProductCategoryValidator productCategoryValidator;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private SpuSkuService spuSkuService;
    @Resource
    ProductCategoryUtils productCategoryUtils;


    @ServiceMethod("del_data")
    public SFAInitDataModel.DelDataResult delData(ServiceContext context, SFAInitDataModel.DelDataArg arg) {
        User user = new User(context.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        SFAInitDataModel.DelDataResult rst = SFAInitDataModel.DelDataResult.builder().build();
        rst.setDelCount(Maps.newHashMap());

        if (CollectionUtils.isEmpty(arg.getApiNames())) return rst;
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(context.getTenantId(), arg.getApiNames());
        Map<String, Integer> tmp = Maps.newHashMap();
        arg.getApiNames().stream().filter(describeMap::containsKey).forEach(s -> {
            int count = delSingleTypeData(user, s);
            tmp.put(s, count);
        });
        rst.setDelCount(tmp);
        return rst;
    }

    private int delSingleTypeData(User user, String apiName) {
        SearchTemplateQueryExt query = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        query.setLimit(200);
        query.addFilter(Operator.EQ, IObjectData.DESCRIBE_API_NAME, apiName);
        query.addFilter(Operator.NEQ, DBRecord.IS_DELETED, String.valueOf(DELETE_STATUS.DELETE.getValue()));
        query.setPermissionType(0);
        query.setNeedReturnCountNum(Boolean.FALSE);
        query.setNeedReturnQuote(Boolean.FALSE);
        int i = 0;
        int c = 200;
        int count = 0;
        while (c >= 200) {
            QueryResult<IObjectData> dataList = serviceFacade.findBySearchQuery(user, apiName, (SearchTemplateQuery) query.getQuery());
            c = dataList.getData().size();
            count += c;
            serviceFacade.bulkDeleteDirect(dataList.getData(), user);
            i++;
            if (i > 10000) break;
        }
        return count;
    }

    @ServiceMethod("del_product_category")
    public SFAInitDataModel.DelDataResult delProductCategory(ServiceContext context) {
        SFAInitDataModel.DelDataResult rst = SFAInitDataModel.DelDataResult.builder().build();
        rst.setDelCount(Maps.newHashMap());

        List<IObjectData> categoryList = productCategoryUtils.findAllCategory(context.getUser()).getData();
        if (CollectionUtils.isEmpty(categoryList)) {
            rst.getDelCount().put("category", 0);
            return rst;
        }

        List<List<IObjectData>> tmp = Lists.partition(categoryList, 200);
        for (List<IObjectData> tmpList : tmp) {
            serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(tmpList, context.getUser());
        }
        rst.getDelCount().put("category", categoryList.size());
        return rst;
    }

    @ServiceMethod("del_deleted_product_category")
    public SFAInitDataModel.DelDataResult delDeletedProductCategory(ServiceContext context) {
        SFAInitDataModel.DelDataResult rst = SFAInitDataModel.DelDataResult.builder().build();
        rst.setDelCount(Maps.newHashMap());

        List<IObjectData> categoryList = productCategoryUtils.findDeletedCategory(context.getUser()).getData();
        if (CollectionUtils.isEmpty(categoryList)) {
            rst.getDelCount().put("category", 0);
            return rst;
        }

        List<List<IObjectData>> tmp = Lists.partition(categoryList, 200);
        for (List<IObjectData> tmpList : tmp) {
            serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(tmpList, context.getUser());
        }
        rst.getDelCount().put("category", categoryList.size());
        return rst;
    }

    @ServiceMethod("old_category_handler")
    public SFAInitDataModel.DelDataResult oldCategoryHandler(List<String> tenants) {
        SFAInitDataModel.DelDataResult rst = SFAInitDataModel.DelDataResult.builder().build();
        rst.setDelCount(new HashMap<>());
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setSearchSource("db");
        searchQuery.setLimit(0);
        List<String> failedTenant = Lists.newArrayList();
        for (String tenant : tenants) {
            try {
                categoryHandle(searchQuery, tenant);
            } catch (Exception e) {
                log.warn("old_category_handler error, tenant:{}", tenant, e);
                failedTenant.add(tenant);
            }
        }
        log.warn("old_category_handler finished, failed:{}", failedTenant);
        for (String tenant : failedTenant) {
            rst.getDelCount().put(tenant, 1);
        }
        return rst;
    }

    @ServiceMethod("spu_sku_handler")
    public SFAInitDataModel.DelDataResult spuSkuHandler(List<String> tenants) {
        SFAInitDataModel.DelDataResult rst = SFAInitDataModel.DelDataResult.builder().build();
        rst.setDelCount(new HashMap<>());
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setSearchSource("db");
        searchQuery.setLimit(0);
        List<IFilter> filters = searchQuery.getFilters();
        Filter filter = new Filter();
        filter.setFieldName("is_deleted");
        filter.setOperator(Operator.GTE);
        filter.setFieldValues(Lists.newArrayList("0"));
        filters.add(filter);
        List<String> failedTenant = Lists.newArrayList();
        for (String tenant : tenants) {
            try {
                User user = new User(tenant, "-10000");
                List<IObjectData> allCategory = productCategoryUtils.findAllCategory(user).getData();
                Map<String, String> codeMap = allCategory.stream().collect(Collectors.toMap(DBRecord::getId, c -> c.get("code", String.class)));
                metaDataFindServiceExt.dealDataByTemplate(user, "ProductObj", searchQuery, dataList -> {
                    updateDataCategory(user, codeMap, dataList);
                });
                if (SFAConfigUtil.isSpuOpen(user.getTenantId())) {
                    metaDataFindServiceExt.dealDataByTemplate(user, "SPUObj", searchQuery, dataList -> {
                        updateDataCategory(user, codeMap, dataList);
                    });
                }
            } catch (Exception e) {
                log.warn("spu_sku_handler error, tenant:{}", tenant, e);
                failedTenant.add(tenant);
            }
        }
        log.warn("old_category_handler finished, failed:{}", failedTenant);
        for (String tenant : failedTenant) {
            rst.getDelCount().put(tenant, 1);
        }
        return rst;
    }

    private void updateDataCategory(User user, Map<String, String> codeMap, List<IObjectData> dataList) {
        List<IObjectData> updateDataList = Lists.newArrayList();
        for (IObjectData data : dataList) {
            String categoryId = data.get("product_category_id", String.class);
            String category = data.get("category", String.class);
            String newCode = codeMap.get(categoryId);
            if (Objects.equals(category, newCode)) {
                continue;
            }
            data.set("category", newCode);
            updateDataList.add(data);
        }
        updateDataList = updateDataList.stream().filter(d -> !StringUtils.isEmpty(ObjectDataUtils.getValueOrDefault(d, "product_category_id", ""))).collect(Collectors.toList());
        metaDataFindServiceExt.bulkUpdateByFields(user, updateDataList, Lists.newArrayList("category"));
    }

    private void categoryHandle(SearchTemplateQuery searchQuery, String tenant) throws Exception {
        User user = new User(tenant, "-10000");
        metaDataFindServiceExt.dealDataByTemplate(user, "ProductCategoryObj", searchQuery, categoryList -> {
            for (IObjectData category : categoryList) {
                category.set("code", productCategoryBizService.getRandomId());
            }
            metaDataFindServiceExt.bulkUpdateByFields(user, categoryList, Lists.newArrayList("code"));
        });
        synchronizeDescribe(user);
    }

    private void update(User user, Map<String, String> codeMap, List<IObjectData> dataList) {
        List<IObjectData> updateDataList = Lists.newArrayList();
        for (IObjectData data : dataList) {
            String originalCategory = ObjectDataUtils.getValueOrDefault(data, "category", "");
            if (StringUtils.isEmpty(originalCategory)) {
                continue;
            }
            String newCode = codeMap.get(originalCategory);
            if (StringUtils.isEmpty(newCode)) {
                continue;
            }
            data.set("category", newCode);
            updateDataList.add(data);
        }
        metaDataFindServiceExt.bulkUpdateByFields(user, updateDataList, Lists.newArrayList("category"));
    }


    private void synchronizeDescribe(User user) throws MetadataServiceException {
        IActionContext actionContext = ActionContextExt.of(user).skipRelevantTeam().getContext();
        actionContext.setFieldSkipBaseValid(true);
        actionContext.put(ActionContextKey.NOT_CHECK_OPTION_HAS_DATA, true);
        CategoryObject categoryObject = productCategoryValidator.getCategoryTreeList(user);
        List<ISelectOption> options = convert(categoryObject);
        ArrayList<String> needUpdateDesc = Lists.newArrayList(Utils.PRODUCT_API_NAME);
        if (SFAConfigUtil.isSpuOpen(user.getTenantId())) {
            needUpdateDesc.add(Utils.SPU_API_NAME);
        }
        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(user.getTenantId(), needUpdateDesc);
        for (String descApiName : needUpdateDesc) {
            SelectOneFieldDescribe category = (SelectOneFieldDescribe) describeMap.get(descApiName).getFieldDescribe("category");
            category.setSelectOptions(options);
            objectDescribeService.updateFieldDescribe(describeMap.get(descApiName), Lists.newArrayList(category), actionContext);
        }
    }

    private List<ISelectOption> convert(CategoryObject categoryObject) {

        List<ISelectOption> selectOptionList = Lists.newArrayList();

        List<CategoryObject> children = categoryObject.getChildren();
        if (CollectionUtils.isNotEmpty(children)) {
            for (CategoryObject child : children) {
                dealCurrentLevel("", selectOptionList, child);
            }
        }
        //循环一遍 selectOneList，将Label结尾有"/"的删除掉。
        for (ISelectOption selectOption : selectOptionList) {
            if (selectOption.getLabel().endsWith("/")) {
                selectOption.setLabel(selectOption.getLabel().substring(0, selectOption.getLabel().length() - 1));
            }
        }
        return selectOptionList;
    }

    private void dealCurrentLevel(String itemNameOld, List<ISelectOption> selectOptionList, CategoryObject categoryObject) {
        String itemName = categoryObject.getName();
        itemName = itemNameOld + itemName + "/";
        List<CategoryObject> children = categoryObject.getChildren();
        if (CollectionUtils.isNotEmpty(children)) {
            for (CategoryObject child : children) {
                dealCurrentLevel(itemName, selectOptionList, child);
            }
        }
        String itemCode = categoryObject.getCode();
        SelectOption selectOption = new SelectOption(itemName, itemCode.toString(), "");
        selectOptionList.add(selectOption);
    }

    @ServiceMethod("ignoreAllSyncDescribe")
    public Boolean ignoreAllSyncDescribe(List<String> tenants) throws MetadataServiceException {
        for (String tenant : tenants) {
            User user = new User(tenant, "-10000");
            synchronizeDescribe(user);
        }
        return true;
    }

    @ServiceMethod("del_category_tree")
    public Boolean delCategoryTree(ServiceContext context, SFAInitDataModel.DelCategoryTreeArg arg) {
        User user = new User(arg.getTenant(), "-10000");
        String rootId = arg.getRootId();
        IObjectData categoryObj = metaDataFindServiceExt.findObjectData(user, rootId, "ProductCategoryObj");
        String code = categoryObj.get("code").toString();
        Set<String> categoryChildren = productCategoryUtils.findCategoryChildren(user, code);
        CheckDeleteCategoryModel.Result result = spuSkuService.checkDeleteCategory(new CheckDeleteCategoryModel.Arg(Lists.newArrayList(categoryChildren)), context);
        if (!result.getResult()) {
            throw new ValidateException(I18N.text(SO_CHECK_CATEGORY_DELETE));
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(categoryChildren.size());
        SearchUtil.fillFilterIn(searchTemplateQuery.getFilters(), CODE, categoryChildren);
        QueryResult<IObjectData> bySearchQuery = metaDataFindServiceExt.findBySearchQuery(context.getUser(), API_NAME, searchTemplateQuery);
        serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(bySearchQuery.getData(), context.getUser());
        return true;
    }
}
