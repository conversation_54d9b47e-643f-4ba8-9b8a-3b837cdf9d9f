package com.facishare.crm.sfa.predefine.service;

import com.google.common.collect.Maps;
import com.facishare.crm.constants.SimplePolicyConstants;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.ExceptionUtils;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.ui.layout.ILayout;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


/**
 * <AUTHOR>
 */
@ServiceModule("simple_price")
@Service
@Slf4j
public class SimplePricePolicyService {

    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private ServiceFacade serviceFacade;

    /**
     * 查询当前租户的布局
     */
    @ServiceMethod("is_simple_price_policy")
    public Map<String, String> checkIsSimplePricePolicy(SimplePricePolicyModel.ArgSimplePricePolicyId arg, ServiceContext context) {
        Map<String, String> result = Maps.newHashMap ();
        IActionContext actionContext = ActionContextExt.of (context.getUser ()).skipRelevantTeam ().getContext ();
        IObjectData masterData = null;
        //判断添加方法,通用的卡片页面,add方法不调用该接口
        if (SimplePolicyConstants.ADDOREDITORLIST.ADD.getValue ().equals (arg.getSimplePricePolicyType ())) {
            result.put (SimplePolicyConstants.ModeType.ModeType, getLayoutType (context));
            //所有的修改,都调用该接口
        } else if (SimplePolicyConstants.ADDOREDITORLIST.EDIT.getValue ().equals (arg.getSimplePricePolicyType ())) {
            //通过价格政策id获取数据,判断modeType字段数据
            if (Strings.isNotBlank (arg.getSimplePricePolicyId ())) {
                try {
                    masterData = getMasterData (arg, context, actionContext);
                } catch (MetadataServiceException e) {
                    log.error ("cannot find pricePolicy data pricePolicyId is {} ", arg.getSimplePricePolicyId ());
                }
                if (null != masterData) {
                    String modeType = masterData.get (SimplePolicyConstants.ModeType.ModeType, String.class);
                    //modeType为空为老数据,返回高级版
                    if (StringUtils.isEmpty (modeType)) {
                        result.put (SimplePolicyConstants.ModeType.ModeType, SimplePolicyConstants.ModeType.COMPLEX);
                    } else {
                        //如果不为空 返回数据本身布局
                        result.put (SimplePolicyConstants.ModeType.ModeType, modeType);
                    }
                } else {
                    //不能查找到价格政策
                    log.error (" pricePolicyId is not blank but connot find by id {} ", arg.getSimplePricePolicyId ());
                }
            } else {
                //价格政策唯一标识
                log.error (" pricePolicyId is blank ");
            }
        }
        return result;
    }

    //返回布局名称
    private String getLayoutType(ServiceContext context) {
        ILayout layout = serviceFacade.findObjectLayout (context.getUser (), "default__c", SFAPreDefineObject.PricePolicy.getApiName ());
        String apiLayoutName = layout.get ("api_name", String.class).toLowerCase ();
        //如果是银鹭企业
        boolean simplePricePolicy = GrayUtil.isSimplePricePolicy (context.getTenantId ());
        //当前登入人没有布局
        if (StringUtils.isEmpty (apiLayoutName)) {
            throw ExceptionUtils.supplier (SFAI18NKeyUtil.SFA_PRICE_POLICY_LAYOUT_USER_NOT_HAVE, "sfa.price.policy.layout.user.not.have").get ();
        }
        //布局名称含有simple__c 但不是银鹭企业依然返回高级版
        if (apiLayoutName.contains ("simple__c") && simplePricePolicy) {
            return SimplePolicyConstants.ModeType.SIMPLE;
            //布局名称含有general__c 返回通用布局
        } else if (apiLayoutName.contains ("general__c")) {
            return SimplePolicyConstants.ModeType.GENERAL;
        } else {
            //返回复杂布局
            return SimplePolicyConstants.ModeType.COMPLEX;
        }
    }


    //通过价格政策id 获取数据
    private IObjectData getMasterData(SimplePricePolicyModel.ArgSimplePricePolicyId arg, ServiceContext context, IActionContext actionContext) throws MetadataServiceException {
        return objectDataService.findById (arg.getSimplePricePolicyId (), context.getTenantId (), actionContext,
                SFAPreDefineObject.PricePolicy.getApiName ());
    }

}
