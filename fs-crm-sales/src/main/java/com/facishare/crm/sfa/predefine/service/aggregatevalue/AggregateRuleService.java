package com.facishare.crm.sfa.predefine.service.aggregatevalue;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.aggregatevalue.AggregateRuleObjectDescribeModel.AggregateRuleObjectDescribe;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.DescribeUtils;
import com.facishare.crm.sfa.utilities.common.UseRangeFieldDataRender.FilterGroup;
import com.facishare.crm.sfa.utilities.constant.AggregateRuleConstants;
import com.facishare.crm.sfa.utilities.constant.AggregateRuleConstants.DateRangeEnum;
import com.facishare.crm.sfa.utilities.constant.AggregateRuleConstants.Field;
import com.facishare.crm.sfa.utilities.dataconverter.FieldValueConvertContext;
import com.facishare.crm.sfa.utilities.dataconverter.FieldValueConvertFactory;
import com.facishare.crm.sfa.utilities.dataconverter.converter.AbstractFieldValueConverter;
import com.facishare.crm.sfa.utilities.util.OperateMappingUtils;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.util.AggregateRuleUtil;
import com.facishare.crm.util.DomainPluginDescribeExt;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.exception.ErrorCode;
import com.facishare.paas.metadata.exception.MetadataValidateException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.reference.data.DeleteArg;
import com.facishare.paas.reference.data.EntityReferenceArg;
import com.facishare.paas.reference.service.EntityReferenceService;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

/**
 * 聚合规则Service
 */
@ServiceModule("aggregate_rule")
@Slf4j
@Component
public class AggregateRuleService {

    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;

    @Autowired
    private EntityReferenceService entityReferenceService;
    @Autowired
    private InfraServiceFacade infraServiceFacade;


    /**
     * 时间范围月数
     */
    public static int range;

    /**
     * 聚合维度模版
     */
    private static String aggregateDimension;

    public static final String FIELD_VALUE = "value";
    public static final String FIELD_OPTIONS = "options";
    public static final String FIELD_LABEL = "label";
    public static final String FIELD_CHILDREN = "children";
    public static final String FIELD_TARGET_API_NAME = "target_api_name";
    public static final String FIELD_PARTNER_ID = "partner_id";
    public static final String FIELD_OPERATOR = "operator";
    public static final String KEY_MASTER_DESCRIBE_API_NAME = "master_describe_api_name";


    static {
        ConfigFactory.getInstance().getConfig("fs-crm-sales-config", config -> {
            range = config.getInt("aggregate_date_range", 13);
        });
        // 加载聚合维度
        aggregateDimension = geAggregateDimensionTemplate("aggregatedimension");
    }

    /**
     * 获取聚合值对象
     *
     * @param serviceContext context
     * @return GetAggregateValueObjectResult
     * @deprecated 废弃聚合对象写死在描述里
     */
    @ServiceMethod("getAggregateValueObject")
    public AggregateRuleObjectDescribeModel.GetAggregateRuleObjectDescribeResult getAggregateValueObject(ServiceContext serviceContext) {
        AggregateRuleObjectDescribeModel.GetAggregateRuleObjectDescribeResult result = AggregateRuleObjectDescribeModel.GetAggregateRuleObjectDescribeResult
                .builder()
                .build();
        List<String> aggregateObjectApiNames = getAggregateObjectApiNames(serviceContext.getTenantId());
        List<IObjectDescribe> describes = serviceFacade.findDescribeListWithoutFields(serviceContext.getTenantId(), aggregateObjectApiNames);
        if (CollectionUtils.notEmpty(describes)) {
            List<AggregateRuleObjectDescribe> aggregateValueObjects = describes.stream().map(
                    describe -> new AggregateRuleObjectDescribe(describe.get("display_name", String.class),
                            describe.get("api_name", String.class), null))
                    .collect(Collectors.toList());
            result.setAggregateValueObjects(aggregateValueObjects);
        }
        return result;
    }

    private List<String> getAggregateObjectApiNames(String tenantId) {
        List<String> aggregateObjectApiNames = Lists.newArrayList();
        IObjectDescribe aggregateRuleDescribe = serviceFacade.findObject(tenantId, Utils.AGGREGATE_RULE_API_NAME);
        IFieldDescribe fieldDescribe = aggregateRuleDescribe.getFieldDescribe(Field.AGGREGATE_OBJECT);
        List<Map> options = fieldDescribe.get(FIELD_OPTIONS, List.class);
        for (Map optionMap : options) {
            aggregateObjectApiNames.add(String.valueOf(optionMap.get(FIELD_VALUE)));
        }
        return aggregateObjectApiNames;
    }

    /**
     * 获取聚合规则对象字段包括引用字段
     *
     * @param serviceContext ServiceContext
     * @param arg            GetAggregateValueConditionArg
     * @return GetAggregateValueObjectDescribeResult
     */
    @ServiceMethod("getAggregateValueAllObjectDescribe")
    public AggregateRuleObjectDescribeModel.GetAggregateRuleObjectDescribeResult getAggregateValueCondition(ServiceContext serviceContext,
                                                                                                            AggregateRuleObjectDescribeModel.GetAggregateRuleConditionArg arg) {
        AggregateRuleObjectDescribeModel.GetAggregateRuleObjectDescribeResult result = AggregateRuleObjectDescribeModel.GetAggregateRuleObjectDescribeResult
                .builder().build();
        // 聚合对象字段
        IObjectDescribe describe = serviceFacade.findObject(serviceContext.getTenantId(), arg.getApiName());
        // 聚合对象引用字段
        List<IObjectDescribe> describes = describeLogicService.findAssociationDescribes(serviceContext.getTenantId(), describe);
        // 合并统一返回
        describes.add(describe);
        if (CollectionUtils.notEmpty(describes)) {
            List<AggregateRuleObjectDescribe> aggregateValueConditionObj = describes.stream().map(
                    conditionDescribe -> new AggregateRuleObjectDescribe(conditionDescribe.get("display_name", String.class),
                            conditionDescribe.get("api_name", String.class), conditionDescribe.get("fields", LinkedHashMap.class)))
                    .collect(Collectors.toList());
            result.setAggregateValueObjects(aggregateValueConditionObj);
        }
        return result;
    }

    /**
     * 聚合维度
     *
     * @param serviceContext ServiceContext
     * @param arg            GetAggregateValueConditionArg
     * @return GetAggregateDimensionResult
     */
    @ServiceMethod("getAggregateDimension")
    public AggregateRuleObjectDescribeModel.GetAggregateDimensionResult getAggregateDimensionResult(ServiceContext serviceContext,
                                                                                                    AggregateRuleObjectDescribeModel.GetAggregateRuleConditionArg arg) {
        AggregateRuleObjectDescribeModel.GetAggregateDimensionResult result = AggregateRuleObjectDescribeModel.GetAggregateDimensionResult
                .builder().build();
        boolean isPartnerEnabled = bizConfigThreadLocalCacheService.isPartnerEnabled(serviceContext.getTenantId());
        String describeApiName = arg.getApiName();
        if (StringUtils.isBlank(describeApiName)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        Optional<AggregateRuleObjectDescribeModel.MasterDetailData> masterDetailData = findMasterDetail(serviceContext, describeApiName);
        if (!masterDetailData.isPresent()) {
            return result;
        }
        result.setDimension(buildDimensionResult(masterDetailData.get(), isPartnerEnabled));
        return result;
    }

    private JSONArray buildDimensionResult(AggregateRuleObjectDescribeModel.MasterDetailData masterDetailData, boolean isPartnerEnabled) {
        List<String> targetApiNames = Lists.newArrayList(SFAPreDefineObject.Account.getApiName(),"LoyaltyMemberObj");
        if (isPartnerEnabled) {
            targetApiNames.add(SFAPreDefineObject.Partner.getApiName());
        }
        JSONArray fieldByTargetApiNames = getFieldByTargetApiNames(masterDetailData.getMasterDescribe(), targetApiNames);
        //非从对象获取聚合维度
        if (masterDetailData.getDetailDescribe() == null) {
            return fieldByTargetApiNames;
        } else {
            JSONObject detailJSONObject = new JSONObject();
            detailJSONObject.put(FIELD_CHILDREN, fieldByTargetApiNames);
            detailJSONObject.put(FIELD_LABEL, masterDetailData.getMasterDescribe().getDisplayName());
            detailJSONObject.put(KEY_MASTER_DESCRIBE_API_NAME, masterDetailData.getMasterDescribe().getApiName());
            detailJSONObject.put(FIELD_VALUE, masterDetailData.getMasterFieldDescribe().getApiName());
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(detailJSONObject);
            return jsonArray;
        }
    }

    private JSONArray getFieldByTargetApiNames(IObjectDescribe masterDescribe, List<String> targetApiNames) {
        JSONArray result = new JSONArray();
        if (CollectionUtils.empty(targetApiNames) || masterDescribe == null) {
            return result;
        }
        Map<String, JSONArray> apiNameToFieldMap = Maps.newHashMap();
        Optional.of(masterDescribe)
                .map(IObjectDescribe::getFieldDescribes)
                .filter(CollectionUtils::notEmpty)
                .ifPresent(fieldDescribeList -> {
                    for (IFieldDescribe fieldDescribe : fieldDescribeList) {
                        if (IFieldType.OBJECT_REFERENCE.equals(fieldDescribe.getType())) {
                            ObjectReferenceFieldDescribe objectReferenceFieldDescribe = (ObjectReferenceFieldDescribe) fieldDescribe;
                          if (targetApiNames.contains(objectReferenceFieldDescribe.getTargetApiName())) {
                                    apiNameToFieldMap.computeIfAbsent(objectReferenceFieldDescribe.getTargetApiName(), k -> new JSONArray())
                                            .add(getChildJSONObject(objectReferenceFieldDescribe));
                            }
                        }
                    }
                });
        for (String targetApiName : targetApiNames) {
            JSONArray jsonArray = apiNameToFieldMap.get(targetApiName);
            if (jsonArray == null) {
                continue;
            }
            result.addAll(jsonArray);
        }
        return result;
    }

    private Object getChildJSONObject(ObjectReferenceFieldDescribe objectReferenceFieldDescribe) {
        String apiName = objectReferenceFieldDescribe.getApiName();
        String label = objectReferenceFieldDescribe.getLabel();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(FIELD_VALUE, apiName);
        jsonObject.put(FIELD_LABEL, label);
        return jsonObject;
    }

    private AggregateRuleObjectDescribeModel.MasterDetailData createMasterDetailData(IObjectDescribe masterDescribe, IObjectDescribe detailDescribe) {
        return AggregateRuleObjectDescribeModel.MasterDetailData.builder()
                .masterDescribe(masterDescribe)
                .detailDescribe(detailDescribe)
                .build();
    }

    private Optional<AggregateRuleObjectDescribeModel.MasterDetailData> findMasterDetail(ServiceContext serviceContext, String describeApiName) {
        IObjectDescribe describe = serviceFacade.findObject(serviceContext.getTenantId(), describeApiName);
        if (describe == null) {
            return Optional.empty();
        }
        Optional<IFieldDescribe> masterDetailFieldDescribeOpt = DescribeUtils.getMasterDetailFieldDescribe(describe);
        if (!masterDetailFieldDescribeOpt.isPresent()) {
            return Optional.of(createMasterDetailData(describe, null));
        } else {
            MasterDetailFieldDescribe masterDetailFieldDescribe = (MasterDetailFieldDescribe) masterDetailFieldDescribeOpt.get();
            IObjectDescribe masterDescribe = serviceFacade.findObject(serviceContext.getTenantId(), masterDetailFieldDescribe.getTargetApiName());
            AggregateRuleObjectDescribeModel.MasterDetailData masterDetailData = createMasterDetailData(masterDescribe, describe);
            masterDetailData.setMasterFieldDescribe(masterDetailFieldDescribe);
            return Optional.of(masterDetailData);
        }
    }

    /**
     * 显示需要特殊处理的列
     *
     * @param user            用户信息
     * @param aggregateValues 聚合值
     */
    public void specialTreatmentData(User user, List<IObjectData> aggregateValues, Boolean conditionFlag) {
        // 加载聚合对象
        StopWatch stopWatch = StopWatch.create("specialTreatmentData");
        Map<String, IObjectDescribe> describeMaps = loadAggregateObjectDescribe(user);
        stopWatch.lap("load aggregateObjectDescribe");
        for (IObjectData aggregateValue : aggregateValues) {
            IObjectDescribe iObjectDescribe = describeMaps.get(aggregateValue.get(Field.AGGREGATE_OBJECT, String.class));
            Optional.ofNullable(iObjectDescribe).ifPresent(describe -> {
                // 特殊处理字段处理预存真实值
                aggregateValue.set(Field.AGGREGATE_FIELD_REAL, aggregateValue.get(Field.AGGREGATE_FIELD));
                aggregateValue.set(Field.DATE_FIELD_REAL, aggregateValue.get(Field.DATE_FIELD));
                aggregateValue.set(Field.DIMENSION_REAL, aggregateValue.get(Field.DIMENSION));
                aggregateValue.set(Field.DATE_RANGE_REAL, aggregateValue.get(Field.DATE_RANGE));
                // 聚合字段 字段/引用字段.字段 显示特殊处理
                aggregateValue.set(Field.AGGREGATE_FIELD, quoteDescribeFieldHandle(describe, describeMaps, aggregateValue.get(Field.AGGREGATE_FIELD, String.class), true));
                // 聚合日期字段 日期字段/引用字段.日期字段 显示特殊处理
                aggregateValue.set(Field.DATE_FIELD, quoteDescribeFieldHandle(describe, describeMaps, aggregateValue.get(Field.DATE_FIELD, String.class), true));
                // 聚合维度
                aggregateValue.set(Field.DIMENSION, quoteDescribeFieldHandle(describe, describeMaps, aggregateValue.get(Field.DIMENSION, String.class), false));
                if (conditionFlag) {
                    // 条件
                    aggregateValue.set(Field.CONDITION, resolveCondition(user, describe, describeMaps, aggregateValue.get(Field.CONDITION, String.class)));

                }
                // 时间范围
                aggregateValue.set(Field.DATE_RANGE, resolveDateRange(aggregateValue.get(Field.DATE_RANGE, Map.class)));
            });
        }
        stopWatch.lap("handleAggregateRuleField");
        stopWatch.logSlow(10000);
    }

    /**
     * 处理时间范围
     *
     * @param dateRangeMap 时间范围
     * @return dateRange 显示
     */
    public String resolveDateRange(Map<String, Object> dateRangeMap) {
        if (CollectionUtils.empty(dateRangeMap)) {
            return null;
        }
        String dateRange = null;
        if (dateRangeMap.containsValue("custom")) {
            JSONArray array = JSON.parseArray(JSON.toJSONString(dateRangeMap.get(FIELD_VALUE)));
            Date startTime = array.getDate(0);
            Date endTime = array.getDate(1);
            dateRange = DateFormatUtils.format(startTime, "yyyy-MM-dd") + " - " + DateFormatUtils.format(endTime, "yyyy-MM-dd");
        } else {
            for (DateRangeEnum value : DateRangeEnum.values()) {
                if (value.getValue().equals(dateRangeMap.get("type"))) {
                    dateRange = I18N.text("sfa.aggregate.rule.date.range."+value.getValue());
                    break;
                }
            }
        }
        return dateRange;
    }

    /**
     * 加载聚合规则所有对象包括引用对象
     *
     * @param user 用户信息
     * @return 聚合对象maps
     */
    public Map<String, IObjectDescribe> loadAggregateObjectDescribe(User user) {
        // 加载聚合对象
        List<String> aggregateApiNames = getAggregateObjectApiNames(user.getTenantId());
        aggregateApiNames.add(Utils.AGGREGATE_RULE_API_NAME);
        Map<String, IObjectDescribe> describeMaps = serviceFacade.findObjects(user.getTenantId(), aggregateApiNames);
        List<IObjectDescribe> objectDescribeList = Lists.newArrayList(describeMaps.values());
        // 加载聚合对象的引用对象
        List<IObjectDescribe> quoteDescribeList = serviceFacade.findAssociationDescribes(user.getTenantId(), objectDescribeList);
        describeMaps.putAll(quoteDescribeList.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, quoteDescribe -> quoteDescribe)));
        return describeMaps;
    }

    /**
     * 聚合规则字段处理
     *
     * @param describe            当前对象
     * @param describeMaps        引用对象
     * @param field               字段
     * @param isTargetDisplayName 是否拼接引用字段
     * @return 显示字段
     */
    private String quoteDescribeFieldHandle(IObjectDescribe describe, Map<String, IObjectDescribe> describeMaps, String field,
                                            Boolean isTargetDisplayName) {
        if (StringUtils.isEmpty(field)) {
            return null;
        }
        String[] fieldArray = field.split("\\.");
        if (fieldArray.length > 1) {
            String targetApiName = Optional.ofNullable(describe.getFieldDescribe(fieldArray[0])).map(fieldDescribe -> fieldDescribe.get(FIELD_TARGET_API_NAME, String.class)).orElse(field);
            if (isTargetDisplayName) {
                return Optional.ofNullable(describeMaps.get(targetApiName))
                        .map(targetDescribe -> String.join(".", targetDescribe.getDisplayName(), targetDescribe.getFieldDescribe(fieldArray[1]).getLabel()))
                        .orElse(field);
            } else {
                return Optional.ofNullable(describeMaps.get(targetApiName))
                        .map(targetDescribe -> targetDescribe.getFieldDescribe(fieldArray[1]).getLabel())
                        .orElse(field);
            }
        } else {
            return Optional.ofNullable(describe.getFieldDescribe(field)).map(IFieldDescribe::getLabel).orElse(field);
        }
    }

    /**
     * 处理聚合条件
     *
     * @param describe      聚合对象描述
     * @param describeMaps  聚合对象描述包括引用对象
     * @param useRangeValue 需要处理条件
     * @return 处理后的条件
     */
    private String resolveCondition(User user, IObjectDescribe describe, Map<String, IObjectDescribe> describeMaps, String useRangeValue) {
        if (StringUtils.isEmpty(useRangeValue)) {
            return null;
        }
        List<FilterGroup> filterGroups = JSONArray.parseArray(useRangeValue, FilterGroup.class);
        for (FilterGroup filterGroup : filterGroups) {
            filterGroup.getFilters().forEach(x -> {
                JSONObject jsonFilter = (JSONObject) x;
                IFilter filter = JSONObject.parseObject(jsonFilter.toJSONString(), IFilter.class);
                String[] fieldDescribeArray = filter.getFieldName().split("\\.");
                String fieldName = null;
                IFieldDescribe fieldDescribe = null;
                if (fieldDescribeArray.length > 1) {
                    String targetApiName = describe.getFieldDescribe(fieldDescribeArray[0]).get(FIELD_TARGET_API_NAME, String.class);
                    if (StringUtils.isNotEmpty(targetApiName)) {
                        IObjectDescribe targetDescribe = describeMaps.get(targetApiName);
                        fieldName = String.join(".", targetDescribe.getDisplayName(), targetDescribe.getFieldDescribe(fieldDescribeArray[1]).getLabel());
                        fieldDescribe = targetDescribe.getFieldDescribe(fieldDescribeArray[1]);
                    }
                } else {
                    fieldDescribe = describe.getFieldDescribe(fieldDescribeArray[0]);
                    fieldName = fieldDescribe != null ? describe.getFieldDescribe(fieldDescribeArray[0]).getLabel() : null;
                }
                jsonFilter.put("field_name__s", fieldName);
                jsonFilter.put("field_values__s", fieldDescribe == null ? "--" : convertFieldValue(user, filter.getFieldValues(), describe, fieldDescribe));
            });
        }
        return JSON.toJSONString(filterGroups);
    }

    /**
     * 处理当单操作符
     *
     * @param useRangeValue 条件
     * @param type          0更新 1为查询
     * @return filterGroups
     * @deprecated 由底层元数据转换
     */
    public String conditionOperator(String useRangeValue, Integer type) {
        if (StringUtils.isEmpty(useRangeValue)) {
            return null;
        }
        List<FilterGroup> filterGroups = JSONArray.parseArray(useRangeValue, FilterGroup.class);
        for (FilterGroup filterGroup : filterGroups) {
            filterGroup.getFilters().forEach(x -> {
                JSONObject jsonFilter = (JSONObject) x;
                IFilter filter = JSONObject.parseObject(jsonFilter.toJSONString(), IFilter.class);
                String operator = filter.getOperator().toString();
                if (type.equals(0)) {
                    jsonFilter.put(FIELD_OPERATOR, OperateMappingUtils.operateMap.getOrDefault(operator, operator));
                } else {
                    BiMap<String, String> biMap = HashBiMap.create(OperateMappingUtils.operateMap);
                    jsonFilter.put(FIELD_OPERATOR, biMap.inverse().get(operator) != null ? biMap.inverse().get(operator) : operator);
                }
            });
        }
        return JSON.toJSONString(filterGroups);
    }

    /**
     * 处理条件值显示
     *
     * @param user          用户信息
     * @param fieldValues   字段
     * @param describe      描述
     * @param fieldDescribe 字段描述
     * @return 字段显示
     */
    private String convertFieldValue(User user, List<String> fieldValues, IObjectDescribe describe, IFieldDescribe fieldDescribe) {
        if (CollectionUtils.empty(fieldValues)) {
            return "";
        }
        if (fieldDescribe.getType().equals("quote")) {
            // "quote_field": "product_id__r.life_status"
            String quoteField = fieldDescribe.getQuoteField();
            String[] quoteFieldArray = quoteField.split("\\.");
            if (quoteFieldArray.length > 1) {
                String quoteFieldStr = quoteFieldArray[0].replace("__r", "");
                IObjectDescribe targetDescribe = serviceFacade.findObject(user.getTenantId(), describe.getFieldDescribe(quoteFieldStr).get(FIELD_TARGET_API_NAME, String.class));
                fieldDescribe = targetDescribe.getFieldDescribe(quoteFieldArray[1]);
            }
        }
        AbstractFieldValueConverter fieldValueConverter = FieldValueConvertFactory.getConverter(fieldDescribe);
        return fieldValueConverter.convert(
                FieldValueConvertContext.builder()
                        .user(user)
                        .fieldDescribe(fieldDescribe)
                        .fieldValues((List<Object>) (List) fieldValues)
                        .build());
    }

    /**
     * 获取时间范围值的月数
     *
     * @return 月数
     */
    public int getRange() {
        return range;
    }

    /**
     * 读取聚合维度模版
     *
     * @param fieldName 文件名称
     * @return 聚合模版
     */
    private static String geAggregateDimensionTemplate(String fieldName) {
        ClassLoader classLoader = AggregateRuleService.class.getClassLoader();
        String jsonStr;
        try {
            jsonStr = IOUtils.toString(classLoader.getResource("aggregatejson/" + fieldName + "_field.json"), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("initProductFieldJSON file parse error");
            throw new MetadataValidateException(ErrorCode.FS_PAAS_MDS_FILE_PARSE_EXCEPTION, "initProductFieldJSON file parse error", e);
        }
        return jsonStr;
    }

    /**
     * 处理聚合对象下拉多语
     *
     * @param user                    用户信息
     * @param aggregateObjectDescribe 聚合对象
     */
    public void buildAggregateObjectDescribe(User user, IObjectDescribe aggregateObjectDescribe) {
        // 加载聚合对象
        Map<String, IObjectDescribe> describeMaps = serviceFacade.findObjects(user.getTenantId(), getAggregateObjectApiNames(user.getTenantId()));
        IFieldDescribe fieldDescribe = aggregateObjectDescribe.getFieldDescribe(Field.AGGREGATE_OBJECT);
        List<Object> options = fieldDescribe.get(FIELD_OPTIONS, List.class);
        for (Object o : options) {
            Map<String, Object> optionMap = (Map<String, Object>) o;
            IObjectDescribe describe = describeMaps.get(optionMap.get(FIELD_VALUE));
            optionMap.put(FIELD_LABEL, describe.getDisplayName());
        }
        fieldDescribe.set(FIELD_OPTIONS, options);
    }

    public void buildAggregateObjectDescribe(User user, IFieldDescribe fieldDescribe) {
        // 加载聚合对象
        Map<String, IObjectDescribe> describeMaps = serviceFacade.findObjects(user.getTenantId(), getAggregateObjectApiNames(user.getTenantId()));
        List<Map> options = fieldDescribe.get(FIELD_OPTIONS, List.class);
        for (Map optionMap : options) {
            IObjectDescribe describe = describeMaps.get(optionMap.get(FIELD_VALUE));
            optionMap.put(FIELD_LABEL, null != describe ? describe.getDisplayName() : null);
        }
    }

    /**
     * @param user                用户信息
     * @param describeApiName     聚合对象apiName
     * @param field               聚合字段
     * @param isTargetDisplayName 聚合字段为引用字段时候是否显示引用对象
     * @return 聚合字段
     */
    public String resolveField(User user, String describeApiName, String field, Boolean isTargetDisplayName) {
        if (StringUtils.isEmpty(field)) {
            return null;
        }
        // 加载聚合对象
        Map<String, IObjectDescribe> describeMaps = loadAggregateObjectDescribe(user);
        return quoteDescribeFieldHandle(describeMaps.get(describeApiName), describeMaps, field, isTargetDisplayName);
    }

    /**
     * 替换条件字段
     *
     * @param user            用户信息
     * @param describeApiName 聚合对象
     * @param useRangeValue   条件
     * @return 条件
     */
    public String resolveCondition(User user, String describeApiName, String useRangeValue) {
        if (StringUtils.isEmpty(useRangeValue)) {
            return null;
        }
        // 加载聚合对象
        Map<String, IObjectDescribe> describeMaps = loadAggregateObjectDescribe(user);
        return resolveCondition(user, describeMaps.get(describeApiName), describeMaps, useRangeValue);
    }

    /**
     * 保存聚合规则关联对象关系
     *
     * @param tenantId        企业id
     * @param aggregateRuleId 聚合规则id
     * @param targetValues    聚合字段关联字段名称
     */
    public void createReferenceRelation(String tenantId, String aggregateRuleId, List<String> targetValues) {
        List<EntityReferenceArg> referenceArgList = Lists.newArrayList();
        for (String targetValue : targetValues) {
            referenceArgList.add(EntityReferenceArg.builder()
                    .tenantId(tenantId)
                    .sourceType(AggregateRuleConstants.ENTITY_SOURCE_TYPE)
                    .sourceLabel(AggregateRuleConstants.ENTITY_SOURCE_LABEL)
                    .sourceValue(aggregateRuleId)
                    .targetType(EntityReferenceService.DESCRIBE_FIELD_TYPE)
                    .targetValue(targetValue)
                    .build());
        }
        if (CollectionUtils.notEmpty(referenceArgList)) {
            entityReferenceService.create(referenceArgList);
        }
    }

    /**
     * 修改聚合规则关联对象关系依赖
     *
     * @param tenantId        企业id
     * @param aggregateRuleId 聚合规则id
     * @param targetValues    聚合字段关联字段名称
     */
    public void updateReferenceRelation(String tenantId, String aggregateRuleId, List<String> targetValues) {
        entityReferenceService.delete(tenantId, AggregateRuleConstants.ENTITY_SOURCE_TYPE, aggregateRuleId);
        createReferenceRelation(tenantId, aggregateRuleId, targetValues);
    }

    /**
     * 删除聚合规则删除作废关系
     *
     * @param tenantId     企业id
     * @param sourceIdList 删除的聚合规则id
     */
    public void deleteReferenceRelation(String tenantId, List<String> sourceIdList, String sourceType) {
        if (CollectionUtils.empty(sourceIdList)) {
            return;
        }
        List<DeleteArg> deleteArgList = Lists.newArrayList();
        for (String id : sourceIdList) {
            DeleteArg.DeleteArgBuilder deleteArgBuilder = DeleteArg.builder();
            deleteArgBuilder.sourceType(sourceType);
            deleteArgBuilder.sourceValue(id);
            deleteArgList.add(deleteArgBuilder.build());
        }
        entityReferenceService.delete(tenantId, deleteArgList);
    }

    public void deleteAggDataAndRelation(String tenantId, List<String> targetIdList) {
        if (CollectionUtils.empty(targetIdList)) {
            return;
        }
        //查询关联引用优惠券范围聚合规则
        List<EntityReferenceArg> queryEntityReference = entityReferenceService.findByTargetTypeAndTargetValues(tenantId, EntityReferenceService.COUPON_PLAN_RELATION, targetIdList, targetIdList.size());
        if (CollectionUtils.empty(queryEntityReference)) {
            return;
        }
        //获取sourceId
        List<String> sourceIdList = queryEntityReference.stream().map(EntityReferenceArg::getSourceValue).collect(Collectors.toList());
        if (CollectionUtils.empty(sourceIdList)) {
            return;
        }
        //删除聚合规则
        serviceFacade.bulkDeleteDirect(buildDeleteData(sourceIdList, tenantId), User.systemUser(tenantId));
        //删除关联关系
        deleteReferenceRelation(tenantId, sourceIdList, AggregateRuleConstants.ENTITY_COUPON_SOURCE_TYPE);
    }

    private List<IObjectData> buildDeleteData(List<String> sourceIdList, String tenantId) {
        List<IObjectData> objectDataList = Lists.newArrayList();
        for (String id : sourceIdList) {
            ObjectData objectData = new ObjectData();
            objectData.setId(id);
            objectData.setTenantId(tenantId);
            objectData.setDescribeApiName(SFAPreDefineObject.AggregateRule.getApiName());
            objectDataList.add(objectData);
        }
        return objectDataList;
    }

    /**
     * 获取关联关系字段
     *
     * @param user               用户信息
     * @param objectDataDocument 聚合规则信息
     * @return
     */
    public List<String> getAggregateRuleReferenceTargetValues(User user, ObjectDataDocument objectDataDocument) {
        List<String> targetValues = Lists.newArrayList();

        // 加载聚合对象
        Map<String, IObjectDescribe> describeMaps = loadAggregateObjectDescribe(user);
        // 聚合对象
        String aggregateObject = objectDataDocument.get(Field.AGGREGATE_OBJECT).toString();
        // 聚合字段
        String aggregateField = objectDataDocument.get(Field.AGGREGATE_FIELD).toString();
        targetValues.add(String.join(".", aggregateObject, aggregateField));
        // 聚合维度
        String dimension = Optional.ofNullable(objectDataDocument.get(Field.DIMENSION)).map(v -> v.toString()).orElse(null);
        if (StringUtils.isNotBlank(dimension)) {
            targetValues.add(referenceFieldHandle(aggregateObject, dimension, describeMaps));
        }
        // 聚合日期字段
        String dateField = Optional.ofNullable(objectDataDocument.get(Field.DATE_FIELD)).map(v -> v.toString()).orElse(null);
        if (StringUtils.isNotEmpty(dateField)) {
            targetValues.add(String.join(".", aggregateObject, dateField));
        }
        String condition = Optional.ofNullable(objectDataDocument.get(Field.CONDITION)).map(v -> v.toString()).orElse(null);
        if (StringUtils.isNotEmpty(condition)) {
            targetValues.add(referenceConditionHandle(condition, aggregateObject, describeMaps));
        }
        // 聚合条件
        return targetValues;
    }

    /**
     * 处理关联字段
     *
     * @param aggregateObject 聚合对象
     * @param field           聚合字段
     * @return 对象apiName.字段apiName
     */
    private String referenceFieldHandle(String aggregateObject, String field, Map<String, IObjectDescribe> describeMaps) {
        if (StringUtils.isEmpty(field)) {
            return null;
        }
        String[] fieldArray = field.split("\\.");
        field = handleField(fieldArray, aggregateObject, describeMaps);
        return field;
    }

    /**
     * 处理聚合条件里的关联关系
     *
     * @param condition       条件
     * @param aggregateObject 聚合对象
     * @return 对象apiName.字段apiName
     */
    private String referenceConditionHandle(String condition, String aggregateObject, Map<String, IObjectDescribe> describeMaps) {
        String field = null;
        List<FilterGroup> filterGroups = JSONArray.parseArray(condition, FilterGroup.class);
        for (FilterGroup filterGroup : filterGroups) {
            for (Object x : filterGroup.getFilters()) {
                JSONObject jsonFilter = (JSONObject) x;
                String[] fieldDescribeArray = jsonFilter.getString("field_name").split("\\.");
                field = handleField(fieldDescribeArray, aggregateObject, describeMaps);
            }
        }
        return field;
    }

    private String handleField(String[] fieldDescribeArray, String aggregateObject, Map<String, IObjectDescribe> describeMaps) {
        String field = null;
        if (fieldDescribeArray.length > 1) {
            IObjectDescribe describe = describeMaps.get(aggregateObject);
            String targetApiName = Optional.ofNullable(describe.getFieldDescribe(fieldDescribeArray[0])).map(fieldDescribe -> fieldDescribe.get(FIELD_TARGET_API_NAME, String.class)).orElse(field);
            field = String.join(".", targetApiName, fieldDescribeArray[1]);
        } else {
            field = String.join(".", aggregateObject, fieldDescribeArray[0]);
        }
        return field;
    }


    public void createDataAndRelation(User user, IObjectData copyDataResetAggFieldData, String aggregateRuleId, ObjectAction action) {
        User superAdmin = User.systemUser(user.getTenantId());
        //1、创建聚合值
        IObjectData saveCopyDataResetAggFieldData = serviceFacade.saveObjectData(superAdmin, copyDataResetAggFieldData);
        if (ObjectAction.UPDATE == action) {
            //1、先查询关联关系是否有值
            List<EntityReferenceArg> queryEntityReference = entityReferenceService.findByTargetTypeAndTargetValue(user.getTenantId(), EntityReferenceService.COUPON_PLAN_RELATION, aggregateRuleId);
            //2、如果有值，先删除聚合规则关联关系
            if (CollectionUtils.notEmpty(queryEntityReference)) {
                //分别获取targetValue和sourceValue
                List<String> sourceValues = queryEntityReference.stream().map(EntityReferenceArg::getSourceValue).collect(Collectors.toList());
                deleteReferenceRelation(user.getTenantId(), Lists.newArrayList(sourceValues), AggregateRuleConstants.ENTITY_COUPON_SOURCE_TYPE);
                //删除旧的聚合值
                Optional.ofNullable(serviceFacade.findObjectDataByIds(user.getTenantId(), sourceValues, SFAPreDefineObject.AggregateRule.getApiName()))
                        .ifPresent(oldObjectData -> serviceFacade.bulkDeleteDirect(oldObjectData, superAdmin));
            }
        }
        EntityReferenceArg entityReferenceArg = EntityReferenceArg.builder()
                .tenantId(user.getTenantId())
                .sourceType(AggregateRuleConstants.ENTITY_COUPON_SOURCE_TYPE)
                .sourceLabel(AggregateRuleConstants.ENTITY_COUPON_SOURCE_LABEL)
                .sourceValue(saveCopyDataResetAggFieldData.getId())
                .targetType(EntityReferenceService.COUPON_PLAN_RELATION)
                .targetValue(aggregateRuleId)
                .build();
        entityReferenceService.create(entityReferenceArg);
    }

    @ServiceMethod("findPluginMapping")
    public FindPluginMappingField.Result findPluginMapping(ServiceContext serviceContext, FindPluginMappingField.Arg arg) {
        validateApiNamesAndPluginName(arg);

        String masterApiName = StringUtils.isBlank(arg.getMasterApiName())
                ? AggregateRuleUtil.getMasterApiNameByDetailApiName(serviceContext.getUser(), arg.getDetailApiName())
                : arg.getMasterApiName();

        DomainPluginParam pluginParam = infraServiceFacade.findPluginParam(serviceContext.getTenantId(), masterApiName, arg.getPluginName());
        FindPluginMappingField.Result result = FindPluginMappingField.Result.builder().build();
        if (pluginParam == null) {
            Map<String, String> emptyMap = Maps.newHashMap();
            Map<String, String> masterMapping = buildFieldMapping(arg.getMasterMapping(), emptyMap::get);
            result.setMasterMapping(masterMapping);
            Map<String, String> detailMapping = buildFieldMapping(arg.getDetailMapping(), emptyMap::get);
            result.setDetailMapping(detailMapping);
            return result;
        }

        DomainPluginDescribeExt ext = DomainPluginDescribeExt.of(masterApiName, pluginParam);
        if (CollectionUtils.notEmpty(arg.getMasterMapping())) {
            Map<String, String> masterMapping = buildFieldMapping(arg.getMasterMapping(), ext::getFieldApiName);
            result.setMasterMapping(masterMapping);
        }
        if (CollectionUtils.notEmpty(arg.getDetailMapping())) {
            Map<String, String> detailMapping = buildFieldMapping(arg.getDetailMapping(), ext::getDefaultDetailFieldApiName);
            result.setDetailMapping(detailMapping);
        }
        return result;
    }

    private void validateApiNamesAndPluginName(FindPluginMappingField.Arg arg) {
        if (StringUtils.isBlank(arg.getMasterApiName()) && StringUtils.isBlank(arg.getDetailApiName()) || StringUtils.isBlank(arg.getPluginName())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONTACT_PARAM_EXCEPTION));
        }
    }

    private Map<String, String> buildFieldMapping(List<String> fieldApiNames, UnaryOperator<String> mappingFunction) {
        Map<String, String> fieldMapping = Maps.newHashMap();
        if (CollectionUtils.empty(fieldApiNames)) {
            return fieldMapping;
        }
        for (String fieldApiName : fieldApiNames) {
            fieldMapping.put(fieldApiName, Optional.ofNullable(mappingFunction.apply(fieldApiName)).orElse(""));
        }
        return fieldMapping;
    }

    /**
     * 获取聚合规则对象显示信息
     *
     * @param serviceContext ServiceContext
     * @param arg            AggregateRuleId
     * @return AggregateRuleData
     */
    @ServiceMethod("getShowAggregateRule")
    public AggregateRuleObjectDescribeModel.AggregateRuleData getShowAggregateRule(ServiceContext serviceContext,
                                                                                                            AggregateRuleObjectDescribeModel.AggregateRuleId arg) {
        AggregateRuleObjectDescribeModel.AggregateRuleData aggregateRuleData = new AggregateRuleObjectDescribeModel.AggregateRuleData();

        List<IObjectData> aggregateRuleDataList =serviceFacade.findObjectDataByIds(serviceContext.getUser().getTenantId(), arg.getRuleIds(), SFAPreDefineObject.AggregateRule.getApiName());
        if(CollectionUtils.notEmpty(aggregateRuleDataList)) {
            specialTreatmentData(serviceContext.getUser(), aggregateRuleDataList, true);
            aggregateRuleData.setRules(ObjectDataDocument.ofList(aggregateRuleDataList));
        }

        return  aggregateRuleData;
    }
}
