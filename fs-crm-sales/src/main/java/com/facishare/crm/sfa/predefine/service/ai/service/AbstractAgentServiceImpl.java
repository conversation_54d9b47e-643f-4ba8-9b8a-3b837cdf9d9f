package com.facishare.crm.sfa.predefine.service.ai.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.api.dto.BaseArgument;
import com.facishare.ai.api.dto.OpenAIChatComplete;
import com.facishare.ai.api.model.Message;
import com.facishare.ai.api.model.service.FsAI;
import com.facishare.crm.sfa.lto.industry.CompanyModel;
import com.facishare.crm.sfa.lto.utils.HttpHeaderUtil;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.SFAKnowledgeService;
import com.facishare.crm.sfa.predefine.service.ai.Util.I18NAIUtil;
import com.facishare.crm.sfa.predefine.service.ai.enums.ActionTargetEnum;
import com.facishare.crm.sfa.predefine.service.ai.enums.OpTypeEnum;
import com.facishare.crm.sfa.predefine.service.ai.model.Agent;
import com.facishare.crm.sfa.predefine.service.ai.model.ButtonAction;
import com.facishare.crm.sfa.predefine.service.ai.service.marketing.CSSessionDataService;
import com.facishare.crm.sfa.predefine.service.bizquery.BizQuerySearchService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.proxy.AiRestProxy;
import com.facishare.crm.sfa.utilities.proxy.FSUserMarketingActionProxy;
import com.facishare.crm.sfa.utilities.proxy.model.AiRestProxyModel;
import com.facishare.eservice.rest.online.model.SearchSceneKnowledgeModel;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/4/11 15:53
 * @description:
 * @IgnoreI18n or IgnoreI18nFile or @IgnoreI18nFile
 */
@Slf4j
public abstract class AbstractAgentServiceImpl implements AgentService {


    @Autowired
    @Qualifier("objectDataPgService")
    protected IObjectDataService objectDataService;

    @Autowired
    private IObjectDescribeService objectDescribeService;

    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    protected CSSessionDataService csSessionDataService;
    @Autowired
    protected PromptService promptService;

    @Autowired
    private SFAKnowledgeService sfaKnowledgeService;

    @Autowired
    BizQuerySearchService bizQuerySearchService;

    @Resource
    private FSUserMarketingActionProxy fsUserMarketingActionProxy;

    @Autowired
    private AiRestProxy aiRestProxy;


    public String model = "gpt-3.5-turbo";

    /*
        section1: xxxx
        section2: xxxx
 */
    public Map<String, String> promptTemplate = new HashMap<>();

    public boolean currentLangIsZHCN = true;

    protected static String promptTemplateTenantId = "1";

    static {
        ConfigFactory.getConfig("fs-gray-sfa", config -> {
            promptTemplateTenantId = config.get("prompt_template_tenant_id", "1");
        });
    }

    public static String JSONFixPrompt = "You are a helpful assistant to fix an invalid JSON response. You need to fix the invalid JSON response to be valid JSON. You must respond in JSON only with no other fluff or bad things will happen. Do not return the JSON inside a code block.";


    @Override
    public String getLangPrompt() {
        return "必须用%s输出内容！必须用%s输出内容！";
    }

    @Override
    public String getTips() {
        return "";
    }

    @Override
    public String getModel() {
        return model;
    }

    public void before(ServiceContext context) {
//        List<IObjectData> promptTemplate1 = getPromptTemplate(context.getUser());
//        for (IObjectData objectData : promptTemplate1) {
//            if (objectData.get("section") == null) {
//                if (objectData.containsField("prompt") && ObjectUtils.isNotEmpty(objectData.get("prompt"))) {
//                    promptTemplate.put(getApiName(), objectData.get("prompt").toString());
//                }
//            } else {
//                promptTemplate.put(objectData.get("section").toString(), objectData.get("prompt").toString());
//            }
//            if (model != null && objectData.get("model") != null) {
//                model = objectData.get("model").toString();
//            }
//        }
        currentLangIsZHCN = handleLangIsZHCN(context);
    }

    @Override
    public List<ButtonAction> getButtonAction(Agent.Arg arg) {
        return Collections.emptyList();
    }

    public ButtonAction buildButtonAction(ActionTargetEnum actionTargetEnum) {
        return ButtonAction.builder()
                .apiName(actionTargetEnum.getCode())
                .describe(actionTargetEnum.getDesc())
                .name(I18N.text(actionTargetEnum.getI18nKey()))
                .actionApiName(getApiName())
                .actionLabel(I18N.text(actionTargetEnum.getI18nKey()))
                .target(actionTargetEnum.getCode())
                .build();
    }


    @Override
    public Agent.Result getObjectData(ServiceContext context, Agent.Arg arg) {
        if (StringUtils.isBlank(arg.getOpType())) {
            arg.setOpType(OpTypeEnum.REFRESH.getCode());
        }
        before(context);
        return Agent.Result.builder()
                .errCode(0)
                .tips(getTips())
                .actionApiName(getApiName())
                .actionList(getButtonAction(arg))
                .action(getActionMap("sfa_text"))
                .updateTime(System.currentTimeMillis())
                .build();
    }

    @Override
    public Object getAIRet(ServiceContext context, String content) {
        return chatComplete(context, content);
    }

    @Override
    public Object getAIRet(ServiceContext context,String corpus,String promptApiName,String langPrompt) {
        Map<String, Object> sceneParamMap = new HashMap<>();
        if(ObjectUtils.isEmpty(langPrompt)){
            corpus = corpus + getLangPrompt(context);
        }else{
            corpus = corpus + getLangPrompt(langPrompt);
        }
        sceneParamMap.put("corpus",corpus);
        if(ObjectUtils.isEmpty(promptApiName)){
            //空白语料
            promptApiName = "prompt_sfa_customized_corpus";
        }
        return getAiComplete(context.getUser(), promptApiName,sceneParamMap,null);
    }
    public Object getAIRet(ServiceContext context,String corpus,String promptApiName) {
        return getAIRet(context,corpus,promptApiName,null);
    }

    protected List<ObjectDataDocument> getDataList(String jsonData) {
        return JSON.parseArray(jsonData, ObjectDataDocument.class);
    }

    protected List<ObjectDataDocument> getDataListFixedInvalidJSON(ServiceContext context, String fixedFormat, String jsonData) {
        List<ObjectDataDocument> dataDocuments;
        jsonData = jsonData.replaceAll("```json", "")
                .replaceAll("```", "")
                .replace("***", "");

        if(jsonData.contains("[") && jsonData.contains("]")){
            int start = jsonData.indexOf("[");
            int end   = jsonData.indexOf("]")+1;
            jsonData = jsonData.substring(start,end);
            jsonData = jsonData.trim().replaceAll("\\\\\\\\n","").replaceAll("\\\\n","").replaceAll("\\\\","");
        }else if(jsonData.contains("{") && jsonData.contains("}")){
            jsonData = jsonData.replaceAll("(^\")|(\"$)", "").replaceAll("\\\\\\\\n","").replaceAll("\\\\n","").replaceAll("\\\\","");
            jsonData = "["+jsonData+"]";
        }

        try {
            dataDocuments = JSON.parseArray(jsonData, ObjectDataDocument.class);
        } catch (JSONException e) {
            String jsonFixed = chatCompleteNoPrompt(context, JSONFixPrompt, fixedFormat + "\n\n这是有问题的json：" + jsonData);
            dataDocuments = JSON.parseArray(jsonFixed, ObjectDataDocument.class);
        }
        return dataDocuments;
    }


    public Map<String, Object> getActionMap() {
        return getActionMap(null);
    }

    /**
     * gpt助手弹框里面需要此参数，来让前端知道解析文本，还是数据列表。
     * 详情页组件可忽略。
     * action 没什么具体的含义，只是深研要求 target 必填， 通过target去映射 contentType
     * target = sfa_text ,  contentType = 100 解析纯文本
     * target = sfa_data_list , contentType = 101 解析dataList
     *
     * @return
     */
    public Map<String, Object> getActionMap(String target) {
        Map<String, Object> actionMap = new HashMap<>();
        actionMap.put("name", "新建");
        actionMap.put("apiName", "agent_add_button");
        actionMap.put("describe", "对象新建");
        if (StringUtils.isBlank(target)) {
            actionMap.put("target", "sfa_text");
        } else {
            actionMap.put("target", target);
        }
        return actionMap;
    }

    public IObjectData findById(ServiceContext actionContext, String objectId, String apiName) {
        IObjectData objectData;
        try {
            objectData = objectDataService.findById(objectId, actionContext.getTenantId(), apiName);
        } catch (MetadataServiceException e) {
            log.error("findById error:", e);
            return null;
        }
        return objectData;
    }

    public ObjectDescribeDocument findDescribe(ServiceContext actionContext, String objectApiName) {
        return ObjectDescribeDocument.of(findDescribe(actionContext.getTenantId(), objectApiName));
    }

    public IObjectDescribe findDescribe(String tenantId, String objectApiName) {
        try {
            return serviceFacade.findObject(tenantId, objectApiName);
        } catch (Exception e) {
            log.error("findDescribe error:", e);
            return null;
        }
    }

    public String chatComplete(ServiceContext context, String message) {
        BaseArgument baseArgument = new BaseArgument();
        baseArgument.setTenantId(context.getTenantId());
        baseArgument.setBusiness("sfa_ai");
        OpenAIChatComplete.Arg arg = new OpenAIChatComplete.Arg();
        arg.setModel(getModel());
        arg.setMessages(getMessage(getLangPrompt(context) + getPrompt(), message));
        arg.setStream(false);
        arg.setMaxTokens(2048);
        arg.setUser_id(context.getUser().getUpstreamOwnerIdOrUserId());
        OpenAIChatComplete.Result result = null;
        try {
            result = FsAI.openai().chatComplete(baseArgument, arg);
        } catch (Exception e) {
            log.warn("OpenAI chatComplete error:", e);
            throw new ValidateException(e.getMessage());
        }
        log.info("OpenAI result: {}", result.getMessage());
        return result.getMessage();
    }

    public String getAiComplete(User user,String apiName,Map<String, Object> sceneParamMap,String dataId){
        AiRestProxyModel.Arg arg = new AiRestProxyModel.Arg();
        arg.setApiName(apiName);
        if(ObjectUtils.isNotEmpty(sceneParamMap)){
            arg.setSceneVariables(sceneParamMap);
        }
        if(ObjectUtils.isNotEmpty(dataId)){
            arg.setBingObjectDataId(dataId);
        }
        AiRestProxyModel.Resposne resposne = aiRestProxy.completions(arg, AiRestProxy.getHeaders(user.getTenantId()));
        if(ObjectUtils.isEmpty(resposne) || resposne.getErrCode()!=0){
            log.error("AbstractAgentServiceImpl aiRestProxy.completions resposne :{}",JSONObject.toJSONString(resposne));
            return "";
        }
        return resposne.getResult().getMessage();
    }

    /**
     * 调用OpenAI 没有拼装 Prompt 适合单独调用
     *
     * @param context
     * @param message
     * @return
     */
    public String chatCompleteNoPrompt(ServiceContext context, String prompt, String message) {
        BaseArgument baseArgument = new BaseArgument();
        baseArgument.setTenantId(context.getTenantId());
        baseArgument.setBusiness("sfa_ai");
        OpenAIChatComplete.Arg arg = new OpenAIChatComplete.Arg();
        arg.setModel(getModel());
        arg.setMessages(getMessage(prompt, message));
        arg.setStream(false);
        arg.setMaxTokens(2048);
        arg.setUser_id(context.getUser().getUpstreamOwnerIdOrUserId());
        OpenAIChatComplete.Result result = null;
        try {
            result = FsAI.openai().chatComplete(baseArgument, arg);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        log.info("OpenAI chatCompleteNoPrompt: {}", result.getMessage());
        return result.getMessage();
    }

    public String getLangPrompt(ServiceContext context) {
        String lang = context.getLang().getValue() == null ? "zh-CN" : context.getLang().getValue();
        lang = getLangText(lang);
        return String.format(getLangPrompt(), lang, lang);
    }
    public String getLangPrompt(String lang) {
        lang = getLangText(lang);
        return String.format(getLangPrompt(), lang, lang);
    }

    private String getLangText(String lang) {
        if (lang.contains("zh-CN")) {
            return "简体中文";
        } else if (lang.contains("zh-TW")) {
            return "繁体中文";
        } else if (lang.contains("en")) {
            return "英文";
        } else if (lang.contains("ja")) {
            return "日文";
        }
        return lang;
    }



    private List<Message> getMessage(String prompt, String text) {
        List<Message> messages = new ArrayList<>();
        Message systemMessage = new Message();
        systemMessage.setRole("system");
        systemMessage.setContent(prompt);
        messages.add(systemMessage);

        Message userMessage = new Message();
        userMessage.setRole("user");
        userMessage.setContent(text);
        messages.add(userMessage);
        return messages;
    }

    public String getFeedContent(ServiceContext context, String apiName, String objectId, int limit) {
        List<IObjectData> dataList = queryActiveRecordListBySql(objectId, apiName, context.getUser(), limit);
        if (CollectionUtils.isEmpty(dataList)) {
            return "";
        }
        // 获取所有的content并用换行符拼接到一起
        return dataList.stream()
                .map(d -> {
                    if (d.get("active_record_content") != null) {
                        return d.get("active_record_content").toString() + "\n";
                    } else {
                        return "";
                    }
                })
                .reduce("", (a, b) -> a + b);
    }


    public CompanyModel.NormalObject getCompanyBasic(ServiceContext context, String name) {
        CompanyModel.CompanyDetailResult companyDetailByName = bizQuerySearchService.getCompanyDetailByName(context.getTenantId(), name);
        if (companyDetailByName == null || companyDetailByName.getCompanyBusinessInfo() == null
                || companyDetailByName.getCompanyBusinessInfo().getCompanyBaseInfo() == null
                || companyDetailByName.getCompanyBusinessInfo().getCompanyBaseInfo().getNormalObject() == null
                || org.apache.commons.lang3.StringUtils.isBlank(companyDetailByName.getCompanyBusinessInfo().getCompanyBaseInfo().getNormalObject().getIndustryName())) {
            return null;
        }
        return companyDetailByName.getCompanyBusinessInfo().getCompanyBaseInfo().getNormalObject();
    }

    /**
     * 获取销售记录
     *
     * @param objectId
     * @param apiName
     * @param user
     * @param limit
     * @return
     */
    public List<IObjectData> queryActiveRecordListBySql(String objectId, String apiName, User user, int limit) {
//        String findSql = String.format(CommonUtil.findActiveRecordSql, apiName, objectId, user.getTenantId(), limit);
//        List<Map> list = null;
//        try {
//            list = objectDataService.findBySql(user.getTenantId(), findSql);
//        } catch (MetadataServiceException e) {
//            throw new RuntimeException(e);
//        }
//        if (CollectionUtils.isEmpty(list)) {
//            return Lists.newArrayList();
//        }
//        List<IObjectData> returnResult = new ArrayList<>();
//        list.stream().forEach(d -> {
//            returnResult.add(ObjectDataDocument.of(d).toObjectData());
//        });
//        return returnResult;
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(limit);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterHasAnyOf(filters, "related_api_names", Lists.newArrayList(apiName));
        SearchUtil.fillFilterEq(filters, "related_object_data", Lists.newArrayList("_id", objectId));
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, user.getTenantId());
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, 0);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, "ActiveRecordObj", searchTemplateQuery);
        return queryResult.getData();
    }

    public Agent.Result getAIRetFromDB(ServiceContext context, Agent.Arg arg, Agent.Result result) {
        IObjectData objectData = selectInsightsResults(context.getTenantId(), arg.getObjectApiName(), arg.getObjectId(), getApiName());
        if (objectData == null) {
            return null;
        }
        result.setContent(JSONObject.toJSONString(objectData.get("insights_result")));
        result.setUpdateTime(Long.parseLong(objectData.get("last_modified_time").toString()));
        return result;
    }

    public void saveAIRetToDB(ServiceContext context, Agent.Arg arg, String ret) {
        saveAIInsightsResultsObj(context.getTenantId(), getApiName(), ret, null, arg.getObjectId(), arg.getObjectApiName());
    }

    /**
     * 根据条件查询数据：<br/>
     * tenantId、relevanceApiName、relevanceId、Type 四个参数查询<br/>
     *
     * @param tenantId          tenant_id
     * @param associatedApiName 关联对象apiName
     * @param associatedId      关联对象id
     * @param type              洞察类别
     * @return 没有数据返回null <br/>
     * 字段apiName：<br/>
     * 洞察结果 -- insights_result<br/>
     * 原始结果 -- original_result<br/>
     * 洞察时间 -- insights_time<br/>
     * 关联对象id -- associated_id<br/>
     * 关联对象apiName -- associated_api_name<br/>
     */
    public IObjectData selectInsightsResults(String tenantId, String associatedApiName, String associatedId, String type) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = com.google.common.collect.Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "tenant_id", tenantId);
        SearchUtil.fillFilterEq(filters, "associated_api_name", associatedApiName);
        SearchUtil.fillFilterEq(filters, "associated_id", associatedId);
        SearchUtil.fillFilterEq(filters, "insights_type", type);
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setLimit(1);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        QueryResult<IObjectData> bySearchQuery = serviceFacade.findBySearchQuery(User.systemUser(tenantId),
                SFAPreDefineObject.AIInsightsResults.getApiName(), searchTemplateQuery);
        List<IObjectData> data = bySearchQuery.getData();
        if (data.isEmpty()) {
            return null;
        }
        return data.get(0);
    }

    /**
     * 根据tenantId、relevanceApiName、relevanceId、Type 四个参数保存数据，如果已经存在就覆盖<br/>
     *
     * @param tenantId          tenant_id
     * @param type              洞察类别
     * @param aiResult          洞察结果，如果为null或为空，不修改或新建结果为：{}
     * @param originalResult    原始结果，如果为null或为空，不修改或存入结果为：{}
     * @param associatedId      关联对象的id，不能为空
     * @param associatedApiName 关联对象的ApiName，不能为空
     */
    public void saveAIInsightsResultsObj(String tenantId, String type,
                                         String aiResult, String originalResult,
                                         String associatedId, String associatedApiName) {
        IObjectData iObjectData = this.selectInsightsResults(tenantId, associatedApiName, associatedId, type);
        if (iObjectData != null) {
            if (!StringUtils.isEmpty(aiResult)) {
                iObjectData.set("insights_result", aiResult);
            }
            if (!StringUtils.isEmpty(originalResult)) {
                iObjectData.set("original_result", originalResult);
            }
            iObjectData.set("associated_id", associatedId);
            iObjectData.set("associated_api_name", associatedApiName);
            iObjectData.set("insights_time", new Date().getTime());
            serviceFacade.updateObjectData(User.systemUser(tenantId), iObjectData);
            return;
        }
        // 新建数据
        iObjectData = new ObjectData();
        iObjectData.setDescribeApiName(SFAPreDefineObject.AIInsightsResults.getApiName());
        iObjectData.setTenantId(tenantId);
        iObjectData.setRecordType(IObjectData.RECORD_TYPE_DEFAULT);
        if (!StringUtils.isEmpty(aiResult)) {
            iObjectData.set("insights_result", aiResult);
        } else {
            iObjectData.set("insights_result", "{}");
        }
        if (!StringUtils.isEmpty(originalResult)) {
            iObjectData.set("original_result", originalResult);
        } else {
            iObjectData.set("original_result", "{}");
        }
        iObjectData.set("associated_id", associatedId);
        iObjectData.set("associated_api_name", associatedApiName);
        iObjectData.set("insights_time", new Date().getTime());
        iObjectData.set("insights_type", type);
        serviceFacade.saveObjectData(User.systemUser(tenantId), iObjectData);
    }

    // 替换这种的 <a style=\"color:#0C6CFF\" href=\"https://fs8.ceshi112.com/cxxepn\" target=\"_blank\">行业客户案例</a>
    // 留下 行业客户案例:https://fs8.ceshi112.com/cxxepn
    public String removeLabel(String content) {
        // 移除 &nbsp;
        content = content.replaceAll("&nbsp;", "");
        // 把 </b> 换成 \n
        content = content.replace("</b>", "\n");
        // 把 </p> 换成 \n
        content = content.replace("</p>", "\n");
        // 替换 <a> 标签并保留文本和链接
        content = content.replaceAll("<a\\s+href=\"([^\"]*)\"[^>]*>([^<]*)</a>", "$2:$1");
        // 移除其他所有标签
        content = content.replaceAll("<[^>]+>", "");
        return content;
    }

    public String removeLabelForMobile(String content) {
        // 移除 &nbsp;
        content = content.replaceAll("&nbsp;", "");
        // 把 </b> 换成 \n
        content = content.replaceAll("</b>", "");
        // 把 </p> 换成 \n
        content = content.replaceAll("</p>", "");
        return content.replaceAll("<[^>]*>", "");
    }

    public List<IObjectData> getPromptTemplate(User user) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(13);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterEq(filters, "api_name", Lists.newArrayList(getApiName()));
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, promptTemplateTenantId);
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, 0);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(new User(promptTemplateTenantId, user.getUserId()), SFAPreDefineObject.SFAPromptTemplate.getApiName(), searchTemplateQuery);
        return queryResult.getData();
    }

    @Override
    public boolean handleLangIsZHCN(ServiceContext context) {
        if (ObjectUtils.isNotEmpty(context.getLang()) && (context.getLang().getValue() == null || context.getLang().getValue().contains("zh-CN"))) {
            return true;
        }
        return false;
    }

    public List<IObjectData> getPersonnelObjById(User user, List<String> ids) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(ids.size());
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        List filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, "user_id", ids);
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, user.getTenantId());
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, 0);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, "PersonnelObj", searchTemplateQuery);
        return queryResult.getData();
    }

    public List<IObjectData> queryContactList(String accountId, User user, int limit) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(limit);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        List filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AccountConstants.Field.ACCOUNT_ID, accountId);
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, user.getTenantId());
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, 0);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, SFAPreDefineObject.Contact.getApiName(), searchTemplateQuery);
        return queryResult.getData();
    }

    public List<IObjectData> queryNewOpportunityContactsList(String newOpportunityId, User user, int limit) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(limit);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterEq(filters, "new_opportunity_id", newOpportunityId);
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, user.getTenantId());
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, 0);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, SFAPreDefineObject.NewOpportunityContacts.getApiName(), searchTemplateQuery);
        return queryResult.getData();
    }

    public void fillData(IObjectDescribe iObjectDescribe, List<IObjectData> dataList, User user) {
        if(CollectionUtils.isEmpty(dataList)){
            return;
        }
        dataList.stream().forEach(x -> {
            promptService.removeFieldList.stream().forEach(f -> {
                x.set(f, null);
            });
        });
        serviceFacade.fillObjectDataWithRefObject(iObjectDescribe, dataList, user);
        serviceFacade.fillUserInfo(iObjectDescribe, dataList, user);
        serviceFacade.fillDepartmentInfo(iObjectDescribe, dataList, user);
        //serviceFacade.fillExtendFieldInfo(iObjectDescribe, dataList,user);
    }

    public Map<String, IObjectDescribe> getIObjectDescribeToMap(User user, List<String> apiNames) {
        List<IObjectDescribe> iObjecctDescribeList = null;
        try {
            iObjecctDescribeList = objectDescribeService.findDescribeListByApiNames(user.getTenantId(), apiNames);
        } catch (MetadataServiceException e) {
            throw new RuntimeException(e);
        }
        return iObjecctDescribeList.stream().collect(Collectors.toMap(d -> d.getApiName(), Function.identity(), (v1, v2) -> v1));
    }


    public List<SearchSceneKnowledgeModel.Result> searchSceneKnowledgeResult(ServiceContext context, IObjectData accountData, String objectApiName) {
        String searchWord = "";
        // 先判断当前客户是否有行业字段，没有去工商里查询
        if (ObjectUtils.isEmpty(accountData.get(AccountConstants.Field.INDUSTRY_LEVEL2)) && ObjectUtils.isEmpty(accountData.get(AccountConstants.Field.INDUSTRY_LEVEL1))) {
            CompanyModel.NormalObject companyBasic = getCompanyBasic(context, accountData.getName());
            if (companyBasic == null) {
                return null;
            }
            searchWord = companyBasic.getIndustryName();
        } else {
            IObjectDescribe describe = findDescribe(context.getTenantId(), objectApiName);
            String value = "";
            String field = "";
            if (ObjectUtils.isNotEmpty(accountData.get(AccountConstants.Field.INDUSTRY_LEVEL2))) {
                value = accountData.get(AccountConstants.Field.INDUSTRY_LEVEL2).toString();
                field = AccountConstants.Field.INDUSTRY_LEVEL2;
            } else {
                value = accountData.get(AccountConstants.Field.INDUSTRY_LEVEL1).toString();
                field = AccountConstants.Field.INDUSTRY_LEVEL1;
            }
            IFieldDescribe fieldDescribe = describe.getFieldDescribe(field);
            List<Map> options = (List<Map>) fieldDescribe.get("options");
            for (Map<String, String> m : options) {
                if (value.equals(m.get("value"))) {
                    searchWord = m.get("label");
                    break;
                }
            }
        }
        return searchSceneKnowledgeResult(context, searchWord);
    }

    public List<SearchSceneKnowledgeModel.Result> searchSceneKnowledgeByLeadsIndustry(ServiceContext context, IObjectData leadsData) {
        String searchWord = "";
        if (leadsData.get("company") == null) {
            return null;
        }
        CompanyModel.NormalObject companyBasic = getCompanyBasic(context, leadsData.get("company").toString());
        if (companyBasic != null) {
            searchWord = companyBasic.getIndustryName();
        }
        return searchSceneKnowledgeResult(context, searchWord);
    }

    public List<SearchSceneKnowledgeModel.Result> searchSceneKnowledgeResult(ServiceContext context, String searchWord) {
        if (ObjectUtils.isEmpty(searchWord)) {
            return null;
        }
       return sfaKnowledgeService.getKnowledgeList(context.getUser(),searchWord,promptService.getKnowledgeBaseFindFcene());
    }

    /**
     * 获取营销动态数据
     *
     * @param context
     * @param arg
     */
    public JSONArray getUserMarketingAction(ServiceContext context, Agent.Arg arg) {
        FSUserMarketingActionProxy.Arg fsuArg = new FSUserMarketingActionProxy.Arg();
        fsuArg.setObjectApiName(arg.getObjectApiName());
        fsuArg.setObjectId(arg.getObjectId());
        fsuArg.setPageNum(1);
        fsuArg.setPageSize(50);
        Map<String, String> headerMap = HttpHeaderUtil.getHeaders(context.getTenantId());
        String rawData = fsUserMarketingActionProxy.queryUserMarketingAction(headerMap, fsuArg);
        // 解析原始json数据
        JSONObject rawJsonObject = (JSONObject) JSON.parse(rawData);
        String code = rawJsonObject.get("code").toString();
        if (!"0".equals(code)) {
            log.error("LeadsMarketingSummary remote call error, msg:{}, request parameters:{}, request header:{}", rawJsonObject.get("message"), fsuArg, headerMap);
        }
        return (JSONArray) ((JSONObject) rawJsonObject.get("data")).get("data");
    }

    protected String analyzeTimeMap(Map<Long, Integer> timeMap) throws ParseException {
        // 统计工作日 和 非工作日 的次数
        //Set<Date> workTime = new HashSet<>();
        //Set<Date> restTime = new HashSet<>();
        Map<Integer, Integer> workHourCountMap = new HashMap<>();
        Map<Integer, Integer> restHourCountMap = new HashMap<>();
        int workDayCount = 0;
        int restDayCount = 0;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH");
        for (Map.Entry<Long, Integer> entry : timeMap.entrySet()) {
            Long timestamp = entry.getKey();
            // 将时间戳转换为LocalDateTime
            LocalDateTime datetime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
            // 判断该时间戳对应的日期是星期几
            DayOfWeek dayOfWeek = datetime.getDayOfWeek();
            // 格式化时间
            //Date formatTime = simpleDateFormat.parse(simpleDateFormat.format(new Date(timestamp)));
            Date formatTime = simpleDateFormat.parse(simpleDateFormat.format(new Date(timestamp)));
            // 统计次数
            if (dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY) {
                workDayCount += timeMap.get(timestamp);
                // 获取timestamp 所在的小时
                int hour = datetime.getHour();
                if (workHourCountMap.containsKey(hour)) {
                    workHourCountMap.put(hour, workHourCountMap.get(hour) + 1);
                } else {
                    workHourCountMap.put(hour, 1);
                }
                //workTime.add(formatTime);
            } else {
                restDayCount += timeMap.get(timestamp);
                //restTime.add(formatTime);
                int hour = datetime.getHour();
                if (restHourCountMap.containsKey(hour)) {
                    restHourCountMap.put(hour, restHourCountMap.get(hour) + 1);
                } else {
                    restHourCountMap.put(hour, 1);
                }
            }
        }
        List<Integer> top3WorkHour = Lists.newArrayList();
        String workOrRest;
        if (workDayCount >= restDayCount) {
            workOrRest = I18N.text(I18NAIUtil.WEEKDAY);
            //top3WorkHour
            workHourCountMap.entrySet().stream().sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue())).limit(3).forEach(e -> top3WorkHour.add(e.getKey()));
        } else {
            workOrRest = I18N.text(I18NAIUtil.REST_DAY);
            restHourCountMap.entrySet().stream().sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue())).limit(3).forEach(e -> top3WorkHour.add(e.getKey()));
        }
        Collections.sort(top3WorkHour);
        if (com.facishare.paas.appframework.common.util.CollectionUtils.empty(top3WorkHour)) {
            return "暂未获取到活跃时间信息";
        }
        return workOrRest + formatTimeRanges(top3WorkHour);
    }

    public static String formatTimeRanges(List<Integer> top3WorkHour) {
        // 对小时列表进行排序
        Collections.sort(top3WorkHour);

        StringBuilder timeStr = new StringBuilder();
        Integer startHour = null; // 跟踪区间开始时间
        Integer prevHour = null; // 跟踪前一个小时，用以判断是否连续

        for (Integer hour : top3WorkHour) {
            if (startHour == null) {
                // 初始化开始时间
                startHour = hour;
            } else if (prevHour != null && hour != prevHour + 1) {
                // 不连续时，结束当前区间并开始新的区间
                timeStr.append(formatRange(startHour, prevHour)).append("，");
                startHour = hour;
            }
            prevHour = hour; // 更新前一个小时记录
        }
        // 处理最后一个区间
        if (startHour != null) {
            timeStr.append(formatRange(startHour, prevHour));
        }
        return timeStr.toString();
    }

    private static String formatRange(Integer start, Integer end) {
        // 格式化单个时间区间
        return String.format("%02d:00 ~ %02d:00", start, end + 1);
    }
}
