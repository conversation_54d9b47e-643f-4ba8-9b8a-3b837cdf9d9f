package com.facishare.crm.sfa.predefine.service.ai.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.paas.ext.dao.businessrisk.entity.*;
import com.facishare.crm.paas.ext.dao.businessrisk.mapper.BICompanyBaseMapper;
import com.facishare.crm.sfa.lto.industry.CompanyModel;
import com.facishare.crm.sfa.predefine.service.ai.enums.ActionTargetEnum;
import com.facishare.crm.sfa.predefine.service.ai.enums.OpTypeEnum;
import com.facishare.crm.sfa.predefine.service.ai.model.Agent;
import com.facishare.crm.sfa.predefine.service.ai.model.ButtonAction;
import com.facishare.crm.sfa.predefine.service.ai.service.AbstractAgentServiceImpl;
import com.facishare.crm.sfa.predefine.service.bizquery.BizQuerySearchService;
import com.facishare.crm.sfa.predefine.service.riskbrain.BusinessBrainService;
import com.facishare.crm.sfa.predefine.service.riskbrain.model.FinancialReportInsightsModel;
import com.facishare.crm.sfa.predefine.service.riskbrain.model.RiskBrainModels;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/4/13 16:50
 * @description: 财报洞察
 * @IgnoreI18n or IgnoreI18nFile or @IgnoreI18nFile
 */
@Component
@Slf4j
public class AccountReportInsights extends AbstractAgentServiceImpl {
    @Autowired
    BICompanyBaseMapper biCompanyBaseMapper;
    @Autowired
    BusinessBrainService businessBrainService;
    @Autowired
    BizQuerySearchService bizQuerySearchService;

    @Override
    public String getApiName() {
        return "AccountReportInsights";
    }

    @Override
    public String getPrompt() {
//        return "请对内容按照公司概况、公司高管、财务状况、市场表现、经营策略和未来规划、研发投入和创新能力、重大事项披露、风险因素、社会责任和可持续发展维度进行总结，并填充下面的内容以JSON格式返回\n" +
//                "[{\"title\":\"公司概况\",\"content\":\"\"},{\"title\":\"公司高管\",\"content\":\"\"},{\"title\":\"财务状况\",\"content\":\"\"},{\"title\":\"市场表现\",\"content\":\"\"},{\"title\":\"经营策略和未来规划\",\"content\":\"\"},{\"title\":\"研发投入和创新能力\",\"content\":\"\"},{\"title\":\"重大事项披露\",\"content\":\"\"},{\"title\":\"风险因素\",\"content\":\"\"},{\"title\":\"社会责任和可持续发展\",\"content\":\"\"}]";
//        return promptTemplate.get(getApiName());
        return "";
    }

    private static String JSON_FORMAT = "[{\"title\":\"公司概况\",\"content\":\"\"},{\"title\":\"公司高管\",\"content\":\"\"},{\"title\":\"财务状况\",\"content\":\"\"},{\"title\":\"市场表现\",\"content\":\"\"},{\"title\":\"经营策略和未来规划\",\"content\":\"\"},{\"title\":\"研发投入和创新能力\",\"content\":\"\"},{\"title\":\"重大事项披露\",\"content\":\"\"},{\"title\":\"风险因素\",\"content\":\"\"},{\"title\":\"社会责任和可持续发展\",\"content\":\"\"}]";// ignoreI18n

    @Override
    public Agent.Result getObjectData(ServiceContext context, Agent.Arg arg) {
        Agent.Result result = super.getObjectData(context, arg);
        result.setActionList(getButtonAction(arg));
        if (OpTypeEnum.UPDATE.getCode().equals(arg.getOpType())) {
            result.setContent("success");
            result.setErrCode(0);
            return result;
        }
        if (OpTypeEnum.REFRESH.getCode().equals(arg.getOpType())) {
            getAIRetFromDB(context, arg, result);
            result.setPreviewContent(result.getContent());
            result.setDataList(getDataList(result.getContent()));
            return result;
        }
        // 重新生成
//        String unorganizedData = "";
//        Object aiRet = getAIRet(context, unorganizedData);
//        try {
//            List<ObjectDataDocument> dataList = getDataList(aiRet.toString());
//            result.setDataList(dataList);
//            if (aiRetFromDB == null || OpTypeEnum.REGENERATE.getCode().equals(arg.getOpType())){
//                saveAIRetToDB(context, arg, aiRet.toString());
//            }
//        } catch (Exception e) {
//            log.error("AccountReportInsights arg:{},ret:{}, ",arg,aiRet,e);
//            result.setDataList(getDataList(getJSONData()));
//        }
//        if (result.getContent() == null || OpTypeEnum.REGENERATE.getCode().equals(arg.getOpType())) {
//            saveAIRetToDB(context, arg, getJSONData());
//        }

//        ParallelUtils.ParallelTask task = ParallelUtils.createBackgroundTask();
//        task.submit(() -> {
//            handleAIResult(context,arg,result);
//        });
//        task.run();
//        getAIRetFromDB(context, arg, result);
//        result.setPreviewContent(result.getContent());
//        result.setDataList(getDataList(result.getContent()));
//        result.setSimpleDescribe(createDescribeMap());
        //handleAIResult(context,arg,result);
        IObjectData objectData = findById(context, arg.getObjectId(), arg.getObjectApiName());
       // String string = String.format(promptTemplate.get(getApiName()),objectData.getName());
        String financialReportInfo = generateInfoAIRet(context, objectData.getName());
       // financialReportInfo = financialReportInfo.trim().replaceAll(" ","").replaceAll("\n", "");
       // if(financialReportInfo.contains("[") && financialReportInfo.contains("]")){
       //     int start = financialReportInfo.indexOf("[");
       //     int end   = financialReportInfo.indexOf("]")+1;
       //     financialReportInfo = financialReportInfo.substring(start,end);
       //     financialReportInfo = financialReportInfo.trim().replaceAll("\\\\\\\\n","").replaceAll("\\\\n","").replaceAll("\\\\","");;
       // }else if(financialReportInfo.contains("{") && financialReportInfo.contains("}")){
       //     financialReportInfo = financialReportInfo.replaceAll("^\"|\"$", "").replaceAll("\\\\\\\\n","").replaceAll("\\\\n","").replaceAll("\\\\","");
       //     financialReportInfo = "["+financialReportInfo+"]";
       // }else {
       //     log.warn("financialReportInfo is not json financialReportInfo:{}",financialReportInfo);
       //     return result;
       // }
        List<ObjectDataDocument> dataList = getDataListFixedInvalidJSON(context, "我需要的json格式如下：" + JSON_FORMAT, financialReportInfo);// ignoreI18n
        result.setContent(JSON.toJSONString(dataList));
        saveAIRetToDB(context, arg, JSON.toJSONString(dataList));
        result.setPreviewContent(convertPreviewContent(dataList));
        result.setDataList(dataList);
        return result;
    }


    public void handleAIResult(ServiceContext context, Agent.Arg arg,Agent.Result result){
        try {
            IObjectData objectData = findById(context, arg.getObjectId(), arg.getObjectApiName());
            if (objectData == null) {
                log.warn("financialReportInsights objectData is null");
                result.setErrMessage("objectData is null");
                return;
            }
            String name = objectData.getName();
            StringBuilder sb = new StringBuilder();
            CompanyModel.CompanyDetailResult baseInfo = bizQuerySearchService.getCompanyDetailByName(context.getTenantId(), name);
            if (baseInfo != null && baseInfo.getCompanyBusinessInfo() != null) {
                if (baseInfo.getCompanyBusinessInfo().getCompanyBaseInfo().getNpoObject() != null) {
                    String businessScope = baseInfo.getCompanyBusinessInfo().getCompanyBaseInfo().getNormalObject().getScope();
                    if (StringUtils.isNotBlank(businessScope)) {
                        sb.append("公司经营范围：" + businessScope);// ignoreI18n
                    }
                }
                if (baseInfo.getCompanyBusinessInfo().getCompanyManagerInfo() != null) {
                    List<CompanyModel.Managers> managers = baseInfo.getCompanyBusinessInfo().getCompanyManagerInfo().getManagers();
                    if (CollectionUtils.isNotEmpty(managers)) {
                        sb.append(",公司高管：");// ignoreI18n
                        for (CompanyModel.Managers manager : managers) {
                            sb.append(manager.getCompanyManagerName() + "-" + manager.getCompanyPositionName() + ",");
                        }
                    }
                }
            }
            String unifiedSocialCreditCode = ObjectUtils.isEmpty(objectData.get("uniform_social_credit_code")) ? "" : objectData.get("uniform_social_credit_code").toString();
            if (StringUtils.isBlank(unifiedSocialCreditCode)) {
                unifiedSocialCreditCode = name;
            }
            RiskBrainModels.RiskInsightsArg businessArg = new RiskBrainModels.RiskInsightsArg();
            businessArg.setUnifiedSocialCreditCode(unifiedSocialCreditCode);
            FinancialReportInsightsModel reportInsightsModel = businessBrainService.financialReportInsights(context, businessArg);
            if (reportInsightsModel != null) {
                BIAnnualReportAssetEntity asset = reportInsightsModel.getBiAnnualReportAssetEntity();
                if (asset != null) {
                    sb.append(",资产总额：" + asset.getTotalAmount() + ",所有者权益合计：" + asset.getEquityAmount() +// ignoreI18n
                            ",营业总收入：" + asset.getBusinessIncome() + ",利润总额：" + asset.getTotalProfitAmount() +// ignoreI18n
                            ",主营业务收入：" + asset.getMainBusinessIncome() + ",纳税总额：" + asset.getTaxAmount() +// ignoreI18n
                            ",负债总额：" + asset.getDebtAmount() + ",获得政府扶持资金、补助：" + asset.getGovernmentSupport() + ",金融贷款：" + asset.getFinancialLoan());// ignoreI18n
                }
                List<BIAnnualReportPartnerEntity> partners = reportInsightsModel.getBiAnnualReportPartnerEntityList();
                if (CollectionUtils.isNotEmpty(partners)) {
                    sb.append(",股东：");// ignoreI18n
                    for (BIAnnualReportPartnerEntity partner : partners) {
                        sb.append("股东名称 " + partner.getStockName() + ",认缴出资额 " + partner.getStockCapital() + ",认缴出资日期 " + partner.getStockDate() + ",");// ignoreI18n
                    }
                }
                List<BIAnnualReportInvestEntity> invests = reportInsightsModel.getBiAnnualReportInvestEntityList();
                if (CollectionUtils.isNotEmpty(invests)) {
                    sb.append(",对外投资：");// ignoreI18n
                    for (BIAnnualReportInvestEntity invest : invests) {
                        sb.append("对外投资企业名称 " + invest.getInvesteeName() + ",统一信用代码 " + invest.getCreditNo() + ",");// ignoreI18n
                    }
                }
                List<BIAnnualReportGuaranteeEntity> guarantees = reportInsightsModel.getBiAnnualReportGuaranteeEntityList();
                if (CollectionUtils.isNotEmpty(guarantees)) {
                    sb.append(",对外担保：");// ignoreI18n
                    for (BIAnnualReportGuaranteeEntity guarantee : guarantees) {
                        sb.append("债权人 " + guarantee.getCreditor() + ",债务人 " + guarantee.getDebtor() + ",主债权种类 " + guarantee.getDebtType() + ",主债权数额 " + guarantee.getGuaranteeAmount() +// ignoreI18n
                                ",履行债务的期限 " + guarantee.getPerformTime() + ",担保期间 " + guarantee.getGuaranteeTerm() +// ignoreI18n
                                ",担保方式 " + guarantee.getGuaranteeType() + ",担保范围 " + guarantee.getGuaranteeScope() + ",");// ignoreI18n
                    }
                }
                List<BIAnnualReportSocialSecurityEntity> socialSecurities = reportInsightsModel.getBiAnnualReportSocialSecurityEntityList();
                if (CollectionUtils.isNotEmpty(socialSecurities)) {
                    sb.append(",社保情况：");// ignoreI18n
                    for (BIAnnualReportSocialSecurityEntity socialSecurity : socialSecurities) {
                        sb.append("保险种类、名称 " + socialSecurity.getInsuranceName() + ",保险缴费基数 " + socialSecurity.getInsuranceBase() +// ignoreI18n
                                ",参保人数 " + socialSecurity.getInsuranceAmount() + ",实际缴费金额 " + socialSecurity.getInsuranceRealCapital() +// ignoreI18n
                                ",累计欠缴金额 " + socialSecurity.getInsuranceArrearage() + ",");
                    }
                }
            }


            // 卡片返回mock 数据
            if (OpTypeEnum.GENERATE.getCode().equals(arg.getOpType())) {
                result.setPreviewContent(getTemplate(context));
                result.setContent(removeLabel(getTemplate(context)));
            }

            if (sb.length() < 10) {
                result.setPreviewContent(getTemplate(context));
                result.setContent(removeLabel(getTemplate(context)));
            } else {
                String financialReportInfo = generateInfoAIRet(context, sb.toString());
                // 移除开头的引号
                if (financialReportInfo.startsWith("\"")) {
                    financialReportInfo = financialReportInfo.substring(1);
                }
                // 移除结尾的引号
                if (financialReportInfo.endsWith("\"")) {
                    financialReportInfo = financialReportInfo.substring(0, financialReportInfo.length() - 1);
                }
                financialReportInfo = financialReportInfo.replaceAll("\\\\", "");
                result.setContent(financialReportInfo);
                List<ObjectDataDocument> dataList = getDataList(financialReportInfo);
                result.setPreviewContent(financialReportInfo);
                result.setDataList(dataList);
            }
            saveAIInsightsResultsObj(context.getTenantId(),getApiName(), JSONObject.toJSONString(result.getDataList()),null,arg.getObjectId(),arg.getObjectApiName());

        } catch (Exception e) {
            log.error("AccountReportInsights arg:{}, ", arg, e);
            result.setPreviewContent(getTemplate(context));
            result.setContent(removeLabel(getTemplate(context)));
            result.setDataList(getDataList(getJSONData(context)));
        }
    }

    private String generateInfoAIRet(ServiceContext context, String originalResult) {
        Object ai = getAIRet(context, originalResult,getApiName());
        return JSON.toJSONString(ai.toString());
    }

    public String getJSONData(ServiceContext context) {

//        if (context.getLang() == null || Lang.zh_CN.getValue().equals(context.getLang().getValue())) {
//            return "";
//        } else {
//            return "";
//        }
        return "";
    }

    public String getTemplate(ServiceContext context) {
        // 获取 getJSONData 的所有value 并用标签展示
//        if (context.getLang() == null || Lang.zh_CN.getValue().equals(context.getLang().getValue())) {
//            return"";
//           /* return "<b>财务状况</b>" +
//                    "<p>2023年，小米集团的总收入达到了2710亿元人民币。尽管全球智能手机市场面临一定的下滑压力，但小米通过产品创新和市场拓展，保持了在全球市场的领先地位，2023年三季度，公司整体存货金额为人民币368亿元，同比下降30.5%，为十一个季度以来的最低水位。这表明公司在库存管理方面取得了积极成效。</p>" +
//                    "<b>市场表现</b>" +
//                    "<p>智能手机业务继续是公司收入的主要来源。在中国大陆地区，小米的高端智能手机系列，如Xiaomi 14系列，获得了用户的广泛认可。小米在中高端市场的布局取得了显著成效，特别是在人民币4,000–6,000元价位段的智能手机市占率提升，巩固了其在细分市场的竞争优势；互联网服务业务表现不俗，收入和毛利率均创历史新高。</p>" +
//                    "<b>经营策略和未来规划</b>" +
//                    "<p>小米集团宣布了“人车家全生态”的最新战略升级，并发布了全新操作系统“小米澎湃OS”。这一战略的实施标志着小米在智能汽车领域的深入布局，以及对未来智慧生活的全方位探索。小米的首款汽车产品Xiaomi SU7系列的亮相引起了业界的广泛关注。小米提出了“规模与利润并重”的经营思路，并成立了集团经营管理委员会和集团人力资源委员会，以优化战略结构、运营管理和资源配置，推动专业化的长期经营治理体系建设。</p>" +
//                    "<b>研发投入和创新能力</b>" +
//                    "<p>2023年，小米集团在研发投入上持续加大，研发支出达到了人民币191亿元，同比增长19.2%。这一投入加强了小米的技术创新能力，并为公司的长期发展奠定了坚实的基础。截至2023年12月31日，小米的研发人员数量达到了17,800人，占员工总数的53%。</p>" +
//                    "<b>重大事项披露</b>" +
//                    "<p>小米集团宣布战略正式升级为“人车家全生态”，并发布了小米澎湃OS操作系统。这一战略的实施标志着小米在智能汽车领域的深入布局，以及对未来智慧生活的全方位探索。小米汽车预计在2024年上半年正式上市，这将是小米“人车家全生态”战略的关键组成部分。小米公开了2040年碳中和承诺，并在ESG方面获得了多项赞誉。公司还通过多种方式在教育、科研人才培养、基础研发等方面践行公益。</p>" +
//                    "<b>风险因素</b>" +
//                    "<p>小米进军智能电动汽车等新业务领域，需要巨额投资和长期的研发，且市场竞争激烈，存在较大的不确定性和风险。中美贸易战等因素可能导致供应链变数，影响小米产品的生产和成本。此外，小米在中低端机型上采用联发科系列芯片，以应对潜在的供应链风险。全球经济下行、通货膨胀、美联储加息等宏观经济因素可能影响消费者的。</p>";
//        */
//        }else {
//            return"";
////            return "<b>Financial Situation</b>" +
////                    "<p>In 2023, Xiaomi Group's total revenue reached 271 billion yuan. Despite the downward pressure on the global smartphone market, Xiaomi has maintained its leading position in the global market through product innovation and market expansion. In the third quarter of 2023, the company's overall inventory amount was 36.8 billion yuan, a year-on-year decrease of 30.5%, the lowest level in eleven quarters. This indicates that the company has achieved positive results in inventory management.</p>" +
////                    "<b>Market Performance</b>" +
////                    "<p>The smart phone business continues to be the main source of revenue for the company. In mainland China, Xiaomi's high-end smart phone series, such as the Xiaomi 14 series, has been widely recognized by users. Xiaomi has achieved significant results in the layout of the mid-to-high-end market, especially in the 4,000–6,000 yuan price segment of the smart phone market share, consolidating its competitive advantage in the segmented market; the Internet service business has performed well, with revenue and gross profit margins reaching historic highs.</p>" +
////                    "<b>Business Strategy and Future Planning</b>" +
////                    "<p>Xiaomi Group announced the latest strategic upgrade of “People, Cars, and Homes Full Ecology” and released the new operating system “Xiaomi Surging OS”. The implementation of this strategy marks Xiaomi's deep layout in the smart car field and the comprehensive exploration of future smart life. The debut of Xiaomi's first car product, the Xiaomi SU7 series, has attracted wide attention from the industry. Xiaomi has proposed an operating philosophy of “scale and profit coexist” and established a group management committee and a group human resources committee to optimize strategic structure, operational management, and resource allocation, and promote the professionalization of long-term business governance system construction.</p>" +
////                    "<b>R&D Investment and Innovation Capability</b>" +
////                    "<p>In 2023, Xiaomi Group continued to increase its investment in research and development, with R&D spending reaching 19.1 billion yuan, a year-on-year increase of 19.2%. This investment has strengthened Xiaomi's technological innovation capabilities and laid a solid foundation for the company's long-term development. As of December 31, 2023, Xiaomi's R&D staff numbered 17,800, accounting for 53% of the total number of employees.</p>" +
////                    "<b> Disclosure of Major Events</b>" +
////                    "<p>Xiaomi Group announced the formal upgrade of its strategy to “People, Cars, and Homes Full Ecology” and released the Xiaomi Surging OS operating system. The implementation of this strategy marks Xiaomi's deep layout in the smart car field and the comprehensive exploration of future smart life. Xiaomi's car is expected to be officially listed in the first half of 2024, which will be a key part of Xiaomi's “People, Cars, and Homes Full Ecology” strategy. Xiaomi has publicly committed to carbon neutrality by 2040 and has received multiple accolades in ESG. The company also practices public welfare in education, scientific research talent training, basic research and development, and other ways.</p>" +
////                    "<b>Risk Factors</b>" +
////                    "<p>Xiaomi's entry into new business areas such as smart electric vehicles requires huge investment and long-term research and development, and the market competition is fierce, with significant uncertainty and risks. Factors such as the China-US trade war may cause changes in the supply chain, affecting the production and cost of Xiaomi products. In addition, Xiaomi uses MediaTek series chips in mid-to-low-end models to address potential supply chain risks. Global economic downturn, inflation, Fed rate hikes, and other macroeconomic factors may affect consumers.</p>";
//        }
        return"";
    }

    public String convertPreviewContent(List<ObjectDataDocument> dataList){
        if(CollectionUtils.isEmpty(dataList)){
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for(ObjectDataDocument data : dataList){
            sb.append("<b>").append(data.get("title")).append("</b>");
            sb.append("<p>").append(data.get("content")).append("</p>");
        }
        return sb.toString();
    }


    public Map createDescribeMap() {
        Map describe = new HashMap();
//        describe.put("title", "标题");
//        describe.put("icon", "图标");
//        describe.put("content", "内容");
        return describe;
    }

    @Override
    public List<ButtonAction> getButtonAction(Agent.Arg arg) {
        List<ButtonAction> buttonActions = Lists.newArrayList();
        buttonActions.addAll(super.getButtonAction(arg));
        buttonActions.add(buildButtonAction(ActionTargetEnum.OBJECT_UPDATE_PROFILE));
        return buttonActions;
    }
}
