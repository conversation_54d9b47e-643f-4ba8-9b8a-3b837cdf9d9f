package com.facishare.crm.sfa.predefine.service.ai.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.ai.Util.I18NAIUtil;
import com.facishare.crm.sfa.predefine.service.ai.enums.ActionTargetEnum;
import com.facishare.crm.sfa.predefine.service.ai.enums.OpTypeEnum;
import com.facishare.crm.sfa.predefine.service.ai.model.Agent;
import com.facishare.crm.sfa.predefine.service.ai.model.ButtonAction;
import com.facishare.crm.sfa.predefine.service.ai.service.AbstractAgentServiceImpl;
import com.facishare.crm.sfa.predefine.service.ai.service.marketing.CSSessionDataService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.rest.core.RestServiceProxyFactory;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/4/13 11:30
 * @description: 线索 BANT 洞察
 * @IgnoreI18n or IgnoreI18nFile or @IgnoreI18nFile
 */
@Component
@Service
@Slf4j
public class LeadsBANTInsights extends AbstractAgentServiceImpl {

    @Autowired
    private CSSessionDataService csSessionDataService;
    @Qualifier("restServiceProxyFactory")
    @Autowired
    private RestServiceProxyFactory restServiceProxyFactory;


    @Override
    public String getPrompt() {
        return promptTemplate.get(getApiName());
    }

    private static String JSON_FORMAT = "[\n" +
            "    {\n" +
            "        \"Category\": \"B\",\n" +
            "        \"CategoryLabel\": \"明确的采购预算\",\n" +
            "        \"Description\": \"\",\n" +
            "        \"Status\": \"\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"Category\": \"A\",\n" +
            "        \"CategoryLabel\": \"关键决策人\",\n" +
            "        \"Description\": \"\",\n" +
            "        \"Status\": \"\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"Category\": \"N\",\n" +
            "        \"CategoryLabel\": \"明确的业务需求\",\n" +
            "        \"Description\": \"\",\n" +
            "        \"Status\": \"\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"Category\": \"T\",\n" +
            "        \"CategoryLabel\": \"明确的采购时间\",\n" +
            "        \"Description\": \"\",\n" +
            "        \"Status\": \"\"\n" +
            "    }\n" +
            "]";


    @Override
    public String getApiName() {
        return "LeadsBANTInsights";
    }

    @Override
    public String getModel() {
        return super.getModel();
    }

    @Override
    public Agent.Result getObjectData(ServiceContext context, Agent.Arg arg) {
        Agent.Result result = super.getObjectData(context, arg);
        result.setSimpleDescribe(createDescribeMap());
        if (OpTypeEnum.REFRESH.getCode().equals(arg.getOpType())) {
            getAIRetFromDB(context, arg, result);
            if (result.getContent() != null) {
                result.setDataList(getDataList(result.getContent()));
                return result;
            }
        }

        StringBuilder sb = new StringBuilder();
        // 销售记录
        String feedContent = getFeedContent(context, arg.getObjectApiName(), arg.getObjectId(), 10);
        if (StringUtils.isNotBlank(feedContent)) {
            sb.append("销售记录：").append(feedContent);
        }
        if (SFAPreDefineObject.Account.getApiName().equals(arg.getObjectApiName())) {
            try {
                List<IObjectData> csSession = csSessionDataService.selectCustomerServiceSessionData(context.getTenantId(), arg.getObjectApiName(), Lists.newArrayList(arg.getObjectId()));
                String csSessionData = csSessionDataService.getCSSessionData(context, csSession);
                if (StringUtils.isNotBlank(feedContent)) {
                    sb.append("客服会话记录：").append(csSessionData);
                }
            } catch (Exception e) {
                log.error("get customer service session error", e);
            }
        } else if (SFAPreDefineObject.Leads.getApiName().equals(arg.getObjectApiName())) {
            try {
                // 获取营销动态
                IObjectData iObjectData = selectInsightsResults(context.getTenantId(), arg.getObjectApiName(), arg.getObjectId(), "LeadsMarketingSummary");
                if (iObjectData != null) {
                    String jsonString = JSON.toJSONString(iObjectData.get("insights_result"));
                    JSONArray objects = JSON.parseArray(jsonString);
                    if (objects != null && !objects.isEmpty()) {
                        JSONObject firstObject = objects.getJSONObject(0);
                        JSONArray repeatBrowseList = firstObject.getJSONArray("repeatBrowseList");
                        if (repeatBrowseList != null && !repeatBrowseList.isEmpty()) {
                            sb.append("\n营销动态：反复浏览列表：").append(repeatBrowseList.toJSONString()).append(", 活跃时间：").append(firstObject.getString("activityTime"));
                        }
                    }
                }
            } catch (Exception e) {
                log.error("get customer service session error", e);
            }
        }

        try {
            // 通话记录
            List<IObjectData> serviceRecordObj = csSessionDataService.selectServiceRecordData(context.getTenantId(), arg.getObjectApiName(), Lists.newArrayList(arg.getObjectId()));
            String recordString = csSessionDataService.getServiceRecordString(serviceRecordObj);
            if (StringUtils.isNotBlank(recordString)) {
                sb.append("\n通话记录：").append(recordString);
            }
        } catch (Exception e) {
            log.error("get service record error", e);
        }
        String content = sb.toString();
        if (StringUtils.isBlank(content)) {
            result.setDataList(getNoneDataList());
            return result;
        }
        Object aiRet = getAIRet(context, content,getApiName());
        log.warn("LeadsBANTInsights getObjectData content:{}, aiRet:{}", content, aiRet);
        List<ObjectDataDocument> dataList = getDataListFixedInvalidJSON(context, "我需要的json格式如下：" + JSON_FORMAT, aiRet.toString());
        result.setDataList(dataList);
        fillStatusLabel(dataList);
        saveAIRetToDB(context, arg, JSON.toJSONString(dataList));
        String previewContent = getPreviewContent(dataList);
        result.setPreviewContent(previewContent);
        result.setContent(removeLabel(previewContent));
        return result;
    }

    private void fillStatusLabel(List<ObjectDataDocument> dataList) {
        for (ObjectDataDocument objectDataDocument : dataList) {
            if (objectDataDocument.get("Status") != null) {
                objectDataDocument.put("Status", getStatus(objectDataDocument.get("Status").toString()));
            }
        }
    }

    private String getPreviewContent(List<ObjectDataDocument> dataList) {
        StringBuilder content = new StringBuilder();
        for (ObjectDataDocument data : dataList) {
            content.append(data.get("Category").toString()).append(" - ").append(data.get("CategoryLabel")).append("<br>");
            content.append(data.get("Description")).append("<br>");
            if (data.get("Status") != null) {
                content.append(getStatus(data.get("Status").toString())).append("<br>");
            }
        }
        return content.toString();
    }

    private String getStatus(String status) {
        if ("0".equals(status)) {
            return I18N.text(I18NAIUtil.UNSATISFY);
        } else if ("1".equals(status)) {
            return I18N.text(I18NAIUtil.SATISFY);
        }
        return "";
    }

    private List<ObjectDataDocument> getNoneDataList() {
        List<ObjectDataDocument> dataList = com.beust.jcommander.internal.Lists.newArrayList();
        for (TitleEnum titleEnum : TitleEnum.values()) {
            ObjectDataDocument BANTSummary = new ObjectDataDocument();
            BANTSummary.put("Category", titleEnum.getSection());
            BANTSummary.put("CategoryLabel", I18N.text(titleEnum.getLabel()));
            BANTSummary.put("Description", I18N.text("oaappsrv.common.label.nodate"));
            BANTSummary.put("Status", I18N.text(I18NAIUtil.UNSATISFY));
            dataList.add(BANTSummary);
        }
        return dataList;
    }

    private Map<String, Object> createDescribeMap() {
        Map<String, Object> describe = new HashMap<>();
        Map<String, Object> status = new HashMap<>();
        status.put("label", "状态");
        status.put("apiName", "");
        List<Map<String, String>> statusOptions = new ArrayList<>();
        statusOptions.add(Maps.of("label", I18N.text(I18NAIUtil.SATISFY), "value", "1"));
        statusOptions.add(Maps.of("label", I18N.text(I18NAIUtil.UNSATISFY), "value", "0"));
        status.put("options", statusOptions);
        describe.put("Status", status);
        describe.put("Category", Maps.of("label", "分类"));
        describe.put("Description", Maps.of("label", "描述"));
        describe.put("CategoryLabel", Maps.of("label", "分类标签"));
        return describe;
    }

    private Map<String, Object> createEnDescribeMap() {
        Map<String, Object> describe = new HashMap<>();
        Map<String, Object> status = new HashMap<>();
        status.put("label", "Status");
        status.put("apiName", ""); // assuming the apiName is intentionally left blank
        List<Map<String, String>> statusOptions = new ArrayList<>();
        statusOptions.add(createOptionMap("Satisfied", "1"));
        statusOptions.add(createOptionMap("Not Satisfied", "0"));
        status.put("options", statusOptions);
        describe.put("Status", status);
        describe.put("Category", createLabelMap("Category"));
        describe.put("Description", createLabelMap("Description"));
        describe.put("CategoryLabel", createLabelMap("Category Label"));
        return describe;
    }

    private Map<String, String> createLabelMap(String label) {
        Map<String, String> labelMap = new HashMap<>();
        labelMap.put("label", label);
        return labelMap;
    }

    private Map<String, String> createOptionMap(String label, String value) {
        Map<String, String> optionMap = new HashMap<>();
        optionMap.put("label", label);
        optionMap.put("value", value);
        return optionMap;
    }

    @Override
    public List<ButtonAction> getButtonAction(Agent.Arg arg) {
        List<ButtonAction> buttonActions = Lists.newArrayList();
        buttonActions.addAll(super.getButtonAction(arg));
        buttonActions.add(buildButtonAction(ActionTargetEnum.OBJECT_UPDATE_PROFILE));
        return buttonActions;
    }


    @Getter
    enum TitleEnum {
        B("B", I18NAIUtil.PURCHASE_BUDGET),
        A("A", I18NAIUtil.KEY_DECISION_MAKER),
        N("N", I18NAIUtil.BUSINESS_DEMAND),
        T("T", I18NAIUtil.PURCHASE_TIME);

        private String section;

        private String label;

        TitleEnum(String section, String label) {
            this.section = section;
            this.label = label;
        }

    }
}
