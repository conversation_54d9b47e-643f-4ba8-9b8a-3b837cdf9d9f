package com.facishare.crm.sfa.predefine.service.cpq;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.BomConstraintLineModel;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.cpq.model.BomFormulaModel;
import com.facishare.crm.sfa.predefine.service.cpq.model.CheckBomModel;
import com.facishare.crm.sfa.predefine.service.cpq.model.TriggerBomFormulaModel;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.constant.BomConstraintConstants;
import com.facishare.crm.sfa.utilities.constant.BomCoreConstants;
import com.facishare.crm.sfa.utilities.constant.CPQConstraintConstants;
import com.facishare.crm.sfa.utilities.constant.objectdata.DbSystemRecord;
import com.facishare.crm.sfa.utilities.enums.EnumUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.ProductConstraintUtil;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.enterprise.common.util.JsonUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.CalculateService;
import com.facishare.paas.appframework.core.predef.service.dto.calculate.ExpressionCheck;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.appframework.log.dto.ObjectInfo;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.expression.SimpleExpression;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BomConstraintServiceImpl implements BomConstraintService {
    private static final String BOM_ATTRIBUTE_CONSTRAINT_OBJ = "BomAttributeConstraintObj";
    private static final String BOM_ATTRIBUTE_CONSTRAINT_LINES_OBJ = "BomAttributeConstraintLinesObj";
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;

    private static final Pattern variable_pattern = Pattern.compile("\\$([a-zA-Z0-9_.#]+)\\$");

    private static final String STANDARD_ATTR_FLAG = "#EXT#ATTR#";

    private static final String NON_STANDARD_ATTR_FLAG = "#EXT#NON_ATTR#";
    private static final String FIELD_NAME_SPLIT_FLAG = "\\.";
    private static final String EXPRESSION_ATTRIBUTES = "attributes";
    private static final String EXPRESSION_ATTRIBUTE = "attribute";
    private static final String EXPRESSION_NONATTRIBUTES = "nonAttributes";

    private static final String EXPRESSION_NONATTRIBUTE = "nonAttribute";
    private static final String EXPRESSION_ID = "id";
    private static final String EXPRESSION_VALUE = "value";
    private static final String EXPRESSION_AMOUNT = "amount";

    private static final String EXPRESSION_FIELD_NAME = "field_name";

    private static final String EXPRESSION_ATTRIBUTEVALUES = "attributeValues";

    private static final String FUNCTION_FIELD_NAME = "name";
    private static final String FUNCTION_FIELD_TYPE = "type";
    private static final String FUNCTION_FIELD_VALUE = "value";

    private static final int SWITCH_TYPE_VALUE_1 = 1;
    private static final int SWITCH_TYPE_VALUE_2 = 2;


    @Autowired
    private CalculateService calculateService;

    @Autowired
    private ExpressionCalculateLogicService expressionCalculateLogicService;
    @Override
    public void checkRule(List<IObjectData> detailDataList, User user, IObjectData masterData) {
        if (GrayUtil.bomMasterSlaveMode(user.getTenantId())) {
            checkExit(BomConstraintConstants.CORE_ID,masterData.get(BomConstraintConstants.CORE_ID, String.class), user, masterData.getId());
        }else{
            checkExit(BomConstraintConstants.PRODUCT_ID,masterData.get(BomConstraintConstants.PRODUCT_ID, String.class), user, masterData.getId());
        }
        if (CollectionUtils.isEmpty(detailDataList)) {
            //产品属性约束明细、新增APL代码、约束赋值APL不能同时为空
            if(StringUtils.isBlank(masterData.get(BomConstraintConstants.APL_API_NAME, String.class))
                    && StringUtils.isBlank(masterData.get(BomConstraintConstants.ASSIGN_VALUE_APL, String.class)) ) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_APL_NOT_ALL_EMPTY));
            } else {
                if(StringUtils.isBlank(masterData.get(BomConstraintConstants.CORE_ID, String.class))) {
                    throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_APL_BOM_CAN_NOT_NULL));
                }
                fillProductId(user, masterData);
                return;
            }
        }
        String bomId = masterData.get(BomConstraintConstants.BOM_ID, String.class);
        if (StringUtils.isBlank(bomId)) {
            IObjectData rootBom = ProductConstraintUtil.getRootBom(user, masterData.get(BomConstraintConstants.PRODUCT_ID, String.class), masterData.get(BomConstraintConstants.CORE_ID, String.class));
            bomId = rootBom.getId();
            masterData.set(CPQConstraintConstants.BOM_ID, bomId);
            masterData.set(CPQConstraintConstants.PRODUCT_PK_ID, rootBom.get(BomConstants.FIELD_PRODUCT_ID));
        }
        String rootId = bomId;
        detailDataList.forEach(x -> x.set(BomConstants.FIELD_ROOT_ID, rootId));
        //高级公式配置条件校验，需要用到rootId
        checkFormula(detailDataList);

        List<IObjectData> bomList = queryBomTreeByCond(user, bomId);
        Map<String, IObjectData> bomMap = new HashMap<>();
        Multimap<String, String> groupDataMap = ArrayListMultimap.create();
        for (IObjectData objectData : bomList) {
            bomMap.putIfAbsent(objectData.getId(), objectData);
            if(StringUtils.isNotBlank(objectData.get(BomConstants.FIELD_PRODUCT_GROUP_ID,String.class))&&StringUtils.isNotBlank(objectData.get(BomConstants.FIELD_BOM_PATH,String.class))){
                groupDataMap.put(objectData.get(BomConstants.FIELD_PRODUCT_GROUP_ID,String.class), objectData.get(BomConstants.FIELD_BOM_PATH,String.class));
            }
        }
        Map<Table<String, String, String>, Table<String, String, String>> requiredMap = Maps.newHashMap();
        Map<Table<String, String, String>, Table<String, String, String>> requiredRangeMap = Maps.newHashMap();
        Map<Table<String, String, String>, Table<String, String, String>> exclusionMap = Maps.newHashMap();
        Map<Table<String, String, String>, Table<String, String, String>> msgMap = Maps.newHashMap();
        Map<String,String> groupMap = Maps.newHashMap();
        Table<String, String, String> groupTable = HashBasedTable.create();
        Map<String, String> pathMap = Maps.newHashMap();
        detailDataList.forEach(x -> {
            Set<String> deletedNodeList = Sets.newHashSet();
            String conJson = x.get(BomConstraintConstants.CONDITION_RANGE, String.class);
            String reJson = x.get(BomConstraintConstants.RESULT_RANGE, String.class);
            Integer constraintType = x.get(BomConstraintConstants.CONSTRAINT_TYPE, Integer.class);
            if (StringUtils.isAnyBlank(conJson, reJson)) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_ITEM_REQUIRE));
            }
            List<BomConstraintLineModel> condition = JSON.parseArray(conJson, BomConstraintLineModel.class);
            List<BomConstraintLineModel> result = JSON.parseArray(reJson, BomConstraintLineModel.class);
            Table<String, String, String> conditionTable = HashBasedTable.create();
            Table<String, String, String> resultTable = HashBasedTable.create();
            Table<String, String, String> conditionMsgTable = HashBasedTable.create();
            Table<String, String, String> resultMsgTable = HashBasedTable.create();
            String groupConditionVal = "";
            String groupResultMapVal = "";
            groupConditionVal =createData(bomMap, deletedNodeList, condition, conditionTable, false,conditionMsgTable,pathMap,groupConditionVal);
            groupResultMapVal = createData(bomMap, deletedNodeList, result, resultTable, true,resultMsgTable,pathMap,groupResultMapVal);
            checkDeletedNode(user, deletedNodeList);
            msgMap.put(conditionTable,conditionMsgTable);
            if(StringUtils.isBlank(groupConditionVal)&&StringUtils.isBlank(groupResultMapVal)){
                if (conditionTable.isEmpty() || resultTable.isEmpty()) {
                    throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_ITEM_REQUIRE));
                }
            }else{
                if(StringUtils.isBlank(groupConditionVal)||StringUtils.isBlank(groupResultMapVal)){
                    throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_ITEM_REQUIRE));
                }
                groupMap.put(groupConditionVal, groupResultMapVal);
                String exist = groupTable.put(groupConditionVal, groupResultMapVal, constraintType+"");
                if (StringUtils.isNotBlank(exist)) {
                    if(Objects.equals(exist,constraintType+"")){
                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_LINES_EXIST));
                    }else{
                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_LINES_GROUP_CONFLICT));
                    }
                }
            }
            Map<String, String> conRootRowMap = conditionTable.row(rootId);
            Map<String, String> reRootRowMap = resultTable.row(rootId);
            //根节点校验
            if (MapUtils.isNotEmpty(conRootRowMap)) {
                checkRootNode(conRootRowMap, reRootRowMap);
            }

            boolean equal = hasDuplicateKey(conditionTable, resultTable, rootId);
            if(equal){
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_NODE_CONSTRAINT_CANNOT_EQUAL));
            }
            //子节点不能约束父节点
            resultTable.rowKeySet().parallelStream().forEach(k1 -> conditionTable.rowKeySet().parallelStream().forEach(k2 -> {
                if (k2.startsWith(k1.concat("."))) {
                    throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_CHILD_NODES_CANNOT_CONSTRAIN_PARENT_NODES));
                }
            }));
            if(!conditionTable.isEmpty()&&!resultTable.isEmpty()){
                //同一约束类型的约束产品不能相同
                if (constraintType == EnumUtil.ConstraintType.must.getValue()) {
                    if (Objects.nonNull(requiredMap.putIfAbsent(conditionTable, resultTable))) {
                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_SAME_CONSTRAINT_TYPE_BOM_CONSTRAINT_LINES_EXIST));
                    }
                }
                if (constraintType == EnumUtil.ConstraintType.unAllow.getValue()) {
                    if (Objects.nonNull(exclusionMap.putIfAbsent(conditionTable, resultTable))) {
                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_SAME_CONSTRAINT_TYPE_BOM_CONSTRAINT_LINES_EXIST));
                    }
                }
                if (constraintType == EnumUtil.ConstraintType.range.getValue()) {
                    if (Objects.nonNull(requiredRangeMap.putIfAbsent(conditionTable, resultTable))) {
                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_SAME_CONSTRAINT_TYPE_BOM_CONSTRAINT_LINES_EXIST));
                    }
                }
            }

        });
        checkGroupRule(groupDataMap, requiredMap, requiredRangeMap, exclusionMap, groupMap);
        for (Map.Entry<Table<String, String, String>, Table<String, String, String>> entry : exclusionMap.entrySet()) {
            checkCycle(entry, exclusionMap);
            Table<String, String, String> requiredResultTable = entry.getKey() ;
            int cut = 0;
            do {
                checkConflict(entry, requiredMap.get(requiredResultTable),msgMap,pathMap);
                requiredResultTable = requiredMap.get(requiredResultTable);
                cut++;
            }while (Objects.nonNull(requiredResultTable)&&cut<10);
            checkConflict(entry, requiredRangeMap.get(entry.getKey()),msgMap,pathMap);
        }
    }

    private void checkFormula(List<IObjectData> detailDataList) {
        if(CollectionUtils.isEmpty(detailDataList)){
            return;
        }
        List<BomConstraintLineModel> allRangeResultList = new ArrayList<>();
        detailDataList.forEach(data -> {
            List<BomConstraintLineModel> tempRangeResultList = JSON.parseArray(data.get(BomConstraintConstants.RESULT_RANGE, String.class), BomConstraintLineModel.class);
            List<BomConstraintLineModel> tempConditionList = JSON.parseArray(data.get(BomConstraintConstants.CONDITION_RANGE, String.class), BomConstraintLineModel.class);
            allRangeResultList.addAll(tempRangeResultList);
            //每条约束规则中配置的高级公式， 校验对应的子件是否都选中
            checkBomSelected(tempRangeResultList, tempConditionList, data.get(BomConstants.FIELD_ROOT_ID, String.class));
        });
        List<String> expressions = allRangeResultList.stream()
                .filter(x->CollectionUtils.isNotEmpty(x.getFormula()))
                .flatMap(x -> x.getFormula().stream())
                .map(BomConstraintLineModel.FormulaInfo::getExpression)
                .collect(Collectors.toList());
        Set<String> allInExpressionBomIds = parseBomIdsIncludeAmountField(expressions);

        //bom_id+"."+amount 作为key
        Set<String> existExpressionBomIds = Sets.newHashSet();
        Map<String, String> productNameMap = Maps.newHashMap();
        for(BomConstraintLineModel line : allRangeResultList) {
            if(CollectionUtils.isNotEmpty(line.getFormula())) {
                existExpressionBomIds.add(line.getBom_id()+".amount");
                productNameMap.put(line.getBom_id()+".amount", line.getProduct_name());
            }
        }
        //如果有交集，则说明参与高级公式计算的子件，又配置了高级公式
        if(!Collections.disjoint(allInExpressionBomIds, existExpressionBomIds)) {
            //保留交集部分
            allInExpressionBomIds.retainAll(existExpressionBomIds);
            StringBuilder productsName = new StringBuilder();
            allInExpressionBomIds.stream().forEach(x -> productsName.append(productNameMap.get(x)).append(","));
            productsName.deleteCharAt(productsName.length() - 1);
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_FORMULA_CANNOT_BE_BOTH_IN_EXPRESSION, productsName.toString()));
        }
    }

    /**
     * 每条约束规则中配置的高级公式， 校验对应的子件是否都选中
     * @param tempRangeResultList
     */
    private void checkBomSelected(List<BomConstraintLineModel> tempRangeResultList, List<BomConstraintLineModel> tempConditionList, String rootBomId) {
        if(CollectionUtils.isEmpty(tempRangeResultList) || CollectionUtils.isEmpty(tempConditionList)){
            return;
        }
        //当前约束规则中所选择的子件
        Set<String> bomIds = Sets.newHashSet(tempRangeResultList.stream().map(BomConstraintLineModel::getBom_id).collect(Collectors.toSet()));
        bomIds.addAll(tempConditionList.stream().map(BomConstraintLineModel::getBom_id).collect(Collectors.toSet()));
        //当前配置的高级公式中，所涉及到的子件
        List<String> formulaExpression = tempRangeResultList.stream()
                .filter(x->CollectionUtils.isNotEmpty(x.getFormula()))
                .flatMap(x -> x.getFormula().stream())
                .map(BomConstraintLineModel.FormulaInfo::getExpression)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(formulaExpression)){
            return;
        }
        Set<String> expressionBomIds = parseBomIds(formulaExpression);
        //去掉母件ID，母件都是会勾选的
        expressionBomIds.removeIf(x->Objects.equals(x, rootBomId));
        if(!bomIds.containsAll(expressionBomIds)) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_FORMULA_SELECTED_BOM_NOT_EXIST));
        }
    }

    /**
     * 从表达式中解析出所有使用了bomId.amount的子件
     * @param formulaExpression
     * @return
     */
    private Set<String> parseBomIdsIncludeAmountField(List<String> formulaExpression) {
        Set<String> expressionBomIds = Sets.newHashSet();
        for (String expression : formulaExpression) {
            if(StringUtils.isBlank(expression)){
                continue;
            }
            Matcher matcher = variable_pattern.matcher(expression);
            //将表达式里的变量替换掉，调用底层提供的接口进行表达式校验
            while (matcher.find()) {
                String variable = matcher.group(1);
                // 确定分割标志
                String splitFlag = getSplitFlag(variable);
                String[] bomAndValues = variable.split(splitFlag);
                if(bomAndValues.length == 2 && EXPRESSION_AMOUNT.equals(bomAndValues[1])){
                    expressionBomIds.add(bomAndValues[0]+"."+EXPRESSION_AMOUNT);
                }
            }
        }
        return expressionBomIds;
    }

    /**
     * 从表达式中解析出所有的bomID
     * @param formulaExpression
     * @return
     */
    private Set<String> parseBomIds(List<String> formulaExpression) {
        Set<String> expressionBomIds = Sets.newHashSet();
        for (String expression : formulaExpression) {
            if(StringUtils.isBlank(expression)){
                continue;
            }
            Matcher matcher = variable_pattern.matcher(expression);
            //将表达式里的变量替换掉，调用底层提供的接口进行表达式校验
            while (matcher.find()) {
                String variable = matcher.group(1);
                String splitFlag = getSplitFlag(variable);
                String[] bomAndValues = variable.split(splitFlag);
                if(bomAndValues.length == 2){
                    expressionBomIds.add(bomAndValues[0]);
                }
            }
        }
        return expressionBomIds;
    }

    private String getSplitFlag(String variable) {
        if(StringUtils.isBlank(variable)) {
            return FIELD_NAME_SPLIT_FLAG;
        }
        String splitFlag = null;
        if (variable.contains(STANDARD_ATTR_FLAG)) {
            splitFlag = STANDARD_ATTR_FLAG;
        } else if (variable.contains(NON_STANDARD_ATTR_FLAG)) {
            splitFlag = NON_STANDARD_ATTR_FLAG;
        } else {
            splitFlag = FIELD_NAME_SPLIT_FLAG;
        }
        return splitFlag;
    }
    private void fillProductId(User user, IObjectData masterData) {
        if(StringUtils.isEmpty(masterData.get(BomConstraintConstants.PRODUCT_ID, String.class))
                && StringUtils.isNotEmpty(masterData.get(BomConstraintConstants.CORE_ID, String.class))) {
            List<IObjectData> dataList  = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(masterData.get(BomConstraintConstants.CORE_ID, String.class)), BomCoreConstants.DESC_API_NAME);
            if(CollectionUtils.isNotEmpty(dataList)) {
                masterData.set(BomConstraintConstants.PRODUCT_ID, dataList.get(0).get(BomCoreConstants.FIELD_PRODUCT_ID, String.class));
            }
        }
    }

    private boolean hasDuplicateKey(Table<String, String, String> conditionTable, Table<String, String, String> resultTable, String rootId) {
        // 获取table1的key集合
        Map<String, Map<String, String>> conditionMap = conditionTable.rowMap();
        for (Map.Entry<String, Map<String, String>> x : conditionMap.entrySet()) {
            if (Objects.equals(x.getValue(), resultTable.row(x.getKey()))&&!Objects.equals(rootId,x.getKey())) {
                return true ;
            }
        }
        return false;
    }

    private void checkGroupRule(Multimap<String, String> groupDataMap, Map<Table<String, String, String>, Table<String, String, String>> requiredMap, Map<Table<String, String, String>, Table<String, String, String>> requiredRangeMap, Map<Table<String, String, String>, Table<String, String, String>> exclusionMap, Map<String, String> groupMap) {
        groupMap.forEach((left, right) -> {
            Collection<String> leftPathList = groupDataMap.get(left);
            Collection<String> rightPathList = groupDataMap.get(right);
            if (CollectionUtils.isNotEmpty(leftPathList)&&CollectionUtils.isNotEmpty(rightPathList)) {
                checkGroupRule(requiredMap, leftPathList, rightPathList);
                checkGroupRule(exclusionMap, leftPathList, rightPathList);
                checkGroupRule(requiredRangeMap, leftPathList, rightPathList);
            }
        });
    }

    private void checkGroupRule(Map<Table<String, String, String>, Table<String, String, String>> constraintMap, Collection<String> leftPathList, Collection<String> rightPathList) {
        for (Map.Entry<Table<String, String, String>, Table<String, String, String>> entry : constraintMap.entrySet()) {
            if(startWith(entry.getKey().rowKeySet(),leftPathList)&&startWith(entry.getValue().rowKeySet(),rightPathList)){
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_LINES_GROUP_NODE_CONFLICT));
            }
        }
    }

    private boolean startWith(Set<String> pathSet,Collection<String> pathList){
        boolean flag = false ;
        if (CollectionUtils.isNotEmpty(pathSet)) {
            String path = pathSet.iterator().next();
            for (String p : pathList) {
                if (path.startsWith(p)) {
                    flag = true;
                    break;
                }
            }
        }
        return flag;
    }
    private void checkConflict(Map.Entry<Table<String, String, String>, Table<String, String, String>> entry, Table<String, String, String> requireResultTable,Map<Table<String, String, String>, Table<String, String, String>> msgMap,Map<String, String> pathMap) {
        if (Objects.isNull(requireResultTable)) {
            return;
        }
        Table<String, String, String> exclusionResultTable = entry.getValue();
        //相同约束产品的被约束规则校验
        requireResultTable.rowKeySet().forEach(r -> {
            exclusionResultTable.rowKeySet().forEach(er -> {
                if (r.startsWith(er+".") && exclusionResultTable.row(er).containsKey("")) {
                    if (msgMap.get(entry.getKey()).isEmpty()) {
                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_LINES_PARENT_CHILD_NODE_CONFLICT));
                    }else{
                        StringBuilder msg = new StringBuilder();
                        String tips = createMsg(msgMap.get(entry.getKey()), msg);
                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_LINES_PARENT_CHILD_NODE_CONFLICT_NEW,tips,pathMap.get(er),pathMap.get(r)));
                    }
                }
            });
            Map<String, String> exclusionMap = exclusionResultTable.row(r);
            Map<String, String> requireMap = requireResultTable.row(r);
            if (exclusionMap.equals(requireMap)) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_LINES_NODE_CONFLICT));
            }else{
                Set<String> exclusionKeys = exclusionMap.keySet();
                if (exclusionKeys.equals(requireMap.keySet())) {
                    boolean flag = true;
                    for (Map.Entry<String, String> keyValue : exclusionMap.entrySet()) {
                        List<String> exclusionList = Splitter.on(",").omitEmptyStrings().splitToList(keyValue.getValue());
                        List<String> requireList = Splitter.on(",").omitEmptyStrings().splitToList(requireMap.get(keyValue.getKey()));
                        if (!exclusionList.containsAll(requireList)) {
                            flag = false;
                            break;
                        }
                    }
                    if(flag){
                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_LINES_NODE_CONFLICT));
                    }
                }
            }
        });
    }

    private void checkCycle(Map.Entry<Table<String, String, String>, Table<String, String, String>> entry, Map<Table<String, String, String>, Table<String, String, String>> dataMap) {
        Table<String, String, String> headNodeKey = entry.getKey();
        Table<String, String, String> nextNodeValue = dataMap.get(entry.getValue());
        if (Objects.isNull(nextNodeValue) || headNodeKey.equals(nextNodeValue)) {
            return;
        }
        checkCycle(entry, nextNodeValue, dataMap);
    }

    private void checkCycle(Map.Entry<Table<String, String, String>, Table<String, String, String>> headEntry, Table<String, String, String> nextNode, Map<Table<String, String, String>, Table<String, String, String>> dataMap) {
        Table<String, String, String> nextNodeValue = dataMap.get(nextNode);
        if (Objects.isNull(nextNodeValue)) {
            return;
        }
        if (nextNodeValue.equals(headEntry.getKey())) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_LINES_CYCLE));
        }
        checkCycle(headEntry, nextNodeValue, dataMap);
    }


    private void checkRootNode(Map<String, String> conRootRowMap, Map<String, String> reRootRowMap) {
        conRootRowMap.forEach((key, value) -> {
            if (StringUtils.isAnyBlank(key, value)) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_ROOT_BOM_CONSTRAINT_DATA_ERROR));
            }
            if (MapUtils.isNotEmpty(reRootRowMap)) {
                if (Objects.equals(value, reRootRowMap.get(key))) {
                    throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_ROOT_BOM_CONSTRAINT_CANNOT_EQUAL));
                }
            }
        });
        reRootRowMap.forEach((key, value) -> {
            if (StringUtils.isAnyBlank(key, value)) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_ROOT_BOM_CONSTRAINT_DATA_ERROR));
            }
        });
    }

    private void checkDeletedNode(User user, Set<String> deletedNodeList) {
        if (CollectionUtils.isEmpty(deletedNodeList)) {
            return;
        }
        Set<String> productIds = Sets.newHashSet();
        StringJoiner sj = new StringJoiner(";");
        deletedNodeList.forEach(d -> productIds.addAll(Splitter.on(".").splitToList(d)));
        Map<String, String> productInfo = ProductConstraintUtil.getObjectName(user, productIds, Utils.PRODUCT_API_NAME);
        deletedNodeList.forEach(d -> {
            String productName = Splitter.on(".").splitToList(d).stream().map(productInfo::get).collect(Collectors.joining(">"));
            sj.add(productName);
        });
        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_NODE_DELETED, sj.toString()));
    }

    private String createData(Map<String, IObjectData> bomMap, Set<String> deletedNodeList, List<BomConstraintLineModel> dataList, Table<String, String, String> dataTable, boolean flag,Table<String, String, String> msgTable,Map<String, String> pathMap,String groupVal) {
        for (BomConstraintLineModel d : dataList) {
            if (StringUtils.isNotBlank(d.getGroup_id())) {
                groupVal = d.getGroup_id();
            } else {
                String bomPath = Optional.ofNullable(bomMap.get(d.getBom_id())).map(x -> x.get(BomConstants.FIELD_BOM_PATH, String.class)).orElse("");
                if (StringUtils.isBlank(d.getProduct_path())) {
                    throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
                }
                if (StringUtils.isBlank(bomPath)) {
                    deletedNodeList.add(d.getProduct_path());
                } else {
                    pathMap.put(bomPath, d.getProduct_name());
                    List<BomConstraintLineModel.Attribute> attribute = d.getAttribute();
                    List<BomConstraintLineModel.NonAttribute> nonstandardAttribute = d.getNonstandardAttribute();
                    if (flag && bomMap.get(d.getBom_id()).get(BomConstants.FIELD_IS_REQUIRED, Boolean.class, false) &&
                            !Objects.equals(d.getBom_id(), bomMap.get(d.getBom_id()).get(BomConstants.FIELD_ROOT_ID)) && CollectionUtils.isEmpty(attribute) && CollectionUtils.isEmpty(nonstandardAttribute)) {
                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_LINES_RIGHT_CANNOT_CHOOSE_REQUIRE));
                    }
                    String attributeValueOld;
                    if (CollectionUtils.isEmpty(attribute) && CollectionUtils.isEmpty(nonstandardAttribute)) {
                        attributeValueOld = dataTable.put(bomPath, "", "");
                        msgTable.put(d.getProduct_name(), "", "");
                        if (Objects.nonNull(attributeValueOld)) {
                            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_LINES_EXIST));
                        }
                    } else {
                        if (CollectionUtils.isNotEmpty(attribute)) {
                            for (BomConstraintLineModel.Attribute attr : attribute) {
                                if (Objects.nonNull(attr)) {
                                    if (CollectionUtils.isEmpty(attr.getAttribute_values())) {
                                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_ROOT_BOM_CONSTRAINT_DATA_ATTR_VALUE_ERROR));
                                    }
                                    attributeValueOld = dataTable.put(bomPath, attr.getId(),
                                            attr.getAttribute_values().stream().map(BomConstraintLineModel.Attribute.AttributeValues::getId).collect(Collectors.joining(",")));
                                    msgTable.put(d.getProduct_name(), attr.getName(), attr.getAttribute_values().stream().map(BomConstraintLineModel.Attribute.AttributeValues::getName).collect(Collectors.joining(",")));
                                    if (Objects.nonNull(attributeValueOld)) {
                                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_LINES_EXIST));
                                    }
                                }
                            }
                        }
                        if (CollectionUtils.isNotEmpty(nonstandardAttribute)) {
                            for (BomConstraintLineModel.NonAttribute attr : nonstandardAttribute) {
                                if (Objects.nonNull(attr)) {
                                    if (CollectionUtils.isEmpty(attr.getAttribute_values())) {
                                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_ROOT_BOM_CONSTRAINT_DATA_ATTR_VALUE_ERROR));
                                    }
                                    String minValue = attr.getAttribute_values().get(0).getMinValue();
                                    String maxValue = attr.getAttribute_values().get(0).getMaxValue();
                                    if (StringUtils.isAllBlank(minValue, maxValue)) {
                                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_ROOT_BOM_CONSTRAINT_DATA_ATTR_VALUE_ERROR));
                                    }
                                    if (StringUtils.isNoneBlank(minValue, maxValue) && toDouble(minValue, 0.0).compareTo(toDouble(maxValue, Double.MAX_VALUE)) > 0) {
                                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_ROOT_BOM_CONSTRAINT_NON_ATTR_VALUE_ERROR));
                                    }
                                    attributeValueOld = dataTable.put(bomPath, attr.getId(), minValue + "-" + maxValue);
                                    msgTable.put(d.getProduct_name(), attr.getName(), minValue + "-" + maxValue);
                                    if (Objects.nonNull(attributeValueOld)) {
                                        throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CONSTRAINT_LINES_EXIST));
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return groupVal;
    }


    private void checkExit(String field,String pkgId, User user, String masterId) {
        if (StringUtils.isNotBlank(pkgId)) {
            SearchTemplateQuery query = ProductConstraintUtil.getQuery(field, pkgId, 1, masterId);
            List<IObjectData> dataList = serviceFacade.findBySearchQueryIgnoreAll(user, BomConstraintConstants.API_NAME, query).getData();
            if (CollectionUtils.isNotEmpty(dataList)) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_PRODUCT_CONSTRAINT_EXIST_NEW));
            }
        }
    }


    private List<IObjectData> queryBomTreeByCond(User user, String rootId) {
        SearchTemplateQuery query = ProductConstraintUtil.getQuery(BomConstants.FIELD_ROOT_ID, rootId, 2000);
        List<IObjectData> bomList = serviceFacade.findBySearchQueryIgnoreAll(user, Utils.BOM_API_NAME, query).getData();
        if (CollectionUtils.isEmpty(bomList)) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_ROOT_NODE_NOT_FOUND));
        }
        return bomList;
    }

    @Override
    public List<IObjectData> findBomConstrainByRootBomId(User user, List<String> bomIds, boolean includeInvalid) {
        if (CollectionUtils.isEmpty(bomIds)) {
            return Collections.emptyList();
        }
        SearchTemplateQuery searchTemplateQuery = SoCommonUtils.buildSearchTemplateQuery();
        if (GrayUtil.isBOMSearchDB(user.getTenantId())) {
            searchTemplateQuery.setSearchSource("db");
        }
        SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.IN, CPQConstraintConstants.BOM_ID, bomIds);
        if (includeInvalid) {
            SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.IN, DbSystemRecord.IS_DELETED, Lists.newArrayList(SystemConstants.IsDeletedStatus.Normal.value, SystemConstants.IsDeletedStatus.Invalid.value));
        } else {
            SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.EQ, CPQConstraintConstants.ACTIVE_STATUS, "true");
            SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.EQ, DbSystemRecord.LIFE_STATUS, SystemConstants.LifeStatus.Normal.value);
            SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.EQ, DbSystemRecord.IS_DELETED, SystemConstants.IsDeletedStatus.Normal.value);
        }
        QueryResult<IObjectData> productConstraintList = serviceFacade.findBySearchQueryIgnoreAll(user, BOM_ATTRIBUTE_CONSTRAINT_OBJ, searchTemplateQuery);
        if (CollectionUtils.isEmpty(productConstraintList.getData())) {
            return Collections.emptyList();
        }
        return productConstraintList.getData();
    }

    @Override
    public List<IObjectData> findBomConstraintLinesByMasterId(User user, List<String> masterIds) {
        if (CollectionUtils.isEmpty(masterIds)) {
            return Collections.emptyList();
        }
        SearchTemplateQuery searchTemplateQuery = SoCommonUtils.buildSearchTemplateQuery();
        if (GrayUtil.isBOMSearchDB(user.getTenantId())) {
            searchTemplateQuery.setSearchSource("db");
        }
        SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.IN, BomConstraintConstants.BOM_ATTRIBUTE_CONSTRAINT_ID, masterIds);
        SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.EQ, "life_status", SystemConstants.LifeStatus.Normal.value);
        SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.EQ, "is_deleted", SystemConstants.IsDeletedStatus.Normal.value);
        QueryResult<IObjectData> productConstraintList = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, BOM_ATTRIBUTE_CONSTRAINT_LINES_OBJ, searchTemplateQuery, Lists.newArrayList(BomConstraintConstants.CONDITION_RANGE, BomConstraintConstants.RESULT_RANGE, BomConstraintConstants.CONSTRAINT_TYPE,BomConstraintConstants.ROOT_ID));
        if (CollectionUtils.isEmpty(productConstraintList.getData())) {
            return Collections.emptyList();
        }
        return productConstraintList.getData();
    }

    @Override
    public CheckBomModel.Result checkBomConstraint(User user, CheckBomModel.Args args) {
        List<CheckBomModel.Arg> treeList = args.getTreeList();
        if (CollectionUtils.isEmpty(treeList)) {
            return CheckBomModel.Result.builder().success(true).build();
        }
        CheckBomModel.Result result = null;
        for (CheckBomModel.Arg arg : treeList) {
            //优先走APL校验
            if (CollectionUtils.isNotEmpty(arg.getAplList()) && CollectionUtils.isNotEmpty(arg.getSelectBomList())) {
                result = aplCheckBomConstraint(user, arg.getAplList(), arg.getSelectBomList());
                //如果APL校验不通过，则直接返回，如果通过，则继续走BOM校验
                if(result != null && !result.isSuccess()) {
                    return result;
                }
            }
        }
        StringJoiner msg = new StringJoiner(";\n");
        boolean msgFlag = false;
        Map<String, List<IObjectData>> bomConstraintLineMap = Maps.newHashMap();
        List<String> rootIds = treeList.stream().filter(x -> CollectionUtils.isEmpty(x.getBomAttributeConstraintLines())&&StringUtils.isNotBlank(x.getRootId())).map(CheckBomModel.Arg::getRootId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(rootIds)) {
            List<IObjectData> masterList = findBomConstrainByRootBomId(user, rootIds, false);
            if (CollectionUtils.isNotEmpty(masterList)) {
                List<IObjectData> slaveList = findBomConstraintLinesByMasterId(user, masterList.stream().map(DBRecord::getId).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(slaveList)) {
                    ProductConstraintUtil.handleData(slaveList,user);
                    bomConstraintLineMap = slaveList.stream().collect(Collectors.groupingBy(x -> x.get(BomConstants.FIELD_ROOT_ID, String.class)));
                }
            }
        }
        for (CheckBomModel.Arg arg : treeList) {
            List<CheckBomModel.BomInfo> selectBomList = arg.toBomInfoList();
            List<ObjectDataDocument> tmpBomConstraintLinesList = arg.getBomAttributeConstraintLines();
            String rootId = arg.getRootId();
            if (CollectionUtils.isEmpty(selectBomList) || StringUtils.isBlank(rootId)) {
                continue;
            }
            List<IObjectData> bomConstraintLinesList;
            if (CollectionUtils.isEmpty(tmpBomConstraintLinesList)) {
                bomConstraintLinesList = bomConstraintLineMap.get(rootId);
                if(CollectionUtils.isEmpty(bomConstraintLinesList)){
                    continue;
                }
            }else{
                bomConstraintLinesList = ObjectDataDocument.ofDataList(tmpBomConstraintLinesList);
            }
            Multimap<String, String> groupMap = ArrayListMultimap.create();
            Map<String, String> bomMap = Maps.newHashMap();
            Map<String, CheckBomModel.BomInfo> selectBomMap = new HashMap<>();
            for (CheckBomModel.BomInfo bomInfo : selectBomList) {
                String currentRootNewPath = StringUtils.isBlank(bomInfo.getCurrent_root_new_path())?arg.getRootId():bomInfo.getCurrent_root_new_path();
                selectBomMap.putIfAbsent(currentRootNewPath+bomInfo.getBomId(), bomInfo);
                if (StringUtils.isNotBlank(bomInfo.getProduct_group_id())) {
                    groupMap.put(currentRootNewPath+bomInfo.getProduct_group_id(),bomInfo.getBomId());
                }
            }
            Table<String, String, String> requireTable = HashBasedTable.create();
            Table<String, String, String> requireRangeTable = HashBasedTable.create();
            Table<String, String, String> exclusionTable = HashBasedTable.create();
            bomConstraintLinesList.forEach(x -> {
                String currentRootNewPath = StringUtils.isBlank(x.get("current_root_new_path",String.class))?arg.getRootId():x.get("current_root_new_path",String.class);
                List<BomConstraintLineModel> conditionList = JSON.parseArray(x.get(BomConstraintConstants.CONDITION_RANGE, String.class), BomConstraintLineModel.class);
                List<BomConstraintLineModel> resultList = JSON.parseArray(x.get(BomConstraintConstants.RESULT_RANGE, String.class), BomConstraintLineModel.class);
                int type = x.get(BomConstraintConstants.CONSTRAINT_TYPE, Integer.class);
                if (CollectionUtils.isNotEmpty(conditionList) && CollectionUtils.isNotEmpty(resultList)) {
                    boolean flag = matchConstraintLine(selectBomMap, conditionList,groupMap,bomMap,currentRootNewPath);
                    if (flag) {
                        if (log.isDebugEnabled()) {
                            log.debug("匹配约束产品行:{}-{}-{}", rootId, x.getId(), x.get(BomConstraintConstants.CONDITION_RANGE, String.class));
                        }
                        matchConstrainedLine(selectBomMap, requireTable, exclusionTable, resultList, type, requireRangeTable,groupMap,bomMap,currentRootNewPath);
                    }
                }
            });
            StringBuilder requireBuild = new StringBuilder();
            StringBuilder requireRangeBuild = new StringBuilder();
            StringBuilder exclusionBuild = new StringBuilder();
            String requireMsg = I18N.text("BomAttributeConstraintLinesObj.field.constraint_type.option.1");
            String exclusionMsg = I18N.text("BomAttributeConstraintLinesObj.field.constraint_type.option.2");
            String requireRangeMsg = I18N.text("BomAttributeConstraintLinesObj.field.constraint_type.option.3");
            requireBuild.append(requireMsg);
            exclusionBuild.append(exclusionMsg);
            requireRangeBuild.append(requireRangeMsg);
            requireMsg = createMsg(requireTable, requireBuild);
            exclusionMsg = createMsg(exclusionTable, exclusionBuild);
            requireRangeMsg = createMsg(requireRangeTable, requireRangeBuild);
            if (!StringUtils.isAllBlank(requireMsg, exclusionMsg,requireRangeMsg)) {
                msgFlag = true;
                msg.add(StringUtils.defaultIfBlank(arg.getProductName(),"").concat(":{").concat(requireMsg).concat(requireRangeMsg).concat(exclusionMsg).concat("}"));
            }
        }
        if (msgFlag) {
            return CheckBomModel.Result.builder().success(false).msg(msg.toString()).build();
        }
        return CheckBomModel.Result.builder().success(true).build();
    }

    private CheckBomModel.Result aplCheckBomConstraint(User user, List<String> aplList, List<ObjectDataDocument> bomList) {
        if(CollectionUtils.isNotEmpty(aplList)) {
            Set<String> aplSet = new HashSet<>(aplList);
            for (String apl : aplSet) {
                Map<String, List<IObjectData>> details = Maps.newHashMap();
                details.put("BOMObj", ObjectDataDocument.ofDataList(bomList));
                IUdefFunction function = serviceFacade.getFunctionLogicService().findUDefFunction(user
                        , apl
                        , BomConstraintConstants.API_NAME);
                if (org.springframework.util.ObjectUtils.isEmpty(function)) {
                    throw new ValidateException(I18N.text(I18NKey.NOT_FIND_FUNCTION, apl));
                }
                RunResult runResult = serviceFacade.getFunctionLogicService().executeUDefFunction(user, function, null, null, details);
                if(runResult.isSuccess()) {
                    Map checkMap = (Map)runResult.getFunctionResult();
                    Boolean success = (Boolean)checkMap.getOrDefault("success", Boolean.TRUE);
                    String msg = (String)checkMap.getOrDefault("errorMessage", "ok");
                    if(!success.booleanValue()) {
                        return CheckBomModel.Result.builder().success(success.booleanValue()).msg(msg).build();
                    }
                } else {
                    return CheckBomModel.Result.builder().success(false).msg(runResult.getErrorInfo()).build();
                }
            }
        }
        return CheckBomModel.Result.builder().success(true).msg("ok").build();
    }

    private void matchConstrainedLine(Map<String, CheckBomModel.BomInfo> selectBomMap, Table<String, String, String> requireTable, Table<String, String, String> exclusionTable, List<BomConstraintLineModel> resultList, int type, Table<String, String, String> requireRangeTable, Multimap<String, String> groupMap, Map<String, String> bomMap, String currentRootNewPath) {
        boolean groupFlag = false ;
        for (BomConstraintLineModel r : resultList) {
            if(StringUtils.isNotBlank(r.getGroup_id())){
                Collection<String> bomPathList = groupMap.get(currentRootNewPath+r.getGroup_id());
                if (CollectionUtils.isNotEmpty(bomPathList)) {
                    for (String p : bomPathList) {
                        if (Objects.nonNull(selectBomMap.get(currentRootNewPath+p))) {
                            if (type == EnumUtil.ConstraintType.unAllow.getValue()) {
                                exclusionTable.put(r.getGroup_name(), "", "");
                            }
                            groupFlag = true;
                        }
                    }
                }
                if(!groupFlag){
                    if (type == EnumUtil.ConstraintType.must.getValue()) {
                        requireTable.put(r.getGroup_name(), "", "");
                    }else if(type == EnumUtil.ConstraintType.range.getValue()){
                        requireRangeTable.put(r.getGroup_name(), "", "");
                    }
                }
                continue;
            }
            CheckBomModel.BomInfo selectBomInfo = selectBomMap.get(currentRootNewPath+r.getBom_id());
            List<BomConstraintLineModel.Attribute> configAttributeList = r.getAttribute();
            List<BomConstraintLineModel.NonAttribute> configNonstandardAttributeList = r.getNonstandardAttribute();
            if (Objects.isNull(selectBomInfo)) {
                if (type == EnumUtil.ConstraintType.must.getValue()) {
                    requireTable.put(r.getProduct_name(), "", "");
                    CollectionUtils.emptyIfNull(configAttributeList).forEach(d ->
                            CollectionUtils.emptyIfNull(d.getAttribute_values()).forEach(v ->
                                    requireTable.put(r.getProduct_name(), d.getName(), v.getName())));
                    CollectionUtils.emptyIfNull(configNonstandardAttributeList).forEach(d ->
                            CollectionUtils.emptyIfNull(d.getAttribute_values()).forEach(v ->
                                    requireTable.put(r.getProduct_name(), d.getName(), StringUtils.defaultString(v.getMinValue(), "").concat("-").concat(StringUtils.defaultString(v.getMaxValue(), "")))));
                }
                if (type == EnumUtil.ConstraintType.range.getValue()) {
                    CollectionUtils.emptyIfNull(configAttributeList).forEach(d -> requireRangeTable.put(r.getProduct_name(), d.getName(),
                            CollectionUtils.emptyIfNull(d.getAttribute_values())
                                    .stream()
                                    .map(BomConstraintLineModel.Attribute.AttributeValues::getName)
                                    .collect(Collectors.joining(","))));
                    CollectionUtils.emptyIfNull(configNonstandardAttributeList).forEach(d ->
                            CollectionUtils.emptyIfNull(d.getAttribute_values()).forEach(v ->
                                    requireRangeTable.put(r.getProduct_name(), d.getName(), StringUtils.defaultString(v.getMinValue(), "").concat("-").concat(StringUtils.defaultString(v.getMaxValue(), "")))));
                }
                continue;
            }
            if(CollectionUtils.isEmpty(configAttributeList)&&CollectionUtils.isEmpty(configNonstandardAttributeList)){
                if (type == EnumUtil.ConstraintType.unAllow.getValue()) {
                    exclusionTable.put(r.getProduct_name(), "", "");
                    continue;
                }
            }
            matchAttr(requireTable, exclusionTable, type, r, selectBomInfo, configAttributeList,requireRangeTable);
            matchNonAttr(requireTable, exclusionTable, type, r, selectBomInfo, configNonstandardAttributeList,requireRangeTable);
        }
    }

    private void matchNonAttr(Table<String, String, String> requireTable, Table<String, String, String> exclusionTable, int type, BomConstraintLineModel r, CheckBomModel.BomInfo selectBomInfo, List<BomConstraintLineModel.NonAttribute> configNonstandardAttributeList, Table<String, String, String> requireRangeTable) {
        List<CheckBomModel.NonStandardAttribute> selectNonAttributes = selectBomInfo.getNonAttributes();
        if (CollectionUtils.isEmpty(configNonstandardAttributeList)) {
            return;
        }
        if (CollectionUtils.isEmpty(selectNonAttributes)) {
            if (type == EnumUtil.ConstraintType.must.getValue()) {
                CollectionUtils.emptyIfNull(configNonstandardAttributeList).forEach(d ->
                        CollectionUtils.emptyIfNull(d.getAttribute_values()).forEach(v ->
                                requireTable.put(r.getProduct_name(), d.getName(), StringUtils.defaultString(v.getMinValue(), "").concat("-").concat(StringUtils.defaultString(v.getMaxValue(), "")))));
            }
            if (type == EnumUtil.ConstraintType.range.getValue()) {
                CollectionUtils.emptyIfNull(configNonstandardAttributeList).forEach(d ->
                        CollectionUtils.emptyIfNull(d.getAttribute_values()).forEach(v ->
                                requireRangeTable.put(r.getProduct_name(), d.getName(), StringUtils.defaultString(v.getMinValue(), "").concat("-").concat(StringUtils.defaultString(v.getMaxValue(), "")))));
            }
            return;
        }
        boolean allMatch = true;
        Map<String, CheckBomModel.NonStandardAttribute> selectNonAttributeMap = selectNonAttributes.stream().collect(Collectors.toMap(CheckBomModel.NonStandardAttribute::getId, Function.identity(), (v1, v2) -> v1));
        for (BomConstraintLineModel.NonAttribute configNonAttribute : configNonstandardAttributeList) {
            boolean attrFlag = false;
            CheckBomModel.NonStandardAttribute selectNonAttribute = selectNonAttributeMap.get(configNonAttribute.getId());
            if (Objects.nonNull(selectNonAttribute)) {
                CheckBomModel.NonStandardAttributeValue selectAttributeValues = selectNonAttribute.getAttributeValues();
                List<BomConstraintLineModel.NonAttribute.AttributeValues> configAttributeValues = configNonAttribute.getAttribute_values();
                String selectAttributeValue = selectAttributeValues.getValue();
                if (StringUtils.isNotBlank(selectAttributeValue)&&CollectionUtils.isNotEmpty(configAttributeValues)) {
                    if (Range.closed(toDouble(configAttributeValues.get(0).getMinValue(), 0.0),
                            toDouble(configAttributeValues.get(0).getMaxValue(), Double.MAX_VALUE)).contains(toDouble(selectAttributeValue, 0.0))) {
                        attrFlag = true;
                    }
                }
            }
            if (!attrFlag) {
                allMatch = false;
                if (type == EnumUtil.ConstraintType.must.getValue()) {
                    CollectionUtils.emptyIfNull(configNonstandardAttributeList).forEach(d ->
                            CollectionUtils.emptyIfNull(d.getAttribute_values()).forEach(v ->
                                    requireTable.put(r.getProduct_name(), d.getName(), StringUtils.defaultString(v.getMinValue(), "").concat("-").concat(StringUtils.defaultString(v.getMaxValue(), "")))));
                    return;
                }
                if (type == EnumUtil.ConstraintType.range.getValue()) {
                    CollectionUtils.emptyIfNull(configNonstandardAttributeList).forEach(d ->
                            CollectionUtils.emptyIfNull(d.getAttribute_values()).forEach(v ->
                                    requireRangeTable.put(r.getProduct_name(), d.getName(), StringUtils.defaultString(v.getMinValue(), "").concat("-").concat(StringUtils.defaultString(v.getMaxValue(), "")))));
                }
            }else{
                if (type == EnumUtil.ConstraintType.unAllow.getValue()) {
                    CollectionUtils.emptyIfNull(configNonstandardAttributeList).forEach(d ->
                            CollectionUtils.emptyIfNull(d.getAttribute_values()).forEach(v ->
                                    exclusionTable.put(r.getProduct_name(), d.getName(), StringUtils.defaultString(v.getMinValue(), "").concat("-").concat(StringUtils.defaultString(v.getMaxValue(), "")))));
                    return;
                }
            }
        }
        if (allMatch && type == EnumUtil.ConstraintType.unAllow.getValue()) {
            CollectionUtils.emptyIfNull(configNonstandardAttributeList).forEach(d ->
                    CollectionUtils.emptyIfNull(d.getAttribute_values()).forEach(v ->
                            exclusionTable.put(r.getProduct_name(), d.getName(), StringUtils.defaultString(v.getMinValue(), "").concat("-").concat(StringUtils.defaultString(v.getMaxValue(), "")))));
        }
    }

    private void matchAttr(Table<String, String, String> requireTable, Table<String, String, String> exclusionTable, int type, BomConstraintLineModel r, CheckBomModel.BomInfo selectBomInfo, List<BomConstraintLineModel.Attribute> configAttributeList, Table<String, String, String> requireRangeTable) {
        List<CheckBomModel.StandardAttribute> selectAttributes = selectBomInfo.getAttributes();
        if (CollectionUtils.isEmpty(configAttributeList)) {
            return;
        }
        if (CollectionUtils.isEmpty(selectAttributes)) {
            if (type == EnumUtil.ConstraintType.must.getValue()) {
                CollectionUtils.emptyIfNull(configAttributeList).forEach(d ->
                        CollectionUtils.emptyIfNull(d.getAttribute_values()).forEach(v ->
                                requireTable.put(r.getProduct_name(), d.getName(), v.getName())));
            }
            if (type == EnumUtil.ConstraintType.range.getValue()) {
                CollectionUtils.emptyIfNull(configAttributeList).forEach(d -> requireRangeTable.put(r.getProduct_name(), d.getName(),
                        CollectionUtils.emptyIfNull(d.getAttribute_values())
                                .stream()
                                .map(BomConstraintLineModel.Attribute.AttributeValues::getName)
                                .collect(Collectors.joining(","))));
            }
            return;
        }
        Map<String, CheckBomModel.StandardAttribute> selectAttrMap = selectAttributes.stream().collect(Collectors.toMap(CheckBomModel.StandardAttribute::getId, Function.identity(), (v1, v2) -> v1));
        boolean allMatch = true;
        for (BomConstraintLineModel.Attribute configAttribute : configAttributeList) {
            boolean attrFlag = false;
            CheckBomModel.StandardAttribute selectAttr = selectAttrMap.get(configAttribute.getId());
            if (Objects.nonNull(selectAttr)) {
                List<BomConstraintLineModel.Attribute.AttributeValues> configAttributeValues = configAttribute.getAttribute_values();
                CheckBomModel.StandardAttributeValues selectAttributeValues = selectAttr.getAttributeValues();
                String selectAttributeValueId = Optional.ofNullable(selectAttributeValues).map(CheckBomModel.StandardAttributeValues::getId).orElse("");
                if (CollectionUtils.isNotEmpty(configAttributeValues)&&
                        configAttributeValues.stream()
                                .map(BomConstraintLineModel.Attribute.AttributeValues::getId)
                                .collect(Collectors.toList())
                                .contains(selectAttributeValueId)) {
                    attrFlag = true;
                }
            }
            if (!attrFlag) {
                allMatch = false;
                if (type == EnumUtil.ConstraintType.must.getValue()) {
                    configAttributeList.forEach(d ->
                            CollectionUtils.emptyIfNull(d.getAttribute_values()).forEach(v ->
                                    requireTable.put(r.getProduct_name(), d.getName(), v.getName())));
                    return;
                }
                if (type == EnumUtil.ConstraintType.range.getValue()) {
                    CollectionUtils.emptyIfNull(configAttributeList).forEach(d -> requireRangeTable.put(r.getProduct_name(), d.getName(),
                            CollectionUtils.emptyIfNull(d.getAttribute_values())
                                    .stream()
                                    .map(BomConstraintLineModel.Attribute.AttributeValues::getName)
                                    .collect(Collectors.joining(","))));
                    return;
                }
            }else{
                if (type == EnumUtil.ConstraintType.unAllow.getValue()) {
                    CollectionUtils.emptyIfNull(configAttributeList).forEach(d -> exclusionTable.put(r.getProduct_name(), d.getName(),
                            CollectionUtils.emptyIfNull(d.getAttribute_values())
                                    .stream()
                                    .map(BomConstraintLineModel.Attribute.AttributeValues::getName)
                                    .collect(Collectors.joining(","))));
                    return;
                }
            }
        }
        if (allMatch && type == EnumUtil.ConstraintType.unAllow.getValue()) {
            CollectionUtils.emptyIfNull(configAttributeList).forEach(d -> exclusionTable.put(r.getProduct_name(), d.getName(),
                    CollectionUtils.emptyIfNull(d.getAttribute_values())
                            .stream()
                            .map(BomConstraintLineModel.Attribute.AttributeValues::getName)
                            .collect(Collectors.joining(","))));
        }
    }

    private String createMsg(Table<String, String, String> dataTable, StringBuilder msg) {
        if (dataTable.isEmpty()) {
            return "";
        }
        msg.append(":【");
        dataTable.rowKeySet().forEach(x -> {
            msg.append(x);
            Map<String, String> row = dataTable.row(x);
            if (MapUtils.isNotEmpty(row)) {
                msg.append(":[");
                row.forEach((k, v) -> {
                            if (StringUtils.isNotBlank(k)) {
                                msg.append(k).append(":").append(v).append(";");
                            }
                        }
                );
                msg.append("]");
            }
        });
        msg.append("】");
        return msg.toString().replace(":[]","; ");
    }

    private boolean matchConstraintLine(Map<String, CheckBomModel.BomInfo> selectBomMap, List<BomConstraintLineModel> conditionList, Multimap<String, String> groupMap, Map<String, String> bomMap, String currentRootNewPath) {
        boolean attrFlag;
        for (BomConstraintLineModel r : conditionList) {
            if(StringUtils.isNotBlank(r.getGroup_id())){
                Collection<String> bomPathList = groupMap.get(currentRootNewPath+r.getGroup_id());
                if (CollectionUtils.isNotEmpty(bomPathList)) {
                    for (String p : bomPathList) {
                        if (Objects.nonNull(selectBomMap.get(currentRootNewPath+p))) {
                            return true;
                        }
                    }
                }
                return false;
            }

            CheckBomModel.BomInfo selectBom = selectBomMap.get(currentRootNewPath+r.getBom_id());
            if (Objects.isNull(selectBom)) {
                return false;
            }
            List<BomConstraintLineModel.Attribute> configAttributeList = r.getAttribute();
            List<BomConstraintLineModel.NonAttribute> nonAttributeList = r.getNonstandardAttribute();
            if (CollectionUtils.isNotEmpty(configAttributeList)) {
                List<CheckBomModel.StandardAttribute> selectAttributes = selectBom.getAttributes();
                if (CollectionUtils.isEmpty(selectAttributes)) {
                    return false;
                }
                Map<String, CheckBomModel.StandardAttribute> selectAttributeMap = selectAttributes.stream().collect(Collectors.toMap(CheckBomModel.StandardAttribute::getId, Function.identity(), (v1, v2) -> v1));
                for (BomConstraintLineModel.Attribute configAttr : configAttributeList) {
                    attrFlag = false;
                    CheckBomModel.StandardAttribute selectAttr = selectAttributeMap.get(configAttr.getId());
                    if (Objects.nonNull(selectAttr)) {
                        CheckBomModel.StandardAttributeValues selectAttributeValues = selectAttr.getAttributeValues();
                        List<BomConstraintLineModel.Attribute.AttributeValues> configAttributeValues = configAttr.getAttribute_values();
                        if (Objects.isNull(selectAttributeValues) || CollectionUtils.isEmpty(configAttributeValues)) {
                            return false;
                        }
                        if (Objects.equals(selectAttributeValues.getId(), configAttributeValues.get(0).getId())) {
                            attrFlag = true;
                        }
                    }
                    if (!attrFlag) {
                        return false;
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(nonAttributeList)) {
                List<CheckBomModel.NonStandardAttribute> selectNonAttributes = selectBom.getNonAttributes();
                if (CollectionUtils.isEmpty(selectNonAttributes)) {
                    return false;
                }
                Map<String, CheckBomModel.NonStandardAttribute> selectNonAttributeMap = selectNonAttributes.stream().collect(Collectors.toMap(CheckBomModel.NonStandardAttribute::getId, Function.identity(), (v1, v2) -> v1));
                for (BomConstraintLineModel.NonAttribute configAttr : nonAttributeList) {
                    attrFlag = false;
                    CheckBomModel.NonStandardAttribute selectNonAttr = selectNonAttributeMap.get(configAttr.getId());
                    if (Objects.nonNull(selectNonAttr)) {
                        CheckBomModel.NonStandardAttributeValue selectAttributeValue = selectNonAttr.getAttributeValues();
                        List<BomConstraintLineModel.NonAttribute.AttributeValues> configAttributeValues = configAttr.getAttribute_values();
                        if (Objects.isNull(selectAttributeValue) || CollectionUtils.isEmpty(configAttributeValues)) {
                            return false;
                        }
                        if (StringUtils.isBlank(selectAttributeValue.getValue())) {
                            return false;
                        }
                        if (Range.closed(toDouble(configAttributeValues.get(0).getMinValue(), 0.0),
                                toDouble(configAttributeValues.get(0).getMaxValue(), Double.MAX_VALUE)).contains(toDouble(selectAttributeValue.getValue(), 0.0))) {
                            attrFlag = true;
                        }
                    }
                    if (!attrFlag) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    private Double toDouble(String value, Double defaultValue) {
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        return Double.parseDouble(value);
    }

    @Override
    public void checkBomConstraintAttr(User user, ArrayListMultimap<String, String> dataMap, boolean attrFlag) {
        if (!bizConfigThreadLocalCacheService.isCPQEnabled(user.getTenantId())) {
            return ;
        }
        if (dataMap.isEmpty()) {
            return;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(2000);
        SearchUtil.fillFilterIn(query.getFilters(), BomConstants.FIELD_PRODUCT_ID, dataMap.keySet());
        SearchUtil.fillFilterEq(query.getFilters(), IObjectData.IS_DELETED, 0);
        QueryResult<IObjectData> result = serviceFacade.findBySearchQueryIgnoreAll(user, Utils.BOM_API_NAME, query);
        if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getData())) {
            return;
        }
        List<IObjectData> dataList = result.getData();
        Map<String, Map<String, List<IObjectData>>> bomMap = dataList.stream().collect(Collectors.groupingBy(x -> x.get(BomConstants.FIELD_ROOT_ID, String.class), Collectors.groupingBy(DBRecord::getId)));
        query = new SearchTemplateQuery();
        query.setLimit(2000);
        SearchUtil.fillFilterIn(query.getFilters(), BomConstants.FIELD_ROOT_ID, bomMap.keySet());
        SearchUtil.fillFilterEq(query.getFilters(), IObjectData.IS_DELETED, 0);
        result = serviceFacade.findBySearchQueryIgnoreAll(user, BOM_ATTRIBUTE_CONSTRAINT_LINES_OBJ, query);
        if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getData())) {
            return;
        }
        dataList = result.getData();
        dataList.forEach(x -> {
            Map<String, List<IObjectData>> bom = bomMap.get(x.get(BomConstants.FIELD_ROOT_ID));
            if (MapUtils.isNotEmpty(bom)) {
                String conJson = x.get(BomConstraintConstants.CONDITION_RANGE, String.class);
                String reJson = x.get(BomConstraintConstants.RESULT_RANGE, String.class);
                List<BomConstraintLineModel> conditionData = JSON.parseArray(conJson, BomConstraintLineModel.class);
                List<BomConstraintLineModel> resultData = JSON.parseArray(reJson, BomConstraintLineModel.class);
                checkAttr(user, dataMap, x, bom, conditionData, attrFlag);
                checkAttr(user, dataMap, x, bom, resultData, attrFlag);
            }
        });
    }

    private void checkAttr(User user, ArrayListMultimap<String, String> dataMap, IObjectData x, Map<String, List<IObjectData>> bom, List<BomConstraintLineModel> constraintData, boolean attrFlag) {
        if (CollectionUtils.isEmpty(constraintData)) {
            return;
        }
        constraintData.forEach(d -> {
            List<IObjectData> tmpDataList = bom.get(d.getBom_id());
            if (CollectionUtils.isNotEmpty(tmpDataList)) {
                String productId = tmpDataList.get(0).get(BomConstants.FIELD_PRODUCT_ID, String.class);
                List<String> attrList = dataMap.get(productId);
                if (attrFlag) {
                    List<String> attrIds = CollectionUtils.emptyIfNull(d.getAttribute()).stream().map(BomConstraintLineModel.Attribute::getId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(attrList) && CollectionUtils.isNotEmpty(attrIds)) {
                        Collection<String> retainList = CollectionUtils.retainAll(attrList, attrIds);
                        if (CollectionUtils.isNotEmpty(retainList)) {
                            Map<String, String> productName = ProductConstraintUtil.getObjectName(user, Sets.newHashSet(productId), Utils.PRODUCT_API_NAME);
                            Map<String, String> attrName = ProductConstraintUtil.getObjectName(user, Sets.newHashSet(retainList), Utils.ATTRIBUTE_OBJ_API_NAME);
                            Map<String, String> bomConstraintName = ProductConstraintUtil.getObjectName(user, Sets.newHashSet(x.get(BomConstraintConstants.BOM_ATTRIBUTE_CONSTRAINT_ID, String.class)), BOM_ATTRIBUTE_CONSTRAINT_OBJ);
                            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_ATTR_CANNOT_REMOVE_PRODUCT_ATTR, bomConstraintName.values(), productName.values(), attrName.values()));
                        }
                    }
                } else {
                    List<String> nonAttrIds = CollectionUtils.emptyIfNull(d.getNonstandardAttribute()).stream().map(BomConstraintLineModel.NonAttribute::getId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(attrList) && CollectionUtils.isNotEmpty(nonAttrIds)) {
                        Collection<String> retainList = CollectionUtils.retainAll(attrList, nonAttrIds);
                        if (CollectionUtils.isNotEmpty(retainList)) {
                            Map<String, String> productName = ProductConstraintUtil.getObjectName(user, Sets.newHashSet(productId), Utils.PRODUCT_API_NAME);
                            Map<String, String> attrName = ProductConstraintUtil.getObjectName(user, Sets.newHashSet(retainList), Utils.NONSTANDARD_ATTRIBUTE_OBJ_API_NAME);
                            Map<String, String> bomConstraintName = ProductConstraintUtil.getObjectName(user, Sets.newHashSet(x.get(BomConstraintConstants.BOM_ATTRIBUTE_CONSTRAINT_ID, String.class)), BOM_ATTRIBUTE_CONSTRAINT_OBJ);
                            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_ATTR_CANNOT_REMOVE_PRODUCT_NON_ATTR, bomConstraintName.values(), productName.values(), attrName.values()));
                        }
                    }
                }
            }
        });
    }

    @Override
    public List<IObjectData> findBomConstrainByCoreId(User user, List<String> coreIds, boolean includeInvalid) {
        if (CollectionUtils.isEmpty(coreIds)) {
            return Collections.emptyList();
        }
        SearchTemplateQuery searchTemplateQuery = SoCommonUtils.buildSearchTemplateQuery();
        SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.IN, CPQConstraintConstants.CORE_ID, coreIds);
        if (includeInvalid) {
            SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.IN, DbSystemRecord.IS_DELETED, Lists.newArrayList(SystemConstants.IsDeletedStatus.Normal.value, SystemConstants.IsDeletedStatus.Invalid.value));
        } else {
            SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.EQ, DbSystemRecord.LIFE_STATUS, SystemConstants.LifeStatus.Normal.value);
            SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.EQ, DbSystemRecord.IS_DELETED, SystemConstants.IsDeletedStatus.Normal.value);
            SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.EQ, CPQConstraintConstants.ACTIVE_STATUS, "true");
        }
        QueryResult<IObjectData> productConstraintList = serviceFacade.findBySearchQueryIgnoreAll(user, BOM_ATTRIBUTE_CONSTRAINT_OBJ, searchTemplateQuery);
        if (CollectionUtils.isEmpty(productConstraintList.getData())) {
            return Collections.emptyList();
        }
        return productConstraintList.getData();
    }


    @Override
    public BomFormulaModel.CheckResult expressionCheck(ServiceContext serviceContext, BomFormulaModel.CheckArg arg) {
        String expression = arg.getExpression();
        if (StringUtils.isBlank(expression)) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_EXPRESSION_NOT_NULL));
        }
        Map<String, Object> map = new HashMap<>();
        Matcher matcher = variable_pattern.matcher(expression);
        //将表达式里的变量替换掉，调用底层提供的接口进行表达式校验
        while (matcher.find()) {
            String variable = matcher.group(1);
            map.put("$"+variable+"$", "1");
        }
        for(Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            expression = expression.replace(key, entry.getValue().toString());
        }
        ExpressionCheck.Arg checkArg = new ExpressionCheck.Arg();
        Map<String, Object> jsonDataMap = Maps.newHashMap();
        jsonDataMap.put("expression", expression);
        jsonDataMap.put("expressionType", "formula");
        jsonDataMap.put("objectDescribeApiName", "BOMObj");
        jsonDataMap.put("calculateFieldApiName", EXPRESSION_AMOUNT);
        jsonDataMap.put("returnType", "number");
        jsonDataMap.put("setDefaultToZero", true);
        checkArg.setJson_data(JsonUtil.toJson(jsonDataMap));
        ExpressionCheck.Result result = calculateService.expressionCheck(checkArg, serviceContext);
        BomFormulaModel.CheckResult checkResult = new BomFormulaModel.CheckResult();
        if (result.getCode() == 0) {
            checkResult.setPass(true);
        } else {
            checkResult.setPass(false);
            checkResult.setMsg(result.getValue());
        }
        return checkResult;
    }

    /**
     * 计算表达式的值，调用平台接口前，将表达式中的变量替换成实际值
     * @param serviceContext
     * @param arg
     * @return
     */
    @Override
    public BomFormulaModel.CalculateResult formulaCalculate(ServiceContext serviceContext, BomFormulaModel.CalculateArg arg) {
        if(CollectionUtils.isEmpty(arg.getFormulas())) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_EXPRESSION_NOT_NULL));
        }
        if(CollectionUtils.isEmpty(arg.getSelectBomList())) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_CALCULATE_SELECTED_BOM_NOT_EMPTY));
        }
        //复用BOM分组处理，计算公式时，只能取对应包里的子件进行计算
        Map<String, Map<String, IObjectData>> currentRootNewPathGroupMap = Maps.newHashMap();
        //完整的BOM结构，new_bom_path唯一
        Map<String, IObjectData> newBomPathMap = Maps.newHashMap();
        //BOM_ID与产品名称的映射关系，方便取产品名称
        Map<String, String> bomIdToProductNameMap = Maps.newHashMap();
        List<ObjectDataDocument> selectBomList = arg.getSelectBomList();
        for (ObjectDataDocument document : selectBomList) {
            IObjectData bomObjectData = document.toObjectData();
            //临时子件过滤掉，不处理
            if(Objects.equals("temp", bomObjectData.get(BomConstants.FIELD_NODE_TYPE, String.class))) {
                continue;
            }
            String currentRootNewPath = bomObjectData.get(BomConstants.VIRTUAL_FIELD_CURRENT_ROOT_NEW_PATH, String.class);
            String bomId = bomObjectData.get(BomConstants.FIELD_BOM_ID, String.class);
            String newBomPath = bomObjectData.get(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, String.class);
            if(StringUtils.isAnyBlank(currentRootNewPath, bomId, newBomPath)) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_EXPRESSION_PARAMETER_MISS));
            }
            Map<String, IObjectData> groupMap = currentRootNewPathGroupMap.computeIfAbsent(currentRootNewPath, k -> Maps.newHashMap());
            groupMap.put(bomId, bomObjectData);
            newBomPathMap.put(newBomPath, bomObjectData);
            bomIdToProductNameMap.put(bomObjectData.get(BomConstants.FIELD_BOM_ID, String.class), bomObjectData.get("product_id__r", String.class,""));
        }

        //需要将复用BOM节点放到复用BOM的分组里
        for(Map.Entry<String, Map<String, IObjectData>> entry : currentRootNewPathGroupMap.entrySet()) {
            String rootPath = entry.getKey();
            Map<String, IObjectData> groupMap = entry.getValue();
            int index = rootPath.lastIndexOf(".");
            //过滤掉母件的分组
            if(index < 1) {
                continue;
            }
            //当前节点的current_root_new_path对应的就是上一个节点的 new_bom_path,取出复用的那条数据，加入到复用的分组里
            IObjectData rootBomData = newBomPathMap.get(rootPath);
            groupMap.putIfAbsent(rootBomData.get(BomConstants.FIELD_BOM_ID, String.class), rootBomData);
        }

        List<SimpleExpression> simpleExpressionsList = Lists.newArrayList();
        List<IObjectData> tempList = Lists.newArrayList();

        int decimalPlaces;
        IObjectDescribe objectDescribe = serviceFacade.findObject(serviceContext.getTenantId(), BomConstants.DESC_API_NAME);
        if(objectDescribe != null && objectDescribe.getFieldDescribe(BomConstants.FIELD_AMOUNT) != null) {
            decimalPlaces = objectDescribe.getFieldDescribe(BomConstants.FIELD_AMOUNT).get("decimal_places", Integer.class, Integer.valueOf(0)).intValue();
        } else {
            decimalPlaces = 0;
        }
        arg.getFormulas().forEach(formula -> {
            if(StringUtils.isBlank(formula.getExpression())) {
                IObjectData currentData = newBomPathMap.get(formula.getNewBomPath());
                if(currentData == null) {
                    throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_EXPRESSION_BOM_ID_IS_NULL));
                }
                throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_EXPRESSION_PARAMETER_ERROR, bomIdToProductNameMap.get(formula.getBomId())));
            }
            //解析表达式
            String expression = null;
            try {
                expression = parseExpress(formula.getBomId(), formula.getExpression(), currentRootNewPathGroupMap.get(formula.getCurrentRootNewPath()), bomIdToProductNameMap);
            }catch (ValidateException e) {
                //如果不是最后一次校验，则忽略有异常的表达式
                if(!arg.isSkipInvalidExpression()) {
                    throw e;
                }
            }
            if(StringUtils.isNotBlank(expression)) {
                //为符合平台表达式计算接口入参要求，组装临时数据
                IObjectData objectData = new ObjectData();
                objectData.set(DBRecord.ID, formula.getNewBomPath());
                objectData.set(BomConstants.FIELD_BOM_ID, formula.getBomId());
                objectData.set(BomConstants.FIELD_PRODUCT_ID, formula.getProductId());
                objectData.set(EXPRESSION_FIELD_NAME, formula.getFieldName());
                tempList.add(objectData);
                //组装简单表达式参数
                SimpleExpression simpleExpression = SimpleExpression.builder().decimalPlaces(decimalPlaces).expression(expression).id(formula.getNewBomPath()).returnType("number").build();
                simpleExpressionsList.add(simpleExpression);
            }
        });

        //调用平台接口计算表达式
        expressionCalculateLogicService.bulkCalculateWithExpression(objectDescribe, tempList, simpleExpressionsList);
        BomFormulaModel.CalculateResult result = new BomFormulaModel.CalculateResult();
        List<BomFormulaModel.FormulaResult> formulaResultList = new ArrayList<>();
        log.info("formula bulkCalculateWithExpression result:{}", tempList);
        //从计算结果中解析出需要返回的数据
        tempList.stream().forEach(r->{
            BomFormulaModel.FormulaResult formulaResult = new BomFormulaModel.FormulaResult();
            formulaResult.setBomId(r.get(BomConstants.FIELD_BOM_ID, String.class));
            formulaResult.setProductId(r.get(BomConstants.FIELD_PRODUCT_ID, String.class));
            formulaResult.setNewBomPath(r.getId());
            formulaResult.setValueResultList(Lists.newArrayList(buildValueResult(BomFormulaModel.ValueType.PRODUCT_FIELD.getType(), r)));
            formulaResultList.add(formulaResult);
        });
        result.setDataList(formulaResultList);
        return result;
    }

    private BomFormulaModel.ValueResult buildValueResult(int type, IObjectData objectData) {
        String fieldName = objectData.get("field_name", String.class);
        Map<String, Object> valueMap = Maps.newHashMap();
        //表达式计算完之后，将结果放到id字段值作为key的value中
        valueMap.put(fieldName, objectData.get(objectData.getId()));
        BomFormulaModel.ValueResult valueResult = new BomFormulaModel.ValueResult();
        valueResult.setType(type);
        valueResult.setFieldName(fieldName);
        valueResult.setValue(valueMap);
        return valueResult;
    }

    private String parseExpress(String bomId, String expression, Map<String, IObjectData> bomMap, Map<String, String> bomToProductNameMap) {
        Matcher matcher = variable_pattern.matcher(expression);
        while (matcher.find()) {
            String variable = matcher.group(1);
            int type = getFormulaType(variable);
            String[] items = variable.split(getSplitFlag(variable));
            String expressionBomId = items[0];
            String selectedAttrId = items[1];
            String selectedNonAttrId = items[1];
            String fieldName = items[1];

            IObjectData bom = bomMap.get(expressionBomId);
            Object realValue = null;
            String currentFieldName = null;
            if(bom == null) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_EXPRESSION_BOM_NOT_EXIST,
                        bomToProductNameMap.getOrDefault(bomId, bomId),
                        bomToProductNameMap.getOrDefault(expressionBomId, expressionBomId)));
            }
            switch (type) {
                case SWITCH_TYPE_VALUE_1:
                    currentFieldName = EXPRESSION_ATTRIBUTES;
                    List selectedAttrList = bom.get(EXPRESSION_ATTRIBUTES, List.class);
                    if(CollectionUtils.isEmpty(selectedAttrList)) {
                        throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_EXPRESSION_BOM_ATTRIBUTE_NOT_EXIST, bomId));
                    }
                    String finalSelectedAttrId = selectedAttrId;
                    Optional<Map> attrMap = selectedAttrList.stream().filter(x->((Map) x).get(EXPRESSION_ID).equals(finalSelectedAttrId)).findFirst();
                    if(!attrMap.isPresent()) {
                        throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_EXPRESSION_BOM_ATTRIBUTE_VALUE_NOT_EXIST, bomId));
                    }
                    Map<String, String> attrValues = (Map<String, String>) attrMap.get().getOrDefault(EXPRESSION_ATTRIBUTEVALUES, Maps.newHashMap());
                    realValue = attrValues.get(EXPRESSION_ID);
                    break;
                case SWITCH_TYPE_VALUE_2:
                    currentFieldName = EXPRESSION_NONATTRIBUTES;
                    List selectedNonAttrList = bom.get(EXPRESSION_NONATTRIBUTES, List.class);
                    if(CollectionUtils.isEmpty(selectedNonAttrList)) {
                        throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_EXPRESSION_BOM_NON_ATTRIBUTE_NOT_EXIST, bomId));
                    }
                    String finalSelectedNonAttrId = selectedNonAttrId;
                    Optional<Map> nonAttrMap = selectedNonAttrList.stream().filter(x->((Map) x).get(EXPRESSION_ID).equals(finalSelectedNonAttrId)).findFirst();
                    if(!nonAttrMap.isPresent()) {
                        throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_EXPRESSION_BOM_NON_ATTRIBUTE_VALUE_NOT_EXIST, bomId));
                    }
                    Map<String, String> nonAttrvalues = (Map<String, String>) nonAttrMap.get().getOrDefault(EXPRESSION_ATTRIBUTEVALUES, Maps.newHashMap());
                    realValue = nonAttrvalues.get(EXPRESSION_VALUE);
                    break;
                default:
                    currentFieldName = fieldName;
                    realValue = bom.get(fieldName, BigDecimal.class);
                    break;
            }
            if(Objects.isNull(realValue) || StringUtils.isBlank(realValue.toString())) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_FORMULA_EXPRESSION_LOST_PARAMETER_VALUE, currentFieldName));
            }
            if(type == BomFormulaModel.ValueType.PRODUCT_STANDARD_ATTRIBUTE.getType()) {
                expression = expression.replace("$" + variable + "$", "'"+realValue.toString()+"'");
            } else{
                expression = expression.replace("$" + variable + "$", realValue.toString());
            }
        }
        log.info("formula parseExpression result:"+expression);
        return expression;
    }

    @Override
    public BomFormulaModel.FormulaCheckResult formulaCheck(ServiceContext serviceContext, BomFormulaModel.FormulaCheckArg arg) {
        if(CollectionUtils.isEmpty(arg.getSelectBomList())) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_CALCULATE_SELECTED_BOM_NOT_EMPTY));
        }
        if(CollectionUtils.isEmpty(arg.getFormulas())) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_EXPRESSION_NOT_NULL));
        }
        Map<String, IObjectData> currentAmountMap = Maps.newHashMap();
        Map<String, String> currentProductMap = Maps.newHashMap();
        Map<String, String> newBomPath2BomIdMap = Maps.newHashMap();
        //判断参数准备
        for(ObjectDataDocument selectedBom : arg.getSelectBomList()) {
            IObjectData iObjectData = selectedBom.toObjectData();
            String newBomPath = iObjectData.get(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, String.class);
            currentAmountMap.put(newBomPath, iObjectData);
            newBomPath2BomIdMap.put(newBomPath, iObjectData.get(BomConstants.FIELD_BOM_ID, String.class));
            currentProductMap.put(newBomPath, iObjectData.get("product_id__r", String.class, ""));
        }
        BomFormulaModel.CalculateArg calculateArg = new BomFormulaModel.CalculateArg();
        calculateArg.setSelectBomList(arg.getSelectBomList());
        calculateArg.setFormulas(arg.getFormulas());
        calculateArg.setSkipInvalidExpression(false);
        //调用高级公式计算接口
        BomFormulaModel.CalculateResult result = this.formulaCalculate(serviceContext, calculateArg);
        List<BomFormulaModel.FormulaResult> dataList = result.getDataList();
        //校验结果,如果没有返回结算结果,则认为校验失败
        if(CollectionUtils.isEmpty(dataList)) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_EXPRESSION_CALCULATE_ERROR));
        }
        Map<String, BigDecimal> calculateMap = Maps.newHashMap();
        //存储时哪个字段做的计算
        Map<String, String> calculateFieldMap = Maps.newHashMap();
        //解析出校验结果，方便比较
        dataList.stream().forEach(x->{
            List<BomFormulaModel.ValueResult> valueResultList = x.getValueResultList();
            if(CollectionUtils.isEmpty(valueResultList)) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_EXPRESSION_CALCULATE_ERROR));
            }
            BomFormulaModel.ValueResult valueResult = valueResultList.stream().filter(
                    item->item.getType() == BomFormulaModel.ValueType.PRODUCT_FIELD.getType()
            ).findFirst().get();
            if(valueResult == null) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_EXPRESSION_CALCULATE_ERROR));
            }
            String fieldName = valueResult.getFieldName() == null ? EXPRESSION_AMOUNT : valueResult.getFieldName();
            BigDecimal calculateValue = (BigDecimal) valueResult.getValue().get(fieldName);
            calculateMap.put(x.getNewBomPath(), calculateValue==null?BigDecimal.ZERO:calculateValue);
            calculateFieldMap.put(x.getNewBomPath(), fieldName);
        });
        BomFormulaModel.FormulaCheckResult checkResult = new BomFormulaModel.FormulaCheckResult();
        checkResult.setPass(true);
        StringBuilder msgBuilder = new StringBuilder();
        List<BomFormulaModel.FormulaCheckDetail> details = Lists.newArrayList();
        log.info("formula CheckResult:"+JsonUtil.toJson(calculateMap));
        calculateMap.keySet().stream().forEach(x->{
            IObjectData old = currentAmountMap.get(x);
            String fieldName = calculateFieldMap.get(x);
            BigDecimal currentValue = old == null ? BigDecimal.ZERO : old.get(fieldName, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal calculateValue = calculateMap.getOrDefault(x, BigDecimal.ZERO);
            if(currentValue.compareTo(calculateValue) != 0) {
                BomFormulaModel.FormulaCheckDetail formulaCheckDetail = new BomFormulaModel.FormulaCheckDetail();
                formulaCheckDetail.setBomId(newBomPath2BomIdMap.getOrDefault(x, ""));
                formulaCheckDetail.setNewBomPath(x);
                formulaCheckDetail.setFieldName(fieldName);
                formulaCheckDetail.setCurrentValue(currentValue);
                formulaCheckDetail.setFormulaValue(calculateValue);
                details.add(formulaCheckDetail);
                msgBuilder.append(I18N.text(BomI18NKeyUtil.BOM_FORMULA_EXPRESSION_CALCULATE_NOT_PASS_PRODUCT_DETAIL, currentProductMap.get(x), currentValue, calculateValue)+"\n");
            }
        });
        checkResult.setDetails(details);
        if(msgBuilder.length() > 0) {
            checkResult.setPass(false);
            checkResult.setMsg(I18N.text(BomI18NKeyUtil.BOM_FORMULA_EXPRESSION_CALCULATE_NOT_PASS_PRODUCT, msgBuilder.toString()));
        } else {
            checkResult.setMsg("check passed!");
        }
        return checkResult;
    }

    @Override
    public BomFormulaModel.AplResult calculateByApl(ServiceContext serviceContext, BomFormulaModel.AplArg arg) {
        if(StringUtils.isBlank(arg.getAplApiName())) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_ASSIGN_APL_NOT_BE_NULL));
        }
        if(CollectionUtils.isEmpty(arg.getSelectBomList())) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_FORMULA_CALCULATE_SELECTED_BOM_NOT_EMPTY));
        }

        IUdefFunction function = serviceFacade.getFunctionLogicService().findUDefFunction(serviceContext.getUser()
                , arg.getAplApiName()
                , null);
        if (function == null || !function.isActive()) {
            throw new ValidateException(I18N.text(I18NKey.NOT_FIND_FUNCTION, arg.getAplApiName()));
        }

        Map<String, Object> param = Maps.newHashMap();
        param.put(FUNCTION_FIELD_NAME, "selectedBomList");
        param.put(FUNCTION_FIELD_TYPE, "List");
        param.put(FUNCTION_FIELD_VALUE, arg.getSelectBomList());

        RunResult runResult = serviceFacade.getFunctionLogicService().executeFunctionController(serviceContext.getRequestContext(), function.getApiName(), Lists.newArrayList(param));
        if(!runResult.isSuccess()) {
            throw new ValidateException(runResult.getErrorInfo());
        }
        Map assignMap = (Map)runResult.getFunctionResult();
        Object dataList = assignMap.get("dataList");
        if(dataList == null) {
            log.info("formula bom.assign.apl:"+JsonUtil.toJson(assignMap));
            throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_ASSIGN_APL_RESULT_FORMAT_ERROR));
        }
        String json = JsonUtil.toJson(dataList);
        try {
            List<BomFormulaModel.FormulaResult> list = JsonUtil.fromJsonByJackson(json, new TypeReference<List<BomFormulaModel.FormulaResult>>() {});
            return BomFormulaModel.AplResult.builder().dataList(list).build();
        } catch (Exception e) {
            log.info("formula bom.assign.apl:"+JsonUtil.toJson(assignMap));
            throw new ValidateException(I18N.text(BomI18NKeyUtil.BOM_ASSIGN_APL_RESULT_FORMAT_ERROR));
        }
    }


    /**
     * 修改子件的数量、属性、非标属性需要触发执行高级公式信息封装
     * @param bomConstraintLinesList
     * @return
     */
    @Override
    public List<ObjectDataDocument> buidlTriggerFormulaMap(List<IObjectData> bomConstraintLinesList) {
        List<ObjectDataDocument> result = Lists.newArrayList();
        if(CollectionUtils.isEmpty(bomConstraintLinesList)){
            return result;
        }
        //key 为当前修改数据的bom_id，value为触发执行高级公式的信息(按字段类型再分一层，数量、属性、非标属性分别封装)
        Map<String, Map<String, List<TriggerBomFormulaModel.CalculateRowInfo>>> returnMap = Maps.newHashMap();
        for(IObjectData it : bomConstraintLinesList){
            String reJson = it.get("result_range", String.class, "[]");
            List<BomConstraintLineModel> resultRange = JSON.parseArray(reJson, BomConstraintLineModel.class);
            if(CollectionUtils.isEmpty(resultRange)) {
                continue;
            }
            resultRange.stream().filter(x->CollectionUtils.isNotEmpty(x.getFormula())).forEach(range->{
                    String triggerBomId = range.getBom_id();
                    String bomPath = range.getProduct_path();
                    List<BomConstraintLineModel.FormulaInfo> formulaList = range.getFormula();
                    if(CollectionUtils.isNotEmpty(formulaList)){
                        formulaList.stream().filter(x->!StringUtils.isBlank(x.getExpression())).forEach(formula->{
                            String expression = formula.getExpression();
                            Matcher matcher = variable_pattern.matcher(expression);
                            //解析出所有参与计算的bom信息
                            while (matcher.find()) {
                                String variable = matcher.group(1);
                                int type = getFormulaType(variable);
                                String realAttrName = getFieldRealName(type, EXPRESSION_AMOUNT);
                                String splitFlag = getSplitFlag(variable);
                                String[] bomAndValues = variable.split(splitFlag);
                                if(bomAndValues.length == 2){
                                    String bomId = bomAndValues[0];
                                    String attrName = bomAndValues[1];
                                    if(type == TriggerBomFormulaModel.ValueType.PRODUCT_FIELD.getType()){
                                        realAttrName = attrName;
                                    }
                                    Map<String, List<TriggerBomFormulaModel.CalculateRowInfo>> attributesMap = returnMap.computeIfAbsent(bomId, k -> Maps.newHashMap());
                                    // 获取或创建attributes
                                    List<TriggerBomFormulaModel.CalculateRowInfo> attributes = attributesMap.computeIfAbsent(realAttrName, k -> new ArrayList<>());
                                    attributes.add(TriggerBomFormulaModel.CalculateRowInfo.builder().bomId(triggerBomId).bomPath(bomPath).expression(expression).fieldName(formula.getField_name()).build());
                                }
                            }
                        });
                    }
                }
            );
        }
        for(String bomId : returnMap.keySet()) {
            Map<String, List<TriggerBomFormulaModel.CalculateRowInfo>> attributesMap = returnMap.getOrDefault(bomId, Maps.newHashMap());
            for(Map.Entry<String, List<TriggerBomFormulaModel.CalculateRowInfo>> entry : attributesMap.entrySet()) {
                String attr = entry.getKey();
                List<TriggerBomFormulaModel.CalculateRowInfo> attributes = entry.getValue();
                int type = getFormulaTypeByAttr(attr);
                ObjectDataDocument objectDataDocument = TriggerBomFormulaModel.TriggerBomFormulaResult.builder()
                        .fieldName(attr)
                        .bomId(bomId)
                        .type(type)
                        .calculateRow(attributes)
                        .build().of();
                result.add(objectDataDocument);
            }
        }
        return result;
    }

    private int getFormulaType(String variable) {
        int type = TriggerBomFormulaModel.ValueType.PRODUCT_FIELD.getType();
        if(StringUtils.isBlank(variable)){
            return type;
        }
        if(variable.contains(STANDARD_ATTR_FLAG)) {
            type = TriggerBomFormulaModel.ValueType.PRODUCT_STANDARD_ATTRIBUTE.getType();
        } else if(variable.contains(NON_STANDARD_ATTR_FLAG)) {
            type = TriggerBomFormulaModel.ValueType.PRODUCT_NON_STANDARD_ATTRIBUTE.getType();
        } else {
            //默认
        }
        return type;
    }

    private int getFormulaTypeByAttr(String attr) {
        if(EXPRESSION_ATTRIBUTE.equals(attr)) {
            return TriggerBomFormulaModel.ValueType.PRODUCT_STANDARD_ATTRIBUTE.getType();
        } else if(EXPRESSION_NONATTRIBUTE.equals(attr)) {
            return TriggerBomFormulaModel.ValueType.PRODUCT_NON_STANDARD_ATTRIBUTE.getType();
        } else {
            return TriggerBomFormulaModel.ValueType.PRODUCT_FIELD.getType();
        }
    }

    private String getFieldRealName(int type, String defaultName) {
        switch (type) {
            case SWITCH_TYPE_VALUE_1:
                return EXPRESSION_ATTRIBUTE;
            case SWITCH_TYPE_VALUE_2:
                return EXPRESSION_NONATTRIBUTE;
            default:
                return defaultName;
        }
    }
}
