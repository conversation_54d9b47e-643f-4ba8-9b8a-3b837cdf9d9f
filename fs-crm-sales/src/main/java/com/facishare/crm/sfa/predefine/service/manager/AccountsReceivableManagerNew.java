package com.facishare.crm.sfa.predefine.service.manager;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.enums.AccountReceivableSwitchEnum;
import com.facishare.crm.sfa.predefine.enums.CollectionTypeEnum;
import com.facishare.crm.sfa.predefine.enums.SettledStatusEnum;
import com.facishare.crm.sfa.predefine.service.model.SalesOrderModel;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AccountsReceivableManagerNew {
    @Resource
    private ArConfigManagerNew arConfigManagerNew;

    @Autowired
    protected ServiceFacade serviceFacade;

    public boolean isAccountsReceivableEnable(String tenantId) {
        AccountReceivableSwitchEnum switchStatus = arConfigManagerNew.getAccountReceivableStatus(tenantId);
        //log.info("isAccountsReceivableEnable  switchStatus[{}]", switchStatus);
        return Objects.equals(switchStatus, AccountReceivableSwitchEnum.OPENED);
    }

    public void checkEditForAccountsReceivableNote(List<ObjectDataDocument> newSalesOrderProductDatas, List<ObjectDataDocument> dbSalesOrderProductDatas) {
        //log.info("checkEditForAccountsReceivableNote newSalesOrderProductDatas.size[{}], newSalesOrderProductDatas.size[{}]", newSalesOrderProductDatas.size(), newSalesOrderProductDatas.size());
        //删除订单产品：当订单产品的已立应收金额 >0 ，则提示“该订单产品已创建了应收，不支持删除！”
        List<String> newSalesOrderProductIds = newSalesOrderProductDatas.stream().map(ObjectDataDocument::getId).collect(Collectors.toList());
        for (ObjectDataDocument db : dbSalesOrderProductDatas) {
            if (newSalesOrderProductIds.contains(db.getId())) {
                continue;
            }

            if(!db.containsKey(SalesOrderProductObjConstants.Field.ArTagAmount.apiName)) {
                continue;
            }

            String arTagAmountStr = (String) db.get(SalesOrderProductObjConstants.Field.ArTagAmount.apiName);
            if (Strings.isNullOrEmpty(arTagAmountStr)) {
                continue;
            }

            BigDecimal arTagAmount = new BigDecimal(arTagAmountStr);
            if (arTagAmount.compareTo(BigDecimal.ZERO) > 0) {
                //log.info("checkEditForAccountsReceivableNote  arTagAmount > 0 dbSalesOrderProductData[{}]", db);
                throw new ValidateException(I18N.text("sfa-interceptor.errorinfo.SalesOrderInterceptorAccountsReceivableManager.30"));
            }
        }

        //当改动后的订单产品的折后金额 < 已立应收金额时，则提示“订单产品的已立应收金额大于折后金额，无法保存”
        if (CollectionUtils.isEmpty(newSalesOrderProductDatas)) {
            return;
        }

        newSalesOrderProductDatas.forEach(d -> {
            String arTagAmountStr = (String) d.get(SalesOrderProductObjConstants.Field.ArTagAmount.apiName);
            String orderProductAmountStr = (String) d.get("order_product_amount");

            BigDecimal arTagAmount = arTagAmountStr == null ? BigDecimal.ZERO : new BigDecimal(arTagAmountStr);
            BigDecimal orderProductAmount = orderProductAmountStr == null ? BigDecimal.ZERO : new BigDecimal(orderProductAmountStr);


            if (arTagAmount.compareTo(orderProductAmount) > 0) {
                //log.info("checkEditForAccountsReceivableNote  arTagAmount > orderProductAmount newSalesOrderProductData[{}]", d);
                throw new ValidateException(I18N.text("sfa-interceptor.errorinfo.SalesOrderInterceptorAccountsReceivableManager.41"));
            }
        });
    }

    public List<BaseImportAction.ImportError> checkARDetailImport(String tenantId, List<BaseImportDataAction.ImportData> dataList) {
        List<BaseImportAction.ImportError> errorList = Lists.newArrayList();
        List<IObjectData> arDatas = new ArrayList<>();
        List<IObjectData> salesOrders = new ArrayList<>();
        Map<String, List<IObjectData>> existDetailMaps = new HashMap<>();
        List<String> arIds = dataList.stream()
                .map(x -> x.getData().get(AccountsReceivableDetailObjConstants.Field.ArId.apiName, String.class))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(arIds)) {
            arDatas = serviceFacade.findObjectDataByIds(tenantId, arIds, AccountsReceivableNoteObjConstants.API_NAME);
            List<IObjectData> existDetails = getArDetailsByArIds(User.systemUser(tenantId), arIds);
            if (!CollectionUtils.isEmpty(existDetails)) {
                existDetailMaps = existDetails.stream()
                        .collect(Collectors.groupingBy(o -> o.get(AccountsReceivableDetailObjConstants.Field.ArId.apiName, String.class)));
            }
        }

        List<String> salesOrderIds = dataList.stream()
                .map(x -> x.getData().get(AccountsReceivableDetailObjConstants.Field.OrderId.apiName, String.class))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(salesOrderIds)) {
            salesOrders = serviceFacade.findObjectDataByIds(tenantId, arIds, Utils.SALES_ORDER_API_NAME);
        }
        List<String> orderProductIds = dataList.stream().
                map(data -> data.getData().get(AccountsReceivableDetailObjConstants.Field.OrderProductId.apiName, String.class)).distinct().collect(Collectors.toList());
        List<IObjectData> salesOrderProductDatas = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, orderProductIds, Utils.SALES_ORDER_PRODUCT_API_NAME);
        List<String> contractLineIds = dataList.stream().
                map(data -> data.getData().get(AccountsReceivableDetailObjConstants.Field.SaleContractLineId.apiName, String.class)).distinct().collect(Collectors.toList());
        List<IObjectData> saleContractLineDatas = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, contractLineIds, Utils.SALE_CONTRACT_LINE_API_NAME);

        //判断是否bom结构
        if (!CollectionUtils.isEmpty(salesOrderProductDatas)) {
            for (BaseImportDataAction.ImportData data : dataList) {
                String orderProductId = data.getData().get(AccountsReceivableDetailObjConstants.Field.OrderProductId.apiName, String.class);
                if (!Strings.isNullOrEmpty(orderProductId)) {
                    IObjectData detail = salesOrderProductDatas.stream().filter(x -> Objects.equals(x.getId(), orderProductId)).findFirst().orElse(null);
                    if (detail != null) {
                        String bomId = detail.get("bom_id", String.class);
                        if (org.apache.logging.log4j.util.Strings.isNotEmpty(bomId)) {
                            //bom结构明细，不触发子健
                            String rootProdPkgKey = detail.get("root_prod_pkg_key", String.class);
                            String prodPkgKey = detail.get("prod_pkg_key", String.class);
                            if (!Objects.equals(rootProdPkgKey, prodPkgKey)) {
                                errorList.add(new BaseImportAction.ImportError(data.getRowNo(), I18N.text("ar.expired.bom.error")));
                            }
                        }
                    }
                }
                String contractLineId = data.getData().get(AccountsReceivableDetailObjConstants.Field.SaleContractLineId.apiName, String.class);
                if (!Strings.isNullOrEmpty(contractLineId)) {
                    IObjectData detail = saleContractLineDatas.stream().filter(x -> Objects.equals(x.getId(), contractLineId)).findFirst().orElse(null);
                    if (detail != null) {
                        String bomId = detail.get("bom_id", String.class);
                        if (org.apache.logging.log4j.util.Strings.isNotEmpty(bomId)) {
                            //bom结构明细，不触发子健
                            String rootProdPkgKey = detail.get("root_prod_pkg_key", String.class);
                            String prodPkgKey = detail.get("prod_pkg_key", String.class);
                            if (!Objects.equals(rootProdPkgKey, prodPkgKey)) {
                                errorList.add(new BaseImportAction.ImportError(data.getRowNo(), I18N.text("ar.expired.bom.error")));
                            }
                        }
                    }
                }
            }
        }

        for (BaseImportDataAction.ImportData data : dataList) {
            String arId = data.getData().get(AccountsReceivableDetailObjConstants.Field.ArId.apiName, String.class);
            String salesOrderId = data.getData().get(AccountsReceivableDetailObjConstants.Field.OrderId.apiName, String.class);
            Optional<IObjectData> arData0 = arDatas.stream().filter(x -> x.getId().equals(arId)).findFirst();
            if (arData0.isPresent()) {
                String accountId = arData0.get().get("account_id", String.class);
                if (!Strings.isNullOrEmpty(salesOrderId)) {
                    Optional<IObjectData> salesOrderData0 = salesOrders.stream().filter(x -> x.getId().equals(salesOrderId)).findFirst();
                    if (salesOrderData0.isPresent()) {
                        String orderAccountId = salesOrderData0.get().get("account_id", String.class);
                        if (!Objects.equals(orderAccountId, accountId)) {
                            errorList.add(new BaseImportAction.ImportError(data.getRowNo(), I18N.text(ArI18NKey.CANNOT_SELECT_ORDER_OF_OTHER_ACCOUNT)));
                        }
                    }
                }
            }

            String pricing_mode = data.getData().get("pricing_mode", String.class, "");
            if (pricing_mode.equals("cycle")) {//周期性產品
                errorList.add(new BaseImportAction.ImportError(data.getRowNo(), I18N.text("ar.add.not_create_pricing_mode")));
            }
        }

        Map<String, List<BaseImportDataAction.ImportData>> maps = dataList.stream()
                .collect(Collectors.groupingBy(o -> o.getData().get(AccountsReceivableDetailObjConstants.Field.ArId.apiName, String.class)));
        for (Map.Entry<String, List<BaseImportDataAction.ImportData>> entry : maps.entrySet()) {
            String arId = entry.getKey();
            List<IObjectData> existDetails = existDetailMaps.get(arId);
            List<BaseImportDataAction.ImportData> importDetails = entry.getValue();
            BigDecimal temp = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(existDetails)) {
                temp = existDetails.get(0).get(AccountsReceivableDetailObjConstants.Field.PriceTaxAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            }
            for (BaseImportDataAction.ImportData detail : importDetails) {
                BigDecimal price_tax_amount = detail.getData().get(AccountsReceivableDetailObjConstants.Field.PriceTaxAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
                if (temp == BigDecimal.ZERO) {
                    temp = price_tax_amount;
                    continue;
                }
                if (temp.multiply(price_tax_amount).signum() < 0) {
                    errorList.add(new BaseImportAction.ImportError(detail.getRowNo(), I18N.text(ArI18NKey.PRICE_TAX_AMOUNT_SAME_SIGN)));
                }
            }
        }
        return errorList;
    }

    public List<IObjectData> getMatchArByAccountId(User user, String accountId, String collectionType) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AccountsReceivableNoteObjConstants.Field.AccountId.apiName, accountId);
        if (Objects.equals(CollectionTypeEnum.Red.getValue(), collectionType)) {
            SearchUtil.fillFilterLT(filters, AccountsReceivableNoteObjConstants.Field.NoSettledAmount.apiName, BigDecimal.ZERO);
        } else {
            SearchUtil.fillFilterGt(filters, AccountsReceivableNoteObjConstants.Field.NoSettledAmount.apiName, BigDecimal.ZERO);
        }
        SearchUtil.fillFilterIn(filters,"life_status", Lists.newArrayList(
                SystemConstants.LifeStatus.Normal.value,
                SystemConstants.LifeStatus.InChange.value));
        SearchUtil.fillFilterNotEq(filters, AccountsReceivableNoteObjConstants.Field.SettledStatus.apiName, SettledStatusEnum.All_Settled.getValue());
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setPermissionType(0); //0不走权限  1走权限
        searchTemplateQuery.setLimit(2000);
        searchTemplateQuery.setOffset(0);

        List<OrderBy> orders =  new ArrayList<>();
        orders.add(new OrderBy(AccountsReceivableNoteObjConstants.Field.DueDate.apiName, Boolean.TRUE));
        orders.add(new OrderBy(SystemConstants.Field.LastModifiedTime.apiName, Boolean.TRUE));

        searchTemplateQuery.setOrders(orders);

        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(user,AccountsReceivableNoteObjConstants.API_NAME, searchTemplateQuery);
        return queryResult.getData();
    }

    private List<IObjectData> getArDetailsByArIds(User user, List<String> arIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, AccountsReceivableDetailObjConstants.Field.ArId.apiName, arIds);
        query.setFilters(filters);
        query.setNeedReturnCountNum(false);
        query.setPermissionType(0);
        query.setLimit(2000);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(user,
                AccountsReceivableDetailObjConstants.API_NAME, query);
        return queryResult.getData();
    }
}