package com.facishare.crm.sfa.predefine.service.manager;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.DescribeWithSimplifiedChineseService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.constant.*;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.RecordTypeLogicService;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 对象初始化
 */
@Slf4j
@Component
public class ArInitObjManagerNew {

    @Resource
    private ArPaymentManagerNew arPaymentManagerNew;

    @Resource
    private ArSalesOrderManagerNew arSalesOrderManagerNew;

    @Resource
    CommonConfigManagerNew commonConfigManagerNew;

    @Resource
    RecordTypeLogicService recordTypeLogicService;

    @Resource
    ConfigService configService;

    @Resource
    private ArConfigManagerNew arConfigManagerNew;
    @Resource
    BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Resource
    ServiceFacade serviceFacade;
    @Resource
    protected IObjectDescribeService objectDescribeService;
    @Resource
    private DescribeWithSimplifiedChineseService describeWithSimplifiedChineseService;

    private static final String AR_TAG_AMOUNT = "{\"return_type\":\"currency\",\"description\":\"\",\"is_unique\":false,\"type\":\"count\",\"decimal_places\":2,\"sub_object_describe_apiname\":\"AccountsReceivableDetailObj\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"field_api_name\":\"sale_contract_line_id\",\"is_index\":false,\"default_result\":\"d_zero\",\"is_active\":true,\"count_type\":\"sum\",\"count_field_api_name\":\"price_tax_amount\",\"label\":\"已立应收金额\",\"count_to_zero\":true,\"api_name\":\"ar_tag_amount\",\"count_field_type\":\"currency\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"released\"}";// ignoreI18n
    private static final String NO_AR_TAG_AMOUNT = "{\"expression_type\":\"js\",\"return_type\":\"currency\",\"description\":\"\",\"is_unique\":false,\"type\":\"formula\",\"decimal_places\":2,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_index\":true,\"is_active\":true,\"expression\":\"$subtotal$-$ar_tag_amount$\",\"label\":\"待立应收金额\",\"api_name\":\"no_ar_tag_amount\",\"help_text\":\"\",\"status\":\"released\"}";// ignoreI18n
    private static final String DEBIT_SETTLED_AMOUNT = "{\"return_type\":\"currency\",\"description\":\"\",\"is_unique\":false,\"type\":\"count\",\"decimal_places\":2,\"sub_object_describe_apiname\":\"MatchNoteDetailObj\",\"is_required\":false,\"define_type\":\"package\",\"field_api_name\":\"debit_detail_object\",\"is_index\":true,\"default_result\":\"d_zero\",\"is_active\":true,\"count_type\":\"sum\",\"count_field_api_name\":\"this_match_amount\",\"label\":\"核销对象已结算金额\",\"count_to_zero\":false,\"api_name\":\"debit_settled_amount\",\"count_field_type\":\"currency\",\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"}";// ignoreI18n
    private static final String CREDIT_SETTLED_AMOUNT = "{\"return_type\":\"currency\",\"description\":\"\",\"is_unique\":false,\"type\":\"count\",\"decimal_places\":2,\"sub_object_describe_apiname\":\"MatchNoteDetailObj\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"field_api_name\":\"credit_detail_object\",\"is_index\":true,\"default_result\":\"d_zero\",\"is_active\":true,\"count_type\":\"sum\",\"count_field_api_name\":\"credit_match_amount\",\"label\":\"收付款对象已结算金额\",\"count_to_zero\":false,\"api_name\":\"credit_settled_amount\",\"count_field_type\":\"currency\",\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"}";// ignoreI18n
    private static final String NO_SETTLED_AMOUNT = "{\"expression_type\":\"js\",\"return_type\":\"number\",\"default_is_expression\":true,\"description\":\"\",\"is_unique\":false,\"type\":\"formula\",\"decimal_places\":2,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_index\":true,\"is_active\":true,\"expression\":\"$payment_amount$-$settled_amount$\",\"label\":\"待结算金额\",\"is_need_convert\":false,\"api_name\":\"no_settled_amount\",\"help_text\":\"\",\"status\":\"released\"}";// ignoreI18n
    private static final String SETTLED_AMOUNT = "{\"expression_type\":\"js\",\"return_type\":\"number\",\"describe_api_name\":\"AccountsReceivableDetailObj\",\"description\":\"\",\"is_unique\":false,\"type\":\"formula\",\"decimal_places\":2,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_index\":true,\"is_active\":true,\"expression\":\"$debit_settled_amount$+$credit_settled_amount$\",\"label\":\"已结算金额\",\"is_need_convert\":false,\"api_name\":\"settled_amount\",\"help_text\":\"\",\"status\":\"released\"}";// ignoreI18n
    private static final String SALE_CONTRACT_LINE_ID = "{\"default_is_expression\":false,\"is_unique\":false,\"type\":\"object_reference\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"default_value\":\"\",\"label\":\"销售合同明细\",\"target_api_name\":\"SaleContractLineObj\",\"target_related_list_name\":\"target_related_list_sale_contract_line_receivable_detail\",\"target_related_list_label\":\"应收单明细\",\"action_on_target_delete\":\"set_null\",\"is_need_convert\":false,\"api_name\":\"sale_contract_line_id\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"}";// ignoreI18n
    private static final String SALE_CONTRACT_ID = "{\"default_is_expression\":false,\"is_unique\":false,\"type\":\"object_reference\",\"is_required\":false,\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"value_type\":2,\"operator\":\"EQ\",\"field_name\":\"account_id\",\"field_values\":[\"$account_id$\"]}]}],\"define_type\":\"package\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"default_value\":\"\",\"label\":\"销售合同\",\"target_api_name\":\"SaleContractObj\",\"target_related_list_name\":\"target_related_list_sale_contract_receivable_note\",\"target_related_list_label\":\"应收单\",\"action_on_target_delete\":\"set_null\",\"is_need_convert\":false,\"api_name\":\"sale_contract_id\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"}";// ignoreI18n
    private static final String SALE_CONTRACT_ID_SETTELMENT = "{\"description\":\"销售合同编号\",\"is_unique\":false,\"type\":\"object_reference\",\"is_required\":false,\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"value_type\":2,\"operator\":\"EQ\",\"field_name\":\"account_id\",\"field_values\":[\"$account_id$\"]}]}],\"define_type\":\"package\",\"is_index\":true,\"is_active\":true,\"default_value\":\"\",\"label\":\"销售合同编号\",\"target_api_name\":\"SaleContractObj\",\"target_related_list_name\":\"related_list_sale_contract_settlement\",\"target_related_list_label\":\"结算单\",\"action_on_target_delete\":\"set_null\",\"api_name\":\"sale_contract_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\"}";// ignoreI18n
    public boolean init(String tenantId, User user) {
        long begin = System.currentTimeMillis();
        log.info("init begin, tenantId:{}, user:{}", tenantId, user);
        ArrayList<String> apiNames = Lists.newArrayList(
                AccountsReceivableNoteObjConstants.API_NAME,
                AccountsReceivableDetailObjConstants.API_NAME,
                MatchNoteObjConstants.API_NAME,
                MatchNoteDetailObjConstants.API_NAME,
                //SalesInvoiceObjConstants.API_NAME,
                //SalesInvoiceDetailObjConstants.API_NAME,
                SettlementObjConstants.API_NAME,
                SettlementDetailObjConstants.API_NAME
        );
        String preKey = "metadata_sys_is_open_";
        List<String> keys = Lists.newArrayList();
        for (String apiName : apiNames) {
            keys.add(preKey + apiName);
        }
        Map<String, String> configs = configService.queryTenantConfigs(user, keys);
        for (String apiName : apiNames) {
            String value = configs.get(preKey + apiName);
            if (!Objects.equals(value, "true")) {
                commonConfigManagerNew.createMetaDataOpenConfig(user, apiName, value);
                //分配业务类型
                recordTypeLogicService.recordTypeInit(user, apiName + "_default_layout__c", tenantId, apiName);
            }
        }
        log.info("init create obj end, tenantId:{}, user:{}, cost:{}", tenantId, user, System.currentTimeMillis() - begin);

        List<String> describeList = Lists.newArrayList(Utils.SALES_ORDER_API_NAME, Utils.SALES_ORDER_PRODUCT_API_NAME, Utils.CUSTOMER_PAYMENT_API_NAME, Utils.ORDER_PAYMENT_API_NAME
                , Utils.SALE_CONTRACT_LINE_API_NAME, Utils.PRODUCT_API_NAME, SFAPreDefineObject.Settlement.getApiName(), AccountsReceivableNoteObjConstants.API_NAME, AccountsReceivableDetailObjConstants.API_NAME);
        Map<String, IObjectDescribe> describeMap = describeWithSimplifiedChineseService.findByDescribeApiNameList(user, describeList);
        //2、回款加字段
        arPaymentManagerNew.addFieldForOpenAccountsReceivable(user,describeMap);
        arPaymentManagerNew.updateOrderPaymentDescribe(user,describeMap);
        log.info("init payment add field end, tenantId:{}, user:{}, cost:{}", tenantId, user, System.currentTimeMillis() - begin);

        //3、订单增加字段
        boolean isOpenCustomAccount = arConfigManagerNew.isCustomerAccountOpen(tenantId);
        arSalesOrderManagerNew.addFieldForOpenAccountsReceivable(user, isOpenCustomAccount,describeMap);
        log.info("init salesOrder add field end, tenantId:{}, user:{}, cost:{} ", tenantId, user, System.currentTimeMillis() - begin);

        addFieldForObject(describeMap, Utils.ORDER_PAYMENT_API_NAME, Lists.newArrayList(DEBIT_SETTLED_AMOUNT, CREDIT_SETTLED_AMOUNT, NO_SETTLED_AMOUNT, SETTLED_AMOUNT));
        if (bizConfigThreadLocalCacheService.isOpenSaleContract(user.getTenantId())) {
            addFieldForObject(describeMap, Utils.SALE_CONTRACT_LINE_API_NAME, Lists.newArrayList(AR_TAG_AMOUNT, NO_AR_TAG_AMOUNT));
            addFieldForObject(describeMap, AccountsReceivableNoteObjConstants.API_NAME, Lists.newArrayList(SALE_CONTRACT_ID));
            addFieldForObject(describeMap, AccountsReceivableDetailObjConstants.API_NAME, Lists.newArrayList(SALE_CONTRACT_LINE_ID));
            //给结算单加合同
            addFieldForObject(describeMap, SFAPreDefineObject.Settlement.getApiName(), Lists.newArrayList(SALE_CONTRACT_ID_SETTELMENT));
        }
        //给产品加统计字段
        //addFieldForObject(describeMap, Utils.PRODUCT_API_NAME, Lists.newArrayList(RELATED_SETTLEMENT_DETAIL_COUNT));

        arPaymentManagerNew.refreshCollectionType(tenantId);

        return true;
    }

    private void addFieldForObject(Map<String, IObjectDescribe> describeMap,String describeApiName,List<String> fieldApiNames) {
        IObjectDescribe describe = describeMap.get(describeApiName);
        if (describe == null) {
            return;
        }
        for (String fieldApiName : fieldApiNames) {
            describe = addField(fieldApiName, describe);
        }
        try {
            objectDescribeService.update(describe);
        } catch (MetadataServiceException e) {
            log.error("addFieldForObject failed:",  e);
        }
    }


    private IObjectDescribe addField(String fieldApiName, IObjectDescribe describe) {
        List<IFieldDescribe> describeFields = describe.getFieldDescribes();
        IFieldDescribe field = FieldDescribeFactory.newInstance(fieldApiName);
        if (describe.getFieldDescribe(field.getApiName()) == null) {
            describeFields.add(field);
        } else {
            return describe;
        }

        describe.setFieldDescribes(describeFields);
        return describe;
    }
}