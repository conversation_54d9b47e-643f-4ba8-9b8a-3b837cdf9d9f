package com.facishare.crm.sfa.predefine.service.manager;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.describebuilder.*;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.enums.ArStatusEnum;
import com.facishare.crm.sfa.utilities.constant.AccountsReceivableDetailObjConstants;
import com.facishare.crm.sfa.utilities.constant.PaymentObjConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderObjConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderProductObjConstants;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Created on 2020/11/23.
 */
@Slf4j
@Service
public class ArSalesOrderManagerNew {

    @Autowired
    private IObjectDescribeService objectDescribeService;

    @Autowired
    private ServiceFacade serviceFacade;

    /**
     * 应收模块开启时 订单&订单产品对象增加应收相关字段
     *
     * @param user
     */
    public void addFieldForOpenAccountsReceivable(User user, boolean isOpenCustomerAccount, Map<String, IObjectDescribe> describeMap) {
        String tenantId = user.getTenantId();
        try {
            List<IObjectDescribe> describeList = Lists.newArrayList(describeMap.get(Utils.SALES_ORDER_API_NAME), describeMap.get(Utils.SALES_ORDER_PRODUCT_API_NAME));
            for (IObjectDescribe objectDescribe : describeList) {
                if (Utils.SALES_ORDER_API_NAME.equals(objectDescribe.getApiName())) {
                    IFieldDescribe arAmountFieldDescribe = CurrencyFieldDescribeBuilder.builder()
                            .apiName(SalesOrderObjConstants.Field.ArAmount.apiName).label(SalesOrderObjConstants.Field.ArAmount.label)
                            .required(false).maxLength(14).length(12).decimalPlaces(2).roundMode(4).currencyUnit("￥").build();
                    arAmountFieldDescribe.setDefaultValue("$order_amount$");
                    arAmountFieldDescribe.setDefaultIsExpression(true);
                    FieldLayoutPojo arAmountFieldLayoutPojo = getFieldLayoutPojo(SystemConstants.RenderType.Currency.renderType, false, false);

                    IFieldDescribe prepayAmountFieldDescribe = CountFieldDescribeBuilder.builder()
                            .apiName(SalesOrderObjConstants.Field.PrepayAmount.apiName).label(SalesOrderObjConstants.Field.PrepayAmount.label)
                            .countFieldApiName(PaymentObjConstants.Field.Amount.apiName).subObjectDescribeApiName(Utils.CUSTOMER_PAYMENT_API_NAME)
                            .fieldApiName(PaymentObjConstants.Field.SalesOrderId.apiName).returnType(IFieldType.CURRENCY)
                            .countFieldType(IFieldType.CURRENCY).countType(Count.TYPE_SUM).wheres(Lists.newArrayList()).defaultResult("d_zero").decimalPlaces(2).build();
                    FieldLayoutPojo prepayAmountFieldLayoutPojo = getFieldLayoutPojo(SystemConstants.RenderType.Count.renderType, false, false);

                    IFieldDescribe arTagAmountFieldDescribe = CountFieldDescribeBuilder.builder()
                            .apiName(SalesOrderObjConstants.Field.ArTagAmount.apiName).label(SalesOrderObjConstants.Field.ArTagAmount.label)
                            .countFieldApiName(SalesOrderProductObjConstants.Field.ArTagAmount.apiName).subObjectDescribeApiName(Utils.SALES_ORDER_PRODUCT_API_NAME)
                            .fieldApiName(SalesOrderProductObjConstants.Field.OrderId.apiName).returnType(IFieldType.CURRENCY)
                            .countFieldType(IFieldType.CURRENCY).countType(Count.TYPE_SUM).wheres(Lists.newArrayList()).defaultResult("d_zero").decimalPlaces(2).index(true).build();
                    FieldLayoutPojo arTagAmountFieldLayoutPojo = getFieldLayoutPojo(SystemConstants.RenderType.Count.renderType, false, false);

                    IFieldDescribe noArTagAmountFieldDescribe = CountFieldDescribeBuilder.builder()
                            .apiName(SalesOrderObjConstants.Field.NoArTagAmount.apiName).label(SalesOrderObjConstants.Field.NoArTagAmount.label)
                            .countFieldApiName(SalesOrderProductObjConstants.Field.NoArTagAmount.apiName).subObjectDescribeApiName(Utils.SALES_ORDER_PRODUCT_API_NAME)
                            .fieldApiName(SalesOrderProductObjConstants.Field.OrderId.apiName).returnType(IFieldType.CURRENCY)
                            .countFieldType(IFieldType.CURRENCY).countType(Count.TYPE_SUM).wheres(Lists.newArrayList()).defaultResult("d_zero").decimalPlaces(2).index(true).build();
                    FieldLayoutPojo noArTagAmountFieldLayoutPojo = getFieldLayoutPojo(SystemConstants.RenderType.Count.renderType, false, false);

                    List<ISelectOption> arStatusSelectOptions = Arrays.stream(ArStatusEnum.values())
                            .map(statusEnum -> SelectOptionBuilder.builder().value(statusEnum.getValue()).label(statusEnum.getLabel()).build()).collect(Collectors.toList());
                    IFieldDescribe arStatusFieldDescribe = SelectOneFieldDescribeBuilder.builder()
                            .apiName(SalesOrderObjConstants.Field.ArStatus.apiName).label(SalesOrderObjConstants.Field.ArStatus.label)
                            .selectOptions(arStatusSelectOptions).expression("IF(EQUALS('' + $ar_tag_amount$, '0.00'), \"1\", IF($no_ar_tag_amount$ >0, \"2\", \"3\"))")
                            .defaultIsExpression(false).required(false).build();
                    arStatusFieldDescribe.set("is_need_calculate", true);
                    FieldLayoutPojo arStatusFieldLayoutPojo = getFieldLayoutPojo(SystemConstants.RenderType.SelectOne.renderType, false, false);

                    List<Tuple<IFieldDescribe, FieldLayoutPojo>> addFieldTupleList = Lists.newArrayList();
                    //if (isOpenCustomerAccount)
                    {
                        IFieldDescribe accountSettledAmountFieldDescribe =
                                CurrencyFieldDescribeBuilder.builder()
                                        .apiName(SalesOrderObjConstants.Field.AccountSettledAmount.apiName).label(SalesOrderObjConstants.Field.AccountSettledAmount.label)
                                        .required(false).maxLength(14).length(12).decimalPlaces(2).roundMode(4).currencyUnit("￥").build();
                        FieldLayoutPojo accountSettledAmountFieldPojo = getFieldLayoutPojo(SystemConstants.RenderType.Currency.renderType, false, false);
                        addFieldTupleList.add(Tuple.of(accountSettledAmountFieldDescribe, accountSettledAmountFieldPojo));
                    }
                    {
                        IFieldDescribe settledAmountFieldDescribe = CountFieldDescribeBuilder.builder()
                                .apiName(SalesOrderObjConstants.Field.SettledAmount.apiName).label(SalesOrderObjConstants.Field.SettledAmount.label)
                                .countFieldApiName(AccountsReceivableDetailObjConstants.Field.SettledAmount.apiName).subObjectDescribeApiName(AccountsReceivableDetailObjConstants.API_NAME)
                                .fieldApiName(AccountsReceivableDetailObjConstants.Field.SourceObject.apiName).returnType(IFieldType.CURRENCY)
                                .countFieldType(IFieldType.CURRENCY).countType(Count.TYPE_SUM).wheres(Lists.newArrayList()).defaultResult("d_zero").decimalPlaces(2).build();

                        FieldLayoutPojo settledAmountFieldPojo = getFieldLayoutPojo(SystemConstants.RenderType.Currency.renderType, false, false);
                        addFieldTupleList.add(Tuple.of(settledAmountFieldDescribe, settledAmountFieldPojo));
                    }
                    addFieldTupleList.add(Tuple.of(arAmountFieldDescribe, arAmountFieldLayoutPojo));
                    addFieldTupleList.add(Tuple.of(prepayAmountFieldDescribe, prepayAmountFieldLayoutPojo));
                    addFieldTupleList.add(Tuple.of(arTagAmountFieldDescribe, arTagAmountFieldLayoutPojo));
                    addFieldTupleList.add(Tuple.of(noArTagAmountFieldDescribe, noArTagAmountFieldLayoutPojo));
                    addFieldTupleList.add(Tuple.of(arStatusFieldDescribe, arStatusFieldLayoutPojo));

                    updateDescribeDefaultLayout(user, objectDescribe, addFieldTupleList, Utils.SALES_ORDER_API_NAME);
                } else if (Utils.SALES_ORDER_PRODUCT_API_NAME.equals(objectDescribe.getApiName())) {
                    IFieldDescribe arTagAmountFieldDescribe = CountFieldDescribeBuilder.builder()
                            .apiName(SalesOrderProductObjConstants.Field.ArTagAmount.apiName).label(SalesOrderProductObjConstants.Field.ArTagAmount.label)
                            .countFieldApiName(AccountsReceivableDetailObjConstants.Field.PriceTaxAmount.apiName).subObjectDescribeApiName(AccountsReceivableDetailObjConstants.API_NAME)
                            .fieldApiName(AccountsReceivableDetailObjConstants.Field.OrderProductId.apiName).returnType(IFieldType.CURRENCY)
                            .countFieldType(IFieldType.CURRENCY).countType(Count.TYPE_SUM).wheres(Lists.newArrayList()).defaultResult("d_zero").decimalPlaces(2).index(true).build();
                    FieldLayoutPojo arTagAmountFieldLayoutPojo = getFieldLayoutPojo(SystemConstants.RenderType.Count.renderType, false, false);

                    IFieldDescribe arTagQuantityFieldDescribe = CountFieldDescribeBuilder.builder()
                            .apiName(SalesOrderProductObjConstants.Field.ArTagQuantity.apiName).label(SalesOrderProductObjConstants.Field.ArTagQuantity.label)
                            .countFieldApiName(AccountsReceivableDetailObjConstants.Field.ArQuantity.apiName).subObjectDescribeApiName(AccountsReceivableDetailObjConstants.API_NAME)
                            .fieldApiName(AccountsReceivableDetailObjConstants.Field.OrderProductId.apiName).returnType(IFieldType.NUMBER)
                            .countFieldType(IFieldType.NUMBER).countType(Count.TYPE_SUM).wheres(Lists.newArrayList()).defaultResult("d_zero").decimalPlaces(2).build();
                    FieldLayoutPojo arTagQuantityFieldLayoutPojo = getFieldLayoutPojo(SystemConstants.RenderType.Count.renderType, false, false);

                    IFieldDescribe noArTagAmountFieldDescribe = FormulaFieldDescribeBuilder.builder()
                            .apiName(SalesOrderProductObjConstants.Field.NoArTagAmount.apiName).label(SalesOrderProductObjConstants.Field.NoArTagAmount.label)
                            .expression("$product_ar_amount$-$ar_tag_amount$").required(false).returnType(IFieldType.CURRENCY).defaultToZero(true).decimalPlaces(2).build();
                    FieldLayoutPojo noArTagAmountFieldLayoutPojo = getFieldLayoutPojo(SystemConstants.RenderType.Formula.renderType, false, false);

                    IFieldDescribe noArTagQuantityFieldDescribe = FormulaFieldDescribeBuilder.builder()
                            .apiName(SalesOrderProductObjConstants.Field.NoArTagQuantity.apiName).label(SalesOrderProductObjConstants.Field.NoArTagQuantity.label)
                            .expression("$quantity$-$ar_tag_quantity$").required(false).returnType(IFieldType.NUMBER).defaultToZero(true).decimalPlaces(2).build();
                    FieldLayoutPojo noArTagQuantityFieldLayoutPojo = getFieldLayoutPojo(SystemConstants.RenderType.Formula.renderType, false, false);

                    IFieldDescribe orderProductAmountFieldDescribe = FormulaFieldDescribeBuilder.builder()
                            .apiName(SalesOrderProductObjConstants.Field.ProductArAmount.apiName).label(SalesOrderProductObjConstants.Field.ProductArAmount.label)
                            .expression("$subtotal$*($order_id__r.ar_amount$/$order_id__r.product_amount$)").required(false).returnType(IFieldType.CURRENCY).defaultToZero(true).decimalPlaces(2).build();
                    FieldLayoutPojo orderProductAmountFieldLayoutPojo = getFieldLayoutPojo(SystemConstants.RenderType.Formula.renderType, false, false);

                    List<Tuple<IFieldDescribe, FieldLayoutPojo>> addFieldTupleList = Lists.newArrayList();
                    addFieldTupleList.add(Tuple.of(arTagAmountFieldDescribe, arTagAmountFieldLayoutPojo));
                    addFieldTupleList.add(Tuple.of(arTagQuantityFieldDescribe, arTagQuantityFieldLayoutPojo));
                    addFieldTupleList.add(Tuple.of(noArTagAmountFieldDescribe, noArTagAmountFieldLayoutPojo));
                    addFieldTupleList.add(Tuple.of(noArTagQuantityFieldDescribe, noArTagQuantityFieldLayoutPojo));
                    addFieldTupleList.add(Tuple.of(orderProductAmountFieldDescribe, orderProductAmountFieldLayoutPojo));

                    updateDescribeDefaultLayout(user, objectDescribe, addFieldTupleList, Utils.SALES_ORDER_PRODUCT_API_NAME);
                }
            }
        } catch (Exception e) {
            log.error("addFieldForOpenAccountsReceivable exception,tenantId:{}", tenantId, e);
            throw new ValidateException("Add SalesOrderObj & SalesOrderProductObj fields failed.");
        }
    }

    private FieldLayoutPojo getFieldLayoutPojo(String renderType, boolean readOnly, boolean required) {
        FieldLayoutPojo fieldLayoutPojo = new FieldLayoutPojo();
        fieldLayoutPojo.setRenderType(renderType);
        fieldLayoutPojo.setReadonly(readOnly);
        fieldLayoutPojo.setRequired(required);
        return fieldLayoutPojo;
    }

    private void removeExistFields(LayoutExt layoutExt, List<Tuple<IFieldDescribe, FieldLayoutPojo>> addFieldTupleList) {
        layoutExt.getFormComponent().ifPresent(formComponentExt -> {
            Iterator<Tuple<IFieldDescribe, FieldLayoutPojo>> iterator = addFieldTupleList.iterator();
            while (iterator.hasNext()) {
                Tuple<IFieldDescribe, FieldLayoutPojo> tuple = iterator.next();
                if (formComponentExt.containsField(tuple.getKey().getApiName())) {
                    iterator.remove();
                }
            }
        });
    }

    private void updateDescribeDefaultLayout(User user,
                                             IObjectDescribe objectDescribe,
                                             List<Tuple<IFieldDescribe, FieldLayoutPojo>> addFieldTupleList,
                                             String objectApiName) throws MetadataServiceException {
        List<IFieldDescribe> fieldDescribes = Lists.newArrayList();
        addFieldTupleList.forEach(tuple -> {
            if (!objectDescribe.containsField(tuple.getKey().getApiName())) {
                fieldDescribes.add(tuple.getKey());
            }
        });
        if (CollectionUtils.isNotEmpty(fieldDescribes)) {
            objectDescribeService.addCustomFieldDescribe(objectDescribe, fieldDescribes);
        }

        ILayout layout = serviceFacade.findDefaultLayout(user, SystemConstants.LayoutType.Detail.layoutType, objectApiName);
        LayoutExt layoutExt = LayoutExt.of(layout);
        removeExistFields(layoutExt, addFieldTupleList);
        if (CollectionUtils.isNotEmpty(addFieldTupleList)) {
            addFieldTupleList.forEach(tuple -> layoutExt.addField(tuple.getKey(), tuple.getValue()));
            serviceFacade.updateLayout(user, layout);
        }
    }

}
