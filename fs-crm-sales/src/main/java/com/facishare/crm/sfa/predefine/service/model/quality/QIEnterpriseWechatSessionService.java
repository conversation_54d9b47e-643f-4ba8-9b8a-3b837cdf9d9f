package com.facishare.crm.sfa.predefine.service.model.quality;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.sfa.lto.quality.models.QualityInspectionConstants.CommonConstants;
import com.facishare.crm.sfa.lto.qywx.mongo.WechatConversion;
import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.sfa.lto.utils.CommonSqlUtil;
import com.facishare.crm.sfa.lto.utils.StringUtil;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.quality.QIGetWechatConversionModel.SessionArg;
import com.facishare.crm.sfa.predefine.service.qywx.mongo.ConversionMongoDaoExt;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.util.CommonSqlUtils;
import com.facishare.crm.util.Safes;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.CommonSqlOperator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.WhereParam;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@ServiceModule("qi_enterprise_wechat_session")
@Service
public class QIEnterpriseWechatSessionService {
	@Autowired
	private EIEAConverter eieaConverter;
	@Autowired
	private ConversionMongoDaoExt conversionMongoDaoExt;

	@ServiceMethod("get_wechat_conversion")
	public QIGetWechatConversionModel.Result queryWechatConversion(ServiceContext serviceContext, QIGetWechatConversionModel.Arg arg) {
		QIGetWechatConversionModel.checkArg(arg);

		String tenantId = serviceContext.getTenantId();
		String id = arg.getId();

		SessionArg sarg = SessionArg.builder().tenantId(tenantId).id(id).build();

		//通知明细
		/*
		Map pushMsgMap = findQualityInspection(tenantId, id);
		log.debug("---------------findQualityInspection: tenantId:{}, id:{}, {}", tenantId, id, pushMsgMap);
		if(CollectionUtil.isEmpty(pushMsgMap)) {
			return QIGetWechatConversionModel.Result.builder().dataList(Lists.newArrayList()).build();
		}
		sarg.setSessionId((String)pushMsgMap.get("session_id"));
		sarg.setChatId((String)pushMsgMap.get("chat_id"));
		sarg.setRuleId((String)pushMsgMap.get("rule_id"));
		sarg.setSeq(StringUtil.convertInt((String)pushMsgMap.get("seq"), 0) );
		*/

		IObjectData objectData = findQualityInspection(serviceContext, id);
		log.debug("---------------findQualityInspection: tenantId:{}, id:{}, {}", tenantId, id, objectData);
		if(objectData == null) {
			return QIGetWechatConversionModel.Result.builder().dataList(Lists.newArrayList()).build();
		}
		sarg.setSessionId(AccountUtil.getStringValue(objectData, "session_id", ""));
		sarg.setChatId(AccountUtil.getStringValue(objectData, "chat_id", ""));
		sarg.setRuleId(AccountUtil.getStringValue(objectData, "rule_id", ""));
		sarg.setSeq(AccountUtil.getIntegerValue(objectData, "seq", 0));

		//获取session pg
		Map dataMap = findSession(tenantId, sarg.getSessionId());
		log.debug("---------------findSession: tenantId:{}, sessionId:{}, {}", tenantId, sarg.getSessionId(), dataMap);
		if(CollectionUtil.isEmpty(dataMap)) {
			return QIGetWechatConversionModel.Result.builder().dataList(Lists.newArrayList()).build();
		}
		String oppositeCipherId = StringUtil.convertString((String) dataMap.get("opposite_plaintext_id"), "");
		int type = (Integer) dataMap.get("type");
		if( type == 3 || type == 4 ) {
			//群聊
			sarg.setRoomId(oppositeCipherId);
		}else {
			//单聊
			String ownerCipherId = StringUtil.convertString((String) dataMap.get("owner_plaintext_id"), "");
			sarg.setFromId(ownerCipherId);
			sarg.setToId(oppositeCipherId);
		}

		log.debug("---------------SessionArg:{}", sarg);

		//获取会话内容mongodb
		String ea = eieaConverter.enterpriseIdToAccount(StringUtil.convertInt(tenantId, 0));
		List<WechatConversion> result;
		if (StringUtils.isNotEmpty(sarg.getRoomId())) {
			result = conversionMongoDaoExt.queryListByRoomId(tenantId, sarg.getRoomId(), arg.getSeq(), arg.getOrder());
		} else {
			result = conversionMongoDaoExt.queryListByFromToId(tenantId, sarg.getFromId(), sarg.getToId(), arg.getSeq(), arg.getOrder());
		}
		log.debug("---------------queryListFromMongoDB:{}, ea:{}, tenantId:{}", result, ea, tenantId);

		List<QIWechatConversion> qiResult = Lists.newArrayList();
		if(CollectionUtil.isNotEmpty(result)) {
			List<Map> dataList = findAllQualityInspectionByChatId(tenantId, result.stream().map(WechatConversion::getId).collect(Collectors.toList()));
			for (WechatConversion wc : result) {
				qiResult.add(fillTag(tenantId, dataList, wc));
			}
		}
		log.debug("---------------returnResult:{}", qiResult);
		return QIGetWechatConversionModel.Result.builder().dataList(qiResult).build();
	}

	private QIWechatConversion fillTag(String tenantId, List<Map> dataList, WechatConversion wc){
		QIWechatConversion qi = convert(wc);
		if(CollectionUtil.isNotEmpty(dataList)){
			List<String> dirtys = Lists.newArrayList();
			for(Map map : dataList){
				String chatId = StringUtil.convertString((String)map.get("chat_id"), "");
				String word = StringUtil.convertString((String)map.get("dirty_words"), "");
				String sessionFeature = StringUtil.convertString((String)map.get("session_feature"), "");
				String ruleId = StringUtil.convertString((String)map.get("rule_id"), "");

				if ( wc.getId().equals(chatId) ) {
					if(StringUtil.isNotEmpty(word)) {
						//敏感词标红，打标记
						dirtys.add(word);
						qi.setDirty(true);
					}
					if(StringUtil.isNotEmpty(sessionFeature)) {
						//超时回复，打标记
						qi.setTimeOut(true);
						qi.setRuleTimeOutText(getTimeoutText(tenantId, ruleId));
					}
				}
			}
			if (qi.isDirty()){
				qi.setDirtyWords(dirtys);
			}
		}
		return qi;
	}

	private QIWechatConversion convert(WechatConversion wc){
		QIWechatConversion qi = new QIWechatConversion();
		qi.setId(wc.getId());
		qi.setFsEa(wc.getFsEa());
		qi.setMessageId(wc.getMessageId());
		qi.setSeq(wc.getSeq());
		qi.setKeyVersion(wc.getKeyVersion());
		qi.setFromUser(wc.getFromUser());
		qi.setToList(wc.getToList());
		qi.setRoomId(wc.getRoomId());
		qi.setMessageTime(wc.getMessageTime());
		qi.setMessageType(wc.getMessageType());
		qi.setContent(wc.getContent());
		qi.setMd5sum(wc.getMd5sum());
		qi.setSdkFileId(wc.getSdkFileId());
		qi.setFileSize(wc.getFileSize());
		qi.setAttachment(wc.getAttachment());
		qi.setNpath(wc.getNpath());
		qi.setFileName(wc.getFileName());
		qi.setFileExt(wc.getFileExt());
		qi.setCreateTime(wc.getCreateTime());
		qi.setUpdateTime(wc.getUpdateTime());
		return qi;
	}

	private String getTimeoutText(String tenantId, String ruleId){
		Map map = findFeatureRule(tenantId, ruleId);
		//String defaultTime = "30分钟";
		String defaultTime = "30" + I18N.text("fs_pay.business.pay_check_tips.minute");
		if(CollectionUtil.isNotEmpty(map)){
			String timeOutMinute = StringUtil.convertString((String)map.get("session_feature_rule"), "00:30");

			if(StringUtil.isEmpty(timeOutMinute) || !timeOutMinute.contains(":")) return defaultTime;

			List<String> times = StringUtil.split(timeOutMinute, ":");
			if(times == null || times.size() != 2) return defaultTime;

			int hour = StringUtil.convertInt(times.get(0), 0);
			int minute = StringUtil.convertInt(times.get(1), 0);
			StringBuilder sb = new StringBuilder();
			if (hour > 0){
				sb.append(hour).append(I18N.text("fs_pay.business.pay_check_tips.hour"));
			}
			if (minute > 0){
				sb.append(minute).append(I18N.text("fs_pay.business.pay_check_tips.minute"));
			}
			defaultTime = sb.toString();
		}
		return defaultTime;
	}

	private Map findFeatureRule(String tenantId, String ruleId) {
		StringBuilder sb = new StringBuilder();
		sb.append("select id, name, session_feature, session_feature_rule, target_type, session_type, msg_push from biz_quality_inspection_rule ");
		sb.append(String.format(" where tenant_id = '%s' and id = '%s' and type = 3 and is_deleted = 0", tenantId, ruleId));

		return Safes.first(CommonSqlUtil.findBySql(tenantId, sb.toString()));
	}

	private Map findSession(String tenantId, String sessionId) {
		List<WhereParam> wheres = Lists.newArrayList();
		CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
		CommonSqlUtils.addWhereParam(wheres, CommonConstants.ID, CommonSqlOperator.EQ, Lists.newArrayList(sessionId));
		CommonSqlUtils.addWhereParam(wheres, CommonConstants.IS_DELETED, CommonSqlOperator.EQ, Lists.newArrayList("0"));
		Map dataMap = Maps.newHashMap();
		try {
			dataMap = Safes.first(CommonSqlUtils.queryData(tenantId, "biz_wechat_session", wheres));
		} catch (MetadataServiceException e) {
			log.error("findSession error,{}", wheres, e);
		}
		return dataMap;
	}

	private List<Map> findAllQualityInspectionByChatId(String tenantId, List<Object> chatIds) {
		List<WhereParam> wheres = Lists.newArrayList();
		CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
		CommonSqlUtils.addWhereParam(wheres, "chat_id", CommonSqlOperator.IN, chatIds);
		CommonSqlUtils.addWhereParam(wheres, CommonConstants.IS_DELETED, CommonSqlOperator.EQ, Lists.newArrayList("0"));
		List<Map> dataMap = Lists.newArrayList();
		try {
			dataMap = CommonSqlUtils.queryData(tenantId, "biz_quality_inspection", wheres);
		} catch (MetadataServiceException ex) {
			log.error("findAllQualityInspectionByChatId {} error", wheres, ex);
		}

		return dataMap;
	}

	/*private Map findQualityInspection(String tenantId, String id) {
		List<WhereParam> wheres = Lists.newArrayList();
		CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
		CommonSqlUtils.addWhereParam(wheres, CommonConstants.ID, CommonSqlOperator.EQ, Lists.newArrayList(id));
		CommonSqlUtils.addWhereParam(wheres, CommonConstants.IS_DELETED, CommonSqlOperator.EQ, Lists.newArrayList("0"));
		Map dataMap = Maps.newHashMap();
		try {
			dataMap = Safes.first(CommonSqlUtils.queryData(tenantId, "biz_quality_inspection", wheres));
		} catch (MetadataServiceException e) {
			log.error("findQualityInspection error,{}", wheres, e);
		}
		return dataMap;
	}*/

	@Autowired
	MetaDataFindService metaDataFindService;
	private IObjectData findQualityInspection(ServiceContext serviceContext, String id) {
		SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
		List<IFilter> filters = Lists.newArrayList();
		SearchUtil.fillFilterEq(filters, CommonConstants._ID, id);
		searchTemplateQuery.setFilters(filters);

		QueryResult<IObjectData> queryResult = metaDataFindService.findBySearchQuery(serviceContext.getUser(), SFAPreDefineObject.QualityInspection.getApiName(), searchTemplateQuery);
		return Safes.first(queryResult.getData());
	}
}
