package com.facishare.crm.sfa.predefine.service.model.quality;

import com.facishare.crm.sfa.lto.utils.StringUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface QIGetWechatConversionModel {
	String record_dirtyword_layout = "record_dirtywords__c";
	String record_feature_layout = "record_sessionfeature__c";
	String record_action_layout = "record_sessionaction__c";

	String dirtyword_layout_api = "layout_QualityInspectionRuleObj_dirtywords__c";
	String action_layout_api = "layout_QualityInspectionRuleObj_action__c";
	String feature_layout_api = "layout_QualityInspectionRuleObj_feature__c";

	@Builder
	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	class SessionArg {
		private String tenantId;
		private String id;
		private String sessionId;
		private String chatId;
		private String ruleId;
		/**
		 * 群id，群聊查询必填
		 */
		private String roomId;
		/**
		 * 发送方id，单聊必填
		 */
		private String fromId;
		/**
		 * 接收方id，单聊必填
		 */
		private String toId;
		/**
		 * 向上翻页时的会话序号（不填默认取最后10条）
		 */
		private Integer seq;
	}


	  @Data
	  @Builder
		@AllArgsConstructor
		@NoArgsConstructor
    class Arg {
			private String id;
			private Integer seq;
			private Integer order;
    }

	static void checkArg(Arg arg) {
		/*if (StringUtils.isEmpty(arg.getRoomId()) && StringUtils.isAnyEmpty(arg.getFromId(), arg.getToId())) {
			throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
		}


		if (StringUtils.isEmpty(arg.getSessionId()) && StringUtils.isEmpty(arg.getFromId(), arg.getToId())) {
			throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
		}

		if (arg.getPageSize() == null) {
			arg.setPageSize(10);
		}
		if (arg.getPageSize() > 10) {
			throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
		}
		*/

		/*
		if ((arg.getStartTime() == null && arg.getEndTime() != null)
				|| (arg.getStartTime() != null && arg.getEndTime() == null)) {
			throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
		}*/

	}

	@Builder
	@Data
    class Result {
        private List<QIWechatConversion> dataList;
    }

	@Builder
	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	class QIUser {

		/**
		 * member_type定义
		 * monitor:  1:member 3:group 5: role
		 * msg_user: 2:member 4:group 6: role
		 */
		private List<String> member;
		private List<String> group;
		private List<String> role;
	}

	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	class JudgeQIReq {
		//当前数据的主键id
		@JsonProperty("objectDataId")
		private String objectDataId;
		private JudgeResultArgs args;
	}

	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	class JudgeResultArgs {
		@JsonProperty("form_judge_result")
		private String judgeResult;
		@JsonProperty("form_judge_info")
		private String judgeInfo;
	}

	static void checkArg(JudgeQIReq req) {
		/**
		 * 检查传参数据
		 */
		if(req == null || StringUtil.isEmpty(req.getObjectDataId()) || req.getArgs() == null || StringUtil.isEmpty(req.getArgs().getJudgeResult())){
			throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
		}

		String code = req.getArgs().getJudgeResult();
		if(!code.equals("0") && !code.equals("1")){
			throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
		}
	}
}
