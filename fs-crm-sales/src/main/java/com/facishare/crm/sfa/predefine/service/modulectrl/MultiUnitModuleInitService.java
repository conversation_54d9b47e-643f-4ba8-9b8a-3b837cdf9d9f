package com.facishare.crm.sfa.predefine.service.modulectrl;

import com.facishare.crm.constants.LayoutConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.CommonUnitService;
import com.facishare.crm.sfa.predefine.service.CrmMenuInitService;
import com.facishare.crm.sfa.predefine.service.DescribeWithSimplifiedChineseService;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule;
import com.facishare.crm.sfa.predefine.service.model.CrmMenuInitFindMenuDataList;
import com.facishare.crm.sfa.predefine.service.real.invoice.InvoiceService;
import com.facishare.crm.sfa.task.AsyncTaskProducer;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.InvoiceApplicationConstants;
import com.facishare.crm.sfa.utilities.util.SFABizLogUtil;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeProxy;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.FuncCodePrivilege;
import com.facishare.paas.appframework.privilege.dto.UpdateRoleModifiedFuncPrivilege;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.ILayoutService;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/7/5 10:10
 * @instruction 多单位开关控制类
 * @IgnoreI18n or IgnoreI18nFile or @IgnoreI18nFile
 */
@Component
@Slf4j
public class MultiUnitModuleInitService extends AbstractModuleInitService {

    private static final String LAYOUT_DETAIL = "detail";
    private static final String LAYOUT_LIST = "list";


    private static final List<String> funcodes = Lists.newArrayList(
            "UnitInfoObj",
            "UnitInfoObj||View",
            "UnitInfoObj||Add",
            "UnitInfoObj||Edit",
            "UnitInfoObj||Abolish",
            "UnitInfoObj||Relate",
            "UnitInfoObj||Recover",
            "UnitInfoObj||Delete",
            "UnitInfoObj||Import",
            "UnitInfoObj||Export",
            "UnitInfoObj||ChangeOwner",
            "UnitInfoObj||EditTeamMember",
            "UnitInfoObj||Print",
            "UnitInfoObj||Lock",
            "UnitInfoObj||Unlock");

    @Resource(name = "crmMenuInitServiceSFA")
    CrmMenuInitService crmMenuInitService;
    @Autowired
    IObjectDescribeService objectDescribeService;
    @Autowired
    ObjectDataServiceImpl objectDataService;
    @Autowired
    ServiceFacade serviceFacade;
    @Autowired
    EnterpriseInitService enterpriseInitService;
    @Autowired
    private ILayoutService layoutService;
    @Autowired
    private FunctionPrivilegeProxy privilegeProxy;
    @Autowired
    private FunctionPrivilegeProxy functionPrivilegeProxy;
    @Autowired
    ConfigService configService;
    @Autowired
    private CommonUnitService commonUnitService;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private AsyncTaskProducer asyncTaskCommonProducer;
    @Autowired
    private DescribeWithSimplifiedChineseService describeWithSimplifiedChineseService;

    private static final String BIZ = "refresh_sale_contract_line_multi_field";

    private static final String isMultipleUnitField = "{\"type\":\"true_or_false\",\"define_type\":\"package\",\"is_active\":true,\"is_index\":true,\"is_need_convert\":false,\"is_required\":true,\"is_unique\":false,\"description\":\"是否多单位\",\"api_name\":\"is_multiple_unit\",\"label\":\"是否多单位\",\"default_value\":false,\"status\":\"released\",\"options\":[{\"label\":\"是\",\"value\":true},{\"label\":\"否\",\"value\":false}]}";

    private static final String priceBookProductListLayout = "{\"components\":[{\"buttons\":[],\"api_name\":\"table_component\",\"ref_object_api_name\":\"PriceBookProductObj\",\"include_fields\":[{\"api_name\":\"product_name\",\"render_type\":\"quote\",\"field_name\":\"product_name\"},{\"api_name\":\"unit\",\"render_type\":\"quote\",\"field_name\":\"unit\"},{\"api_name\":\"pricebook_sellingprice\",\"render_type\":\"currency\",\"field_name\":\"pricebook_sellingprice\"},{\"api_name\":\"discount\",\"render_type\":\"percentile\",\"field_name\":\"discount\"}],\"type\":\"table\"}],\"buttons\":[{\"action_type\":\"default\",\"api_name\":\"Add_button_default\",\"action\":\"Add\",\"label\":\"新建\"}],\"package\":\"CRM\",\"ref_object_api_name\":\"PriceBookProductObj\",\"layout_type\":\"list\",\"display_name\":\"价目表明细终端列表布局\",\"is_default\":false,\"agent_type\":\"agent_type_mobile\",\"is_deleted\":false,\"api_name\":\"layout_PriceBookProductObj_mobile\",\"is_show_fieldname\":true,\"version\":\"1\"}";
    private static final String productListLayout = "{\"components\":[{\"buttons\":[],\"api_name\":\"table_component\",\"ref_object_api_name\":\"ProductObj\",\"include_fields\":[{\"api_name\":\"name\",\"label\":\"产品名称\",\"render_type\":\"text\"},{\"api_name\":\"unit\",\"label\":\"单位\",\"render_type\":\"select_one\"},{\"api_name\":\"price\",\"label\":\"价格\",\"render_type\":\"currency\"}],\"type\":\"table\"}],\"buttons\":[],\"package\":\"CRM\",\"ref_object_api_name\":\"ProductObj\",\"layout_type\":\"list\",\"display_name\":\"产品列表布局\",\"is_default\":false,\"agent_type\":\"agent_type_mobile\",\"is_deleted\":false,\"api_name\":\"layout_ProductObj_mobile\",\"is_show_fieldname\":true,\"version\":\"1\"}";

    private static final String invoiceModeThreeActualUnit = "{ \"describe_api_name\": \"InvoiceApplicationLinesObj\", \"is_index\": false, \"is_active\": true, \"quote_field_type\": \"select_one\", \"is_unique\": false, \"label\": \"实际单位\", \"type\": \"quote\", \"is_abstract\": null, \"quote_field\": \"order_product_id__r.actual_unit\", \"is_required\": false, \"api_name\": \"product_actual_unit\", \"define_type\": \"package\", \"is_index_field\": false, \"is_single\": false, \"help_text\": \"\", \"status\": \"new\" }";
    private static final String FIELD_UNIT_ID = "{\"default_is_expression\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"description\":\"\",\"where_type\":\"field\",\"type\":\"object_reference\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_values\":[\"normal\"],\"field_name\":\"life_status\"}]}],\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"target_api_name\":\"UnitInfoObj\",\"label\":\"单位\",\"target_related_list_name\":\"target_related_list_bom_unit_id\",\"target_related_list_label\":\"单位\",\"action_on_target_delete\":\"cascade_delete\",\"related_wheres\":[],\"api_name\":\"unit_id\",\"is_index_field\":true,\"status\":\"released\",\"help_text\":\"\"}";

    @Override
    public String getModuleCode() {
        return IModuleInitService.MODULE_MULTIPLE_UNIT;
    }

    @Override
    public ConfigCtrlModule.Result initModule(String tenantId, String userId) {
        User user = new User(tenantId, userId);
        String cpq = configService.findTenantConfig(user, "cpq");
        if (StringUtils.equals("1",cpq)) {
            return ConfigCtrlModule.Result.builder()
                    .errCode(ConfigCtrlModule.ResultInfo.Fail.getErrCode())
                    .errMessage(I18N.text(BomI18NKeyUtil.SFA_CPQ_CONFIG_MULTIPLE_UNIT_WARN))
                    .value(
                            ConfigCtrlModule.Value.builder()
                                    .openStatus(ConfigCtrlModule.OpenStatus.CLOSE.toString()).build()
                    ).build();
        }
        String moduleConfig = configService.findTenantConfig(user, IS_OPEN_ATTRIBUTE);
        if (StringUtils.isNotEmpty(moduleConfig) && moduleConfig.equals("1")) {
            return ConfigCtrlModule.Result.builder()
                    .errCode(ConfigCtrlModule.ResultInfo.Fail.getErrCode())
                    .errMessage(I18N.text("sfa.multi.init.0"))
                    .value(
                            ConfigCtrlModule.Value.builder()
                                    .openStatus(ConfigCtrlModule.OpenStatus.CLOSE.toString()).build()
                    ).build();
        }
        boolean isSpuOpen = SFAConfigUtil.isSpuOpen(tenantId);
        boolean isOpenSaleContract = bizConfigThreadLocalCacheService.isOpenSaleContract(tenantId);
        List<String> xxDetailsApiNameList =
                Lists.newArrayList(Utils.SALES_ORDER_PRODUCT_API_NAME, Utils.QUOTE_LINES_API_NAME);
        // 订单产品
        if (isOpenSaleContract) {
            xxDetailsApiNameList.add(Utils.SALE_CONTRACT_LINE_API_NAME);
        }
        try {
            List<ISelectOption> spuOptions = getSpuDesc(tenantId, isSpuOpen);
            //先让sku描述落地，防止后续描述并发创建问题
            initProductDescribe(tenantId);
            // 刷单位描述
            createUnitInfoDesc(tenantId, Utils.UNIT_INFO_API_NAME);
            // 刷多单位关联关系描述
            createUnitInfoDesc(tenantId, Utils.MULTI_UNIT_RELATED_API_NAME);
            // 刷单位详情页布局
            createObjectLayOut(user, LAYOUT_DETAIL, Utils.UNIT_INFO_API_NAME);
            // 刷单位列表页布局
            createObjectLayOut(user, LAYOUT_LIST, Utils.UNIT_INFO_API_NAME);
            //初始化常用单位设置
            commonUnitService.init(user, tenantId);
            // 刷关联关系单选字段：单位的options
            createMultiUnitRelatedObj(tenantId, spuOptions);
            String apiName = isSpuOpen ? Utils.SPU_API_NAME : Utils.PRODUCT_API_NAME;
            IObjectDescribe spuDescribe = describeWithSimplifiedChineseService.findByDescribeApiName(user, apiName);
            // 给xx描述添加字段
            ModuleInitLayoutUtil.addFieldForXXDetail(tenantId, spuOptions, xxDetailsApiNameList);
            updateSpuAndSkuLayout(tenantId, isSpuOpen);

            if (isSpuOpen) {
                updateSpuAndSkuUnitFieldUnEdit(spuDescribe);
                addFieldForSpuDetail(spuDescribe);
                //addFieldForSkuDetail(tenantId);
            }
            // 刷关联关系的布局
            addMultiUnitRelatedLayout(tenantId, user);
            // 刷菜单
            addUnitInfoObjToMenu(tenantId, user, isSpuOpen);
            // 刷权限
            addUnitInfoObjToAuth(tenantId, user);
            // 刷除了管理员之外的所有角色
            addUnitInfoObjWithOutAdmin(tenantId, user, isSpuOpen);
            // 刷detail
            ModuleInitLayoutUtil.addFormLayout(tenantId, xxDetailsApiNameList);
            //刷价目表明细和产品对象的终端布局
            addUnitFieldForPriceBookProductAndProduct(tenantId, user);
            // 刷单位options同步到单位对象数据
            syncUnitOptionsToDB(tenantId, user, spuOptions);
            //刷映射规则
            ModuleInitLayoutUtil.refreshMappingRule(user);
            if(invoiceService.isNewInvoiceOpen(user)
                    && invoiceService.getInvoiceMode(tenantId).equals(InvoiceApplicationConstants.InvoiceApplicationMode.SALES_ORDER_PRODUCT)){
                addFieldForInvoiceLinesDetail(tenantId);
            }
            if (SFAConfigUtil.isSimpleBom(tenantId)) {
                addFieldForSimpleBomDetail(tenantId);
            }
            //判断是否是快销企业
            Set<String> module = licenseService.getModule(tenantId);
            if (module.contains("kx_peculiarity")) {
                log.warn("is kx_peculiarity");
                SFAConfigUtil.setConfigValue(tenantId, user.getUpstreamOwnerIdOrUserId(), "is_kx_peculiarity_enabled", "1");
            }
            /*// 刷历史数据 是否为多单位 为否
            if (isOpenSaleContract) {
                //刷数据
                sendRefreshSaleContractLineMultiFieldMsg(user.getTenantId());
            }*/
            updateHistoryDataUnitIsFalse(tenantId, isSpuOpen);
        } catch (Exception e) {
            log.error("MultiUnitModuleInitService initModule Fail tenantId->{}", tenantId, e);
            sendAuditLog(tenantId, userId, e.toString(), "InitMultipleUnitFailed");
            return ConfigCtrlModule.Result.builder()
                    .errCode(ConfigCtrlModule.ResultInfo.Fail.getErrCode())
                    .errMessage(e.getMessage())
                    .value(ConfigCtrlModule.Value.builder().openStatus(ConfigCtrlModule.OpenStatus.CLOSE.toString()).build()
                    ).build();

        }
        sendAuditLog(tenantId, userId, "success", "InitMultipleUnitSucceed");
        sendMqAfterInitModuleSuccess(tenantId);
        return ConfigCtrlModule.Result.builder()
                .errCode(ConfigCtrlModule.ResultInfo.Sucess.getErrCode())
                .errMessage(ConfigCtrlModule.ResultInfo.Sucess.getErrMessage())
                .value(
                        ConfigCtrlModule.Value.builder()
                                .openStatus(ConfigCtrlModule.OpenStatus.OPEN.toString()).build()
                ).build();
    }

    private void initProductDescribe(String tenantId) throws MetadataServiceException {
        IObjectDescribe skuDescribe = objectDescribeService.findByTenantIdAndObjectDescribeApiNameIncludedDeletedData(tenantId, Utils.PRODUCT_API_NAME);
        IFieldDescribe newField = FieldDescribeFactory.newInstance(isMultipleUnitField);
        if (skuDescribe.getFieldDescribe(newField.getApiName()) == null) {
            skuDescribe.addFieldDescribe(newField);
        }
        updateSpuAndSkuUnitFieldUnEdit(skuDescribe);
        /*if (BooleanUtils.isTrue(skuDescribe.isUdef())) {
            return;
        }*/
        objectDescribeService.update(skuDescribe);
        //Thread.sleep(1000);
    }
    private void sendRefreshSaleContractLineMultiFieldMsg(String tenantId) {
        asyncTaskCommonProducer.create(BIZ, tenantId);
    }

    private void addUnitFieldForPriceBookProductAndProduct(String tenantId, User user) throws MetadataServiceException {
        List<Layout> productlayoutList = layoutService.findByTypes(tenantId, Lists.newArrayList(ILayout.LIST_LAYOUT_TYPE),
                Utils.PRODUCT_API_NAME);
        String layoutId;
        for (Layout layout : productlayoutList) {
            layoutId = layout.getId();
            layout.fromJsonString(productListLayout);
            layout.setTenantId(tenantId);
            layout.setId(layoutId);
            layoutService.replace(layout);
        }
        List<Layout> pricebookproductlayoutList = layoutService.findByTypes(tenantId, Lists.newArrayList(ILayout.LIST_LAYOUT_TYPE),
                Utils.PRICE_BOOK_PRODUCT_API_NAME);
        for (Layout layout : pricebookproductlayoutList) {
            layoutId = layout.getId();
            layout.fromJsonString(priceBookProductListLayout);
            layout.setTenantId(tenantId);
            layout.setId(layoutId);
            layoutService.replace(layout);
        }
    }

    public ConfigCtrlModule.Result initModuleRepair(ServiceContext context, String tenantId) {
        User user = context.getUser();
        updateHistoryDataUnitIsFalse(tenantId, false);
        boolean cpq = bizConfigThreadLocalCacheService.isCPQEnabled(context.getTenantId());
        if (cpq) {
            return ConfigCtrlModule.Result.builder()
                    .errCode(ConfigCtrlModule.ResultInfo.Fail.getErrCode())
                    .errMessage(I18N.text("sfa.multi.init.1"))
                    .value(
                            ConfigCtrlModule.Value.builder()
                                    .openStatus(ConfigCtrlModule.OpenStatus.CLOSE.toString()).build()
                    ).build();
        }
        boolean isSpuOpen = SFAConfigUtil.isSpuOpen(tenantId);
        try {
            List<ISelectOption> spuOptions = getSpuDesc(tenantId, isSpuOpen);
            addUnitFieldForPriceBookProductAndProduct(tenantId, user);
            // 刷spu和sku的布局
            updateSpuAndSkuLayout(tenantId, isSpuOpen);
            // 刷关联关系的布局
            addMultiUnitRelatedLayout(tenantId, user);
            // 刷菜单
            addUnitInfoObjToMenu(tenantId, user, isSpuOpen);
            // 刷权限
            addUnitInfoObjToAuth(tenantId, user);
            // 刷除了管理员之外的所有角色
            addUnitInfoObjWithOutAdmin(tenantId, user, isSpuOpen);
            // 刷detail
            //addFormLayout(tenantId, false);
            // 刷单位options同步到单位对象数据
            syncUnitOptionsToDB(tenantId, user, spuOptions);
            // 刷历史数据 是否为多单位 为否
            updateHistoryDataUnitIsFalse(tenantId, isSpuOpen);
            sendMqAfterInitModuleSuccess(tenantId);
        } catch (Exception e) {
            log.error("MultiUnitModuleInitService initModule Fail tenantId->{}", tenantId, e);
            sendAuditLog(tenantId, context.getUser().getUpstreamOwnerIdOrUserId(), e.toString(), "InitMultipleUnitFailed");
            return ConfigCtrlModule.Result.builder()
                    .errCode(ConfigCtrlModule.ResultInfo.Fail.getErrCode())
                    .errMessage(e.getMessage())
                    .value(ConfigCtrlModule.Value.builder().openStatus(ConfigCtrlModule.OpenStatus.CLOSE.toString()).build()
                    ).build();

        }
        return ConfigCtrlModule.Result.builder()
                .errCode(ConfigCtrlModule.ResultInfo.Sucess.getErrCode())
                .errMessage(ConfigCtrlModule.ResultInfo.Sucess.getErrMessage())
                .value(
                        ConfigCtrlModule.Value.builder()
                                .openStatus(ConfigCtrlModule.OpenStatus.OPEN.toString()).build()
                ).build();
    }


    /**
     * 把多单位字段刷到spu和sku的布局中
     *
     * @param tenantId
     */
    private void updateSpuAndSkuLayout(String tenantId, boolean isSpuOpen) throws MetadataServiceException {
        try {
            if (isSpuOpen) {
                List<Layout> spuLayoutList = layoutService.findByTypes(tenantId, Lists.newArrayList(ILayout.DETAIL_LAYOUT_TYPE),
                        Utils.SPU_API_NAME);
                for (Layout layout : spuLayoutList) {
                    replaceLayout(layout);
                }
            }
            List<Layout> skuLayoutList = layoutService.findByTypes(tenantId, Lists.newArrayList(ILayout.DETAIL_LAYOUT_TYPE),
                    Utils.PRODUCT_API_NAME);
            for (Layout layout : skuLayoutList) {
                replaceLayout(layout);
            }
        } catch (MetadataServiceException e) {
            log.warn("MultiUnitModuleInitService initModule updateSpuAndSkuLayout Fail tenantId->{}", tenantId, e);
        }
    }

    /**
     * 更新布局
     *
     * @param layout
     * @throws MetadataServiceException
     */
    private void replaceLayout(Layout layout) throws MetadataServiceException {
        List<IFormField> formFieldList = Lists.newArrayList();
        formFieldList.add(convertFormFieldByDescJson(isMultipleUnitField, false));
        ModuleInitLayoutUtil.addFieldsToDetailLayoutFormComponent(layout, formFieldList);
        layoutService.replace(layout);
    }
    private IFormField convertFormFieldByDescJson(String descJson, boolean flag) {
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(descJson);
        IFormField formField = new FormField();
        formField.setFieldName(fieldDescribe.getApiName());
        formField.setRequired(fieldDescribe.isRequired());
        formField.setReadOnly(flag);
        formField.setRenderType(fieldDescribe.getType());
        return formField;
    }


    /**
     * 刷对象的布局
     *
     * @param user
     * @param key
     * @param apiName
     */
    private void createObjectLayOut(User user, String key, String apiName) throws MetadataServiceException {
        List<ILayout> layouts = serviceFacade.findLayoutByObjectApiName(user.getTenantId(), apiName);
        if (layouts == null||layouts.isEmpty()) {
            String layoutJson = enterpriseInitService.getLayoutJsonFromResourceByApiName(apiName, key);
            ILayout layout = new Layout(Document.parse(layoutJson));
            serviceFacade.createLayout(user, layout);
        }
    }


    /**
     * 刷单位对象，除了管理员之外的所有有商品权限的角色
     *
     * @param tenantId
     * @param user
     */
    private void addUnitInfoObjWithOutAdmin(String tenantId, User user, boolean isSpuOpen) {
        List<String> roleCodes = Lists.newArrayList();
        String apiName = isSpuOpen ? Utils.SPU_API_NAME : Utils.PRODUCT_API_NAME;
        FuncCodePrivilege.Arg arg = new FuncCodePrivilege.Arg(AuthContext.builder()
                .appId(PrivilegeConstants.APP_ID)
                .tenantId(tenantId)
                .userId(user.getUpstreamOwnerIdOrUserId())
                .build(), Lists.newArrayList(apiName));

        FuncCodePrivilege.Result result = privilegeProxy.getHaveFuncCodesPrivilegeRoles(arg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(user.getTenantId()));
        if (result.isSuccess()) {
            result.getResult().forEach((k, v) -> {
                roleCodes.addAll(v);
            });
        }


        roleCodes.forEach(roleCode -> {
            UpdateRoleModifiedFuncPrivilege.Arg roleArg = new UpdateRoleModifiedFuncPrivilege.Arg(AuthContext.builder()
                    .appId(PrivilegeConstants.APP_ID)
                    .tenantId(user.getTenantId())
                    .userId(user.getUpstreamOwnerIdOrUserId())
                    .build(),
                    roleCode, funcodes, null, null);
            UpdateRoleModifiedFuncPrivilege.Result roleRole = functionPrivilegeProxy.updateRoleModifiedFuncPrivilege(roleArg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(user.getTenantId()));
            log.info("result:{}", result.toString());
        });
    }

    /**
     * 刷单位对象描述
     *
     * @param tenantId
     */
    private void createUnitInfoDesc(String tenantId, String apiName) throws MetadataServiceException {
        IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, apiName);
        if (describe == null) {
            enterpriseInitService.createDesc(tenantId, apiName);
        }
    }

    /**
     * 刷单位关联关系对象
     *
     * @param tenantId
     * @param options
     */
    private void createMultiUnitRelatedObj(String tenantId, List<ISelectOption> options) throws MetadataServiceException {
        IObjectDescribe multiUnitRelatedDescribe = describeWithSimplifiedChineseService.findByDescribeApiName(User.systemUser(tenantId), Utils.MULTI_UNIT_RELATED_API_NAME);
        IFieldDescribe unitDescribe = multiUnitRelatedDescribe.getFieldDescribe("unit_id");
        SelectOneFieldDescribe unitSelectOneField = (SelectOneFieldDescribe) unitDescribe;
        unitSelectOneField.setSelectOptions(options);
        multiUnitRelatedDescribe.setTenantId(tenantId);
        objectDescribeService.update(multiUnitRelatedDescribe);

        IObjectDescribe commonUnitDescribe = describeWithSimplifiedChineseService.findByDescribeApiName(User.systemUser(tenantId), SFAPreDefineObject.CommonUnit.getApiName());
        IFieldDescribe commonUnitField = commonUnitDescribe.getFieldDescribe("common_unit");
        unitSelectOneField = (SelectOneFieldDescribe) commonUnitField;
        unitSelectOneField.setSelectOptions(options);
        commonUnitDescribe.setTenantId(tenantId);
        objectDescribeService.update(commonUnitDescribe);
    }

    /**
     * 刷菜单
     *
     * @param tenantId
     * @param user
     */
    private void addUnitInfoObjToMenu(String tenantId, User user, boolean isSpuOpen) {

        CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListArg arg = new CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListArg();
        arg.setApiNames(Lists.newArrayList(Utils.UNIT_INFO_API_NAME));
        CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListResult menuItemByApiName = crmMenuInitService.innerFindMenuDataListByApiNames(arg, tenantId);
        if (CollectionUtils.isNotEmpty(menuItemByApiName.getMenuDataList())) {
            return;
        }
        crmMenuInitService.createMenuItem(user,Lists.newArrayList("UnitInfoObj"),"");
    }

    /**
     * 刷权限
     *
     * @param tenantId
     * @param user
     */
    private void addUnitInfoObjToAuth(String tenantId, User user) {
        enterpriseInitService.initPrivilegeRelate(Lists.newArrayList(Utils.UNIT_INFO_API_NAME), user, null, "2", null);
    }

    /**
     * 刷单位options同步到单位对象数据
     *
     * @param tenantId
     * @param user
     * @param spuOptions
     */
    public void syncUnitOptionsToDB(String tenantId, User user, List<ISelectOption> spuOptions) {
        try {
            List<IObjectData> saveDatas = Lists.newArrayList();
            List<IObjectData> invalidDatas = Lists.newArrayList();
            int index = 1;

            for (ISelectOption option : spuOptions) {
                IObjectData data = new ObjectData();
                String unitValue = option.getValue();
                String unitLabe = option.getLabel();
                Boolean notUsable = option.get("not_usable", Boolean.class);

                data.setId(unitValue);
                if (unitLabe.length() < 100) {
                    data.setName(unitLabe);
                    data.set("order_field", index);
                    data.setDescribeId(serviceFacade.generateId());
                    data.setRecordType("default__c");
                    data.setTenantId(tenantId);
                    data.setDescribeApiName("UnitInfoObj");
                    data.set("life_status", "normal");
                    data.setOwner(Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
                    saveDatas.add(data);
                    if (notUsable != null) {
                        if (notUsable) {
                            invalidDatas.add(data);
                        }
                    }
                    index++;
                }
            }


            List<List<IObjectData>> unitInfoDatas = Lists.partition(saveDatas, 300);
            unitInfoDatas.forEach(o -> serviceFacade.bulkSaveObjectData(o, user));

            ActionContext actionContext = new ActionContext();
            actionContext.setEnterpriseId(tenantId);
            actionContext.setUserId(user.getUpstreamOwnerIdOrUserId());

            List<List<IObjectData>> unitInfoInvalidDatas = Lists.partition(invalidDatas, 300);
            unitInfoInvalidDatas.forEach(o -> serviceFacade.bulkInvalid(o, user));
        } catch (MetaDataBusinessException e) {
            log.warn("syncUnitOptionsToDB error:{}", e);
        }
    }


    private void updateSpuAndSkuUnitFieldUnEdit(IObjectDescribe spuDescribe) throws MetadataServiceException {
        IFieldDescribe unitField = spuDescribe.getFieldDescribe("unit");
        // 移除掉编辑
        Map config = unitField.getConfig();
        config.put("add", 0);
        config.put("remove", 0);
        unitField.setConfig(config);
        // 处理option的config
        SelectOneFieldDescribe selectOneField = (SelectOneFieldDescribe) unitField;
        selectOneField.getSelectOptions().forEach(option -> {
            Map map = Maps.newHashMap();
            map.put("edit", 0);
            map.put("enable", 0);
            map.put("remove", 0);
            option.set("config", map);
        });
    }

    private void addFieldForSpuDetail(IObjectDescribe objectDescribe) throws MetadataServiceException {
        IFieldDescribe newField = FieldDescribeFactory.newInstance(isMultipleUnitField);
        if (objectDescribe.getFieldDescribe(newField.getApiName()) == null) {
            objectDescribe.addFieldDescribe(newField);
            objectDescribeService.update(objectDescribe);
        }
    }


    private void addFieldForSkuDetail(String tenantId) throws MetadataServiceException {
        try {
            addFieldForAnObjectDesc(tenantId, Utils.PRODUCT_API_NAME, isMultipleUnitField);
        } catch (MetadataServiceException e) {
            //Thread.sleep(1000);
            log.error("addFieldForSkuDetail failed first time", e);
            sendAuditLog(tenantId, "1000", e.toString(), "addFieldForAnObjectDesc first time failed");
            addFieldForAnObjectDesc(tenantId, Utils.PRODUCT_API_NAME, isMultipleUnitField);
        }
    }


    private void addFieldForInvoiceLinesDetail(String tenantId) throws MetadataServiceException {
        try {
            addFieldForAnObjectDesc(tenantId, Utils.INVOICE_APPLICATION_LINES_API_NAME, invoiceModeThreeActualUnit);
            addFieldForLayOut(tenantId, "detail",Utils.INVOICE_APPLICATION_LINES_API_NAME, Lists.newArrayList(FieldDescribeFactory.newInstance(invoiceModeThreeActualUnit)));
        } catch (MetadataServiceException e) {
            //Thread.sleep(1000);
            log.error("addFieldForInvoiceLinesDetail failed first time", e);
            sendAuditLog(tenantId, "1000", e.toString(), "addFieldForInvoiceLinesDetail first time failed");
            addFieldForAnObjectDesc(tenantId, Utils.INVOICE_APPLICATION_LINES_API_NAME, invoiceModeThreeActualUnit);
        }
    }

    private void addFieldForSimpleBomDetail(String tenantId) throws MetadataServiceException {
        try {
            addFieldForAnObjectDesc(tenantId, Utils.BOM_API_NAME, FIELD_UNIT_ID);
            addFieldForLayOut(tenantId, "detail",Utils.BOM_API_NAME, Lists.newArrayList(FieldDescribeFactory.newInstance(FIELD_UNIT_ID)));
        } catch (MetadataServiceException e) {
            //Thread.sleep(1000);
            log.error("addFieldForSimpleBomDetail failed:", e);
        }
    }


    private void addFieldForAnObjectDesc(String tenantId, String objApiName, String fieldJson) throws MetadataServiceException {
        IFieldDescribe newField = FieldDescribeFactory.newInstance(fieldJson);
        IObjectDescribe objDescribe = describeWithSimplifiedChineseService.findByDescribeApiName(User.systemUser(tenantId), objApiName);
        if (objDescribe.getFieldDescribe(newField.getApiName()) == null) {
            objDescribe.addFieldDescribe(newField);
            objectDescribeService.update(objDescribe);
        }
    }

    /**
     * 刷多单位关联关系布局
     *
     * @param tenantId
     * @param user
     */
    private void addMultiUnitRelatedLayout(String tenantId, User user) {
        List<ILayout> layouts = serviceFacade.findLayoutByObjectApiName(user.getTenantId(), Utils.MULTI_UNIT_RELATED_API_NAME);
        if (layouts == null||layouts.isEmpty()) {
            String listLayoutStr = enterpriseInitService.getLayoutJsonFromResourceByApiName(Utils.MULTI_UNIT_RELATED_API_NAME, "list");
            Layout layout = new Layout();
            layout.fromJsonString(listLayoutStr);
            layout.setTenantId(tenantId);
            serviceFacade.createLayout(user, layout);
        }
    }


    /**
     * 更新spu和sku是否多单位字段为否
     * 已自测
     *
     * @param tenantId
     */
    private void updateHistoryDataUnitIsFalse(String tenantId, boolean isSpuOpen) {
        User user = new User(tenantId, "-10000");
        SearchTemplateQuery query = getTemplateQuery(tenantId);
        List<String> apiNames = Lists.newArrayList(Utils.PRODUCT_API_NAME);
        if (isSpuOpen) {
            apiNames.add(Utils.SPU_API_NAME);
        }
        apiNames.forEach(apiName -> {
            int loopTimes = 0;
            while (true) {
                loopTimes++;
                if (loopTimes > 1000) {
                    log.warn("updateHistoryDataUnitIsFalse loop limit, limit:{}, tenantId:{}", loopTimes, tenantId);
                    SFABizLogUtil.sendAuditLog(SFABizLogUtil.Arg.builder()
                            .action("sfa_loop_limit")
                            .objectApiNames(Utils.PRODUCT_API_NAME)
                            .message("updateHistoryDataUnitIsFalse")
                            .build(), User.systemUser(tenantId));
                    break;
                }
                Integer number = updateDatas(apiName, user, query);
                if (number == 0) {
                    break;
                }
            }
        });
    }


    private Integer updateDatas(String apiName, User user, SearchTemplateQuery query) {
        QueryResult<IObjectData> result = serviceFacade.findBySearchQueryIgnoreAll(user, apiName, query);
        List<IObjectData> datas = result.getData();
        serviceFacade.bulkUpdateObjectDataOneField("is_multiple_unit", datas, false, user);
        if (datas.size() < 1000) {
            return 0;
        }
        return 1;
    }


    private SearchTemplateQuery getTemplateQuery(String tenantId) {
        List<IFilter> filters = Lists.newArrayList();

        IFilter filter = new Filter();
        filter.setFieldName("is_multiple_unit");
        filter.setOperator(Operator.IS);
        filter.setFieldValues(null);
        filters.add(filter);

        SearchUtil.fillFilterEq(filters, IObjectData.TENANT_ID, tenantId);

        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setWheres(Lists.newArrayList());
        //0不走权限  1走权限
        searchTemplateQuery.setPermissionType(0);

        return searchTemplateQuery;
    }


    /**
     * 获取商品单位的options信息
     *
     * @param tenantId
     * @return
     */
    public List<ISelectOption> getSpuDesc(String tenantId, boolean isSpuOpen) throws MetadataServiceException {
        String apiName = isSpuOpen ? Utils.SPU_API_NAME : Utils.PRODUCT_API_NAME;
        IObjectDescribe objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, apiName);
        if (objectDescribe == null) {
            return Lists.newArrayList();
        }
        IFieldDescribe unitField = objectDescribe.getFieldDescribe("unit");
        if (unitField == null) {
            return Lists.newArrayList();
        }
        SelectOneFieldDescribe selectOneField = (SelectOneFieldDescribe) unitField;
        return selectOneField.getSelectOptions();
    }

    private void  addFieldForLayOut(String tenantId, String layoutType, String describeApiName, List<IFieldDescribe> addFieldsDescribes){
        List<Layout> layoutList = Lists.newArrayList();
        try {
            layoutList = layoutService.findByTypes(tenantId, Lists.newArrayList(layoutType), describeApiName);
        } catch (MetadataServiceException e) {
            log.error("MultiUnitModuleInitService addFieldForLayOut fail tenantId->{}", tenantId, e);
        }

        List<Layout> detailLayouts = layoutList.stream().collect(Collectors.toList());
        detailLayouts.forEach(x-> {
            try {
                x.getComponents().forEach(iComponent -> {
                    if (java.util.Objects.equals(iComponent.getName(), LayoutConstants.FORM_COMPONENT_API_NAME)) {
                        FormComponent fc = (FormComponent) iComponent;
                        Optional<IFieldSection> fieldSection = fc.getFieldSections().stream()
                                .filter(it -> it.getName().equals("base_field_section__c"))
                                .findFirst();
                        if (fieldSection.isPresent()) {
                            List<IFormField> layoutFields = fieldSection.get().getFields();
                            for(IFieldDescribe fieldDescribe : addFieldsDescribes) {
                                IFormField formField = new FormField();
                                formField.setFieldName(fieldDescribe.getApiName());
                                formField.setRequired(fieldDescribe.isRequired());
                                formField.setReadOnly(false);
                                formField.setRenderType(fieldDescribe.getType());
                                layoutFields.add(formField);
                            }
                            fieldSection.get().setFields(layoutFields);
                        }
                    }
                });
                layoutService.replace(x);
            } catch (MetadataServiceException e) {
                log.error("get components error",e);
            }
        });
    }
}
