package com.facishare.crm.sfa.predefine.service.modulectrl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.model.BatchAddObjectToPRMModel;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.modulectrl.util.InitUtil;
import com.facishare.crm.sfa.service.IPrmService;
import com.facishare.crm.sfa.utilities.util.DataOrganizationUtils;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.ContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.ILayoutService;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.ErrorCode;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.exception.MetadataValidateException;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.Where;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 相关对象开启后销售合同添加描述和布局
 * @IgnoreI18nFile
 */
@Service
@Slf4j
public class SaleContractModuleInitServiceExt {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    IObjectDescribeService objectDescribeService;
    @Autowired
    private ILayoutService layoutService;
    @Autowired
    private SFABizObjMappingRuleWrapperService mappingRuleWrapperService;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Autowired
    private IPrmService prmService;

    private static final String EXTEND_INFO_ATTR_JSON = "{\"show_positive_sign\":true}";

    public static final String FIELD_JSON_SALE_CONTRACT_LINE_BOM_CORE_ID = "{\"action_on_target_delete\":\"set_null\",\"api_name\":\"bom_core_id\",\"define_type\":\"package\",\"is_index\":false,\"label\":\"产品组合\",\"max_length\":256,\"pattern\":\"\",\"is_required\":false,\"is_unique\":false,\"target_api_name\":\"BomCoreObj\",\"target_related_list_label\":\"销售合同产品\",\"target_related_list_name\":\"bome_core_list\",\"type\":\"object_reference\"}";
    public static final String FIELD_JSON_BOM_TYPE = "{\"api_name\":\"bom_type\",\"define_type\":\"package\",\"description\":\"bom类型\",\"is_index\":false,\"is_need_convert\":false,\"is_required\":false,\"is_unique\":false,\"label\":\"bom类型\",\"quote_field\":\"bom_core_id__r.category\",\"quote_field_type\":\"select_one\",\"status\":\"released\",\"type\":\"quote\"}";
    public static final String FIELD_JSON_BOM_VERSION = "{\"api_name\":\"bom_version\",\"define_type\":\"package\",\"description\":\"bom版本\",\"is_index\":false,\"is_need_convert\":false,\"is_required\":false,\"is_unique\":false,\"label\":\"bom版本\",\"quote_field\":\"bom_core_id__r.core_version\",\"quote_field_type\":\"auto_number\",\"status\":\"released\",\"type\":\"quote\"}";
    public static final String FIELD_JSON_LINES_RELATED_CORE_ID = "{\"description\":\"复用产品组合Id\",\"is_unique\":false,\"type\":\"text\",\"is_required\":false,\"define_type\":\"package\",\"max_length\":100,\"is_index\":false,\"is_active\":true,\"default_value\":\"\",\"label\":\"复用产品组合Id\",\"is_need_convert\":false,\"api_name\":\"related_core_id\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"}";

    public static final String FIELD_JSON_LINES_NEW_BOM_PATH = "{\"description\": \"Bom完整路径\",\"is_unique\": false,\"type\": \"text\",\"is_required\": false,\"define_type\": \"package\",\"max_length\": 500,\"is_index\": false,\"is_active\": true,\"default_value\": \"\",\"label\": \"Bom完整路径\",\"is_need_convert\": false,\"api_name\": \"new_bom_path\",\"is_index_field\": false,\"help_text\": \"\",\"status\": \"released\"}";


    public void handle(String tenantId, Map<String, IObjectDescribe> descMap, StopWatch stopWatch) throws MetadataServiceException {
        JSONObject contractFields = JSON.parseObject(getDescribeJsonFromResourceByApiName(Utils.SALE_CONTRACT_API_NAME));
        JSONObject contractLineFields = JSON.parseObject(getDescribeJsonFromResourceByApiName(Utils.SALE_CONTRACT_LINE_API_NAME));
        List<IFormField> contractLineFormFieldList = Lists.newArrayList();
        List<IFormField> contractFormFieldList = Lists.newArrayList();
        Map<String, String> masterFieldMapping = Maps.newHashMap();
        Map<String, String> ql2sclFieldMapping = Maps.newHashMap();
        Map<String, String> scl2sopFieldMapping = Maps.newHashMap();
        List<IFieldDescribe> contractAddFields = Lists.newArrayList();
        List<IFieldDescribe> contractLineAddFields = Lists.newArrayList();
        List<IFieldDescribe> contractLineUpdateFields = Lists.newArrayList();
        bomBussiness(tenantId, contractLineFields, contractLineAddFields, descMap, contractLineFormFieldList, ql2sclFieldMapping, scl2sopFieldMapping, contractLineUpdateFields);
        stopWatch.lap("bomBussiness");
        priceBookBussiness(tenantId, contractFields, contractLineFields, descMap, contractFormFieldList, contractLineFormFieldList, masterFieldMapping, ql2sclFieldMapping, contractAddFields, contractLineAddFields, contractLineUpdateFields);
        stopWatch.lap("priceBookBussiness");
        attributeBussiness(tenantId, contractLineFields, descMap, contractLineFormFieldList, ql2sclFieldMapping, scl2sopFieldMapping, contractLineAddFields);
        stopWatch.lap("attributeBussiness");
        multiCurrencyBussiness(tenantId, contractFields, contractLineFields, descMap, contractFormFieldList, contractLineFormFieldList, contractAddFields, contractLineAddFields);
        stopWatch.lap("multiCurrencyBussiness");
        partnerBussiness(tenantId);
        stopWatch.lap("partnerBussiness");
        doSaveData(tenantId, stopWatch, descMap, contractLineFormFieldList, contractFormFieldList, masterFieldMapping, ql2sclFieldMapping, scl2sopFieldMapping, contractAddFields, contractLineAddFields, contractLineUpdateFields);
        //多组织
        DataOrganizationUtils.addDataOrganizationField(tenantId, Lists.newArrayList(Utils.SALE_CONTRACT_API_NAME,Utils.SALE_CONTRACT_LINE_API_NAME));
    }

    private void doSaveData(String tenantId, StopWatch stopWatch, Map<String, IObjectDescribe> descMap, List<IFormField> contractLineFormFieldList, List<IFormField> contractFormFieldList, Map<String, String> masterFieldMapping, Map<String, String> ql2sclFieldMapping, Map<String, String> scl2sopFieldMapping, List<IFieldDescribe> contractAddFields, List<IFieldDescribe> contractLineAddFields, List<IFieldDescribe> contractLineUpdateFields) throws MetadataServiceException {
        IObjectDescribe contractDescribe = descMap.get(Utils.SALE_CONTRACT_API_NAME);
        IObjectDescribe contractLineDescribe = descMap.get(Utils.SALE_CONTRACT_LINE_API_NAME);
        if (CollectionUtils.notEmpty(contractAddFields)) {
            contractAddFields.removeIf(x->Objects.nonNull(contractDescribe.getFieldDescribe(x.getApiName())));
            if (CollectionUtils.notEmpty(contractAddFields)) {
                objectDescribeService.addCustomFieldDescribe(contractDescribe, contractAddFields);
            }
        }
        if (CollectionUtils.notEmpty(contractLineAddFields)) {
            contractLineAddFields.removeIf(x->Objects.nonNull(contractLineDescribe.getFieldDescribe(x.getApiName())));
            if (CollectionUtils.notEmpty(contractLineAddFields)) {
                objectDescribeService.addCustomFieldDescribe(contractLineDescribe, contractLineAddFields);
            }
        }
        if (CollectionUtils.notEmpty(contractLineUpdateFields)) {
            objectDescribeService.updateFieldDescribe(contractLineDescribe, contractLineUpdateFields);
        }
        stopWatch.lap("updateDescribe");
        if (CollectionUtils.notEmpty(contractFormFieldList)) {
            refreshLayouts(tenantId, Utils.SALE_CONTRACT_API_NAME, contractFormFieldList);
        }
        if (CollectionUtils.notEmpty(contractLineFormFieldList)) {
            refreshLayouts(tenantId, Utils.SALE_CONTRACT_LINE_API_NAME, contractLineFormFieldList);
        }

        stopWatch.lap("updateLayout");
        if (MapUtils.isNotEmpty(masterFieldMapping)) {
            mappingRuleWrapperService.addFieldMapping(tenantId, "rule_quoteobj2salecontractobj__c", masterFieldMapping);
            mappingRuleWrapperService.addFieldMapping(tenantId, "rule_salecontractobj2salesorderobj__c", masterFieldMapping);
        }
        Map<String,String> fieldOption = Maps.newHashMap();
        if(bizConfigThreadLocalCacheService.isTempNodeEnabled(tenantId)){
            fieldOption.put("node_type","[{\"source_option\":\"standard\",\"target_option\":\"standard\"},{\"source_option\":\"temp\",\"target_option\":\"temp\"}]");
        }
        if (MapUtils.isNotEmpty(ql2sclFieldMapping)) {
            mappingRuleWrapperService.addFieldMapping(tenantId, "rule_quotelinesobj2salecontractlineobj__c", ql2sclFieldMapping,fieldOption);
        }
        if (MapUtils.isNotEmpty(scl2sopFieldMapping)) {
            mappingRuleWrapperService.addFieldMapping(tenantId, "rule_salecontractlineobj2salesorderproductobj__c", scl2sopFieldMapping,fieldOption);
        }
        stopWatch.lap("updateMapping");
    }

    private void partnerBussiness(String tenantId) {
        if (!bizConfigThreadLocalCacheService.isPartnerEnabled(tenantId)) {
            return;
        }
        ServiceContext context = ContextManager.buildServiceContext("SaleContractModuleInitServiceExt", "partnerBussiness");
        BatchAddObjectToPRMModel.Arg arg = new BatchAddObjectToPRMModel.Arg();
        arg.setApiNames(Lists.newArrayList(Utils.SALE_CONTRACT_API_NAME));
        Boolean flag = prmService.batchAddObjectToPRM(context, arg);
        if (!flag) {
            log.warn("{}:Failed to open the sales contract object to add the partner field", tenantId);
        }
    }

    private void multiCurrencyBussiness(String tenantId, JSONObject contractFields, JSONObject contractLineFields, Map<String, IObjectDescribe> descMap, List<IFormField> contractFormFieldList, List<IFormField> contractLineFormFieldList, List<IFieldDescribe> contractAddFields, List<IFieldDescribe> contractLineAddFields) throws MetadataServiceException {
        if (!bizConfigThreadLocalCacheService.isCurrencyEnabled(tenantId)) {
            return;
        }
        IObjectDescribe orderDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, Utils.SALES_ORDER_API_NAME);
        IObjectDescribe contractDescribe = descMap.get(Utils.SALE_CONTRACT_API_NAME);
        IObjectDescribe contractLineDescribe = descMap.get(Utils.SALE_CONTRACT_LINE_API_NAME);
        if (Objects.nonNull(orderDescribe)) {
            ImmutableMap.of(Utils.SALE_CONTRACT_API_NAME, contractAddFields, Utils.SALE_CONTRACT_LINE_API_NAME, contractLineAddFields).forEach((k, v) -> Lists.newArrayList("mc_currency", "mc_functional_currency", "mc_exchange_rate_version", "mc_exchange_rate").forEach(x -> {
                IFieldDescribe field = orderDescribe.getFieldDescribe(x);
                IFieldDescribe fieldDescribe = FieldDescribeExt.copy(field);
                if (Objects.nonNull(fieldDescribe)) {
                    fieldDescribe.setId(null);
                    fieldDescribe.setDescribeApiName(k);
                    fieldDescribe.setCreateTime(System.currentTimeMillis());
                    v.add(fieldDescribe);
                }

            }));
        }
        generateNewField(Utils.SALE_CONTRACT_API_NAME, contractDescribe, contractFields.getString("base_amount"), contractAddFields);
        generateNewField(Utils.SALE_CONTRACT_API_NAME, contractDescribe, contractFields.getString("base_executed_amount"), contractAddFields);
        generateNewField(Utils.SALE_CONTRACT_API_NAME, contractDescribe, contractFields.getString("base_unexecuted_amount"), contractAddFields);
        contractFormFieldList.add(convertFormFieldByDescJson(contractFields.getString("base_amount")));
        contractFormFieldList.add(convertFormFieldByDescJson(contractFields.getString("base_executed_amount")));
        contractFormFieldList.add(convertFormFieldByDescJson(contractFields.getString("base_unexecuted_amount")));
        contractFormFieldList.add(convertFormFieldByDescJson(contractFields.getString("mc_currency")));
        contractFormFieldList.add(convertFormFieldByDescJson(contractFields.getString("mc_exchange_rate")));
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("base_executed_order_subtotal"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("base_unexecuted_order_subtotal"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("base_subtotal"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("base_product_price"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("base_sales_price"), contractLineAddFields);
        contractLineFormFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("base_executed_order_subtotal")));
        contractLineFormFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("base_unexecuted_order_subtotal")));
        contractLineFormFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("base_subtotal")));
        contractLineFormFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("base_product_price")));
        contractLineFormFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("base_sales_price")));
        contractLineFormFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("mc_currency")));
        contractLineFormFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("mc_exchange_rate")));
    }

    private void attributeBussiness(String tenantId, JSONObject contractLineFields, Map<String, IObjectDescribe> descMap, List<IFormField> contractLineFormFieldList, Map<String, String> ql2sclFieldMapping, Map<String, String> scl2sopFieldMapping, List<IFieldDescribe> contractLineAddFields) {
        IObjectDescribe contractLineDescribe = descMap.get(Utils.SALE_CONTRACT_LINE_API_NAME);
        if (bizConfigThreadLocalCacheService.isOpenAttribute(tenantId)) {
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("attribute"), contractLineAddFields);
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("attribute_json"), contractLineAddFields);
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("attribute_price_book_id"), contractLineAddFields);
            contractLineFormFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("attribute")));
            contractLineFormFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("attribute_json")));
            contractLineFormFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("attribute_price_book_id")));
            ql2sclFieldMapping.put("attribute", "attribute");
            ql2sclFieldMapping.put("attribute_json", "attribute_json");
            ql2sclFieldMapping.put("attribute_price_book_id", "attribute_price_book_id");
            scl2sopFieldMapping.putAll(ql2sclFieldMapping);
        }
        if (bizConfigThreadLocalCacheService.isOpenNonstandardAttribute(tenantId)) {
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("nonstandard_attribute"), contractLineAddFields);
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("nonstandard_attribute_json"), contractLineAddFields);
            contractLineFormFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("nonstandard_attribute")));
            contractLineFormFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("nonstandard_attribute_json")));
            ql2sclFieldMapping.put("nonstandard_attribute", "nonstandard_attribute");
            ql2sclFieldMapping.put("nonstandard_attribute_json","nonstandard_attribute_json");
            scl2sopFieldMapping.putAll(ql2sclFieldMapping);
        }
    }

    private void priceBookBussiness(String tenantId, JSONObject contractFields, JSONObject contractLineFields, Map<String, IObjectDescribe> descMap, List<IFormField> contractFormFieldList, List<IFormField> contractLineFormFieldList, Map<String, String> masterFieldMapping, Map<String, String> ql2sclFieldMapping, List<IFieldDescribe> contractAddFields, List<IFieldDescribe> contractLineAddFields, List<IFieldDescribe> contractLineUpdateFields) throws MetadataServiceException {
        if (!bizConfigThreadLocalCacheService.isPriceBookEnabled(tenantId)) {
            return;
        }
        priceBookBussinessContract(contractFields, descMap, contractFormFieldList, masterFieldMapping, contractAddFields);

        priceBookBussinessContractLine(tenantId, contractLineFields, descMap, contractLineFormFieldList, ql2sclFieldMapping, contractLineAddFields, contractLineUpdateFields);
    }

    private void priceBookBussinessContractLine(String tenantId, JSONObject contractLineFields, Map<String, IObjectDescribe> descMap, List<IFormField> contractLineFormFieldList, Map<String, String> ql2sclFieldMapping, List<IFieldDescribe> contractLineAddFields, List<IFieldDescribe> contractLineUpdateFields) {
        IObjectDescribe contractLineDescribe = descMap.get(Utils.SALE_CONTRACT_LINE_API_NAME);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("price_book_id"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("price_book_product_id"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("price_book_discount"), contractLineAddFields);
        IFieldDescribe discountField = contractLineDescribe.getFieldDescribe("discount");
        discountField.set("default_value", "$price_book_product_id__r.discount$");
        discountField.set("default_is_expression", true);
        contractLineUpdateFields.add(discountField);
        IFieldDescribe productPriceField = contractLineDescribe.getFieldDescribe("product_price");
        productPriceField.set("default_value", "$price_book_product_id__r.pricebook_sellingprice$");
        IFieldDescribe priceBookPriceField = FieldDescribeFactory.newInstance(contractLineFields.getString("price_book_price"));
        priceBookPriceField.setDescribeApiName(Utils.SALE_CONTRACT_LINE_API_NAME);
        if (bizConfigThreadLocalCacheService.isCurrencyEnabled(tenantId)) {
            productPriceField.set("default_value", "$price_book_product_id__r.pricebook_sellingprice$*EXCHANGERATE($price_book_product_id__r.mc_currency$,$mc_currency$,1.0)");
            priceBookPriceField.set("default_value", "$product_price$*EXCHANGERATE($price_book_product_id__r.mc_currency$,$mc_currency$,1.0)*$price_book_discount$");
        }
        if (contractLineUpdateFields.removeIf(x -> Objects.equals("product_price", x.getApiName()))) {
            productPriceField.setExtendInfo((Map) JSON.parse(EXTEND_INFO_ATTR_JSON));
        }
        contractLineUpdateFields.add(productPriceField);
        if (contractLineDescribe.getFieldDescribe(priceBookPriceField.getApiName()) == null) {
            contractLineAddFields.add(priceBookPriceField);
        }
        contractLineFormFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("price_book_id")));
        contractLineFormFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("price_book_product_id")));
        contractLineFormFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("price_book_price"), true));
        contractLineFormFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("price_book_discount"), true));
        ql2sclFieldMapping.put("price_book_id", "price_book_id");
        ql2sclFieldMapping.put("price_book_product_id", "price_book_product_id");
    }

    private void priceBookBussinessContract(JSONObject contractFields, Map<String, IObjectDescribe> descMap, List<IFormField> contractFormFieldList, Map<String, String> masterFieldMapping, List<IFieldDescribe> contractAddFields) {
        IObjectDescribe contractDescribe = descMap.get(Utils.SALE_CONTRACT_API_NAME);
        generateNewField(Utils.SALE_CONTRACT_API_NAME, contractDescribe, contractFields.getString("price_book_id"), contractAddFields);
        contractFormFieldList.add(convertFormFieldByDescJson(contractFields.getString("price_book_id")));
        masterFieldMapping.put("price_book_id", "price_book_id");
    }

    private void bomBussiness(String tenantId, JSONObject contractLineFields, List<IFieldDescribe> contractLineAddFields, Map<String, IObjectDescribe> descMap, List<IFormField> formFieldList, Map<String, String> ql2sclFieldMapping, Map<String, String> scl2sopFieldMapping, List<IFieldDescribe> contractLineUpdateFields) {
        if (!bizConfigThreadLocalCacheService.isCPQEnabled(tenantId)) {
            return;
        }
        IObjectDescribe contractLineDescribe = descMap.get(Utils.SALE_CONTRACT_LINE_API_NAME);
        ObjectReferenceFieldDescribe productIdField = (ObjectReferenceFieldDescribe) contractLineDescribe.getFieldDescribe("product_id");
        productIdField.setWheres(getProductIdLookupWheres(productIdField.getWheres()));
        contractLineUpdateFields.add(productIdField);
        ObjectReferenceFieldDescribe priceBookProductIdField = (ObjectReferenceFieldDescribe) contractLineDescribe.getFieldDescribe("price_book_product_id");
        if (priceBookProductIdField != null) {
            priceBookProductIdField.setWheres(getProductIdLookupWheres(priceBookProductIdField.getWheres()));
            contractLineUpdateFields.add(priceBookProductIdField);
        }
        IFieldDescribe productPriceField = contractLineDescribe.getFieldDescribe("product_price");
        productPriceField.setExtendInfo((Map) JSON.parse(EXTEND_INFO_ATTR_JSON));
        contractLineUpdateFields.add(productPriceField);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("is_package"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("is_saleable"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("product_status"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("product_life_status"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("bom_id"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("prod_pkg_key"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("parent_prod_pkg_key"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("root_prod_pkg_key"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("product_group_id"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("print_hierarchy"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("price_editable"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("price_mode"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("amount_editable"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("max_amount"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("min_amount"), contractLineAddFields);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("increment"), contractLineAddFields);
        if (bizConfigThreadLocalCacheService.isBomInstanceEnabled(tenantId)) {
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("bom_instance_tree_id"), contractLineAddFields);
        }

        if (GrayUtil.bomMasterSlaveMode(tenantId)) {
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, FIELD_JSON_SALE_CONTRACT_LINE_BOM_CORE_ID, contractLineAddFields);
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, FIELD_JSON_BOM_TYPE, contractLineAddFields);
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, FIELD_JSON_BOM_VERSION, contractLineAddFields);
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, FIELD_JSON_LINES_RELATED_CORE_ID, contractLineAddFields);
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, FIELD_JSON_LINES_NEW_BOM_PATH, contractLineAddFields);

            formFieldList.add(convertFormFieldByDescJson(FIELD_JSON_SALE_CONTRACT_LINE_BOM_CORE_ID));
            formFieldList.add(convertFormFieldByDescJson(FIELD_JSON_BOM_TYPE));
            formFieldList.add(convertFormFieldByDescJson(FIELD_JSON_BOM_VERSION));
            formFieldList.add(convertFormFieldByDescJson(FIELD_JSON_LINES_RELATED_CORE_ID));
            formFieldList.add(convertFormFieldByDescJson(FIELD_JSON_LINES_NEW_BOM_PATH));

            ql2sclFieldMapping.put("bom_core_id", "bom_core_id");
            ql2sclFieldMapping.put("bom_type", "bom_type");
            ql2sclFieldMapping.put("bom_version", "bom_version");
            ql2sclFieldMapping.put("related_core_id", "related_core_id");
            ql2sclFieldMapping.put("new_bom_path", "new_bom_path");
        }
        formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("is_package")));
        formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("is_saleable")));
        formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("product_status")));
        formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("product_life_status")));
        formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("bom_id")));
        formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("prod_pkg_key")));
        formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("parent_prod_pkg_key")));
        formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("root_prod_pkg_key")));
        formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("product_group_id")));
        formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("print_hierarchy")));
        formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("price_editable")));
        formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("price_mode")));
        formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("amount_editable")));
        formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("max_amount")));
        formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("min_amount")));
        formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("increment")));
        ql2sclFieldMapping.put("bom_id", "bom_id");
        ql2sclFieldMapping.put("prod_pkg_key", "prod_pkg_key");
        ql2sclFieldMapping.put("parent_prod_pkg_key", "parent_prod_pkg_key");
        ql2sclFieldMapping.put("root_prod_pkg_key", "root_prod_pkg_key");

        if(bizConfigThreadLocalCacheService.isTempNodeEnabled(tenantId)){
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("node_type"), contractLineAddFields);
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("node_no"), contractLineAddFields);
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("temp_node_group_id"), contractLineAddFields);
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, contractLineDescribe, contractLineFields.getString("temp_node_bom_id"), contractLineAddFields);
            formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("node_type")));
            formFieldList.add(convertFormFieldByDescJson(contractLineFields.getString("temp_node_group_id")));
            ql2sclFieldMapping.put("node_type", "node_type");
            ql2sclFieldMapping.put("node_no", "node_no");
            ql2sclFieldMapping.put("temp_node_group_id", "temp_node_group_id");
            ql2sclFieldMapping.put("temp_node_bom_id", "temp_node_bom_id");
        }
        scl2sopFieldMapping.putAll(ql2sclFieldMapping);
    }

    private void refreshLayouts(String tenantId, String describeApiName, List<IFormField> formFieldList) throws MetadataServiceException {
        List<Layout> layoutList = layoutService.findByTypes(tenantId, Lists.newArrayList(ILayout.DETAIL_LAYOUT_TYPE), describeApiName);
        for (ILayout layout : layoutList) {
            ModuleInitLayoutUtil.addFieldsToDetailLayoutFormComponent(layout, formFieldList);
            layoutService.replace(layout);
        }
    }

    private IFormField convertFormFieldByDescJson(String descJson) {
        return convertFormFieldByDescJson(descJson, false);
    }

    private IFormField convertFormFieldByDescJson(String descJson, Boolean readOnly) {
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(descJson);
        IFormField formField = new FormField();
        formField.setFieldName(fieldDescribe.getApiName());
        formField.setRequired(false);
        formField.setReadOnly(readOnly);
        formField.setRenderType(fieldDescribe.getType());
        return formField;
    }

    private void generateNewField(String objDescribeApiName, IObjectDescribe describe, String fieldJson, List<IFieldDescribe> addFields) {
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldJson);
        fieldDescribe.setDescribeApiName(objDescribeApiName);
        if (describe.getFieldDescribe(fieldDescribe.getApiName()) == null) {
            addFields.add(fieldDescribe);
        }
    }

    public static List<LinkedHashMap> getProductIdLookupWheres(List<LinkedHashMap> oldWheres) {
        List<LinkedHashMap> wheres = Lists.newArrayList();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("connector", Where.CONN.OR);
        List<Map> filters = Lists.newArrayList();
        Map<String, Object> filter = Maps.newHashMap();
        filter.put("field_name", "is_saleable");
        filter.put("operator", Operator.EQ.name());
        filter.put("field_values", Lists.newArrayList(Boolean.TRUE));
        filters.add(filter);
        if (CollectionUtils.notEmpty(oldWheres)) {
            Map<String, Object> oldmap = oldWheres.get(0);
            List<Map> oldfilters = (List<Map>) oldmap.get("filters");
            if (CollectionUtils.notEmpty(oldfilters)) {
                filters.addAll(oldfilters);
            }
            if (oldfilters.contains(filter)) {
                filters.remove(filter);
            }
        }
        map.put("filters", filters);
        wheres.add(map);
        return wheres;
    }

    private static String getDescribeJsonFromResourceByApiName(String apiName) {
        ClassLoader classLoader = InitUtil.class.getClassLoader();
        String jsonstr;
        try {
            jsonstr = IOUtils.toString(classLoader.getResource(
                    "saleconstractfieldjson/" + apiName + "_field.json"), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("initCRMDescribeByApiName file parse error" + e.getMessage(), e);
            throw new MetadataValidateException(ErrorCode.FS_PAAS_MDS_FILE_PARSE_EXCEPTION, "initCRMDescribeByApiName file parse error", e);
        }
        return jsonstr;
    }

}
