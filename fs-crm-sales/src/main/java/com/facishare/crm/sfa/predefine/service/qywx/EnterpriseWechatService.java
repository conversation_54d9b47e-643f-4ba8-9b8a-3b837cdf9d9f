package com.facishare.crm.sfa.predefine.service.qywx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.constants.CRMFeedConstants;
import com.facishare.crm.constants.EnterpriseWechatConstant;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.sfa.lto.qywx.WecomIntegrationService;
import com.facishare.crm.sfa.lto.qywx.WecomUtils;
import com.facishare.crm.sfa.lto.qywx.proxy.WecomServiceProxy;
import com.facishare.crm.sfa.lto.qywx.proxy.models.*;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.EnterpriseWechatDuplicatedSearchModel.Arg;
import com.facishare.crm.sfa.predefine.service.model.EnterpriseWechatDuplicatedSearchModel.Result;
import com.facishare.crm.sfa.predefine.service.model.QywxJoinRelatedTeamModel;
import com.facishare.crm.sfa.predefine.service.model.WechatSaveActiveRecordModel;
import com.facishare.crm.sfa.predefine.service.model.qywx.FriendBatchMappingModel;
import com.facishare.crm.sfa.predefine.service.model.qywx.FriendSyncConfigModel;
import com.facishare.crm.sfa.predefine.service.model.qywx.GetExternalUserIdModel;
import com.facishare.crm.sfa.predefine.service.model.qywx.HistoryDataSyncModel;
import com.facishare.crm.sfa.predefine.service.model.qywx.JudgeGroupUserModel;
import com.facishare.crm.sfa.predefine.service.model.qywx.WechatFriendInfoModel;
import com.facishare.crm.sfa.predefine.service.model.qywx.WechatGroupRelatedAccountModel;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.util.CRMRemindRecordUtil;
import com.facishare.crm.util.Safes;
import com.facishare.marketing.outapi.arg.WechatExternalUserSyncArg;
import com.facishare.marketing.outapi.result.WechatWorkExternalUserResult;
import com.facishare.marketing.outapi.service.QywxDataSyncService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.dto.InternationalItem;
import com.facishare.paas.appframework.common.service.dto.OrganizationInfo;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.service.dto.tag.FindAllTagByBulkDataId;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.MetaDataMiscService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.ObjectMappingService;
import com.facishare.paas.appframework.metadata.TagLogicService;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DataAndSubTag;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.fxiaoke.rocketmq.producer.DefaultTopicMessage;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.facishare.crm.constants.CRMFeedConstants.ACTIVE_RECORD_API_NAME;
import static com.facishare.crm.constants.CRMFeedConstants.Field;
import static com.facishare.crm.constants.EnterpriseWechatConstant.ENTERPRISE_WECHAT_USER_ID;
import static com.facishare.crm.constants.EnterpriseWechatConstant.FRIEND_FIELD_MAP;
import static com.facishare.crm.constants.EnterpriseWechatConstant.WECHAT_FRIENDS_RECORD_OBJ;
import static com.facishare.crm.constants.EnterpriseWechatConstant.WECHAT_FRIEND_ID;
import static com.facishare.crm.constants.EnterpriseWechatConstant.mappingRuleNameMap;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_PARAMET_ERERROR;


@Slf4j
@Component
@ServiceModule("enterprise_wechat")
public class EnterpriseWechatService {

	@Autowired
	private WecomServiceProxy enterpriseWeChatServiceProxy;
	@Autowired
	private EIEAConverter eieaConverter;

	@Resource(name = "qywxHistoryDataSyncProducer")
	private AutoConfMQProducer qywxHistoryDataSyncProducer;

	@Autowired
	private WechatSyncConfigService wechatSyncConfigService;

	@Resource(name = "qywxDataSyncProducer")
	private AutoConfMQProducer qywxDataSyncProducer;

	@Autowired
	private ServiceFacade serviceFacade;
	@Autowired
	private MetaDataFindService metaDataFindService;
	@Autowired
	private MetaDataMiscService metaDataMiscService;
	@Autowired
	private TagLogicService tagLogicService;
	@Autowired
	private ObjectMappingService objectMappingService;
	@Autowired
	private WecomServiceProxy wecomServiceProxy;

	@Autowired
	private WecomIntegrationService wecomIntegrationService;

	@Autowired
	private CRMNotificationService crmNotificationService;
	private static final List<String> SUPPORT_API_NAME = Lists.newArrayList(
			SFAPreDefineObject.Leads.getApiName(),
			SFAPreDefineObject.Contact.getApiName(),
			SFAPreDefineObject.Account.getApiName());

	private static final ImmutableMap<Pair, List<String>> NEW_SEARCH_FILED_MAP = ImmutableMap.<Pair, List<String>>builder()
			.put(Pair.of(SFAPreDefineObject.Leads.getApiName(), "byPhone"), Lists.newArrayList("mobile"))
			.put(Pair.of(SFAPreDefineObject.Leads.getApiName(), "byTel"), Lists.newArrayList("tel"))
			.put(Pair.of(SFAPreDefineObject.Account.getApiName(), "byPhone"), Lists.newArrayList("tel"))
			.put(Pair.of(SFAPreDefineObject.Contact.getApiName(), "byPhone"), Lists.newArrayList("mobile1","mobile2","mobile3","mobile4","mobile5"))
			.put(Pair.of(SFAPreDefineObject.Contact.getApiName(), "byTel"), Lists.newArrayList("tel1","tel2","tel3","tel4","tel4"))
			.build();



	@ServiceMethod("query_by_duplicate_config")
	public Result queryByDuplicateConfig(ServiceContext serviceContext, Arg arg) {

		if (!SUPPORT_API_NAME.contains(arg.getObjectApiName()) || StringUtils.isEmpty(arg.getMobile())) {
			throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
		}

		FriendSyncConfigModel.FriendSyncConfigInfo configInfo = wechatSyncConfigService.queryConfigByTenantId(serviceContext.getTenantId());

		FriendSyncConfigModel.DuplicateSetting setting = null;
		if (SFAPreDefineObject.Leads.getApiName().equals(arg.getObjectApiName())) {
			if (configInfo.isLeadsCheckDuplicate()) {
				setting = configInfo.getLeadsDuplicateConfig();
			}
		}
		if (SFAPreDefineObject.Contact.getApiName().equals(arg.getObjectApiName())) {
			if (configInfo.isContactCheckDuplicate()) {
				setting = configInfo.getContactDuplicateConfig();
			}
		}
		if (SFAPreDefineObject.Account.getApiName().equals(arg.getObjectApiName())) {
			if (configInfo.isAccountCheckDuplicate()) {
				setting = configInfo.getAccountDuplicateConfig();
			}
		}
		if (setting == null) {
			return Result.builder().dataList(Lists.newArrayList()).build();
		}

		// 如果开启了归属组织查重，补充归属组织
		if (setting.isByOrganization() && Safes.isNotEmpty(arg.getDataOwnOrganization())) {
			String userId = serviceContext.getUser().getUpstreamOwnerIdOrUserId();
			OrganizationInfo organizationInfo = serviceFacade.findMainOrgAndDeptByUserId(serviceContext.getTenantId(), userId, Lists.newArrayList(userId));
			if (organizationInfo != null && organizationInfo.getMainOrg(userId) != null) {
				arg.setDataOwnOrganization(Lists.newArrayList(organizationInfo.getMainOrgId(userId)));
			}
		}

		List<IObjectData> duplicatedDataList = this.queryBySetting(serviceContext.getUser(), arg.getObjectApiName(),
				arg.getMobile(), serviceContext.getUser().getUpstreamOwnerIdOrUserId(), arg.getDataOwnOrganization(), setting);
		Map<String, Permissions> permissionsMap = Maps.newHashMap();
		Map<String, String> idMap = Maps.newHashMap();
		if (Safes.isNotEmpty(duplicatedDataList)) {
			IObjectDescribe describe = serviceFacade.findObject(serviceContext.getTenantId(), arg.getObjectApiName());
			// 有查看详情权限的数据，前端会展示关联按钮，否则展示联系负责人
			permissionsMap = metaDataMiscService.checkDataPrivilege(
					serviceContext.getUser(), duplicatedDataList, describe, ObjectAction.VIEW_DETAIL.getActionCode());
			// CRM员工id和企微员工id的绑定关系，用于跳转到联系对应的负责人的企微对话页面
			idMap = getQywxUserIdMap(serviceContext, duplicatedDataList);

			serviceFacade.fillUserInfo(describe, duplicatedDataList, serviceContext.getUser());
			serviceFacade.fillObjectDataWithRefObject(describe, duplicatedDataList, serviceContext.getUser());
			serviceFacade.fillDepartmentInfo(describe, duplicatedDataList, serviceContext.getUser());
		}

		return Result.builder().dataList(ObjectDataDocument.ofList(duplicatedDataList)).viewDetailPermissionsMap(permissionsMap).userIdMap(idMap).build();
	}

	private Map<String, String> getQywxUserIdMap(ServiceContext serviceContext, List<IObjectData> duplicatedDataList) {
		List<Integer> ownerList = duplicatedDataList.stream().map(IObjectData::getOwner)
				.map(Safes::first)
				.filter(StringUtils::isNotEmpty)
				.distinct()
				.map(Integer::parseInt)
				.collect(Collectors.toList());
		if (Safes.isEmpty(ownerList)) {
			return Maps.newHashMap();
		}
		String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(serviceContext.getTenantId()));
		return this.getQwIdMap(ea, ownerList);
	}

	private Map<String, String> getQwIdMap(String ea, List<Integer> ownerList) {
		EnterpriseWeChatResult<JSONArray> idMapResult = enterpriseWeChatServiceProxy.fsIds2OpenIds(
				QywxIdMapModel.Arg.builder().ea(ea).fsIds(ownerList).build());
		if (idMapResult != null && idMapResult.getData() != null) {
			List<QywxIdMapModel.IdMapping> idMappings = idMapResult.getData()
					.toJavaList(QywxIdMapModel.IdMapping.class);
			return idMappings.stream().collect(
					Collectors.toMap(d -> d.getFsId().toString(), QywxIdMapModel.IdMapping::getOpenId,
							(a, b) -> a));
		}
		return Maps.newHashMap();
	}


	public List<IObjectData> queryBySetting(User user, String objectApiName, String mobile, String owner, List<String> dataOwnOrganization, FriendSyncConfigModel.DuplicateSetting setting) {
		if (!setting.isByPhone() && !setting.isByTel()) {
			return Lists.newArrayList();
		}

		SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
		List<IFilter> filters = Lists.newArrayList();
		if (setting.isByPhone()) {
			List<String> byPhoneFieldName = NEW_SEARCH_FILED_MAP.get(Pair.of(objectApiName, "byPhone"));
			Safes.of(byPhoneFieldName).forEach(f -> SearchUtil.fillFilterEq(filters, f, mobile));
		}
		if (setting.isByTel()) {
			List<String> byTelFieldName = NEW_SEARCH_FILED_MAP.get(Pair.of(objectApiName, "byTel"));
			Safes.of(byTelFieldName).forEach(f -> SearchUtil.fillFilterEq(filters, f, mobile));
		}
		int index = 1;
		StringBuilder pattern = new StringBuilder();
		if (filters.size() > 1) {
			pattern.append("( ");
			for (IFilter filter : filters) {
				pattern.append(index);
				if (index < filters.size()) {
					pattern.append(" OR ");
				}
				index++;
			}
			pattern.append(")");
		}

		if (setting.isByOwner()) {
			SearchUtil.fillFilterEq(filters, IObjectData.OWNER, owner);
			pattern.append(" and ").append(index++);
		}
		if (setting.isByOrganization()) {
			if (Safes.isEmpty(dataOwnOrganization)) {
				log.warn("开启了根据多组织查重且数据归属组织字段为空");
			} else {
				SearchUtil.fillFilterEq(filters, "data_own_organization", dataOwnOrganization);
				pattern.append(" and ").append(index++);
			}
		}
		SearchUtil.fillFilterNotEq(filters, "life_status", ObjectLifeStatus.INVALID.getCode());
		pattern.append(" and ").append(index);

		searchTemplateQuery.setPattern(pattern.toString());
		searchTemplateQuery.setFilters(filters);
		List<OrderBy> orders = new ArrayList<>();
		orders.add(new OrderBy("last_modified_time", Boolean.FALSE));
		searchTemplateQuery.setOrders(orders);
		QueryResult<IObjectData> queryResult = metaDataFindService.findBySearchQuery(user, objectApiName, searchTemplateQuery);
		if (setting.isByMembership()) {
			return this.filterByRelevantTeam(queryResult.getData(), owner);
		}

		return queryResult.getData();
	}

	private List<IObjectData> filterByRelevantTeam(List<IObjectData> dataList, String owner) {
		return dataList.stream().filter(o ->
				Safes.of(ObjectDataExt.of(o).getTeamMembers()).stream().map(TeamMember::getEmployee)
						.collect(Collectors.toList()).contains(owner)
		).collect(Collectors.toList());
	}

	public void fillRelatedWechatWorkExternalUserId(User user, IObjectDescribe describe, List<IObjectData> dataList) {
		if (describe.getFieldDescribe(EnterpriseWechatConstant.EXTERNAL_USER_ID) == null) {
			return;
		}

		List<String> wechatIdList = Safes.of(dataList).stream()
				.map(d -> d.get(EnterpriseWechatConstant.ENTERPRISE_WECHAT_USER_ID, String.class))
				.filter(Objects::nonNull)
				.collect(Collectors.toList());
		this.fillRelatedWechatWorkExternalUserId(user, wechatIdList, dataList);
	}

	public void fillRelatedWechatWorkExternalUserId(User user,  List<String> weChatIds, List<IObjectData> dataList) {
		if (Safes.isEmpty(weChatIds)) {
			return;
		}
		SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
		List<IFilter> filters = Lists.newArrayList();
		SearchUtil.fillFilterIn(filters, EnterpriseWechatConstant.EXTERNAL_USER_ID, weChatIds);
		searchTemplateQuery.setLimit(30);
		searchTemplateQuery.setFilters(filters);
		QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user,
				EnterpriseWechatConstant.WECHAT_WORK_EXTERNAL_USER_OBJ, searchTemplateQuery);
		List<IObjectData> relateDataList = queryResult.getData();
		if (Safes.isNotEmpty(relateDataList)) {
			Map<String, String> idMap = relateDataList.stream().collect(
					Collectors.toMap(x -> AccountUtil.getStringValue(x, EnterpriseWechatConstant.EXTERNAL_USER_ID, ""), IObjectData::getId, (a, b) -> a));
			for (IObjectData objectData : dataList) {
				String enterpriseWechatId = AccountUtil.getStringValue(objectData, EnterpriseWechatConstant.ENTERPRISE_WECHAT_USER_ID, "");
				String wechatWorkExternalUserId = idMap.get(enterpriseWechatId);
				if (StringUtils.isNotEmpty(wechatWorkExternalUserId)) {
					objectData.set(EnterpriseWechatConstant.EXTERNAL_USER_ID, wechatWorkExternalUserId);
				}
			}
		}
	}

	@ServiceMethod("save_active_record")
	public WechatSaveActiveRecordModel.Result saveActiveRecord(ServiceContext serviceContext, WechatSaveActiveRecordModel.Arg arg) {
		IObjectData objectData = new ObjectData();
		objectData.setDescribeApiName(ACTIVE_RECORD_API_NAME);
		objectData.setCreatedBy(serviceContext.getUser().getUserId());
		objectData.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);

		Map<String, Object> activeRecordContent = Maps.newHashMap();
		StringBuilder fileText = new StringBuilder();
		arg.getFileList().forEach(file -> fileText.append("[").append(file.getName()).append("]"));
		//发送附件{0}给企微联系人{1}
		String text = I18N.text("sfa.enterprise.wechat.send.file.to.contact", fileText.toString(),
				arg.getWechatNickName() + "(" + arg.getWechatUserId() + ")");
		activeRecordContent.put(Field.TEXT, text);
		activeRecordContent.put(Field.ATTACHMENTS, arg.getFileList());
		objectData.set(Field.ACTIVE_RECORD_CONTENT, activeRecordContent);

		Map<String, Object> relatedObject = Maps.newHashMap();
		relatedObject.put(arg.getObjectApiName(), Lists.newArrayList(arg.getDataId()));
		objectData.set(Field.RELATED_OBJECT, relatedObject);
		// 企业微信
		objectData.set(Field.ACTIVE_RECORD_TYPE, CRMFeedConstants.ActiveRecordType.ENTERPRISE_WECHAT);

		BaseObjectSaveAction.Arg saveArg = new BaseObjectSaveAction.Arg();
		saveArg.setObjectData(ObjectDataDocument.of(objectData));
		serviceContext.getRequestContext().setAttribute(ActionContextKey.SKIP_REQUIRED_VALIDATE, true);
		ActionContext actionContext = new ActionContext(serviceContext.getRequestContext(), ACTIVE_RECORD_API_NAME,
				SystemConstants.ActionCode.Add.getActionCode());

		BaseObjectSaveAction.Result result = serviceFacade.triggerAction(actionContext, saveArg,
				BaseObjectSaveAction.Result.class);
		return new WechatSaveActiveRecordModel.Result(Lists.newArrayList(result.getObjectData()));
	}

	/**
	 * 发送申请加入相关团队的通知
	 */
	@ServiceMethod("apply_join_related_team")
	public QywxJoinRelatedTeamModel.Result applyJoinRelatedTeam(ServiceContext serviceContext, QywxJoinRelatedTeamModel.Arg arg) {

		if (StringUtils.isAnyEmpty(arg.getObjectId(), arg.getObjectApiName(), arg.getDataName(), arg.getEmployeeUserId())) {
			throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
		}

		String userName = serviceFacade.getUser(serviceContext.getTenantId(), serviceContext.getUser().getUpstreamOwnerIdOrUserId()).getUserName();
		List<String> contentParamList = Lists.newArrayList(userName, arg.getDataName());

		NewCrmNotification newCrmNotification = NewCrmNotification.builder()
				.receiverIDs(CRMRemindRecordUtil.getReceivedSet(Lists.newArrayList(arg.getEmployeeUserId())))
				.sourceId(UUID.randomUUID().toString().replace("-", ""))
				.type(NewCrmNotification.CUSTOM_REMIND_RECORD_TYPE)
				.remindSender(false)
				.senderId(User.SUPPER_ADMIN_USER_ID)
				.urlType(1)
				.urlParameter(CRMRemindRecordUtil.getUrlParameter(arg.getObjectApiName(), arg.getObjectId()))
				.title("【申请加入相关团队】")// ignoreI18n
				.titleInfo(InternationalItem.builder()
						.internationalKey(SFAI18NKeyUtil.SFA_APPLY_JOIN_RELATED_TEAM_TITLE)
						.internationalParameters(
						Collections.emptyList()).build())
				.fullContent(String.format("%s向你申请加入%s的相关团队，请处理。", contentParamList.toArray()))// ignoreI18n
				.fullContentInfo(InternationalItem.builder()
						.internationalKey(SFAI18NKeyUtil.SFA_APPLY_JOIN_RELATED_TEAM_CONTENT)
						.internationalParameters(contentParamList).build())
				.appId(serviceContext.getAppId())
				.build();

		crmNotificationService.sendNewCrmNotification(serviceContext.getUser(), newCrmNotification);
		return new QywxJoinRelatedTeamModel.Result();
	}

	/**
	 * 获取企微好友信息
	 */
	@ServiceMethod("get_friend_info")
	public WechatFriendInfoModel.Result getFriendInfo(ServiceContext serviceContext, WechatFriendInfoModel.Arg arg) {
		WechatFriendInfoModel.checkArg(arg);
		List<IObjectData> friendsDataList = queryFriendByExternalId(serviceContext.getTenantId(), serviceContext.getUser(), arg.getExternalUserId(), arg.getOwner());

		if (Safes.isEmpty(friendsDataList)) {
			// 没查到有同步的好友数据，补偿同步一次
			compensateImportFriendInfo(serviceContext.getTenantId(), arg.getExternalUserId(), arg.getOwner());
		}

		List<DataAndSubTag> tagList = null;
		if (Safes.isNotEmpty(friendsDataList)) {
			Set<String> friendsIdList = friendsDataList.stream().map(IObjectData::getId).collect(Collectors.toSet());
			tagList = tagLogicService.findAllTagByBulkDataId(
					EnterpriseWechatConstant.WECHAT_FRIENDS_RECORD_OBJ, friendsIdList, serviceContext.getUser());

			IObjectDescribe describe = serviceFacade.findObject(serviceContext.getTenantId(), EnterpriseWechatConstant.WECHAT_FRIENDS_RECORD_OBJ);
			serviceFacade.fillUserInfo(describe, friendsDataList, serviceContext.getUser());
			serviceFacade.fillObjectDataWithRefObject(describe, friendsDataList, serviceContext.getUser());
			serviceFacade.fillDepartmentInfo(describe, friendsDataList, serviceContext.getUser());
		}

		return WechatFriendInfoModel.Result.builder().dataList(ObjectDataDocument.ofList(friendsDataList)).dataTagInfo(
				FindAllTagByBulkDataId.DataTag.fromDataAndSubTagList(tagList)).build();
	}

	public List<IObjectData> queryFriendByExternalId(String tenantId, User user, String externalUserId, String owner) {
		if (StringUtils.isAnyEmpty(externalUserId, owner)) {
			return Collections.emptyList();
		}
		if (GrayUtil.isServiceProviderEi(tenantId)) {
			String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
			Map<String, String> cipherIdMap = this.convertCipherIdMap2(ea, Lists.newArrayList(externalUserId));
			log.warn("转换企微密文id，tenantId:{}, cipherIdMap:{}", tenantId, cipherIdMap);
			if (StringUtils.isNotEmpty(cipherIdMap.get(externalUserId))) {
				externalUserId = cipherIdMap.get(externalUserId);
			}
		}

		List<IObjectData> externalUserList = this.getExternalUserList(user, Lists.newArrayList(externalUserId));
		List<String> wechatExternalUserIdList = Safes.of(externalUserList).stream().map(IObjectData::getId).collect(Collectors.toList());
		return this.getFriendsDataList(user, owner, wechatExternalUserIdList);
	}



	@ServiceMethod("batch_mapping_friend")
	public FriendBatchMappingModel.Result batchMappingFriend(ServiceContext serviceContext, FriendBatchMappingModel.Arg arg) {
		FriendBatchMappingModel.checkArg(arg);
		if (GrayUtil.isServiceProviderEi(serviceContext.getTenantId())) {
			String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(serviceContext.getTenantId()));
			Map<String, String> cipherIdMap = this.convertCipherIdMap2(ea, arg.getExternalUserIdList());
			log.warn("转换企微密文id，tenantId:{}, cipherIdMap:{}", serviceContext.getTenantId(), cipherIdMap);
			if (Safes.isNotEmpty(cipherIdMap)) {
				arg.setExternalUserIdList(arg.getExternalUserIdList().stream().map(cipherIdMap::get).collect(Collectors.toList()));
			}
		}


		List<IObjectData> mappingDataList = Lists.newArrayList();
		List<IObjectData> externalUserList = this.getExternalUserList(serviceContext.getUser(), arg.getExternalUserIdList());
		if (Safes.isEmpty(externalUserList)) {
			return FriendBatchMappingModel.Result.builder().build();
		}
		List<String> wechatExternalUserIdList = externalUserList.stream().map(IObjectData::getId).collect(Collectors.toList());
		List<IObjectData> friendsDataList = this.getFriendsDataList(serviceContext.getUser(), arg.getOwner(), wechatExternalUserIdList);
		if (Safes.isNotEmpty(friendsDataList)) {
			String ruleName = mappingRuleNameMap.get(arg.getObjectApiName());
			mappingDataList = friendsDataList.parallelStream().map(friendData -> {
				ObjectMappingService.MappingDataArg mappingArg = new ObjectMappingService.MappingDataArg();
				mappingArg.setObjectData(friendData);
				mappingArg.setRuleApiName(ruleName);
				IObjectData objectData = objectMappingService.mappingData(serviceContext.getUser(), mappingArg)
						.getObjectData();
				objectData.set(WECHAT_FRIEND_ID, friendData.getId());
				return objectData;
			}).collect(Collectors.toList());

		}
		return FriendBatchMappingModel.Result.builder().dataList(ObjectDataDocument.ofList(mappingDataList)).build();
	}



	public void compensateImportFriendInfo(String tenantId, String externalId, String owner) {
		try {
			String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
			Map<String, String> userIdMapping = wecomIntegrationService.getUserIdMapping(ea);
			QueryContactModel.QueryResult queryDetailResult = wecomServiceProxy.queryExternalContact(ea, externalId);
			queryDetailResult.checkSuccess();
			QueryContactModel.ContactInfoResult contactInfoResult = WecomUtils.fillCRMIDForContactInfo(userIdMapping, queryDetailResult);
			DefaultTopicMessage msg = new DefaultTopicMessage("external_user_info", JSON.toJSONBytes(contactInfoResult));
			qywxDataSyncProducer.send(msg);
		} catch (Exception e) {
			log.error("makeup friend error!", e);
		}
	}

	public void compensateGroupInfo(String tenantId, String chatId, String owner) {
		try {
			String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
			Map<String, String> userIdMapping = wecomIntegrationService.getUserIdMapping(ea);
			GroupDetailModel.QueryResult detailResult = wecomServiceProxy.queryGroupDetail(ea, chatId);
			detailResult.checkSuccess();
			GroupDetailModel.ChatDetail chatDetail = WecomUtils.fillCRMIDForChatDetail(userIdMapping, detailResult);
			DefaultTopicMessage msg = new DefaultTopicMessage("chat_group_data", JSON.toJSONBytes(chatDetail));
			qywxDataSyncProducer.send(msg);
		} catch (Exception e) {
			log.error("makeup group error!", e);
		}
	}

	private List<IObjectData> getFriendsDataList(User user, String owner,
			List<String> wechatExternalUserIdList) {
		if (Safes.isEmpty(wechatExternalUserIdList)) {
			return Lists.newArrayList();
		}
		SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
		List<IFilter> filters = Lists.newArrayList();
		SearchUtil.fillFilterIn(filters, "external_user_id", wechatExternalUserIdList);
		SearchUtil.fillFilterEq(filters, "owner", owner);
		SearchUtil.fillFilterEq(filters, "friend_status", "0");
		searchTemplateQuery.setFilters(filters);
		QueryResult<IObjectData> externalResult = metaDataFindService.findBySearchQuery(user, EnterpriseWechatConstant.WECHAT_FRIENDS_RECORD_OBJ, searchTemplateQuery);
		return externalResult.getData();
	}

	private List<IObjectData> getExternalUserList(User user, List<String> externalUserIdList) {
		SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
		List<IFilter> filters = Lists.newArrayList();
		SearchUtil.fillFilterIn(filters, EnterpriseWechatConstant.EXTERNAL_USER_ID, externalUserIdList);
		searchTemplateQuery.setFilters(filters);
		QueryResult<IObjectData> externalResult = metaDataFindService.findBySearchQuery(user, EnterpriseWechatConstant.WECHAT_WORK_EXTERNAL_USER_OBJ, searchTemplateQuery);
		return externalResult.getData();
	}

	@ServiceMethod("query_related_account")
	public WechatGroupRelatedAccountModel.Result queryRelatedAccount(ServiceContext serviceContext, WechatGroupRelatedAccountModel.Arg arg) {
		WechatGroupRelatedAccountModel.checkArg(arg);
		List<IObjectData> externalGroupUserList = getExternalGroupUserListByGroupId(serviceContext, arg);
		if (Safes.isEmpty(externalGroupUserList)) {
			return WechatGroupRelatedAccountModel.Result.builder().dataList(Lists.newArrayList()).build();
		}
		IObjectData groupData = Safes.first(metaDataFindService.findObjectDataByIds(serviceContext.getTenantId(),
				Lists.newArrayList(arg.getGroupId()), EnterpriseWechatConstant.WECHAT_GROUP_OBJ));
		if (groupData == null) {
			/*群聊信息错误！*/
			throw new ValidateException(I18N.text("sfa.wechat.group.error"));
		}
		List<IObjectData> friendRecordList = findFriendRecordList(serviceContext, arg, externalGroupUserList, groupData);
		List<String> accountIdList;
		IActionContext actionContext = ActionContextExt.of(serviceContext.getUser(), serviceContext.getRequestContext())
				.getContext();
		if (Objects.equals(arg.getQueryType(), WechatGroupRelatedAccountModel.QueryType.FRIEND_BIND_ACCOUNT)) {
			// tab1: 群里的客户群成员和群主的好友记录 所关联的客户
			accountIdList = Safes.of(friendRecordList).stream()
					.filter(d -> StringUtils.isNotEmpty(AccountUtil.getStringValue(d, "custom_id", "")))
					.map(d -> d.get("custom_id", String.class)).collect(Collectors.toList());

		} else {
			// tab1: 群里的客户群成员和群主的好友记录 所关联的联系人所关联的客户
			List<String> contactIdList = Safes.of(friendRecordList).stream()
					.filter(d -> StringUtils.isNotEmpty(AccountUtil.getStringValue(d, "contact_id", "")))
					.map(d -> d.get("contact_id", String.class)).collect(Collectors.toList());
			List<IObjectData> contactList = metaDataFindService.findObjectDataByIdsExcludeInvalid(actionContext,
					contactIdList, SFAPreDefineObject.Contact.getApiName());
			accountIdList = Safes.of(contactList).stream()
					.filter(d -> StringUtils.isNotEmpty(AccountUtil.getStringValue(d, "account_id", "")))
					.map(d -> d.get("account_id", String.class)).collect(Collectors.toList());
		}
		List<IObjectData> accountList = metaDataFindService.findObjectDataByIdsExcludeInvalid(actionContext,
				accountIdList, SFAPreDefineObject.Account.getApiName());

		if (Safes.isNotEmpty(accountList)) {
			IObjectDescribe describe = serviceFacade.findObject(serviceContext.getTenantId(), SFAPreDefineObject.Account.getApiName());
			Map<String, Permissions> permissionsMap = metaDataMiscService.checkDataPrivilege(serviceContext.getUser(),
					accountList, describe, ObjectAction.VIEW_DETAIL.getActionCode());
			accountList.removeIf(d -> Objects.equals(permissionsMap.get(d.getId()), Permissions.NO_PERMISSION));
		}

		return WechatGroupRelatedAccountModel.Result.builder().dataList(ObjectDataDocument.ofList(accountList)).build();
	}

	private List<IObjectData> findFriendRecordList(ServiceContext serviceContext, WechatGroupRelatedAccountModel.Arg arg,
			List<IObjectData> externalGroupUserList, IObjectData groupData) {
		String leaderId = groupData.get("leader_id", String.class);
		if (StringUtils.isEmpty(leaderId)) {
			return Lists.newArrayList();
		}

		SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
		List<IFilter> filters = Lists.newArrayList();
		StringBuilder pattern = new StringBuilder();
		int index = 1;
		for (int i = 0; i < externalGroupUserList.size(); i++) {
			IObjectData groupUser = externalGroupUserList.get(i);
			String qwContactId = AccountUtil.getStringValue(groupUser, "qw_contact_id", "");
			if (StringUtils.isNotEmpty(qwContactId)) {
				SearchUtil.fillFilterEq(filters, "external_user_id", qwContactId);
				SearchUtil.fillFilterEq(filters, "qywx_user_id", leaderId);
				SearchUtil.fillFilterEq(filters, "friend_status", "0");
				pattern.append("(").append(index++).append(" AND ").append(index++).append(" AND ").append(index++).append(")");
				if (i < externalGroupUserList.size() - 1) {
					pattern.append(" OR ");
				}
			}
		}
		if (Safes.isEmpty(filters)) {
			return Lists.newArrayList();
		}
		String patternString = pattern.toString();
		if (pattern.toString().endsWith(" OR ")) {
			patternString = patternString.substring(0 , patternString.length() - 4);
		}
		searchTemplateQuery.setFilters(filters);
		searchTemplateQuery.setPattern(patternString);
		searchTemplateQuery.setLimit(20);
		return metaDataFindService.findBySearchQuery(serviceContext.getUser(),
				EnterpriseWechatConstant.WECHAT_FRIENDS_RECORD_OBJ, searchTemplateQuery).getData();
	}

	private List<IObjectData> getExternalGroupUserListByGroupId(ServiceContext serviceContext, WechatGroupRelatedAccountModel.Arg arg) {
		SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
		List<IFilter> filters = Lists.newArrayList();
		SearchUtil.fillFilterEq(filters, "qw_group_chat_id", arg.getGroupId());
		SearchUtil.fillFilterEq(filters, "type", 2);
		SearchUtil.fillFilterEq(filters, "status", 0);
		searchTemplateQuery.setFilters(filters);
		searchTemplateQuery.setLimit(20);
		return metaDataFindService.findBySearchQuery(serviceContext.getUser(),
				EnterpriseWechatConstant.WECHAT_GROUP_USER_OBJ, searchTemplateQuery).getData();
	}

	@ServiceMethod("judge_group_user")
	public JudgeGroupUserModel.Result judgeGroupUser(ServiceContext serviceContext, JudgeGroupUserModel.Arg arg) {
		String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(serviceContext.getTenantId()));
		String upstreamOwnerIdOrUserId = serviceContext.getUser().getUpstreamOwnerIdOrUserId();
		int fxUserId = Integer.parseInt(upstreamOwnerIdOrUserId);
		Map<String, String> qwIdMap = this.getQwIdMap(ea, Lists.newArrayList(fxUserId));
		log.warn("转换企微员工id，ea:{}, fxUserId:{}, qwIdMap:{}", ea, fxUserId, qwIdMap);
		String qwUserId = qwIdMap.get(upstreamOwnerIdOrUserId);
		if (StringUtils.isEmpty(qwUserId)) {
			return JudgeGroupUserModel.Result.builder().isGroupUser(false).build();
		}

		SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
		List<IFilter> filters = Lists.newArrayList();
		SearchUtil.fillFilterEq(filters, "qw_group_chat_id", arg.getGroupId());
		SearchUtil.fillFilterEq(filters, "type", 1);
		SearchUtil.fillFilterEq(filters, "user_id", qwUserId);
		searchTemplateQuery.setFilters(filters);
		searchTemplateQuery.setLimit(1);
		QueryResult<IObjectData> queryResult = metaDataFindService.findBySearchQuery(serviceContext.getUser(),
				EnterpriseWechatConstant.WECHAT_GROUP_USER_OBJ, searchTemplateQuery);
		return JudgeGroupUserModel.Result.builder().isGroupUser(Safes.isNotEmpty(queryResult.getData())).build();
	}


	@ServiceMethod("get_external_user_id")
	public GetExternalUserIdModel.Result getExternalUserId(ServiceContext serviceContext, GetExternalUserIdModel.Arg arg) {
		GetExternalUserIdModel.checkArg(arg);
		Map<String, String> externalUserIdMap = this.getExternalUserIdForNewRelation(serviceContext.getUser(), arg.getObjectIds(), arg.getObjectApiName());
		return GetExternalUserIdModel.Result.builder().externalUserIdMap(externalUserIdMap).build();
	}



	/**
	 * 新链接可能没有维护enterprise_wechat_user_id
	 * 这个接口查询参数业务数据所关联的企微好友的enterprise_wechat_user_id
	 */
	public Map<String, String> getExternalUserIdForNewRelation(User user, List<String> dataIdList, String objectApiName){
		if (Safes.isEmpty(dataIdList)) {
			return Maps.newHashMap();
		}

		Map<String, String> result = new HashMap<>();
		if (StringUtils.equals(objectApiName, SFAPreDefineObject.Leads.getApiName())) {
			List<IObjectData> dataList = metaDataFindService.findObjectDataByIds(user.getTenantId(), dataIdList, objectApiName);
			List<String> friendIds = dataList.stream().map(d -> d.get(WECHAT_FRIEND_ID, String.class)).filter(Objects::nonNull).collect(Collectors.toList());
			List<IObjectData> friendList = metaDataFindService.findObjectDataByIds(user.getTenantId(), friendIds, WECHAT_FRIENDS_RECORD_OBJ);
			Map<String, String> friendIdExternalIdMap = friendList.stream().collect(Collectors.toMap(DBRecord::getId, d -> d.get(ENTERPRISE_WECHAT_USER_ID, String.class)));
			for (IObjectData data : dataList) {
				String friendId = data.get(WECHAT_FRIEND_ID, String.class);
				if (Safes.isNotEmpty(friendId)) {
					result.put(data.getId(), friendIdExternalIdMap.get(friendId));
				}
			}
		} else {
			String relatedFieldApiName = FRIEND_FIELD_MAP.get(objectApiName);
			SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
			List<IFilter> filters = searchTemplateQuery.getFilters();
			SearchUtil.fillFilterHasAnyOf(filters, relatedFieldApiName, dataIdList);
			SearchUtil.fillFilterIn(filters, IObjectData.OWNER, Lists.newArrayList(user.getUserIdOrOutUserIdIfOutUser()));
			searchTemplateQuery.setLimit(dataIdList.size());
			ActionContextExt actionContextExt = ActionContextExt.of(user);
			List<String> queryFields = Lists.newArrayList(relatedFieldApiName, "enterprise_wechat_user_id");
			QueryResult<IObjectData> queryResult = metaDataFindService.findBySearchTemplateQueryWithFields(
					actionContextExt.getContext(), WECHAT_FRIENDS_RECORD_OBJ, searchTemplateQuery, queryFields);
			result = queryResult.getData().stream().collect(Collectors.toMap(d -> d.get(relatedFieldApiName, String.class), d -> d.get(ENTERPRISE_WECHAT_USER_ID, String.class), (a, b) -> a));
		}
		return result;
	}




	@ServiceMethod("history_data_sync")
	public HistoryDataSyncModel.Result historyDataSync(ServiceContext serviceContext, HistoryDataSyncModel.Arg arg) {
		arg.setTenantId(serviceContext.getTenantId());
		DefaultTopicMessage msg = new DefaultTopicMessage(JSON.toJSONBytes(arg));
		qywxHistoryDataSyncProducer.send(msg);
		return HistoryDataSyncModel.Result.builder().build();
	}


	/**
	 * 企微实时查询明密文（id升级的接口）
	 */
	public Map<String, String> convertCipherIdMap2(String ea, List<String> plaintextIdList) {
		EnterpriseWechatConvertModel.Result result = enterpriseWeChatServiceProxy.batchConvertId(
				EnterpriseWechatConvertModel.Arg.builder().ea(ea).externalUserIds(plaintextIdList).build());
		if (result != null && result.getData() != null) {
			return result.getData().stream().collect(Collectors.toMap(
					EnterpriseWechatConvertModel.ConvertDto::getExternal_userid,
					EnterpriseWechatConvertModel.ConvertDto::getNew_external_userid));
		}
		return Maps.newHashMap();
	}


	public Map<String, String> convertCipherIdMap(String ea, List<String> plaintextIdList) {
		EnterpriseWeChatResult<JSONArray> convertResult = enterpriseWeChatServiceProxy.convertCipherId(
				ConvertCipherIdModel.Arg.builder().fsEa(ea).plaintextIds(plaintextIdList).build());
		Map<String, String> cipherIdMap = Maps.newHashMap();
		if (convertResult != null && convertResult.getData() != null) {
			List<ConvertCipherIdModel.CipherIdMapDto> cipherIdMapDtos = convertResult.getData()
					.toJavaList(ConvertCipherIdModel.CipherIdMapDto.class);
			cipherIdMap = cipherIdMapDtos.stream().collect(Collectors.toMap(
					ConvertCipherIdModel.CipherIdMapDto::getOpenid, ConvertCipherIdModel.CipherIdMapDto::getPlaintextId));
		}
		return cipherIdMap;
	}


}
