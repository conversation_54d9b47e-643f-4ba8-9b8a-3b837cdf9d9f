package com.facishare.crm.sfa.predefine.service.task;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.enums.ActionCodeEnum;
import com.facishare.crm.sfa.predefine.service.model.RecalculateMessage;
import com.facishare.pay.common.utils.MD5Util;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-13 16:10
 */

@Component
public class RecalculateTaskService {


    @Autowired
    private TaskService taskService;

    private static final String RECYCLING_RECALCULATE_BATCH = "recycling-recalculate-batch";



    /**
     * 发送重算到期时间的task
     *
     * @param tenantId
     * @param objectId
     * @param apiName
     * @param actionCodeEnum
     */
    public void send(String tenantId, String objectId, String apiName, ActionCodeEnum actionCodeEnum) {
        if ("AccountObj".equals(apiName) || "LeadsObj".equals(apiName)) {
            //发送客户动作
            RecalculateMessage message = RecalculateMessage.builder().actionCode(actionCodeEnum.getActionCode())
                    .objectApiName(apiName)
                    .objectId(objectId)
                    .tenantId(tenantId)
                    .build();
            taskService.createOrUpdateTask("crm_object_recycling_task_recalculate",
                    tenantId,
                    objectId,
                    new Date(), JSONObject.toJSON(message).toString());
        }
    }

    /**
     * 发送重算到期时间的task
     *
     * @param tenantId
     * @param objectId
     * @param apiName
     * @param actionCodeEnum
     */
    public void send(String tenantId, String objectId, String apiName, ActionCodeEnum actionCodeEnum,Double extendDays) {
        if ("AccountObj".equals(apiName) || "LeadsObj".equals(apiName)) {
            //发送客户动作
            RecalculateMessage message = RecalculateMessage.builder().actionCode(actionCodeEnum.getActionCode())
                    .objectApiName(apiName)
                    .objectId(objectId)
                    .tenantId(tenantId)
                    .extendDays(extendDays)
                    .build();
            taskService.createOrUpdateTask("crm_object_recycling_task_recalculate",
                    tenantId,
                    objectId,
                    (objectId+tenantId),
                    new Date(), JSONObject.toJSON(message).toString());
        }
    }

    /**
     * @param tenantId
     * @param objectId
     * @param targetId       目标id(公海、线索池)
     * @param apiName
     * @param actionCodeEnum
     */
    public void send(String tenantId, String objectId, String targetId, String apiName, ActionCodeEnum actionCodeEnum) {

        if ("AccountObj".equals(apiName) || "LeadsObj".equals(apiName)) {
            //发送客户动作
            RecalculateMessage message = RecalculateMessage.builder().actionCode(actionCodeEnum.getActionCode())
                    .objectApiName(apiName)
                    .objectId(objectId)
                    .tenantId(tenantId)
                    .targetId(targetId)
                    .build();
            taskService.createOrUpdateTask("crm_object_recycling_task_recalculate",
                    tenantId,
                    objectId,
                    new Date(), JSONObject.toJSON(message).toString());
        }
    }

    /**
     * 客户、线索 规则变更发送消息
     * @param tenantId
     * @param objectId
     * @param apiName
     * @param actionCodeEnum
     */
    public void sendChangeRuleMsg(String tenantId, String objectId, String apiName, ActionCodeEnum actionCodeEnum){
        if ("AccountObj".equals(apiName) || "LeadsObj".equals(apiName)
                || Utils.HIGHSEAS_API_NAME.equalsIgnoreCase(apiName) || Utils.LEADS_POOL_API_NAME.equals(apiName)) {
            Integer callQueueMod = tenantId.concat(RECYCLING_RECALCULATE_BATCH).hashCode() & 0x7fffffff;
            //发送客户动作
            RecalculateMessage message = RecalculateMessage.builder().actionCode(actionCodeEnum.getActionCode())
                    .objectApiName(apiName)
                    .objectId(objectId)
                    .highSeas(Boolean.TRUE)
                    .tenantId(tenantId)
                    .build();
            taskService.createOrUpdateTask(RECYCLING_RECALCULATE_BATCH,
                    tenantId,
                    actionCodeEnum.getActionCode()+objectId,
                    new Date(), JSONObject.toJSON(message).toString(),callQueueMod);
        }
    }

    /**
     *
     * 非公海规则变更发送消息
     * @param tenantId
     * @param deptIds 非公海部门id,
     * @param apiName
     * @param actionCodeEnum
     */
    public void sendChangeRuleMsg(String tenantId, List<String> deptIds, String apiName, ActionCodeEnum actionCodeEnum){
        if (deptIds == null || deptIds.size() < 1){
            return;
        }
        if (Utils.ACCOUNT_API_NAME.equals(apiName)) {
            Integer callQueueMod = tenantId.concat(RECYCLING_RECALCULATE_BATCH).hashCode() & 0x7fffffff;
            //发送客户动作
            RecalculateMessage message = RecalculateMessage.builder().actionCode(actionCodeEnum.getActionCode())
                    .objectApiName(apiName)
                    .deptIds(deptIds)
                    .highSeas(Boolean.FALSE)
                    .tenantId(tenantId)
                    .build();
            String messageString = JSONObject.toJSON(message).toString();
            String messageMD5 = MD5Util.MD5Encode(messageString);
            taskService.createOrUpdateTask(RECYCLING_RECALCULATE_BATCH,
                    tenantId,
                    messageMD5,
                    new Date(), JSONObject.toJSON(message).toString(),callQueueMod);
        }
    }

    public void delete(String tenantId, String objectId, String apiName){
        if ("AccountObj".equals(apiName) || "LeadsObj".equals(apiName)){
            taskService.deleteTask("crm_object_recycling_task_recycling",tenantId,objectId);
            taskService.deleteTask("crm_object_recycling_task_remind",tenantId,objectId);
        }

    }


}
