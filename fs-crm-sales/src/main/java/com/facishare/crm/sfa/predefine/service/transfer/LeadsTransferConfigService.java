package com.facishare.crm.sfa.predefine.service.transfer;


import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.sfa.expression.SFAExpressionService;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.enums.TenantConfigBizType;
import com.facishare.crm.sfa.predefine.service.ObjectLimitCommonService;
import com.facishare.crm.sfa.predefine.service.TenantConfigService;
import com.facishare.crm.sfa.predefine.service.transfer.model.TransferConfigModel;
import com.facishare.crm.util.Safes;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_PARAMET_ERERROR;

@Slf4j
@Service
@ServiceModule("leads_transfer_config")
public class LeadsTransferConfigService {


	@Autowired
	private ConfigService configService;

	@Autowired
	private ServiceFacade serviceFacade;
	
	@Autowired
	private TenantConfigService tenantConfigService;

	@Autowired
	private SFAExpressionService sfaExpressionService;

	@Autowired
	private ObjectLimitCommonService objectLimitCommonService;

	public static final String EMPTY_JSON_ARR = "[]";

	/**
	 * 查询线索转换配置
	 */
	@ServiceMethod("query")
	public TransferConfigModel.QueryResult query(ServiceContext serviceContext, TransferConfigModel.QueryArg queryArg) {

		TransferConfigModel.TransferConfig transferConfig = this.getTransferConfig(serviceContext.getUser());

		return TransferConfigModel.QueryResult.builder().transferConfig(transferConfig).build();
	}


	/**
	 * 保存线索转换配置
	 */
	@ServiceMethod("save")
	public TransferConfigModel.SaveResult save(ServiceContext serviceContext, TransferConfigModel.SaveArg saveArg) {

		TransferConfigModel.TransferConfig transferConfig = saveArg.getTransferConfig();
		if (transferConfig == null) {
			throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
		}
		tenantConfigService.saveTenantConfig(serviceContext.getUser(), TenantConfigBizType.LEADS_TRANSFER, JSON.toJSONString(transferConfig));

		return TransferConfigModel.SaveResult.builder().build();
	}



	@ServiceMethod("verify")
	public TransferConfigModel.VerifyResult verify(ServiceContext serviceContext, TransferConfigModel.VerifyArg arg) {
		if (Safes.isEmpty(arg.getLeadsId())) {
			throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
		}
		IObjectData leadsData = serviceFacade.findObjectData(serviceContext.getUser(), arg.getLeadsId(),
				SFAPreDefineObject.Leads.getApiName());
		if (leadsData == null) {
			throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
		}

		IObjectDescribe leadsDescribe = serviceFacade.findObject(serviceContext.getTenantId(),
				SFAPreDefineObject.Leads.getApiName());

		return verifyConfig(serviceContext.getUser(), leadsData, leadsDescribe);

	}

	public TransferConfigModel.VerifyResult verifyConfig(User user, IObjectData leadsData, IObjectDescribe leadsDescribe) {
		if (leadsDescribe == null) {
			leadsDescribe = serviceFacade.findObject(user.getTenantId(), SFAPreDefineObject.Leads.getApiName());
		}

		TransferConfigModel.TransferConfig transferConfig = getTransferConfig(user);

		TransferConfigModel.VerifyResult verifyResult = new TransferConfigModel.VerifyResult();
		// NOSONAR
		BeanUtils.copyProperties(transferConfig, verifyResult);
		String contactAllowTransferCondition = transferConfig.getContactAllowTransferCondition();
		if (StringUtils.isNotEmpty(contactAllowTransferCondition) && !EMPTY_JSON_ARR.equals(contactAllowTransferCondition)) {
			Boolean evaluate = sfaExpressionService.evaluate(contactAllowTransferCondition, leadsData, leadsDescribe);
			verifyResult.setContactMatchAllowCondition(evaluate);
			String wheresDesc = objectLimitCommonService.getWheresContent(user, contactAllowTransferCondition, leadsDescribe);
			verifyResult.setContactAllowConditionDesc(wheresDesc);
		} else {
			verifyResult.setContactMatchAllowCondition(true);
		}
		if (transferConfig.isContactValidateMustTransfer()) {
			String contactMustTransferCondition = transferConfig.getContactMustTransferCondition();
			if (StringUtils.isNotEmpty(contactMustTransferCondition) && !EMPTY_JSON_ARR.equals(contactMustTransferCondition)) {
				Boolean evaluate = sfaExpressionService.evaluate(contactMustTransferCondition, leadsData, leadsDescribe);
				verifyResult.setContactMatchMustCondition(evaluate);
				String wheresDesc = objectLimitCommonService.getWheresContent(user, contactMustTransferCondition, leadsDescribe);
				verifyResult.setContactMustConditionDesc(wheresDesc);
			} else {
				verifyResult.setContactMatchMustCondition(true);
			}
		}
		String opportunityAllowTransferCondition = transferConfig.getOpportunityAllowTransferCondition();
		if (StringUtils.isNotEmpty(opportunityAllowTransferCondition) && !EMPTY_JSON_ARR.equals(opportunityAllowTransferCondition)) {
			Boolean evaluate = sfaExpressionService.evaluate(opportunityAllowTransferCondition, leadsData, leadsDescribe);
			verifyResult.setOpportunityMatchAllowCondition(evaluate);
			String wheresDesc = objectLimitCommonService.getWheresContent(user, opportunityAllowTransferCondition, leadsDescribe);
			verifyResult.setOpportunityAllowConditionDesc(wheresDesc);
		} else {
			verifyResult.setOpportunityMatchAllowCondition(true);
		}
		if (transferConfig.isOpportunityValidateMustTransfer()) {
			String opportunityMustTransferCondition = transferConfig.getOpportunityMustTransferCondition();
			if (StringUtils.isNotEmpty(opportunityMustTransferCondition) && !EMPTY_JSON_ARR.equals(opportunityMustTransferCondition)) {
				Boolean evaluate = sfaExpressionService.evaluate(opportunityMustTransferCondition, leadsData, leadsDescribe);
				verifyResult.setOpportunityMatchMustCondition(evaluate);
				String wheresDesc = objectLimitCommonService.getWheresContent(user, opportunityMustTransferCondition, leadsDescribe);
				verifyResult.setOpportunityMustConditionDesc(wheresDesc);
			} else {
				verifyResult.setOpportunityMatchMustCondition(true);
			}
		}
		if (transferConfig.isNewOpportunityValidateMustTransfer()) {
			String newOpportunityMustTransferCondition = transferConfig.getNewOpportunityMustTransferCondition();
			if (StringUtils.isNotEmpty(newOpportunityMustTransferCondition) && !EMPTY_JSON_ARR.equals(newOpportunityMustTransferCondition)) {
				Boolean evaluate = sfaExpressionService.evaluate(newOpportunityMustTransferCondition, leadsData, leadsDescribe);
				verifyResult.setNewOpportunityMatchMustCondition(evaluate);
				String wheresDesc = objectLimitCommonService.getWheresContent(user, newOpportunityMustTransferCondition, leadsDescribe);
				verifyResult.setNewOpportunityMustConditionDesc(wheresDesc);
			} else {
				verifyResult.setNewOpportunityMatchMustCondition(true);
			}
		}
		return verifyResult;
	}


	private TransferConfigModel.TransferConfig getTransferConfig(User user) {
		TransferConfigModel.TransferConfig transferConfig = tenantConfigService.queryTenantConfig(user.getTenantId(),
				TenantConfigBizType.LEADS_TRANSFER, TransferConfigModel.TransferConfig.class);
		if (transferConfig == null) {
			transferConfig = getTransferConfigV2(user);
		}
		if (transferConfig == null) {
			transferConfig = getTransferConfigV1(user);
		}
		if (transferConfig == null) {
			transferConfig = getDefaultConfig();
		}
		return transferConfig;
	}

	private TransferConfigModel.TransferConfig getDefaultConfig() {
		TransferConfigModel.TransferConfig transferConfig = new TransferConfigModel.TransferConfig();
		transferConfig.setAccountAllowTransferCondition(null);
		transferConfig.setAccountValidateMustTransfer(true);
		transferConfig.setAccountMustTransferCondition(null);

		transferConfig.setContactAllowTransferCondition(null);
		transferConfig.setContactValidateMustTransfer(false);
		transferConfig.setContactMustTransferCondition(null);

		transferConfig.setOpportunityAllowTransferCondition(null);

		transferConfig.setOpportunityValidateMustTransfer(false);
		transferConfig.setOpportunityMustTransferCondition(null);

		transferConfig.setNewOpportunityValidateMustTransfer(false);
		transferConfig.setNewOpportunityMustTransferCondition(null);

		return transferConfig;
	}

	/**
	 * 第一版线索转换配置，只按对象校验
	 */
	private TransferConfigModel.TransferConfig getTransferConfigV1(User user) {
		String v1Config = configService.findTenantConfig(user, ConfigType.LeadsTransferSetting.getKey());
		if (StringUtils.isEmpty(v1Config)) {
			return null;
		}
		v1Config = ConfigType.LeadsTransferSetting.getValue(v1Config);
		String[] split = v1Config.split(",");
		if (split.length < 4) {
			log.error("配置错误，value:{}", v1Config);
			throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
		}
		TransferConfigModel.TransferConfig transferConfig = getDefaultConfig();
		if (split[1].equals("1")) {
			transferConfig.setContactValidateMustTransfer(true);
		}
		if (split[2].equals("1")) {
			transferConfig.setOpportunityValidateMustTransfer(true);
		}
		if (split[3].equals("1")) {
			transferConfig.setNewOpportunityValidateMustTransfer(true);
		}
		return transferConfig;
	}

	/**
	 * 第二版线索转换配置
	 */
	private TransferConfigModel.TransferConfig getTransferConfigV2(User user) {
		String v2Config = configService.findTenantConfig(user, ConfigType.MUST_TRANSFER_BY_TYPE.getKey());
		if (StringUtils.isEmpty(v2Config)) {
			return null;
		}

		/*
		 *  第2版线索转换配置
		 *	820配置：支持按照业务类型设置必转，新的value格式：{"default__c":"1,1,0,1", "typeA":"1,0,0,1"}
		 *  {@see http://wiki.firstshare.cn/pages/viewpage.action?pageId=176143229}
		 */
		@SuppressWarnings("unchecked") Map<String, String> map = JSON.parseObject(v2Config, Map.class);

		List<String> mustTransferContactTypeList = Lists.newArrayList();
		List<String> mustTransferOpportunityTypeList = Lists.newArrayList();
		List<String> mustTransferNewOpportunityTypeList = Lists.newArrayList();
		for (Map.Entry<String, String> entry : map.entrySet()) {
			String key = entry.getKey();
			String value = entry.getValue();
			if (StringUtils.isEmpty(value)) {
				continue;
			}
			String[] values = value.split(",");
			if (values.length < 4) {
				log.error("配置错误，value:{}", v2Config);
				throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
			}
			if (values[1].equals("1")) {
				mustTransferContactTypeList.add(key);
			}
			if (values[2].equals("1")) {
				mustTransferOpportunityTypeList.add(key);
			}
			if (values[3].equals("1")) {
				mustTransferNewOpportunityTypeList.add(key);
			}
		}

		TransferConfigModel.TransferConfig transferConfig = getDefaultConfig();
		if (Safes.isNotEmpty(mustTransferContactTypeList)) {
			transferConfig.setContactValidateMustTransfer(true);
			transferConfig.setContactMustTransferCondition(convert2Wheres(mustTransferContactTypeList));
		}
		if (Safes.isNotEmpty(mustTransferOpportunityTypeList)) {
			transferConfig.setOpportunityValidateMustTransfer(true);
			transferConfig.setOpportunityMustTransferCondition(convert2Wheres(mustTransferOpportunityTypeList));
		}
		if (Safes.isNotEmpty(mustTransferNewOpportunityTypeList)) {
			transferConfig.setNewOpportunityValidateMustTransfer(true);
			transferConfig.setNewOpportunityMustTransferCondition(convert2Wheres(mustTransferNewOpportunityTypeList));
		}
		return transferConfig;
	}

	private String convert2Wheres(List<String> typeList) {
		Wheres wheres = new Wheres();
		Filter filter = new Filter();
		filter.setOperator(Operator.IN);
		filter.setFieldName(IObjectData.RECORD_TYPE);
		filter.setFieldValues(typeList);
		wheres.setFilters(Lists.newArrayList(filter));
		return new Gson().toJson(Lists.newArrayList(wheres));
	}






}
