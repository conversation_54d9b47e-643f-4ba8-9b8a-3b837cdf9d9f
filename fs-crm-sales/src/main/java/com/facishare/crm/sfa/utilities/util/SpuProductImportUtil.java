package com.facishare.crm.sfa.utilities.util;

import com.facishare.crm.sfa.utilities.constant.AttributeConstants;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.metadata.api.Tenantable;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/3/3 上午11:24
 * @illustration
 * @slogan:
 * @version:
 */
public class SpuProductImportUtil {

    public static final String FIELD_NAME_CATEGORY = "category";
    public static final String FIELD_NAME_IS_SPEC = "is_spec";
    public static final String FIELD_NAME_PRODUCT_LINE = "product_line";
    public static final String FIELD_NAME_UNIT = "unit";
    public static final String FIELD_NAME_BATCH_SN = "batch_sn";
    public static final String FIELD_NAME_SPU_ID = "spu_id";
    public static final String FIELD_NAME_PRODUCT_SPEC = "product_spec";
    public static final String FIELD_NAME_SPEC_KEY = "spec";
    public static final String FIELD_NAME_SPEC_VALUE_KEY = "specValue";
    public static final String FIELD_NAME_SPEC_ID = "spec_id";
    public static final String FIELD_NAME_SPEC_VALUE_ID = "spec_value_id";
    public static final String FIELD_NAME_ON_SHELVES_TIME = "on_shelves_time";
    public static final String FIELD_NAME_RECORD_TYPE = "record_type";
    public static final String FIELD_NAME_OWNER = "owner";
    public static final String PRODUCT_CATEGORY_ID = "product_category_id";
    public static final String SHOP_CATEGORY_ID = "shop_category_id";
    public static final String PRODUCT_IS_PACKAGE = "is_package";

    public static final int ATTRIBUTE_MAX_LENGTH = 30;

    public static final List<String> SPU_INSERT_IMPORT_REMOVE_FIELDS = Lists.newArrayList("is_multiple_unit", "is_package","bom_type");

    public static final List<String> SPU_UPDATE_IMPORT_REMOVE_FIELDS = Lists.newArrayList("is_multiple_unit", "batch_sn", "is_spec","is_package","bom_type");

    public static final List<String> SKU_UPDATE_IMPORT_REMOVE_FIELDS = Lists.newArrayList("category", "product_line", "unit",
            "is_multiple_unit", "batch_sn", "is_spec", "product_spec", "spu_id", "is_package",
            "owner_department","off_shelves_time", "on_shelves_time", "product_category_id","bom_type", "shop_category_id");

    public static final List<String> SKU_INSERT_IMPORT_REMOVE_FIELDS = Lists.newArrayList("category", "product_line",
            "unit", "on_shelves_time", "off_shelves_time", "is_package", "is_multiple_unit", "batch_sn","product_category_id","bom_type", "shop_category_id");

    public static List<String> skuImportRemoveAttributeFields(String tenantId) {
        List<String> result = new ArrayList<>();
        int i = 1;
        int limit = AttributeUtils.getAttributeLimit(tenantId);
        for (; i <= limit; i++) {
            result.add(AttributeConstants.ATTRIBUTE + i);
        }
        result.add(AttributeConstants.ProductField.ATTRIBUTE_IDS);
        result.add(AttributeConstants.ProductField.PRICING_ATTRIBUTES);
        return result;
    }


    /**
     * 校验ProductSpec 的规范性
     *
     * @param productSpec
     * @return
     */
    public static boolean validatePatternSpec(String productSpec){
        char[] chars = productSpec.toCharArray();
        int i = 0;
        int j = 0;
        for (char aChar : chars) {
            if (aChar == ':') {
                i++;
            }
            if (aChar == ';') {
                j++;
            }
        }

        return i == j;
    }

    /**
     * 颜色:红色; 内存:256G; 尺寸: 5.0;
     */
    public static Map<String, List<String>> specAndSpecValue(String productSpec) {
        int firstPoint = 0;
        int secondPoint = 0;
        int index = 0;
        int length = productSpec.length();
        String newStr = productSpec;

        List<String> specList = Lists.newArrayList();
        List<String> specValueList = Lists.newArrayList();
        StringBuilder buffer = new StringBuilder();


        while (newStr.indexOf(":") != -1) {
            firstPoint = newStr.indexOf(":");
            String spec = newStr.substring(0, firstPoint);
            specList.add(spec);
            newStr = newStr.substring(firstPoint + 1);

            secondPoint = newStr.indexOf(";");
            String specValue = newStr.substring(0, secondPoint);
            specValueList.add(specValue);
            newStr = newStr.substring(secondPoint + 1);

            index = index + firstPoint + secondPoint + 2;
            sqlWhere(buffer, index < length, spec, specValue);
        }

        Map<String, List<String>> maps = Maps.newHashMap();
        maps.put("spec", specList);
        maps.put("specValue", specValueList);
        maps.put("sqlWhere", Lists.newArrayList(buffer.toString()));

        return maps;
    }

    public static String replaceProductSpec(String productSpec){
        String newProductSpec = replace(productSpec);
        String lastWord = newProductSpec.substring(newProductSpec.length() - 1);
        if(!Objects.equals(lastWord, ";")){
            newProductSpec = newProductSpec.concat(";");
        }
        return newProductSpec;
    }

    public static String getSuffixName(String productSpec){
        String[] split = productSpec.split(";");
        StringBuilder result = new StringBuilder("[");
        for (String temp : split) {
            String[] split1 = temp.split(":");
            result.append(split1[1]).append("-");
        }
        return result.substring(0, result.length() - 1).concat("]");
    }


    private static String replace(String source){
        return source.replaceAll("：",":").replaceAll("；",";");
    }


    private static void sqlWhere(StringBuilder buffer, boolean last, String spec, String specValue) {
        buffer.append("(spec_and_spec_value.spec_name = '").append(translationValue(spec)).append("' and spec_and_spec_value.spec_value_name = '").append(translationValue(specValue)).append("')");
        if (last) {
            buffer.append(" or ");
        }
    }


    public static List<String> translationValue(Collection<String> translationList){
        List<String> result = new ArrayList<>();
        for (String translationValue : translationList) {
            result.add(translationValue(translationValue));
        }
        return result;
    }

    public static String translationValue(String translationValue) {
        if (!translationValue.contains("'")) {
            return translationValue;
        }
        StringBuilder newTranStrBuilder = new StringBuilder();
        char[] chars = translationValue.toCharArray();
        for (char aChar : chars) {
            newTranStrBuilder.append(aChar);
            if (aChar == '\'') {
                newTranStrBuilder.append("'");
            }
        }
        return newTranStrBuilder.toString();
    }

    public static List<BaseImportAction.ImportError> mergeErrorList(List<BaseImportAction.ImportError> errors){
        Map<Integer, BaseImportAction.ImportError> rowNoToMessage = Maps.newHashMap();
        for (BaseImportAction.ImportError error : errors) {
            int rowNo = error.getRowNo();
            String errorMessage = error.getErrorMessage();
            if(rowNoToMessage.containsKey(rowNo)){
                BaseImportAction.ImportError importError = rowNoToMessage.get(rowNo);
                String mergeErrorMessage = String.format("%s\n%s", importError.getErrorMessage(), errorMessage);
                importError.setErrorMessage(mergeErrorMessage);
                rowNoToMessage.put(rowNo,importError);
            }else{
                BaseImportAction.ImportError importError = new BaseImportAction.ImportError(rowNo, errorMessage);
                rowNoToMessage.put(rowNo, importError);
            }
        }
        List<BaseImportAction.ImportError> allErrorList = Lists.newArrayList();
        allErrorList.addAll(rowNoToMessage.values());
       return allErrorList;
    }


    public static Map<String, String>  buildSpuSkuRelateData(String spuId, String specId, String specValueId, String orderIndex, String tenantId, String skuId){
        Map<String, String> skuSpecMapping = Maps.newHashMap();
        skuSpecMapping.put("spu_id", spuId);
        skuSpecMapping.put("spec_id", specId);
        skuSpecMapping.put("spec_value_id", specValueId);
        skuSpecMapping.put("order_field", orderIndex);
        skuSpecMapping.put("object_describe_api_name", "SpuSkuSpecValueRelateObj");
        skuSpecMapping.put(Tenantable.TENANT_ID, tenantId);
        skuSpecMapping.put("sku_id",skuId);
        return skuSpecMapping;
    }

    public static String appendSpecValueToName(List<String> specValueList, String name,
                                               List<String> specList, List<String> existSpecList){
        String newName = name + "[%s]";
        List<String> newSpecValueList = Lists.newArrayListWithExpectedSize(specValueList.size());
        for (String spec : existSpecList) {
            int index = specList.indexOf(spec);
            if (index > -1) {
                newSpecValueList.add(specValueList.get(index));
            }
        }
        return String.format(newName, Joiner.on("-").join(newSpecValueList));
    }



    public static String countSkuNumberBySpuIds(String tenantId, Set<String> spuIds){
        return String.format(QUERY_SKU_COUNT_GROUP_BY_SPU_ID_SQL,
                tenantId, Joiner.on("','").join(translationValue(spuIds)));
    }

    public static String findSpecAndSpecValueDataList(List<String> specList, List<String> specValueList, List<String> sqlWhereList, String tenantId){
        return String.format(QUERY_SPEC_AND_SPEC_VALUE_NAME_TO_ID_BY_SQL,
                tenantId,
                Joiner.on("','").join(translationValue(specList)),
                Joiner.on("','").join(translationValue(specValueList)),
                tenantId,
                Joiner.on(" or ").join(sqlWhereList));
    }

    public static String findSpuSkuSpecSpecValueJoinSQL(String tenantId, List<String> spuIds) {
        return String.format(QUERY_SPU_SKU_SPEC_SPEC_VALUE_SQL,
                tenantId,
                Joiner.on("','").join(translationValue(spuIds)),
                tenantId,
                tenantId);
    }

    public static String findProductSpecBySpuSQL(String tenantId, List<String> spuIds){
        return String.format(QUERY_SKU_PRODUCT_SPEC_BY_SPU_ID_SQL,
                tenantId,
                Joiner.on("','").join(translationValue(spuIds)));
    }



    public static final String QUERY_SPEC_AND_SPEC_VALUE_NAME_TO_ID_BY_SQL = "select *\n" +
            "from (select spec.id        as spec_id,\n" +
            "             specvalue.id   as spec_value_id,\n" +
            "             spec.tenant_id as tenant_id,\n" +
            "             spec.name      as spec_name,\n" +
            "             specvalue.name as spec_value_name\n" +
            "      from specification as spec\n" +
            "               join specification_value as specvalue on spec.id = specvalue.specification_id\n" +
            "          and spec.tenant_id = specvalue.tenant_id\n" +
            "      where spec.tenant_id = '%s'\n" +
            "        and spec.is_deleted = 0\n" +
            "        and spec.status = '1'\n" +
            "        and specvalue.is_deleted = 0\n" +
            "        and specvalue.status = '1'\n" +
            "        and spec.name in ('%s')\n" +
            "        and specvalue.name in ('%s')) as spec_and_spec_value\n" +
            "where spec_and_spec_value.tenant_id = '%s'\n" +
            "  and (%s);";


    public static final String QUERY_SKU_COUNT_GROUP_BY_SPU_ID_SQL = "\n" +
            "select spu_id, count(*) as count\n" +
            "from biz_product\n" +
            "where tenant_id = '%s'\n" +
            "  and is_deleted in (0, 1)\n" +
            "  and spu_id in ('%s')\n" +
            "group by spu_id;";


    public static final String QUERY_SPU_SKU_SPEC_SPEC_VALUE_SQL = "select spu_id, sku_id, temp1.spec_id, temp1.spec_value_id, order_field, spec_name, spec_value_name\n" +
            "from (select spkr.spu_id, spkr.sku_id, spkr.spec_id, spkr.spec_value_id, spkr.order_field\n" +
            "      from spu_sku_spec_value_relate as spkr\n" +
            "               join (select s.spu_id, s.id as sku_id\n" +
            "                     from (\n" +
            "                              select *, row_number() over (partition by spu_id order by id) as group_idx\n" +
            "                              from biz_product\n" +
            "                              where tenant_id = '%s'\n" +
            "                                and spu_id in ('%s')\n" +
            "                          ) as s\n" +
            "                     where s.group_idx = 1) as spusku on spkr.spu_id = spusku.spu_id and spkr.sku_id = spusku.sku_id\n" +
            "          and spkr.tenant_id = '%s'\n" +
            "      order by spkr.sku_id, order_field) as temp1\n" +
            "         join (select spec.id        as spec_id,\n" +
            "                      specvalue.id   as spec_value_id,\n" +
            "                      spec.name      as spec_name,\n" +
            "                      specvalue.name as spec_value_name\n" +
            "               from specification as spec\n" +
            "                        join specification_value as specvalue on spec.id = specvalue.specification_id\n" +
            "                   and spec.tenant_id = specvalue.tenant_id\n" +
            "               where spec.tenant_id = '%s') as temp2\n" +
            "              on temp1.spec_id = temp2.spec_id and temp1.spec_value_id = temp2.spec_value_id order by spu_id,order_field;";



    public static final String QUERY_SKU_PRODUCT_SPEC_BY_SPU_ID_SQL = "select spu_id, product_spec\n" +
            "from biz_product\n" +
            "where tenant_id = '%s'\n" +
            "  and spu_id in ('%s')\n" +
            "  and is_deleted in ('0', '1');";


}
