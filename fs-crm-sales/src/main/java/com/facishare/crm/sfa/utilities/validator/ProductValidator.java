package com.facishare.crm.sfa.utilities.validator;

import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.constant.SpuSkuConstants;
import com.facishare.crm.sfa.utilities.util.CommonSearchUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.IGroupByParameter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.GroupByParameter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.predef.action.BaseImportTemplateAction.IMPORT_TYPE_EDIT;

public class ProductValidator {
    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    private static final int MAX_BATCH_SIZE = 1000;
    private static final int LOOP_COUNT = 10;
    private static final String BATCH_SN = "batch_sn";
    private static final String IS_MULTIPLE_UNIT = "is_multiple_unit";
    private static final String ENABLE_SERIAL_MANAGEMENT = "3";
    private static final String SKU_ID = "sku_id";
    private static final String SPEC_ID = "spec_id";
    private static final String SPEC_VALUE_ID = "spec_value_id";
    private static final String SPU_ID = "spu_id";
    private static final String STATUS = "status";
    private static final String SPEC_AND_VALUE = "spec_and_value";
    private static final String FIELD_I18N_KEY = "ProductObj.field.%s.label";
    private static final List<String> FIELDS_API_NAMES = Lists.newArrayList(ProductConstants.PRICING_CYCLE, ProductConstants.PRICING_FREQUENCY, ProductConstants.SETTLEMENT_MODE, ProductConstants.SETTLEMENT_CYCLE, ProductConstants.SETTLEMENT_FREQUENCY, ProductConstants.WHOLE_PERIOD_SALE);
    private static final List<String> GREATER_ZERO_API_NAMES = Lists.newArrayList(ProductConstants.PRICING_FREQUENCY, ProductConstants.SETTLEMENT_FREQUENCY);

    public static void validateSerialNumberAndMultiUnit(IObjectData objectData) {
        //序列号商品不允许启用多单位
        String batchSn = objectData.get(BATCH_SN, String.class);
        if (ENABLE_SERIAL_MANAGEMENT.equals(batchSn)) {
            Boolean isMultipleUnit = objectData.get(IS_MULTIPLE_UNIT, Boolean.class);
            if (Boolean.TRUE.equals(isMultipleUnit)) {
                throw new ValidateException(I18N.text("spu.sku.batch.sn.msg"));
            }
        }
    }

    public static void validateSpecStatusActive(String tenantId, List<IObjectData> skuList) {
        validateSpecStatusActive(tenantId, skuList, null);
    }

    public static void validateSpecStatusActive(String tenantId, List<IObjectData> toAddSkuList, List<IObjectData> toEditSkuList) {
        Set<String> specIds = Sets.newHashSet();
        Set<String> specValueIds = Sets.newHashSet();
        getSpecIdSet2Validate(tenantId, toAddSkuList, toEditSkuList, specIds, specValueIds);
        validateStatusActive(tenantId, Lists.newArrayList(specIds), SFAPreDefineObject.Specification.getApiName());
        validateStatusActive(tenantId, Lists.newArrayList(specValueIds), SFAPreDefineObject.SpecificationValue.getApiName());
    }

    public static void validateSpecValueExistSubset(String tenantId, String spuId, Set<String> specValueIds) {
        List<IObjectData> skuDataList = getSkuData(tenantId, spuId);
        if (CollectionUtils.empty(skuDataList)) {
            return;
        }
        List<String> skuIds = skuDataList.stream().map(r -> r.getId()).collect(Collectors.toList());
        List<IObjectData> skuRelatedSpecDataList = getRelatedSpecListBySpuId(tenantId, spuId, skuIds);
        if (CollectionUtils.empty(skuRelatedSpecDataList)) {
            return;
        }
        Map<String, Set<String>> skuSpecValueIdsMap = skuRelatedSpecDataList.stream()
                .collect(Collectors.groupingBy(r -> r.get(SKU_ID, String.class),
                        Collectors.mapping(r -> r.get(SPEC_VALUE_ID, String.class), Collectors.toSet())));
        int currentSize = specValueIds.size();
        Set<String> usedSpecValueIds;
        for (Map.Entry<String, Set<String>> entry : skuSpecValueIdsMap.entrySet()) {
            usedSpecValueIds = entry.getValue();
            int usedSize = usedSpecValueIds.size();
            usedSpecValueIds.retainAll(specValueIds);
            if (currentSize == usedSpecValueIds.size() || usedSize == usedSpecValueIds.size()) {
                throw new ValidateException(I18N.text("spu.spec_value.overlap"));
            }
        }
    }

    private static void validateStatusActive(String tenantId, List<String> idList, String apiName) {
        if (CollectionUtils.empty(idList) || Strings.isNullOrEmpty(apiName)) {
            return;
        }
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(tenantId, idList, apiName);
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        List<String> disabledNames = objectDataList.stream()
                .filter(objectData -> !PriceBookConstants.ActiveStatus.ON.getStatus().equals(objectData.get(STATUS, String.class)))
                .map(IObjectData::getName)
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(disabledNames)) {
            throw new ValidateException(String.format(I18N.text("sfa.specification.disabled.warn"),
                    String.join("、", disabledNames)));
        }
    }

    private static void getSpecIdSet2Validate(String tenantId, List<IObjectData> toAddSkuList, List<IObjectData> toEditSkuList,
                                              Set<String> specIds, Set<String> specValueIds) {
        if (CollectionUtils.notEmpty(toAddSkuList)) {
            for (IObjectData objectData : toAddSkuList) {
                List<Map<String, String>> specValues = objectData.get(SPEC_AND_VALUE, List.class);
                for (Map<String, String> item : specValues) {
                    String specId = item.get(SPEC_ID);
                    if (!Strings.isNullOrEmpty(specId)) {
                        specIds.add(specId);
                    }
                    String specValueId = item.get(SPEC_VALUE_ID);
                    if (!Strings.isNullOrEmpty(specValueId)) {
                        specValueIds.add(specValueId);
                    }
                }
            }
        }
        if (CollectionUtils.notEmpty(toEditSkuList)) {
            String spuId = toEditSkuList.get(0).get(SPU_ID, String.class);
            if (Strings.isNullOrEmpty(spuId)) {
                return;
            }
            Set<String> originalSpecIds = Sets.newHashSet();
            Set<String> originalSpecValueIds = Sets.newHashSet();
            List<String> skuIds = toEditSkuList.stream().map(objectData -> objectData.getId()).collect(Collectors.toList());
            getRelatedSpecAndValueBySpuId(tenantId, spuId, skuIds, originalSpecIds, originalSpecValueIds);
            for (IObjectData objectData : toEditSkuList) {
                List<Map<String, String>> specValues = objectData.get(SPEC_AND_VALUE, List.class);
                for (Map<String, String> item : specValues) {
                    String specId = item.get(SPEC_ID);
                    if (!Strings.isNullOrEmpty(specId) && !originalSpecIds.contains(specId)) {
                        specIds.add(specId);
                    }
                    String specValueId = item.get(SPEC_VALUE_ID);
                    if (!Strings.isNullOrEmpty(specValueId) && !originalSpecValueIds.contains(specValueId)) {
                        specValueIds.add(specValueId);
                    }
                }
            }
        }
    }

    private static void getRelatedSpecAndValueBySpuId(String tenantId, String spuId, List<String> skuIds,
                                                      Set<String> specIds, Set<String> specValueIds) {
        List<IObjectData> relatedSpecList = getRelatedSpecListBySpuId(tenantId, spuId, skuIds);
        if (CollectionUtils.empty(relatedSpecList)) {
            return;
        }
        for (IObjectData objectData : relatedSpecList) {
            String specId = objectData.get(SPEC_ID, String.class);
            if (!Strings.isNullOrEmpty(specId)) {
                specIds.add(specId);
            }
            String specValueId = objectData.get(SPEC_VALUE_ID, String.class);
            if (!Strings.isNullOrEmpty(specValueId)) {
                specValueIds.add(specValueId);
            }
        }
    }

    private static List<IObjectData> getRelatedSpecListBySpuId(String tenantId, String spuId, List<String> skuIds) {
        SearchTemplateQuery query = buildSearchQuery(spuId, skuIds);
        List<IObjectData> objectDataList = Lists.newArrayList();
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        int offset = 0;
        int loopCnt = 0;
        //同商品下最多15个规格，最多300个产品
        while (loopCnt < LOOP_COUNT) {
            query.setOffset(offset);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, SpuSkuConstants.SPUSKUSPECVALUERELATEOBJ, query);
            if (queryResult != null && CollectionUtils.notEmpty(queryResult.getData())) {
                objectDataList.addAll(queryResult.getData());
            }
            if (queryResult == null || CollectionUtils.empty(queryResult.getData()) || queryResult.getData().size() < MAX_BATCH_SIZE) {
                break;
            }
            offset += MAX_BATCH_SIZE;
            loopCnt++;
        }
        return objectDataList;
    }

    private static SearchTemplateQuery buildSearchQuery(String spuId, List<String> skuIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName(SPU_ID);
        filter.setFieldValues(Lists.newArrayList(spuId));
        filter.setOperator(Operator.EQ);
        filters.add(filter);
        IFilter skuFilter = new Filter();
        skuFilter.setFieldName(SKU_ID);
        skuFilter.setFieldValues(skuIds);
        skuFilter.setOperator(Operator.IN);
        filters.add(skuFilter);
        query.setFilters(filters);
        query.setPermissionType(0);
        query.setLimit(MAX_BATCH_SIZE);
        query.setNeedReturnCountNum(Boolean.FALSE);
        query.setNeedReturnQuote(Boolean.FALSE);
        return query;
    }

    private static List<IObjectData> getSkuData(String tenantId, String spuId) {
        List<IObjectData> objectDataList = Lists.newArrayList();
        SearchTemplateQuery searchTemplateQuery = buildSkuSearchQueryIncludeInvalid(spuId);
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, SpuSkuConstants.PRODUCTOBJ, searchTemplateQuery);
        if (null != queryResult && CollectionUtils.notEmpty(queryResult.getData())) {
            objectDataList.addAll(queryResult.getData());
        }
        return objectDataList;
    }

    private static SearchTemplateQuery buildSkuSearchQueryIncludeInvalid(String spuId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName(SPU_ID);
        filter.setFieldValues(Lists.newArrayList(spuId));
        filter.setOperator(Operator.EQ);
        filters.add(filter);
        IFilter deleteFilter = new Filter();
        deleteFilter.setFieldName(ObjectData.IS_DELETED);
        deleteFilter.setFieldValues(Lists.newArrayList("0", "1"));
        deleteFilter.setOperator(Operator.IN);
        filters.add(deleteFilter);
        query.setFilters(filters);
        query.setPermissionType(0);
        query.setLimit(MAX_BATCH_SIZE);
        query.setNeedReturnCountNum(Boolean.FALSE);
        query.setNeedReturnQuote(Boolean.FALSE);
        return query;
    }

    public static void handlePeriodicProduct(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList) || !bizConfigThreadLocalCacheService.openPeriodicProduct(user.getTenantId())) {
            return;
        }
        for (IObjectData objectData : objectDataList) {
            String pricingMode = objectData.get(ProductConstants.PRICING_MODE, String.class);
            if (StringUtils.isEmpty(pricingMode)) {
                throw new ValidateException(String.format(
                        I18N.text(SFAI18NKeyUtil.SFA_OBJECT_DISPLAY_NAME_NOTNULL),
                        I18N.text(String.format(FIELD_I18N_KEY, ProductConstants.PRICING_MODE))));
            }
            for (String fieldApiName : FIELDS_API_NAMES) {
                if (ProductConstants.PricingModeEnum.ONE.getValue().equals(pricingMode)
                        && StringUtils.isNotEmpty(objectData.get(fieldApiName, String.class))) {
                    //定价模式为一次性，定价周期定价频率及结算模式等字段数据不能有值
                    throw new ValidateException(String.format(
                            I18N.text(SFAI18NKeyUtil.SFA_CYCLICAL_PRODUCTS_CANNOT_HAVE_VALUE),
                            I18N.text(String.format(FIELD_I18N_KEY, fieldApiName))));
                }
                if (ProductConstants.PricingModeEnum.CYCLE.getValue().equals(pricingMode)) {
                    //定价模式为周期性，必须填写定价周期定价频率及结算模式等字段数据
                    String fieldValue = objectData.get(fieldApiName, String.class);
                    if (StringUtils.isEmpty(fieldValue)) {
                        throw new ValidateException(String.format(
                                I18N.text(SFAI18NKeyUtil.SFA_CYCLICAL_PRODUCTS_DISPLAY_NAME_NOTNULL),
                                I18N.text(String.format(FIELD_I18N_KEY, fieldApiName))));
                    }
                    //定价频率，结算频率不能小于0
                    if (GREATER_ZERO_API_NAMES.contains(fieldApiName)) {
                        BigDecimal value = new BigDecimal(fieldValue);
                        if (value.compareTo(BigDecimal.ZERO) <= 0) {
                            throw new ValidateException(String.format(
                                    I18N.text(SFAI18NKeyUtil.SFA_CYCLE_PRODUCTS_FREQUENCY_ZERO_CHECK),
                                    I18N.text(String.format(FIELD_I18N_KEY, fieldApiName))));
                        }
                    }
                }

            }
        }
    }

    public static void checkUpdateImportPeriodicProduct(User user, List<BaseImportDataAction.ImportData> dataList,
                                                        Map<String, ObjectDataExt> dbData, List<BaseImportAction.ImportError> allErrorList,
                                                        BaseImportAction.Arg arg) {
        //导入数据的字段有空值时，是否更新现有字段。如果选择是：直接用导入参数中的数据；如果选择否：判断字段是否为空，如果为空，则用数据库中的字段进行判断
        if (CollectionUtils.empty(dataList) || !bizConfigThreadLocalCacheService.openPeriodicProduct(user.getTenantId())) {
            return;
        }
        boolean isEmptySetDbDate = arg.getImportType() == IMPORT_TYPE_EDIT && !Boolean.TRUE.equals(arg.getIsEmptyValueToUpdate());
        if (isEmptySetDbDate) {
            for (BaseImportDataAction.ImportData data : dataList) {
                IObjectData objectData = data.getData();
                updateFieldIfEmpty(objectData, dbData, ProductConstants.PRICING_MODE);
                for (String fieldApiName : FIELDS_API_NAMES) {
                    updateFieldIfEmpty(objectData, dbData, fieldApiName);
                }
            }
        }
        checkImportPeriodicProduct(user, dataList, allErrorList);
        checkImportPricingMode(user, dataList, dbData, allErrorList);
    }

    private static void updateFieldIfEmpty(IObjectData objectData, Map<String, ObjectDataExt> dbData, String fieldApiName) {
        if (StringUtils.isEmpty(objectData.get(fieldApiName, String.class))) {
            ObjectDataExt dbObjectData = dbData.get(objectData.getId());
            if (dbObjectData != null) {
                objectData.set(fieldApiName, dbObjectData.get(fieldApiName, String.class));
            }
        }
    }

    public static void checkImportPeriodicProduct(User user, List<BaseImportDataAction.ImportData> dataList,
                                                  List<BaseImportAction.ImportError> allErrorList) {
        if (CollectionUtils.empty(dataList) || !bizConfigThreadLocalCacheService.openPeriodicProduct(user.getTenantId())) {
            return;
        }
        for (BaseImportDataAction.ImportData data : dataList) {
            IObjectData objectData = data.getData();
            String pricingMode = objectData.get(ProductConstants.PRICING_MODE, String.class);
            if (StringUtils.isEmpty(pricingMode)) {
                allErrorList.add(new BaseImportAction.ImportError(data.getRowNo(), String.format(
                        I18N.text(SFAI18NKeyUtil.SFA_OBJECT_DISPLAY_NAME_NOTNULL),
                        I18N.text(String.format(FIELD_I18N_KEY, ProductConstants.PRICING_MODE)))));
                continue;
            }
            List<String> pricingModeOneErrorList = Lists.newArrayList();
            List<String> pricingModeCycleErrorList = Lists.newArrayList();
            List<String> frequencyGreaterZeroErrorList = Lists.newArrayList();
            for (String fieldApiName : FIELDS_API_NAMES) {
                if (ProductConstants.PricingModeEnum.ONE.getValue().equals(pricingMode) && StringUtils.isNotEmpty(objectData.get(fieldApiName, String.class))) {
                    pricingModeOneErrorList.add(I18N.text(String.format(FIELD_I18N_KEY, fieldApiName)));
                }
                if (ProductConstants.PricingModeEnum.CYCLE.getValue().equals(pricingMode)) {
                    if (StringUtils.isEmpty(objectData.get(fieldApiName, String.class))) {
                        pricingModeCycleErrorList.add(I18N.text(String.format(FIELD_I18N_KEY, fieldApiName)));
                    }
                    if (GREATER_ZERO_API_NAMES.contains(fieldApiName) && StringUtils.isNotEmpty(objectData.get(fieldApiName, String.class))) {
                        BigDecimal value = new BigDecimal(objectData.get(fieldApiName, String.class));
                        if (value.compareTo(BigDecimal.ZERO) <= 0) {
                            frequencyGreaterZeroErrorList.add(I18N.text(String.format(FIELD_I18N_KEY, fieldApiName)));
                        }
                    }
                }
            }
            if (CollectionUtils.notEmpty(pricingModeOneErrorList)) {
                allErrorList.add(new BaseImportAction.ImportError(data.getRowNo(), String.format(
                        I18N.text(SFAI18NKeyUtil.SFA_ONE_PRODUCTS_CANNOT_HAVE_VALUE),
                        StringUtils.join(pricingModeOneErrorList, ","))));
            }
            if (CollectionUtils.notEmpty(pricingModeCycleErrorList)) {
                allErrorList.add(new BaseImportAction.ImportError(data.getRowNo(), String.format(
                        I18N.text(SFAI18NKeyUtil.SFA_CYCLE_PRODUCTS_NOTNULL),
                        StringUtils.join(pricingModeCycleErrorList, ","))));
            }
            if (CollectionUtils.notEmpty(frequencyGreaterZeroErrorList)) {
                allErrorList.add(new BaseImportAction.ImportError(data.getRowNo(), String.format(
                        I18N.text(SFAI18NKeyUtil.SFA_CYCLE_PRODUCTS_FREQUENCY_ZERO_CHECK),
                        StringUtils.join(frequencyGreaterZeroErrorList, ","))));
            }
        }
    }

    /**
     * 校验产品定价模式是否可以修改
     */
    public static void checkUpdatePricingMode(User user, IObjectData objectData, IObjectData dbObjectData) {
        if (!bizConfigThreadLocalCacheService.openPeriodicProduct(user.getTenantId())
                || !bizConfigThreadLocalCacheService.isOpenPricePolicy(user.getTenantId())) {
            return;
        }
        if (Objects.equals(objectData.get(ProductConstants.PRICING_MODE, String.class), dbObjectData.get(ProductConstants.PRICING_MODE, String.class))) {
            return;
        }
        Set<String> priceModeExistSet = checkProductUsedAsGift(user, Lists.newArrayList(objectData));
        if (CollectionUtils.notEmpty(priceModeExistSet)) {
            throw new ValidateException(String.format(
                    I18N.text(SFAI18NKeyUtil.SFA_PRICING_MODE_NOT_ALLOW_MODIFY),
                    I18N.text(String.format(FIELD_I18N_KEY, ProductConstants.PRICING_MODE))));
        }
    }

    private static void checkImportPricingMode(User user, List<BaseImportDataAction.ImportData> dataList,
                                               Map<String, ObjectDataExt> dbData, List<BaseImportAction.ImportError> allErrorList) {
        if (!bizConfigThreadLocalCacheService.isOpenPricePolicy(user.getTenantId())) {
            return;
        }
        List<IObjectData> objectDataList = dataList.stream().map(BaseImportDataAction.ImportData::getData).collect(Collectors.toList());
        Set<String> priceModeExistSet = checkProductUsedAsGift(user, objectDataList);
        if (CollectionUtils.empty(priceModeExistSet)) {
            return;
        }
        for (BaseImportDataAction.ImportData data : dataList) {
            IObjectData objectData = data.getData();
            String pricingMode = objectData.get(ProductConstants.PRICING_MODE, String.class);
            if (dbData.get(objectData.getId()) == null) {
                continue;
            }
            String dbPricingMode = dbData.get(objectData.getId()).get(ProductConstants.PRICING_MODE, String.class);
            if (priceModeExistSet.contains(objectData.getId()) && !Objects.equals(pricingMode, dbPricingMode)) {
                allErrorList.add(new BaseImportAction.ImportError(data.getRowNo(), String.format(
                        I18N.text(SFAI18NKeyUtil.SFA_PRICING_MODE_NOT_ALLOW_MODIFY),
                        I18N.text(String.format(FIELD_I18N_KEY, ProductConstants.PRICING_MODE)))));
            }
        }
    }

    /**
     * 校验产品是否已经当赠品使用过
     */
    public static Set<String> checkProductUsedAsGift(User user, List<IObjectData> objectDataList) {
        Set<String> producSet=Sets.newHashSet();
        if (CollectionUtils.empty(objectDataList)) {
            return producSet;
        }
        // 获取产品id
        List<String> productIds = objectDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, PricePolicyConstants.PRODUCT_ID, productIds);
        SearchUtil.fillFilterEq(filters, PricePolicyConstants.IS_GIVE_AWAY, "1");
        IGroupByParameter groupByParameter = new GroupByParameter();
        groupByParameter.setGroupBy(Lists.newArrayList(PricePolicyConstants.PRODUCT_ID));

        producSet = queryObjProduct(user, SFAPreDefine.SalesOrder.getApiName(),SFAPreDefine.SalesOrderProduct.getApiName(), productIds, filters, groupByParameter);
        producSet.addAll(queryObjProduct(user,SFAPreDefine.Quote.getApiName(), SFAPreDefine.QuoteLines.getApiName(), productIds, filters, groupByParameter));

        return producSet;
    }

    private static Set<String> queryObjProduct(User user, String apiName, String detailApiName, List<String> productIds,
                                               List<IFilter> filters, IGroupByParameter groupByParameter) {
        Set<String> producSet=Sets.newHashSet();
        if(bizConfigThreadLocalCacheService.isOpenPricePolicy(user.getTenantId(),apiName)){
            SearchTemplateQuery query = CommonSearchUtil.getSearchTemplateQuery(filters, productIds.size());
            query.setNeedReturnQuote(false);
            query.setGroupByParameter(groupByParameter);
            List<IObjectData> orderData = serviceFacade.aggregateFindBySearchQuery(user, query,
                    detailApiName);
            if(CollectionUtils.notEmpty(orderData)){
                producSet=
                        orderData.stream()
                                .filter(o -> o.get("groupbycount", Long.class) > 0)
                                .map(o -> o.get(PricePolicyConstants.PRODUCT_ID, String.class))
                                .collect(Collectors.toSet());}
        }
        return producSet;
    }
}
