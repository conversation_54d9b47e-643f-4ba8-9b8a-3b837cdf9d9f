package com.facishare.crm.sfa.predefine.action

import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.utilities.util.AccountAddrUtil
import com.facishare.crm.sfa.utilities.util.AccountUtil
import com.facishare.paas.appframework.core.model.ActionContext
import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.facishare.paas.appframework.core.model.PreDefineAction
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.metadata.impl.ObjectData
import com.google.common.collect.Lists
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.api.support.membermodification.MemberMatcher
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyBoolean
import static org.mockito.ArgumentMatchers.anyList
import static org.mockito.ArgumentMatchers.anyString

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([AccountUtil,AccountAddrUtil])
@SuppressStaticInitializationFor([
        "com.facishare.paas.appframework.core.model.PreDefineAction",
        "com.facishare.crm.sfa.utilities.util.AccountAddrUtil",
        "com.facishare.crm.sfa.utilities.util.AccountUtil"
])
class CampaignMembersChangeStatusActionTest extends EnhancedBaseGroovyTest {

    @Shared
    CampaignMembersChangeStatusAction campaignMembersChangeStatusAction
    @Shared
    ServiceFacade serviceFacade
    @Shared
    ActionContext actionContext

    def setupSpec() {
        removeConfigFactory()
        removeI18N()
        campaignMembersChangeStatusAction = PowerMockito.spy(new CampaignMembersChangeStatusAction())
        serviceFacade = PowerMockito.mock(ServiceFacade)
        actionContext = createActionContext()
        actionContext.objectApiName >> "apiName"
        Whitebox.setInternalState(campaignMembersChangeStatusAction, "actionContext", actionContext)
        Whitebox.setInternalState(campaignMembersChangeStatusAction, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(campaignMembersChangeStatusAction, "dataList", Lists.newArrayList(new ObjectData()))
    }

    def "test doAct method"() {
        PowerMockito.mockStatic(AccountUtil)
        PowerMockito.mockStatic(AccountAddrUtil)
        PowerMockito.doReturn("other").when(AccountUtil, "getStringValue", any(ObjectDataDocument) as ObjectDataDocument, anyString(), anyString())
        PowerMockito.doReturn(Lists.newArrayList()).when(AccountAddrUtil, "batchUpdateIgnoreOther", any(), anyList(), anyList(), anyBoolean(), anyBoolean())

        CampaignMembersChangeStatusAction.Arg arg = new CampaignMembersChangeStatusAction.Arg()
        arg.setArgs(new ObjectDataDocument())
        when:
        campaignMembersChangeStatusAction.doAct(arg)
        then:
        noExceptionThrown()
    }

    def "test getFuncPrivilegeCodes method"() {
        when:
        campaignMembersChangeStatusAction.getFuncPrivilegeCodes()
        then:
        noExceptionThrown()
    }

    def "test getDataPrivilegeIds method"() {
        given:
        CampaignMembersChangeStatusAction.Arg arg = new CampaignMembersChangeStatusAction.Arg()
        arg.setObjectDataId("id")
        when:
        campaignMembersChangeStatusAction.getDataPrivilegeIds(arg)
        then:
        noExceptionThrown()
    }

    def "test before method"() {
        given:
        CampaignMembersChangeStatusAction.Arg arg = new CampaignMembersChangeStatusAction.Arg()
        PowerMockito.suppress(MemberMatcher.methods(PreDefineAction.class, "before"))
        when:
        campaignMembersChangeStatusAction.before(arg)
        then:
        noExceptionThrown()
    }

    def "test after method"() {
        given:
        CampaignMembersChangeStatusAction.Arg arg = new CampaignMembersChangeStatusAction.Arg()
        CampaignMembersChangeStatusAction.Result result = new CampaignMembersChangeStatusAction.Result()
        PowerMockito.suppress(MemberMatcher.methods(PreDefineAction.class, "after"))
        when:
        campaignMembersChangeStatusAction.after(arg, result)
        then:
        noExceptionThrown()
    }

    def "test getButtonApiName method"() {
        when:
        campaignMembersChangeStatusAction.getButtonApiName()
        then:
        noExceptionThrown()
    }

    def "test getPreObjectData method"() {
        given:
        CampaignMembersChangeStatusAction.Arg arg = new CampaignMembersChangeStatusAction.Arg()
        Whitebox.setInternalState(campaignMembersChangeStatusAction, "arg", arg)
        arg.setObjectDataId("id")
        when:
        campaignMembersChangeStatusAction.getPreObjectData()
        then:
        noExceptionThrown()
    }

    def "test getPostObjectData method"() {
        given:
        campaignMembersChangeStatusAction = PowerMockito.spy(new CampaignMembersChangeStatusAction())
        Whitebox.setInternalState(campaignMembersChangeStatusAction, "actionContext", actionContext)
        Whitebox.setInternalState(campaignMembersChangeStatusAction, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(campaignMembersChangeStatusAction, "dataList", Lists.newArrayList(new ObjectData()))
        CampaignMembersChangeStatusAction.Arg arg = new CampaignMembersChangeStatusAction.Arg()
        arg.setObjectDataId("id")
        Whitebox.setInternalState(campaignMembersChangeStatusAction, "arg", arg)
        when:
        campaignMembersChangeStatusAction.getPostObjectData()
        then:
        noExceptionThrown()
    }

    def "test getArgs method"() {
        given:
        CampaignMembersChangeStatusAction.Arg arg = new CampaignMembersChangeStatusAction.Arg()
        arg.setArgs(args)
        Whitebox.setInternalState(campaignMembersChangeStatusAction, "arg", arg)
        when:
        campaignMembersChangeStatusAction.getArgs()
        then:
        noExceptionThrown()
        where:
        args                     | _
        null                     | _
        new ObjectDataDocument() | _
    }

}
