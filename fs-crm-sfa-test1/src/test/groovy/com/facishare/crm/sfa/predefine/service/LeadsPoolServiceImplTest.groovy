package com.facishare.crm.sfa.predefine.service

import com.facishare.crm.describebuilder.SelectOneFieldDescribeBuilder
import com.facishare.crm.describebuilder.SelectOptionBuilder
import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.model.SFAObjectPoolCommon
import com.facishare.crm.sfa.predefine.exception.SFABusinessException
import com.facishare.crm.sfa.predefine.service.managegroup.ManageGroupQueryModel
import com.facishare.crm.sfa.predefine.service.model.GetLeadsPoolAdminResult
import com.facishare.crm.sfa.predefine.service.model.GetLeadsPoolListResult
import com.facishare.crm.sfa.predefine.service.model.SyncLeadsPoolOption
import com.facishare.crm.sfa.utilities.constant.LeadsConstants
import com.facishare.crm.sfa.utilities.util.AccountUtil
import com.facishare.crm.sfa.utilities.util.LeadsUtils
import com.facishare.paas.appframework.common.util.AppIdMapping
import com.facishare.paas.appframework.common.util.CollectionUtils
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.controller.BaseListController
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.ISelectOption
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.service.IObjectDescribeService
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.google.common.collect.BiMap
import com.google.common.collect.HashBiMap
import com.google.common.collect.Lists
import org.apache.commons.lang3.StringUtils
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.api.support.membermodification.MemberMatcher
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.Logger
import org.spockframework.runtime.Sputnik
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.*

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([CollectionUtils.class, StringUtils.class, LeadsUtils.class, AccountUtil.class, AppIdMapping.class])
@SuppressStaticInitializationFor([
        "com.facishare.paas.appframework.core.util.RequestUtil",
        "com.facishare.crm.sfa.utilities.util.AccountUtil"
])
class LeadsPoolServiceImplTest extends EnhancedBaseGroovyTest {


    @Shared
    String tenantId = "79337"
    @Shared
    ServiceContext serviceContext
    @Shared
    def userId = "1000"
    @Shared
    def user



    def setupSpec() {
        removeConfigFactory()
        removeI18N()
        Whitebox.setInternalState(LeadsPoolServiceImpl, "log", Mock(Logger))
        initSpringContext()
        user = PowerMockito.mock(User)
        PowerMockito.doReturn("1000").when(user).getUserId()
        PowerMockito.doReturn("0").when(user).getTenantId()
        PowerMockito.doReturn("1000").when(user).getUpstreamOwnerIdOrUserId()
        serviceContext = createServiceContext([tenantId: tenantId,user:user])
    }


    def "choose"() {
        given:
        SFAObjectPoolCommon.Result expected = SFAObjectPoolCommon.Result.builder().successList(objectIds).build()
        LeadsPoolServiceImpl leadsPoolService = PowerMockito.spy(new LeadsPoolServiceImpl())
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        def teamMemberService = PowerMockito.mock(TeamMemberService)
//        IObjectData objectPoolData = Mock(IObjectData)

        PowerMockito.mockStatic(StringUtils.class)
        PowerMockito.mockStatic(AccountUtil.class)
        PowerMockito.mockStatic(LeadsUtils.class)
        Whitebox.setInternalState(leadsPoolService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(leadsPoolService, "teamMemberService", teamMemberService)
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(BaseObjectPoolService.class))
//        PowerMockito.when(leadsPoolService.getObjectPoolById(any(), any())).thenReturn(objectPoolData)
//        PowerMockito.when(AccountUtil.getBooleanValue(objectPoolData, "is_recycling_team_member", false)).thenReturn(cleanTeamMember)
        PowerMockito.when(LeadsUtils.getUserMainDepartId(anyString(), anyString())).thenReturn(ownerDeptId)
        PowerMockito.when(serviceFacade.findObjectDataByIds(anyString(), eq(objectIds), anyString())).thenReturn(objectDataList)
        PowerMockito.doNothing().when(teamMemberService, "removeObjectAllInnerTeamMember", any(), any(), any(), any())
        PowerMockito.doNothing().when(teamMemberService, "changeOwner", any(), any(), any(), any(), any())
        PowerMockito.doNothing().when(teamMemberService, "changeInnerOwner", any(), any(), any())
        PowerMockito.doNothing().when(leadsPoolService, "saveLeadsData", any(), any(), any(), any())

        when:
        // 调用测试方法
        leadsPoolService.choose(user, objectPoolId, objectIds, eventId, partnerId)

        then:
        noExceptionThrown()

        where:
        objectIds      | cleanTeamMember | ownerDeptId | eventId | objectDataList                              | objectPoolId | partnerId
        []             | false           | ""          | ""      | getObjectDataList(["account_id", "_id"], 3) | "pool_1"     | null
        ["id1", "id2"] | true            | "dep_123"   | "e123"  | getObjectDataList(["account_id", "_id"], 3) | "pool_1"     | "partnerId"
        ["id3"]        | false           | "dep_456"   | "e456"  | getObjectDataList(["account_id", "_id"], 3) | "pool_2"     | null
    }


    def "saveLeadsData"() {
        given:
        LeadsPoolServiceImpl leadsPoolService = PowerMockito.spy(new LeadsPoolServiceImpl())
        List<String> editExceptFields = Lists.newArrayList("is_duplicated", "is_collected", "collected_to", "refresh_duplicated_version");
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(leadsPoolService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(LeadsPoolServiceImpl.class, "editExceptFields", editExceptFields)
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(BaseObjectPoolService.class))
//        if (e == 1) {
//            PowerMockito.doThrow(new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR)).when(serviceFacade, "batchUpdateByFields", eq(user), any(), eq(updateFieldList))
//        } else {
        PowerMockito.doReturn(null).when(serviceFacade, "batchUpdateByFields", eq(user), any(), any(List))
//        }

        when:
        leadsPoolService.saveLeadsData(user, objectDataList, updateFieldList, eventId)
        throw new OK()

        then:
        thrown(exception2)

        where:
        user             | objectDataList                              | updateFieldList | eventId   | e | exception2
        new User("", "") | getObjectDataList(["account_id", "_id"], 3) | ["field1"]      | "eventId" | 1 | OK
        new User("", "") | getObjectDataList(["account_id", "_id"], 3) | ["field1"]      | "eventId" | 0 | OK
    }

    def "move objects to new pool"() {
        given:
        SFAObjectPoolCommon.Result expected = SFAObjectPoolCommon.Result.builder().successList(objectIds).build()
        TeamMemberService teamMemberService = PowerMockito.mock(TeamMemberService.class)
        LeadsPoolServiceImpl leadsPoolService = PowerMockito.spy(new LeadsPoolServiceImpl())
        PowerMockito.mockStatic(AccountUtil.class)
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(leadsPoolService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(leadsPoolService, "teamMemberService", teamMemberService)
        PowerMockito.when(serviceFacade.findObjectDataByIds(anyString(), eq(objectIds), anyString())).thenReturn(objectDataList)
        PowerMockito.when(leadsPoolService.getObjectPoolById(any(), any())).thenReturn(objectData)
        PowerMockito.doNothing().when(teamMemberService, "removeObjectAllInnerTeamMember", any(), any(), any(), any())
        PowerMockito.doNothing().when(teamMemberService, "removeObjectInnerOwner", any(), any())
        PowerMockito.when(teamMemberService.removeObjectPartnerByOutOwnerNotUpdate(any(), anyList())).thenReturn(update)
        PowerMockito.when(AccountUtil.getBooleanValue(eq(objectData), eq("is_clean_owner"), eq(false))).thenReturn(cleanOwner)
        PowerMockito.when(AccountUtil.getBooleanValue(eq(objectData), eq("is_recycling_team_member"), eq(false))).thenReturn(cleanTeamMember)
        PowerMockito.when(AccountUtil.getStringValue(eq(objectData), eq(LeadsConstants.Field.BIZ_STATUS.getApiName()), anyString())).thenReturn(bizStatus)
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(BaseObjectPoolService.class))
        PowerMockito.doNothing().when(leadsPoolService, "saveLeadsData", any(), anyList(), anyList(), eq(eventId))

        when:
        // 调用测试方法
        SFAObjectPoolCommon.Result result = leadsPoolService.move(user, objectPoolId, objectIds, eventId)

        then:
        result == expected
        result.successList.size() == objectIds.size()

        where:
        objectIds | emptyRule | cleanOwner | cleanTeamMember | bizStatus     | update | objectDataList                              | objectPoolId | eventId | objectData
        ["id1"]   | null      | false      | false           | "transformed" | false  | getObjectDataList(["account_id", "_id"], 3) | ""           | ""      | getObjectData(["_id"], ["dasd127635"])
        ["id2"]   | null      | true       | true            | "processed"   | true   | getObjectDataList(["account_id", "_id"], 3) | "xx"         | "xx"    | getObjectData(["_id"], ["dasd127635"])
        ["id3"]   | null      | true       | false           | "transformed" | true   | getObjectDataList(["account_id", "_id"], 3) | "xx"         | "xxx"   | getObjectData(["_id"], ["dasd127635"])
    }

    def "back"() {
        given:
        SFAObjectPoolCommon.Result expected = SFAObjectPoolCommon.Result.builder().successList(objectIds).build()
        TeamMemberService teamMemberService = PowerMockito.mock(TeamMemberService.class)
        LeadsPoolServiceImpl leadsPoolService = PowerMockito.spy(new LeadsPoolServiceImpl())
        PowerMockito.mockStatic(AccountUtil.class)
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(leadsPoolService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(leadsPoolService, "teamMemberService", teamMemberService)
        PowerMockito.when(serviceFacade.findObjectDataByIds(anyString(), eq(objectIds), anyString())).thenReturn(objectDataList)
        PowerMockito.when(leadsPoolService.getObjectPoolById(any(), any())).thenReturn(objectPoolData)
        PowerMockito.doNothing().when(teamMemberService, "removeObjectAllInnerTeamMember", any(), any(), any(), any())
        PowerMockito.doNothing().when(teamMemberService, "removeObjectInnerOwner", any(), any())
        PowerMockito.when(teamMemberService.removeObjectPartnerByOutOwnerNotUpdate(any(), anyList())).thenReturn(update)
        PowerMockito.when(AccountUtil.getBooleanValue(eq(objectData), eq("is_clean_owner"), eq(false))).thenReturn(cleanOwner)
        PowerMockito.when(AccountUtil.getBooleanValue(eq(objectData), eq("is_recycling_team_member"), eq(false))).thenReturn(cleanTeamMember)
        PowerMockito.when(AccountUtil.getStringValue(eq(objectData), eq(LeadsConstants.Field.BIZ_STATUS.getApiName()), anyString())).thenReturn(bizStatus)
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(BaseObjectPoolService.class))
        PowerMockito.doNothing().when(leadsPoolService, "saveLeadsData", any(), anyList(), anyList(), eq(eventId))

        when:
        // 调用测试方法
        SFAObjectPoolCommon.Result result = leadsPoolService.back(user, objectPoolId, objectIds, operationType, backReason, backReasonOther, eventId, isPrmOpen)

        then:
        result == expected
        result.successList.size() == objectIds.size()

        where:
        //operationType, backReason, backReasonOther, eventId, isPrmOpen
        objectIds | operationType | backReason | backReasonOther | isPrmOpen | cleanOwner | cleanTeamMember | bizStatus     | update | objectDataList                              | objectPoolId | eventId | objectData | objectPoolData
        ["id1"]   | 1             | "reason"   | "other"         | true      | false      | false           | "transformed" | false  | getObjectDataList(["account_id", "_id"], 3) | "xx"           | ""      | getObjectData(["_id"], ["dasd127635"]) | getObjectData(["_id"], ["xx"])
        ["id2"]   | 2             | "reason"   | "other"         | false     | true       | true            | "processed"   | true   | getObjectDataList(["account_id", "_id"], 3) | "xx"         | "xx"    | getObjectData(["_id"], ["dasd127635"]) | getObjectData(["_id"], ["xx"])
        ["id3"]   | 3             | "reason"   | "other"         | true      | true       | false           | "transformed" | true   | getObjectDataList(["account_id", "_id"], 3) | "xx"         | "xxx"   | getObjectData(["_id"], ["dasd127635"]) |getObjectData(["_id"], ["xx"])
    }


    def "takeBack"() {
        given:
        SFAObjectPoolCommon.Result expected = SFAObjectPoolCommon.Result.builder().successList(objectIds).build()
        TeamMemberService teamMemberService = PowerMockito.mock(TeamMemberService.class)
        LeadsPoolServiceImpl leadsPoolService = PowerMockito.spy(new LeadsPoolServiceImpl())
        PowerMockito.mockStatic(AccountUtil.class)
        def serviceFacade = PowerMockito.mock(ServiceFacade.class)
        Whitebox.setInternalState(leadsPoolService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(leadsPoolService, "teamMemberService", teamMemberService)
        PowerMockito.when(serviceFacade.findObjectDataByIds(anyString(), eq(objectIds), anyString())).thenReturn(objectDataList)
        PowerMockito.when(leadsPoolService.getObjectPoolById(any(), any())).thenReturn(objectPoolData)
        PowerMockito.doNothing().when(teamMemberService, "removeObjectAllInnerTeamMember", any(), any(), any(), any())
        PowerMockito.doNothing().when(teamMemberService, "removeObjectInnerOwner", any(), any())
        PowerMockito.when(teamMemberService.removeObjectPartnerByOutOwnerNotUpdate(any(), anyList())).thenReturn(update)
        PowerMockito.when(AccountUtil.getBooleanValue(eq(objectData), eq("is_clean_owner"), eq(false))).thenReturn(cleanOwner)
        PowerMockito.when(AccountUtil.getBooleanValue(eq(objectData), eq("is_recycling_team_member"), eq(false))).thenReturn(cleanTeamMember)
        PowerMockito.when(AccountUtil.getStringValue(eq(objectData), eq(LeadsConstants.Field.BIZ_STATUS.getApiName()), anyString())).thenReturn(bizStatus)
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(BaseObjectPoolService.class))
        PowerMockito.doNothing().when(leadsPoolService, "saveLeadsData", any(), anyList(), anyList(), eq(eventId))

        when:
        // 调用测试方法
        SFAObjectPoolCommon.Result result = leadsPoolService.takeBack(user, objectPoolId, objectIds, eventId, isPrmOpen)

        then:
        result == expected
        result.successList.size() == objectIds.size()

        where:
        objectIds | emptyRule | cleanOwner | cleanTeamMember | bizStatus     | update | objectDataList                              | objectPoolId | eventId | objectData                             | isPrmOpen | objectPoolData
        ["id1"]   | null      | false      | false           | "transformed" | false  | getObjectDataList(["account_id", "_id"], 3) | "xx"         | ""      | getObjectData(["_id"], ["dasd127635"]) | true      | getObjectData(["_id"], ["xx"])
        ["id2"]   | null      | true       | true            | "processed"   | true   | getObjectDataList(["account_id", "_id"], 3) | "xx"         | "xx"    | getObjectData(["_id"], ["dasd127635"]) | false     | getObjectData(["_id"], ["xx"])
        ["id3"]   | null      | true       | false           | "transformed" | true   | getObjectDataList(["account_id", "_id"], 3) | "xx"         | "xxx"   | getObjectData(["_id"], ["dasd127635"]) | true      | getObjectData(["_id"], ["xx"])
    }

    def "allocate"() {
        given:
        SFAObjectPoolCommon.Result expected = SFAObjectPoolCommon.Result.builder().successList(objectIds).build()
        TeamMemberService teamMemberService = PowerMockito.mock(TeamMemberService.class)
        LeadsPoolServiceImpl leadsPoolService = PowerMockito.spy(new LeadsPoolServiceImpl())
        PowerMockito.mockStatic(AccountUtil.class)
        def objectPoolData = getObjectData(["is_recycling_team_member"], [cleanTeamMemberStr])
        def serviceFacade = PowerMockito.mock(ServiceFacade.class)
        Whitebox.setInternalState(leadsPoolService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(leadsPoolService, "teamMemberService", teamMemberService)
        PowerMockito.when(serviceFacade.findObjectDataByIds(anyString(), eq(objectIds), anyString())).thenReturn(objectDataList)
        PowerMockito.when(leadsPoolService.getObjectPoolById(any(), any())).thenReturn(objectPoolData)
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(BaseObjectPoolService.class))
        PowerMockito.doNothing().when(leadsPoolService, "saveLeadsData", any(), anyList(), anyList(), eq(eventId))
        PowerMockito.doNothing().when(teamMemberService, "removeObjectAllInnerTeamMember", any(), anyList())
        PowerMockito.doNothing().when(teamMemberService, "changeOwner", any(), anyString(), anyList(), anyString(), anyString())
        PowerMockito.doNothing().when(teamMemberService, "changeInnerOwner", any(), anyString(), anyList())

        when:
        // 调用测试方法
        leadsPoolService.allocate(user, objectPoolId, objectIds, ownerId, eventId, outTenantId, outOwnerId, partnerId)

        then:
        noExceptionThrown()

        where:
        objectIds | objectDataList                            | objectPoolId | eventId | cleanTeamMemberStr | ownerId   | outTenantId | outOwnerId | partnerId
        ["id1"]   | [getObjectData(["resale_count"], [2])]    | ""           | ""      | "true"             | "ownerId" | null        | null       | null
        ["id2"]   | [getObjectData(["resale_count"], [1])]    | "xx"         | "xx"    | "true"             | "ownerId" | 12345L      | 67890L     | "partnerId"
        ["id3"]   | [getObjectData(["resale_count"], [null])] | "xx"         | "xxx"   | "false"            | "ownerId" | 98765L      | 43210L     | "partnerId"
    }


    def "syncLeadsPoolOption"() {
        given:
        SyncLeadsPoolOption.Result expected = new SyncLeadsPoolOption.Result();
        SyncLeadsPoolOption.Arg arg = new SyncLeadsPoolOption.Arg();
        List<SyncLeadsPoolOption.LeadsOptionOption> options = Lists.newArrayList();
        for (i in 0..<5) {
            SyncLeadsPoolOption.LeadsOptionOption option = new SyncLeadsPoolOption.LeadsOptionOption();
            option.setValue("value" + i);
            option.setLabel("label" + i);
            options.add(option)
        }
        arg.setOptions(options)
        Map originalSelectOption = ["value": "value1", "label": "label1"]
        List<ISelectOption> originalSelectOptions = originalSelectOption.collect { key, value ->
            SelectOptionBuilder.builder().value(key).label(value).build()
        }
        LeadsPoolServiceImpl leadsPoolService = PowerMockito.spy(new LeadsPoolServiceImpl())
        IObjectDescribe objectDescribe = new ObjectDescribe();
        SelectOneFieldDescribe selectOneFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName("leads_pool_id")
                .label("线索池id").selectOptions(originalSelectOptions).build();
        objectDescribe.setFieldDescribes(Lists.newArrayList(selectOneFieldDescribe))

        def serviceFacade = PowerMockito.mock(ServiceFacade.class)
        def objectDescribeService = PowerMockito.mock(IObjectDescribeService)
//        private DescribeWithSimplifiedChineseService describeWithSimplifiedChineseService;
        def describeWithSimplifiedChineseService = PowerMockito.mock(DescribeWithSimplifiedChineseService.class)
        Whitebox.setInternalState(leadsPoolService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(leadsPoolService, "objectDescribeService", objectDescribeService)
        Whitebox.setInternalState(leadsPoolService, "describeWithSimplifiedChineseService", describeWithSimplifiedChineseService)
        PowerMockito.when(serviceFacade.findObject(anyString(), anyString())).thenReturn(objectDescribe)
        PowerMockito.when(describeWithSimplifiedChineseService.findByDescribeApiName(any(), anyString())).thenReturn(objectDescribe)
        PowerMockito.when(objectDescribeService, "updateFieldDescribe", any(), anyList(), any()).thenReturn(null)

        when:
        // 调用测试方法
        SyncLeadsPoolOption.Result result = leadsPoolService.syncLeadsPoolOption(serviceContext, arg)

        then:
        noExceptionThrown()
    }


    def "getTargetObjectPoolList"() {

        given:

        def poolList = new ArrayList<ObjectData>();
        def queryResult = new ArrayList<Map<String, Object>>();
        for (i in 0..<5) {
            def objectData = new ObjectData();
            objectData.set("only_allow_member_move", only_allow_member_move)
            objectData.set("only_allow_member_return", only_allow_member_return)
            objectData.set("_id", i)
            objectData.setTenantId(tenantId)
            poolList.add(objectData)

            if (!queryResultNone) {
                def map = new HashMap<String, Object>();
                if (inClude) {
                    map.put("pool_id", i)
                } else {
                    map.put("pool_id", i + 1)
                }

                map.put("is_admin", isAdmin)
                queryResult.add(map)
            } else {
                queryResult = null
            }
        }


        def localUser = Spy(user)
        localUser.getUpstreamOwnerIdOrUserId() >> userId

        PowerMockito.mockStatic(LeadsUtils.class)
        PowerMockito.mockStatic(AccountUtil.class)
        LeadsPoolServiceImpl leadsPoolService = PowerMockito.spy(new LeadsPoolServiceImpl())
        PowerMockito.when(LeadsUtils.isCrmAdmin(localUser)).thenReturn(isAdmin)
        PowerMockito.doReturn(poolList).when(leadsPoolService).getAllObjectPools(any(User.class))
//        PowerMockito.when(leadsPoolService.getObjectPoolPermissionByUser(any(), any(), any(), any())).thenReturn(queryResult)
        PowerMockito.doReturn(queryResult).when(leadsPoolService, "getObjectPoolPermissionByUser", any(), any(), any(), any())
        PowerMockito.when(AccountUtil.getAllowMemberMove(any())).thenReturn(only_allow_member_move)
        PowerMockito.when(AccountUtil.getAllowMemberReturn(any())).thenReturn(only_allow_member_return)


        when:
        leadsPoolService.getTargetObjectPoolList(tenantId, localUser, action)
        throw new OK()

        then:
        thrown(exception2)

        where:
        action   | only_allow_member_move | inClude | isAdmin | only_allow_member_return | queryResultNone | exception2
        "move"   | false                  | true    | true    | true                     | true            | OK
        "move"   | false                  | true    | true    | true                     | false           | OK
        "move"   | false                  | true    | false   | true                     | false           | OK
        "move"   | false                  | false   | false   | true                     | false           | OK
        "move"   | true                   | false   | false   | false                    | false           | OK
        "move"   | true                   | true    | false   | false                    | false           | OK
        "move"   | true                   | true    | false   | false                    | false           | OK
        "move"   | true                   | true    | false   | false                    | false           | OK
        "add"    | true                   | true    | false   | false                    | false           | SFABusinessException
        "add"    | true                   | true    | false   | false                    | false           | SFABusinessException
        "add"    | true                   | true    | false   | false                    | false           | SFABusinessException
        "add"    | true                   | true    | false   | false                    | false           | SFABusinessException
        "return" | false                  | true    | true    | true                     | true            | OK
        "return" | false                  | true    | true    | true                     | false           | OK
        "return" | false                  | true    | false   | true                     | false           | OK
        "return" | false                  | true    | false   | true                     | false           | OK
        "return" | true                   | true    | false   | false                    | false           | OK
        "return" | true                   | true    | false   | false                    | false           | OK
        "return" | true                   | true    | false   | false                    | false           | OK
        "return" | true                   | false   | false   | false                    | false           | OK
    }


    def "getLeadsPoolListByEmployeeId"() {
        given:
        GetLeadsPoolListResult.Result expected = GetLeadsPoolListResult.Result.builder().value(Lists.newArrayList()).build()

        // 不直接创建User对象，而是使用PowerMock mock
        User mockUser = PowerMockito.mock(User.class)
        PowerMockito.doReturn(tenantId).when(mockUser).getTenantId()
        PowerMockito.doReturn("1000").when(mockUser).getUserId()
        PowerMockito.doReturn(outUserId).when(mockUser).getOutUserId()
        PowerMockito.doReturn("111").when(mockUser).getUpstreamOwnerIdOrUserId()

        RequestContext requestContext = PowerMockito.mock(RequestContext.class)
        PowerMockito.doReturn(tenantId).when(requestContext).getTenantId()
        PowerMockito.doReturn(mockUser).when(requestContext).getUser()
        PowerMockito.doReturn(appId).when(requestContext).getAppId()

        ServiceContext context = new ServiceContext(requestContext, "", "")

        def poolList = new ArrayList<ObjectData>();
        for (i in 0..<5) {
            def objectData = new ObjectData();
            objectData.set("overtime_hours", 1)
            objectData.set("leads_count", 1)
            objectData.set("assigner_id", 1)
            objectData.set("limit_count", 1)
            objectData.set("is_visible_to_member", true)
            objectData.set("is_choose_to_notify", true)
            objectData.set("_id", i)
            objectData.setTenantId(tenantId)
            poolList.add(objectData)
        }

        GetLeadsPoolListResult.Arg arg = new GetLeadsPoolListResult.Arg()
        arg.setEmployeeId(1)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(LeadsUtils.class)
        PowerMockito.mockStatic(AccountUtil.class)
        BiMap<String, String> biMap = HashBiMap.create();
        biMap.put("prm", appId);
        Whitebox.setInternalState(AppIdMapping, "appIdMapping", biMap)
        LeadsPoolServiceImpl leadsPoolService = PowerMockito.spy(new LeadsPoolServiceImpl())
        PowerMockito.doReturn(poolList).when(leadsPoolService).getAllObjectPoolList(any(), any(), any())

        when:
        // 调用测试方法
        GetLeadsPoolListResult.Result result = leadsPoolService.getLeadsPoolListByEmployeeId(context, arg)

        then:
        result.getValue().size() > 0

        where:
        partnerId   | outUserId   | appId
        "partnerId" | null        | "prm"
        null        | "outUserId" | "prm"
        null        | null        | "prm"
    }


    def "checkUserAuthForEnterpriseRelationAccess"() {
        given:
        LeadsPoolServiceImpl leadsPoolService = PowerMockito.spy(new LeadsPoolServiceImpl())
        GetLeadsPoolListResult.CheckAuthorityResult expected = GetLeadsPoolListResult.CheckAuthorityResult.builder()
                .data(GetLeadsPoolListResult.AccessLevelResult.builder().accessLevel(1).build())
                .errCode(0).errMsg("success").build()
        when:
        // 调用测试方法
        GetLeadsPoolListResult.CheckAuthorityResult result = leadsPoolService.checkUserAuthForEnterpriseRelationAccess(serviceContext)

        then:
        result == expected
        result.getData().getAccessLevel() == 1
    }


    def "manageGroupQuery"() {
        given:
        LeadsPoolServiceImpl leadsPoolService = PowerMockito.spy(new LeadsPoolServiceImpl())
        ManageGroupQueryModel.Result expected = ManageGroupQueryModel.Result.builder().data(new ManageGroupQueryModel.ListResult(Lists.newArrayList())).build()
        ServiceFacade serviceFacade = PowerMockito.mock(ServiceFacade.class)
        Whitebox.setInternalState(leadsPoolService, "serviceFacade", serviceFacade)
        ManageGroupQueryModel.Arg arg = new ManageGroupQueryModel.Arg()
        List<ManageGroupQueryModel.ManageInfo> manageInfos = Lists.newArrayList()
        for (i in 0..<2) {
            ManageGroupQueryModel.ManageInfo manageInfo = new ManageGroupQueryModel.ManageInfo()
            manageInfo.setApiName("LeadsPoolObj")
            manageInfos.add(manageInfo)
        }
        arg.setManageInfos(manageInfos)

        // 创建RequestContext和ServiceContext
        RequestContext requestContext = PowerMockito.mock(RequestContext.class)
        PowerMockito.doReturn(tenantId).when(requestContext).getTenantId()

        ServiceContext context = new ServiceContext(requestContext, "", "")

        PowerMockito.doReturn(new BaseListController.Result()).when(serviceFacade).triggerController(any(), any(), any())

        when:
        // 调用测试方法
        ManageGroupQueryModel.Result result = leadsPoolService.manageGroupQuery(context, arg)

        then:
        result != null
    }


    def "getLeadsPoolAdmins"() {
        given:
        LeadsPoolServiceImpl leadsPoolService = PowerMockito.spy(new LeadsPoolServiceImpl())
        def map = new HashMap<String, List<String>>()
        GetLeadsPoolAdminResult.Result expected = GetLeadsPoolAdminResult.Result.builder().value(map).build()

        // 使用PowerMock创建User对象
        User mockUser = PowerMockito.mock(User.class)
        PowerMockito.doReturn(tenantId).when(mockUser).getTenantId()
        PowerMockito.doReturn("1000").when(mockUser).getUserId()
        PowerMockito.doReturn("1000").when(mockUser).getUpstreamOwnerIdOrUserId()

        // 创建RequestContext和ServiceContext
        RequestContext requestContext = PowerMockito.mock(RequestContext.class)
        PowerMockito.doReturn(tenantId).when(requestContext).getTenantId()
        PowerMockito.doReturn(mockUser).when(requestContext).getUser()

        ServiceContext context = new ServiceContext(requestContext, "", "")

        GetLeadsPoolAdminResult.Arg arg = new GetLeadsPoolAdminResult.Arg()
        arg.setLeadsPoolIdList(poolList)
        PowerMockito.doReturn(map).when(leadsPoolService).getPoolAdminByIds(any(User.class), eq(poolList))

        when:
        // 调用测试方法
        leadsPoolService.getLeadsPoolAdmins(context, arg)
        throw new OK()

        then:
        thrown(exception2)

        where:
        poolList   | exception2
        []         | ValidateException
        ["1", "2"] | OK
    }

    def "getAllObjectPools"() {
        given:
        LeadsPoolServiceImpl leadsPoolService = PowerMockito.spy(new LeadsPoolServiceImpl())
        ServiceFacade serviceFacade = PowerMockito.mock(ServiceFacade.class)
        Whitebox.setInternalState(leadsPoolService, "serviceFacade", serviceFacade)

        // 使用PowerMock正确创建User对象
        def mockUser = PowerMockito.mock(User.class)
        PowerMockito.doReturn(tenantId).when(mockUser).getTenantId()
        PowerMockito.doReturn("1000").when(mockUser).getUserId()
        PowerMockito.doReturn("1000").when(mockUser).getUpstreamOwnerIdOrUserId()

        def poolList = new ArrayList<ObjectData>();
        for (i in 0..<5) {
            def objectData = new ObjectData();
            objectData.set("_id", "pool_" + i)
            objectData.set("name", "线索池" + i)
            objectData.setTenantId(tenantId)
            poolList.add(objectData)
        }

        // 创建QueryResult对象并mock findBySearchQuery方法
        QueryResult<IObjectData> queryResult = new QueryResult<>()
        queryResult.setData(poolList)
        PowerMockito.doReturn(queryResult).when(serviceFacade).findBySearchQuery(
            any() as User,
            anyString(),
            any() as SearchTemplateQuery
        )

        // 重写getObjectPoolApiName方法的返回值
        PowerMockito.doReturn("LeadsPoolObj").when(leadsPoolService).getObjectPoolApiName()

        when:
        List<ObjectData> result = leadsPoolService.getAllObjectPools(mockUser)

        then:
        result != null
        result.size() == 5
        result.each { pool ->
            assert pool.getTenantId() == tenantId
            assert pool.get("_id") != null
            assert pool.get("name") != null
        }
    }

    def "getAllObjectPoolList"() {
        given:
        LeadsPoolServiceImpl leadsPoolService = PowerMockito.spy(new LeadsPoolServiceImpl())
        List<ObjectData> poolList = new ArrayList<>()

        for (i in 0..<5) {
            def objectData = new ObjectData()
            objectData.set("_id", "pool_" + i)
            objectData.set("name", "线索池" + i)
            objectData.set("pool_type", "public")
            objectData.set("description", "描述" + i)
            objectData.set("create_time", ********** + i)
            objectData.set("create_user_id", "user_" + i)
            objectData.setTenantId(tenantId)
            poolList.add(objectData)
        }

        PowerMockito.doReturn(poolList).when(leadsPoolService).getAllObjectPools(any(User.class))
        PowerMockito.mockStatic(AccountUtil.class)
        PowerMockito.mockStatic(LeadsUtils.class)

        def mockUser = PowerMockito.mock(User.class)
        PowerMockito.doReturn(tenantId).when(mockUser).getTenantId()
        PowerMockito.doReturn("1000").when(mockUser).getUserId()
        PowerMockito.suppress(MemberMatcher.methods(BaseObjectPoolService.class, "getAllObjectPoolList"))


        when:
        leadsPoolService.getAllObjectPoolList(tenantId, userId, tenantId)

        then:
        noExceptionThrown()
    }

    def "getObjectPoolById"() {
        given:
        LeadsPoolServiceImpl leadsPoolService = PowerMockito.spy(new LeadsPoolServiceImpl())
        ServiceFacade serviceFacade = PowerMockito.mock(ServiceFacade.class)
        Whitebox.setInternalState(leadsPoolService, "serviceFacade", serviceFacade)

        // 使用PowerMock创建User对象
        def mockUser = PowerMockito.mock(User.class)
        PowerMockito.doReturn(tenantId).when(mockUser).getTenantId()
        PowerMockito.doReturn("1000").when(mockUser).getUserId()
        PowerMockito.doReturn("1000").when(mockUser).getUpstreamOwnerIdOrUserId()


        PowerMockito.when(serviceFacade.findObjectDataByIds(anyString(), anyList(), anyString())).thenReturn(objectDataList)

        when:
        IObjectData result = leadsPoolService.getObjectPoolById(tenantId, objectdataId)

        then:
        noExceptionThrown()

        where:
        objectdataId | objectDataList
        "11"   | getObjectDataList(["account_id", "_id"], 3)
        null   | null
        "1111"   | []
    }
}
