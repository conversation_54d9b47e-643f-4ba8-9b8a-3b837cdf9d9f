package com.facishare.crm.sfa.predefine.service

import com.facishare.crm.sfa.RemoveUseless
import com.facishare.crm.sfa.model.CategoryImportValidateModel
import com.facishare.crm.sfa.model.ProductCategoryTree
import com.facishare.crm.sfa.predefine.SFAPreDefineObject
import com.facishare.crm.sfa.predefine.action.ProductCategoryInsertImportDataAction
import com.facishare.crm.sfa.predefine.enums.CategoryFilterConditionEnum
import com.facishare.crm.sfa.predefine.enums.CategoryFilterEnum
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.predefine.service.model.CategoryObject
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject
import com.facishare.crm.sfa.predefine.service.treepath.impl.TreePathService
import com.facishare.crm.sfa.prm.api.enhancer.DescribeEnhancer
import com.facishare.crm.sfa.task.AsyncTaskProducer
import com.facishare.crm.sfa.utilities.util.CategoryRandomTimeUtil
import com.facishare.crm.sfa.utilities.util.ProductCategoryUtils
import com.facishare.crm.sfa.utilities.validator.ProductCategoryV2Validator
import com.facishare.crm.sfa.utilities.validator.ProductCategoryValidator
import com.facishare.paas.appframework.common.util.Tuple
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController
import com.facishare.paas.appframework.core.predef.controller.BaseListController
import com.facishare.paas.appframework.core.predef.controller.StandardSummaryFieldController
import com.facishare.paas.appframework.metadata.ProductCategoryService
import com.facishare.paas.metadata.api.DBRecord
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.ISelectOption
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.search.IFilter
import com.facishare.paas.metadata.api.search.Wheres
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe
import com.facishare.paas.metadata.impl.describe.SelectOption
import com.facishare.paas.metadata.impl.search.Filter
import com.facishare.paas.metadata.impl.search.Operator
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl
import com.facishare.paas.metadata.ui.layout.ILayout
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.apache.commons.collections4.CollectionUtils
import org.junit.runner.RunWith
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Shared

import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Filed.*
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Metadata.API_NAME
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject.*
/**
 * <AUTHOR>
 * @time 2024-04-07 10:31
 * @Description
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
class ProductCategoryBizServiceTest extends RemoveUseless {
    ProductCategoryBizService productCategoryBizServiceTest
    @Shared
    User user
    @Shared
    RequestContext requestContext
    @Shared
    ServiceContext serviceContext

    def setupSpec() {
        user = Mock(User) {
            getTenantId() >> "71568"
            getUserId() >> "-10000"
        }

        requestContext = Mock(RequestContext) {
            getUser() >> user
            getTenantId() >> "71568"
        }

        serviceContext = Mock(ServiceContext) {
            getRequestContext() >> requestContext
            getUser() >> user
            getTenantId() >> "71568"
        }
    }

    def setup() {
        productCategoryBizServiceTest = Spy(ProductCategoryBizService)
    }

    def "should return converted enum value of getCategoryFilterEnums method"() {
        when:
        productCategoryBizServiceTest.getCategoryFilterEnums(arg)
        then:
        notThrown(Exception)
        where:
        arg                                                                           || categoryFilterEnum
        null                                                                          || CategoryFilterEnum.ALL
        new ProductCategoryObject.ProductCategoryListArg("filterCategory": "Shop")    || CategoryFilterEnum.SHOP
        new ProductCategoryObject.ProductCategoryListArg("filterCategory": "ALL")     || CategoryFilterEnum.ALL
        new ProductCategoryObject.ProductCategoryListArg("filterCategory": "Product") || CategoryFilterEnum.PRODUCT
    }

    def "should throw validate exception of getCategoryFilterEnums method"() {
        given: "mock I18N"
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_, _) >> ""
        productCategoryBizServiceTest.setCategoryStaticUtilService(categoryStaticUtilService)
        when: "convert arg"
        productCategoryBizServiceTest.getCategoryFilterEnums(arg)
        then:
        thrown(ValidateException)
        where:
        arg                                                                                                                 || _
        new ProductCategoryObject.ProductCategoryListArg("filterCategory": null)                                            || _
        new ProductCategoryObject.ProductCategoryListArg("filterCategory": "ALL", "filterByShopCategory": Boolean.TRUE)     || _
        new ProductCategoryObject.ProductCategoryListArg("filterCategory": "Product", "filterByShopCategory": Boolean.TRUE) || _

    }

    def "should return extend field of getExtendFields method"() {
        given:

        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_ as String) >> closeOldCategory
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        and: "mock bizConfigThreadLocalCacheService#getBizConfig"
        def mockBizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService);
        mockBizConfigThreadLocalCacheService.getBizConfig(user.getTenantId(), "category_custom_display_fields") >> customDisplayFields
        productCategoryBizServiceTest.setBizConfigThreadLocalCacheService(mockBizConfigThreadLocalCacheService)

        when: "getExtendFields"
        productCategoryBizServiceTest.getExtendFields(user, Sets.newHashSet())

        then: "begin test getExtendFields"
        notThrown(ValidateException)

        where:
        closeOldCategory | customDisplayFields | rst
        true            || ""                  | []
        false           || ""                  | ProductCategoryModel.Filed.LIST_DEFAULT_FIELDS
        false            | "test"              | ProductCategoryModel.Filed.LIST_DEFAULT_FIELDS
        false            | "test,test2"        | ProductCategoryModel.Filed.LIST_DEFAULT_FIELDS
    }

    def "havaChildNode method"() {
        given: "create user"
        def queryResult = new QueryResult()
        queryResult.setData(dataList)
        def metaDataFindServiceExtMock = Mock(MetaDataFindServiceExt)
        metaDataFindServiceExtMock.findBySearchQuery(user, "ProductCategoryObj", _ as SearchTemplateQuery) >> queryResult
        productCategoryBizServiceTest.setMetaDataFindServiceExt(metaDataFindServiceExtMock)

        expect:
        productCategoryBizServiceTest.havaChildNode(user, pidList) == rst

        where:
        pidList         || dataList           | rst
        []              || [new ObjectData()] | true
        ["1", "2", "3"] || []                 | false
    }

    def "bulkAddTreeHandler with valid data"() {
        given:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.getMaxOrderUnderParent(_, _) >> 1
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)


        ProductCategoryTree root = new ProductCategoryTree(name: "root", pid: "")

        and:
        ProductCategoryInsertImportDataAction.Recorder recorder = Mock(ProductCategoryInsertImportDataAction.Recorder)

        and:
        productCategoryBizServiceTest.getGenerateId() >> "11111"

        when:
        productCategoryBizServiceTest.bulkAddTreeHandler(root, user, recorder)

        then:
        notThrown(Exception)
    }

    def "bulkAddTreeHandler with null root"() {
        given:
        ProductCategoryInsertImportDataAction.Recorder recorder = Mock(ProductCategoryInsertImportDataAction.Recorder)
        when:
        List<ProductCategoryTree> result = productCategoryBizServiceTest.bulkAddTreeHandler(null, user, recorder)

        then:
        CollectionUtils.isEmpty(result)
    }

    def "bulkAddTreeHandler with duplicate names throws exception"() {
        given:
        ProductCategoryTree root = new ProductCategoryTree(name: "root", pid: "", children: []) // 初始化children为空列表
        ProductCategoryTree child1 = new ProductCategoryTree(name: "duplicate", pid: "1")
        ProductCategoryTree child2 = new ProductCategoryTree(name: "duplicate", pid: "1") // Duplicate name

        and:
        productCategoryBizServiceTest.getGenerateId() >> "1"

        and:
        root.children << child1
        root.children << child2
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.getMaxOrderUnderParent(_, _) >> 1
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_, _) >> ""
        categoryStaticUtilService.text(_) >> ""
        productCategoryBizServiceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        ProductCategoryInsertImportDataAction.Recorder recorder = Mock(ProductCategoryInsertImportDataAction.Recorder)
        when:
        productCategoryBizServiceTest.bulkAddTreeHandler(root, user, recorder)

        then:
        thrown(ValidateException)
    }

    def "bulkAddTreeHandler with empty tree returns empty list"() {
        given:
        def root = new ProductCategoryTree();
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.getMaxOrderUnderParent(_, _) >> 1
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)
        and:
        productCategoryBizServiceTest.getGenerateId() >> "1"
        ProductCategoryInsertImportDataAction.Recorder recorder = Mock(ProductCategoryInsertImportDataAction.Recorder)
        when:
        List<ProductCategoryTree> result = productCategoryBizServiceTest.bulkAddTreeHandler(root, user, recorder)
        then:
        notThrown(Exception)
    }


    def "Test importTreeHandler method"() {
        given:

        ProductCategoryTree root = new ProductCategoryTree(name: 'root')
        List<ProductCategoryTree> children = [new ProductCategoryTree(name: 'child1'),
                                              new ProductCategoryTree(name: 'child2')]
        root.children = children

        def globalVariable = Mock(CategoryImportValidateModel.CategoryGlobalVariable)
        ProductCategoryInsertImportDataAction.Recorder recorder = Mock(ProductCategoryInsertImportDataAction.Recorder)

        and:
        productCategoryBizServiceTest.getGenerateId() >> "1"

        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.getMaxOrderUnderParent(_, _) >> 1
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        List<ProductCategoryTree> result = productCategoryBizServiceTest.importTreeHandler(root, globalVariable, recorder)

        then:
        notThrown(Exception)
    }


    def "Test handleCategoryForCPQ with valid inputs"() {
        given:
        String categoryByPriceBookIdSql = "SELECT code FROM category WHERE price_book_id = ?"
        Boolean filterByShopCategory = true

        and:
        def bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService)
        bizConfigThreadLocalCacheService.isCPQEnabled(_ as String) >> true
        productCategoryBizServiceTest.setBizConfigThreadLocalCacheService(bizConfigThreadLocalCacheService)

        def objectDataService = Mock(ObjectDataServiceImpl)
        objectDataService.findBySql(_ as String, _ as String) >> []
        productCategoryBizServiceTest.setObjectDataService(objectDataService)

        when:
        productCategoryBizServiceTest.handleCategoryForCPQ(serviceContext, [], new CategoryObject(), categoryByPriceBookIdSql, filterByShopCategory)

        then:
        notThrown(Exception)
    }

    def "test findUsedCategorySet method"() {
        given:
        // 准备测试数据
        String tenantId = 'someTenantId'
        String sql = 'SELECT * FROM categories WHERE tenant_id = ?'
        Boolean filterByShopCategory = true
        List<IObjectData> categoryDataList = [new ObjectData()]

        // 模拟findBySql方法的行为，这里假设返回一个非空的Map列表
        String[] array = ["category2"]
        List<Map> mockMapList = Arrays.asList([category: 'category1', shop_category_id: array, code: '1'], [category: 'category2', code: '2'])

        and:
        def objectDataService = Mock(ObjectDataServiceImpl)
        objectDataService.findBySql(_ as String, _ as String) >> mockMapList
        productCategoryBizServiceTest.setObjectDataService(objectDataService)

        def bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService)
        bizConfigThreadLocalCacheService.isCPQEnabled("someTenantId") >> true
        productCategoryBizServiceTest.setBizConfigThreadLocalCacheService(bizConfigThreadLocalCacheService)

        when:
        // 调用待测试的方法
        Set<String> result = productCategoryBizServiceTest.findUsedCategorySet(tenantId, sql, filterByShopCategory, categoryDataList)

        then:
        // 验证结果
        result.isEmpty()
    }

    def "remove"() {
        given:

        def object = new CategoryObject();
        object.setCode("1")

        def child = new CategoryObject();

        child.setCode("2")

        object.setChildren([child])
        when:
        productCategoryBizServiceTest.remove(new Stack<>(), object, ["2"] as Set<String>)
        then:
        notThrown(Exception)
    }

    def "handleCategoryMappingCategoryId !isCloseOldProductCategory"() {
        given:
        def data = new ObjectData()

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> false
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.handleCategoryMappingCategoryId(requestContext.getUser(),
                data, "ProductCategoryObj")
        then:
        notThrown(Exception)
    }

    def "handleCategoryMappingCategoryId findObject is null"() {
        given:
        def data = new ObjectData()

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def describeEnhancer = Mock(DescribeEnhancer)
        describeEnhancer.fetchObject(_, _) >> Mock(IObjectDescribe)
        productCategoryBizServiceTest.setDescribeEnhancer(describeEnhancer)
        when:
        productCategoryBizServiceTest.handleCategoryMappingCategoryId(requestContext.getUser(),
                data, "ProductCategoryObj")
        then:
        notThrown(Exception)
    }

    def "handleCategoryMappingCategoryId getCategoryOptionSize is null"() {
        given:
        def data = new ObjectData()

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def describeEnhancer = Mock(DescribeEnhancer)
        describeEnhancer.fetchObject(_, _) >> Mock(IObjectDescribe)
        productCategoryBizServiceTest.setDescribeEnhancer(describeEnhancer)

        productCategoryBizServiceTest.getCategoryOptionSize(_) >> 0

        when:
        productCategoryBizServiceTest.handleCategoryMappingCategoryId(requestContext.getUser(),
                data, "ProductCategoryObj")
        then:
        notThrown(Exception)
    }


    def "handleCategoryMappingCategoryId all"() {
        given:
        def data = new ObjectData()
        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def describe = Mock(IObjectDescribe)
        def category = new SelectOneFieldDescribe()
        category.setSelectOptions([new SelectOption()])
        category.setApiName(CATEGORY)

        describe.getFieldDescribe(_) >> category

        and:
        def describeEnhancer = Mock(DescribeEnhancer)
        describeEnhancer.fetchObject(_, _) >> describe
        productCategoryBizServiceTest.setDescribeEnhancer(describeEnhancer)

        productCategoryBizServiceTest.getCategoryOptionSize(_) >> 1

        productCategoryBizServiceTest.handleCategoryMappingCategoryId(requestContext.getUser(),
                data, _ as List<ISelectOption>) >> {}

        when:
        productCategoryBizServiceTest.handleCategoryMappingCategoryId(requestContext.getUser(),
                data, "ProductCategoryObj")
        then:
        notThrown(Exception)
    }

    def "handleCategoryMappingCategoryId selectOptions category and  productCategoryId is null"() {
        given:
        def data = new ObjectData()

        and:
        productCategoryBizServiceTest.getDataFieldValue(_, _, _) >> null

        when:
        productCategoryBizServiceTest.handleCategoryMappingCategoryId(requestContext.getUser(),
                data, [])
        then:
        notThrown(Exception)
    }

    def "handleCategoryMappingCategoryId selectOptions productCategoryId is null"() {
        given:
        def data = new ObjectData()

        and:
        productCategoryBizServiceTest.getDataFieldValue(_, PRODUCT_CATEGORY_ID, _) >> null
        productCategoryBizServiceTest.getDataFieldValue(_, CATEGORY, _) >> "category"

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.getCategoryIdByCode(_, _) >> "categoryId"
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)
        when:
        productCategoryBizServiceTest.handleCategoryMappingCategoryId(requestContext.getUser(),
                data, [])
        then:
        notThrown(Exception)
    }

    def "handleCategoryMappingCategoryId selectOptions productCategoryId not null"() {
        given:
        def data = new ObjectData()

        and:
        productCategoryBizServiceTest.getDataFieldValue(_, PRODUCT_CATEGORY_ID, _) >> "categoryId"
        productCategoryBizServiceTest.getDataFieldValue(_, CATEGORY, _) >> "category"

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.getCategoryIdByCode(_, _) >> "categoryCode"
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.handleCategoryMappingCategoryId(requestContext.getUser(),
                data, [])
        then:
        notThrown(Exception)
    }

    def "getCategoryOptionSize des == null"() {
        given:

        when:
        productCategoryBizServiceTest.getCategoryOptionSize(null)
        then:
        notThrown(Exception)
    }

    def "getCategoryOptionSize CATEGORY == null"() {
        given:

        def describe = Mock(IObjectDescribe)
        describe.getFieldDescribe(_) >> null
        when:
        productCategoryBizServiceTest.getCategoryOptionSize(describe)
        then:
        notThrown(Exception)
    }

    def "getCategoryOptionSize CATEGORY not null"() {
        given:

        def describe = Mock(IObjectDescribe)
        def category = new SelectOneFieldDescribe()
        describe.getFieldDescribe(_) >> category

        when:
        productCategoryBizServiceTest.getCategoryOptionSize(describe)
        then:
        notThrown(Exception)
    }

    def "test findUsedCategorySet filterByShopCategory = false method"() {
        given:
        // 准备测试数据
        String tenantId = 'someTenantId'
        String sql = 'SELECT * FROM categories WHERE tenant_id = ?'
        Boolean filterByShopCategory = false
        List<IObjectData> categoryDataList = [new ObjectData()]

        // 模拟findBySql方法的行为，这里假设返回一个非空的Map列表
        String[] array = ["category2"]
        List<Map> mockMapList = Arrays.asList([category: 'category1', shop_category_id: array, code: '1'], [category: 'category2', code: '2'], [_id: "id1", code: '3'])


        def objectDataService = Mock(ObjectDataServiceImpl)
        objectDataService.findBySql(_ as String, _ as String) >> mockMapList
        productCategoryBizServiceTest.setObjectDataService(objectDataService)

        def bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService)
        bizConfigThreadLocalCacheService.isCPQEnabled(_ as String) >> true
        productCategoryBizServiceTest.setBizConfigThreadLocalCacheService(bizConfigThreadLocalCacheService)

        when:
        // 调用待测试的方法
        Set<String> result = productCategoryBizServiceTest.findUsedCategorySet(tenantId, sql, filterByShopCategory, categoryDataList)

        then:
        // 验证结果
        !result.isEmpty()
    }

    def "Test bulkAddDataFill"() {
        given:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> false
        productCategoryUtils.findMaxCode(_) >> 100
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        List<ProductCategoryTree> result = []
        Map<String, String> categoryPidMap = [:]

        // 准备ProductCategoryTree实例列表作为输入参数
        ProductCategoryTree tree1 = new ProductCategoryTree(id: 'id1',
                name: 'Category 1',
                orderField: 2,
                categoryCode: 'CC1',
                categoryImage: [])
        ProductCategoryTree tree2 = new ProductCategoryTree(id: 'id2',
                name: 'Category 2',
                orderField: 1,
                categoryCode: '出',
                categoryImage: [])
        result << tree1
        result << tree2

        def categoryRandomTimeUtil = Mock(CategoryRandomTimeUtil)
        categoryRandomTimeUtil.nextId() >> "1212112112"
        productCategoryBizServiceTest.setCategoryRandomTimeUtil(categoryRandomTimeUtil)

        when:
        List<IObjectData> dataList = productCategoryBizServiceTest.bulkAddDataFill(user, result, categoryPidMap)

        then:
        // 验证返回的数据列表长度
        dataList.size() == 2

        // 验证每个IObjectData对象的关键属性设置是否正确
        dataList.eachWithIndex { IObjectData data, int index ->
            assert data.getTenantId() == '71568'
            if (index == 0) {
                assert data.get('code') == '101'
                assert data.get('category_code') == 'CC1'
            } else {
                assert data.get('code') != null // 或者更具体的随机ID验证逻辑
                assert data.get('category_code') != null // 确认随机ID被设置
            }
        }
    }

    def "categoryNodeList should handle empty result correctly"() {
        given:
        def query = new SearchTemplateQuery();

        and:
        def serviceFacadeProxy = Mock(ServiceFacadeProxy)
        serviceFacadeProxy.findBySearchQuery(_, _, _) >> null
        productCategoryBizServiceTest.setServiceFacadeProxy(serviceFacadeProxy)

        when:
        ProductCategoryObject.CategoryListResult result = productCategoryBizServiceTest.categoryNodeList(user, query)

        then:
        result == null || CollectionUtils.isEmpty(result.result)
    }

    def "categoryNodeList should process non-empty results"() {
        given:
        def searchTemplateQuery = new SearchTemplateQuery();


        and:
        def serviceFacadeProxy = Mock(ServiceFacadeProxy)
        QueryResult<IObjectData> queryResult = Mock()
        serviceFacadeProxy.findBySearchQuery(user, SFAPreDefineObject.ProductCategory.getApiName(), searchTemplateQuery) >> queryResult
        def parentCategoryList = []
        for (i in 0..<3) {
            def data = new ObjectData();
            data.setId("cat" + i);
            data.setName("cat" + i)
            parentCategoryList.add(data)
        }
        serviceFacadeProxy.findObjectDataByIdsIgnoreAll(_, _, _) >> parentCategoryList
        productCategoryBizServiceTest.setServiceFacadeProxy(serviceFacadeProxy)

        and:
        queryResult.getData() >> parentCategoryList

        when:
        ProductCategoryObject.CategoryListResult result = productCategoryBizServiceTest.categoryNodeList(user, searchTemplateQuery)

        then:
        result != null
        result.getResult().size() == 3
    }

    def "should log warning when any parameter is null or apiName is blank"() {
        given:
        IObjectData originalData = null
        IObjectData argData = new ObjectData();
        argData.setId("sdjlkasjdlk1212")
        String apiName = ""

        when:
        productCategoryBizServiceTest.handleCategoryMappingCategoryId(user, originalData, argData, apiName)
        then:
        notThrown(Exception.class)
    }

    def "should return early if category field is not changed"() {
        given:
        IObjectData originalData = new ObjectData()
        originalData.setId("sdsad")
        IObjectData argData = new ObjectData()
        argData.setId("sdasdada")
        String apiName = "testApi"

        and:
        def productCategoryV2Validator = Mock(ProductCategoryV2Validator)
        productCategoryV2Validator.checkCategoryFieldChanged(originalData, argData) >> false
        productCategoryBizServiceTest.setProductCategoryV2Validator(productCategoryV2Validator)

        when:
        productCategoryBizServiceTest.handleCategoryMappingCategoryId(user, originalData, argData, apiName)
        then:
        notThrown(Exception.class)// No further calls should be made if the category field is not changed.
    }

    def "asyncBackCategoryField"() {
        given:

        def asyncTaskProducer = Mock(AsyncTaskProducer)
        asyncTaskProducer.create("sync_category_field", _ as String, null, 4);
        productCategoryBizServiceTest.setAsyncTaskProducer(asyncTaskProducer)
        when:
        productCategoryBizServiceTest.asyncBackCategoryField(user, "dsb", "xxxObj")
        then:
        notThrown(Exception.class)// No further calls should be made if the category field is not changed.
    }

    def "non isCloseOldProductCategory removeListHeaderCategoryField"() {
        given:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> false
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.removeListHeaderCategoryField(user, null)
        then:
        notThrown(Exception.class)// No further calls should be made if the category field is not changed.
    }


    def "getCategoryDataByPathIds returns empty list when pathIds is empty"() {
        given:
        ProductCategoryBizService service = new ProductCategoryBizService()
        Set<String> pathIds = []

        when:
        List<IObjectData> result = service.getCategoryDataByPathIds(user, pathIds)
        then:
        result == []
    }

    def "getCategoryDataByPathIds returns correct data when pathIds contains #pathIds"() {
        given:
        Set<String> pathIds = ['id1', 'id2']
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery()
        def filter = new Filter();
        filter.fieldName = DBRecord.ID
        filter.operator = Operator.IN
        filter.fieldValues = pathIds as List
        searchTemplateQuery.filters = [filter]

        List<IObjectData> expectedData = [new ObjectData()]
        def queryResult = new QueryResult()
        queryResult.setData(expectedData)
        and:
        def metaDataFindServiceExt = Mock(MetaDataFindServiceExt)
        metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, API_NAME, _ as SearchTemplateQuery) >> queryResult
        productCategoryBizServiceTest.setMetaDataFindServiceExt(metaDataFindServiceExt)
        when:
        productCategoryBizServiceTest.getCategoryDataByPathIds(user, pathIds)

        then:
        notThrown(Exception.class)
    }


    def "test handleCategoryName not closeOldProductCategory"() {
        given:
        BaseListController.Result result = new BaseListController.Result()
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> false
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        expect:
        result == productCategoryBizServiceTest.handleCategoryName(user, result, _ as String, _ as String)
    }

    def "test handleCategoryName-AbstractStandardDetailController not close category"() {
        given:
        AbstractStandardDetailController.Result result = new AbstractStandardDetailController.Result()
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> false
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        expect:
        result == productCategoryBizServiceTest.handleCategoryName(user, result, _ as String, _ as String)
    }

    def "test handleCategoryName-AbstractStandardDetailController close category"() {
        given:
        def data = new ObjectData()
        AbstractStandardDetailController.Result result = Mock(AbstractStandardDetailController.Result)
        def mockDoc = Mock(ObjectDataDocument)
        mockDoc.toObjectData() >> data
        result.getData() >> mockDoc

        and:
        productCategoryBizServiceTest.handleCategoryName(user, Lists.newArrayList(data), "categoryFieldApiName", "displayApiName") >> Lists.newArrayList(data)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        expect:
        result == productCategoryBizServiceTest.handleCategoryName(user, result, "categoryFieldApiName", "displayApiName")
    }


    def "test handleCategoryName closeOldProductCategory and dataList is empty"() {
        given:
        BaseListController.Result result = new BaseListController.Result()
        result.setDataList([])
        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)
        expect:
        result == productCategoryBizServiceTest.handleCategoryName(user, result, _ as String, _ as String)
    }

    def "test sendGrayCategoryObjectMq"() {
        given:
        BaseListController.Result result = new BaseListController.Result()
        and:
        def user = Mock(User)
        user.getTenantId() >> "71568"
        def asyncTaskProducer = Mock(AsyncTaskProducer)
        asyncTaskProducer.create("gray_product_category_object", _ as String, _ as String) >> null
        def productCategoryBizService = new ProductCategoryBizService("asyncTaskProducer": asyncTaskProducer)
        when:
        productCategoryBizService.sendGrayCategoryObjectMq(user)
        then:
        1 * asyncTaskProducer.create("gray_product_category_object", _ as String, _ as String)
    }

    def "test transferProductCategorySearch not closeOldProductCategory"() {
        given:
        StandardSummaryFieldController.Arg arg = new StandardSummaryFieldController.Arg()
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> false
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)
        expect:
        arg == productCategoryBizServiceTest.transferProductCategorySearch(user, arg)
    }

    def "test transferProductCategorySearch closeOldProductCategory"() {
        given:
        StandardSummaryFieldController.Arg arg = new StandardSummaryFieldController.Arg()
        String searchInfoJson = "{\"limit\":20,\"offset\":0,\"filters\":[{\"field_name\":\"product_category_id_search\",\"field_values\":[\"60335967325a970001f290de\"],\"operator\":\"EQ\"},{\"field_name\":\"shop_category_id\",\"field_values\":[\"60335967325a970001f290de\"],\"operator\":\"NIN\"}],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}"
        arg.setSearchQueryInfo(searchInfoJson)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        and:
        List<IObjectData> dataList = []
        for (i in 0..<10) {
            def data = new ObjectData();
            data.setId(i as String)
            dataList.add(data)
        }
        def treePathService = Mock(TreePathService)
        treePathService.getChildren(user, "product_category_path", ['60335967325a970001f290de']) >> dataList
        productCategoryBizServiceTest.setTreePathService(treePathService)

        when:
        productCategoryBizServiceTest.transferProductCategorySearch(user, arg)
        then:
        notThrown(Exception.class)
    }

    def "test checkCategoryListArg arg parse error"() {
        given:
        ProductCategoryObject.ProductCategoryListArg arg = new ProductCategoryObject.ProductCategoryListArg()
        String searchInfoJson = "60335967325a970001f290de\"],\"operator\":\"EQ\"},{\"field_name\":\"shop_category_id\",\"field_values\":[\"60335967325a970001f290de\"],\"operator\":\"NIN\"}],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}"
        arg.setSearchQueryInfo(searchInfoJson)


        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_, _) >> ""
        categoryStaticUtilService.text(_) >> ""
        productCategoryBizServiceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryBizServiceTest.checkCategoryListArg(user, arg)
        then:
        thrown(ValidateException.class)
    }

    def "test checkCategoryListArg nodeConditions is empty"() {
        given:
        ProductCategoryObject.ProductCategoryListArg arg = new ProductCategoryObject.ProductCategoryListArg()
        String searchInfoJson = "{\"limit\":20,\"offset\":0,\"filters\":[{\"field_name\":\"product_category_id_search\",\"field_values\":[\"60335967325a970001f290de\"],\"operator\":\"EQ\"},{\"field_name\":\"shop_category_id\",\"field_values\":[\"60335967325a970001f290de\"],\"operator\":\"NIN\"}],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}"
        arg.setSearchQueryInfo(searchInfoJson)
        when:
        productCategoryBizServiceTest.checkCategoryListArg(user, arg)
        then:
        notThrown(Exception.class)
    }

    def "test checkCategoryListArg nodeConditions is not empty"() {
        given:
        ProductCategoryObject.ProductCategoryListArg arg = new ProductCategoryObject.ProductCategoryListArg()
        String searchInfoJson = "{\"limit\":20,\"offset\":0,\"filters\":[{\"field_name\":\"product_category_id_search\",\"field_values\":[\"60335967325a970001f290de\"],\"operator\":\"EQ\"},{\"field_name\":\"shop_category_id\",\"field_values\":[\"60335967325a970001f290de\"],\"operator\":\"NIN\"}],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}"
        arg.setSearchQueryInfo(searchInfoJson)
        arg.setNodeConditions(['include_brother_node'])
        when:
        productCategoryBizServiceTest.checkCategoryListArg(user, arg)
        then:
        notThrown(Exception.class)
    }

    def "test checkCategoryListArg nodeConditions is not empty but error param"() {
        given:
        ProductCategoryObject.ProductCategoryListArg arg = new ProductCategoryObject.ProductCategoryListArg()
        String searchInfoJson = "{\"limit\":20,\"offset\":0,\"filters\":[{\"field_name\":\"product_category_id_search\",\"field_values\":[\"60335967325a970001f290de\"],\"operator\":\"EQ\"},{\"field_name\":\"shop_category_id\",\"field_values\":[\"60335967325a970001f290de\"],\"operator\":\"NIN\"}],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}"
        arg.setSearchQueryInfo(searchInfoJson)
        arg.setNodeConditions(['include_brother_node', 'test'])
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_, _) >> ""
        categoryStaticUtilService.text(_) >> ""
        productCategoryBizServiceTest.setCategoryStaticUtilService(categoryStaticUtilService)
        when:
        productCategoryBizServiceTest.checkCategoryListArg(user, arg)
        then:
        thrown(ValidateException.class)
    }

    def "test queryProductAllCategories"() {
        given:
        List<IObjectData> allShopCategoryData = []
        for (i in 0..<10) {
            def data = new ObjectData()
            data.setId(i as String)
            data.set('code', i as String)
            data.set('name', i as String)
            data.set('order_field', i as String)
            if (i != 0) {
                data.set('pid', (i - 1) as String)
            }
            allShopCategoryData.add(data)
        }
        when:
        productCategoryBizServiceTest.queryProductAllCategories(allShopCategoryData)
        then:
        notThrown(Exception.class)
    }

    def "test getCategoryCodeSQL"() {
        given:
        def bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService)
        bizConfigThreadLocalCacheService.isCPQEnabled(_ as String) >> openCPQ
        productCategoryBizServiceTest.setBizConfigThreadLocalCacheService(bizConfigThreadLocalCacheService)

        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.getCategoryByPriceBookIdAndProducts(_, _, _) >> "false"
        categoryStaticUtilService.getCategoryByProducts(_, _) >> "false"
        productCategoryBizServiceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryBizServiceTest.getCategoryCodeSQL(user, productIds as Set, priceBookId, filterByShopCategory, ignoreProductStatus)

        then:
        notThrown(Exception.class)
        where:
        filterByShopCategory | priceBookId | ignoreProductStatus | openCPQ | productIds || sql
        true                 | '11'        | true                | true    | []         || ""
        true                 | '11'        | true                | true    | ['prod1']  || ""
        true                 | '11'        | true                | false   | []         || ""
        true                 | '11'        | true                | false   | ['prod1']  || ""
        true                 | '11'        | false               | true    | []         || ""
        true                 | '11'        | false               | true    | ['prod1']  || ""
        true                 | '11'        | false               | false   | []         || ""
        true                 | '11'        | false               | false   | ['prod1']  || ""
        true                 | ''          | true                | true    | []         || ""
        true                 | ''          | true                | true    | ['prod1']  || ""
        true                 | ''          | true                | false   | []         || ""
        true                 | ''          | true                | false   | ['prod1']  || ""
        true                 | ''          | false               | true    | []         || ""
        true                 | ''          | false               | true    | ['prod1']  || ""
        true                 | ''          | false               | false   | []         || ""
        true                 | ''          | false               | false   | ['prod1']  || ""
        false                | '11'        | true                | true    | []         || ""
        false                | '11'        | true                | true    | ['prod1']  || ""
        false                | '11'        | true                | false   | []         || ""
        false                | '11'        | true                | false   | ['prod1']  || ""
        false                | '11'        | false               | true    | []         || ""
        false                | '11'        | false               | true    | ['prod1']  || ""
        false                | '11'        | false               | false   | []         || ""
        false                | '11'        | false               | false   | ['prod1']  || ""
        false                | ''          | true                | true    | []         || ""
        false                | ''          | true                | true    | ['prod1']  || ""
        false                | ''          | true                | false   | []         || ""
        false                | ''          | true                | false   | ['prod1']  || ""
        false                | ''          | false               | true    | []         || ""
        false                | ''          | false               | true    | ['prod1']  || ""
        false                | ''          | false               | false   | []         || ""
        false                | ''          | false               | false   | ['prod1']  || ""
    }

    def "test findListCategoryData is not preCategoryField"() {
        given:
        def query = new SearchTemplateQuery();
        MetaDataFindServiceExt metaDataFindServiceExt = Mock(MetaDataFindServiceExt)
        def queryResult = new QueryResult()
        queryResult.setData(Lists.newArrayList())
        metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, SFAPreDefineObject.ProductCategory.getApiName(), query) >> queryResult
        productCategoryBizServiceTest.setMetaDataFindServiceExt(metaDataFindServiceExt)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)
        when:
        productCategoryBizServiceTest.findListCategoryData(user, ["include_parent_node"], false, query, Sets.newHashSet())
        then:
        notThrown(Exception.class)
    }

    def "test buildListSearchQuery throw ValidateException of categoryFilter == null"() {
        given:
        def arg = ProductCategoryObject.ProductCategoryListArg.builder().build();
        arg.setFilterCategory(null)
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_, _) >> ""
        categoryStaticUtilService.text(_) >> ""
        productCategoryBizServiceTest.setCategoryStaticUtilService(categoryStaticUtilService)
        when:
        productCategoryBizServiceTest.buildListSearchQuery(user, arg, true)
        then:
        thrown(ValidateException.class)
    }

    def "test buildListSearchQuery throw ValidateException"() {
        given:
        def arg = ProductCategoryObject.ProductCategoryListArg.builder().build();
        arg.setFilterByShopCategory(true)
        arg.setFilterCategory("ALL")
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_, _) >> ""
        categoryStaticUtilService.text(_) >> ""
        productCategoryBizServiceTest.setCategoryStaticUtilService(categoryStaticUtilService)
        when:
        productCategoryBizServiceTest.buildListSearchQuery(user, arg, true)
        then:
        thrown(ValidateException.class)
    }

    def "test buildListSearchQuery not throw ValidateException of arg is not null"() {
        given:
        def arg = ProductCategoryObject.ProductCategoryListArg.builder().build();
        arg.setFilterByShopCategory(false)
        arg.setFilterCategory("ALL")

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.fillCategoryFilter(_, _) >> {}
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.buildListSearchQuery(user, arg, true)
        then:
        notThrown(ValidateException.class)
    }

    def "test getCategoryTree"() {
        given:
        def arg = new ProductCategoryObject.ChildCategoryArg()
        arg.setObjectDataId("123")
        and:
        def data = new ObjectData()
        data.setId(arg.objectDataId)
        data.setName("单元测试")

        and:
        def serviceFacadeProxy = Mock(ServiceFacadeProxy)
        serviceFacadeProxy.findObjectData(_, _, _) >> data
        productCategoryBizServiceTest.setServiceFacadeProxy(serviceFacadeProxy)

        and:
        def result = new QueryResult<IObjectData>()
        result.setData([data])
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findAllCategory(_) >> result
        productCategoryUtils.getTreeList(_, _, _) >> []
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.getCategoryTree(user, arg)
        then:
        notThrown(ValidateException.class)
    }

    def "test convertProductCategory arg is null"() {
        given:
        Map arg = null
        when:
        def categories = productCategoryBizServiceTest.convertProductCategory((Map) arg)
        then:
        categories.size() == 0
    }

    def "test handleSqlForCPQ not isCPQEnabled"() {
        given:
        Map arg = null

        def bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService)
        bizConfigThreadLocalCacheService.isCPQEnabled(_ as String) >> true

        productCategoryBizServiceTest.setBizConfigThreadLocalCacheService(bizConfigThreadLocalCacheService)
        when:
        productCategoryBizServiceTest.handleSqlForCPQ("", user);
        then:
        notThrown(Exception)
    }

    def "test getAllParentNodeIds"() {
        given:
        def data = new ObjectData();
        def arg = [data]
        productCategoryBizServiceTest.getCategoryParentPathNodeIds(data) >> []
        when:
        productCategoryBizServiceTest.getAllParentNodeIds(arg);
        then:
        notThrown(Exception)
    }

    def "test getCategoryParentPathNodeIds path is null"() {
        given:
        def data = new ObjectData();
        when:
        productCategoryBizServiceTest.getCategoryParentPathNodeIds(data)
        then:
        notThrown(Exception)
    }

    def "test getCategoryParentPathNodeIds splitNodes.length <= 1"() {
        given:
        def data = new ObjectData()
        data.set("product_category_path", "1")
        when:
        productCategoryBizServiceTest.getCategoryParentPathNodeIds(data)
        then:
        notThrown(Exception)
    }

    def "test getCategoryParentPathNodeIds splitNodes.length > 1"() {
        given:
        def data = new ObjectData()
        data.set("product_category_path", "1.2.3")
        when:
        productCategoryBizServiceTest.getCategoryParentPathNodeIds(data)
        then:
        notThrown(Exception)
    }

    def "test getAllBrotherNodeData parentNodeIds is null"() {
        given:

        def data = new ObjectData()
        data.set("pid", "1")
        data.set("product_category_path", "1.2.3")

        QueryResult<IObjectData> q = new QueryResult();
        q.setData(Lists.newArrayList(data))

        and:
        def metaDataFindServiceExt = Mock(MetaDataFindServiceExt)
        metaDataFindServiceExt.findBySearchQueryIgnoreAll(requestContext.getUser(), _ as String, _ as SearchTemplateQuery) >> q
        productCategoryBizServiceTest.setMetaDataFindServiceExt(metaDataFindServiceExt)

        and:
        productCategoryBizServiceTest.findBySearchQueryWithFieldsIgnoreAll(requestContext.getUser(), "ProductCategoryObj",
                _, _) >> []
        when:
        productCategoryBizServiceTest.getAllBrotherNodeData(requestContext.getUser(), [], [], [data])
        then:
        notThrown(Exception)
    }

    def "getAllParentNodeData allParentNodeIds == null"() {
        given:
        when:
        productCategoryBizServiceTest.getAllParentNodeData(requestContext.getUser(),
                [], [], [])
        then:
        notThrown(Exception)
    }

    def "getAllParentNodeData extendFields == null"() {
        given:
        def data1 = new ObjectData()
        data1.setId("1")
        def data2 = new ObjectData()
        data2.setId("2")
        data2.set("product_category_path", "2")
        def data3 = new ObjectData()
        data3.setId("3")
        data3.set("pid", "2")
        data3.set("product_category_path", "2.3")
        List<IObjectData> categoryData = [data1, data2, data3]


        and:
        def metaDataFindServiceExt = Mock(MetaDataFindServiceExt)
        metaDataFindServiceExt.findObjectByIdsIgnoreAll(requestContext.getUser(),
                _ as List<String>, SFAPreDefineObject.ProductCategory.getApiName()) >> []
        productCategoryBizServiceTest.setMetaDataFindServiceExt(metaDataFindServiceExt)

        when:
        productCategoryBizServiceTest.getAllParentNodeData(requestContext.getUser(),
                [], [], categoryData)
        then:
        notThrown(Exception)
    }

    def "getAllParentNodeData extendFields != null"() {
        given:
        def data1 = new ObjectData()
        data1.setId("1")
        def data2 = new ObjectData()
        data2.setId("2")
        data2.set("product_category_path", "2")
        def data3 = new ObjectData()
        data3.setId("3")
        data3.set("pid", "2")
        data3.set("product_category_path", "2.3")
        List<IObjectData> categoryData = [data1, data2, data3]


        and:
        def metaDataFindServiceExt = Mock(MetaDataFindServiceExt)
        metaDataFindServiceExt.findObjectByIdsWithFieldsIgnoreAll(requestContext.getUser(),
                _ as List<String>, SFAPreDefineObject.ProductCategory.getApiName(), ["code"]) >> []
        productCategoryBizServiceTest.setMetaDataFindServiceExt(metaDataFindServiceExt)

        when:
        productCategoryBizServiceTest.getAllParentNodeData(requestContext.getUser(),
                [], ["code"], categoryData)
        then:
        notThrown(Exception)
    }


    def "getAllBrotherNodeData parentNodeIds != null"() {
        given:
        def data1 = new ObjectData()
        data1.setId("1")
        def data2 = new ObjectData()
        data2.setId("2")
        data2.set("product_category_path", "2")
        def data3 = new ObjectData()
        data3.setId("3")
        data3.set("pid", "2")
        data3.set("product_category_path", "2.3")
        List<IObjectData> categoryData = [data1, data2, data3]

        and:
        productCategoryBizServiceTest.findBySearchQueryWithFieldsIgnoreAll(requestContext.getUser(),
                SFAPreDefineObject.ProductCategory.getApiName(), _ as SearchTemplateQuery, ["code"]) >> []

        when:
        productCategoryBizServiceTest.getAllBrotherNodeData(requestContext.getUser(),
                [], ["code"], categoryData)
        then:
        notThrown(Exception)
    }


    def "getAllChildrenNodeData remainingCategoryData == null"() {
        given:
        def service = Spy(ProductCategoryBizService)
        service.findBySearchQueryWithFieldsIgnoreAll(requestContext.getUser(),
                SFAPreDefineObject.ProductCategory.getApiName(), _ as SearchTemplateQuery, _ as List<String>) >> []

        when:
        service.getAllChildrenNodeData(requestContext.getUser(),
                [], Lists.newArrayList("code"))
        then:
        notThrown(Exception)
    }

    def "getAllChildrenNodeData remainingCategoryData != null"() {
        given:
        def data1 = new ObjectData()
        data1.setId("1")
        def data2 = new ObjectData()
        data2.setId("2")
        data2.set("product_category_path", "2")
        def data3 = new ObjectData()
        data3.setId("3")
        data3.set("pid", "2")
        data3.set("product_category_path", "2.3")
        List<IObjectData> categoryData = [data1, data2, data3]

        and:
        def service = Spy(ProductCategoryBizService)
        service.findBySearchQueryWithFieldsIgnoreAll(requestContext.getUser(),
                SFAPreDefineObject.ProductCategory.getApiName(), _ as SearchTemplateQuery, _ as List<String>) >> categoryData

        when:
        service.getAllChildrenNodeData(requestContext.getUser(),
                [], Lists.newArrayList("code"))
        then:
        notThrown(Exception)
    }


    def "findRelatedCategoryNodes remainingCategoryData != null"() {
        given:
        Set<CategoryFilterConditionEnum> conditions = Sets.newHashSet(CategoryFilterConditionEnum.INCLUDE_PARENT_NODE,
                CategoryFilterConditionEnum.INCLUDE_BROTHER_NODE,
                CategoryFilterConditionEnum.INCLUDE_CHILDREN_NODE)

        List<String> extendFields = ["code"]


        and:
        def data1 = new ObjectData()
        data1.setId("1")
        def data2 = new ObjectData()
        data2.setId("2")
        data2.set("product_category_path", "2")
        def data3 = new ObjectData()
        data3.setId("3")
        data3.set("pid", "2")
        data3.set("product_category_path", "2.3")
        List<IObjectData> categoryData = [data1, data2, data3]

        and:
        def service = Spy(ProductCategoryBizService)
        service.getAllParentNodeData(requestContext.getUser(), _ as List<String>, extendFields, categoryData) >> []
        service.getAllBrotherNodeData(requestContext.getUser(), _ as List<String>, extendFields, categoryData) >> []
        service.getAllChildrenNodeData(requestContext.getUser(), _ as List<String>, extendFields) >> []

        when:
        service.findRelatedCategoryNodes(requestContext.getUser(), extendFields, categoryData, conditions)
        then:
        notThrown(Exception)

    }


    def "findRelatedCategoryNodesByConditions nodeConditions != null"() {
        given:
        Set<CategoryFilterConditionEnum> conditions = Sets.newHashSet(CategoryFilterConditionEnum.INCLUDE_PARENT_NODE,
                CategoryFilterConditionEnum.INCLUDE_BROTHER_NODE,
                CategoryFilterConditionEnum.INCLUDE_CHILDREN_NODE)

        List<String> extendFields = ["code"]


        and:
        def data1 = new ObjectData()
        data1.setId("1")
        def data2 = new ObjectData()
        data2.setId("2")
        data2.set("product_category_path", "2")
        def data3 = new ObjectData()
        data3.setId("3")
        data3.set("pid", "2")
        data3.set("product_category_path", "2.3")
        List<IObjectData> categoryData = [data1, data2, data3]

        and:
        def service = Spy(ProductCategoryBizService)
        service.findRelatedCategoryNodes(requestContext.getUser(), extendFields, categoryData, _ as Set<CategoryFilterConditionEnum>) >> []
        when:
        service.findRelatedCategoryNodesByConditions(requestContext.getUser(), conditions, extendFields, categoryData)
        then:
        notThrown(Exception)

    }

    def "findRelatedCategoryNodesByConditions nodeConditions == null"() {
        given:
        Set<CategoryFilterConditionEnum> conditions = Sets.newHashSet()

        List<String> extendFields = ["code"]


        and:
        def data1 = new ObjectData()
        data1.setId("1")
        def data2 = new ObjectData()
        data2.setId("2")
        data2.set("product_category_path", "2")
        def data3 = new ObjectData()
        data3.setId("3")
        data3.set("pid", "2")
        data3.set("product_category_path", "2.3")
        List<IObjectData> categoryData = [data1, data2, data3]

        and:
        def service = Spy(ProductCategoryBizService)
        service.findRelatedCategoryNodes(requestContext.getUser(), extendFields, categoryData, conditions) >> []
        when:
        service.findRelatedCategoryNodesByConditions(requestContext.getUser(), conditions, extendFields, categoryData)
        then:
        notThrown(Exception)

    }

    def "getIObjectDataList nodeConditions == null"() {
        given:
        Set<CategoryFilterConditionEnum> conditions = Sets.newHashSet()

        List<String> extendFields = ["code"]


        and:
        def data1 = new ObjectData()
        data1.setId("1")
        def data2 = new ObjectData()
        data2.setId("2")
        data2.set("product_category_path", "2")
        def data3 = new ObjectData()
        data3.setId("3")
        data3.set("pid", "2")
        data3.set("product_category_path", "2.3")
        List<IObjectData> categoryData = [data1, data2, data3]

        and:
        def service = Spy(ProductCategoryBizService)
        service.findRelatedCategoryNodesByConditions(requestContext.getUser(), conditions, extendFields, categoryData) >> []
        when:
        service.getIObjectDataList(requestContext.getUser(), conditions, extendFields, categoryData)
        then:
        notThrown(Exception)

    }


    def "isPreCategoryField"() {
        given:

        when:
        productCategoryBizServiceTest.isPreCategoryField("1")
        then:
        notThrown(Exception)

    }

    def "getTupleSupplier"() {
        given:
        def arg = new ProductCategoryObject.ProductCategoryListArg()

        arg.setMasterDataForWhere(new ObjectDataDocument())
        arg.setObjectDataForWhere(new ObjectDataDocument())
        arg.setDetailsForWhere(Maps.newHashMap())

        and:
        ServiceFacadeProxy serviceFacadeProxy = Mock(ServiceFacadeProxy)
        serviceFacadeProxy.fillQuoteValueVirtualField(requestContext.getUser(),
                _ as IObjectData, _ as Map<String, List<IObjectData>>) >> {}
        productCategoryBizServiceTest.setServiceFacadeProxy(serviceFacadeProxy)
        when:
        productCategoryBizServiceTest.getTupleSupplier(requestContext.getUser(), arg)
        then:
        notThrown(Exception)
    }

    def "handleCategoryMappingCategoryIdOfImport !isCloseOldProductCategory"() {
        given:
        def data1 = new ObjectData()
        data1.setId("1")
        def data2 = new ObjectData()
        data2.setId("2")
        data2.set("product_category_path", "2")
        def data3 = new ObjectData()
        data3.setId("3")
        data3.set("pid", "2")
        data3.set("product_category_path", "2.3")
        List<IObjectData> categoryData = [data1, data2, data3]

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> false
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)


        when:
        productCategoryBizServiceTest.handleCategoryMappingCategoryIdOfImport(requestContext.getUser(), categoryData, "ProductCategoryObj")
        then:
        notThrown(Exception)

    }


    def "handleCategoryMappingCategoryIdOfImport objectDescribe == null"() {
        given:

        def describe = Mock(IObjectDescribe)
        def category = new SelectOneFieldDescribe()
        describe.getFieldDescribe(_) >> category

        and:
        def data1 = new ObjectData()
        data1.setId("1")
        def data2 = new ObjectData()
        data2.setId("2")
        data2.set("product_category_path", "2")
        def data3 = new ObjectData()
        data3.setId("3")
        data3.set("pid", "2")
        data3.set("product_category_path", "2.3")
        List<IObjectData> categoryData = [data1, data2, data3]


        and:
        def describeEnhancer = Mock(DescribeEnhancer)
        describeEnhancer.fetchObject(_, _) >> null
        productCategoryBizServiceTest.setDescribeEnhancer(describeEnhancer)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.handleCategoryMappingCategoryIdOfImport(requestContext.getUser(), categoryData, "ProductCategoryObj")
        then:
        notThrown(Exception)

    }

    def "handleCategoryMappingCategoryIdOfImport category == null"() {
        given:
        def describe = Mock(IObjectDescribe)
        def category = new SelectOneFieldDescribe()
        category.setSelectOptions([new SelectOption()])
        describe.getFieldDescribe(_) >> category

        and:
        def data1 = new ObjectData()
        data1.setId("1")
        def data2 = new ObjectData()
        data2.setId("2")
        data2.set("product_category_path", "2")
        def data3 = new ObjectData()
        data3.setId("3")
        data3.set("pid", "2")
        data3.set("product_category_path", "2.3")
        List<IObjectData> categoryData = [data1, data2, data3]

        and:
        def describeEnhancer = Mock(DescribeEnhancer)
        describeEnhancer.fetchObject(_, _) >> describe
        productCategoryBizServiceTest.setDescribeEnhancer(describeEnhancer)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)
        Map<String, String> map = Maps.newHashMap()
        map.put("2", "code")
        productCategoryUtils.getCodeByCategoryIds(_, _) >> map
        when:
        productCategoryBizServiceTest.handleCategoryMappingCategoryIdOfImport(requestContext.getUser(), categoryData, "ProductCategoryObj")
        then:
        notThrown(Exception)

    }

    def "handleCategoryMappingCategoryIdOfImport SelectOption == null"() {
        given:

        def describe = Mock(IObjectDescribe)
        def category = new SelectOneFieldDescribe()
        describe.getFieldDescribe(_) >> category

        and:
        def data1 = new ObjectData()
        data1.setId("1")
        def data2 = new ObjectData()
        data2.setId("2")
        data2.set("product_category_path", "2")
        def data3 = new ObjectData()
        data3.setId("3")
        data3.set("pid", "2")
        data3.set("product_category_path", "2.3")
        List<IObjectData> categoryData = [data1, data2, data3]

        and:
        def describeEnhancer = Mock(DescribeEnhancer)
        describeEnhancer.fetchObject(_, _) >> describe
        productCategoryBizServiceTest.setDescribeEnhancer(describeEnhancer)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.handleCategoryMappingCategoryIdOfImport(requestContext.getUser(), categoryData, "ProductCategoryObj")
        then:
        notThrown(Exception)

    }

    def "handleCategoryMappingCategoryIdOfImport all"() {
        given:
        def describe = Mock(IObjectDescribe)
        def category = new SelectOneFieldDescribe()
        category.setSelectOptions([new SelectOption()])
        describe.getFieldDescribe(_) >> category

        and:
        def data1 = new ObjectData()
        data1.setId("1")
        data1.set("product_category_id", "2")
        def data2 = new ObjectData()
        data2.setId("2")
        data2.set("product_category_path", "2")
        def data3 = new ObjectData()
        data3.setId("3")
        data3.set("pid", "2")
        data3.set("product_category_path", "2.3")
        List<IObjectData> categoryData = [data1, data2, data3]
        and:
        def describeEnhancer = Mock(DescribeEnhancer)
        describeEnhancer.fetchObject(_, _) >> describe
        productCategoryBizServiceTest.setDescribeEnhancer(describeEnhancer)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(_) >> true
        Map<String, String> map = Maps.newHashMap()
        map.put("2", "code")
        productCategoryUtils.getCodeByCategoryIds(_, _) >> map
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.handleCategoryMappingCategoryIdOfImport(requestContext.getUser(), categoryData, "ProductCategoryObj")
        then:
        notThrown(Exception)
    }

    def "filterByAvailableRange arg not null"() {
        given:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        productCategoryBizServiceTest.setCategoryStaticUtilService(categoryStaticUtilService)
        categoryStaticUtilService.isDhtOrAccessoriesMallRequest(_, _) >> true
        categoryStaticUtilService.getAccountIdByOutTenantId(_, _, _) >> ""

        and:
        productCategoryBizServiceTest.filterCategoryByAvailableRange(user, _, _) >> []

        and:
        def arg = new ProductCategoryObject.ProductCategoryListArg()
        def data = new ObjectData()
        def document = Mock(ObjectDataDocument)
        document.toObjectData() >> data

        arg.setObjectData(document)
        when:
        productCategoryBizServiceTest.filterByAvailableRange(serviceContext, arg, null)
        then:
        notThrown(Exception)
    }

    def "filterByAvailableRange arg is null"() {
        when:
        productCategoryBizServiceTest.filterByAvailableRange(serviceContext, null, null)
        then:
        notThrown(Exception)
    }

    def "filterCategoryByAvailableRange not isAvailableRangeEnabled"() {
        given:
        BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService)
        bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId()) >> false
        productCategoryBizServiceTest.setBizConfigThreadLocalCacheService(bizConfigThreadLocalCacheService)
        and:
        def arg = new ProductCategoryObject.ProductCategoryListArg()
        def data = new ObjectData()
        def document = Mock(ObjectDataDocument)
        document.toObjectData() >> data
        arg.setObjectData(document)
        when:
        productCategoryBizServiceTest.filterCategoryByAvailableRange(user, arg, [])
        then:
        notThrown(Exception)
    }

    def "filterCategoryByAvailableRange is isAvailableRangeEnabled and dataDocument == null"() {
        given:
        BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService)
        bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId()) >> true
        productCategoryBizServiceTest.setBizConfigThreadLocalCacheService(bizConfigThreadLocalCacheService)
        and:
        def arg = new ProductCategoryObject.ProductCategoryListArg()
        arg.getObjectData() >> null
        when:
        productCategoryBizServiceTest.filterCategoryByAvailableRange(user, arg, [])
        then:
        notThrown(Exception)
    }


    def "filterCategoryByAvailableRange account_id is null"() {
        given:
        BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService)
        bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId()) >> true
        productCategoryBizServiceTest.setBizConfigThreadLocalCacheService(bizConfigThreadLocalCacheService)
        and:
        def arg = new ProductCategoryObject.ProductCategoryListArg()
        def data = new ObjectData()
        def document = Mock(ObjectDataDocument)
        document.toObjectData() >> data
        arg.setObjectData(document)
        when:
        productCategoryBizServiceTest.filterCategoryByAvailableRange(user, arg, [])
        then:
        notThrown(Exception)
    }

    def "filterCategoryByAvailableRange availableTuple.getKey() is false"() {
        given:
        BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService)
        bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId()) >> true
        productCategoryBizServiceTest.setBizConfigThreadLocalCacheService(bizConfigThreadLocalCacheService)

        and:
        def dhtPriceBookService = Mock(DhtPriceBookService)
        dhtPriceBookService.detailDataDocument2detailData(_, _) >> {}

        dhtPriceBookService.matchAvailableProduct(user, _, _, _, null, _, _) >> Tuple.of(false, null)
        productCategoryBizServiceTest.setDhtPriceBookService(dhtPriceBookService)

        and:
        def arg = new ProductCategoryObject.ProductCategoryListArg()
        def data = new ObjectData()
        data.set("account_id", "account_id")
        def document = Mock(ObjectDataDocument)
        document.toObjectData() >> data
        arg.setObjectData(document)
        when:
        productCategoryBizServiceTest.filterCategoryByAvailableRange(user, arg, [])
        then:
        notThrown(Exception)
    }


    def "filterCategoryByAvailableRange availableTuple.getKey() is true"() {
        given:
        BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService)
        bizConfigThreadLocalCacheService.isAvailableRangeEnabled(user.getTenantId()) >> true
        productCategoryBizServiceTest.setBizConfigThreadLocalCacheService(bizConfigThreadLocalCacheService)

        and:
        def dhtPriceBookService = Mock(DhtPriceBookService)
        dhtPriceBookService.detailDataDocument2detailData(_, _) >> {}

        dhtPriceBookService.matchAvailableProduct(user, _, _, _, null, _, _) >> Tuple.of(true, null)
        productCategoryBizServiceTest.setDhtPriceBookService(dhtPriceBookService)

        and:
        productCategoryBizServiceTest.getCategoryCodeSQL(user, _, _, _, _) >> ""
        productCategoryBizServiceTest.findUsedCategorySet(user.getTenantId(), _, _, _) >> []
        productCategoryBizServiceTest.filterByUsedCategory(_, _, _, _) >> {}


        and:
        def arg = new ProductCategoryObject.ProductCategoryListArg()
        def data = new ObjectData()
        data.set("account_id", "account_id")
        def document = Mock(ObjectDataDocument)
        document.toObjectData() >> data
        arg.setObjectData(document)
        when:
        productCategoryBizServiceTest.filterCategoryByAvailableRange(user, arg, [])
        then:
        notThrown(Exception)
    }


    def "filterByUsedCategory"() {
        given:
        def data1 = new ObjectData()
        data1.set("code", "1")


        def data2 = new ObjectData()
        data2.set("code", "2")
        data2.set("pid", "1")
        def categoryDataList = [data1, data2]

        Set<String> categorySet = ["1", "2"]
        Set<String> parentIdSet = []
        List<String> tempDataList = []


        when:
        productCategoryBizServiceTest.filterByUsedCategory(categoryDataList, categorySet, parentIdSet, tempDataList as List<IObjectData>)
        then:
        notThrown(Exception)
    }

    def "categoryPathAssemblyOfExport not closeOldProductCategory"() {
        given:
        def dataList = []
        def invoke = {}

        and:
        def categoryUtils = Mock(ProductCategoryUtils)
        categoryUtils.isCloseOldProductCategory(user.getTenantId()) >> false
        productCategoryBizServiceTest.setProductCategoryUtils(categoryUtils)

        when:
        productCategoryBizServiceTest.categoryPathAssemblyOfExport(user, dataList, invoke)
        then:
        notThrown(Exception)
    }

    def "categoryPathAssemblyOfExport closeOldProductCategory "() {
        given:
        def data = new ObjectData()
        data.set("product_category_id", "1")

        def dataList = [data]
        def invoke = {}

        and:
        def categoryUtils = Mock(ProductCategoryUtils)
        categoryUtils.isCloseOldProductCategory(user.getTenantId()) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(categoryUtils)

        and:
        productCategoryBizServiceTest.handleCategoryName(user, dataList, PRODUCT_CATEGORY_ID, PRODUCT_CATEGORY_ID) >> {}

        when:
        productCategoryBizServiceTest.categoryPathAssemblyOfExport(user, dataList, invoke)
        then:
        notThrown(Exception)
    }

    def "buildCategorySelectOptions not closeOldProductCategory"() {
        given:
        def describeDocument = Mock(ObjectDescribeDocument)

        and:
        def categoryUtils = Mock(ProductCategoryUtils)
        categoryUtils.isCloseOldProductCategory(user.getTenantId()) >> false
        productCategoryBizServiceTest.setProductCategoryUtils(categoryUtils)

        when:
        productCategoryBizServiceTest.buildCategorySelectOptions(user, describeDocument)
        then:
        notThrown(Exception)
    }

    def "buildCategorySelectOptions is closeOldProductCategory is null"() {
        given:
        def describeDocument = Mock(ObjectDescribeDocument)
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> []

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> false
        productCategoryUtils.findAllSortedCategory(user, Lists.newArrayList(CODE, NAME, ORDER_FIELD, PID, PRODUCT_CATEGORY_PATH), true, CategoryFilterEnum.PRODUCT) >> queryResult
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.buildCategorySelectOptions(user, describeDocument)
        then:
        notThrown(Exception)
    }

    def "buildCategorySelectOptions is closeOldProductCategory is not null"() {
        given:
        def describeDocument = Mock(ObjectDescribeDocument)
        def describe = Mock(IObjectDescribe)
        describeDocument.toObjectDescribe() >> describe

        def selectOneFieldDescribe = Mock(SelectOneFieldDescribe)
        describe.getFieldDescribe("category") >> selectOneFieldDescribe
        selectOneFieldDescribe.setSelectOptions(_) >> {}

        def queryResult = Mock(QueryResult)
        queryResult.getData() >> [new ObjectData()]

        def root = new CategoryObject();
        def ch1 = new CategoryObject();
        ch1.setName("ch1")
        ch1.setCode("ch1")
        def ch11 = new CategoryObject();
        ch11.setName("ch11")
        ch11.setCode("ch11")
        ch1.setChildren([ch11])
        def ch2 = new CategoryObject();
        ch2.setName("ch2")
        ch2.setCode("ch2")
        root.setChildren([ch1, ch11, ch2])

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> true
        productCategoryUtils.findAllSortedCategory(user, Lists.newArrayList(CODE, NAME, ORDER_FIELD, PID, PRODUCT_CATEGORY_PATH), true, CategoryFilterEnum.PRODUCT) >> queryResult
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        and:
        productCategoryBizServiceTest.buildCategoryTree(_) >> root

        when:
        productCategoryBizServiceTest.buildCategorySelectOptions(user, describeDocument)
        then:
        notThrown(Exception)
    }


    def "buildCategoryTree"() {
        given:
        def chData1 = new ObjectData()
        chData1.setId("1")
        chData1.setName("chData1")
        chData1.set("code", "chData1")
        chData1.set("category_code", "chData1")
        chData1.set("order_field", 1)

        def chData11 = new ObjectData()
        chData11.setId("11")
        chData11.setName("chData11")
        chData11.set("code", "chData11")
        chData11.set("category_code", "chData11")
        chData11.set("order_field", 1)
        chData11.set("pid", "1")
        def chData2 = new ObjectData()
        chData2.setId("2")
        chData2.setName("chData2")
        chData2.set("code", "chData2")
        chData2.set("category_code", "chData2")
        chData2.set("order_field", 2)
        def allProductCategory = [chData1, chData2, chData11]

        def ch1 = new CategoryObject()
        ch1.setName("chData1")
        ch1.setId("1")
        ch1.setOrderField(1)
        def ch2 = new CategoryObject()
        ch2.setName("chData2")
        ch2.setId("2")
        ch2.setOrderField(2)

        def ch11 = new CategoryObject()
        ch11.setName("chData11")
        ch11.setId("chData11")
        ch11.setPid("1")
        ch11.setOrderField(1)


        and:
        def productCategoryValidator = Mock(ProductCategoryValidator)
        productCategoryValidator.isInnerServiceRequest() >> true

        productCategoryBizServiceTest.getCategoryObjects(allProductCategory, user.getTenantId(), true) >> [ch1, ch2, ch11]
        productCategoryBizServiceTest.setProductCategoryValidator(productCategoryValidator)

        and:
        productCategoryBizServiceTest.getTenantId() >> user.getTenantId()

        when:
        productCategoryBizServiceTest.buildCategoryTree(allProductCategory)
        then:
        notThrown(Exception)
    }

    def "getCategoryObjects"() {
        when:
        productCategoryBizServiceTest.getCategoryObjects([], user.getTenantId(), true)
        then:
        notThrown(Exception)
    }

    def "fillBaseFieldValue not closeOldProductCategory "() {
        given:
        def data1 = new ObjectData()
        data1.set("category_code", "data1")

        def data2 = new ObjectData()
        data2.set("category_code", "data2")
        def validList = [data1, data2]


        def tree1 = new ProductCategoryTree()
        tree1.setCategoryCode("q")
        def tree2 = new ProductCategoryTree()
        tree2.setId("data1")
        tree2.setName("data1")
        tree2.setOrderField(1)
        tree2.setCategoryCode("data1")
        def categoryTreeData = [tree1, tree2]

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> false
        productCategoryUtils.findMaxCode(user) >> "1"
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.fillBaseFieldValue(user, validList, categoryTreeData)
        then:
        notThrown(Exception)
    }

    def "fillBaseFieldValue is closeOldProductCategory "() {
        given:
        def data1 = new ObjectData()
        data1.set("category_code", "data1")

        def data2 = new ObjectData()
        data2.set("category_code", "data2")
        def validList = [data1, data2]


        def tree1 = new ProductCategoryTree()
        tree1.setCategoryCode("q")
        def tree2 = new ProductCategoryTree()
        tree2.setId("data1")
        tree2.setName("data1")
        tree2.setOrderField(1)
        tree2.setCategoryCode("data1")
        def categoryTreeData = [tree1, tree2]

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        and:
        productCategoryBizServiceTest.getRandomId() >> "1"

        when:
        productCategoryBizServiceTest.fillBaseFieldValue(user, validList, categoryTreeData)
        then:
        notThrown(Exception)
    }

    def "fillCategoryField arg is null"() {
        given:
        def actualList = []
        def fillCategoryData = []
        when:
        productCategoryBizServiceTest.fillCategoryField(user, actualList, fillCategoryData as Set<String>, "apiName")
        then:
        notThrown(Exception)
    }

    def "fillCategoryField arg not null"() {
        given:
        def data = new ObjectData()
        data.setId("1")
        def actualList = [data]
        def fillCategoryData = ["12", "1"]

        and:

        def asyncTaskProducer = Mock(AsyncTaskProducer)
        asyncTaskProducer.create(_, _, _, _) >> {}
        productCategoryBizServiceTest.setAsyncTaskProducer(asyncTaskProducer)
        when:
        productCategoryBizServiceTest.fillCategoryField(user, actualList, fillCategoryData as Set<String>, "apiName")
        then:
        notThrown(Exception)
    }

    def "categorySearchByCode wheres is null"() {
        given:
        def searchTemplateQuery = Mock(SearchTemplateQuery)

        and:
        def productCategoryService = Mock(ProductCategoryService)
        productCategoryService.handleCategoryFilters(user.getTenantId(), _ as String, _ as List<IFilter>) >> {}
        productCategoryBizServiceTest.setProductCategoryService(productCategoryService)

        when:
        productCategoryBizServiceTest.categorySearchByCode(user, searchTemplateQuery)
        then:
        notThrown(Exception)
    }

    def "categorySearchByCode wheres not null"() {
        given:
        def searchTemplateQuery = Mock(SearchTemplateQuery)
        def where = Mock(Wheres)

        where.getFilters() >> [Mock(IFilter)]
        searchTemplateQuery.getWheres() >> [where]


        and:
        def productCategoryService = Mock(ProductCategoryService)
        productCategoryService.handleCategoryFilters(user.getTenantId(), _ as String, _ as List<IFilter>) >> {}
        productCategoryBizServiceTest.setProductCategoryService(productCategoryService)

        when:
        productCategoryBizServiceTest.categorySearchByCode(user, searchTemplateQuery)
        then:
        notThrown(Exception)
    }


    def "transferFilters filters is null"() {
        given:
        def filters = []
        def transferField = ""


        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.transferFilters(user, filters, transferField)
        then:
        notThrown(Exception)
    }

    def "transferFilters filters is not null"() {
        given:
        String searchInfoJson = "{\"limit\":20,\"offset\":0,\"filters\":[{\"field_name\":\"category\",\"field_values\":[\"60335967325a970001f290de\"],\"operator\":\"EQ\"},{\"field_name\":\"shop_category_id\",\"field_values\":[\"60335967325a970001f290de\"],\"operator\":\"NIN\"}],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}"
        SearchTemplateQuery argQueryInfo = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(searchInfoJson);
        def transferField = ""

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        Map<String, String> map = new HashMap<>()
        map.put("1", "1")
        productCategoryUtils.getCategoryIdByCodes(user, _ as List<String>) >> map
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        and:
        productCategoryBizServiceTest.fillChildCategoryId(user, _ as IFilter, _ as String) >> {}

        when:
        productCategoryBizServiceTest.transferFilters(user, argQueryInfo.getFilters(), transferField)
        then:
        notThrown(Exception)
    }

    def "categorySearchById where is null"() {
        given:
        def searchTemplateQuery = Mock(SearchTemplateQuery)
        searchTemplateQuery.getWheres() >> []

        and:
        productCategoryBizServiceTest.transferFilters(user, _ as List<IFilter>, _ as String) >> {}
        when:
        productCategoryBizServiceTest.categorySearchById(user, searchTemplateQuery, "transferField")
        then:
        notThrown(Exception)
    }

    def "categorySearchById where is not null"() {
        given:
        def searchTemplateQuery = Mock(SearchTemplateQuery)
        def where = Mock(Wheres)

        where.getFilters() >> [Mock(IFilter)]
        searchTemplateQuery.getWheres() >> [where]

        and:
        productCategoryBizServiceTest.transferFilters(user, _ as List<IFilter>, _ as String) >> {}
        when:
        productCategoryBizServiceTest.categorySearchById(user, searchTemplateQuery, "transferField")
        then:
        notThrown(Exception)
    }

    def "oldCategoryTransferNewCategoryListFilters not isCloseOldProductCategory"() {
        given:
        def searchTemplateQuery = Mock(SearchTemplateQuery)
        def where = Mock(Wheres)

        where.getFilters() >> [Mock(IFilter)]
        searchTemplateQuery.getWheres() >> [where]

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.isNewCategorySearch(user.getTenantId()) >> true
        productCategoryBizServiceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> false
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)


        and:
        productCategoryBizServiceTest.categorySearchByCode(user, searchTemplateQuery) >> {}


        when:
        productCategoryBizServiceTest.oldCategoryTransferNewCategoryListFilters(user, searchTemplateQuery, "transferField")
        then:
        notThrown(Exception)
    }

    def "oldCategoryTransferNewCategoryListFilters isCloseOldProductCategory"() {
        given:
        def searchTemplateQuery = Mock(SearchTemplateQuery)
        def where = Mock(Wheres)

        where.getFilters() >> [Mock(IFilter)]
        searchTemplateQuery.getWheres() >> [where]

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.isNewCategorySearch(user.getTenantId()) >> true
        productCategoryBizServiceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)


        and:
        productCategoryBizServiceTest.categorySearchById(user, searchTemplateQuery, _ as String) >> {}


        when:
        productCategoryBizServiceTest.oldCategoryTransferNewCategoryListFilters(user, searchTemplateQuery, "transferField")
        then:
        notThrown(Exception)
    }


    def "handleNewCategoryListFilters not isCloseOldProductCategory"() {
        given:
        def searchTemplateQuery = Mock(SearchTemplateQuery)
        def where = Mock(Wheres)
        def argQueryStr = ""

        where.getFilters() >> [Mock(IFilter)]
        searchTemplateQuery.getWheres() >> [where]

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> false
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.handleNewCategoryListFilters(user, argQueryStr, searchTemplateQuery, "transferField", "shopField")
        then:
        notThrown(Exception)
    }

    def "handleNewCategoryListFilters isCloseOldProductCategory and argQueryStr is empty "() {
        given:
        def searchTemplateQuery = Mock(SearchTemplateQuery)
        def where = Mock(Wheres)
        def argQueryStr = ""

        where.getFilters() >> [Mock(IFilter)]
        searchTemplateQuery.getWheres() >> [where]

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.handleNewCategoryListFilters(user, argQueryStr, searchTemplateQuery, "transferField", "shopField")
        then:
        notThrown(Exception)
    }

    def "handleNewCategoryListFilters "() {
        given:
        def searchTemplateQuery = Mock(SearchTemplateQuery)
        def where = Mock(Wheres)
        String argQueryStr = "{\"limit\":20,\"offset\":0,\"filters\":[{\"field_name\":\"product_category_id_search\",\"field_values\":[\"60335967325a970001f290de\"],\"operator\":\"EQ\"},{\"field_name\":\"shop_category_id\",\"field_values\":[\"60335967325a970001f290de\"],\"operator\":\"NIN\"}],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}"

        where.getFilters() >> [Mock(IFilter)]
        searchTemplateQuery.getWheres() >> [where]
        searchTemplateQuery.getFilters() >> []

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.handleNewCategoryListFilters(user, argQueryStr, searchTemplateQuery, "transferField", "shopField")
        then:
        notThrown(Exception)
    }

    def "filterShopCategory "() {
        given:
        def filter1 = new Filter()
        filter1.setFieldName("shop_category_id")
        filter1.setOperator(Operator.HASANYOF)
        filter1.setFieldValues(["1"])
        List<IFilter> filters = [filter1]

        and:
        def treePathService = Mock(TreePathService)
        treePathService.getChildren(user, _, _) >> []
        productCategoryBizServiceTest.setTreePathService(treePathService)
        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.filterShopCategory(user, filters, null)
        then:
        notThrown(Exception)
    }

    def "fillOldCategoryMustField "() {
        given:
        def dataDocument = Mock(ObjectDataDocument)
        dataDocument.toObjectData() >> new ObjectData()
        when:
        productCategoryBizServiceTest.fillOldCategoryMustField(dataDocument)
        then:
        notThrown(Exception)
    }

    def "fillPidAndPath findObjectByCategoryCode is null"() {
        given:
        def needSearchCategoryCodeList = []
        def categoryPidMap = new HashMap()

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findObjectByCategoryCode(user, _, _) >> []
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.fillPidAndPath(user, needSearchCategoryCodeList as Set<String>, categoryPidMap)
        then:
        notThrown(Exception)
    }

    def "fillPidAndPath findObjectByCategoryCode isCloseOldProductCategory"() {
        given:
        def needSearchCategoryCodeList = []
        def categoryPidMap = new HashMap()
        categoryPidMap.put("1", "11")

        and:
        def data = new ObjectData()
        data.set("category_code", "1")

        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findObjectByCategoryCode(user, _, _) >> [data]
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)


        and:
        def treePathService = Mock(TreePathService)
        treePathService.fillPathWithTheseDataListNoUpdate(user, _ as List<IObjectData>, PRODUCT_CATEGORY_PATH, PID) >> []
        productCategoryBizServiceTest.setTreePathService(treePathService)

        and:
        def metaDataFindServiceExt = Mock(MetaDataFindServiceExt)
        metaDataFindServiceExt.bulkUpdateByFields(user, _, _) >> {}
        productCategoryBizServiceTest.setMetaDataFindServiceExt(metaDataFindServiceExt)

        when:
        productCategoryBizServiceTest.fillPidAndPath(user, needSearchCategoryCodeList as Set<String>, categoryPidMap)
        then:
        notThrown(Exception)
    }

    def "fillPidAndPath findObjectByCategoryCode not CloseOldProductCategory"() {
        given:
        def needSearchCategoryCodeList = []
        def categoryPidMap = new HashMap()
        categoryPidMap.put("1", "11")

        and:
        def data = new ObjectData()
        data.set("category_code", "1")

        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findObjectByCategoryCode(user, _, _) >> [data]
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> false
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)


        and:
        def metaDataFindServiceExt = Mock(MetaDataFindServiceExt)
        metaDataFindServiceExt.bulkUpdateByFields(user, _, _) >> {}
        productCategoryBizServiceTest.setMetaDataFindServiceExt(metaDataFindServiceExt)

        when:
        productCategoryBizServiceTest.fillPidAndPath(user, needSearchCategoryCodeList as Set<String>, categoryPidMap)
        then:
        notThrown(Exception)
    }

    def "initOrderField pid is null"() {
        given:
        def data = new ObjectData()
        data.set("category_code", "1")
        data.set("order_field", "1")

        and:
        productCategoryBizServiceTest.getDataFieldValue(data, PID, _) >> null
        productCategoryBizServiceTest.getDataFieldValue(data, ORDER_FIELD, _) >> "1"

        and:
        def serviceFacadeProxy = Mock(ServiceFacadeProxy)
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> [data]
        serviceFacadeProxy.findBySearchQuery(user, API_NAME, _ as SearchTemplateQuery) >> queryResult
        productCategoryBizServiceTest.setServiceFacadeProxy(serviceFacadeProxy)

        when:
        productCategoryBizServiceTest.initOrderField(user, data)
        then:
        notThrown(Exception)
    }

    def "initOrderField pid is not  null"() {
        given:
        def data = new ObjectData()
        data.set("category_code", "1")
        data.set("order_field", "1")

        and:
        productCategoryBizServiceTest.getDataFieldValue(data, PID, _) >> "1"
        productCategoryBizServiceTest.getDataFieldValue(data, ORDER_FIELD, _) >> "1"

        and:
        def serviceFacadeProxy = Mock(ServiceFacadeProxy)
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> [data]
        serviceFacadeProxy.findBySearchQuery(user, API_NAME, _ as SearchTemplateQuery) >> queryResult
        productCategoryBizServiceTest.setServiceFacadeProxy(serviceFacadeProxy)

        when:
        productCategoryBizServiceTest.initOrderField(user, data)
        then:
        notThrown(Exception)
    }

    def "initCodeField isCloseOldProductCategory"() {
        given:
        def data = new ObjectData()
        data.set("category_code", "1")
        data.set("order_field", "1")

        and:
        productCategoryBizServiceTest.getDataFieldValue(data, "code", _ as String) >> "1"
        productCategoryBizServiceTest.getRandomId() >> "1"
        productCategoryBizServiceTest.checkCode(_) >> {}

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.initCodeField(user, data)
        then:
        notThrown(Exception)
    }

    def "initCodeField isCloseOldProductCategory dataCode is null"() {
        given:
        def data = new ObjectData()
        data.set("category_code", "1")
        data.set("order_field", "1")

        and:
        productCategoryBizServiceTest.getDataFieldValue(data, "code", _ as String) >> ""
        productCategoryBizServiceTest.getRandomId() >> "1"
        productCategoryBizServiceTest.checkCode(_) >> {}

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.initCodeField(user, data)
        then:
        notThrown(Exception)
    }

    def "initCodeField not closeOldProductCategory"() {
        given:
        def data = new ObjectData()
        data.set("category_code", "1")
        data.set("order_field", "1")

        and:
        productCategoryBizServiceTest.getDataFieldValue(data, "code", _ as String) >> "1"
        productCategoryBizServiceTest.getRandomId() >> "1"
        productCategoryBizServiceTest.checkCode(_) >> {}

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> false
        productCategoryUtils.findMaxCode(user) >> "1"
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryBizServiceTest.initCodeField(user, data)
        then:
        notThrown(Exception)
    }

    def "getRandomId"() {
        given:
        def categoryRandomTimeUtil = Mock(CategoryRandomTimeUtil)
        categoryRandomTimeUtil.nextId() >> "1"
        productCategoryBizServiceTest.setCategoryRandomTimeUtil(categoryRandomTimeUtil)
        when:
        productCategoryBizServiceTest.getRandomId()
        then:
        notThrown(Exception)
    }

    def "sendSynchronizeDescribeMq isStopSyncCategoryTenant"() {
        given:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.isStopSyncCategoryTenant(user.getTenantId()) >> true
        when:
        productCategoryBizServiceTest.sendSynchronizeDescribeMq(user) >> "1"
        then:
        notThrown(Exception)
    }

    def "sendSynchronizeDescribeMq not stopSyncCategoryTenant"() {
        given:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.isStopSyncCategoryTenant(user.getTenantId()) >> false
        productCategoryBizServiceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def asyncTaskProducer = Mock(AsyncTaskProducer)
        asyncTaskProducer.create(_, _, _) >> {}
        productCategoryBizServiceTest.setAsyncTaskProducer(asyncTaskProducer)
        when:
        productCategoryBizServiceTest.sendSynchronizeDescribeMq(user)
        then:
        notThrown(Exception)
    }

    def "sendSynchronizeDescribeMqByGray >= NEW_CATEGORY_LIMIT "() {
        given:
        productCategoryBizServiceTest.getCategoryOptionSize(user) >> 10000
        when:
        productCategoryBizServiceTest.sendSynchronizeDescribeMqByGray(user)
        then:
        notThrown(Exception)
    }

    def "sendSynchronizeDescribeMqByGray add_to_db "() {
        given:
        productCategoryBizServiceTest.getCategoryOptionSize(user) >> 10
        when:
        productCategoryBizServiceTest.sendSynchronizeDescribeMqByGray(user, "add_to_db")
        then:
        notThrown(Exception)
    }

    def "sendSynchronizeDescribeMqByGray < NEW_CATEGORY_LIMIT "() {
        given:
        productCategoryBizServiceTest.getCategoryOptionSize(user) >> 10
        productCategoryBizServiceTest.sendSynchronizeDescribeMq(user) >> {}
        when:
        productCategoryBizServiceTest.sendSynchronizeDescribeMqByGray(user)
        then:
        notThrown(Exception)
    }

    def "getCategoryOptionSize"() {
        given:
        def describeEnhancer = Mock(DescribeEnhancer)
        describeEnhancer.fetchObject(user, _ as String) >> Mock(IObjectDescribe)
        productCategoryBizServiceTest.setDescribeEnhancer(describeEnhancer)
        when:
        productCategoryBizServiceTest.getCategoryOptionSize(user)
        then:
        notThrown(Exception)
    }

    def "initPathField"() {
        given:
        def treePathService = Mock(TreePathService)
        treePathService.initDataPath(user, _, API_NAME, PRODUCT_CATEGORY_PATH, PID) >> {}
        productCategoryBizServiceTest.setTreePathService(treePathService)
        when:
        productCategoryBizServiceTest.initPathField(user, Mock(ObjectData))
        then:
        notThrown(Exception)
    }

    def "changePathByParentId"() {
        given:
        def treePathService = Mock(TreePathService)
        treePathService.changePathByParentId(user, _, _, API_NAME, PRODUCT_CATEGORY_PATH) >> {}
        productCategoryBizServiceTest.setTreePathService(treePathService)
        when:
        productCategoryBizServiceTest.changePathByParentId(user, "", "")
        then:
        notThrown(Exception)
    }

    def "removeFieldsOfSpuSkuImport isCloseOldProductCategory"() {
        given:
        def fieldDescribe = Mock(IFieldDescribe)
        fieldDescribe.getApiName() >> "1"
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)
        when:
        productCategoryBizServiceTest.removeFieldsOfSpuSkuImport(user, [fieldDescribe])
        then:
        notThrown(Exception)
    }

    def "removeFieldsOfSpuSkuImport not closeOldProductCategory"() {
        given:
        def fieldDescribe = Mock(IFieldDescribe)
        fieldDescribe.getApiName() >> "1"
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> false
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)
        when:
        productCategoryBizServiceTest.removeFieldsOfSpuSkuImport(user, [fieldDescribe])
        then:
        notThrown(Exception)
    }

    def "removeLayoutCategoryField not isCloseOldProductCategory"() {
        given:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> false
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)
        when:
        productCategoryBizServiceTest.removeLayoutCategoryField(user, Mock(ILayout))
        then:
        notThrown(Exception)
    }

    def "removeLayoutCategoryField layout is null"() {
        given:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> true
        productCategoryBizServiceTest.setProductCategoryUtils(productCategoryUtils)
        when:
        productCategoryBizServiceTest.removeLayoutCategoryField(user, null)
        then:
        notThrown(Exception)
    }

    def "parsePaths"() {
        given:
        def map = new HashMap()
        map.put("1", "1.2.3")
        when:
        productCategoryBizServiceTest.parsePaths(map)
        then:
        notThrown(Exception)
    }

    def "asyncSyncDescribe isStopSyncCategoryTenant"() {
        given:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.isStopSyncCategoryTenant(user.getTenantId()) >> true
        productCategoryBizServiceTest.setCategoryStaticUtilService(categoryStaticUtilService)
        when:
        productCategoryBizServiceTest.asyncSyncDescribe(user, 1)
        then:
        notThrown(Exception)
    }

    def "asyncSyncDescribe"() {
        given:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.isStopSyncCategoryTenant(user.getTenantId()) >> false
        productCategoryBizServiceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def asyncTaskProducer = Mock(AsyncTaskProducer)
        asyncTaskProducer.create(_, _, _, _, _) >> {}
        productCategoryBizServiceTest.setAsyncTaskProducer(asyncTaskProducer)

        when:
        productCategoryBizServiceTest.asyncSyncDescribe(user, 1)
        then:
        notThrown(Exception)
    }

    def "getCurrentCategoryMapping ID"() {
        given:
        def categoryFieldApiName = "_id"
        def data = new ObjectData()
        data.setId("date")
        def dataList = [data]

        when:
        productCategoryBizServiceTest.getCurrentCategoryMapping(user, dataList, categoryFieldApiName)
        then:
        notThrown(Exception)
    }

    def "getCurrentCategoryMapping not ID"() {
        given:
        def categoryFieldApiName = "_id111"
        def data = new ObjectData()
        data.setId("date")
        def dataList = [data]

        and:
        productCategoryBizServiceTest.getDataFieldValue(_, categoryFieldApiName, "") >> ""
        when:
        productCategoryBizServiceTest.getCurrentCategoryMapping(user, dataList, categoryFieldApiName)
        then:
        notThrown(Exception)
    }
    def "getCurrentCategoryMapping not ID exists"() {
        given:
        def categoryFieldApiName = "code"
        def data = new ObjectData()
        data.setId("date")
        data.set("code", "code")
        def dataList = [data]

        and:
        productCategoryBizServiceTest.getDataFieldValue(_, categoryFieldApiName, "") >> "code"
        def treePathService = Mock(TreePathService)
        treePathService.getCurrentPathByIds(user, _, API_NAME, PRODUCT_CATEGORY_PATH)
        productCategoryBizServiceTest.setTreePathService(treePathService)

        when:
        productCategoryBizServiceTest.getCurrentCategoryMapping(user, dataList, categoryFieldApiName)
        then:
        notThrown(Exception)
    }
}
