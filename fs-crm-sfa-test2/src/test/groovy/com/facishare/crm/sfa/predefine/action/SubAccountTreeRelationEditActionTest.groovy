package com.facishare.crm.sfa.predefine.action

import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.lto.utils.CommonTreeRelationUtil
import com.facishare.crm.sfa.utilities.util.AccountTreeUtil
import com.facishare.paas.appframework.core.model.ActionContext
import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.action.AbstractStandardEditAction
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.service.IObjectDataService
import com.facishare.paas.metadata.util.SpringUtil
import com.google.common.collect.Lists
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.api.support.membermodification.MemberMatcher
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([SpringUtil.class, StandardUpdateImportDataAction.class, CommonTreeRelationUtil.class, BaseImportDataAction.class])
@PowerMockIgnore(["javax.management.*", "org.xml.sax.*", "javax.xml.*", "com.sun.org.apache.xerces.*"])
@SuppressStaticInitializationFor([
        "com.facishare.paas.I18N",
        "com.facishare.crm.sfa.utilities.util.AccountTreeUtil",
        "com.facishare.crm.sfa.utilities.util.CommonTreeRelationUtil",
        "com.facishare.paas.appframework.core.predef.action.AbstractStandardEditAction",
        "com.facishare.paas.appframework.common.util.AppFrameworkConfig",
        "com.facishare.paas.appframework.metadata.relation.EditCalculateParam",
])
class SubAccountTreeRelationEditActionTest extends EnhancedBaseGroovyTest {

    @Shared
    SubAccountTreeRelationEditAction action

    @Shared
    ApplicationContext applicationContext
    @Shared
    ServiceFacade serviceFacade
    @Shared
    IObjectDataService objectDataService
    @Shared
    ActionContext actionContext

    def setupSpec() {
        removeConfigFactory()
        removeI18N()
        initSpringContext()
        applicationContext = PowerMockito.mock(ApplicationContext.class)
        serviceFacade = PowerMockito.mock(ServiceFacade.class)
        actionContext = createActionContext()
    }

    def setup() {
        PowerMockito.mockStatic(SpringUtil.class)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(applicationContext)

        action = PowerMockito.spy(new SubAccountTreeRelationEditAction())

        PowerMockito.suppress(MemberMatcher.methodsDeclaredIn(AbstractStandardEditAction))
        Whitebox.setInternalState(action, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(action, "actionContext", actionContext)
        Whitebox.setInternalState(action, "afterUpdate", new CommonTreeRelationUtil.AfterUpdate())
    }


    def "before"() {
        given: "needTriggerApprovalFlow"
        def  arg = new BaseObjectSaveAction.Arg()
        arg.setObjectData(objectData)
        PowerMockito.stub(PowerMockito.method(AccountTreeUtil.class, "replaceNameField", User.class, IObjectData.class, IObjectData.class))
                .toReturn(null)
        PowerMockito.stub(PowerMockito.method(CommonTreeRelationUtil.class, "beforeUpdate", User.class, List.class, List.class, ServiceFacade.class))
                .toReturn(null)
        when: "account"
        def result = action.before(arg)
        then: "no exceptions are thrown and account is merge"
        notThrown(Exception)
        where:
        objectData                                                                                                     | configValue
        ObjectDataDocument.of(getObjectData(Lists.newArrayList("_id","is_root"),Lists.newArrayList("xxxx",true)))      | "1"
    }

}
