package com.facishare.crm.sfa.predefine.service

import com.beust.jcommander.internal.Maps

import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.predefine.SFAPreDefineObject
import com.facishare.crm.sfa.predefine.privilege.AccountDataPrivilegeProvider
import com.facishare.crm.sfa.predefine.service.model.ChangeAccountPath
import com.facishare.crm.sfa.predefine.service.model.GetAccountTree
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission
import com.facishare.crm.sfa.utilities.constant.AccountConstants
import com.facishare.crm.sfa.utilities.util.*
import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.util.AppIdMapping
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.metadata.MetaDataFindService
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService
import com.facishare.paas.appframework.privilege.FunctionPrivilegeServiceImpl
import com.facishare.paas.appframework.privilege.dto.Permissions
import com.facishare.paas.license.arg.QueryModuleArg
import com.facishare.paas.license.http.LicenseClient
import com.facishare.paas.metadata.api.action.IActionContext
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.util.SpringUtil
import com.facishare.privilege.api.UserPrivilegeRestService
import com.facishare.privilege.api.module.user.BatchGetObjectFieldByUserVo
import com.google.common.collect.HashBiMap
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared

import java.lang.reflect.InvocationTargetException
import java.lang.reflect.Method

import static org.mockito.ArgumentMatchers.*

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([I18N.class,UdobjGrayConfig.class,MetaDataFindService.class,ServiceFacade.class,SpringUtil.class,LicenseClient.class,LoggerFactory.class,GrayUtil.class, AppIdMapping.class, ContactAtlasUtil.class,AccountPathUtil.class, AccountUtil.class, ObjectPoolUtil.class])
// 限制 RequestUtil, AccountAddrService 类里的静态代码块初始化, 这里会加载一些静态资源，可以禁止初始化。
@SuppressStaticInitializationFor([
        "com.facishare.paas.appframework.core.util.RequestUtil",
        "com.facishare.paas.I18N"
])
class AccountPathServiceTest extends EnhancedBaseGroovyTest {
    @Shared
    private User user
    @Shared
    AccountPathService accountPathService
    @Shared
    ServiceFacade serviceFacade
    @Shared
    UserPrivilegeRestService userPrivilegeRestService
    @Shared
    AccountDataPrivilegeProvider accountDataPrivilegeProvider
    @Shared
    ConfigService configService
    @Shared
    ApplicationContext applicationContext

    def setupSpec() {

        removeConfigFactory()
        removeI18N()
        initSpringContext()
        user = User.systemUser("82681")
        applicationContext = PowerMockito.mock(ApplicationContext)
        PowerMockito.mockStatic(SpringUtil)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(applicationContext)
        accountPathService = PowerMockito.spy(new AccountPathService())
         serviceFacade = PowerMockito.mock(ServiceFacade)
         userPrivilegeRestService = PowerMockito.mock(UserPrivilegeRestService)
         accountDataPrivilegeProvider = PowerMockito.mock(AccountDataPrivilegeProvider)
         configService = PowerMockito.mock(ConfigService)
        Whitebox.setInternalState(AccountPathService, "log", Mock(Logger))
        Whitebox.setInternalState(AppIdMapping.class, "appIdMapping", HashBiMap.create(Maps.newHashMap("prm","prm")));
        Whitebox.setInternalState(AccountPathService.class, "MUST_SHOW_FIELDS",  Lists.newArrayList("name"));
        Whitebox.setInternalState(AccountPathService.class, "PRIVILEGE_LIST",  2);
    }

    def setup() {
        PowerMockito.mockStatic(SpringUtil)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(applicationContext)
        PowerMockito.stub(PowerMockito.method(I18N.class, "text", String.class))
                .toReturn("xx")
        PowerMockito.stub(PowerMockito.method(UdobjGrayConfig.class, "isAllow", String.class, String.class))
                .toReturn(true)
    }

/**
 *
 * @return
 */
    def "get_account_tree"() {
        given:
        def arg = new GetAccountTree.Arg()
        arg.setAccountId(accountId)

        Whitebox.setInternalState(accountPathService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(accountPathService, "userPrivilegeRestService", userPrivilegeRestService)

        Whitebox.setInternalState(accountPathService, "accountDataPrivilegeProvider", accountDataPrivilegeProvider)
        Whitebox.setInternalState(accountPathService, "configService", configService)
        PowerMockito.stub(PowerMockito.method(GrayUtil.class, "isSerializeEmptyForAccountPath", String.class))
                .toReturn(isSerialize)
        PowerMockito.stub(PowerMockito.method(AccountPathUtil.class, "getAccountList", User.class,Set.class, List.class))
                .toReturn(allTreeAccountList)
        PowerMockito.stub(PowerMockito.method(AccountPathUtil.class, "getObjectDescribe", User.class))
                .toReturn(accountDescribe)
        PowerMockito.stub(PowerMockito.method(ContactAtlasUtil.class, "asyncFillDataByField", User.class,List.class,IObjectDescribe.class,List.class))
                .toReturn(null)
        PowerMockito.stub(PowerMockito.method(ContactAtlasUtil.class, "asyncFillData", User.class,List.class,IObjectDescribe.class))
                .toReturn(accountDescribe)
        PowerMockito.doReturn(objectData).when(serviceFacade, "findObjectDataIncludeDeleted", any(), any(), any())
        PowerMockito.doReturn(new ObjectData("price_book_id": "pb1")).when(serviceFacade, "findObjectData", any(IActionContext) as IActionContext, anyString(), any(IObjectDescribe) as IObjectDescribe)
        PowerMockito.doReturn(permissionsMap).when(serviceFacade, "checkDataPrivilege", any(), any(), any(), any())
        PowerMockito.doReturn("").when(configService,"findTenantConfig",any(),any())
        PowerMockito.doReturn(null).when(userPrivilegeRestService, "batchGetObjectFieldByUser", any(), any());
        PowerMockito.doReturn(Maps.newHashMap()).when(accountDataPrivilegeProvider,"getPoolPermissionsMap",any(),any(),any())

        def functionPrivilegeService = PowerMockito.spy(new FunctionPrivilegeServiceImpl())
        Whitebox.setInternalState(AccountPathService, "functionPrivilegeService", functionPrivilegeService)
        PowerMockito.doReturn(Maps.newHashMap("AccountObj",Maps.newHashMap("name",true))).when(functionPrivilegeService,"batchFunPrivilegeCheck",any(User.class), anyList(), anyList())

        when:
        accountPathService.getAccountTree(getServiceContext(user,"AccountPathService", "getAccountTree"), arg)
        then:
        true
        where:
        accountId | accountDescribe                                       |objectData                                                                                                               |sortFields                 |allTreeAccountList    |isSerialize |permissionsMap
//        null      | null                                                  |null                                                                                                                     |null                       |null                  |true        |null
//        "dewd"    | getDescribe(SFAPreDefineObject.Account.getApiName())  |null                                                                                                                     |null                       |null                  |true        |null
        "32dew"   | getDescribe(SFAPreDefineObject.Account.getApiName())  |getObjectData(Lists.newArrayList(AccountConstants.Field.FIELD_ACCOUNT_PATH,"_id"), Lists.newArrayList("32dew","dewdew")) |Lists.newArrayList("name") | Lists.newArrayList() |true        |Maps.newHashMap()
        "dewd"    | getDescribe(SFAPreDefineObject.Account.getApiName())  |getObjectData(Lists.newArrayList(AccountConstants.Field.FIELD_ACCOUNT_PATH,"_id"), Lists.newArrayList("32dew","dewdew")) |Lists.newArrayList("name") | Lists.newArrayList() |false       |Maps.newHashMap();
    }
/**
 *
 * @return
 */
    def "setButtonsInfo"() {
        given:

        when:
        // 使用反射调用私有方法
        Method method = AccountPathService.class.getDeclaredMethod("setButtonsInfo", ServiceContext.class, Map.class, boolean.class, List.class)
        method.setAccessible(true);
        method.invoke(accountPathService, getServiceContext(user,"AccountPathService", "setButtonsInfo"),actionCodePrivilegeMapping,parentAccountIdFieldPrivilege,currentAccountList);
        then:
        true
        where:
        actionCodePrivilegeMapping                                                                     |parentAccountIdFieldPrivilege |currentAccountList
        null                                                                                           |true                          | null
        null                                                                                           |true                          | Lists.newArrayList(getObjectData(Lists.newArrayList("name")))
        Maps.newHashMap("AddSuperior",true,"AddSubordinate",true,"RelieveSuperior",true,"Edit",true)   |true                          | Lists.newArrayList(getObjectData(Lists.newArrayList("name")));
    }

/**
 *
 * @return
 */
    def "getFieldPrivilege"() {
        given:



        Whitebox.setInternalState(accountPathService, "userPrivilegeRestService", userPrivilegeRestService)
        PowerMockito.doReturn(result).when(userPrivilegeRestService,"batchGetObjectFieldByUser",any(),any())
        when:
        // 使用反射调用私有方法
        Method method = AccountPathService.class.getDeclaredMethod("getFieldPrivilege", ServiceContext.class)
        method.setAccessible(true)
        method.invoke(accountPathService, getServiceContext(user,"AccountPathService", "setButtonsInfo"))
        then:
        true
        where:
        result                                                  |parentAccountIdFieldPrivilege
        null                                                    |true
        getBatchGetObjectFieldByUserVo("dew",1)                 |true
        getBatchGetObjectFieldByUserVo("parent_account_id",2)   |true
        getBatchGetObjectFieldByUserVo("parent_account_id",3)   |true       ;
    }

/**
 *
 * @return
 */
    @Test
    def "getObjectDataForLimit"() {
        given:

        Whitebox.setInternalState(accountPathService, "userPrivilegeRestService", userPrivilegeRestService)

        PowerMockito.stub(PowerMockito.method(GrayUtil.class, "isShowOnlyCurrentForAccountPath", String.class))
                .toReturn(isShowOnly)

        when:
        // 使用反射调用私有方法
        Method method = AccountPathService.class.getDeclaredMethod("getObjectDataForLimit", User.class,String.class,String.class,List.class)
        method.setAccessible(true)
        method.invoke(accountPathService, this.user,currentAccountId,currentAccountPath,allTreeAccountList)
        then:
        true
        where:
        allTreeAccountList                                                                                  |currentAccountId|currentAccountPath|isShowOnly
        Lists.newArrayList()                                                                                |"true"          |"d3d"             |true
        getObjectDataList(Lists.newArrayList("_id","name",AccountConstants.Field.FIELD_ACCOUNT_PATH),1001)  |"true"          |"xxxx"            |true
        getObjectDataList(Lists.newArrayList("_id","name",AccountConstants.Field.FIELD_ACCOUNT_PATH),1001)  |"true"          |"3434"            |false
        getObjectDataList(Lists.newArrayList("_id","name",AccountConstants.Field.FIELD_ACCOUNT_PATH),1001)  |"true"          |"xxxx"            |false
        getList(1001)                                                                                       |"true"          |"xxxx"            |false;
    }

    @Test(expected = ValidateException.class)
    def "changeAccountPath"() {
        given:

        accountPathService = PowerMockito.spy(new AccountPathService())

        Whitebox.setInternalState(accountPathService, "userPrivilegeRestService", userPrivilegeRestService)

        when:
        // 使用反射调用私有方法

        try {
            Method method = AccountPathService.class.getDeclaredMethod("changeAccountPath", User.class,List.class)
            method.setAccessible(true)
            method.invoke(accountPathService, this.user,allTreeAccountList)
        } catch (InvocationTargetException ite) {
            // 提取实际的异常
            def actualException = ite.targetException
            if (actualException instanceof ValidateException) {
                // System.out.println("xxxxxx")
            } else {
                throw ite
            }
        }
        // 调用 changeAccountPath 方法

        then:
        true
        where:
        allTreeAccountList                                                                                                |isShowOnly
        Lists.newArrayList()                                                                                              |true
        getObjectDataList(Lists.newArrayList("_id","name",AccountConstants.Field.OWNER_MODIFIED_TIME),1001)               |true
        getObjectDataListDifferentValue(Lists.newArrayList("_id","name",AccountConstants.Field.FIELD_ACCOUNT_PATH),2001)  |false
        getList(1001)                                                                                                     |false;
    }


    def "changeAccountList"() {
        given:

        accountPathService = PowerMockito.spy(new AccountPathService())

        Whitebox.setInternalState(accountPathService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(accountPathService, "accountDataPrivilegeProvider", accountDataPrivilegeProvider)

        PowerMockito.doReturn(invisibleFields).when(serviceFacade,"getUnauthorizedFields",any(User.class) as User,anyString())
        PowerMockito.doReturn(isCrmAdmin).when(serviceFacade,"isAdmin",any())
        PowerMockito.doReturn(poolPermissionsMap).when(accountDataPrivilegeProvider,"getPoolPermissionsMap",any(),any(),any())

        PowerMockito.stub(PowerMockito.method(ObjectPoolUtil.class, "getHideFields", String.class, String.class,List.class))
                .toReturn(Lists.newArrayList(Maps.newHashMap("pool_id","xxxx","field_name","xxxx")))

        when:
        // 使用反射调用私有方法
        Method method = AccountPathService.class.getDeclaredMethod("changeAccountList", User.class,List.class,Map.class,List.class)
        method.setAccessible(true)
        method.invoke(accountPathService, this.user,allTreeAccountList,permissionsMap,sortFields)
        then:
        true
        where:
        allTreeAccountList                                                                                                                                               |isCrmAdmin|permissionsMap                                     |sortFields                                                          |poolHideFields                                                                                     |invisibleFields   |poolPermissionsMap
        getObjectDataList(Lists.newArrayList("name"),2)                                                                                                                  |true      |Maps.newHashMap()                                  | Lists.newArrayList()                                               |Maps.newHashMap()                                                                                  |null              |Maps.newHashMap()
        getObjectDataList(Lists.newArrayList("_id","name"),2)                                                                                                            |false     |Maps.newHashMap("xxxx", Permissions.NO_PERMISSION) | Lists.newArrayList(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID) |Maps.newHashMap()                                                                                  |null              |Maps.newHashMap()
        getObjectDataList(Lists.newArrayList("_id","name"),2)                                                                                                            |false     |Maps.newHashMap("xxxx", Permissions.NO_PERMISSION) | Lists.newArrayList(AccountConstants.Field.FIELD_ACCOUNT_PATH)      |Maps.newHashMap()                                                                                  |null              |Maps.newHashMap()
        Lists.newArrayList(getObjectData(Lists.newArrayList("_id",AccountConstants.Field.HIGH_SEAS_ID,"owner"),Lists.newArrayList("xxxx","xxxx", Lists.newArrayList()))) |false     |Maps.newHashMap("xxxf", Permissions.NO_PERMISSION) | Lists.newArrayList(AccountConstants.Field.FIELD_ACCOUNT_PATH)      |Maps.newHashMap("xxxx", Lists.newArrayList("namx",AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID)) |Sets.newHashSet() |Maps.newHashMap()
        Lists.newArrayList(getObjectData(Lists.newArrayList("_id",AccountConstants.Field.HIGH_SEAS_ID,"owner"),Lists.newArrayList("xxxx","xxxx", Lists.newArrayList()))) |false     |Maps.newHashMap("xxxf", Permissions.NO_PERMISSION) | Lists.newArrayList(AccountConstants.Field.FIELD_ACCOUNT_PATH)      |Maps.newHashMap("xxxx", Lists.newArrayList("namx",AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID)) |Sets.newHashSet() |Maps.newHashMap("xxxx", ObjectPoolPermission.ObjectPoolPermissions.POOL_MEMBER);
    }

    def "getPoolHideFields"() {
        given:


        PowerMockito.stub(PowerMockito.method(ObjectPoolUtil.class, "getHideFields", String.class, String.class,List.class))
                .toReturn(hideFields)
        when:
        // 使用反射调用私有方法
        Method method = AccountPathService.class.getDeclaredMethod("getPoolHideFields", User.class,Set.class)
        method.setAccessible(true)
        method.invoke(accountPathService, this.user,highSeasIds)
        then:
        true
        where:
        highSeasIds                  |hideFields
        Sets.newHashSet("xxxx")      |Lists.newArrayList(Maps.newHashMap("pool_id","xxxx","field_name","xxxx"))    ;
    }


    def "get_account_path_header"() {
        given:
        accountPathService = PowerMockito.spy(new AccountPathService())

        Whitebox.setInternalState(accountPathService, "configService", configService)
        Whitebox.setInternalState(accountPathService, "serviceFacade", serviceFacade)
        PowerMockito.stub(PowerMockito.method(AccountPathUtil.class, "getObjectDescribe", User.class))
                .toReturn(objectDescribe)

        PowerMockito.doReturn("").when(configService,"findTenantConfig",any(),any())
        PowerMockito.doReturn(isAdmin).when(serviceFacade,"isAdmin",any())
        when:
        // 使用反射调用私有方法
        accountPathService.getAccountPathHeader( getServiceContext(user,"AccountPathService", "getAccountPathHeader"))
        then:
        true
        where:
        objectDescribe                                            |sortFields|isAdmin
        getDescribe(SFAPreDefineObject.Account.getApiName())      |Lists.newArrayList("pool_id","xxxx","field_name","xxxx") |true   ;
    }

    def "changeAccountPath11"() {
        given:
        def arg = new ChangeAccountPath.Arg();
        arg.setObjectIds(objectIds)

        PowerMockito.stub(PowerMockito.method(AccountPathUtil.class, "getObjectDescribe", User.class))
                .toReturn(getDescribe(SFAPreDefineObject.Account.getApiName()))
        PowerMockito.stub(PowerMockito.method(AccountPathUtil.class, "getAccountListByIdWithDeleted", User.class, IObjectDescribe.class, List.class, List.class))
                .toReturn(Lists.newArrayList())
        PowerMockito.stub(PowerMockito.method(AccountPathUtil.class, "changeAccountPath", User.class, List.class, Map.class, Boolean.class, String.class, List.class))
                .toReturn(Lists.newArrayList())
        PowerMockito.stub(PowerMockito.method(AccountPathUtil.class, "getAccountListByPathWithDeleted", User.class, IObjectDescribe.class, Set.class))
                .toReturn(Lists.newArrayList())
        when:
        // 使用反射调用私有方法
        accountPathService.changeAccountPath( getServiceContext(user,"AccountPathService", "changeAccountPath"),arg)
        then:
        true
        where:
        objectIds                    |sortFields
        null                         |true
        Lists.newArrayList("xxxxx")  |true   ;
    }

    def "getSortFields"() {
        given:
        accountPathService = PowerMockito.spy(new AccountPathService())

        Whitebox.setInternalState(accountPathService, "configService", configService)
        PowerMockito.stub(PowerMockito.method(AccountPathUtil.class, "getObjectDescribe", User.class))
                .toReturn(objectDescribe)

        PowerMockito.doReturn(queryRst).when(configService,"findTenantConfig",any(),any())
        when:
        // 使用反射调用私有方法
        Method method = AccountPathService.class.getDeclaredMethod("getSortFields", User.class, IObjectDescribe.class)
        method.setAccessible(true)
        method.invoke(accountPathService, this.user,objectDescribe)
        then:
        true
        where:
        objectDescribe                                            |queryRst |isAdmin
        getDescribe(SFAPreDefineObject.Account.getApiName())      |"xxxx"   |true   ;
    }

    def "get_has_account_hierarchy_license"() {
        given:
        Mockito.reset(accountPathService)
        def licenseClient = Mock(LicenseClient){
            queryModule(_ as QueryModuleArg) >> result
        }
        Whitebox.setInternalState(accountPathService, "licenseClient", licenseClient)
        when:
        // 使用反射调用私有方法
        accountPathService.getHasAccountHierarchyLicense( getServiceContext(user,"AccountPathService", "getAccounxtPathHeader"))
        then:
        true
        where:
        result                                            |isAdmin
        null                                              |true
        getModuleInfoResult("null")                       |true
        getModuleInfoResult("account_hierarchy_app")      |true   ;
    }

    def "getFunctionPrivilege"() {
        given:

        def functionPrivilegeService = PowerMockito.spy(new FunctionPrivilegeServiceImpl())

        PowerMockito.when(applicationContext.getBean(FunctionPrivilegeService.class)).thenReturn(functionPrivilegeService)
        Whitebox.setInternalState(AccountPathService, "functionPrivilegeService", functionPrivilegeService)

        PowerMockito.doReturn(result).when(functionPrivilegeService,"batchFunPrivilegeCheck",any(User.class), anyList(), anyList())
        when:
        // 使用反射调用私有方法
        Method method = AccountPathService.class.getDeclaredMethod("getFunctionPrivilege", ServiceContext.class)
        method.setAccessible(true)
        method.invoke(accountPathService, getServiceContext(user,"AccountPathService", "getAccountPathHeader"))
        then:
        true
        where:
        result                                                    |isAdmin
        null                                                      |true
        Maps.newHashMap("AccountObj",Maps.newHashMap("name",true))|true;
    }

    BatchGetObjectFieldByUserVo.Result getBatchGetObjectFieldByUserVo(String field,Integer num){
        BatchGetObjectFieldByUserVo.Result result =new BatchGetObjectFieldByUserVo.Result();
        List<BatchGetObjectFieldByUserVo.ObjectFieldPermission> objectFieldPermissions = new ArrayList<>()
        BatchGetObjectFieldByUserVo.ObjectFieldPermission objectFieldPermission = new BatchGetObjectFieldByUserVo.ObjectFieldPermission()
        objectFieldPermission.setFieldPermission(Maps.newHashMap(field,num))
        objectFieldPermissions.add(objectFieldPermission)
        result.setObjectFieldPermissions(objectFieldPermissions)
        return result
    }

    List<ObjectData> getList(Integer num){
        List<ObjectData> getList = getObjectDataListDifferentValue(Lists.newArrayList("name",AccountConstants.Field.FIELD_ACCOUNT_PATH),num);
        getList.add(getObjectData(Lists.newArrayList(AccountConstants.Field.FIELD_ACCOUNT_PATH),Lists.newArrayList("xxxxxxxxx")))
        return getList
    }
}
