package com.facishare.crm.sfa.predefine.service

import com.beust.jcommander.internal.Maps
import com.facishare.crm.openapi.Utils
import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.lto.objectlimit.ObjectLimitService
import com.facishare.crm.sfa.lto.utils.CommonTreeRelationUtil
import com.facishare.crm.sfa.model.ObjectLimitRuleModel
import com.facishare.crm.sfa.predefine.SFAPreDefineObject
import com.facishare.crm.sfa.predefine.action.SubAccountTreeRelationUpdateImportDataAction
import com.facishare.crm.sfa.predefine.service.SfaCrmAccountService
import com.facishare.crm.sfa.predefine.service.model.AccountServiceModels
import com.facishare.crm.sfa.predefine.service.model.GetAccountSummaryInfo
import com.facishare.crm.sfa.utilities.constant.AccountConstants
import com.facishare.crm.sfa.utilities.proxy.FeedsSaleActionProxy
import com.facishare.crm.sfa.utilities.proxy.model.FeedsModel
import com.facishare.crm.sfa.utilities.util.*
import com.facishare.paas.appframework.common.service.dto.QueryPhoneNumberInformation
import com.facishare.paas.appframework.coordination.AccountCostContentServiceProxy
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction
import com.facishare.paas.appframework.core.predef.service.ObjectLogService
import com.facishare.paas.appframework.core.predef.service.dto.log.GetSnapShotForWeb
import com.facishare.paas.appframework.core.util.RequestUtil
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.license.LicenseService
import com.facishare.paas.appframework.metadata.MetaDataComputeService
import com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl
import com.facishare.paas.appframework.metadata.OutDataPrivilege
import com.facishare.paas.appframework.metadata.dto.sfa.CustomerLimit
import com.facishare.paas.appframework.privilege.DataPrivilegeService
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.ISelectOption
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.PhoneNumberFieldDescribe
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe
import com.facishare.paas.metadata.impl.describe.SelectOption
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl
import com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl
import com.facishare.paas.metadata.support.GDSHandler
import com.facishare.paas.metadata.util.SpringUtil
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.api.support.membermodification.MemberMatcher
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.Logger
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared

import java.lang.reflect.Method

import static org.mockito.ArgumentMatchers.*

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([UdobjGrayConfig.class,CommonUtil.class,RequestUtil.class,RequestContextManager.class, GrayUtil.class, NewOpportunityUtil.class,CustomerAccountConfig.class, AccountUtil.class,SpringUtil.class, ObjectLimitUtil.class])
// 限制 RequestUtil, AccountAddrService 类里的静态代码块初始化, 这里会加载一些静态资源，可以禁止初始化。
@SuppressStaticInitializationFor(["com.facishare.paas.appframework.core.util.RequestUtil",
"com.facishare.paas.appframework.core.util.UdobjGrayConfig"])
class SfaCrmAccountServiceTest extends EnhancedBaseGroovyTest {
    @Shared
    SfaCrmAccountService sfaCrmAccountService
    @Shared
    ServiceFacade serviceFacade
    @Shared
    ObjectDescribeServiceImpl objectDescribeService
    @Shared
    ApplicationContext applicationContext
    @Shared
    User user
    def setupSpec() {

        removeConfigFactory()
        removeI18N()
        initSpringContext()
        PowerMockito.mockStatic(SpringUtil.class)
        applicationContext = PowerMockito.mock(ApplicationContext.class)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(applicationContext)
        sfaCrmAccountService = PowerMockito.spy(new SfaCrmAccountService())
        applicationContext = PowerMockito.mock(ApplicationContext.class)
        user = User.systemUser("82681")
        objectDescribeService = PowerMockito.mock(ObjectDescribeServiceImpl.class)
        serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(SfaCrmAccountService, "log", Mock(Logger))
    }

    def setup() {
        PowerMockito.mockStatic(SpringUtil.class)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(applicationContext)

        PowerMockito.stub(PowerMockito.method(UdobjGrayConfig.class, "isAllow", String.class, String.class))
                .toReturn(true)
    }
/**
 *
 * @return
 */
    def "get_account_relation_object_list"() {
        given:

        def licenseService = PowerMockito.mock(LicenseService)
        Whitebox.setInternalState(sfaCrmAccountService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(sfaCrmAccountService, "licenseService", licenseService)
        PowerMockito.stub(PowerMockito.method(NewOpportunityUtil.class, "getIsChangeToNewOpportunity", User.class))
                .toReturn(getIsChangeToNewOpportunity)
        PowerMockito.doReturn(appVersion).when(licenseService,"getVersion",any());
//        PowerMockito.doNothing().when(accountAddrService, "fillInfo", any(), any(), any(), eq(includeAllData), any(), any(), any());
        when:
        sfaCrmAccountService.getAccountRelationObjectList(getServiceContext(user,"SfaCrmAccountService", "getAccountRelationObjectList"))
        then:
        true
        where:
        getIsChangeToNewOpportunity | appVersion
        true                        | "1.0"
        false                       | "1.0"      ;
    }

    def "get_account_summary_info"() {
        given:
        def arg = new GetAccountSummaryInfo.GetAccountSummaryInfoArg();
        arg.setObjectId(objectId)

        def metaDataComputeService = PowerMockito.mock(MetaDataComputeService)
        Whitebox.setInternalState(sfaCrmAccountService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(sfaCrmAccountService, "metaDataComputeService", metaDataComputeService)
        PowerMockito.stub(PowerMockito.method(GrayUtil.class, "isGrayStatisticsAmount", String.class))
                .toReturn(isGrayStatisticsAmount)
        PowerMockito.stub(PowerMockito.method(NewOpportunityUtil.class, "getIsChangeToNewOpportunity",User.class))
                .toReturn(isGrayStatisticsAmount)
        PowerMockito.stub(PowerMockito.method(UdobjGrayConfig.class, "isAllow", String.class, String.class))
                .toReturn(true)
        PowerMockito.doReturn(isCrmAdmin).when(serviceFacade,"isAdmin",any());
        PowerMockito.doReturn(new BigDecimal(100)).when(metaDataComputeService,"getAggregateResult",any(User.class),anyString(),anyString(),anyString(),anyInt(),any(SearchTemplateQuery.class));
        PowerMockito.doReturn(objectDataList).when(serviceFacade).findObjectDataByIdsIncludeDeleted(any(User) as User, anyList(), anyString())
        when:
        sfaCrmAccountService.getAccountSummaryMoney(getServiceContext(user,"SfaCrmAccountService", "getAccountSummaryMoney"),arg)
        then:
        true
        where:
        objectDataList                                                      | objectId | isCrmAdmin|objectRightsInfos     |lookupRoles                |isGrayStatisticsAmount|allRelatedRoles
        Lists.newArrayList(getObjectData(Lists.newArrayList("_id","name"))) |"nu"     | true      |getObjectRightsInfos()| Lists.newArrayList("name")|true                  |null
        Lists.newArrayList(getObjectData(Lists.newArrayList("_id","name"))) |"nu"     | true      |getObjectRightsInfos()| Lists.newArrayList("name")|true                  |Maps.newHashMap("account_main_data_id", Lists.newArrayList("name"))
//        Lists.newArrayList(getObjectData(Lists.newArrayList("_id","name"))) |"nu"     | false     |getObjectRightsInfos()| Lists.newArrayList("name")|false                 |Maps.newHashMap("account_main_data_id", Lists.newArrayList("name"));
    }

    def "getAmontsInfo method"() {
        given:
        def arg = new GetAccountSummaryInfo.GetAccountSummaryInfoArg();
        arg.setObjectId(objectId)

        def dataPrivilegeService = PowerMockito.mock(DataPrivilegeService)
        def metaDataComputeService = PowerMockito.mock(MetaDataComputeService)

        Whitebox.setInternalState(sfaCrmAccountService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(sfaCrmAccountService, "dataPrivilegeService", dataPrivilegeService)
        Whitebox.setInternalState(sfaCrmAccountService, "metaDataComputeService", metaDataComputeService)
        PowerMockito.doReturn(outDataPrivilege).when(dataPrivilegeService,"getOutDataPrivilege",any(), any(), any());

        PowerMockito.stub(PowerMockito.method(RequestContextManager.class, "getContext"))
                .toReturn(serviceContexta.getRequestContext())
        PowerMockito.stub(PowerMockito.method(CustomerAccountConfig.class, "isCustomerAccount735Tenant",String.class))
                .toReturn(isCustomerAccount735Tenant)

        PowerMockito.stub(PowerMockito.method(UdobjGrayConfig.class, "isAllow", String.class, String.class))
                .toReturn(true)
//        PowerMockito.doReturn(getAggregateResult).when(metaDataComputeService, "getAggregateResult", this.user, anyString(), anyString(), anyString(), anyInt(), any(SearchTemplateQuery) as SearchTemplateQuery);
        arg.setObjectId(objectId)
        when:
        // 使用反射调用私有方法
        Method method = SfaCrmAccountService.class.getDeclaredMethod("getAmontsInfo", ServiceContext.class, String.class,
                GetAccountSummaryInfo.ObjectAmountsInfo.class, boolean.class, List.class, Map.class,
                GetAccountSummaryInfo.ObjectRightsInfo.class);
        method.setAccessible(true);
        method.invoke(sfaCrmAccountService, serviceContexta,objectId,objectAmountsInfo,isCrmAdmin,lookupRoles,allRelatedRoles,rightsInfo);

        then:
        true
        where:
        objectId | isCrmAdmin|serviceContexta                                                |objectAmountsInfo                                        |lookupRoles                |rightsInfo                                    |allRelatedRoles                                                            |outDataPrivilege        |getAggregateResult    |isCustomerAccount735Tenant
        "dewd"   | false     |getServiceContext(user,"SfaCrmAccountService", "getAmontsInfo")|GetAccountSummaryInfo.ObjectAmountsInfo.builder().build()| Lists.newArrayList("name")|getObjectRightsInfo("OpportunityObj")         |Maps.newHashMap("OpportunityObj", Lists.newArrayList("name"))              |OutDataPrivilege.PRIVATE|BigDecimal.valueOf(10)|true
        "dewd"   | false     |getServiceContext(user,"SfaCrmAccountService", "getAmontsInfo")|GetAccountSummaryInfo.ObjectAmountsInfo.builder().build()| Lists.newArrayList("id")  |getObjectRightsInfo("OpportunityObj")         |Maps.newHashMap("OpportunityObj", Lists.newArrayList("name"))              |OutDataPrivilege.PRIVATE|BigDecimal.valueOf(10)|true
        "dewd"   | false     |getServiceContext(user,"SfaCrmAccountService", "getAmontsInfo")|GetAccountSummaryInfo.ObjectAmountsInfo.builder().build()| Lists.newArrayList("id")  |getObjectRightsInfo("NewOpportunityObj")      |Maps.newHashMap("NewOpportunityObj", Lists.newArrayList("name"))           |OutDataPrivilege.PRIVATE|BigDecimal.valueOf(10)|true
        "dewd"   | false     |getServiceContext(user,"SfaCrmAccountService", "getAmontsInfo")|GetAccountSummaryInfo.ObjectAmountsInfo.builder().build()| Lists.newArrayList("id")  |getObjectRightsInfo("SalesOrderObj")          |Maps.newHashMap("SalesOrderObj", Lists.newArrayList("name"))               |OutDataPrivilege.PRIVATE|BigDecimal.valueOf(10)|true
        "dewd"   | false     |getServiceContext(user,"SfaCrmAccountService", "getAmontsInfo")|GetAccountSummaryInfo.ObjectAmountsInfo.builder().build()| Lists.newArrayList("id")  |getObjectRightsInfo("RefundObj")              |Maps.newHashMap("RefundObj", Lists.newArrayList("name"))                   |OutDataPrivilege.PRIVATE|BigDecimal.valueOf(10)|true
        "dewd"   | false     |getServiceContext(user,"SfaCrmAccountService", "getAmontsInfo")|GetAccountSummaryInfo.ObjectAmountsInfo.builder().build()| Lists.newArrayList("id")  |getObjectRightsInfo("ReturnedGoodsInvoiceObj")|Maps.newHashMap("ReturnedGoodsInvoiceObj", Lists.newArrayList("name"))     |OutDataPrivilege.PRIVATE|BigDecimal.valueOf(10)|true
        "dewd"   | false     |getServiceContext(user,"SfaCrmAccountService", "getAmontsInfo")|GetAccountSummaryInfo.ObjectAmountsInfo.builder().build()| Lists.newArrayList("id")  |getObjectRightsInfo("PaymentObj")             |Maps.newHashMap("PaymentObj", Lists.newArrayList("name"))                  |OutDataPrivilege.PRIVATE|BigDecimal.valueOf(10)|true;
    }

    def "getLookupRoles method"() {
        given:


        Whitebox.setInternalState(sfaCrmAccountService, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(dataList).when(serviceFacade,"findObjectDataByIdsIncludeDeleted",any(), any(), any());
        when:
        // 使用反射调用私有方法
        Method method = SfaCrmAccountService.class.getDeclaredMethod("getLookupRoles", ServiceContext.class, String.class);
        method.setAccessible(true);
        method.invoke(sfaCrmAccountService, serviceContexta,objectId);

        then:
        true
        where:
        objectId |dataList                                                                                         |serviceContexta
//        "dewd"   |null                                                                                             |getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")
        "dewd"   |Lists.newArrayList(getObjectData(Lists.newArrayList("name")))                                    |getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")
        "dewd"   |getTeamMember()                                                                                   |getServiceContext(user,"SfaCrmAccountService", "getLookupRoles");
    }

    def "getAllRelatedObjectRoles method"() {
        given:



        Whitebox.setInternalState(sfaCrmAccountService, "serviceFacade", serviceFacade)

        PowerMockito.stub(PowerMockito.method(NewOpportunityUtil.class, "getIsChangeToNewOpportunity",User.class))
                .toReturn(getIsChangeToNewOpportunity)

        PowerMockito.when(applicationContext.getBean(ObjectDescribeServiceImpl.class)).thenReturn(objectDescribeService)
        Whitebox.setInternalState(SfaCrmAccountService, "objectDescribeService", objectDescribeService)

        PowerMockito.when(objectDescribeService.findDescribeListByApiNamesWithMultiField(any(), any(), any())).thenReturn(objectDescribeList)
        when:
        // 使用反射调用私有方法
        Method method = SfaCrmAccountService.class.getDeclaredMethod("getAllRelatedObjectRoles", ServiceContext.class);
        method.setAccessible(true);
        method.invoke(sfaCrmAccountService, serviceContexta);

        then:
        true
        where:
        getIsChangeToNewOpportunity  |serviceContexta                                            |objectDescribeList
        true                         |getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")| null
        false                        |getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")| Lists.newArrayList(getDescribe(SFAPreDefineObject.Account.getApiName()))
        true                         |getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")| Lists.newArrayList(getDescribe(SFAPreDefineObject.SalesOrder.getApiName()));
    }

    def "getObjectRightsInfo method"() {
        given:


        def functionPrivilegeService = PowerMockito.mock(FunctionPrivilegeService)
        Whitebox.setInternalState(sfaCrmAccountService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(sfaCrmAccountService, "functionPrivilegeService", functionPrivilegeService)

        PowerMockito.stub(PowerMockito.method(NewOpportunityUtil.class, "getIsChangeToNewOpportunity",User.class))
                .toReturn(getIsChangeToNewOpportunity)
        PowerMockito.doReturn(isAdmin).when(serviceFacade,"isAdmin",any());
        PowerMockito.doReturn(hasRightApiNames).when(functionPrivilegeService,"funPrivilegeCheck",any(User) as User,anyList(),anyString());
        PowerMockito.doReturn(unauthorizedFields).when(functionPrivilegeService,"getUnauthorizedFields",any(User) as User,anyList());
        when:
        // 使用反射调用私有方法
        Method method = SfaCrmAccountService.class.getDeclaredMethod("getObjectRightsInfo", ServiceContext.class);
        method.setAccessible(true);
        method.invoke(sfaCrmAccountService, serviceContexta);

        then:
        true
        where:
        getIsChangeToNewOpportunity  |isAdmin|serviceContexta                                            |hasRightApiNames  |unauthorizedFields
        false                        |true   |getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|getApiName()      |getUnauthorizedFields()
        true                         |false  |getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|getApiName()      |getUnauthorizedFields();
    }

    def "get_account_filling_checker"() {
        given:


        def objectDataService = PowerMockito.mock(ObjectDataServiceImpl.class)
        PowerMockito.when(applicationContext.getBean(ObjectDataServiceImpl.class)).thenReturn(objectDataService)
        Whitebox.setInternalState(SfaCrmAccountService, "objectDataService", objectDataService)

        Whitebox.setInternalState(sfaCrmAccountService, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(queryResult).when(objectDataService,"findBySql",anyString(),anyString());
        when:
        sfaCrmAccountService.getAccountFillingChecker(serviceContexta)
        then:
        true
        where:
        serviceContexta                                            |queryResult
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")| Lists.newArrayList(Maps.newHashMap("name","111")) ;
    }

    def "get_account_back_reason"() {
        given:



        PowerMockito.when(applicationContext.getBean(ObjectDescribeServiceImpl.class)).thenReturn(objectDescribeService)
        Whitebox.setInternalState(SfaCrmAccountService, "objectDescribeService", objectDescribeService)

        Whitebox.setInternalState(sfaCrmAccountService, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(objectDescribe).when(objectDescribeService,"findByTenantIdAndDescribeApiName",anyString(),anyString());
        when:
        sfaCrmAccountService.getAccountBackReason(serviceContexta)
        then:
        true
        where:
        serviceContexta                                            |objectDescribe
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|getDescribe() ;
    }

    def "get_account_limit_info"() {
        given:


        def objectLimitService = PowerMockito.mock(ObjectLimitService.class)

        PowerMockito.mockStatic(RequestUtil.class)
        PowerMockito.when(RequestUtil.isMobileRequest()).thenReturn(isMobileRequest)

        PowerMockito.stub(PowerMockito.method(AccountUtil.class, "getCustomerLimit",User.class,Integer.class))
                .toReturn(rstResult)
        PowerMockito.stub(PowerMockito.method(AccountUtil.class, "getOwnerCustomerCount",String.class,String.class,Boolean.class,Boolean.class))
                .toReturn(totalCount)

        PowerMockito.stub(PowerMockito.method(ObjectLimitUtil.class, "getObjectLimitRuleByEmployeeId",User.class,String.class,String.class))
                .toReturn(employeeLimitRuleList)
        PowerMockito.stub(PowerMockito.method(ObjectLimitUtil.class, "getObjectCount",User.class,String.class,String.class,ObjectLimitRuleModel.ObjectLimitFilter.class))
                .toReturn(totalCount)

        Whitebox.setInternalState(sfaCrmAccountService, "objectLimitService", objectLimitService)
        PowerMockito.doReturn(isGrayObjectLimit).when(objectLimitService,"isGrayObjectLimit",anyString());
        when:
        sfaCrmAccountService.getAccountLimitInfo(serviceContexta)
        then:
        true
        where:
        serviceContexta                                            |objectDescribe|isGrayObjectLimit|rstResult          |totalCount|employeeLimitRuleList        |isMobileRequest
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|getDescribe() |false            |null               | 10       |null                         |true
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|getDescribe() |false            |getRstResult()     | 10       |null                         |true
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|getDescribe() |true             |null               | 10       |getEmployeeLimitRuleList(0,0)|true
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|getDescribe() |true             |null               | 10       |getEmployeeLimitRuleList(0,0)|false
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|getDescribe() |true             |null               | 10       |getEmployeeLimitRuleList(2,0)|false
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|getDescribe() |true             |null               | 10       |getEmployeeLimitRuleList(1,0)|false
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|getDescribe() |true             |null               | 10       |getEmployeeLimitRuleList(1,2)|false
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|getDescribe() |true             |null               | 10       |getEmployeeLimitRuleList(1,1)|false;
    }

    def "get_account_cost_info"() {
        given:

        def arg = new AccountServiceModels.GetAccountCostInfoArg();
        PowerMockito.doReturn(rstResult).when(sfaCrmAccountService,"handleCallHowInterface",any(),any());
        when:
        sfaCrmAccountService.getAccountCostInfo(serviceContexta,arg)
        then:
        true
        where:
        serviceContexta                                            |rstResult
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|null                                   ;
    }

    def "get_account_count_by_limit_rule"() {
        given:

        def arg = new AccountServiceModels.GetAccountCountByLimitRuleArg();

        PowerMockito.stub(PowerMockito.method(ObjectLimitUtil.class, "getObjectLimitFilterById",User.class,String.class,String.class,String.class))
                .toReturn(limitFilter)
        PowerMockito.stub(PowerMockito.method(ObjectLimitUtil.class, "getObjectCount",User.class,String.class,String.class,ObjectLimitRuleModel.ObjectLimitFilter.class))
                .toReturn(totalCount)

        when:
        sfaCrmAccountService.getAccountCountByLimitRule(serviceContexta,arg)
        then:
        true
        where:
        serviceContexta                                            |totalCount|limitFilter
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|10        |null
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|1         |getObjectLimitFilter() ;
    }



    def "mergeSaleRecord"() {
        given:

        def arg = new AccountServiceModels.MergeSaleRecordArg();
        arg.setSourceDataIds(getSourceDataIds)
        PowerMockito.stub(PowerMockito.method(CommonUtil.class, "transferResources",String.class,String.class,List.class,Integer.class,String.class,String.class))
                .toReturn(transferResourcesResult)
        when:
        sfaCrmAccountService.mergeSaleRecord(this.user,arg)
        then:
        true
        where:
        getSourceDataIds                                            |transferResourcesResult|limitFilter
        null                                                        |false                  |null
        Lists.newArrayList("SfaCrmAccountService", "getLookupRoles")|true                   |null
        Lists.newArrayList("SfaCrmAccountService", "getLookupRoles")|false                  |null;
    }

    def "get_unallocated_account_info"() {
        given:

        def arg = new AccountServiceModels.GetAccountCostInfoArg();
        PowerMockito.doReturn(rstResult).when(sfaCrmAccountService,"handleCallHowInterface",any(),any());
        when:
        sfaCrmAccountService.getUnallocatedAccountInfo(serviceContexta,arg)
        then:
        true
        where:
        serviceContexta                                            |rstResult
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|null                                   ;
    }

    def "getSnapShotForWeb"() {
        given:

        def logService=PowerMockito.mock(ObjectLogService.class)
        Whitebox.setInternalState(sfaCrmAccountService,"logService",logService)
        def arg = new AccountServiceModels.GetAccountCostInfoArg();
        PowerMockito.doReturn(rstResult).when(logService,"getSnapShotForWeb",any(),any());
        when:
        sfaCrmAccountService.getSnapShotForWeb(serviceContexta,arg)
        then:
        true
        where:
        serviceContexta                                            |rstResult
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|new GetSnapShotForWeb.Result()                                   ;
    }

    def "get_account_contracts_tel_info"() {
        given:

        def serviceFacade=PowerMockito.mock(ServiceFacade.class)
        def metaDataMiscService=PowerMockito.mock(MetaDataMiscServiceImpl.class)
        Whitebox.setInternalState(sfaCrmAccountService,"serviceFacade",serviceFacade)
        Whitebox.setInternalState(sfaCrmAccountService,"metaDataMiscService",metaDataMiscService)
        def arg = new AccountServiceModels.GetAccountRelatedContractTelInfoArg();
        arg.setObjectDataId("dddd")
        PowerMockito.doReturn(contactDescribe).when(serviceFacade,"findObject",any(),any());
        PowerMockito.doReturn(searchTemplateQuery).when(serviceFacade,"getSearchTemplateQuery",any(),any(),any(),any());
        PowerMockito.doReturn(queryResult).when(serviceFacade,"findBySearchQuery",any(),any(),any(),any());
//        PowerMockito.doReturn(null).when(serviceFacade,"fillMaskFieldValue",any(User) as User,anyList(),contactDescribe,anyBoolean());
        PowerMockito.doNothing().when(metaDataMiscService,"fillPhoneNumberInformation",any(),any());
        when:
        sfaCrmAccountService.getAccountRelatedContractTelInfoResult(serviceContexta,arg)
        then:
        true
        where:
        serviceContexta                                            |contactDescribe |searchTemplateQuery|queryResult
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|getDescribe()   |buildSearchQuery() |buildQueryResult()
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|getDescribe()   |buildSearchQuery() |getbuildQueryResult()
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|getDescribe()   |buildSearchQuery() |getbuildQueryResult1() ;
    }

    def "testFillIndustryEnterInfo"() {
        given:

        def serviceFacade=PowerMockito.mock(ServiceFacade.class)
        Whitebox.setInternalState(sfaCrmAccountService,"serviceFacade",serviceFacade)

        PowerMockito.doReturn(dataList).when(serviceFacade,"findObjectDataByIds",any(),any(),any());
        when:
        sfaCrmAccountService.testFillIndustryEnterInfo(serviceContexta)
        then:
        true
        where:
        serviceContexta                                            |dataList
        getServiceContext(user,"SfaCrmAccountService", "getLookupRoles")|Lists.newArrayList(getObjectData(Lists.newArrayList("name")))   ;
    }

    def "handleCallHowInterface"() {
        given:

        def serviceFacade=PowerMockito.mock(ServiceFacade.class)
        def gdsHandler=PowerMockito.mock(GDSHandler.class)
        def accountCostContentServiceProxy=PowerMockito.mock(AccountCostContentServiceProxy.class)
        Whitebox.setInternalState(sfaCrmAccountService,"serviceFacade",serviceFacade)
        Whitebox.setInternalState(sfaCrmAccountService,"gdsHandler",gdsHandler)
        Whitebox.setInternalState(sfaCrmAccountService,"accountCostContentServiceProxy",accountCostContentServiceProxy)

        PowerMockito.stub(PowerMockito.method(AccountUtil.class, "isGrayUseNewSalesEventsInterface",String.class))
                .toReturn(isGrayUseNewSalesEventsInterface)


        PowerMockito.doReturn(ea).when(gdsHandler,"getEAByEI",anyString());
        PowerMockito.doReturn(null).when(accountCostContentServiceProxy,"getCostContentInfoByCustomerID",any(),any());
        PowerMockito.doReturn(null).when(sfaCrmAccountService,"getCostContentInfoByCustomerID",any(),any());
        when:
        sfaCrmAccountService.handleCallHowInterface(this.user,accountId)
        then:
        true
        where:
        accountId   |isGrayUseNewSalesEventsInterface|ea
        "ssss"      |true                            |"dse"
        "ssss"      |false                           |"dse";
    }

    def "getCostContentInfoByCustomerID"() {
        given:

        def feedsSaleActionProxy=PowerMockito.mock(FeedsSaleActionProxy.class)
        Whitebox.setInternalState(sfaCrmAccountService,"feedsSaleActionProxy",feedsSaleActionProxy)

        PowerMockito.doReturn(result).when(feedsSaleActionProxy,"GetCostContentInfoByCustomerID",any(),any());
        when:
        sfaCrmAccountService.getCostContentInfoByCustomerID(this.user,accountId)
        then:
        true
        where:
        accountId   |result
//        "ssss"      |FeedsModel.CostContentInfoResult.builder().code(1).build()
        "ssss"      |FeedsModel.CostContentInfoResult.builder().code(0).build()
        "ssss"      |FeedsModel.CostContentInfoResult.builder().costContentEntity(getCostContentEntity()).code(0).build();
    }



    List<GetAccountSummaryInfo.ObjectRightsInfo> getObjectRightsInfos(){
        GetAccountSummaryInfo.ObjectRightsInfo objectRightsInfo = GetAccountSummaryInfo.ObjectRightsInfo.builder().build()
        return Lists.newArrayList(objectRightsInfo);
    }

    GetAccountSummaryInfo.ObjectRightsInfo getObjectRightsInfo(String apiName){
        GetAccountSummaryInfo.ObjectRightsInfo objectRightsInfo = GetAccountSummaryInfo.ObjectRightsInfo.builder()
                .apiName(apiName).hasFunctionRight(true).hasFieldRight(true).build();
        return objectRightsInfo
    }

    List<IObjectData> getTeamMember(){
        List<IObjectData> list = new ArrayList<>()
        IObjectData objectData = new ObjectData();
        List<Map> relevantTeam = Lists.newArrayList();
        Map teamMemberEmployee = Maps.newHashMap();
        teamMemberEmployee.put("teamMemberEmployee", Lists.newArrayList("1000"));
        teamMemberEmployee.put("teamMemberRole", "1");
        teamMemberEmployee.put("teamMemberPermissionType", "2");
        relevantTeam.add(teamMemberEmployee);
        objectData.set("relevant_team",relevantTeam)
        list.add(objectData)
        return list;
    }
    List<String> getApiName(){
        return Lists.newArrayList(SFAPreDefineObject.SalesOrder.getApiName(),
                SFAPreDefineObject.Refund.getApiName(), SFAPreDefineObject.ReturnedGoodsInvoice.getApiName(),SFAPreDefineObject.NewOpportunity.getApiName(),
                SFAPreDefineObject.Payment.getApiName(),SFAPreDefineObject.Opportunity.getApiName())
    }

    Map getUnauthorizedFields(){
        Map<String,Set<String>> map = new HashMap();
        map.put(SFAPreDefineObject.Opportunity.getApiName(),Sets.newHashSet("expected_deal_amount"))
        map.put(SFAPreDefineObject.NewOpportunity.getApiName(),Sets.newHashSet("amount"))
        map.put(SFAPreDefineObject.SalesOrder.getApiName(),Sets.newHashSet("order_amount"))
        map.put(SFAPreDefineObject.Refund.getApiName(),Sets.newHashSet("refunded_amount"))
        map.put(SFAPreDefineObject.ReturnedGoodsInvoice.getApiName(),Sets.newHashSet("returned_goods_inv_amount"))
        map.put(SFAPreDefineObject.Payment.getApiName(),Sets.newHashSet("payment_amount"))
        return map;
    }

    IObjectDescribe getDescribe(){
        IObjectDescribe objectDescribe = getDescribe(Utils.ACCOUNT_API_NAME)
        IFieldDescribe fieldDescribe = new SelectOneFieldDescribe()
        fieldDescribe.setApiName(AccountConstants.Field.BACK_REASON)
        List<ISelectOption> selectOptions = new ArrayList<>();
        ISelectOption optionNone = new SelectOption();
        optionNone.setBooleanValue(false)
        optionNone.setLabel("222")
        optionNone.setValue("111")
        selectOptions.add(optionNone)
        fieldDescribe.setSelectOptions(selectOptions)

        IFieldDescribe fieldDescribe1 = new PhoneNumberFieldDescribe();
        fieldDescribe1.setActive(true)
        fieldDescribe1.setApiName("phone_number")
        objectDescribe.setFieldDescribes(Lists.newArrayList(fieldDescribe,fieldDescribe1));
        return objectDescribe;
    }

    CustomerLimit.Result getRstResult(){
        CustomerLimit.Result result = new CustomerLimit.Result();
        CustomerLimit.CustomerLimitResult customerLimitResult = new CustomerLimit.CustomerLimitResult();
        customerLimitResult.setLimitNum(1)
        result.setValue(customerLimitResult)
        return result;
    }

    List<ObjectLimitRuleModel.ObjectLimitRule> getEmployeeLimitRuleList(int num,int num1){
        List<ObjectLimitRuleModel.ObjectLimitRule> list = new ArrayList<>()
        for(int i=0;i<num;i++){
            ObjectLimitRuleModel.ObjectLimitRule rule = ObjectLimitRuleModel.ObjectLimitRule.builder().build()
            rule.setDataId("ssss")
            List<ObjectLimitRuleModel.ObjectLimitFilter> l = new ArrayList<>();
            for(int j=0;j<num1;j++){
                ObjectLimitRuleModel.ObjectLimitFilter limitFilter = ObjectLimitRuleModel.ObjectLimitFilter.builder().build()
                limitFilter.setId("ddd")
                l.add(limitFilter)
            }
            rule.setObjectLimitFilterList(l);
            list.add(rule)
        }
        return list;
    }
    ObjectLimitRuleModel.ObjectLimitFilter getObjectLimitFilter(){
        ObjectLimitRuleModel.ObjectLimitFilter limitFilter = ObjectLimitRuleModel.ObjectLimitFilter.builder().build()
        limitFilter.setId("ddd")
        return limitFilter;
    }

    QueryResult getbuildQueryResult(){
        QueryResult queryResult =  buildQueryResult();

        queryResult.setData(Lists.newArrayList(getObjectData(Lists.newArrayList("phone_number") ,Lists.newArrayList("phone_number"))))
        queryResult.setTotalNumber(10);
        return queryResult
    }

    QueryResult getbuildQueryResult1(){
        QueryResult queryResult =  buildQueryResult();

        queryResult.setData(Lists.newArrayList(getObjectData(Lists.newArrayList("phone_number","job_title","phone_number__s","phone_number__p") ,Lists.newArrayList("phone_number","job_title","phone_number",new QueryPhoneNumberInformation.Result()))))
        queryResult.setTotalNumber(10);
        return queryResult
    }

    FeedsModel.CostContentEntity getCostContentEntity(){
        FeedsModel.CostContentEntity content = FeedsModel.CostContentEntity.builder().build();
        content.setCount(10)
        return content;
    }

}
