package com.facishare.crm.sfa.predefine.service.decisionchain


import com.beust.jcommander.internal.Maps

import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.predefine.SFAPreDefineObject
import com.facishare.crm.sfa.predefine.service.model.OpportunityDecisionChainModel
import com.facishare.crm.sfa.utilities.util.AccountPathUtil
import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.service.IObjectDescribeService
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.Logger
import org.spockframework.runtime.Sputnik
import spock.lang.Shared

import java.lang.reflect.InvocationTargetException
import java.lang.reflect.Method

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyList

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([UdobjGrayConfig.class,ServiceFacade.class, SFAPreDefineObject.class])
// 限制 RequestUtil, AccountAddrService 类里的静态代码块初始化, 这里会加载一些静态资源，可以禁止初始化。
@SuppressStaticInitializationFor([
        "com.facishare.paas.appframework.core.util.RequestUtil",
        "com.facishare.crm.sfa.predefine.SFAPreDefineObject",
        "com.facishare.crm.sfa.predefine.service.decisionchain.OpportunityDecisionChainService"
])
class OpportunityDecisionChainServiceTest extends EnhancedBaseGroovyTest {
    @Shared
    private ServiceFacade serviceFacade
    @Shared
    private OpportunityDecisionChainService opportunityDecisionChainService
    @Shared
    User user

    def setupSpec() {

        removeConfigFactory()
        removeI18N()
        initSpringContext()
        user = PowerMockito.spy(new User("71568", "-10000"));
        serviceFacade = PowerMockito.mock(ServiceFacade)
        opportunityDecisionChainService = PowerMockito.spy(new OpportunityDecisionChainService())
        //opportunityDecisionChainService = new OpportunityDecisionChainService(serviceFacade: serviceFacade)
        Whitebox.setInternalState(OpportunityDecisionChainService, "log", Mock(Logger))
    }

    def setup() {
        PowerMockito.stub(PowerMockito.method(UdobjGrayConfig.class, "isAllow", String.class, String.class))
                .toReturn(true)
        SFAPreDefineObject relationTemplate = PowerMockito.mock(SFAPreDefineObject.class);
        PowerMockito.when(relationTemplate.getApiName()).thenReturn("xxxxx");

        SFAPreDefineObject OpportunityDecisionChain = PowerMockito.mock(SFAPreDefineObject.class);
        PowerMockito.when(OpportunityDecisionChain.getApiName()).thenReturn("OpportunityDecisionChainObj");
        SFAPreDefineObject OpportunityDecisionChainDetail = PowerMockito.mock(SFAPreDefineObject.class);
        PowerMockito.when(OpportunityDecisionChainDetail.getApiName()).thenReturn("OpportunityDecisionChainDetailObj");
        // 通过反射设置枚举实例
        Whitebox.setInternalState(SFAPreDefineObject.class, "RelationTemplate", relationTemplate);
        Whitebox.setInternalState(SFAPreDefineObject.class, "OpportunityDecisionChain", OpportunityDecisionChain);
        Whitebox.setInternalState(SFAPreDefineObject.class, "OpportunityDecisionChainDetail", OpportunityDecisionChainDetail);
    }
    def "queryView"() {
        given:
        Whitebox.setInternalState(opportunityDecisionChainService, "serviceFacade", serviceFacade)
        def arg = new OpportunityDecisionChainModel.Arg()
        arg.setId("xxxxx")
        arg.setDescribe_api_name("xxxxx")
        PowerMockito.when(serviceFacade.findBySearchQuery(any(User) as User, any(String) as String, any(SearchTemplateQuery) as SearchTemplateQuery)).thenReturn(new QueryResult<IObjectData>(data: [new ObjectData()]))

        when:
        def result = opportunityDecisionChainService.queryView(getServiceContext(user,"OpportunityDecisionChainService", "queryView"), arg)
        then:
        true
        where:
        queryResult        | queryResultDetail
        null               | buildQueryResult()
        buildQueryResult() | buildQueryResult();
    }


    def "fillRelatedObjectData"() {
        given:


        when:
        // 使用反射调用私有方法
        Method method = OpportunityDecisionChainService.class.getDeclaredMethod("fillRelatedObjectData", IObjectData.class, String.class, Map.class, Map.class, Map.class)
        method.setAccessible(true)
        method.invoke(opportunityDecisionChainService, d, relatedApiName, contentMap, relatedMap, unauthorizedFieldMap)
        then:
        true
        where:
        d                                                  | relatedApiName | contentMap                                     | relatedMap                                                                                    | unauthorizedFieldMap
        getObjectData(Lists.newArrayList("name", "xxxxx")) | "xxxxx"        | Maps.newHashMap("xxxxx", getCardShowContent()) | Maps.newHashMap("xxxxx", Maps.newHashMap("xxxxx", getObjectData(Lists.newArrayList("name")))) | Maps.newHashMap("xxxxx", Sets.newHashSet("xxxxx"))
        getObjectData(Lists.newArrayList("name", "xxxxx")) | "xxxxx"        | Maps.newHashMap("xxxxx", getCardShowContent()) | Maps.newHashMap("xxxxx", Maps.newHashMap("xxxx", getObjectData(Lists.newArrayList("name"))))  | Maps.newHashMap("xxxxx", Sets.newHashSet("xxxxx"));
    }

    def "removeNotShowOfField"() {
        given:


        when:
        // 使用反射调用私有方法
        Method method = OpportunityDecisionChainService.class.getDeclaredMethod("removeNotShowOfField", IObjectData.class, List.class, Set.class)
        method.setAccessible(true)
        method.invoke(opportunityDecisionChainService, relatedData, show_fields, unauthorizedFieldSet)
        then:
        true
        where:
        relatedData                                        | show_fields                         | unauthorizedFieldSet
        getObjectData(Lists.newArrayList("name", "xxxxx")) | null                                | Sets.newHashSet("name")
        getObjectData(Lists.newArrayList("name", "xxxxx")) | Lists.newArrayList("name", "233ed") | Sets.newHashSet("name");
    }

    def "queryCardOfData"() {
        given:

        opportunityDecisionChainService = PowerMockito.spy(new OpportunityDecisionChainService())
        Whitebox.setInternalState(opportunityDecisionChainService, "serviceFacade", serviceFacade)
        PowerMockito.doNothing().when(serviceFacade, "fillExtendFieldInfo", any(), any(), any())
        PowerMockito.doReturn(null).when(opportunityDecisionChainService, "getIObjectDescribeByApiName", any(), any())
        PowerMockito.doReturn(temData).when(opportunityDecisionChainService, "queryRelatedObjectData", any(), any(), any(), any())
        when:
        // 使用反射调用私有方法
        Method method = OpportunityDecisionChainService.class.getDeclaredMethod("queryCardOfData", User.class, String.class, List.class, List.class)
        method.setAccessible(true)
        method.invoke(opportunityDecisionChainService, this.user, relatedApiName, temData, cardShowContentList)
        then:
        true
        where:
        relatedApiName | temData                                          | cardShowContentList
        "xxxxx"        | null                                             | null
        "xxxxx"        | getObjectDataList(Lists.newArrayList("name"), 1) | Lists.newArrayList(getCardShowContent());
    }

    def "queryRelatedObjectData"() {
        given:

        opportunityDecisionChainService = PowerMockito.spy(new OpportunityDecisionChainService())
        Whitebox.setInternalState(opportunityDecisionChainService, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(new SearchTemplateQuery()).when(serviceFacade, "getSearchTemplateQuery", any(), any(), any(), any())
        PowerMockito.doReturn(null).when(serviceFacade, "findBySearchQuery", any(), any(), any(), any())
        when:
        // 使用反射调用私有方法
        Method method = OpportunityDecisionChainService.class.getDeclaredMethod("queryRelatedObjectData", User.class, OpportunityDecisionChainModel.CardShowContent.class, List.class, IObjectDescribe.class)
        method.setAccessible(true)
        method.invoke(opportunityDecisionChainService, this.user, content, conditionDatas, objectDescribe)
        then:
        true
        where:
        content              | conditionDatas                                    | objectDescribe
        getCardShowContent() | null                                              | null
        getCardShowContent() | getObjectDataList(Lists.newArrayList("xxxx"), 1)  | null
        getCardShowContent() | getObjectDataList(Lists.newArrayList("xxxxx"), 1) | getDescribe("xxxx");
    }


    def "buildSearchTemplateQuery"() {
        given:

        def opportunityDecisionChainService = new OpportunityDecisionChainService(serviceFacade: serviceFacade)

        when:
        def result = opportunityDecisionChainService.buildSearchTemplateQuery()
        then:
        true
        where:
        queryResult | queryResultDetail
        null        | buildQueryResult();
    }

    def "sortEntities"() {
        given:



        when:
        def result = opportunityDecisionChainService.sortEntities(dataList)
        then:
        true
        where:
        dataList                                                              | queryResultDetail
        Lists.newArrayList(getDataResult("parent_id"), getDataResult("name")) | buildQueryResult();
    }

    def "sortDetailsEntities"() {
        given:



        when:
        def result = opportunityDecisionChainService.sortDetailsEntities(dataList)
        then:
        true
        where:
        dataList                                       | queryResultDetail
        Lists.newArrayList(getDataResult("parent_id")) | buildQueryResult();
    }

    def "saveView"() {
        given:
        def arg = new OpportunityDecisionChainModel.SaveArg()
        arg.setRelation_template_id(relation_template_id)
        arg.setDataObjectList(dataObjectList)
        arg.setReference_data_id("xxxxxxxx")

        opportunityDecisionChainService = PowerMockito.spy(new OpportunityDecisionChainService())
        Whitebox.setInternalState(opportunityDecisionChainService, "serviceFacade", serviceFacade)

        PowerMockito.doNothing().when(opportunityDecisionChainService, "checkParam", any())
        PowerMockito.doNothing().when(opportunityDecisionChainService, "handleTemplate", any())
        PowerMockito.doReturn(funPrivilegeMap).when(serviceFacade, "batchFunPrivilegeCheck", any(), any(), any())
        PowerMockito.doNothing().when(opportunityDecisionChainService, "handleFunPrivilegeCheck", any(), any(), any(), any())
        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQuery", any(), any(), any())
        PowerMockito.doNothing().when(opportunityDecisionChainService, "bulkInvalid", any(), any(), any())
        PowerMockito.doReturn(sortAfterDataList).when(opportunityDecisionChainService, "sortEntitiesOfSave", any())
        PowerMockito.doReturn(newAddODChainList).when(opportunityDecisionChainService, "filterNeedAdd", any(), any(), any())
        PowerMockito.doReturn(updODChainList).when(opportunityDecisionChainService, "filterNeedUpdate", any(), any(), any())
        PowerMockito.doReturn(deleteODChainIds).when(opportunityDecisionChainService, "filterDeleteIds", any(), any())
        PowerMockito.doNothing().when(opportunityDecisionChainService, "add", any(), any(), any())
        PowerMockito.doNothing().when(opportunityDecisionChainService, "update", any(), any(), any(), any())

        when:
        def result = opportunityDecisionChainService.saveView(getServiceContext(user,"OpportunityDecisionChainService", "saveView"), arg)
        then:
        true
        where:
        funPrivilegeMap                                      | relation_template_id | queryResult        | sortAfterDataList                 | newAddODChainList                 | updODChainList                    | deleteODChainIds             | dataObjectList
        Maps.newHashMap("xxx", Maps.newHashMap("xxx", true)) | null                 | buildQueryResult() | Lists.newArrayList(getSaveData()) | Lists.newArrayList(getSaveData()) | Lists.newArrayList(getSaveData()) | Lists.newArrayList("xxxxxx") | null
        Maps.newHashMap("xxx", Maps.newHashMap("xxx", true)) | "xx"                 | buildQueryResult() | Lists.newArrayList(getSaveData()) | Lists.newArrayList(getSaveData()) | Lists.newArrayList(getSaveData()) | Lists.newArrayList("xxxxxx") | Lists.newArrayList(getSaveData());
    }


    def "handleFunPrivilegeCheck"() {
        given:
        opportunityDecisionChainService = PowerMockito.spy(new OpportunityDecisionChainService())

        Whitebox.setInternalState(opportunityDecisionChainService, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(objectDescribe).when(opportunityDecisionChainService, "getIObjectDescribeByApiName", any(), any())
        when:
        // 使用反射调用私有方法
        try {
            Method method = OpportunityDecisionChainService.class.getDeclaredMethod("handleFunPrivilegeCheck", User.class, Map.class, String.class, List.class)
            method.setAccessible(true)
            method.invoke(opportunityDecisionChainService, this.user, funPrivilegeMap, describeApiName, objectActions)
        }catch(InvocationTargetException e){
            assert e
        }
        then:
        true
        where:
        funPrivilegeMap                                       | describeApiName | objectActions             | objectDescribe
        Maps.newHashMap("xxx", Maps.newHashMap("xxx", false)) | "xxx"           | Lists.newArrayList("xxx") | getDescribe("xxxx");
    }

    def "handleTemplate"() {
        given:
        def arg = new OpportunityDecisionChainModel.SaveArg()
        arg.setReference_data_id("xxx")
        arg.setReference_api_name("RelationTemplateObj")
        arg.setDataObjectList(Lists.newArrayList(getSaveData()))


        Whitebox.setInternalState(opportunityDecisionChainService, "serviceFacade", serviceFacade)
        when:
        // 使用反射调用私有方法
        Method method = OpportunityDecisionChainService.class.getDeclaredMethod("handleTemplate", OpportunityDecisionChainModel.SaveArg.class)
        method.setAccessible(true)
        method.invoke(opportunityDecisionChainService, arg)
        then:
        true
        where:
        funPrivilegeMap                                       | describeApiName | objectActions             | objectDescribe
        Maps.newHashMap("xxx", Maps.newHashMap("xxx", false)) | "xxx"           | Lists.newArrayList("xxx") | getDescribe("xxxx");
    }

    def "filterDeleteIds"() {
        given:


        Whitebox.setInternalState(opportunityDecisionChainService, "serviceFacade", serviceFacade)
        when:
        // 使用反射调用私有方法
        Method method = OpportunityDecisionChainService.class.getDeclaredMethod("filterDeleteIds", List.class, List.class)
        method.setAccessible(true)
        method.invoke(opportunityDecisionChainService, oldIds, existDataList)
        then:
        true
        where:
        oldIds                    | existDataList
        null                      | null
        Lists.newArrayList("xxx") | Lists.newArrayList(getSaveData());
    }

    def "filterNeedUpdate"() {
        given:


        Whitebox.setInternalState(opportunityDecisionChainService, "serviceFacade", serviceFacade)
        when:
        // 使用反射调用私有方法
        Method method = OpportunityDecisionChainService.class.getDeclaredMethod("filterNeedUpdate", List.class, List.class, String.class)
        method.setAccessible(true)
        method.invoke(opportunityDecisionChainService, oldIds, existDataList, describeApiName)
        then:
        true
        where:
        oldIds                    | existDataList                     | describeApiName
        null                      | null                              | "xxx"
        Lists.newArrayList("xxx") | Lists.newArrayList(getSaveData()) | "xxx";
    }

    def "filterNeedAdd"() {
        given:


        Whitebox.setInternalState(opportunityDecisionChainService, "serviceFacade", serviceFacade)
        when:
        // 使用反射调用私有方法
        Method method = OpportunityDecisionChainService.class.getDeclaredMethod("filterNeedAdd", List.class, List.class, String.class)
        method.setAccessible(true)
        method.invoke(opportunityDecisionChainService, oldIds, existDataList, describeApiName)
        then:
        true
        where:
        oldIds                    | existDataList                     | describeApiName
        null                      | Lists.newArrayList(getSaveData()) | "xxx"
        Lists.newArrayList("xxx") | Lists.newArrayList(getSaveData()) | "xxx";
    }

    def "add"() {
        given:


        Whitebox.setInternalState(opportunityDecisionChainService, "serviceFacade", serviceFacade)
        when:
        // 使用反射调用私有方法
        Method method = OpportunityDecisionChainService.class.getDeclaredMethod("add", ServiceContext.class, String.class, List.class)
        method.setAccessible(true)
        method.invoke(opportunityDecisionChainService, getServiceContext(user,"OpportunityDecisionChainService", "add"), describeApiName, saveDataListArg)
        then:
        true
        where:
        saveDataListArg                   | describeApiName
        null                              | "xxx"
        Lists.newArrayList(getSaveData()) | "xxx";
    }

    def "update"() {
        given:


        Whitebox.setInternalState(opportunityDecisionChainService, "serviceFacade", serviceFacade)
        when:
        // 使用反射调用私有方法
        Method method = OpportunityDecisionChainService.class.getDeclaredMethod("update", ServiceContext.class, String.class, List.class, List.class)
        method.setAccessible(true)
        method.invoke(opportunityDecisionChainService, getServiceContext(user,"OpportunityDecisionChainService", "add"), describeApiName, saveDataListArg, oldDataList)
        then:
        true
        where:
        saveDataListArg                   | describeApiName | oldDataList
        null                              | "xxx"           | null
        Lists.newArrayList(getSaveData()) | "xxx"           | Lists.newArrayList(getObjectData(Lists.newArrayList("_id", "name"), Lists.newArrayList("xxxxx", "xxxxx")));
    }

    def "bulkInvalid"() {
        given:


        Whitebox.setInternalState(opportunityDecisionChainService, "serviceFacade", serviceFacade)
        when:
        // 使用反射调用私有方法
        Method method = OpportunityDecisionChainService.class.getDeclaredMethod("bulkInvalid", ServiceContext.class, String.class, List.class)
        method.setAccessible(true)
        method.invoke(opportunityDecisionChainService, getServiceContext(user,"OpportunityDecisionChainService", "bulkInvalid"), describeApiName, ids)
        then:
        true
        where:
        saveDataListArg                   | describeApiName | ids
        null                              | "xxx"           | null
        Lists.newArrayList(getSaveData()) | "xxx"           | Lists.newArrayList("xxxxx");
    }

    def "sortEntitiesOfSave"() {
        given:

        opportunityDecisionChainService = PowerMockito.spy(new OpportunityDecisionChainService())
        Whitebox.setInternalState(opportunityDecisionChainService, "serviceFacade", serviceFacade)
        PowerMockito.doNothing().when(opportunityDecisionChainService, "sortEntitiesOfSave", anyList(), anyList())
        when:
        def result = opportunityDecisionChainService.sortEntitiesOfSave(saveDataList)
        then:
        true
        where:
        saveDataList                                                                                                     | dada
        Lists.newArrayList(getSaveData("OpportunityDecisionChainObj"))                                                   | null
        Lists.newArrayList(getSaveData("OpportunityDecisionChainDetailObj"))                                             | null
        Lists.newArrayList(getSaveData("OpportunityDecisionChainObj"), getSaveData("OpportunityDecisionChainDetailObj")) | null;
    }

    def "sortEntitiesOfSave2"() {
        given:


        Whitebox.setInternalState(opportunityDecisionChainService, "serviceFacade", serviceFacade)

        when:
        // 使用反射调用私有方法
        Method method = OpportunityDecisionChainService.class.getDeclaredMethod("sortEntitiesOfSave", List.class, List.class)
        method.setAccessible(true)
        method.invoke(opportunityDecisionChainService, saveDataList, newDataList)
        then:
        true
        where:
        saveDataList                                                                                                                                          | newDataList
        Lists.newArrayList(getSaveDataParent_idIsNull("OpportunityDecisionChainObj", true), getSaveDataParent_idIsNull("OpportunityDecisionChainObj", false)) | Lists.newArrayList();
    }

    def "checkParam"() {
        given:


        Whitebox.setInternalState(opportunityDecisionChainService, "serviceFacade", serviceFacade)

        when:
        // 使用反射调用私有方法
        Method method = OpportunityDecisionChainService.class.getDeclaredMethod("checkParam", List.class)
        method.setAccessible(true)
        method.invoke(opportunityDecisionChainService, saveDataList)
        then:
        true
        where:
        saveDataList                                                                               | newDataList
        Lists.newArrayList(getSaveDataParent_idIsNull("OpportunityDecisionChainDetailObj", false)) | Lists.newArrayList();
    }


    def "getIObjectDescribeByApiName"() {
        given:
        opportunityDecisionChainService = PowerMockito.spy(new OpportunityDecisionChainService())

        Whitebox.setInternalState(opportunityDecisionChainService, "serviceFacade", serviceFacade)
        def objectDescribeService = PowerMockito.mock(IObjectDescribeService)
        Whitebox.setInternalState(opportunityDecisionChainService, "objectDescribeService", objectDescribeService)

        when:
        def result = opportunityDecisionChainService.getIObjectDescribeByApiName(this.user.getTenantId(), apiName)
        then:
        true
        where:
        apiName | dada
        "xxxxx" | null;
    }

    OpportunityDecisionChainModel.CardShowContent getCardShowContent() {
        OpportunityDecisionChainModel.CardShowContent cardShowContent = new OpportunityDecisionChainModel.CardShowContent();
        cardShowContent.setDescribe_api_name("xxxxx")
        cardShowContent.setRelated_field("xxxxx")
        cardShowContent.setReference_api_name("xxxxx")
        return cardShowContent
    }

    OpportunityDecisionChainModel.DataResult getDataResult(String field) {
        OpportunityDecisionChainModel.DataResult dataResult = new OpportunityDecisionChainModel.DataResult()
        ObjectDataDocument objectDataDocument = new ObjectDataDocument()
        objectDataDocument.put(field, "xxxx")
        objectDataDocument.put("_id", "xxxx")
        dataResult.setDecisionChain(objectDataDocument)
        ObjectDataDocument objectDataDocument1 = new ObjectDataDocument()
        objectDataDocument1.put("xxxxx", "xxxx")
        objectDataDocument1.put("_id", "xxxx")
        dataResult.setDecisionChainDetails(Lists.newArrayList(objectDataDocument, objectDataDocument1))
        return dataResult
    }

    OpportunityDecisionChainModel.SaveData getSaveData() {
        OpportunityDecisionChainModel.SaveData saveData = new OpportunityDecisionChainModel.SaveData();
        saveData.setName("xxxx")
        saveData.set_id("xxxxx")
        saveData.setRank(1)
        saveData.setParent_id("xxxxx")
        saveData.setNew_opportunity_contact_id("xxxx")
        saveData.setOpportunity_decision_chain_id("xxxx")
        saveData.setRelation_template_id("xxxx")
        saveData.setObject_describe_api_name("OpportunityDecisionChainObj")
        return saveData;
    }

    OpportunityDecisionChainModel.SaveData getSaveData(String x) {
        OpportunityDecisionChainModel.SaveData saveData = new OpportunityDecisionChainModel.SaveData();
        saveData.setName("xxxx")
        saveData.set_id("xxxxx")
        saveData.setRank(1)
        saveData.setParent_id("xxxxx")
        saveData.setNew_opportunity_contact_id("xxxx")
        saveData.setOpportunity_decision_chain_id("xxxx")
        saveData.setRelation_template_id("xxxx")
        saveData.setObject_describe_api_name(x)
        return saveData;
    }

    OpportunityDecisionChainModel.SaveData getSaveDataParent_idIsNull(String x, boolean flag) {
        OpportunityDecisionChainModel.SaveData saveData = new OpportunityDecisionChainModel.SaveData();
        saveData.setName("xxxx")
        saveData.set_id("xxxxx")
        saveData.setRank(1)
        if (!flag) {
            saveData.setParent_id("xxxxx")
        }
        saveData.setNew_opportunity_contact_id("xxxx")
        saveData.setOpportunity_decision_chain_id("xxxx")
        saveData.setRelation_template_id("xxxx")
        saveData.setObject_describe_api_name(x)
        return saveData;
    }


}
