package com.facishare.crm.sfa.predefine.service.relationTemplate


import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.predefine.service.model.RelationTemplateModels
import com.facishare.crm.sfa.utilities.util.AccountUtil
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.metadata.LayoutExt
import com.facishare.paas.metadata.ui.layout.ILayout
import com.facishare.paas.metadata.util.SpringUtil
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.Logger
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([ SpringUtil.class,AccountUtil.class,UdobjGrayConfig.class,RelationTemplateService.class])
// 限制 RequestUtil, AccountAddrService 类里的静态代码块初始化, 这里会加载一些静态资源，可以禁止初始化。
@SuppressStaticInitializationFor(["com.facishare.paas.appframework.core.util.RequestUtil", "com.facishare.crm.sfa.predefine.service.mcr.RelationTemplateService"])
class RelationTemplateServiceTest extends EnhancedBaseGroovyTest {
    @Shared
    ServiceFacade serviceFacade
    @Shared
    RelationTemplateService relationTemplateService
    @Shared
    User user

    def setupSpec() {


        removeConfigFactory()
        removeI18N()
        initSpringContext()
        user = PowerMockito.spy(new User("71568", "-10000"));
        serviceFacade = PowerMockito.mock(ServiceFacade)
        relationTemplateService = PowerMockito.spy(new RelationTemplateService())
        Whitebox.setInternalState(RelationTemplateService, "log", Mock(Logger))
    }
    def setup() {
        def applicationContext = PowerMockito.mock(ApplicationContext)
        PowerMockito.mockStatic(SpringUtil)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(applicationContext)
        PowerMockito.stub(PowerMockito.method(UdobjGrayConfig.class, "isAllow", String.class, String.class))
                .toReturn(true)
    }

    def "check_priority"() {
        given:
        def arg = new RelationTemplateModels.Arg()
        when:
        relationTemplateService.checkPriority(getServiceContext(user,"ContactRelationshipService", "uploadPhone"), arg)
        then:
        true
        where:
        getAddressBookSettingConfigValue | phoneList
        false                            | null;
    }
    def "get_tree_module_config"() {
        given:

        def arg = new RelationTemplateModels.Arg()
        Whitebox.setInternalState(relationTemplateService, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(layout).when(serviceFacade, "findLayoutByApiName", any(User.class), anyString(), anyString())
        when:
        relationTemplateService.getTreeModuleConfig(getServiceContext(user,"ContactRelationshipService", "uploadPhone"), arg)
        then:
        true
        where:
        layout       | phoneList
        null         | null
        getILayout() |null;
    }


    ILayout getILayout(){
        ILayout layout = LayoutExt.buildSalesOrderProductListLayout()
        return layout;
    }

}
