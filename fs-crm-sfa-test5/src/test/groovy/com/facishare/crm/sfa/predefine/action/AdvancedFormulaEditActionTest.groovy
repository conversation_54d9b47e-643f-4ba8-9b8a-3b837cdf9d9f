package com.facishare.crm.sfa.predefine.action


import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.predefine.service.AdvancedFormulaService
import com.facishare.crm.sfa.utilities.util.GrayUtil
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ActionContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.CurrencyFieldDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe
import com.facishare.paas.metadata.util.SpringContextUtil
import com.google.common.collect.Lists
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.any

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([I18N.class, GrayUtil.class, SFAConfigUtil.class])
@SuppressStaticInitializationFor([
        "com.facishare.paas.I18N",
        "com.facishare.crm.sfa.utilities.util.GrayUtil", "com.facishare.crm.sfa.utilities.util.SFAConfigUtil"])
class  AdvancedFormulaEditActionTest extends EnhancedBaseGroovyTest {
    @Shared
    protected ActionContext actionContext
    def setupSpec() {
        PowerMockito.mockStatic(I18N.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(SFAConfigUtil.class)
        actionContext = Mock(ActionContext)
        actionContext.getUser() >> Spy(new User("1","1"))
        def stubApplicationContext = Stub(ApplicationContext)
        def util = new SpringContextUtil()
        util.setApplicationContext(stubApplicationContext)
        Map<Class, Object> map = new HashMap<>()
        stubApplicationContext.getBean(_ as Class) >> { Class requiredType ->
            map.computeIfAbsent(requiredType, { key ->
                Stub(requiredType)
            })
        }

        Map<String, Object> map2 = new HashMap<>()
        stubApplicationContext.getBean(_ as String, _ as Class) >> { String name, Class requiredType ->
            map2.computeIfAbsent(name, { key ->
                Stub(requiredType)
            })
        }
    }

    def "test check"() {
        given:
        def tester = PowerMockito.spy(new AdvancedFormulaEditAction())
        actionContext.setAttribute("skipBaseValidate", true)
        tester.actionContext = actionContext
        def advancedFormulaService = PowerMockito.mock(AdvancedFormulaService)
        Whitebox.setInternalState(tester, "advancedFormulaService", advancedFormulaService)
        PowerMockito.doNothing().when(advancedFormulaService, "checkRepeat", any(), any())
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName("BOMObj")
        IFieldDescribe fieldDescribe = field
        fieldDescribe.setApiName("f_abc")
        fieldDescribe.setDefaultValue(defaultValue)
        describe.setFieldDescribes(Lists.newArrayList(fieldDescribe))
        QueryResult<IObjectData> queryResult = new QueryResult();
        queryResult.setData([data])
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.when(serviceFacade.findObject(any(), any())).thenReturn(describe)
        PowerMockito.when(serviceFacade.findBySearchQueryIgnoreAll(any(), any(), any())).thenReturn(queryResult)

        Whitebox.setInternalState(tester, "objectData", data)
        when:
        try {
            Whitebox.invokeMethod(tester, "check")
        } catch (Exception e) {
            assert e instanceof ValidateException
        }

        then:
        1 == 1
        where:
        defaultValue                                                                                                                                                                | field                       | data
        "{\"return_type\":\"number\",\"expression\":\"IF(\$EXT#ATTR#6586c748d37628000788e522\$=='6586c748d37628000788e526', 5, 2)\",\"decimal_places\":2,\"default_to_zero\":true}" | new TextFieldDescribe()     | new ObjectData("_id": "0", "ref_object_api_name": "BOMObj", "product_id": "p1", "ref_field_name": "f_abc", "action_type": "update")
        "{\"return_type\":\"currency\",\"expression\":\"10000+(\$EXT#NON_ATTR#65e9375782823000078eeba6\$-2)/2*2000\",\"decimal_places\":2,\"default_to_zero\":true}"                | new CurrencyFieldDescribe() | new ObjectData("_id": "0", "ref_object_api_name": "BOMObj", "ref_field_name": "f_abc", "action_type": "update")
        "{\"return_type\":\"currency\",\"expression\":\"10000+(\$EXT#NON_ATTR#65e9375782823000078eeba6\$-2)/2*2000\",\"decimal_places\":2,\"default_to_zero\":true}"                | new CurrencyFieldDescribe() | new ObjectData("_id": "0", "ref_object_api_name": "BOMObj", "product_id": "p1", "ref_field_name": "f_abc", "action_type": "update")
        "{\"return_type\":\"currency\",\"expression\":\"10000+(\$EXT#NON_ATTR#65e9375782823000078eeba6\$-2)/2*2000\",\"decimal_places\":2,\"default_to_zero\":true}"                | new CurrencyFieldDescribe() | new ObjectData("_id": "0", "ref_object_api_name": "BOMObj", "bom_id": "p1", "ref_field_name": "f_abc", "formula": "{\"return_type\":\"currency\",\"expression\":\"10000+(\$EXT#NON_ATTR#65e9375782823000078eeba6\$-2)/2*2000\",\"decimal_places\":2,\"default_to_zero\":true}")
    }


}
