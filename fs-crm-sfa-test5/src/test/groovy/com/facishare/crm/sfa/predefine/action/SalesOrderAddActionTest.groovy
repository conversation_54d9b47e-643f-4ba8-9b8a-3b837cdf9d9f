package com.facishare.crm.sfa.predefine.action

import com.facishare.crm.describebuilder.ObjectDescribeBuilder
import com.facishare.crm.rest.JobCenterProxy
import com.facishare.crm.sfa.RemoveUseless
import com.facishare.crm.sfa.predefine.bizvalidator.BizValidator
import com.facishare.crm.sfa.predefine.service.ProductCoreService
import com.facishare.crm.sfa.predefine.service.pricepolicy.DynamicAmortizeService
import com.facishare.crm.sfa.predefine.service.task.BomTaskService
import com.facishare.crm.sfa.utilities.proxy.SalesOrderBizProxy
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.SalesOrderInterceptorModel
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.SalesOrderAddAfterModel
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.common.CommonModel.Result;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil
import com.facishare.crm.sfa.utilities.util.SFARoleUtil
import com.facishare.crm.sfa.utilities.util.SoCommonUtils
import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.util.StopWatch
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction
import com.facishare.paas.appframework.core.predef.action.StandardAction
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.util.SpringContextUtil
import com.facishare.crm.sfa.utilities.util.GrayUtil
import com.facishare.crm.sfa.utilities.util.PromotionUtil
import com.facishare.crm.sfa.utilities.util.SFABizLogUtil
import com.facishare.crm.sfa.utilities.util.SFARestHeaderUtil
import com.google.common.collect.Lists
import com.facishare.crm.sfa.utilities.util.ConfigConstant
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.*

/**
 * @描述说明 ：
 *
 * @作 者：chench
 *
 * @创建日 期：2024-10-16
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([SFARoleUtil.class, ContextManager.class, SFARestHeaderUtil.class, SFAConfigUtil.class, ConfigConstant.class, SFABizLogUtil.class, PromotionUtil.class, GrayUtil.class, SoCommonUtils.class, ParamsIdempotentAddAction.class, BaseObjectSaveAction.class, PreDefineAction.class, StandardAction.class])
@SuppressStaticInitializationFor(["com.facishare.crm.sfa.utilities.util.SFARoleUtil","com.facishare.paas.appframework.core.model.ContextManager", "com.facishare.crm.sfa.utilities.util.SFARestHeaderUtil","com.facishare.crm.sfa.utilities.util.SFAConfigUtil","com.facishare.crm.sfa.utilities.util.ConfigConstant","com.facishare.crm.sfa.utilities.util.SFABizLogUtil","com.facishare.crm.sfa.utilities.util.PromotionUtil","com.facishare.crm.sfa.utilities.util.GrayUtil","com.facishare.crm.sfa.utilities.util.SoCommonUtils","com.facishare.crm.sfa.predefine.action.ParamsIdempotentAddAction","com.facishare.paas.I18N", "com.facishare.paas.appframework.core.predef.action.StandardAction", "com.facishare.paas.appframework.core.model.PreDefineAction"])
class SalesOrderAddActionTest extends RemoveUseless {

    @Shared
    protected ActionContext actionContext;
    @Shared
    protected User user;
    @Shared
    protected SalesOrderAddAction action;
    @Mock
    @Shared
    private ServiceFacade serviceFacade;
    @Mock
    @Shared
    private DynamicAmortizeService dynamicAmortizeService;
    @Mock
    @Shared
    private BomTaskService BOM_TASK_SERVICE;
    @Mock
    @Shared
    private JobCenterProxy jobCenterProxy;
    @Mock
    @Shared
    private ProductCoreService productCoreService;
    @Mock
    @Shared
    private SalesOrderBizProxy salesOrderBizProxy;
    @Mock
    @Shared
    private ThreadPoolTaskExecutor executor;
    @Mock
    @Shared
    private InfraServiceFacade infraServiceFacade;

    def setupSpec() {
        MockitoAnnotations.initMocks(this);

        initSpringContext();
        action = new SalesOrderAddAction();
        actionContext = createActionContext([requestContext: Mock(RequestContext)])
        user = actionContext.getUser();

        actionContext.getAttribute("x-fs-partnertype") >> "1";

        Whitebox.setInternalState(action, "actionContext", actionContext);
        Whitebox.setInternalState(action, "objectData", getObjectData());
        Whitebox.setInternalState(action, "detailObjectData", getDetailObjectData());
        Whitebox.setInternalState(action, "stopWatch", StopWatch.create(getClass().getSimpleName()));

        Whitebox.setInternalState(action, "objectDescribe", getDescribe("SalesOrderObj"));
        Whitebox.setInternalState(action, "objectDescribes", ["SalesOrderProductObj":getDescribe("SalesOrderProductObj")]);

        Whitebox.setInternalState(action, "infraServiceFacade", infraServiceFacade);
        PowerMockito.mockStatic(PreDefineAction.class);
        PowerMockito.mockStatic(StandardAction.class);
        PowerMockito.mockStatic(I18N.class);
        PowerMockito.mockStatic(I18N.I18NContext.class);
    }

    void initSpringContext() {
        def stubApplicationContext = Stub(ApplicationContext)
        def util = new SpringContextUtil()
        util.setApplicationContext(stubApplicationContext)
        Map<Class, Object> map = new HashMap<>()
        stubApplicationContext.getBean(_ as Class) >> { Class requiredType ->
            map.computeIfAbsent(requiredType, { key ->
                Stub(requiredType)
            })
        }

        Map<String, Object> map2 = new HashMap<>()
        stubApplicationContext.getBean(_ as String, _ as Class) >> { String name, Class requiredType ->
            map2.computeIfAbsent(name, { key ->
                Stub(requiredType)
            })
        }

        stubApplicationContext.getBean(_ as String) >> { String name ->
            if ("taskExecutor" == name) {
                Mock(ThreadPoolTaskExecutor)
            } else {
                null
            }
        }
    }

    def "init"() {
        when:
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(ParamsIdempotentAddAction));
        action.init();
        then:
        noExceptionThrown();
    }

    def "doFunPrivilegeCheck"() {
        when:
        Whitebox.setInternalState(action, "serviceFacade", serviceFacade);
        PowerMockito.doReturn(["button_quoteobj2salesorderobj__c": Boolean.TRUE]).when(serviceFacade, "funPrivilegeCheck", any(User), anyString(), anyList());
        PowerMockito.mockStatic(SoCommonUtils.class);
        PowerMockito.when(SoCommonUtils.quoteConvertOrderWithOutAddAuth(anyString())).thenReturn(true);
        action.doFunPrivilegeCheck();
        then:
        noExceptionThrown();
    }

    def "before"() {
        given:
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(ParamsIdempotentAddAction));

        PowerMockito.mockStatic(GrayUtil.class);
        PowerMockito.when(GrayUtil.isChangeToNewOpportunity(any())).thenReturn(true);
        PowerMockito.mockStatic(PromotionUtil.class);
        PowerMockito.when(PromotionUtil.getIsPromotionEnable(any(), any())).thenReturn(true);

        BizValidator validator = PowerMockito.mock(BizValidator.class);
        PowerMockito.doNothing().when(validator, "doValidate");

        PowerMockito.mockStatic(SFABizLogUtil.class);
        PowerMockito.doNothing().when(SFABizLogUtil, "sendAuditLogGray", any(), any());

        PowerMockito.doReturn(null).when(dynamicAmortizeService, "fullDynamicAmortize", any(), any(), any());

        PowerMockito.doNothing().when(BOM_TASK_SERVICE, "handleMasterDataFieldValue", any(), any());

        PowerMockito.doReturn(["button_quoteobj2salesorderobj__c": Boolean.valueOf(needReturn)]).when(serviceFacade, "funPrivilegeCheck", any(User), anyString(), anyList());
        PowerMockito.mockStatic(SoCommonUtils.class);
        PowerMockito.when(SoCommonUtils.quoteConvertOrderWithOutAddAuth(anyString())).thenReturn(true);

        if(!empty) {
            action.objectData.set("customer_account_message", "abc");
        }

        when:
        action.before(new BaseObjectSaveAction.Arg(objectData: ObjectDataDocument.of(action.objectData)));
        then:
        noExceptionThrown();
        where:
        needReturn      || other || empty
        true            || ""    || true
        false           || ""    || true
        false           || ""    || false
    }

    def "validateValidationRules"() {
        when:
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(ParamsIdempotentAddAction));

        PowerMockito.mockStatic(I18N.class);
        PowerMockito.when(I18N.text(anyString())).thenReturn("ok");
        PowerMockito.mockStatic(ConfigConstant.class);
        Whitebox.setInternalState(ConfigConstant.class, "DISPLAY_TIME_COST", Boolean.TRUE);
        PowerMockito.doReturn(null).when(jobCenterProxy, "updateJobStatus", any());
        Whitebox.setInternalState(action, "asyncCreate", asyncCreate);
        Whitebox.setInternalState(action, "jobId", jobId);
        action.validateValidationRules(getObjectData(), getDetailObjectData(), "");
        then:
        noExceptionThrown();
        where:
        asyncCreate || jobId
        false        || ""
        true         || ""
        true         || "111"
    }

    def "callValidationFunction"() {
        when:
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(ParamsIdempotentAddAction));
        Whitebox.setInternalState(action, "asyncCreate", false);
        PowerMockito.mockStatic(I18N.class);
        PowerMockito.when(I18N.text(anyString())).thenReturn("ok");
        action.callValidationFunction();
        then:
        noExceptionThrown();
    }

    def "checkDuplicate"() {
        when:
        PowerMockito.mockStatic(I18N.class);
        PowerMockito.when(I18N.text(anyString())).thenReturn("ok");
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(ParamsIdempotentAddAction));
        Whitebox.setInternalState(action, "asyncCreate", false);
        action.checkDuplicate();
        then:
        noExceptionThrown();
    }

    def "doSaveData"() {
        when:
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(ParamsIdempotentAddAction));
        PowerMockito.doNothing().when(productCoreService, "handleSpuField", any(), any());
        action.doSaveData();
        then:
        noExceptionThrown();
    }

    def "after"() {
        given:
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg(objectData: ObjectDataDocument.of(action.objectData));
        BaseObjectSaveAction.Result result = new BaseObjectSaveAction.Result();
        result.setObjectData(ObjectDataDocument.of(getObjectData()));
        when:
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(ParamsIdempotentAddAction));
        PowerMockito.doNothing().when(BOM_TASK_SERVICE, "sendCreateBomInstanceMessage", any(), any(), any());
        PowerMockito.doNothing().when(BOM_TASK_SERVICE, "sendCreateStandardBomMessage", any(), any(), any());
        PowerMockito.mockStatic(GrayUtil.class);
        PowerMockito.when(GrayUtil.needOptimizeOrder(any())).thenReturn(false);
        PowerMockito.mockStatic(SFAConfigUtil.class);
        PowerMockito.when(SFAConfigUtil.hasErpStock(any())).thenReturn(true);
        PowerMockito.doReturn(getAddAfterResult()).when(salesOrderBizProxy, "addAfter", any(), any())
        PowerMockito.doNothing().when(executor, "execute", any());
        PowerMockito.doNothing().when(dynamicAmortizeService, "saveAmortizeData", any(), any(), any(), any());
        PowerMockito.doNothing().when(infraServiceFacade, "deleteDraftById", any(), any());
        ServiceContext serviceContext = createServiceContext();
        PowerMockito.mockStatic(SFARestHeaderUtil.class);
        PowerMockito.when(SFARestHeaderUtil.getCrmHeader(serviceContext.getTenantId(), serviceContext.getUser())).thenReturn([:]);
        Whitebox.setInternalState(action, "draftId", "11111");
        PowerMockito.mockStatic(SFABizLogUtil.class);
        PowerMockito.doNothing().when(SFABizLogUtil, "sendAuditLog", any(SFABizLogUtil.Arg), any(ActionContext));

        PowerMockito.mockStatic(ContextManager.class);
        PowerMockito.when(ContextManager.buildServiceContext(anyString(), anyString())).thenReturn(serviceContext);
        action.after(arg, result);
        then:
        noExceptionThrown();
    }

    def "getActionListenerClassList"() {
        when:
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(ParamsIdempotentAddAction));
        PowerMockito.mockStatic(GrayUtil.class);
        PowerMockito.when(GrayUtil.needOptimizeOrder(any())).thenReturn(true);
        PowerMockito.mockStatic(SFAConfigUtil.class);
        PowerMockito.when(SFAConfigUtil.hasErpStock(any())).thenReturn(false);
        PowerMockito.when(SFAConfigUtil.hasDhtStock(any())).thenReturn(false);
        PowerMockito.when(SFAConfigUtil.hasDeliveryNote(any())).thenReturn(false);
        action.getActionListenerClassList();
        then:
        noExceptionThrown();
    }


    def "updateOpportunityLastFollowTime"() {
        given:
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(ParamsIdempotentAddAction));
        Whitebox.setInternalState(action, "serviceFacade", serviceFacade);
        when:
        PowerMockito.doReturn(null).when(serviceFacade, "batchUpdateByFields", any(User), any(List), any(List));
        Whitebox.invokeMethod(action, "updateOpportunityLastFollowTime", "2222");
        then:
        noExceptionThrown();
    }

    def "getDeliveryIds"() {
        given:
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(ParamsIdempotentAddAction));
        Whitebox.setInternalState(action, "serviceFacade", serviceFacade);
        when:
        PowerMockito.mockStatic(SFARoleUtil.class);
        PowerMockito.when(SFARoleUtil.getApproverByRoles(any(), any(), any())).thenReturn(["00000000000000000000000000000020":[2222]]);
        Whitebox.invokeMethod(action, "getDeliveryIds", getObjectData());
        then:
        noExceptionThrown();
    }

    def "doAct"() {
        when:
        PowerMockito.mockStatic(I18N.class);
        PowerMockito.when(I18N.text(anyString())).thenReturn("ok");
        PowerMockito.suppress(PowerMockito.methodsDeclaredIn(ParamsIdempotentAddAction));
        PowerMockito.doNothing().when(productCoreService, "handleSpuField", any(), any());
        action.doAct(new BaseObjectSaveAction.Arg(objectData: ObjectDataDocument.of(action.objectData)));
        then:
        noExceptionThrown();
    }

    private ServiceContext createServiceContext() {
        ServiceContext serviceContext = Mock(ServiceContext);
        serviceContext.getTenantId() >> user.getTenantId()
        serviceContext.getUser() >> user;
        return serviceContext;
    }
    private SalesOrderInterceptorModel.AddAfterResult getAddAfterResult() {
        SalesOrderInterceptorModel.AddAfterResult result = new SalesOrderInterceptorModel.AddAfterResult();
        result.setCode(0);
        result.setMessage("ok");
        Result data = new Result();
        result.setData(data);
        return result;
    }
    private IObjectData getObjectData() {
        IObjectData objectData = new ObjectData();
        objectData.set("_id", "id_11");
        objectData.set("requestId", "req_111");
        objectData.set("life_status", "normal");
        objectData.set("owner", ["1000"]);
        return objectData;
    }

    private Map<String, List<IObjectData>> getDetailObjectData() {
        return ["SalesOrderProductObj": Lists.newArrayList(new ObjectData(["_id":"detail_11111"]))];
    }

    def getDescribe(String apiName) {
        ObjectDescribeBuilder.builder()
                .apiName(apiName)
                .displayName(apiName)
                .build()
    }

    ActionContext createActionContext(Map params = [:]) {
        def actionContext = Mock(ActionContext)
        actionContext.requestContext >> (params.requestContext ?: createRequestContext(params))
        actionContext.getTenantId() >> (params.tenantId ?: "90242")
        actionContext.getUser() >> (params.user ?: createUser(params))
        return actionContext
    }

    User createUser(Map params = [:]) {
        def user = Mock(User);
        user.getTenantId() >> (params.tenantId ?: "90242");
        user.getUserId() >> (params.userId ?: "1000");
        user.getUpstreamOwnerIdOrUserId() >>  "2000";
        return user
    }

    RequestContext createRequestContext(Map params = [:]) {
        def requestContext = Mock(RequestContext)
        requestContext.getTenantId() >> (params.tenantId ?: "90242")
        requestContext.getUser() >> (params.user ?: createUser(params))
        return requestContext
    }
}
