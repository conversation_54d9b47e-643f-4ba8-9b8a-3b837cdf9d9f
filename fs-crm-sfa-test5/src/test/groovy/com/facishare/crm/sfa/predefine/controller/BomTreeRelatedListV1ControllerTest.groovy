package com.facishare.crm.sfa.predefine.controller

import com.alibaba.fastjson.JSON
import com.facishare.crm.sfa.RemoveUseless
import com.facishare.crm.sfa.predefine.service.AvailableRangeCoreService
import com.facishare.crm.sfa.predefine.service.NonstandardAttributeService
import com.facishare.crm.sfa.predefine.service.attribute.AttributeCoreService
import com.facishare.crm.sfa.predefine.service.attribute.model.Attribute
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.predefine.service.cpq.*
import com.facishare.crm.sfa.predefine.service.cpq.model.QueryAllSubBomModel
import com.facishare.crm.sfa.utilities.util.GrayUtil
import com.facishare.crm.sfa.utilities.util.Price.RealPriceModel
import com.facishare.crm.sfa.utilities.util.ProductConstraintUtil
import com.facishare.crm.util.MtCurrentUtil
import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.controller.StandardController
import com.facishare.paas.appframework.core.predef.controller.StandardTreeRelatedListV1Controller
import com.facishare.paas.appframework.core.util.RequestUtil
import com.facishare.paas.appframework.metadata.LayoutLogicService
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.NameCache
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.impl.ui.layout.Layout
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent
import com.facishare.paas.metadata.ui.layout.ILayout
import com.facishare.paas.metadata.util.SpringContextUtil
import com.facishare.paas.metadata.util.SpringUtil
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.LoggerFactory
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.spy
import static org.powermock.reflect.Whitebox.setInternalState

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["org.springframework.*", "javax.management.*"])
@PrepareForTest([RequestUtil.class, AppFrameworkConfig.class, GrayUtil.class, I18N.class, SpringUtil.class, StandardController.class, PreDefineController.class, StandardTreeRelatedListV1Controller.class, MtCurrentUtil.class, ProductConstraintUtil.class])
@SuppressStaticInitializationFor(["com.facishare.crm.sfa.utilities.util.GrayUtil","com.facishare.paas.appframework.common.util.AppFrameworkConfig", "com.facishare.paas.appframework.core.model.PreDefineController", "com.facishare.paas.appframework.core.predef.controller.StandardController", "com.facishare.paas.metadata.util.SpringUtil", "com.facishare.paas.I18N", "com.facishare.crm.util.MtCurrentUtil", "com.facishare.crm.sfa.utilities.util.ProductConstraintUtil"])

class BomTreeRelatedListV1ControllerTest extends RemoveUseless {

    @Shared
    protected ControllerContext controllerContext
    @Shared
    protected BomTreeRelatedListV1Controller tester
    @Shared
    protected User user
    @Shared
    private BomCoreService bomCoreService

    def setupSpec(){
        def stubApplicationContext = Stub(ApplicationContext)
        def util = new SpringContextUtil()
        util.setApplicationContext(stubApplicationContext)
        Map<Class, Object> map = new HashMap<>()
        stubApplicationContext.getBean(_ as Class) >> { Class requiredType ->
            map.computeIfAbsent(requiredType, { key ->
                Stub(requiredType)
            })
        }

        Map<String, Object> map2 = new HashMap<>()
        stubApplicationContext.getBean(_ as String, _ as Class) >> { String name, Class requiredType ->
            map2.computeIfAbsent(name, { key ->
                Stub(requiredType)
            })
        }
        setInternalState(I18N, "THREAD_LOCAL", new InheritableThreadLocal<>())
        setInternalState(I18N, "I18N_RESOURCES", Lists.newArrayList())
        setInternalState(I18N, "log", LoggerFactory.getLogger(I18N.class))
        tester = spy(new BomTreeRelatedListV1Controller())
        controllerContext = Mock(ControllerContext)
        user = Spy(new User("1", "1"))
        user.getUpstreamOwnerIdOrUserId()>>"1"
        controllerContext.getUser() >> user
        RequestContext requestContext = Mock(RequestContext)
        controllerContext.getRequestContext()>>requestContext
        setInternalState(tester, "controllerContext", controllerContext)
        bomCoreService = PowerMockito.mock(BomCoreService)
        setInternalState(tester, "bomCoreService", bomCoreService)
    }

    def setup() {
        PowerMockito.mockStatic(StandardController.class)
        PowerMockito.mockStatic(PreDefineController.class)
        PowerMockito.mockStatic(I18N.class)
        PowerMockito.mockStatic(GrayUtil)
        PowerMockito.mockStatic(MtCurrentUtil)
        PowerMockito.mockStatic(ProductConstraintUtil)
        PowerMockito.mockStatic(RequestUtil)
        PowerMockito.mockStatic(AppFrameworkConfig)
    }

    def "test queryAllProductList"() {
        given:
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(null).when(serviceFacade, "findObjectDataByIdsIgnoreRelevantTeam", any(), any(), any())
        when:
        Whitebox.invokeMethod(tester, "queryAllProductList", ObjectDataDocument.ofList([new ObjectData("_id": "1", "product_id": "p1")]))
        then:
        1 == 1
    }

    def "test fillAttrInfo"() {
        given:
        StandardTreeRelatedListV1Controller.Arg arg = new StandardTreeRelatedListV1Controller.Arg()
        arg.setRootProductIds(Sets.newHashSet("1"))
        Whitebox.setInternalState(tester, "arg", arg)
        def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        Whitebox.setInternalState(tester, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenAttribute", any())
        Attribute attr = new Attribute()
        attr.setId("a1")
        def attributeCoreService = PowerMockito.mock(AttributeCoreService)
        Whitebox.setInternalState(tester, "attributeCoreService", attributeCoreService)
        PowerMockito.doReturn(["p1": Lists.newArrayList(attr)]).when(attributeCoreService, "getAttributeByProductIds", any(), any(), eq(false), any())
        def nonstandardAttributeService = PowerMockito.mock(NonstandardAttributeService)
        Whitebox.setInternalState(tester, "nonstandardAttributeService", nonstandardAttributeService)
        PowerMockito.doReturn(["p1": [new ObjectData("_id": "1")]]).when(nonstandardAttributeService, "getNonstandardAttributeDataByProductIds", any(), any(), any())
        when:
        Whitebox.invokeMethod(tester, "fillAttrInfo", arg, ObjectDataDocument.ofList([new ObjectData("_id": "1", "product_id": "p1")]))
        then:
        1 == 1
    }

    def "test fillPriceBookProduct"() {
        given:
        StandardTreeRelatedListV1Controller.Arg arg = new StandardTreeRelatedListV1Controller.Arg()
        arg.setRootProductIds(Sets.newHashSet("1"))
        Whitebox.setInternalState(tester, "arg", arg)


        def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        Whitebox.setInternalState(tester, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isCurrencyEnabled", any())
        Attribute attr = new Attribute()
        attr.setId("a1")
        def bomCoreService = PowerMockito.mock(BomCoreService)
        Whitebox.setInternalState(tester, "bomCoreService", bomCoreService)
        PowerMockito.doReturn([new ObjectData("_id": "1", "product_id": "p1")]).when(bomCoreService, "getPriceBookProductList", any(), any(), any())
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn([new NameCache(["name": "111"])]).when(serviceFacade, "findRecordName", any(), any(), any())
        PowerMockito.when(MtCurrentUtil.getExchangeRateMap(any() as User, any() as String)).thenReturn(["1": "1"])

        StandardTreeRelatedListV1Controller.Result result = new StandardTreeRelatedListV1Controller.Result()
        result.setDataMapList(Lists.newArrayList())
        when:
        Whitebox.invokeMethod(tester, "fillPriceBookProduct", ObjectDataDocument.ofList([new ObjectData("_id": "1", "product_id": "p1")]), "price1", result, "mc_currency")
        then:
        1 == 1
    }

    def "test fillPriceBookProduct2"() {
        given:
        StandardTreeRelatedListV1Controller.Arg arg = new StandardTreeRelatedListV1Controller.Arg()
        arg.setRootProductIds(Sets.newHashSet("1"))
        Whitebox.setInternalState(tester, "arg", arg)


        def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        Whitebox.setInternalState(tester, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isCurrencyEnabled", any())
        Attribute attr = new Attribute()
        attr.setId("a1")
        def bomCoreService = PowerMockito.mock(BomCoreService)
        Whitebox.setInternalState(tester, "bomCoreService", bomCoreService)
        PowerMockito.doReturn([new ObjectData("_id": "1", "product_id": "p1")]).when(bomCoreService, "getPriceBookProductList", any(), any(), any())
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn([new NameCache(["name": "111"])]).when(serviceFacade, "findRecordName", any(), any(), any())
        PowerMockito.when(MtCurrentUtil.getExchangeRateMap(any() as User, any() as String)).thenReturn(["1": "1"])

        StandardTreeRelatedListV1Controller.Result result = new StandardTreeRelatedListV1Controller.Result()
        result.setDataMapList(Lists.newArrayList())
        when:
        Whitebox.invokeMethod(tester, "fillPriceBookProduct", "price1", result, "mc_currency")
        then:
        1 == 1
    }

    def "test getBomPriceByAccountIdAndProId"() {
        given:
        StandardTreeRelatedListV1Controller.Arg arg = new StandardTreeRelatedListV1Controller.Arg()
        arg.setRootProductIds(Sets.newHashSet("1"))
        arg.setAccountId(accountId)
        arg.setBomList(bomList)
        Whitebox.setInternalState(tester, "arg", arg)


        def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        Whitebox.setInternalState(tester, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isEnforcePriority", any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "bomAdaptationPriceListRules", any())
        def availableRangeCoreService = PowerMockito.mock(AvailableRangeCoreService)
        Whitebox.setInternalState(tester, "availableRangeCoreService", availableRangeCoreService)
        RealPriceModel.Result priceRe = new RealPriceModel.Result()
        priceRe.setNewRst(ObjectDataDocument.ofList([new ObjectData("_id": "1", "product_id": "p1", "bom_id": "1")]))
        PowerMockito.when(availableRangeCoreService.getRealPrice(any() as User, any() as RealPriceModel.Arg)).thenReturn(priceRe)

        when:
        Whitebox.invokeMethod(tester, "getBomPriceByAccountIdAndProId", ObjectDataDocument.ofList([new ObjectData("_id": "1", "product_id": "p1", "price_mode": "2")]))
        then:
        1 == 1
        where:
        accountId | bomList
        ""        | []
        "1"       | []
        "1"       | [ObjectDataDocument.of(new ObjectData("_id": "1", "product_id": "p1", "bom_id": "1"))]
    }

    def "test fillProductConstraint"() {
        given:
        Whitebox.setInternalState(tester, "needProductConstraint", true)
        Whitebox.setInternalState(tester, "globalRootBomList", globalRootBomList)
        Whitebox.setInternalState(tester, "bomCoreId", "1")
        StandardTreeRelatedListV1Controller.Arg arg = new StandardTreeRelatedListV1Controller.Arg()
        arg.setRootProductIds(Sets.newHashSet("1"))
        Whitebox.setInternalState(tester, "arg", arg)


        def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        Whitebox.setInternalState(tester, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isEnforcePriority", any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "bomAdaptationPriceListRules", any())
        def availableRangeCoreService = PowerMockito.mock(AvailableRangeCoreService)
        Whitebox.setInternalState(tester, "availableRangeCoreService", availableRangeCoreService)
        RealPriceModel.Result priceRe = new RealPriceModel.Result()
        priceRe.setNewRst(ObjectDataDocument.ofList([new ObjectData("_id": "1", "product_id": "p1")]))
        PowerMockito.when(availableRangeCoreService.getRealPrice(any() as User, any() as RealPriceModel.Arg)).thenReturn(priceRe)


        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(objectDesc).when(serviceFacade, "findObject", any(), any())

        def bomConstraintService = PowerMockito.mock(BomConstraintService)
        Whitebox.setInternalState(tester, "bomConstraintService", bomConstraintService)
        PowerMockito.doReturn([new ObjectData("_id": "id1")]).when(bomConstraintService, "findBomConstrainByRootBomId", any(), any(), eq(false))
        PowerMockito.doReturn([new ObjectData("_id": "id1")]).when(bomConstraintService, "findBomConstraintLinesByMasterId", any(), any())
        PowerMockito.doReturn(bomConstrain).when(bomConstraintService, "findBomConstrainByCoreId", any(), any(), eq(false))
        StandardTreeRelatedListV1Controller.Result result = new StandardTreeRelatedListV1Controller.Result()
        result.setDataMapList(Lists.newArrayList())

        def productConstraintService = PowerMockito.mock(ProductConstraintService)
        Whitebox.setInternalState(tester, "productConstraintService", productConstraintService)
        PowerMockito.doReturn([new ObjectData("_id": "1", "product_id": "p1")]).when(productConstraintService, "findProductConstrainByRootBomId", any(), any())
        def constraintLinesService = PowerMockito.mock(ProductConstraintLinesService)
        Whitebox.setInternalState(tester, "constraintLinesService", constraintLinesService)
        PowerMockito.doReturn([new ObjectData("up_bom_id": "b1", "down_bom_id": "b2")]).when(constraintLinesService, "findProductConstraintLinesByMasterId", any(), any())
        PowerMockito.doReturn([new ObjectData("_id": "b1", "bom_path": "b2"), new ObjectData("_id": "b2", "bom_path": "b2")]).when(serviceFacade, "findObjectDataByIds", any(), any(), any())

        when:
        Whitebox.invokeMethod(tester, "fillProductConstraint", result)
        then:
        1 == 1
        where:
        objectDesc           | globalRootBomList                                                           | bomConstrain
        null                 | []                                                                          | []
        null                 | ObjectDataDocument.ofList([new ObjectData("_id": "1", "product_id": "p1")]) | []
        new ObjectDescribe() | ObjectDataDocument.ofList([new ObjectData("_id": "1", "product_id": "p1")]) | []
        new ObjectDescribe() | ObjectDataDocument.ofList([new ObjectData("_id": "1", "product_id": "p1")]) | [new ObjectData("check_mode": "1", "apl_api_name": ["name": "1"])]
        null                 | ObjectDataDocument.ofList([new ObjectData("_id": "1", "product_id": "p1")]) | [new ObjectData("check_mode": "1", "apl_api_name": "p1")]
    }

    def "test buildTreeSearchTemplateQuery"() {
        given:
        Whitebox.setInternalState(tester, "needProductConstraint", true)
        Whitebox.setInternalState(tester, "bomCoreId", "1")
        Whitebox.setInternalState(tester, "enableBomCore", enableBomCore)
        StandardTreeRelatedListV1Controller.Arg arg = new StandardTreeRelatedListV1Controller.Arg()
        arg.setRootProductIds(Sets.newHashSet("1"))
        arg.setQueryFirstLevelChild(true)
        Whitebox.setInternalState(tester, "arg", arg)

        QueryResult<IObjectData> rootBomList = new QueryResult()
        rootBomList.setData(dataList)
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(new SearchTemplateQuery()).when(serviceFacade, "getSearchTemplateQuery", any(), any(), any(), any(), eq(true))
        PowerMockito.doReturn(rootBomList).when(serviceFacade, "findBySearchQueryIgnoreAll", any(), any(), any())

        def bomCoreService = PowerMockito.mock(BomCoreService)
        Whitebox.setInternalState(tester, "bomCoreService", bomCoreService)
        PowerMockito.doReturn(rootBomList).when(bomCoreService, "findBomByRootProductId", any(), any(), any())
        when:
        tester.buildTreeSearchTemplateQuery(ObjectDescribeExt.of(new ObjectDescribe()), "{\"limit\":2000,\"offset\":0,\"filters\":[{\"field_name\":\"enabled_status\",\"field_values\":true,\"operator\":\"EQ\"}]}")
        then:
        1 == 1
        where:
        enableBomCore | dataList
        true          | []
        true          | [new ObjectData("_id": "1", "product_id": "p1")]
        false         | [new ObjectData("_id": "1", "product_id": "p1")]
        false         | [new ObjectData("_id": "1", "product_id": "p1"), new ObjectData("_id": "2", "product_id": "p2")]
    }


    def "test fillProductGroup"() {
        given:
        Whitebox.setInternalState(tester, "needProductConstraint", true)
        Whitebox.setInternalState(tester, "globalRootBomList", globalRootBomList)
        Whitebox.setInternalState(tester, "bomCoreId", "1")
        Whitebox.setInternalState(tester, "allProductDataList", Lists.newArrayList())
        StandardTreeRelatedListV1Controller.Arg arg = new StandardTreeRelatedListV1Controller.Arg()
        arg.setRootProductIds(Sets.newHashSet("1"))
        arg.setFilterEmptyGroups(true)
        arg.setPageFlag(pageFlag)
        Whitebox.setInternalState(tester, "arg", arg)

        StandardTreeRelatedListV1Controller.Result result = new StandardTreeRelatedListV1Controller.Result()
        result.setDataMapList(Lists.newArrayList())
        QueryResult<IObjectData> rootBomList = new QueryResult()
        rootBomList.setData([new ObjectData("_id": "1", "product_id": "p1", "name": "n1")])
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn([new ObjectData("_id": "1", "product_id": "p1", "name": "n1")]).when(serviceFacade, "findObjectDataByIds", any(), any(), any())
        PowerMockito.doReturn(rootBomList).when(serviceFacade, "findBySearchQueryIgnoreAll", any(), any(), any())
        PowerMockito.when(GrayUtil.isBOMSearchDB(anyString())).thenReturn(true)
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(rootBomList)
        bomCoreService = PowerMockito.mock(BomCoreService)
        Whitebox.setInternalState(tester, "bomCoreService", bomCoreService)
        PowerMockito.when(bomCoreService.getNewTemplateQuery()).thenReturn(new SearchTemplateQuery())
        when:
        Whitebox.invokeMethod(tester, "fillProductGroup", ObjectDataDocument.ofList([new ObjectData("_id": "1", "product_id": "p1", "bom_path": "1.2.3", "root_id": "1", "product_group_id": "g1"), new ObjectData("_id": "2", "product_id": "p1", "bom_path": "1.2.3.4", "parent_bom_id": "1", "root_id": "1", "product_group_id": "g2"), new ObjectData("_id": "3", "product_id": "p1", "bom_path": "1.2.3.4.5", "parent_bom_id": "2", "root_id": "1", "product_group_id": "g2")]), result)
        then:
        1 == 1
        where:
        pageFlag | globalRootBomList
        true     | ObjectDataDocument.ofList([new ObjectData("_id": "1", "product_id": "p1")])
        false    | ObjectDataDocument.ofList([new ObjectData("_id": "1", "product_id": "p1")])
    }


    def "test fillRootProduct"() {
        given:
        Whitebox.setInternalState(tester, "nonstandardAttributeMap", [:])
        Whitebox.setInternalState(tester, "globalRootBomList", ObjectDataDocument.ofList([new ObjectData("_id": "1", "product_id": "p1")]))
        Whitebox.setInternalState(tester, "allProductDataList", [new ObjectData("_id": "p1", "product_id": "p1")])
        Whitebox.setInternalState(tester, "bomCoreId", "1")
        Whitebox.setInternalState(tester, "enableBomCore", true)
        StandardTreeRelatedListV1Controller.Arg arg = new StandardTreeRelatedListV1Controller.Arg()
        arg.setRootProductIds(Sets.newHashSet("1"))
        arg.setFilterEmptyGroups(true)
        Whitebox.setInternalState(tester, "arg", arg)

        StandardTreeRelatedListV1Controller.Result result = new StandardTreeRelatedListV1Controller.Result()
        result.setDataMapList(Lists.newArrayList())
        when:
        Whitebox.invokeMethod(tester, "fillRootProduct", arg, result)
        then:
        1 == 1
    }

    def "test fillData"() {
        given:
        Whitebox.setInternalState(tester, "nonstandardAttributeMap", [:])
        Whitebox.setInternalState(tester, "globalRootBomList", ObjectDataDocument.ofList([new ObjectData("_id": "1", "product_id": "p1")]))
        Whitebox.setInternalState(tester, "allProductDataList", [new ObjectData("_id": "p1", "product_id": "p1", "unit": "个")])
        Whitebox.setInternalState(tester, "bomCoreId", "1")
        Whitebox.setInternalState(tester, "enableBomCore", true)
        StandardTreeRelatedListV1Controller.Arg arg = new StandardTreeRelatedListV1Controller.Arg()
        arg.setRootProductIds(Sets.newHashSet("1"))
        arg.setFilterEmptyGroups(true)
        Whitebox.setInternalState(tester, "arg", arg)

        StandardTreeRelatedListV1Controller.Result result = new StandardTreeRelatedListV1Controller.Result()
        result.setDataMapList(Lists.newArrayList())
        IObjectDescribe productObjDesc = new ObjectDescribe()
        IFieldDescribe fieldDescribe = new SelectOneFieldDescribe()
        fieldDescribe.setApiName("unit")
        fieldDescribe.set("options", [["个": "个"]])
        productObjDesc.addFieldDescribe(fieldDescribe)
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(productObjDesc).when(serviceFacade, "findObject", any(), any())
        when:
        Whitebox.invokeMethod(tester, "fillData", ObjectDataDocument.ofList([new ObjectData("_id": "1", "product_id": "p1", "bom_path": "1.2", "node_type": "temp")]), 1)
        then:
        1 == 1
    }

    def "test buildResult"() {
        given:
        IFieldDescribe adjustPrice = new TextFieldDescribe()
        adjustPrice.setApiName("adjust_price")
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName("BOMObj")
        describe.addFieldDescribe(adjustPrice)
        Whitebox.setInternalState(tester, "objectDescribe", ObjectDescribeExt.of(describe))
        StandardTreeRelatedListV1Controller.Arg arg = new StandardTreeRelatedListV1Controller.Arg()
        arg.setIncludeDesc(true)
        Whitebox.setInternalState(tester, "arg", arg)
        PowerMockito.when(RequestUtil.isMobileRequest()).thenReturn(true)
        when:
        tester.buildResult(Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList())
        then:
        1 == 1
    }

    def "test getChildDetailLayout"() {
        given:
        IFieldDescribe adjustPrice = new TextFieldDescribe()
        adjustPrice.setApiName("adjust_price")
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName("BOMObj")
        describe.addFieldDescribe(adjustPrice)
        Whitebox.setInternalState(tester, "objectDescribe", ObjectDescribeExt.of(describe))
        StandardTreeRelatedListV1Controller.Arg arg = new StandardTreeRelatedListV1Controller.Arg()
        arg.setIncludeDesc(true)
        Whitebox.setInternalState(tester, "arg", arg)
        PowerMockito.when(RequestUtil.isMobileRequest()).thenReturn(true)
        String str = "{\"field_section\":[{\"show_header\":true,\"form_fields\":[{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"object_reference\",\"field_name\":\"product_id\",\"target_display_name\":\"产品\"},{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"number\",\"field_name\":\"order_field\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"currency\",\"field_name\":\"adjust_price\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"number\",\"field_name\":\"amount\"},{\"is_readonly\":false,\"is_required\":false,\"is_tiled\":false,\"render_type\":\"select_one\",\"field_name\":\"price_mode\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"true_or_false\",\"field_name\":\"amount_any\"},{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"true_or_false\",\"field_name\":\"selected_by_default\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"text\",\"field_name\":\"node_bom_core_version\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"master_detail\",\"field_name\":\"core_id\",\"target_display_name\":\"产品组合\"},{\"is_readonly\":true,\"is_required\":false,\"is_tiled\":true,\"render_type\":\"select_one\",\"field_name\":\"node_bom_core_type\"},{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"true_or_false\",\"field_name\":\"is_required\"},{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"true_or_false\",\"field_name\":\"price_editable\"},{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"true_or_false\",\"field_name\":\"amount_editable\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"number\",\"field_name\":\"max_amount\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"number\",\"field_name\":\"min_amount\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"number\",\"field_name\":\"increment\"},{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"true_or_false\",\"field_name\":\"enabled_status\"},{\"is_readonly\":false,\"full_line\":false,\"is_required\":false,\"render_type\":\"long_text\",\"field_name\":\"remark\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"object_reference\",\"field_name\":\"product_group_id\",\"target_display_name\":\"产品分组\"},{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"auto_number\",\"field_name\":\"name\"},{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"employee\",\"field_name\":\"owner\"},{\"is_readonly\":false,\"is_required\":false,\"is_tiled\":true,\"render_type\":\"select_one\",\"field_name\":\"field_Q1v5m__c\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"currency\",\"field_name\":\"field_7gDe2__c\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"currency\",\"field_name\":\"field_74B23__c\"},{\"is_required\":false,\"field_name\":\"mc_exchange_rate\",\"render_type\":\"number\"},{\"is_required\":false,\"field_name\":\"owner_department\",\"render_type\":\"text\"},{\"is_required\":false,\"field_name\":\"lock_status\",\"render_type\":\"select_one\"},{\"is_required\":false,\"field_name\":\"product_life_status\",\"render_type\":\"quote\"},{\"is_required\":false,\"field_name\":\"share_rate\",\"render_type\":\"percentile\"},{\"is_required\":false,\"field_name\":\"data_own_department\",\"render_type\":\"department\"},{\"is_required\":false,\"field_name\":\"product_status\",\"render_type\":\"quote\"},{\"is_required\":false,\"field_name\":\"out_owner\",\"render_type\":\"employee\"},{\"is_required\":false,\"field_name\":\"is_package\",\"render_type\":\"true_or_false\"},{\"is_required\":false,\"field_name\":\"life_status\",\"render_type\":\"select_one\"},{\"is_required\":false,\"field_name\":\"mc_currency\",\"render_type\":\"select_one\"},{\"is_required\":false,\"field_name\":\"record_type\",\"render_type\":\"record_type\"}],\"api_name\":\"base_field_section__c\",\"tab_index\":\"ltr\",\"column\":2,\"header\":\"基本信息\",\"is_show\":true},{\"show_header\":true,\"form_fields\":[{\"is_readonly\":true,\"is_required\":false,\"render_type\":\"employee\",\"field_name\":\"created_by\"},{\"is_readonly\":true,\"is_required\":false,\"render_type\":\"employee\",\"field_name\":\"last_modified_by\"},{\"is_readonly\":true,\"is_required\":false,\"render_type\":\"date_time\",\"field_name\":\"create_time\"},{\"is_readonly\":true,\"is_required\":false,\"render_type\":\"date_time\",\"field_name\":\"last_modified_time\"}],\"api_name\":\"sysinfo_section__c\",\"tab_index\":\"ltr\",\"column\":2,\"header\":\"系统信息\",\"is_show\":true}],\"buttons\":[],\"related_list_name\":\"\",\"column\":2,\"is_hidden\":false,\"nameI18nKey\":\"paas.udobj.detail_info\",\"type\":\"form\",\"isSticky\":false,\"api_name\":\"form_component\",\"header\":\"详细信息\",\"_id\":\"form_component\",\"grayLimit\":1,\"order\":8}"
        Map map = JSON.parseObject(str, Map.class)
        FormComponent formComponent = new FormComponent(map)
        ILayout childLayout = new Layout()
        childLayout.setComponents(Lists.newArrayList(formComponent))
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.when(serviceFacade.findDefaultLayout(any() as LayoutLogicService.LayoutContext, any() as String, any() as String)).thenReturn(childLayout)
        PowerMockito.when(RequestUtil.isMobileRequest()).thenReturn(true)
        when:
        tester.getChildDetailLayout()
        then:
        1 == 1
    }

    def "test init"() {
        given:
        IFieldDescribe adjustPrice = new TextFieldDescribe()
        adjustPrice.setApiName("adjust_price")
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName("BOMObj")
        describe.addFieldDescribe(adjustPrice)
        Whitebox.setInternalState(tester, "objectDescribe", ObjectDescribeExt.of(describe))
        StandardTreeRelatedListV1Controller.Arg arg = new StandardTreeRelatedListV1Controller.Arg()
        arg.setRootProductIds(rootIds as Set<String>)
        arg.setBomId(bomId)
        arg.setRootBomCoreId(coreId)
        arg.setRootProductList(rootProductList)
        Whitebox.setInternalState(tester, "arg", arg)
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(describe).when(serviceFacade, "findObject", any(), any())
        def bomService = PowerMockito.mock(BomService)
        Whitebox.setInternalState(tester, "bomService", bomService)
        PowerMockito.doReturn([new ObjectData("_id": "1")]).when(bomService, "getBomsByProductId", any(), any())
        PowerMockito.doReturn([new ObjectData("_id": "1")]).when(bomService, "getBomListByIds", any(), any())
        PowerMockito.doReturn(new ObjectData("_id": "1")).when(bomService, "getRootBomByCoreId", any(), any(), eq(false))
        PowerMockito.when(GrayUtil.bomMasterSlaveMode(any())).thenReturn(true)
        when:
        tester.init()
        then:
        1 == 1
        where:
        coreId | bomId | rootIds              | rootProductList
        ""     | "1"   | Sets.newHashSet("1") | []
        "1"    | ""    | Sets.newHashSet("1") | []
        ""     | ""    | Sets.newHashSet("1") | []
        ""     | ""    | Sets.newHashSet("1") | [ObjectDataDocument.of(new ObjectData("product_id": "1"))]
    }

    def "test after"() {
        given:
        IFieldDescribe adjustPrice = new TextFieldDescribe()
        adjustPrice.setApiName("price_mode")
        IFieldDescribe unit = new TextFieldDescribe()
        adjustPrice.setApiName("unit")
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName("BOMObj")
        describe.addFieldDescribe(adjustPrice)
        describe.addFieldDescribe(unit)
        Whitebox.setInternalState(tester, "objectDescribe", ObjectDescribeExt.of(describe))
        Whitebox.setInternalState(tester, "enableBomCore", true)
        Whitebox.setInternalState(tester, "globalRootBomList", [ObjectDataDocument.of(new ObjectData("node_type":"temp","product_id":"p1"))])
        StandardTreeRelatedListV1Controller.Arg arg = new StandardTreeRelatedListV1Controller.Arg()
        arg.setRootProductIds(Sets.newHashSet("1"))
        arg.setIncludeAllSubCoreId(true)
        arg.setBomList([ObjectDataDocument.of(new ObjectData("node_type":"temp","product_id":"p1"))])
        Whitebox.setInternalState(tester, "arg", arg)

        def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        Whitebox.setInternalState(tester, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isCurrencyEnabled", any())
        Attribute attr = new Attribute()
        attr.setId("a1")
        bomCoreService = PowerMockito.mock(BomCoreService)
        PowerMockito.doReturn([new ObjectData("_id": "1", "product_id": "p1")]).when(bomCoreService, "getPriceBookProductList", any(), any(), any())
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn([new NameCache(["name": "111"])]).when(serviceFacade, "findRecordName", any(), any(), any())
        PowerMockito.doReturn(describe).when(serviceFacade, "findObject", any(), any())
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData([new ObjectData("_id": "1", "product_id": "p1", "name": "n1")])
        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQueryIgnoreAll", any() as User, any() as String,any() as SearchTemplateQuery)
        PowerMockito.when(GrayUtil.grayAsynBomPrice(any())).thenReturn(true)

        StandardTreeRelatedListV1Controller.Result result = new StandardTreeRelatedListV1Controller.Result()
        result.setDataMapList(Lists.newArrayList())
        bomCoreService = PowerMockito.mock(BomCoreService)
        Whitebox.setInternalState(tester, "bomCoreService", bomCoreService)
        PowerMockito.when(bomCoreService.getNewTemplateQuery()).thenReturn(new SearchTemplateQuery())
        def bomService = PowerMockito.mock(BomService)
        Whitebox.setInternalState(tester, "bomService", bomService)

        PowerMockito.when(bomService.queryAllSubBomCore(any(), any())).thenReturn(QueryAllSubBomModel.Result.builder().allSubBomList(Lists.newArrayList(QueryAllSubBomModel.SubBomInfo.builder().bomCoreId("1").build())).build())
        when:
        tester.after(arg,result)
        then:
        1 == 1
    }

}
