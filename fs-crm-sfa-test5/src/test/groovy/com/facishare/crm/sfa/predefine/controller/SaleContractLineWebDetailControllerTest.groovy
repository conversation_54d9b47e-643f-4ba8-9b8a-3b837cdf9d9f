package com.facishare.crm.sfa.predefine.controller

import com.alibaba.fastjson.JSON
import com.facishare.crm.openapi.Utils
import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants
import com.facishare.crm.sfa.utilities.util.GrayUtil
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController
import com.facishare.paas.appframework.core.predef.controller.StandardController
import com.facishare.paas.appframework.core.util.RequestUtil
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.ui.layout.Layout
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent
import com.facishare.paas.metadata.ui.layout.ILayout
import com.facishare.paas.metadata.util.SpringContextUtil
import com.facishare.paas.metadata.util.SpringUtil
import com.google.common.collect.Lists
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.LoggerFactory
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared

import static org.powermock.api.mockito.PowerMockito.spy
import static org.powermock.reflect.Whitebox.setInternalState

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([PreDefLayoutUtil.class, UdobjGrayConfig.class, RequestUtil.class, GrayUtil.class, I18N.class, SpringUtil.class, StandardController.class, PreDefineController.class])
@SuppressStaticInitializationFor(["com.facishare.crm.sfa.utilities.util.GrayUtil", "com.facishare.paas.appframework.core.model.PreDefineController", "com.facishare.paas.appframework.core.predef.controller.StandardController", "com.facishare.paas.I18N", "com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil"])
class SaleContractLineWebDetailControllerTest extends EnhancedBaseGroovyTest {
    @Shared
    protected ControllerContext controllerContext
    @Shared
    protected User user

    def setupSpec() {
        PowerMockito.mockStatic(StandardController.class)
        PowerMockito.mockStatic(PreDefineController.class)
        PowerMockito.mockStatic(I18N.class)
        PowerMockito.mockStatic(GrayUtil)
        PowerMockito.mockStatic(RequestUtil)
        PowerMockito.mockStatic(PreDefLayoutUtil)
        PowerMockito.mockStatic(UdobjGrayConfig)
        def stubApplicationContext = Stub(ApplicationContext)
        def util = new SpringContextUtil()
        util.setApplicationContext(stubApplicationContext)
        Map<Class, Object> map = new HashMap<>()
        stubApplicationContext.getBean(_ as Class) >> { Class requiredType ->
            map.computeIfAbsent(requiredType, { key ->
                Stub(requiredType)
            })
        }

        Map<String, Object> map2 = new HashMap<>()
        stubApplicationContext.getBean(_ as String, _ as Class) >> { String name, Class requiredType ->
            map2.computeIfAbsent(name, { key ->
                Stub(requiredType)
            })
        }
        setInternalState(I18N, "THREAD_LOCAL", new InheritableThreadLocal<>())
        setInternalState(I18N, "I18N_RESOURCES", Lists.newArrayList())
        setInternalState(I18N, "log", LoggerFactory.getLogger(I18N.class))
        controllerContext = Mock(ControllerContext)
        user = Spy(new User("1", "1"))
        user.getUpstreamOwnerIdOrUserId()>>"1"
        controllerContext.getUser() >> user
        RequestContext requestContext = Mock(RequestContext)
        controllerContext.getRequestContext()>>requestContext
    }

    def setup() {
        removeConfigFactory()
        removeI18N()
    }


    def "test after"() {
        given:
        SaleContractLineWebDetailController tester = spy(new SaleContractLineWebDetailController())
        Whitebox.setInternalState(tester, "controllerContext", controllerContext)
        AbstractStandardDetailController.Arg arg = new AbstractStandardDetailController.Arg()
        AbstractStandardDetailController.Result result = new AbstractStandardDetailController.Result()
        arg.setLayoutAgentType(SalesOrderConstants.MOBILE)
        arg.setObjectDescribeApiName(Utils.SALES_ORDER_API_NAME)
        Whitebox.setInternalState(tester, "data", new ObjectData())
        PowerMockito.when(RequestUtil.isMobileRequest()).thenReturn(true)
        String str = "{\"field_section\":[{\"show_header\":true,\"form_fields\":[{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"object_reference\",\"field_name\":\"product_id\",\"target_display_name\":\"产品\"},{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"number\",\"field_name\":\"order_field\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"currency\",\"field_name\":\"adjust_price\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"number\",\"field_name\":\"amount\"},{\"is_readonly\":false,\"is_required\":false,\"is_tiled\":false,\"render_type\":\"select_one\",\"field_name\":\"price_mode\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"true_or_false\",\"field_name\":\"amount_any\"},{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"true_or_false\",\"field_name\":\"selected_by_default\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"text\",\"field_name\":\"node_bom_core_version\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"master_detail\",\"field_name\":\"core_id\",\"target_display_name\":\"产品组合\"},{\"is_readonly\":true,\"is_required\":false,\"is_tiled\":true,\"render_type\":\"select_one\",\"field_name\":\"node_bom_core_type\"},{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"true_or_false\",\"field_name\":\"is_required\"},{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"true_or_false\",\"field_name\":\"price_editable\"},{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"true_or_false\",\"field_name\":\"amount_editable\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"number\",\"field_name\":\"max_amount\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"number\",\"field_name\":\"min_amount\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"number\",\"field_name\":\"increment\"},{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"true_or_false\",\"field_name\":\"enabled_status\"},{\"is_readonly\":false,\"full_line\":false,\"is_required\":false,\"render_type\":\"long_text\",\"field_name\":\"remark\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"object_reference\",\"field_name\":\"product_group_id\",\"target_display_name\":\"产品分组\"},{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"auto_number\",\"field_name\":\"name\"},{\"is_readonly\":false,\"is_required\":true,\"render_type\":\"employee\",\"field_name\":\"owner\"},{\"is_readonly\":false,\"is_required\":false,\"is_tiled\":true,\"render_type\":\"select_one\",\"field_name\":\"field_Q1v5m__c\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"currency\",\"field_name\":\"field_7gDe2__c\"},{\"is_readonly\":false,\"is_required\":false,\"render_type\":\"currency\",\"field_name\":\"field_74B23__c\"},{\"is_required\":false,\"field_name\":\"mc_exchange_rate\",\"render_type\":\"number\"},{\"is_required\":false,\"field_name\":\"owner_department\",\"render_type\":\"text\"},{\"is_required\":false,\"field_name\":\"lock_status\",\"render_type\":\"select_one\"},{\"is_required\":false,\"field_name\":\"product_life_status\",\"render_type\":\"quote\"},{\"is_required\":false,\"field_name\":\"share_rate\",\"render_type\":\"percentile\"},{\"is_required\":false,\"field_name\":\"data_own_department\",\"render_type\":\"department\"},{\"is_required\":false,\"field_name\":\"product_status\",\"render_type\":\"quote\"},{\"is_required\":false,\"field_name\":\"out_owner\",\"render_type\":\"employee\"},{\"is_required\":false,\"field_name\":\"is_package\",\"render_type\":\"true_or_false\"},{\"is_required\":false,\"field_name\":\"life_status\",\"render_type\":\"select_one\"},{\"is_required\":false,\"field_name\":\"mc_currency\",\"render_type\":\"select_one\"},{\"is_required\":false,\"field_name\":\"record_type\",\"render_type\":\"record_type\"}],\"api_name\":\"base_field_section__c\",\"tab_index\":\"ltr\",\"column\":2,\"header\":\"基本信息\",\"is_show\":true},{\"show_header\":true,\"form_fields\":[{\"is_readonly\":true,\"is_required\":false,\"render_type\":\"employee\",\"field_name\":\"created_by\"},{\"is_readonly\":true,\"is_required\":false,\"render_type\":\"employee\",\"field_name\":\"last_modified_by\"},{\"is_readonly\":true,\"is_required\":false,\"render_type\":\"date_time\",\"field_name\":\"create_time\"},{\"is_readonly\":true,\"is_required\":false,\"render_type\":\"date_time\",\"field_name\":\"last_modified_time\"}],\"api_name\":\"sysinfo_section__c\",\"tab_index\":\"ltr\",\"column\":2,\"header\":\"系统信息\",\"is_show\":true}],\"buttons\":[],\"related_list_name\":\"\",\"column\":2,\"is_hidden\":false,\"nameI18nKey\":\"paas.udobj.detail_info\",\"type\":\"form\",\"isSticky\":false,\"api_name\":\"form_component\",\"header\":\"详细信息\",\"_id\":\"form_component\",\"grayLimit\":1,\"order\":8}"
        Map map = JSON.parseObject(str, Map.class)
        FormComponent formComponent = new FormComponent(map)
        ILayout childLayout = new Layout()
        childLayout.setComponents(Lists.newArrayList(formComponent))
        result.setLayout(LayoutDocument.of(childLayout))
        result.setData(new ObjectDataDocument())
        when:
        tester.after(arg, result)
        then:
        1 == 1
    }

}
