package com.facishare.crm.sfa.predefine.service

import com.facishare.crm.sfa.RemoveUseless
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreServiceImpl
import com.facishare.crm.sfa.predefine.service.cpq.model.BomTreeModel
import com.facishare.crm.sfa.predefine.service.task.ProductStatusChangeTaskService
import com.facishare.paas.metadata.exception.MetadataServiceException
import com.facishare.paas.metadata.exception.ErrorCode
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil
import com.facishare.crm.sfa.utilities.util.ConcatenateSqlUtils
import com.facishare.crm.sfa.utilities.util.GrayUtil
import com.facishare.crm.sfa.utilities.util.ProductConstraintUtil
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.InfraServiceFacade
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.Lang
import com.facishare.paas.appframework.metadata.ProductCategoryService
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.action.IActionContext
import com.facishare.paas.metadata.api.search.IFilter
import com.facishare.paas.metadata.api.search.Wheres
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.impl.search.Where
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.LoggerFactory
import org.spockframework.runtime.Sputnik
import spock.lang.Shared
import com.facishare.crm.sfa.predefine.service.cpq.model.CheckBomModel
import com.facishare.crm.sfa.utilities.constant.BomConstants
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe


import static org.mockito.ArgumentMatchers.any

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([ProductConstraintUtil.class, ConcatenateSqlUtils.class, GrayUtil.class, I18N.class, BizConfigThreadLocalCacheService.class])
@SuppressStaticInitializationFor([
        "com.facishare.crm.sfa.utilities.util.ProductConstraintUtil",
        "com.facishare.crm.sfa.utilities.util.ConcatenateSqlUtils",
        "com.facishare.crm.sfa.utilities.util.GrayUtil",
        "com.facishare.paas.I18N",
        "com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService"
])
class BomCoreServiceImplTest extends RemoveUseless {

    @Shared
    private ServiceFacade serviceFacade;

    @Shared
    private User user;

    @Shared
    private BomCoreServiceImpl bomCoreServiceImplTester;
    @Shared
    private ProductCategoryService productCategoryService;
    @Shared
    private ObjectDataServiceImpl objectDataServiceImpl;
    @Shared
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;


    def setup() {
        PowerMockito.mockStatic(ConcatenateSqlUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(ProductConstraintUtil.class)
        PowerMockito.mockStatic(I18N.class)
        PowerMockito.mockStatic(BizConfigThreadLocalCacheService.class)
    }

    def setupSpec() {
        removeConfigFactory()
        Whitebox.setInternalState(I18N, "log", LoggerFactory.getLogger("test"))
        Whitebox.setInternalState(I18N, "I18N_RESOURCES", Collections.emptyList())
        Whitebox.setInternalState(I18N, "THREAD_LOCAL", new InheritableThreadLocal<>())
        bomCoreServiceImplTester = Spy(new BomCoreServiceImpl());
        user = Spy(new User("1", "1"))
        user.getUpstreamOwnerIdOrUserId() >> "1"
        productCategoryService = PowerMockito.mock(ProductCategoryService)
        serviceFacade = PowerMockito.mock(ServiceFacade)
        objectDataServiceImpl = PowerMockito.mock(ObjectDataServiceImpl)
        bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        Whitebox.setInternalState(bomCoreServiceImplTester, "productCategoryService", productCategoryService)
        Whitebox.setInternalState(bomCoreServiceImplTester, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(bomCoreServiceImplTester, "objectDataService", objectDataServiceImpl)
        Whitebox.setInternalState(bomCoreServiceImplTester, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
    }

    def "test node list"() {
        given:
        def queryResult = new QueryResult()
        queryResult.setData([new ObjectData("_id": "1", "bom_path": "1")])
        queryResult.setTotalNumber(1)
        PowerMockito.doNothing().when(productCategoryService, "handleCategoryFilters", any(), any(), any())
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        PowerMockito.when(serviceFacade.findObjectDataByIdsIgnoreFormula(any() as String, any() as List, any() as String)).thenReturn([new ObjectData("_id": "1", "product_id": "p1")])
        PowerMockito.when(objectDataServiceImpl.findBySql(any() as String, any() as String)).thenReturn([["parent_bom_id": "1", "child_count": 5]] as List<Map>)
        PowerMockito.when(ConcatenateSqlUtils.checkHasChildGroup(any(), any())).thenReturn("")
        PowerMockito.when(ProductConstraintUtil.getProductName(any(), any())).thenReturn(["p1": "zhangSan"])
        when:
        def result = bomCoreServiceImplTester.nodeList(user, searchTemplateQuery, isQueryRoot)
        then:
        result.getTotal() == ret
        where:
        searchTemplateQuery | ret | isQueryRoot
        buildSearchQuery()  | 1   | true
        buildSearchQuery()  | 1   | false
    }

    def "test haveConfigBOMPrivilege"() {
        given:
        def bomCoreServiceImplTester = new BomCoreServiceImpl()
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(bomCoreServiceImplTester, "serviceFacade", serviceFacade)
        and:
        Map<String, Boolean> actionCode2Status = Maps.newHashMap()
        actionCode2Status.put("Add", ret)
        PowerMockito.when(serviceFacade.funPrivilegeCheck(any() as User, any() as String, any() as List)).thenReturn(actionCode2Status)
        when:
        def result = bomCoreServiceImplTester.haveConfigBOMPrivilege(new User("1", "1"))
        then:
        result == ret
        where:
        searchTemplateQuery | ret
        buildSearchQuery()  | false
        buildSearchQuery()  | true
    }

    def "test bulkDeleteBomRootNode"() {
        given:
        def bomCoreServiceImplTester = new BomCoreServiceImpl()
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(bomCoreServiceImplTester, "serviceFacade", serviceFacade)
        and:
        def queryResult = new QueryResult()
        queryResult.setData([new ObjectData("_id": "1")])
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        PowerMockito.when(serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(any() as List, any() as User)).thenReturn("")
        when:
        bomCoreServiceImplTester.bulkDeleteBomRootNode(new User("1", "1"), Lists.newArrayList("1"))
        then:
        1 == 1
    }

    def "test bulkDeleteBomCore"() {
        given:
        def bomCoreServiceImplTester = new BomCoreServiceImpl()
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(bomCoreServiceImplTester, "serviceFacade", serviceFacade)
        and:
        def queryResult = new QueryResult()
        queryResult.setData([new ObjectData("_id": "1")])
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        PowerMockito.when(serviceFacade.findBySearchQueryIgnoreAll(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        PowerMockito.when(serviceFacade.bulkDeleteDirect(any() as List, any() as User)).thenReturn([])
        when:
        bomCoreServiceImplTester.bulkDeleteBomCore(new User("1", "1"), Lists.newArrayList("1"))
        then:
        1 == 1
    }

    def "test findBomByRootProductId"() {
        given:
        def bomCoreServiceImplTester = new BomCoreServiceImpl()
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(bomCoreServiceImplTester, "serviceFacade", serviceFacade)
        and:
        def queryResult = new QueryResult()
        queryResult.setData([new ObjectData("_id": "1")])
        PowerMockito.when(serviceFacade.findBySearchQueryIgnoreAll(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        when:
        bomCoreServiceImplTester.findBomByRootProductId(new User("1", "1"), new SearchTemplateQuery(), Lists.newArrayList("1"))
        then:
        1 == 1
    }

    def "test findProductGroupByIds"() {
        given:
        def bomCoreServiceImplTester = new BomCoreServiceImpl()
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(bomCoreServiceImplTester, "serviceFacade", serviceFacade)
        and:
        def queryResult = new QueryResult()
        queryResult.setData(dataList)
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        when:
        def result = bomCoreServiceImplTester.findProductGroupByIds(new User("1", "1"), Lists.newArrayList("1"))
        then:
        result.size() == cut
        where:
        dataList                     | cut
        []                           | 0
        [new ObjectData("_id": "1")] | 1
    }

    def "test getPriceBookProductList"() {
        given:
        def bomCoreServiceImplTester = new BomCoreServiceImpl()
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(bomCoreServiceImplTester, "serviceFacade", serviceFacade)
        and:
        def queryResult = new QueryResult()
        queryResult.setData(dataList)
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as IActionContext, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        when:
        def result = bomCoreServiceImplTester.getPriceBookProductList(new User("1", "1"), "1", Lists.newArrayList("1"))
        then:
        result.size() == cut
        where:
        dataList                     | cut
        []                           | 0
        [new ObjectData("_id": "1")] | 1
    }

    def "test findBomListByCoreId"() {
        given:
        def bomCoreServiceImplTester = new BomCoreServiceImpl()
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(bomCoreServiceImplTester, "serviceFacade", serviceFacade)
        and:
        def queryResult = new QueryResult()
        queryResult.setData(dataList)
        PowerMockito.when(serviceFacade.findBySearchQueryIgnoreAll(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        when:
        def result = bomCoreServiceImplTester.findBomListByCoreId(new User("1", "1"), Lists.newArrayList("1"))
        then:
        result.size() == cut
        where:
        dataList                     | cut
        []                           | 0
        [new ObjectData("_id": "1")] | 1
    }

    def "test bulkDeleteBomByProductIds"() {
        given:
        def bomCoreServiceImplTester = new BomCoreServiceImpl()
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(bomCoreServiceImplTester, "serviceFacade", serviceFacade)
        def productStatusChangeTaskService = PowerMockito.mock(ProductStatusChangeTaskService)
        Whitebox.setInternalState(bomCoreServiceImplTester, "productStatusChangeTaskService", productStatusChangeTaskService)
        and:
        def queryResult = new QueryResult()
        queryResult.setData(dataList)
        PowerMockito.when(serviceFacade.findBySearchQueryIgnoreAll(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        when:
        bomCoreServiceImplTester.bulkDeleteBomByProductIds(new User("1", "1"), Lists.newArrayList("1"))
        then:
        1 == 1
        where:
        dataList                                                                                                                                                                                                                                                                                               | cut
        []                                                                                                                                                                                                                                                                                                     | 0
        [new ObjectData("_id": "r1", "root_id": "r1", "bom_path": "r1"), new ObjectData("_id": "2", "root_id": "r1", "bom_path": "r1.2"), new ObjectData("_id": "3", "root_id": "r1", "bom_path": "r1.2.3")]                                                                                                   | 1
        [new ObjectData("_id": "r1", "root_id": "r1", "bom_path": "r1"), new ObjectData("_id": "2", "root_id": "r1", "bom_path": "r1.2", "product_group_id": "g1", "parent_bom_id": "r1"), new ObjectData("_id": "3", "root_id": "r1", "bom_path": "r1.2.3", "product_group_id": "g1", "parent_bom_id": "r1")] | 1
    }

    def "test checkExistChildNode"() {
        given:
        def bomCoreServiceImplTester = new BomCoreServiceImpl()
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(bomCoreServiceImplTester, "serviceFacade", serviceFacade)
        def productStatusChangeTaskService = PowerMockito.mock(ProductStatusChangeTaskService)
        Whitebox.setInternalState(bomCoreServiceImplTester, "productStatusChangeTaskService", productStatusChangeTaskService)
        and:
        def queryResult = new QueryResult()
        queryResult.setData(dataList)
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        PowerMockito.when(GrayUtil.bomMasterSlaveMode(any())).thenReturn(cpq)
        when:
        try {
            bomCoreServiceImplTester.checkExistChildNode(new User("1", "1"), id, flag)
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then:
        1 == 1
        where:
        id  | flag  | dataList                     | cpq
        ""  | false | []                           | true
        "1" | false | []                           | true
        "1" | true  | []                           | true
        "1" | false | [new ObjectData("_id": "1")] | true
        "1" | true  | [new ObjectData("_id": "1")] | true
        "1" | false | [new ObjectData("_id": "1")] | false
        "1" | true  | [new ObjectData("_id": "1")] | false
    }

    def "test findBomByBomIds"() {
        given:
        def bomCoreServiceImplTester = new BomCoreServiceImpl()
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(bomCoreServiceImplTester, "serviceFacade", serviceFacade)
        and:
        def queryResult = new QueryResult()
        queryResult.setData(dataList)
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        when:
        def result = bomCoreServiceImplTester.findBomByBomIds(new User("1", "1"), Lists.newArrayList("1"))
        then:
        result.size() == cut
        where:
        dataList                     | cut
        []                           | 0
        [new ObjectData("_id": "1")] | 1
    }

    def "test deleteProductGroupWithParentBomIdIsNull"() {
        given:
        def bomCoreServiceImplTester = new BomCoreServiceImpl()
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(bomCoreServiceImplTester, "serviceFacade", serviceFacade)
        and:
        def queryResult = new QueryResult()
        queryResult.setData([new ObjectData("_id": "1")])
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        PowerMockito.when(serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(any() as List, any() as User)).thenReturn("")
        when:
        bomCoreServiceImplTester.deleteProductGroupWithParentBomIdIsNull(new User("1", "1"), Lists.newArrayList("1"))
        then:
        1 == 1
    }

    def "queryBomTree method by param error"() {
        given:
        def bomTreeArg = new BomTreeModel.BomTreeArg();
        User user = User.builder().tenantId("89242").userId("1000").build();
        BomCoreServiceImpl bomCoreService = new BomCoreServiceImpl();
        Whitebox.setInternalState(bomCoreService, "serviceFacade", PowerMockito.mock(ServiceFacade));
        Whitebox.setInternalState(bomCoreService, "objectDataService", PowerMockito.mock(ObjectDataServiceImpl));
        Whitebox.setInternalState(bomCoreService, "infraServiceFacade", PowerMockito.mock(InfraServiceFacade));
        when:
        PowerMockito.mockStatic(I18N);
        PowerMockito.when(I18N.text(any())).thenReturn("缺少参数");
        bomCoreService.queryBomTree(user, bomTreeArg);
        then:
        ValidateException exception = thrown(ValidateException);
        exception != null && exception.getMessage() == "缺少参数";
    }

    def "test findChildCount by exception"() {
        when:
        PowerMockito.when(ConcatenateSqlUtils.checkHasChildGroup(any(), any())).thenReturn("")
        PowerMockito.when(objectDataServiceImpl.findBySql(any() as String, any() as String)).thenThrow(new MetadataServiceException(ErrorCode.ES_NOT_SUPPORT, "错误"));
        bomCoreServiceImplTester.findChildCount(new User("1", "1"), Lists.newArrayList("1"))
        then:
        thrown(ValidateException.class);
    }

//    def "test findChildGroupCount by exception"() {
//        when:
//        PowerMockito.when(ConcatenateSqlUtils.checkHasChildGroup(any(), any())).thenReturn("")
//        PowerMockito.when(objectDataServiceImpl.findBySql(any() as String, any() as String)).thenThrow(new MetadataServiceException(ErrorCode.ES_NOT_SUPPORT, "错误"));
//        bomCoreServiceImplTester.findChildGroupCount(new User("1", "1"), Lists.newArrayList("1"))
//        then:
//        thrown(ValidateException.class);
//    }

    def "test existConstraint"() {
        given:
        User user = new User("1", "1")
        CheckBomModel.CheckArgs arg = new CheckBomModel.CheckArgs()
        arg.setCoreId(coreId)

        PowerMockito.when(bizConfigThreadLocalCacheService.isCPQEnabled(any())).thenReturn(isCPQEnabled)

        if (isCPQEnabled) {
            IObjectDescribe objectDescribe = PowerMockito.mock(IObjectDescribe.class)
            PowerMockito.when(serviceFacade.findObject(any(), any())).thenReturn(objectDescribe)
            def queryResult = new QueryResult()
            queryResult.setData(resultData)
            PowerMockito.when(serviceFacade.findBySearchQueryIgnoreAll(any(), any(), any())).thenReturn(queryResult)
        }

        when:
        def result = bomCoreServiceImplTester.existConstraint(user, arg)

        then:
        result == expected

        where:
        isCPQEnabled   | findObjectThrowsException   |  coreId  | resultData                     | expected
        false          | false                       | null     | []                             | false
        true           | false                       | "123"    | [new ObjectData(["_id": "1"])]   | true
    }

    def "test queryBomTree param error"() {
        given:
        def bomTreeArg = new BomTreeModel.BomTreeArg()
        bomTreeArg.setRootBomCoreId(null)
        User user = User.builder().tenantId("89242").userId("1000").build()

        and:
        PowerMockito.when(I18N.text(any())).thenReturn("缺少参数")

        when:
        bomCoreServiceImplTester.queryBomTree(user, bomTreeArg)

        then:
        ValidateException exception = thrown(ValidateException)
        exception.getMessage() == "缺少参数"
    }

    def "test queryBomTree empty result"() {
        given:
        def bomTreeArg = new BomTreeModel.BomTreeArg()
        bomTreeArg.setRootBomCoreId("core1")
        User user = User.builder().tenantId("89242").userId("1000").build()

        when:
        def queryResult = new QueryResult()
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as IActionContext, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)

        def result = bomCoreServiceImplTester.queryBomTree(user, bomTreeArg)

        then:
        result != null
        result.getDataMapList() == null || result.getDataMapList().isEmpty()
    }

    def "test queryBomTree no object describe"() {
        given:
        def bomTreeArg = new BomTreeModel.BomTreeArg()
        bomTreeArg.setRootBomCoreId("core1")
        User user = User.builder().tenantId("89242").userId("1000").build()

        def queryResult = new QueryResult()
        queryResult.setData(Lists.newArrayList(new ObjectData()))
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as IActionContext, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        // Mock no object describe
        PowerMockito.when(serviceFacade.findObject(any(), any())).thenReturn(null)

        when:
        def result = bomCoreServiceImplTester.queryBomTree(user, bomTreeArg)

        then:
        result != null
        result.getDataMapList() == null || result.getDataMapList().isEmpty()
    }

    def "test queryBomTree success with no root"() {
        given:
        def bomTreeArg = new BomTreeModel.BomTreeArg()
        bomTreeArg.setRootBomCoreId("core1")
        User user = User.builder().tenantId("89242").userId("1000").build()

        def queryResult = new QueryResult()
        queryResult.setData(Lists.newArrayList(new ObjectData("parent_bom_id":"111")))
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as IActionContext, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)

        IObjectDescribe objectDescribe = PowerMockito.mock(IObjectDescribe.class)
        IFieldDescribe fieldDescribe = PowerMockito.mock(IFieldDescribe.class)
        PowerMockito.when(fieldDescribe.getApiName()).thenReturn(BomConstants.FIELD_AMOUNT)
        PowerMockito.when(fieldDescribe.get("decimal_places", Integer.class, 0)).thenReturn(2)
        PowerMockito.when(objectDescribe.getFieldDescribes()).thenReturn([fieldDescribe])
        PowerMockito.when(serviceFacade.findObject(any(), any())).thenReturn(objectDescribe)

        when:
        def result = bomCoreServiceImplTester.queryBomTree(user, bomTreeArg)

        then:
        result != null
        result.getDataMapList() == null || result.getDataMapList().isEmpty()
    }

    def "test queryBomTree success with root and no reusable bom"() {
        given:
        def bomTreeArg = new BomTreeModel.BomTreeArg()
        bomTreeArg.setRootBomCoreId("core1")
        bomTreeArg.setIncludeAll(includeAll)
        bomTreeArg.setNeedSubBom(needSubBom)
        User user = User.builder().tenantId("89242").userId("1000").build()
        String rootId = "rootId"

        ObjectData rootBom = new ObjectData()
        rootBom.set("_id", "bom1")
        rootBom.set(BomConstants.FIELD_BOM_PATH, "bom1")
        rootBom.set(BomConstants.FIELD_ROOT_ID, rootId)

        ObjectData childBom = new ObjectData()
        childBom.set("_id", "bom2")
        childBom.set(BomConstants.FIELD_PARENT_BOM_ID, "bom1")
        childBom.set(BomConstants.FIELD_BOM_PATH, "bom1.bom2")
        childBom.set(BomConstants.FIELD_AMOUNT, new BigDecimal("1.0"))
        if (hasRelatedCore) {
            childBom.set(BomConstants.FIELD_RELATED_CORE_ID, "core2")
        }

        List<IObjectData> bomList = [rootBom, childBom]

        def queryResult = new QueryResult()
        queryResult.setData(bomList)
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as IActionContext, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)

        IObjectDescribe objectDescribe = PowerMockito.mock(IObjectDescribe.class)
        IFieldDescribe fieldDescribe = PowerMockito.mock(IFieldDescribe.class)
        PowerMockito.when(fieldDescribe.getApiName()).thenReturn(BomConstants.FIELD_AMOUNT)
        PowerMockito.when(fieldDescribe.get("decimal_places", Integer.class, 0)).thenReturn(2)
        PowerMockito.when(objectDescribe.getFieldDescribes()).thenReturn([fieldDescribe])
        PowerMockito.when(serviceFacade.findObject(any(), any())).thenReturn(objectDescribe)

        // Mock random id generation
        PowerMockito.when(serviceFacade.generateId()).thenReturn("randomId1", "randomId2", "randomId3")

        // Mock for reusable BOM if needed
        if (hasRelatedCore && needSubBom) {
            ObjectData relatedRootBom = new ObjectData()
            relatedRootBom.set("_id", "rbom1")
            relatedRootBom.set(BomConstants.FIELD_BOM_PATH, "rbom1")

            List<IObjectData> relatedBomList = [relatedRootBom]
            PowerMockito.doReturn(relatedBomList).when(bomCoreServiceImplTester, "getBomListByCoreId", "core2", user)
        }

        // Mock for product group nodes filtering if not including all
        if (!includeAll) {
            childBom.set(BomConstants.FIELD_PRODUCT_GROUP_ID, "pg1")
            if (productGroupFound) {
                PowerMockito.when(serviceFacade.findObjectDataByIdsExcludeInvalidIgnoreAll(any(), any(), any())).thenReturn([new ObjectData("_id": "pg1")])
            } else {
                PowerMockito.when(serviceFacade.findObjectDataByIdsExcludeInvalidIgnoreAll(any(), any(), any())).thenReturn([])
            }
        }

        // Mock for bomAmount if provided
        if (hasBomAmount) {
            bomTreeArg.setBomAmount(new BigDecimal("2.0"))
        }

        when:
        def result = bomCoreServiceImplTester.queryBomTree(user, bomTreeArg)

        then:
        result != null
        result.getDataMapList() != null
        !result.getDataMapList().isEmpty()
        result.getDataMapList().size() == 1
        def dataList = result.getDataMapList().get(0).getDataList()
        dataList != null
        dataList.size() == expectedSize

        where:
        includeAll   |  needSubBom  | hasRelatedCore   |  productGroupFound  |  hasBomAmount  | expectedSize
        true         | false        | false            | true                | false          | 2
        true         | true         | false            | true                | false          | 2
        true         | false        | false            | true                | true           | 2
        false        | false        | false            | true                | false          | 2
        false        | false        | false            | false               | false          | 1
    }

//    def "test queryBomTree throw exception when node count exceeds limit"() {
//        given:
//        def bomTreeArg = new BomTreeModel.BomTreeArg()
//        bomTreeArg.setRootBomCoreId("core1")
//        bomTreeArg.setNeedSubBom(true)
//        User user = User.builder().tenantId("89242").userId("1000").build()
//
//        and:
//        // Mock random id generation
//        PowerMockito.when(serviceFacade.generateId()).thenReturn("randomId1", "randomId2", "randomId3")
//
//        // Create root bom
//        ObjectData rootBom = new ObjectData()
//        rootBom.set("_id", "bom1")
//        rootBom.set(BomConstants.FIELD_BOM_PATH, "bom1")
//
//        // Create child bom with related core id
//        ObjectData childBom = new ObjectData()
//        childBom.set("_id", "bom2")
//        childBom.set(BomConstants.FIELD_PARENT_BOM_ID, "bom1")
//        childBom.set(BomConstants.FIELD_BOM_PATH, "bom1.bom2")
//        childBom.set(BomConstants.FIELD_RELATED_CORE_ID, "core2")
//        childBom.set(BomConstants.FIELD_PROD_PKG_KEY, "pkg1")
//        childBom.set(BomConstants.FIELD_ROOT_PROD_PKG_KEY, "rootpkg1")
//        childBom.set(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, "bom1.bom2")
//        childBom.set(BomConstants.FIELD_AMOUNT, new BigDecimal("1.0"))
//        childBom.set(BomConstants.FIELD_CORE_ID, "core1")
//
//        List<IObjectData> bomList = [rootBom, childBom]
//
//        def queryResult = new QueryResult()
//        queryResult.setData(bomList)
//        PowerMockito.when(serviceFacade.findBySearchQuery(any() as IActionContext, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
//
//
//        // Mock object describe
//        IObjectDescribe objectDescribe = PowerMockito.mock(IObjectDescribe.class)
//        IFieldDescribe fieldDescribe = PowerMockito.mock(IFieldDescribe.class)
//        PowerMockito.when(fieldDescribe.getApiName()).thenReturn(BomConstants.FIELD_AMOUNT)
//        PowerMockito.when(fieldDescribe.get("decimal_places", Integer.class, 0)).thenReturn(2)
//        PowerMockito.when(objectDescribe.getFieldDescribes()).thenReturn([fieldDescribe])
//        PowerMockito.when(serviceFacade.findObject(any(), any())).thenReturn(objectDescribe)
//
//        // Mock for recursive bom that would cause node count to exceed limit
//        PowerMockito.when(I18N.text(any(), any())).thenReturn("节点数不能超过2000")
//
//        // Force recursive call to throw exception for too many nodes
//        PowerMockito.doAnswer { invocation ->
//            throw new ValidateException("节点数不能超过2000")
//        }.when(bomCoreServiceImplTester).findReusableBomListByCoreId(any(), any(), any(), any(), any())
//
//        when:
//        bomCoreServiceImplTester.queryBomTree(user, bomTreeArg)
//
//        then:
//        ValidateException exception = thrown(ValidateException)
//        exception.getMessage() == "节点数不能超过2000"
//    }

    def buildSearchQuery() {
        def query = buildSearchQueryNoFilter()
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "is_deleted", true);
        query.setFilters(filters)

        List<IFilter> filters2 = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters2, "biz_status", "normal");
        query.setFilters(filters2)
        Wheres wheres1 = new Wheres();
        wheres1.setConnector(Where.CONN.OR.toString());
        wheres1.setFilters(filters2);
        query.setWheres([wheres1]);
        query
    }

    def buildSearchQueryNoFilter() {
        SearchTemplateQuery query = new SearchTemplateQuery()
        query.setNeedReturnCountNum(false)
        query.setPermissionType(0)
        query.setNeedReturnQuote(false)
        query.setOffset(0)
        query.setLimit(1000)
        query
    }

    RequestContext initRequestContext() {
        RequestContext.RequestContextBuilder requestContextBuilder = RequestContext.builder()
        requestContextBuilder.tenantId("79263")
        requestContextBuilder.user(new User("79263", "1000"))
        requestContextBuilder.contentType(RequestContext.ContentType.FULL_JSON)
        requestContextBuilder.requestSource(RequestContext.RequestSource.CEP)
        requestContextBuilder.lang(Lang.zh_CN)
        def requestContext = requestContextBuilder.build()
        return requestContext
    }
}
