package com.facishare.crm.sfa.predefine.service

import com.alibaba.fastjson.JSON
import com.facishare.crm.describebuilder.SelectOneFieldDescribeBuilder
import com.facishare.crm.openapi.Utils
import com.facishare.crm.sfa.RemoveUseless
import com.facishare.crm.sfa.cache.RedisDataAccessor
import com.facishare.crm.sfa.predefine.service.AvailableRangeCoreService
import com.facishare.crm.sfa.predefine.service.BomPriceServiceImpl
import com.facishare.crm.sfa.predefine.service.attribute.AttributeCoreService
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.predefine.service.cpq.BomService
import com.facishare.crm.sfa.predefine.service.cpq.model.CheckProductBomModel
import com.facishare.crm.sfa.predefine.service.cpq.model.QueryBomPriceModel
import com.facishare.crm.sfa.utilities.util.GrayUtil
import com.facishare.crm.sfa.utilities.util.Price.RealPriceModel
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.service.CalculateService
import com.facishare.paas.appframework.core.predef.service.dto.calculate.BatchCalculate
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam
import com.facishare.paas.metadata.api.ISelectOption
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.SelectOption
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.apache.commons.lang3.StringUtils
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.spy

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([I18nServiceImpl.class, UdobjGrayConfig, ServiceContext.class, I18N.class, GrayUtil.class, User.class])
@SuppressStaticInitializationFor([
        "com.fxiaoke.i18n.client.impl.I18nServiceImpl",
        "com.facishare.paas.appframework.core.util.UdobjGrayConfig",
        "com.facishare.crm.sfa.utilities.util.GrayUtil", "com.facishare.paas.appframework.core.util.UdobjGrayConfig"])
class BomPriceServiceImplTest extends RemoveUseless {
    @Shared
    private BomPriceServiceImpl tester
    @Shared
    private User user

    def setupSpec() {
        PowerMockito.mockStatic(I18N.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        tester = new BomPriceServiceImpl()
        user = spy(new User("71568", "-10000"))
    }

    def "test queryBomPrice"() {
        given:
        def describe = new ObjectDescribe();
        describe.addFieldDescribeList(getField())
        def arg = new QueryBomPriceModel.Param()
        def queryResult = new QueryResult();
        queryResult.setData(dataList)
        arg.setAccountId("account1")
        arg.setRootBomId("r1")
        arg.setApiName("SalesOrderObj")
        arg.setDetailApiName("SalesOrderProductObj")
        arg.setCoreId("c1")
        arg.setRootAmount("1")
        arg.setNoCalPrice(noCalPrice)
        arg.setBomList(bomList)
        arg.setSwitchMasterPriceBook(switchMasterPriceBook)
        PowerMockito.when(GrayUtil.bomMasterSlaveMode(anyString())).thenReturn(bomMasterSlave)
        def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        Whitebox.setInternalState(tester, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(false).when(bizConfigThreadLocalCacheService, "bomPriceCalculationConfiguration", anyString())
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQueryIgnoreAll", any(), any(), any())
        PowerMockito.doReturn("1").when(serviceFacade, "generateId")
        PowerMockito.doReturn(describe).when(serviceFacade, "findObject", any(), any())
        PowerMockito.doReturn([new ObjectData("_id": "p1", "product_status": "1"), new ObjectData("_id": "p2", "product_status": "1"), new ObjectData("_id": "p3", "product_status": "1"), new ObjectData("_id": "p4", "product_status": "1")]).when(serviceFacade, "findObjectDataByIdsIgnoreFormula", any(), any(), any())
        def attributeCoreService = PowerMockito.mock(AttributeCoreService)
        Whitebox.setInternalState(tester, "attributeCoreService", attributeCoreService)
        PowerMockito.doNothing().when(attributeCoreService, "attachAttributeData", any(), any(), any())
        def infraServiceFacade = PowerMockito.mock(InfraServiceFacade)
        Whitebox.setInternalState(tester, "infraServiceFacade", infraServiceFacade)
        PowerMockito.doReturn(domain()).when(infraServiceFacade, "findPluginParam", any(), any(), any())
        def redisDataAccessor = PowerMockito.mock(RedisDataAccessor)
        Whitebox.setInternalState(tester, "redisDataAccessor", redisDataAccessor)
        PowerMockito.doNothing().when(redisDataAccessor, "set", "bom1", "bom1", 1)
        RealPriceModel.Result realPrice = new RealPriceModel.Result();
        realPrice.setNewRst(ObjectDataDocument.ofList(getPriceList()))
        def availableRangeCoreService = PowerMockito.mock(AvailableRangeCoreService)
        Whitebox.setInternalState(tester, "availableRangeCoreService", availableRangeCoreService)
        PowerMockito.doReturn(realPrice).when(availableRangeCoreService, "getRealPrice", any(), any())
        def bomService = PowerMockito.mock(BomService)
        Whitebox.setInternalState(tester, "bomService", bomService)
        PowerMockito.doReturn(dataList).when(bomService, "getBomListByCoreId", any(), any(), eq(false), eq(false))
        def result = Lists.newArrayList()
        when:
        try {
            result = tester.queryBomPrice(getServiceContext(user,"",""), arg)
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then:
        1 == 1
        where:
        switchMasterPriceBook | noCalPrice | bomMasterSlave | dataList                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | bomList | re
        false                 | false      | false          | []                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | getBomList("[{\"newBomPath\":\"662e0f46d0b54c000804aed9.662e0f46d0b54c000804aedb\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"relatedCoreId\":null,\"bomId\":\"662e0f46d0b54c000804aedb\",\"productId\":\"65dda40a3f319f00070bc11c\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"nodeType\":\"standard\"},{\"coreId\":\"662e0f46d0b54c000804aede\",\"newBomPath\":\"662e0f46d0b54c000804aed9\",\"nodeBomCoreType\":\"configure\",\"nodeBomCoreVersion\":\"b20240428-v001301\",\"bomId\":\"r1\",\"productId\":\"65ddd3c83f319f000728ee73\",\"priceBookId\":\"6594c79bc715080007e4b05b\",\"price\":2,\"amount\":\"1\",\"updateFlag\":false,\"orderField\":1,\"nodeType\":null,\"rootProdKey\":\"\",\"prodKey\":\"1714299421984252\"}]") | true
        false                 | false      | true           | [new ObjectData("_id": "662e0f46d0b54c000804aedb")]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | getBomList("[{\"newBomPath\":\"662e0f46d0b54c000804aed9.662e0f46d0b54c000804aedb\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"relatedCoreId\":null,\"bomId\":\"662e0f46d0b54c000804aedb\",\"productId\":\"65dda40a3f319f00070bc11c\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"nodeType\":\"standard\",\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299421984252\",\"prodKey\":\"1714299374267245\"},{\"coreId\":\"662e0f46d0b54c000804aede\",\"newBomPath\":\"662e0f46d0b54c000804aed9\",\"nodeBomCoreType\":\"configure\",\"nodeBomCoreVersion\":\"b20240428-v001301\",\"bomId\":\"r1\",\"productId\":\"65ddd3c83f319f000728ee73\",\"priceBookId\":\"6594c79bc715080007e4b05b\",\"price\":2,\"amount\":\"1\",\"updateFlag\":false,\"orderField\":1,\"nodeType\":null,\"rootProdKey\":\"1714299421984252\",\"prodKey\":\"1714299421984252\"}]") | true
        false                 | false      | true           | [new ObjectData("_id": "662e0f46d0b54c000804aedb2", "enabled_status": true, "parent_bom_id": "662e0f46d0b54c000804aedb1", "selected_by_default": true), new ObjectData("_id": "662e0f46d0b54c000804aedb1", "enabled_status": true, "parent_bom_id": "r1", "selected_by_default": true), new ObjectData("_id": "r1", "enabled_status": true), new ObjectData("_id": "662e0f46d0b54c000804aedb", "enabled_status": true, "parent_bom_id": "r1")]                                                                                                                                                                                                                                                                                                                                                                                                  | getBomList("[{\"nodeType\":\"standard\",\"newBomPath\":\"662e0f46d0b54c000804aed9.662e0f46d0b54c000804aedb\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"relatedCoreId\":\"rcid1\",\"bomId\":\"662e0f46d0b54c000804aedb\",\"productId\":\"65dda40a3f319f00070bc11c\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299421984252\",\"prodKey\":\"1714299374267245\"},{\"coreId\":\"662e0f46d0b54c000804aede\",\"newBomPath\":\"662e0f46d0b54c000804aed9\",\"nodeBomCoreType\":\"configure\",\"nodeBomCoreVersion\":\"b20240428-v001301\",\"bomId\":\"r1\",\"productId\":\"65ddd3c83f319f000728ee73\",\"priceBookId\":\"6594c79bc715080007e4b05b\",\"price\":2,\"amount\":\"1\",\"updateFlag\":false,\"orderField\":1,\"rootProdKey\":\"1714299421984252\",\"prodKey\":\"1714299421984252\"},{\"nodeType\":\"standard\",\"newBomPath\":\"662e0f46d0b54c000804aed9.662e0f46d0b54c000804aedb1\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"relatedCoreId\":null,\"bomId\":\"662e0f46d0b54c000804aedb1\",\"productId\":\"65dda40a3f319f00070bc11c1\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299421984252\",\"prodKey\":\"1714299374267250\"}]") | true
        false                 | true       | true           | [new ObjectData("_id": "662e0f46d0b54c000804aedb2", "enabled_status": true, "parent_bom_id": "662e0f46d0b54c000804aedb1", "selected_by_default": true), new ObjectData("_id": "662e0f46d0b54c000804aedb1", "bom_path": "r1.662e0f46d0b54c000804aedb1", "enabled_status": true, "parent_bom_id": "r1", "selected_by_default": true, "share_rate": "10"), new ObjectData("_id": "r1", "bom_path": "r1", "enabled_status": true), new ObjectData("_id": "662e0f46d0b54c000804aedb", "bom_path": "r1.662e0f46d0b54c000804aedb", "enabled_status": true, "parent_bom_id": "r1")]                                                                                                                                                                                                                                                                     | getBomList("[{\"nodeType\":\"standard\",\"newBomPath\":\"r1.662e0f46d0b54c000804aedb\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"relatedCoreId\":\"rcid1\",\"bomId\":\"662e0f46d0b54c000804aedb\",\"productId\":\"65dda40a3f319f00070bc11c\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299421984252\",\"prodKey\":\"1714299374267245\"},{\"coreId\":\"662e0f46d0b54c000804aede\",\"newBomPath\":\"r1\",\"nodeBomCoreType\":\"configure\",\"nodeBomCoreVersion\":\"b20240428-v001301\",\"bomId\":\"r1\",\"productId\":\"65ddd3c83f319f000728ee73\",\"priceBookId\":\"6594c79bc715080007e4b05b\",\"price\":2,\"amount\":\"1\",\"updateFlag\":false,\"orderField\":1,\"rootProdKey\":\"1714299421984252\",\"prodKey\":\"1714299421984252\"},{\"nodeType\":\"standard\",\"newBomPath\":\"r1.662e0f46d0b54c000804aedb1\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"relatedCoreId\":null,\"bomId\":\"662e0f46d0b54c000804aedb1\",\"productId\":\"65dda40a3f319f00070bc11c1\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299421984252\",\"prodKey\":\"1714299374267250\"}]") | false
        false                 | false      | false          | [new ObjectData("_id": "662e0f46d0b54c000804aedb2", "enabled_status": true, "product_id": "p1", "price_mode": 2, "parent_bom_id": "662e0f46d0b54c000804aedb1", "selected_by_default": true), new ObjectData("_id": "662e0f46d0b54c000804aedb1", "product_id": "p2", "bom_path": "r1.662e0f46d0b54c000804aedb1", "price_mode": 2, "enabled_status": true, "parent_bom_id": "r1", "selected_by_default": true, "share_rate": "10"), new ObjectData("_id": "r1", "product_id": "p4", "bom_path": "r1", "price_mode": 2, "enabled_status": true), new ObjectData("_id": "662e0f46d0b54c000804aedb", "product_id": "p3", "price_mode": 2, "bom_path": "r1.662e0f46d0b54c000804aedb", "enabled_status": true, "parent_bom_id": "r1")]                                                                                             | getBomList("[{\"nodeType\":\"standard\",\"newBomPath\":\"r1.662e0f46d0b54c000804aedb\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"bomId\":\"662e0f46d0b54c000804aedb\",\"productId\":\"65dda40a3f319f00070bc11c\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299421984252\",\"prodKey\":\"1714299374267245\"},{\"coreId\":\"662e0f46d0b54c000804aede\",\"newBomPath\":\"r1\",\"nodeBomCoreType\":\"configure\",\"nodeBomCoreVersion\":\"b20240428-v001301\",\"bomId\":\"r1\",\"productId\":\"65ddd3c83f319f000728ee73\",\"priceBookId\":\"6594c79bc715080007e4b05b\",\"price\":2,\"amount\":\"1\",\"updateFlag\":false,\"orderField\":1,\"rootProdKey\":\"1714299421984252\",\"prodKey\":\"1714299421984252\"},{\"nodeType\":\"standard\",\"newBomPath\":\"r1.662e0f46d0b54c000804aedb1\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"relatedCoreId\":null,\"bomId\":\"662e0f46d0b54c000804aedb1\",\"productId\":\"65dda40a3f319f00070bc11c1\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299421984252\",\"prodKey\":\"1714299374267250\"}]") | true
        true                  | false      | false          | [new ObjectData("_id": "662e0f46d0b54c000804aedb2", "enabled_status": true, "product_id": "p1", "price_mode": 2, "parent_bom_id": "662e0f46d0b54c000804aedb1", "bom_path": "r1.662e0f46d0b54c000804aedb1.662e0f46d0b54c000804aedb2", "selected_by_default": true), new ObjectData("_id": "662e0f46d0b54c000804aedb1", "product_id": "p2", "bom_path": "r1.662e0f46d0b54c000804aedb1", "price_mode": 2, "enabled_status": true, "parent_bom_id": "662e0f46d0b54c000804aedb", "selected_by_default": true, "share_rate": "10"), new ObjectData("_id": "r1", "product_id": "p4", "bom_path": "r1", "price_mode": 2, "enabled_status": true), new ObjectData("_id": "662e0f46d0b54c000804aedb", "product_id": "p3", "price_mode": 2, "bom_path": "r1.662e0f46d0b54c000804aedb", "enabled_status": true, "parent_bom_id": "r1")]                     | getBomList("[{\"nodeType\":\"temp\",\"newBomPath\":\"r1.662e0f46d0b54c000804aedb\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"bomId\":\"662e0f46d0b54c000804aedb\",\"productId\":\"65dda40a3f319f00070bc11c\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299421984252\",\"prodKey\":\"1714299374267245\"},{\"coreId\":\"662e0f46d0b54c000804aede\",\"newBomPath\":\"r1\",\"nodeBomCoreType\":\"configure\",\"nodeBomCoreVersion\":\"b20240428-v001301\",\"bomId\":\"r1\",\"productId\":\"65ddd3c83f319f000728ee73\",\"priceBookId\":\"6594c79bc715080007e4b05b\",\"price\":2,\"amount\":\"1\",\"updateFlag\":false,\"orderField\":1,\"rootProdKey\":\"1714299421984252\",\"prodKey\":\"1714299421984252\"},{\"nodeType\":\"standard\",\"newBomPath\":\"r1.662e0f46d0b54c000804aedb1\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"relatedCoreId\":null,\"bomId\":\"662e0f46d0b54c000804aedb1\",\"productId\":\"65dda40a3f319f00070bc11c1\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299374267245\",\"prodKey\":\"1714299374267250\",\"selected_by_default\":true}]") | true
        false                 | false      | false          | [new ObjectData("_id": "662e0f46d0b54c000804aedb2", "enabled_status": true, "product_id": "p1", "price_mode": 2, "parent_bom_id": "662e0f46d0b54c000804aedb1", "bom_path": "r1.662e0f46d0b54c000804aedb1.662e0f46d0b54c000804aedb2", "selected_by_default": true), new ObjectData("_id": "662e0f46d0b54c000804aedb1", "product_id": "p2", "bom_path": "r1.662e0f46d0b54c000804aedb1", "price_mode": 2, "enabled_status": true, "parent_bom_id": "662e0f46d0b54c000804aedb", "selected_by_default": true, "share_rate": "10"), new ObjectData("_id": "r1", "product_id": "p4", "bom_path": "r1", "price_mode": 2, "enabled_status": true), new ObjectData("_id": "662e0f46d0b54c000804aedb", "product_id": "p3", "price_mode": 2, "bom_path": "r1.662e0f46d0b54c000804aedb", "enabled_status": true, "parent_bom_id": "r1")]                     | getBomList("[{\"nodeType\":\"temp\",\"newBomPath\":\"r1.662e0f46d0b54c000804aedb\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"bomId\":\"662e0f46d0b54c000804aedb\",\"productId\":\"65dda40a3f319f00070bc11c\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299421984252\",\"prodKey\":\"1714299374267245\"},{\"coreId\":\"662e0f46d0b54c000804aede\",\"newBomPath\":\"r1\",\"nodeBomCoreType\":\"configure\",\"nodeBomCoreVersion\":\"b20240428-v001301\",\"bomId\":\"r1\",\"productId\":\"65ddd3c83f319f000728ee73\",\"priceBookId\":\"6594c79bc715080007e4b05b\",\"price\":2,\"amount\":\"1\",\"updateFlag\":false,\"orderField\":1,\"rootProdKey\":\"1714299421984252\",\"prodKey\":\"1714299421984252\"},{\"nodeType\":\"standard\",\"newBomPath\":\"r1.662e0f46d0b54c000804aedb1\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"relatedCoreId\":null,\"bomId\":\"662e0f46d0b54c000804aedb1\",\"productId\":\"65dda40a3f319f00070bc11c1\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299374267245\",\"prodKey\":\"1714299374267250\",\"selected_by_default\":true}]") | true
        true                  | false      | true           | [new ObjectData( "_id": "662e0f46d0b54c000804aedb2", "enabled_status": true, "product_id": "p1", "price_mode": 2, "parent_bom_id": "662e0f46d0b54c000804aedb1", "bom_path": "r1.662e0f46d0b54c000804aedb1.662e0f46d0b54c000804aedb2", "selected_by_default": true), new ObjectData("_id": "662e0f46d0b54c000804aedb1", "product_id": "p2", "bom_path": "r1.662e0f46d0b54c000804aedb1", "price_mode": 2, "enabled_status": true, "parent_bom_id": "662e0f46d0b54c000804aedb", "selected_by_default": true, "share_rate": "10"), new ObjectData("_id": "r1", "product_id": "p4", "bom_path": "r1", "price_mode": 2, "enabled_status": true), new ObjectData("_id": "662e0f46d0b54c000804aedb", "product_id": "p3", "price_mode": 2, "bom_path": "r1.662e0f46d0b54c000804aedb", "enabled_status": true, "parent_bom_id": "r1")] | getBomList("[{\"nodeType\":\"temp\",\"newBomPath\":\"r1.662e0f46d0b54c000804aedb\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"bomId\":\"662e0f46d0b54c000804aedb\",\"productId\":\"65dda40a3f319f00070bc11c\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299421984252\",\"prodKey\":\"1714299374267245\"},{\"coreId\":\"662e0f46d0b54c000804aede\",\"newBomPath\":\"r1\",\"nodeBomCoreType\":\"configure\",\"nodeBomCoreVersion\":\"b20240428-v001301\",\"bomId\":\"r1\",\"productId\":\"65ddd3c83f319f000728ee73\",\"priceBookId\":\"6594c79bc715080007e4b05b\",\"price\":2,\"amount\":\"1\",\"updateFlag\":false,\"orderField\":1,\"rootProdKey\":\"1714299421984252\",\"prodKey\":\"1714299421984252\"},{\"nodeType\":\"standard\",\"newBomPath\":\"r1.662e0f46d0b54c000804aedb1\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"relatedCoreId\":null,\"bomId\":\"662e0f46d0b54c000804aedb1\",\"productId\":\"65dda40a3f319f00070bc11c1\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299374267245\",\"prodKey\":\"1714299374267250\",\"selected_by_default\":true}]") | true
        false                 | false      | true           | [new ObjectData("_id": "662e0f46d0b54c000804aedb2", "enabled_status": true, "product_id": "p1", "price_mode": 2, "parent_bom_id": "662e0f46d0b54c000804aedb1", "bom_path": "r1.662e0f46d0b54c000804aedb1.662e0f46d0b54c000804aedb2", "selected_by_default": true), new ObjectData("_id": "662e0f46d0b54c000804aedb1", "product_id": "p2", "bom_path": "r1.662e0f46d0b54c000804aedb1", "price_mode": 2, "enabled_status": true, "parent_bom_id": "662e0f46d0b54c000804aedb", "selected_by_default": true, "share_rate": "10"), new ObjectData("_id": "r1", "product_id": "p4", "bom_path": "r1", "price_mode": 2, "enabled_status": true), new ObjectData("_id": "662e0f46d0b54c000804aedb", "product_id": "p3", "price_mode": 2, "bom_path": "r1.662e0f46d0b54c000804aedb", "enabled_status": true, "parent_bom_id": "r1")]                     | getBomList("[{\"nodeType\":\"temp\",\"newBomPath\":\"r1.662e0f46d0b54c000804aedb\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"bomId\":\"662e0f46d0b54c000804aedb\",\"productId\":\"65dda40a3f319f00070bc11c\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299421984252\",\"prodKey\":\"1714299374267245\"},{\"coreId\":\"662e0f46d0b54c000804aede\",\"newBomPath\":\"r1\",\"nodeBomCoreType\":\"configure\",\"nodeBomCoreVersion\":\"b20240428-v001301\",\"bomId\":\"r1\",\"productId\":\"65ddd3c83f319f000728ee73\",\"priceBookId\":\"6594c79bc715080007e4b05b\",\"price\":2,\"amount\":\"1\",\"updateFlag\":false,\"orderField\":1,\"rootProdKey\":\"1714299421984252\",\"prodKey\":\"1714299421984252\"},{\"nodeType\":\"standard\",\"newBomPath\":\"r1.662e0f46d0b54c000804aedb1\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"relatedCoreId\":null,\"bomId\":\"662e0f46d0b54c000804aedb1\",\"productId\":\"65dda40a3f319f00070bc11c1\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299374267245\",\"prodKey\":\"1714299374267250\",\"selected_by_default\":true}]") | true
    }

    ServiceContext getServiceContext(User user, String serviceName, String serviceMethod) {
        RequestContext.RequestContextBuilder requestContextBuilder = RequestContext.builder()
        requestContextBuilder.user(user)
        requestContextBuilder.tenantId(user.getTenantId())
        return new ServiceContext(requestContextBuilder.build(), serviceName, serviceMethod)
    }


    def "test getBomPriceByCond"() {
        given:
        def arg = new QueryBomPriceModel.Param()
        arg.setAccountId(accoutId)
        arg.setRootBomId("r1")
        arg.setApiName("SalesOrderObj")
        arg.setDetailApiName("SalesOrderProductObj")
        arg.setCoreId("c1")
        arg.setRootAmount("1")
        arg.setNoCalPrice(false)
        arg.setBomList(bomList)
        def result = new RealPriceModel.Result()
        result.setNewRst(priceList)
        def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        Whitebox.setInternalState(tester, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(false).when(bizConfigThreadLocalCacheService, "bomAdaptationPriceListRules", anyString())
        def availableRangeCoreService = PowerMockito.mock(AvailableRangeCoreService)
        Whitebox.setInternalState(tester, "availableRangeCoreService", availableRangeCoreService)
        PowerMockito.doReturn(result).when(availableRangeCoreService, "getRealPrice", any(), any())
        when:
        Whitebox.invokeMethod(tester, "getBomPriceByCond", dataList, arg, user)
        then:
        priceList.isEmpty().equals(re)
        where:
        re    | accoutId | dataList                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | bomList | user | priceList
        true  | ""       | [new ObjectData("price_mode": 2, "nodeType": "standard", "newBomPath": "662e0f46d0b54c000804aed9.662e0f46d0b54c000804aedb", "nodeBomCoreType": null, "nodeBomCoreVersion": null, "relatedCoreId": null, "bomId": "662e0f46d0b54c000804aedb", "productId": "65dda40a3f319f00070bc11c", "priceBookId": "", "price": 10, "amount": 2, "updateFlag": true, "orderField": 0, "rootProdKey": "1714299421984252", "parentProdKey": "1714299421984252", "prodKey": "1714299374267245"), new ObjectData("coreId": "662e0f46d0b54c000804aede", "newBomPath": "662e0f46d0b54c000804aed9", "nodeBomCoreType": "configure", "nodeBomCoreVersion": "b20240428-v001301", "bomId": "r1", "productId": "65ddd3c83f319f000728ee73", "priceBookId": "6594c79bc715080007e4b05b", "price": 2, "amount": "1", "updateFlag": false, "orderField": 1, "nodeType": "temp", "rootProdKey": "1714299421984252", "prodKey": "1714299421984252")]                                    | getBomList("[{\"price_mode\":2,\"nodeType\":\"temp\",\"newBomPath\":\"662e0f46d0b54c000804aed9.662e0f46d0b54c000804aedb\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"relatedCoreId\":null,\"bomId\":\"662e0f46d0b54c000804aedb\",\"productId\":\"65dda40a3f319f00070bc11c\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"nodeType\":\"standard\",\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299421984252\",\"prodKey\":\"1714299374267245\"},{\"coreId\":\"662e0f46d0b54c000804aede\",\"newBomPath\":\"662e0f46d0b54c000804aed9\",\"nodeBomCoreType\":\"configure\",\"nodeBomCoreVersion\":\"b20240428-v001301\",\"bomId\":\"r1\",\"productId\":\"65ddd3c83f319f000728ee73\",\"priceBookId\":\"6594c79bc715080007e4b05b\",\"price\":2,\"amount\":\"1\",\"updateFlag\":false,\"orderField\":1,\"nodeType\":null,\"rootProdKey\":\"1714299421984252\",\"prodKey\":\"1714299421984252\",\"nodeType\":\"temp\"}]") | new User("71568", "-10000") | []
        true  | "z1"     | [new ObjectData("price_mode": 2, "nodeType": "standard", "newBomPath": "662e0f46d0b54c000804aed9.662e0f46d0b54c000804aedb", "nodeBomCoreType": null, "nodeBomCoreVersion": null, "relatedCoreId": null, "bomId": "662e0f46d0b54c000804aedb", "productId": "65dda40a3f319f00070bc11c", "priceBookId": "", "price": 10, "amount": 2, "updateFlag": true, "orderField": 0, "rootProdKey": "1714299421984252", "parentProdKey": "1714299421984252", "prodKey": "1714299374267245"), new ObjectData("coreId": "662e0f46d0b54c000804aede", "newBomPath": "662e0f46d0b54c000804aed9", "nodeBomCoreType": "configure", "nodeBomCoreVersion": "b20240428-v001301", "bomId": "r1", "productId": "65ddd3c83f319f000728ee73", "priceBookId": "6594c79bc715080007e4b05b", "price": 2, "amount": "1", "updateFlag": false, "orderField": 1, "nodeType": "temp", "rootProdKey": "1714299421984252", "prodKey": "1714299421984252")]                                    | getBomList("[{\"price_mode\":2,\"nodeType\":\"temp\",\"newBomPath\":\"662e0f46d0b54c000804aed9.662e0f46d0b54c000804aedb\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"relatedCoreId\":null,\"bomId\":\"662e0f46d0b54c000804aedb\",\"productId\":\"65dda40a3f319f00070bc11c\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"nodeType\":\"standard\",\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299421984252\",\"prodKey\":\"1714299374267245\"},{\"coreId\":\"662e0f46d0b54c000804aede\",\"newBomPath\":\"662e0f46d0b54c000804aed9\",\"nodeBomCoreType\":\"configure\",\"nodeBomCoreVersion\":\"b20240428-v001301\",\"bomId\":\"r1\",\"productId\":\"65ddd3c83f319f000728ee73\",\"priceBookId\":\"6594c79bc715080007e4b05b\",\"price\":2,\"amount\":\"1\",\"updateFlag\":false,\"orderField\":1,\"nodeType\":null,\"rootProdKey\":\"1714299421984252\",\"prodKey\":\"1714299421984252\",\"nodeType\":\"temp\"}]") | new User("71568", "-10000") | []
        false | "z1"     | [new ObjectData("_id": "662e0f46d0b54c000804aedb", "price_mode": 2, "nodeType": "standard", "newBomPath": "662e0f46d0b54c000804aed9.662e0f46d0b54c000804aedb", "nodeBomCoreType": null, "nodeBomCoreVersion": null, "relatedCoreId": null, "bomId": "662e0f46d0b54c000804aedb", "productId": "65dda40a3f319f00070bc11c", "priceBookId": "", "price": 10, "amount": 2, "updateFlag": true, "orderField": 0, "rootProdKey": "1714299421984252", "parentProdKey": "1714299421984252", "prodKey": "1714299374267245"), new ObjectData("coreId": "662e0f46d0b54c000804aede", "newBomPath": "662e0f46d0b54c000804aed9", "nodeBomCoreType": "configure", "nodeBomCoreVersion": "b20240428-v001301", "bomId": "r1", "productId": "65ddd3c83f319f000728ee73", "priceBookId": "6594c79bc715080007e4b05b", "price": 2, "amount": "1", "updateFlag": false, "orderField": 1, "nodeType": "temp", "rootProdKey": "1714299421984252", "prodKey": "1714299421984252")] | getBomList("[{\"price_mode\":2,\"nodeType\":\"temp\",\"newBomPath\":\"662e0f46d0b54c000804aed9.662e0f46d0b54c000804aedb\",\"nodeBomCoreType\":null,\"nodeBomCoreVersion\":null,\"relatedCoreId\":null,\"bomId\":\"662e0f46d0b54c000804aedb\",\"productId\":\"65dda40a3f319f00070bc11c\",\"priceBookId\":\"\",\"price\":10,\"amount\":2,\"updateFlag\":true,\"orderField\":0,\"nodeType\":\"standard\",\"rootProdKey\":\"1714299421984252\",\"parentProdKey\":\"1714299421984252\",\"prodKey\":\"1714299374267245\"},{\"coreId\":\"662e0f46d0b54c000804aede\",\"newBomPath\":\"662e0f46d0b54c000804aed9\",\"nodeBomCoreType\":\"configure\",\"nodeBomCoreVersion\":\"b20240428-v001301\",\"bomId\":\"r1\",\"productId\":\"65ddd3c83f319f000728ee73\",\"priceBookId\":\"6594c79bc715080007e4b05b\",\"price\":2,\"amount\":\"1\",\"updateFlag\":false,\"orderField\":1,\"nodeType\":null,\"rootProdKey\":\"1714299421984252\",\"prodKey\":\"1714299421984252\",\"nodeType\":\"temp\"}]") | new User("71568", "-10000") | [new ObjectDataDocument("bom_id": "662e0f46d0b54c000804aedb")]


    }


    def getBomList(String str) {
        def array = JSON.parseArray(str, QueryBomPriceModel.BomInfo.class)
        array
    }

    def getPriceList() {
        def array = [new ObjectData("_id": "662e0f46d0b54c000804aedb2", "bom_id": "662e0f46d0b54c000804aedb2", "enabled_status": true, "parent_bom_id": "662e0f46d0b54c000804aedb1", "selected_by_default": true), new ObjectData("_id": "662e0f46d0b54c000804aedb1", "bom_id": "662e0f46d0b54c000804aedb1", "bom_path": "r1.662e0f46d0b54c000804aedb1", "enabled_status": true, "parent_bom_id": "r1", "selected_by_default": true, "share_rate": "10"), new ObjectData("_id": "r1", "bom_id": "r1", "bom_path": "r1", "enabled_status": true), new ObjectData("_id": "662e0f46d0b54c000804aedb", "bom_id": "662e0f46d0b54c000804aedb", "bom_path": "r1.662e0f46d0b54c000804aedb", "enabled_status": true, "parent_bom_id": "r1")]
        array
    }

    def getField() {
        List<ISelectOption> selectOptions = Lists.newArrayList();
        SelectOption one = new SelectOption("个", "Electronic", "");
        SelectOption two = new SelectOption("箱", "Paper", "");
        selectOptions.add(one)
        selectOptions.add(two)
        IFieldDescribe fieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName("unit")
                .label("单位")
                .required(false)
                .selectOptions(selectOptions)
                .build();
        List<IFieldDescribe> list = Lists.newArrayList()
        list.add(fieldDescribe)
        list
    }

    def domain() {
        def domain = new DomainPluginParam()
        def detail = new DomainPluginParam.DetailObj()
        detail.setObjectApiName("SalesOrderProductObj")
        detail.setFieldMapping(Maps.newHashMap())
        domain.setDetails([detail])
        domain
    }


    def idMapArg(String id) {
        def map = Maps.newHashMap()
        map.put(id, new ObjectData("_id": id))
        map

    }

    def groupMapArg(String id) {
        def map = Maps.newHashMap()
        map.put(id, new ObjectData("_id": id, "group_options_control": true, "min_prod_count": 3, "max_prod_count": 1))
        map

    }

    def getArg(String str) {
        def array = Lists.newArrayList()
        if (StringUtils.isNotBlank(str)) {
            array = JSON.parseArray(str, CheckProductBomModel.ProductBom.class)
        }
        array
    }

    def "test calculateBalance"() {
        given:
        def serviceContext = getServiceContext(user, "", "")
        def arg = new QueryBomPriceModel.Param()
        arg.setRootBomId("r1")
        arg.setRootAmount("2")
        arg.setRootSubtotal("100")
        arg.setApiName("SalesOrderObj")
        arg.setDetailApiName("SalesOrderProductObj")
        arg.setRootRowId("row1")
        String str = "{\"masterObjectApiName\":\"SalesOrderObj\",\"masterData\":{\"object_describe_api_name\":\"SalesOrderObj\",\"record_type\":\"record_SrpMR__c\",\"created_by\":[\"1000\"],\"owner\":[\"1000\"],\"data_own_department\":[\"1000\"],\"data_own_department__r\":\"研发部门\",\"single_choice_1__c\":\"option_example_option__c\",\"is_user_define_work_flow\":false,\"dynamic_amount\":\"0\",\"changed_status\":\"normal\",\"close_status\":false,\"remark\":\"test\",\"bom_created_status\":false,\"base_returned_goods_amount\":null,\"base_plan_payment_amount\":null,\"payment_amount\":null,\"field_ODQrj__c\":\"100.00\",\"invoice_amount\":null,\"base_refund_amount\":null,\"returned_goods_amount\":null,\"refund_amount\":null,\"product_amount\":\"100.00\",\"base_product_amount\":\"100.00\",\"field_IYo2p__c\":null,\"field_9l8k6__c\":null,\"base_invoice_amount\":null,\"plan_payment_amount\":null,\"close_amount\":\"0.00\",\"base_payment_amount\":null,\"field_Yctyb__c\":\"100.00\",\"attribute_constraint_id__c\":\"\",\"ship_to_party\":\"67493652a7fae500079d11a2\",\"order_status\":\"7\",\"field_OQ8o7__c\":\"444.00\",\"field_sbe2d__c\":\"100.00\",\"field_3KkBo__c\":\"100.00\",\"order_amount\":\"100.00\",\"no_invoice_amount\":\"100.00\",\"base_no_invoice_amount\":\"100.00\",\"invoice_status\":\"3\",\"discount\":\"100.0000\",\"receivable_amount\":\"100.00\",\"base_receivable_amount\":\"100.00\",\"base_order_amount\":\"100.00\",\"created_by__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":*************,\"modifyTime\":*************,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"owner__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":*************,\"modifyTime\":*************,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"1000\",\"deptName\":\"研发部门\",\"status\":0,\"deptType\":\"dept\"}],\"order_time\":*************,\"field_XlNzp__c\":444,\"account_id__r\":\"北京百度网讯科技有限公司\",\"account_id\":\"67493652a7fae500079d11a2\",\"ship_to_party__r\":\"北京百度网讯科技有限公司\",\"ship_to_add\":\"北京市海淀区上地十街10号百度大厦2层\",\"ship_to_tel\":\"\",\"ship_to_id\":\"\",\"ship_to_id__r\":\"\"},\"detailDataMap\":{\"SalesOrderProductObj\":{\"****************\":{\"record_type\":\"default__c\",\"object_describe_api_name\":\"SalesOrderProductObj\",\"rowId\":\"****************\",\"object_describe_id\":\"6594c79fc715080007e4b227\",\"lock_rule\":\"default_lock_rule\",\"detail_type_changed\":false,\"base_order_product_amount\":\"1.00\",\"life_status\":\"normal\",\"pricing_period\":1,\"dynamic_amount\":\"0\",\"changed_status\":\"normal\",\"lock_status\":\"0\",\"detail_type\":\"standard\",\"close_status\":false,\"field_d6akF__c\":\"1\",\"settlement_amount_per_period\":\"\",\"settlement_period\":1,\"order_product_amount\":\"1.00\",\"field_single_choice__c\":\"option1\",\"print_hierarchy\":\".\",\"product_id\":\"677b4821201f020007a52d49\",\"product_id__r\":\"测试123\",\"attribute_group\":{\"66e5526ee45a130007dc1c5e\":\"66e5526ee45a130007dc1c5f\",\"66e552e9e45a130007dc236f\":\"66e552e9e45a130007dc2370\",\"677ceebf7afc980007614dfb\":\"677ceebf7afc980007614dfc\",\"677cee977afc980007614842\":\"677cee977afc980007614843\"},\"attribute_json\":{\"66e5526ee45a130007dc1c5e\":\"66e5526ee45a130007dc1c5f\",\"66e552e9e45a130007dc236f\":\"66e552e9e45a130007dc2370\",\"677ceebf7afc980007614dfb\":\"677ceebf7afc980007614dfc\",\"677cee977afc980007614842\":\"677cee977afc980007614843\"},\"attribute_group_text\":\"现场服务:是;网络仲裁:是;属性无分组2:8aod;属性无分组1:5768678\",\"price_book_product_id\":\"677b4821201f020007a52d4990242\",\"price_book_product_id__r\":\"PBProdCode20250106002820\",\"price_book_id\":\"6594c79bc715080007e4b05b\",\"price_book_id__r\":\"标准价格表\",\"product_price\":100,\"discount\":\"100\",\"price_book_discount\":\"100\",\"price_book_price\":\"100.00\",\"is_package\":\"是\",\"is_package__v\":true,\"quantity\":\"1\",\"node_type\":null,\"bom_core_id\":\"682bef270ccfe3000763cbbb\",\"bom_core_id__r\":\"20250520-001930\",\"bom_id\":\"682bef270ccfe3000763cbb8\",\"new_bom_path\":\"682bef270ccfe3000763cbb8\",\"bom_type__v\":\"configure\",\"prod_pkg_key\":\"****************\",\"root_prod_pkg_key\":\"****************\",\"attribute\":\"现场服务:是;网络仲裁:是;属性无分组2:8aod;属性无分组1:5768678\",\"selectedAttr\":{\"66e5526ee45a130007dc1c5e\":{\"name\":\"现场服务\",\"value_ids\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"name\":\"是\"}]},\"66e552e9e45a130007dc236f\":{\"name\":\"网络仲裁\",\"value_ids\":[{\"id\":\"66e552e9e45a130007dc2370\",\"name\":\"是\"}]},\"677ceebf7afc980007614dfb\":{\"name\":\"属性无分组2\",\"value_ids\":[{\"id\":\"677ceebf7afc980007614dfc\",\"name\":\"8aod\"}]},\"677cee977afc980007614842\":{\"name\":\"属性无分组1\",\"value_ids\":[{\"id\":\"677cee977afc980007614843\",\"name\":\"5768678\"}]}},\"pricing_mode\":\"one\",\"service_start_time\":\"\",\"service_end_time\":\"\",\"whole_period_sale\":\"\",\"base_sales_price\":\"100.00\",\"base_product_price\":\"100.00\",\"product_status__v\":\"1\",\"is_saleable\":\"是\",\"field_wuIbU__c\":\"200.00\",\"product_status__r\":\"已上架\",\"mc_exchange_rate\":null,\"settlement_cycle\":\"\",\"dynamic_amortize_amount\":\"0.00\",\"pricing_rate\":\"0\",\"settlement_rate\":\"0\",\"field_0pkIi__c\":\"100.00\",\"custom_reference_field__c__r\":\"\",\"bom_type\":\"配置BOM\",\"field_mWdmO__c\":\"100.00\",\"bom_type__r\":\"配置BOM\",\"product_life_status\":\"正常\",\"custom_reference_field__c__v\":null,\"field_ws65F__c\":1,\"unit\":\"\",\"subtotal\":\"100\",\"field_G7hgK__c\":\"1.00\",\"close_amount\":\"0.00\",\"field_0QNiv__c\":\"100.00\",\"product_life_status__r\":\"正常\",\"product_status\":\"已上架\",\"field_81sf2__c\":\"100.000000\",\"amortize_price\":\"100.00\",\"settlement_mode\":\"\",\"field_qq7p0__c\":\"100.00\",\"bom_version\":\"b20250520-v001930\",\"custom_reference_field__c\":\"\",\"field_Cj2J2__c\":\"7.00\",\"owner__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":*************,\"modifyTime\":*************,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"product_life_status__v\":\"normal\",\"mc_functional_currency\":null,\"is_saleable__v\":true,\"owner__r\":{\"picAddr\":\"\",\"mobile\":null,\"description\":\"\",\"dept\":\"1000\",\"supervisorId\":null,\"title\":null,\"empNum\":\"\",\"modifyTime\":*************,\"post\":\"\",\"createTime\":*************,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"position\":null,\"enterpriseName\":null,\"email\":\"\",\"status\":0},\"field_XvfF1__c\":\"100.00\",\"owner\":[\"1000\"],\"price_list_discount_custom__c\":\"100.00\",\"unit__r\":\"\",\"field_2klbf__c\":\"3.00\",\"field_2siog__c\":\"100.00\",\"mc_currency\":\"\",\"pricing_cycle\":\"\",\"field_0eO1T__c\":\"100.00\",\"base_subtotal\":\"100.00\",\"sales_price\":\"100\",\"unit__v\":null,\"mc_exchange_rate_version\":null,\"amortize_subtotal\":\"100.00\",\"price_per_set\":100,\"__originalTotalMoney\":100,\"field_hw19f__c\":100,\"children\":[\"1747721739324913\"],\"_trLevel\":0,\"_trFoldIcon\":true,\"__operateWidth\":160,\"_cellStatus\":{\"price_book_id\":{\"readonly\":true},\"product_id\":{\"readonly\":true},\"price_book_product_id\":{\"readonly\":true},\"sale_contract_line_id\":{\"readonly\":true},\"node_price\":{\"readonly\":true},\"node_discount\":{\"readonly\":true},\"product_price\":{\"readonly\":true},\"bom_id\":{\"readonly\":true},\"parent_prod_package_id\":{\"readonly\":true},\"price_book_price\":{\"readonly\":true},\"pricing_period\":{\"readonly\":true},\"service_start_time\":{\"readonly\":true},\"service_end_time\":{\"readonly\":true},\"settlement_cycle\":{\"readonly\":true},\"settlement_rate\":{\"readonly\":true},\"settlement_period\":{\"readonly\":true},\"settlement_amount_per_period\":{\"readonly\":true},\"price_per_set\":{\"readonly\":true}},\"__tbIndex\":0}}},\"calculateFieldApiNames\":{\"SalesOrderProductObj\":[\"base_sales_price\",\"field_0QNiv__c\",\"base_product_price\",\"field_mWdmO__c\",\"price_book_price\",\"field_wuIbU__c\",\"price_per_set\",\"amortize_price\",\"field_0eO1T__c\",\"subtotal\",\"base_subtotal\",\"sales_price\",\"close_amount\",\"field_0pkIi__c\",\"amortize_subtotal\"]},\"modifiedObjectApiName\":\"SalesOrderProductObj\",\"modifiedDataIndexList\":[\"****************\"],\"calculateFields\":{\"SalesOrderProductObj\":[{\"fieldName\":\"base_sales_price\",\"order\":10},{\"fieldName\":\"field_0QNiv__c\",\"order\":7},{\"fieldName\":\"base_product_price\",\"order\":7},{\"fieldName\":\"field_mWdmO__c\",\"order\":7},{\"fieldName\":\"price_book_price\",\"order\":7},{\"fieldName\":\"field_wuIbU__c\",\"order\":10},{\"fieldName\":\"price_per_set\",\"order\":6},{\"fieldName\":\"amortize_price\",\"order\":13},{\"fieldName\":\"field_0eO1T__c\",\"order\":8},{\"fieldName\":\"subtotal\",\"order\":10},{\"fieldName\":\"base_subtotal\",\"order\":12},{\"fieldName\":\"sales_price\",\"order\":8},{\"fieldName\":\"close_amount\",\"order\":14},{\"fieldName\":\"field_0pkIi__c\",\"order\":7},{\"fieldName\":\"amortize_subtotal\",\"order\":12}]}}"
        BatchCalculate.Arg object = new BatchCalculate.Arg();
        def map = Maps.newHashMap()
        map.put("row1",ObjectDataDocument.of(new ObjectData()))
        def map2 = Maps.newHashMap()
        map2.put(Utils.SALES_ORDER_PRODUCT_API_NAME,map)
        object.setDetailDataMap(map2)
        arg.setCalculateArg(object)
        
        def resultList = [
            new ObjectData("_id": "r1", "adjust_price": "50", "price_book_discount": "80", "amount": "2"),
            new ObjectData("_id": "b1", "adjust_price": "30", "price_book_discount": "90", "amount": "1")
        ]

        def infraServiceFacade = PowerMockito.mock(InfraServiceFacade)
        Whitebox.setInternalState(tester, "infraServiceFacade", infraServiceFacade)
        PowerMockito.doReturn(domain()).when(infraServiceFacade, "findPluginParam", any(), any(), any())

        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        def objectDescribe = new ObjectDescribe()
        objectDescribe.addFieldDescribeList(getField())
        PowerMockito.doReturn(objectDescribe).when(serviceFacade, "findObject", any(), any())

        def calculateService = PowerMockito.mock(CalculateService)
        Whitebox.setInternalState(tester, "calculateService", calculateService)
        def calculateResult = new BatchCalculate.Result()
        def calculateResultMap = Maps.newHashMap()
        calculateResultMap.put("row1", new ObjectDataDocument("subtotal": new BigDecimal("100")))
        calculateResult.setCalculateResult(Maps.newHashMap())
        calculateResult.getCalculateResult().put("SalesOrderProductObj", calculateResultMap)
        PowerMockito.doReturn(calculateResult).when(calculateService, "batchCalculate", any(), any())

        when:
        def balance = Whitebox.invokeMethod(tester, "calculateBalance", arg, resultList, serviceContext)

        then:
        balance == new BigDecimal("-20") // (50 * 0.8 * 2) - 100 = -20
        resultList.find { it.getId() == "r1" }.get("subtotal") == new BigDecimal("100")
        resultList.find { it.getId() == "r1" }.get("node_balance") == new BigDecimal("-20")

    }

    def calculateArg(){
        String str = "{\"masterObjectApiName\":\"SalesOrderObj\",\"masterData\":{\"object_describe_api_name\":\"SalesOrderObj\",\"record_type\":\"record_SrpMR__c\",\"created_by\":[\"1000\"],\"owner\":[\"1000\"],\"data_own_department\":[\"1000\"],\"data_own_department__r\":\"研发部门\",\"single_choice_1__c\":\"option_example_option__c\",\"is_user_define_work_flow\":false,\"dynamic_amount\":\"0\",\"changed_status\":\"normal\",\"close_status\":false,\"remark\":\"test\",\"bom_created_status\":false,\"base_returned_goods_amount\":null,\"base_plan_payment_amount\":null,\"payment_amount\":null,\"field_ODQrj__c\":\"100.00\",\"invoice_amount\":null,\"base_refund_amount\":null,\"returned_goods_amount\":null,\"refund_amount\":null,\"product_amount\":\"100.00\",\"base_product_amount\":\"100.00\",\"field_IYo2p__c\":null,\"field_9l8k6__c\":null,\"base_invoice_amount\":null,\"plan_payment_amount\":null,\"close_amount\":\"0.00\",\"base_payment_amount\":null,\"field_Yctyb__c\":\"100.00\",\"attribute_constraint_id__c\":\"\",\"ship_to_party\":\"67493652a7fae500079d11a2\",\"order_status\":\"7\",\"field_OQ8o7__c\":\"444.00\",\"field_sbe2d__c\":\"100.00\",\"field_3KkBo__c\":\"100.00\",\"order_amount\":\"100.00\",\"no_invoice_amount\":\"100.00\",\"base_no_invoice_amount\":\"100.00\",\"invoice_status\":\"3\",\"discount\":\"100.0000\",\"receivable_amount\":\"100.00\",\"base_receivable_amount\":\"100.00\",\"base_order_amount\":\"100.00\",\"created_by__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":*************,\"modifyTime\":*************,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"owner__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":*************,\"modifyTime\":*************,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"1000\",\"deptName\":\"研发部门\",\"status\":0,\"deptType\":\"dept\"}],\"order_time\":*************,\"field_XlNzp__c\":444,\"account_id__r\":\"北京百度网讯科技有限公司\",\"account_id\":\"67493652a7fae500079d11a2\",\"ship_to_party__r\":\"北京百度网讯科技有限公司\",\"ship_to_add\":\"北京市海淀区上地十街10号百度大厦2层\",\"ship_to_tel\":\"\",\"ship_to_id\":\"\",\"ship_to_id__r\":\"\"},\"detailDataMap\":{\"SalesOrderProductObj\":{\"****************\":{\"record_type\":\"default__c\",\"object_describe_api_name\":\"SalesOrderProductObj\",\"rowId\":\"****************\",\"object_describe_id\":\"6594c79fc715080007e4b227\",\"lock_rule\":\"default_lock_rule\",\"detail_type_changed\":false,\"base_order_product_amount\":\"1.00\",\"life_status\":\"normal\",\"pricing_period\":1,\"dynamic_amount\":\"0\",\"changed_status\":\"normal\",\"lock_status\":\"0\",\"detail_type\":\"standard\",\"close_status\":false,\"field_d6akF__c\":\"1\",\"settlement_amount_per_period\":\"\",\"settlement_period\":1,\"order_product_amount\":\"1.00\",\"field_single_choice__c\":\"option1\",\"print_hierarchy\":\".\",\"product_id\":\"677b4821201f020007a52d49\",\"product_id__r\":\"测试123\",\"attribute_group\":{\"66e5526ee45a130007dc1c5e\":\"66e5526ee45a130007dc1c5f\",\"66e552e9e45a130007dc236f\":\"66e552e9e45a130007dc2370\",\"677ceebf7afc980007614dfb\":\"677ceebf7afc980007614dfc\",\"677cee977afc980007614842\":\"677cee977afc980007614843\"},\"attribute_json\":{\"66e5526ee45a130007dc1c5e\":\"66e5526ee45a130007dc1c5f\",\"66e552e9e45a130007dc236f\":\"66e552e9e45a130007dc2370\",\"677ceebf7afc980007614dfb\":\"677ceebf7afc980007614dfc\",\"677cee977afc980007614842\":\"677cee977afc980007614843\"},\"attribute_group_text\":\"现场服务:是;网络仲裁:是;属性无分组2:8aod;属性无分组1:5768678\",\"price_book_product_id\":\"677b4821201f020007a52d4990242\",\"price_book_product_id__r\":\"PBProdCode20250106002820\",\"price_book_id\":\"6594c79bc715080007e4b05b\",\"price_book_id__r\":\"标准价格表\",\"product_price\":100,\"discount\":\"100\",\"price_book_discount\":\"100\",\"price_book_price\":\"100.00\",\"is_package\":\"是\",\"is_package__v\":true,\"quantity\":\"1\",\"node_type\":null,\"bom_core_id\":\"682bef270ccfe3000763cbbb\",\"bom_core_id__r\":\"20250520-001930\",\"bom_id\":\"682bef270ccfe3000763cbb8\",\"new_bom_path\":\"682bef270ccfe3000763cbb8\",\"bom_type__v\":\"configure\",\"prod_pkg_key\":\"****************\",\"root_prod_pkg_key\":\"****************\",\"attribute\":\"现场服务:是;网络仲裁:是;属性无分组2:8aod;属性无分组1:5768678\",\"selectedAttr\":{\"66e5526ee45a130007dc1c5e\":{\"name\":\"现场服务\",\"value_ids\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"name\":\"是\"}]},\"66e552e9e45a130007dc236f\":{\"name\":\"网络仲裁\",\"value_ids\":[{\"id\":\"66e552e9e45a130007dc2370\",\"name\":\"是\"}]},\"677ceebf7afc980007614dfb\":{\"name\":\"属性无分组2\",\"value_ids\":[{\"id\":\"677ceebf7afc980007614dfc\",\"name\":\"8aod\"}]},\"677cee977afc980007614842\":{\"name\":\"属性无分组1\",\"value_ids\":[{\"id\":\"677cee977afc980007614843\",\"name\":\"5768678\"}]}},\"pricing_mode\":\"one\",\"service_start_time\":\"\",\"service_end_time\":\"\",\"whole_period_sale\":\"\",\"base_sales_price\":\"100.00\",\"base_product_price\":\"100.00\",\"product_status__v\":\"1\",\"is_saleable\":\"是\",\"field_wuIbU__c\":\"200.00\",\"product_status__r\":\"已上架\",\"mc_exchange_rate\":null,\"settlement_cycle\":\"\",\"dynamic_amortize_amount\":\"0.00\",\"pricing_rate\":\"0\",\"settlement_rate\":\"0\",\"field_0pkIi__c\":\"100.00\",\"custom_reference_field__c__r\":\"\",\"bom_type\":\"配置BOM\",\"field_mWdmO__c\":\"100.00\",\"bom_type__r\":\"配置BOM\",\"product_life_status\":\"正常\",\"custom_reference_field__c__v\":null,\"field_ws65F__c\":1,\"unit\":\"\",\"subtotal\":\"100\",\"field_G7hgK__c\":\"1.00\",\"close_amount\":\"0.00\",\"field_0QNiv__c\":\"100.00\",\"product_life_status__r\":\"正常\",\"product_status\":\"已上架\",\"field_81sf2__c\":\"100.000000\",\"amortize_price\":\"100.00\",\"settlement_mode\":\"\",\"field_qq7p0__c\":\"100.00\",\"bom_version\":\"b20250520-v001930\",\"custom_reference_field__c\":\"\",\"field_Cj2J2__c\":\"7.00\",\"owner__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":*************,\"modifyTime\":*************,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"product_life_status__v\":\"normal\",\"mc_functional_currency\":null,\"is_saleable__v\":true,\"owner__r\":{\"picAddr\":\"\",\"mobile\":null,\"description\":\"\",\"dept\":\"1000\",\"supervisorId\":null,\"title\":null,\"empNum\":\"\",\"modifyTime\":*************,\"post\":\"\",\"createTime\":*************,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"position\":null,\"enterpriseName\":null,\"email\":\"\",\"status\":0},\"field_XvfF1__c\":\"100.00\",\"owner\":[\"1000\"],\"price_list_discount_custom__c\":\"100.00\",\"unit__r\":\"\",\"field_2klbf__c\":\"3.00\",\"field_2siog__c\":\"100.00\",\"mc_currency\":\"\",\"pricing_cycle\":\"\",\"field_0eO1T__c\":\"100.00\",\"base_subtotal\":\"100.00\",\"sales_price\":\"100\",\"unit__v\":null,\"mc_exchange_rate_version\":null,\"amortize_subtotal\":\"100.00\",\"price_per_set\":100,\"__originalTotalMoney\":100,\"field_hw19f__c\":100,\"children\":[\"1747721739324913\"],\"_trLevel\":0,\"_trFoldIcon\":true,\"__operateWidth\":160,\"_cellStatus\":{\"price_book_id\":{\"readonly\":true},\"product_id\":{\"readonly\":true},\"price_book_product_id\":{\"readonly\":true},\"sale_contract_line_id\":{\"readonly\":true},\"node_price\":{\"readonly\":true},\"node_discount\":{\"readonly\":true},\"product_price\":{\"readonly\":true},\"bom_id\":{\"readonly\":true},\"parent_prod_package_id\":{\"readonly\":true},\"price_book_price\":{\"readonly\":true},\"pricing_period\":{\"readonly\":true},\"service_start_time\":{\"readonly\":true},\"service_end_time\":{\"readonly\":true},\"settlement_cycle\":{\"readonly\":true},\"settlement_rate\":{\"readonly\":true},\"settlement_period\":{\"readonly\":true},\"settlement_amount_per_period\":{\"readonly\":true},\"price_per_set\":{\"readonly\":true}},\"__tbIndex\":0}}},\"calculateFieldApiNames\":{\"SalesOrderProductObj\":[\"base_sales_price\",\"field_0QNiv__c\",\"base_product_price\",\"field_mWdmO__c\",\"price_book_price\",\"field_wuIbU__c\",\"price_per_set\",\"amortize_price\",\"field_0eO1T__c\",\"subtotal\",\"base_subtotal\",\"sales_price\",\"close_amount\",\"field_0pkIi__c\",\"amortize_subtotal\"]},\"modifiedObjectApiName\":\"SalesOrderProductObj\",\"modifiedDataIndexList\":[\"****************\"],\"calculateFields\":{\"SalesOrderProductObj\":[{\"fieldName\":\"base_sales_price\",\"order\":10},{\"fieldName\":\"field_0QNiv__c\",\"order\":7},{\"fieldName\":\"base_product_price\",\"order\":7},{\"fieldName\":\"field_mWdmO__c\",\"order\":7},{\"fieldName\":\"price_book_price\",\"order\":7},{\"fieldName\":\"field_wuIbU__c\",\"order\":10},{\"fieldName\":\"price_per_set\",\"order\":6},{\"fieldName\":\"amortize_price\",\"order\":13},{\"fieldName\":\"field_0eO1T__c\",\"order\":8},{\"fieldName\":\"subtotal\",\"order\":10},{\"fieldName\":\"base_subtotal\",\"order\":12},{\"fieldName\":\"sales_price\",\"order\":8},{\"fieldName\":\"close_amount\",\"order\":14},{\"fieldName\":\"field_0pkIi__c\",\"order\":7},{\"fieldName\":\"amortize_subtotal\",\"order\":12}]}}"
        BatchCalculate.Arg object = JSON.parseObject(str, BatchCalculate.Arg.class)
        object
    }

    def "test calculateNodeAllocationField"() {
        given:
        def arg = new QueryBomPriceModel.Param()
        arg.setRootBomId("r1")
        arg.setRootAmount("2")
        arg.setRootSubtotal("100")
        
        def resultList = [
            new ObjectData("_id": "r1", "adjust_price": "50", "price_book_discount": "80", "amount": "2", "pricing_period": "1"),
            new ObjectData("_id": "b1", "adjust_price": "30", "price_book_discount": "90", "amount": "1", "pricing_period": "1")
        ]
        
        def balance = new BigDecimal("-20")
        def orderProductObjDesc = new ObjectDescribe()
        orderProductObjDesc.addFieldDescribeList(getField())
        
        when:
        Whitebox.invokeMethod(tester, "calculateNodeAllocationField", arg, resultList, balance, orderProductObjDesc, periodPlugin)
        
        then:
        def rootNode = resultList.find { it.getId() == "r1" }
        def childNode = resultList.find { it.getId() == "b1" }
        
        if (periodPlugin) {
            rootNode.get("node_balance") == new BigDecimal("-20")
            rootNode.get("node_price") == new BigDecimal("50")
            rootNode.get("node_discount") == new BigDecimal("80")
            rootNode.get("node_subtotal") == new BigDecimal("100")
            rootNode.get("price_per_set") == new BigDecimal("50")
            
            childNode.get("node_balance") == new BigDecimal("0")
            childNode.get("node_price") == new BigDecimal("30")
            childNode.get("node_discount") == new BigDecimal("90")
            childNode.get("node_subtotal") == new BigDecimal("30")
            childNode.get("price_per_set") == new BigDecimal("30")
        } else {
            rootNode.get("node_balance") == new BigDecimal("-20")
            rootNode.get("node_price") == new BigDecimal("50")
            rootNode.get("node_discount") == new BigDecimal("80")
            rootNode.get("node_subtotal") == new BigDecimal("100")
            
            childNode.get("node_balance") == new BigDecimal("0")
            childNode.get("node_price") == new BigDecimal("30")
            childNode.get("node_discount") == new BigDecimal("90")
            childNode.get("node_subtotal") == new BigDecimal("30")
        }
        
        where:
        periodPlugin << [true, false]
    }

    def "test calculateNodeField"() {
        given:
        def resultList = [
            new ObjectData("_id": "r1", "adjust_price": "50", "price_book_discount": "80", "amount": "2", "pricing_period": "1","prod_pkg_key":"111"),
            new ObjectData("_id": "b1", "adjust_price": "30", "price_book_discount": "90", "amount": "1", "pricing_period": "1","prod_pkg_key":"111")
        ]
        
        def bom = resultList.find { it.getId() == "r1" }
        def rootDiscount = new BigDecimal("80")
        def balance = new BigDecimal("-20")
        def rootAmount = new BigDecimal("2")
        def salePriceScale = 2
        def discountScale = 2
        def nodeSubtotalScale = 2
        
        when:
        Whitebox.invokeMethod(tester, "calculateNodeField", resultList, bom, rootDiscount, balance, rootAmount, salePriceScale, discountScale, nodeSubtotalScale, true)
        
        then:
        1==1
    }

}
