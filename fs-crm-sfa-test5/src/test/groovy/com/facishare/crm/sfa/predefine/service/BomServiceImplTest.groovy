package com.facishare.crm.sfa.predefine.service

import com.alibaba.fastjson.JSON
import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.predefine.service.BomServiceImpl
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreServiceImpl
import com.facishare.crm.sfa.predefine.service.cpq.model.CheckProductBomModel
import com.facishare.crm.sfa.predefine.service.cpq.model.CreateBomModel
import com.facishare.crm.sfa.predefine.service.cpq.model.GetAllParentProdListModel
import com.facishare.crm.sfa.predefine.service.cpq.model.QueryAllSubBomModel
import com.facishare.crm.sfa.utilities.util.BomUtil
import com.facishare.crm.sfa.utilities.util.GrayUtil
import com.facishare.crm.sfa.utilities.util.ProductConstraintUtil
import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.util.CollectionUtils
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.action.IActionContext
import com.facishare.paas.metadata.exception.MetadataServiceException
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.apache.commons.lang3.StringUtils
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import spock.lang.Shared

import java.util.stream.Collectors

import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.spy

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([I18nServiceImpl.class, BomUtil.class, ProductConstraintUtil.class, UdobjGrayConfig, BomCoreServiceImpl.class, ServiceContext.class, I18N.class, GrayUtil.class, User.class])
@SuppressStaticInitializationFor([
        "com.facishare.crm.sfa.utilities.util.BomUtil",
        "com.facishare.crm.sfa.utilities.util.ProductConstraintUtil",
        "com.fxiaoke.i18n.client.impl.I18nServiceImpl",
        "com.facishare.paas.appframework.core.util.UdobjGrayConfig",
        "com.facishare.paas.I18N", "com.facishare.paas.metadata.impl.search.SearchTemplateQuery",
        "com.facishare.crm.sfa.utilities.util.GrayUtil",
        "com.facishare.paas.appframework.core.model.ServiceContext", "com.facishare.paas.appframework.core.util.UdobjGrayConfig", "com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl"])
class BomServiceImplTest extends EnhancedBaseGroovyTest {

    @Shared
    private BomServiceImpl tester

    @Shared
    private User user
    @Shared
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService
    @Shared
    private ObjectDataServiceImpl objectDataService


    def setupSpec() {
        tester = new BomServiceImpl()
        user = spy(new User("71568", "-10000"))
    }

    def setup() {
        PowerMockito.mockStatic(I18N.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(BomUtil.class)
        PowerMockito.mockStatic(ProductConstraintUtil.class)

        bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        objectDataService = PowerMockito.mock(ObjectDataServiceImpl)
        Whitebox.setInternalState(tester, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        Whitebox.setInternalState(tester, "objectDataService", objectDataService)
    }

    def "test check bom"() {
        given:

        def arg = new CheckProductBomModel.Arg()
        def queryResult = new QueryResult();
        queryResult.setData(dataList)
        arg.setProductBomList(param)
        PowerMockito.when(GrayUtil.bomMasterSlaveMode(anyString())).thenReturn(mode)
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        def configService = PowerMockito.mock(ConfigService)
        Whitebox.setInternalState(tester, "configService", configService)
        PowerMockito.doReturn([new ObjectData("_id": "661270d31628d30007fb8efe", "name": "n1")]).when(serviceFacade, "findObjectDataByIdsIgnoreAll", anyString(), anyList(), anyString())
        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQuery", any(), any(), any())
        when:
        try {
            tester.checkBom(user, arg)
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then:
        1 == 1
        where:
        mode  | flag | param | dataList
        false | 0    | getArg("[{\"core_id\":\"661270f21628d30007fb9449\",\"node_bom_core_type\":\"configure\",\"node_bom_core_version\":\"b20240407-v000320\",\"rootProductId\":\"661270d31628d30007fb8efe\",\"newProductPrice\":3001,\"subBomList\":[{\"new_bom_path\":\"661270f21628d30007fb9447.661270f21628d30007fb9448\",\"core_id\":\"661270f21628d30007fb9449\",\"related_core_id\":\"64c0d78c95c5da0001eeadc2\",\"node_bom_core_type\":\"configure\",\"node_bom_core_version\":\"b20230726-v000034\",\"node_type\":\"standard\",\"product_id\":\"646dac1f91772a000100929d\",\"_id\":\"661270f21628d30007fb9448\",\"product_status\":\"已上架\",\"product_id__r\":\"智能冰箱\",\"adjust_price\":3000,\"object_describe_api_name\":\"BOMObj\",\"amount\":\"1.00\",\"product_group_id\":null,\"bom_id\":\"661270f21628d30007fb9448\",\"modified_adjust_price\":3000,\"__adjust_price\":3000,\"prod_pkg_key\":\"1713951022624243\",\"parent_prod_pkg_key\":\"1713951081470251\",\"price_book_id\":\"61011e22b1975a0001de3a05\"},{\"new_bom_path\":\"661270f21628d30007fb9447.661270f21628d30007fb9448.6471b607a2eed70001123b7f\",\"core_id\":\"64c0d78c95c5da0001eeadc2\",\"related_core_id\":null,\"node_bom_core_type\":\"product\",\"node_bom_core_version\":null,\"node_type\":\"standard\",\"product_id\":\"647066bd71e41c0001cc33fa\",\"_id\":\"6471b607a2eed70001123b7f\",\"product_status\":\"已上架\",\"product_id__r\":\"cx必填_不符合\",\"adjust_price\":0,\"object_describe_api_name\":\"BOMObj\",\"amount\":\"1.00\",\"product_group_id\":null,\"bom_id\":\"6471b607a2eed70001123b7f\",\"modified_adjust_price\":0,\"__adjust_price\":0,\"prod_pkg_key\":\"1713951022624244\",\"parent_prod_pkg_key\":\"1713951022624243\",\"price_book_id\":\"61011e22b1975a0001de3a05\"}],\"rootProdKey\":\"1713951081470251\",\"rootBomId\":\"661270f21628d30007fb9447\",\"priceBookId\":\"61011e22b1975a0001de3a05\"}]") | [new ObjectData("_id": "I1", "name": "n1")]
        false | 0    | getArg("[{\"core_id\":\"661270f21628d30007fb9449\",\"node_bom_core_type\":\"configure\",\"node_bom_core_version\":\"b20240407-v000320\",\"rootProductId\":\"661270d31628d30007fb8efe\",\"newProductPrice\":3001,\"subBomList\":[{\"new_bom_path\":\"661270f21628d30007fb9447.661270f21628d30007fb9448\",\"core_id\":\"661270f21628d30007fb9449\",\"related_core_id\":\"64c0d78c95c5da0001eeadc2\",\"node_bom_core_type\":\"configure\",\"node_bom_core_version\":\"b20230726-v000034\",\"node_type\":\"standard\",\"product_id\":\"646dac1f91772a000100929d\",\"_id\":\"661270f21628d30007fb9448\",\"product_status\":\"已上架\",\"product_id__r\":\"智能冰箱\",\"adjust_price\":3000,\"object_describe_api_name\":\"BOMObj\",\"amount\":\"1.00\",\"product_group_id\":null,\"bom_id\":\"661270f21628d30007fb9448\",\"modified_adjust_price\":3000,\"__adjust_price\":3000,\"prod_pkg_key\":\"1713951022624243\",\"parent_prod_pkg_key\":\"1713951081470251\",\"price_book_id\":\"61011e22b1975a0001de3a05\"},{\"new_bom_path\":\"661270f21628d30007fb9447.661270f21628d30007fb9448.6471b607a2eed70001123b7f\",\"core_id\":\"64c0d78c95c5da0001eeadc2\",\"related_core_id\":null,\"node_bom_core_type\":\"product\",\"node_bom_core_version\":null,\"node_type\":\"standard\",\"product_id\":\"647066bd71e41c0001cc33fa\",\"_id\":\"6471b607a2eed70001123b7f\",\"product_status\":\"已上架\",\"product_id__r\":\"cx必填_不符合\",\"adjust_price\":0,\"object_describe_api_name\":\"BOMObj\",\"amount\":\"1.00\",\"product_group_id\":null,\"bom_id\":\"6471b607a2eed70001123b7f\",\"modified_adjust_price\":0,\"__adjust_price\":0,\"prod_pkg_key\":\"1713951022624244\",\"parent_prod_pkg_key\":\"1713951022624243\",\"price_book_id\":\"61011e22b1975a0001de3a05\"}],\"rootProdKey\":\"1713951081470251\",\"rootBomId\":\"661270f21628d30007fb9447\",\"priceBookId\":\"61011e22b1975a0001de3a05\"}]") | [new ObjectData("_id": "661270d31628d30007fb8efe", "name": "n1")]
        false | 0    | getArg("[{\"node_bom_core_type\":\"configure\",\"node_bom_core_version\":\"b20240407-v000320\",\"rootProductId\":\"661270d31628d30007fb8efe\",\"newProductPrice\":3001,\"subBomList\":[{\"new_bom_path\":\"661270f21628d30007fb9447.661270f21628d30007fb9448\",\"core_id\":\"661270f21628d30007fb9449\",\"related_core_id\":\"64c0d78c95c5da0001eeadc2\",\"node_bom_core_type\":\"configure\",\"node_bom_core_version\":\"b20230726-v000034\",\"node_type\":\"standard\",\"product_id\":\"646dac1f91772a000100929d\",\"_id\":\"661270f21628d30007fb9448\",\"product_status\":\"已上架\",\"product_id__r\":\"智能冰箱\",\"adjust_price\":3000,\"object_describe_api_name\":\"BOMObj\",\"amount\":\"1.00\",\"product_group_id\":null,\"bom_id\":\"661270f21628d30007fb9448\",\"modified_adjust_price\":3000,\"__adjust_price\":3000,\"prod_pkg_key\":\"1713951022624243\",\"parent_prod_pkg_key\":\"1713951081470251\",\"price_book_id\":\"61011e22b1975a0001de3a05\"},{\"new_bom_path\":\"661270f21628d30007fb9447.661270f21628d30007fb9448.6471b607a2eed70001123b7f\",\"core_id\":\"64c0d78c95c5da0001eeadc2\",\"related_core_id\":null,\"node_bom_core_type\":\"product\",\"node_bom_core_version\":null,\"node_type\":\"standard\",\"product_id\":\"647066bd71e41c0001cc33fa\",\"_id\":\"6471b607a2eed70001123b7f\",\"product_status\":\"已上架\",\"product_id__r\":\"cx必填_不符合\",\"adjust_price\":0,\"object_describe_api_name\":\"BOMObj\",\"amount\":\"1.00\",\"product_group_id\":null,\"bom_id\":\"6471b607a2eed70001123b7f\",\"modified_adjust_price\":0,\"__adjust_price\":0,\"prod_pkg_key\":\"1713951022624244\",\"parent_prod_pkg_key\":\"1713951022624243\",\"price_book_id\":\"61011e22b1975a0001de3a05\"}],\"rootProdKey\":\"1713951081470251\",\"rootBomId\":\"661270f21628d30007fb9447\",\"priceBookId\":\"61011e22b1975a0001de3a05\"}]") | [new ObjectData("lock_rule": "default_lock_rule", "field_4vPR0__c": "345.99", "price_editable": true, "mc_exchange_rate": "1.000000", "extend_obj_data_id": "6614ba6a2c18830007e29974", "amount_any": true, "product_id": "66066eb43cd715000765c3be", "owner_department_id": "1001", "owner_department": "研发中心", "searchAfterId": ["1712634474531", "6614ba682c18830007e297d3"], "price_mode": "2", "lock_status": "0", "package": "CRM", "create_time": 1712634474531, "order_field": "2", "field_obVn8__c": "12345", "product_life_status": "normal", "version": "1", "created_by": ["1000"], "bom_path": "661270f21628d30007fb9447.6614ba682c18830007e297d3", "data_own_department": ["1001"], "name": "20240409059817", "_id": "6614ba682c18830007e297d3", "tenant_id": "82769", "field_7MMwO__c": "测试", "selected_by_default": false, "product_status": "1", "core_id__relation_ids": "661270f21628d30007fb9449", "product_id__relation_ids": "66066eb43cd715000765c3be", "core_id": "661270f21628d30007fb9449", "is_deleted": false, "is_required": false, "object_describe_api_name": "BOMObj", "root_id": "661270f21628d30007fb9447", "mc_functional_currency": "CNY", "owner": ["1000"], "amount": "1.00", "is_package": false, "last_modified_time": 1712634474531, "life_status": "normal", "enabled_status": true, "last_modified_by": ["1000"], "amount_editable": true, "mc_currency": "CNY", "record_type": "default__c", "parent_bom_id": "661270f21628d30007fb9447", "node_bom_core_type": "product", "max_amount": "5.0", "order_by": "10", "mc_exchange_rate_version": "1710333335138"), new ObjectData("lock_rule": "default_lock_rule", "field_4vPR0__c": "345.99", "price_editable": true, "mc_exchange_rate": "1.000000", "extend_obj_data_id": "661270f31628d30007fb956b", "amount_any": false, "field_e1c2f__c": "bx0001", "product_id": "646dac1f91772a000100929d", "owner_department_id": "1001", "owner_department": "研发中心", "searchAfterId": ["1712484598389", "661270f21628d30007fb9448"], "price_mode": "2", "lock_status": "0", "package": "CRM", "create_time": 1712484595736, "order_field": "1", "field_obVn8__c": "12345", "product_life_status": "normal", "version": "2", "created_by": ["1000"], "bom_path": "661270f21628d30007fb9447.661270f21628d30007fb9448", "data_own_department": ["1001"], "name": "20240407059809", "_id": "661270f21628d30007fb9448", "related_core_id": "64c0d78c95c5da0001eeadc2", "tenant_id": "82769", "field_7MMwO__c": "测试", "selected_by_default": true, "product_status": "1", "core_id__relation_ids": "661270f21628d30007fb9449", "product_id__relation_ids": "646dac1f91772a000100929d", "core_id": "661270f21628d30007fb9449", "is_deleted": false, "is_required": false, "object_describe_api_name": "BOMObj", "root_id": "661270f21628d30007fb9447", "mc_functional_currency": "CNY", "node_bom_core_version": "b20230726-v000034", "owner": ["1000"], "amount": "1.00", "is_package": false, "last_modified_time": 1712484598389, "life_status": "normal", "enabled_status": true, "last_modified_by": ["-10000"], "amount_editable": true, "mc_currency": "CNY", "record_type": "default__c", "parent_bom_id": "661270f21628d30007fb9447", "node_bom_core_type": "configure", "max_amount": "5.0", "order_by": "10", "mc_exchange_rate_version": "1710333335138"), new ObjectData("lock_rule": "default_lock_rule", "field_4vPR0__c": "345.99", "price_editable": false, "extend_obj_data_id": "6471b607a2eed70001123ba0", "product_id": "647066bd71e41c0001cc33fa", "searchAfterId": ["1690359693024", "6471b607a2eed70001123b7f"], "price_mode": "2", "lock_status": "0", "package": "CRM", "create_time": 1685173767929, "order_field": "1", "field_obVn8__c": "12345", "product_life_status": "normal", "version": "2", "created_by": ["1000"], "bom_path": "6471b607a2eed70001123b7e.6471b607a2eed70001123b7f", "name": "20230527050016", "_id": "6471b607a2eed70001123b7f", "tenant_id": "82769", "field_7MMwO__c": "测试", "selected_by_default": true, "product_status": "1", "core_id__relation_ids": "64c0d78c95c5da0001eeadc2", "product_id__relation_ids": "647066bd71e41c0001cc33fa", "core_id": "64c0d78c95c5da0001eeadc2", "is_deleted": false, "is_required": false, "object_describe_api_name": "BOMObj", "root_id": "6471b607a2eed70001123b7e", "amount": "1.00", "last_modified_time": 1690359693024, "life_status": "normal", "enabled_status": true, "last_modified_by": ["-10000"], "amount_editable": true, "record_type": "default__c", "parent_bom_id": "6471b607a2eed70001123b7e", "node_bom_core_type": "product", "max_amount": "5.0")]
        true  | 0    | getArg("[{\"node_bom_core_type\":\"configure\",\"node_bom_core_version\":\"b20240407-v000320\",\"rootProductId\":\"661270d31628d30007fb8efe\",\"newProductPrice\":3001,\"subBomList\":[{\"new_bom_path\":\"661270f21628d30007fb9447.661270f21628d30007fb9448\",\"core_id\":\"661270f21628d30007fb9449\",\"related_core_id\":\"64c0d78c95c5da0001eeadc2\",\"node_bom_core_type\":\"configure\",\"node_bom_core_version\":\"b20230726-v000034\",\"node_type\":\"standard\",\"product_id\":\"646dac1f91772a000100929d\",\"_id\":\"661270f21628d30007fb9448\",\"product_status\":\"已上架\",\"product_id__r\":\"智能冰箱\",\"adjust_price\":3000,\"object_describe_api_name\":\"BOMObj\",\"amount\":\"1.00\",\"product_group_id\":null,\"bom_id\":\"661270f21628d30007fb9448\",\"modified_adjust_price\":3000,\"__adjust_price\":3000,\"prod_pkg_key\":\"1713951022624243\",\"parent_prod_pkg_key\":\"1713951081470251\",\"price_book_id\":\"61011e22b1975a0001de3a05\"},{\"new_bom_path\":\"661270f21628d30007fb9447.661270f21628d30007fb9448.6471b607a2eed70001123b7f\",\"core_id\":\"64c0d78c95c5da0001eeadc2\",\"related_core_id\":null,\"node_bom_core_type\":\"product\",\"node_bom_core_version\":null,\"node_type\":\"standard\",\"product_id\":\"647066bd71e41c0001cc33fa\",\"_id\":\"6471b607a2eed70001123b7f\",\"product_status\":\"已上架\",\"product_id__r\":\"cx必填_不符合\",\"adjust_price\":0,\"object_describe_api_name\":\"BOMObj\",\"amount\":\"1.00\",\"product_group_id\":null,\"bom_id\":\"6471b607a2eed70001123b7f\",\"modified_adjust_price\":0,\"__adjust_price\":0,\"prod_pkg_key\":\"1713951022624244\",\"parent_prod_pkg_key\":\"1713951022624243\",\"price_book_id\":\"61011e22b1975a0001de3a05\"}],\"rootProdKey\":\"1713951081470251\",\"rootBomId\":\"661270f21628d30007fb9447\",\"priceBookId\":\"61011e22b1975a0001de3a05\"}]") | [new ObjectData("lock_rule": "default_lock_rule", "field_4vPR0__c": "345.99", "price_editable": true, "mc_exchange_rate": "1.000000", "extend_obj_data_id": "6614ba6a2c18830007e29974", "amount_any": true, "product_id": "66066eb43cd715000765c3be", "owner_department_id": "1001", "owner_department": "研发中心", "searchAfterId": ["1712634474531", "6614ba682c18830007e297d3"], "price_mode": "2", "lock_status": "0", "package": "CRM", "create_time": 1712634474531, "order_field": "2", "field_obVn8__c": "12345", "product_life_status": "normal", "version": "1", "created_by": ["1000"], "bom_path": "661270f21628d30007fb9447.6614ba682c18830007e297d3", "data_own_department": ["1001"], "name": "20240409059817", "_id": "6614ba682c18830007e297d3", "tenant_id": "82769", "field_7MMwO__c": "测试", "selected_by_default": false, "product_status": "1", "core_id__relation_ids": "661270f21628d30007fb9449", "product_id__relation_ids": "66066eb43cd715000765c3be", "core_id": "661270f21628d30007fb9449", "is_deleted": false, "is_required": false, "object_describe_api_name": "BOMObj", "root_id": "661270f21628d30007fb9447", "mc_functional_currency": "CNY", "owner": ["1000"], "amount": "1.00", "is_package": false, "last_modified_time": 1712634474531, "life_status": "normal", "enabled_status": true, "last_modified_by": ["1000"], "amount_editable": true, "mc_currency": "CNY", "record_type": "default__c", "parent_bom_id": "661270f21628d30007fb9447", "node_bom_core_type": "product", "max_amount": "5.0", "order_by": "10", "mc_exchange_rate_version": "1710333335138"), new ObjectData("lock_rule": "default_lock_rule", "field_4vPR0__c": "345.99", "price_editable": true, "mc_exchange_rate": "1.000000", "extend_obj_data_id": "661270f31628d30007fb956b", "amount_any": false, "field_e1c2f__c": "bx0001", "product_id": "646dac1f91772a000100929d", "owner_department_id": "1001", "owner_department": "研发中心", "searchAfterId": ["1712484598389", "661270f21628d30007fb9448"], "price_mode": "2", "lock_status": "0", "package": "CRM", "create_time": 1712484595736, "order_field": "1", "field_obVn8__c": "12345", "product_life_status": "normal", "version": "2", "created_by": ["1000"], "bom_path": "661270f21628d30007fb9447.661270f21628d30007fb9448", "data_own_department": ["1001"], "name": "20240407059809", "_id": "661270f21628d30007fb9448", "related_core_id": "64c0d78c95c5da0001eeadc2", "tenant_id": "82769", "field_7MMwO__c": "测试", "selected_by_default": true, "product_status": "1", "core_id__relation_ids": "661270f21628d30007fb9449", "product_id__relation_ids": "646dac1f91772a000100929d", "core_id": "661270f21628d30007fb9449", "is_deleted": false, "is_required": false, "object_describe_api_name": "BOMObj", "root_id": "661270f21628d30007fb9447", "mc_functional_currency": "CNY", "node_bom_core_version": "b20230726-v000034", "owner": ["1000"], "amount": "1.00", "is_package": false, "last_modified_time": 1712484598389, "life_status": "normal", "enabled_status": true, "last_modified_by": ["-10000"], "amount_editable": true, "mc_currency": "CNY", "record_type": "default__c", "parent_bom_id": "661270f21628d30007fb9447", "node_bom_core_type": "configure", "max_amount": "5.0", "order_by": "10", "mc_exchange_rate_version": "1710333335138"), new ObjectData("lock_rule": "default_lock_rule", "field_4vPR0__c": "345.99", "price_editable": false, "extend_obj_data_id": "6471b607a2eed70001123ba0", "product_id": "647066bd71e41c0001cc33fa", "searchAfterId": ["1690359693024", "6471b607a2eed70001123b7f"], "price_mode": "2", "lock_status": "0", "package": "CRM", "create_time": 1685173767929, "order_field": "1", "field_obVn8__c": "12345", "product_life_status": "normal", "version": "2", "created_by": ["1000"], "bom_path": "6471b607a2eed70001123b7e.6471b607a2eed70001123b7f", "name": "20230527050016", "_id": "6471b607a2eed70001123b7f", "tenant_id": "82769", "field_7MMwO__c": "测试", "selected_by_default": true, "product_status": "1", "core_id__relation_ids": "64c0d78c95c5da0001eeadc2", "product_id__relation_ids": "647066bd71e41c0001cc33fa", "core_id": "64c0d78c95c5da0001eeadc2", "is_deleted": false, "is_required": false, "object_describe_api_name": "BOMObj", "root_id": "6471b607a2eed70001123b7e", "amount": "1.00", "last_modified_time": 1690359693024, "life_status": "normal", "enabled_status": true, "last_modified_by": ["-10000"], "amount_editable": true, "record_type": "default__c", "parent_bom_id": "6471b607a2eed70001123b7e", "node_bom_core_type": "product", "max_amount": "5.0")]
    }

    def "test getAllParentProdList"() {
        given:
        QueryResult<IObjectData> queryResult = new QueryResult();
        queryResult.setData(dataList)
        PowerMockito.when(GrayUtil.bomMasterSlaveMode(anyString())).thenReturn(false)
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        PowerMockito.doReturn(dataList).when(serviceFacade, "findObjectDataByIds", any(), any(), any())
        def result = new GetAllParentProdListModel.Result()
        when:
        try {
            result = tester.getAllParentProdList(getServiceContext(user, "SFA", "query"), new GetAllParentProdListModel.Arg(productId))
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then:
        CollectionUtils.nullToEmpty(Optional.ofNullable(result).map({ x -> x.getParentProdIdList() }).orElse(Lists.newArrayList())).size() == re
        where:
        productId | dataList                                                             | re
        ""        | []                                                                   | 0
        "1"       | []                                                                   | 0
        "1"       | [new ObjectData("_id": "1", "product_id": "p1", "bom_path": "b.b1")] | 1
    }


    def "test checkSubBomAmount"() {
        given:
        when:
        Whitebox.invokeMethod(tester, "checkSubBomAmount", user, bomList, result, true)
        then:
        result.getBomResultList().size() == re
        where:
        bomList                                                                                                                                      | result                                  | re
        Lists.newArrayList()                                                                                                                         | new CheckProductBomModel.SingleResult() | 0
        [new ObjectData("amount": 2, "min_amount": 1, "max_amount": 1, "increment": 1, "product_id": "p1")]                                          | new CheckProductBomModel.SingleResult() | 1
        [new ObjectData("amount": 1, "min_amount": 2, "max_amount": 3, "increment": 1, "product_id": "p1")]                                          | new CheckProductBomModel.SingleResult() | 1
        [new ObjectData("amount": 3, "min_amount": 2, "max_amount": 3, "increment": 2, "product_id": "p1")]                                          | new CheckProductBomModel.SingleResult() | 1
        [new ObjectData("amount": 3, "amountDB": 5, "min_amount": 2, "max_amount": 3, "increment": 2, "product_id": "p1", "amount_editable": false)] | new CheckProductBomModel.SingleResult() | 1
    }

    def "test fillBomListByDB"() {
        given:
        PowerMockito.when(GrayUtil.bomMasterSlaveMode(anyString())).thenReturn(true)
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn([new ObjectData("_id": "p1", "name": "n1")]).when(serviceFacade, "findObjectDataByIdsIgnoreAll", any(), any(), any())
        when:
        Whitebox.invokeMethod(tester, "fillBomListByDB", "", result, bomList, dbBomList, true, idMap)
        then:
        result.getBomResultList().size() == re
        where:
        bomList                                                                                                                   | dbBomList                                                                   | result                                  | idMap           | re
        Lists.newArrayList()                                                                                                      | Lists.newArrayList()                                                        | new CheckProductBomModel.SingleResult() | idMapArg("id1") | 0
        [new ObjectData("_id": "id1", "product_id": "p1"), new ObjectData("_id": "id2", "product_id": "p1", "node_type": "temp")] | [new ObjectData("_id": "id1", "is_package": true, "related_core_id": "r1")] | new CheckProductBomModel.SingleResult() | idMapArg("id1") | 1
        [new ObjectData("_id": "id1", "product_id": "p1"), new ObjectData("_id": "id2", "product_id": "p2", "node_type": "temp")] | [new ObjectData("_id": "id1", "is_package": true, "related_core_id": "r1")] | new CheckProductBomModel.SingleResult() | idMapArg("id1") | 2
    }

    def "test checkSubBomIsRequired"() {
        given:
        when:
        Whitebox.invokeMethod(tester, "checkSubBomIsRequired", bomList, dbBomList, rootId, result, dbBomList)
        then:
        result.getBomResultList().size() == re
        where:
        bomList                                                                                                                   | dbBomList                                                                                                                                                                                      | result                                  | idMap           | re | rootId
        Lists.newArrayList()                                                                                                      | Lists.newArrayList()                                                                                                                                                                           | new CheckProductBomModel.SingleResult() | idMapArg("id1") | 0  | "r1"
        [new ObjectData("_id": "id1", "product_id": "p1"), new ObjectData("_id": "id2", "product_id": "p1", "node_type": "temp")] | [new ObjectData("_id": "id1", "is_package": true, "related_core_id": "r1")]                                                                                                                    | new CheckProductBomModel.SingleResult() | idMapArg("id1") | 0  | "r1"
        [new ObjectData("_id": "id3", "product_id": "p3"), new ObjectData("_id": "id2", "product_id": "p2", "node_type": "temp")] | [new ObjectData("_id": "id1", "is_required": true, "parent_bom_id": "id1", "product_id": "p3"), new ObjectData("_id": "id2", "is_required": true, "parent_bom_id": "id1", "product_id": "p3")] | new CheckProductBomModel.SingleResult() | idMapArg("id1") | 1  | "r1"
        [new ObjectData("_id": "id3", "product_id": "p3"), new ObjectData("_id": "id2", "product_id": "p2", "node_type": "temp")] | [new ObjectData("_id": "id1", "is_required": true, "parent_bom_id": "id1", "product_id": "p3"), new ObjectData("_id": "id2", "is_required": true, "parent_bom_id": "id1", "product_id": "p3")] | new CheckProductBomModel.SingleResult() | idMapArg("id1") | 1  | "id1"
    }

    def "test checkSubProductStatus"() {
        given:
        when:
        Whitebox.invokeMethod(tester, "checkSubProductStatus", bomList, result, rootId)
        then:
        result.getBomResultList().size() == re
        where:
        bomList                                                                                                                   | result                                  | re | rootId
        Lists.newArrayList()                                                                                                      | new CheckProductBomModel.SingleResult() | 0  | "r1"
        [new ObjectData("_id": "id1", "product_id": "p1"), new ObjectData("_id": "id2", "product_id": "p1", "node_type": "temp")] | new CheckProductBomModel.SingleResult() | 1  | "id1"
        [new ObjectData("_id": "id1", "product_id": "p1", "product_status": false)]                                               | new CheckProductBomModel.SingleResult() | 1  | "r1"
        [new ObjectData("_id": "id1", "product_id": "p1", "product_life_status": "p1")]                                           | new CheckProductBomModel.SingleResult() | 1  | "r1"
    }


    def "test checkBomTree"() {
        given:
        when:
        Whitebox.invokeMethod(tester, "checkBomTree", bomList, dbBomList, rootId, result)
        then:
        result.getBomResultList().size() == re
        where:
        bomList                                                                                                                   | dbBomList                                                                                                                                                                                                | result                                  | rootId | re
        Lists.newArrayList()                                                                                                      | Lists.newArrayList()                                                                                                                                                                                     | new CheckProductBomModel.SingleResult() | "r1"   | 0
        [new ObjectData("_id": "id1", "product_id": "p1"), new ObjectData("_id": "id2", "product_id": "p2")]                      | [new ObjectData("_id": "id2", "bom_path": "id1.id2", "is_package": true, "related_core_id": "r1")]                                                                                                       | new CheckProductBomModel.SingleResult() | "r1"   | 0
        [new ObjectData("_id": "id1", "product_id": "p1"), new ObjectData("_id": "id2", "product_id": "p2")]                      | [new ObjectData("_id": "id2", "bom_path": "id1.id2.id3", "is_package": true, "related_core_id": "r1"), new ObjectData("_id": "id3", "bom_path": "id1.id2", "is_package": true, "related_core_id": "r1")] | new CheckProductBomModel.SingleResult() | "r1"   | 1
        [new ObjectData("_id": "id1", "product_id": "p1"), new ObjectData("_id": "id2", "product_id": "p2", "node_type": "temp")] | [new ObjectData("_id": "id1", "is_package": true, "related_core_id": "r1")]                                                                                                                              | new CheckProductBomModel.SingleResult() | "r1"   | 0
    }


    def "test checkGroupRule"() {
        given:
        when:
        Whitebox.invokeMethod(tester, "checkGroupRule", groupMap, bomList, result)
        then:
        1 == 1
        where:
        groupMap          | bomList                                                                                                                                                                                | result                                  | re
        Maps.newHashMap() | Lists.newArrayList()                                                                                                                                                                   | new CheckProductBomModel.SingleResult() | 0
        groupMapArg("g1") | [new ObjectData("parent_prod_pkg_key": "p1", "product_id": "p1")]                                                                                                                      | new CheckProductBomModel.SingleResult() | 0
        groupMapArg("g1") | [new ObjectData("parent_prod_pkg_key": "p1", "product_group_id": "g2", "product_id": "p1")]                                                                                            | new CheckProductBomModel.SingleResult() | 1
        groupMapArg("g1") | [new ObjectData("parent_prod_pkg_key": "p1", "product_group_id": "g1", "product_id": "p1"), new ObjectData("parent_prod_pkg_key": "p1", "product_group_id": "g1", "product_id": "p1")] | new CheckProductBomModel.SingleResult() | 1

    }

    def "test checkGroupRequiredNew"() {
        given: "准备测试数据和模拟依赖"

        // 模拟 I18N.text 方法
        PowerMockito.when(I18N.text(anyString())).thenReturn("Error Message")
        PowerMockito.when(I18N.text(anyString(), anyString())).thenReturn("Error Message: %s")

        // 准备方法参数
        List<IObjectData> boms = Lists.newArrayList(
                new ObjectData(parent_bom_id: "rootBom1", _id: "bom1", root_id: "rootBom1", product_group_id: "reqGroup1", enabled_status: bomEnabled, product_status: bomStatus),
                new ObjectData(parent_bom_id: "rootBom1", _id: "bom1", root_id: "rootBom1", product_group_id: "reqGroup1", enabled_status: bomEnabled, product_status: bomStatus)
        )
        List<CheckProductBomModel.ProductBom> bomListParam = Lists.newArrayList(
                new CheckProductBomModel.ProductBom(rootBomId: "rootBom1", subBomList: ObjectDataDocument.ofList(subBomList))
        )
        Map<String, String> rootBomIdToProductNameMap = [rootBom1: "Root Product 1"]
        List<IObjectData> groupDataListParam = Lists.newArrayList(
                new ObjectData(_id: "reqGroup1", name: "Required Group 1", min_prod_count: 1, parent_bom_id: "bom1") // 假设分组需要至少1个产品
        )

        when: "调用 checkGroupRequiredNew 方法"

        try {
            Whitebox.invokeMethod(tester, "checkGroupRequiredNew", boms, bomListParam, rootBomIdToProductNameMap, groupDataListParam)
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then: "验证结果"
        1 == 1

        where:
        scenario                              | hasRequiredGroup | subBomList                                                       | bomEnabled | bomStatus | shouldThrowException
        "No required groups"                  | false            | [new ObjectData(_id: "subBom1", product_group_id: "otherGroup")] | true       | "1"       | false
        "Required group present"              | true             | [new ObjectData(_id: "subBom1", product_group_id: "reqGroup1")]  | true       | "1"       | false
        "Required group missing"              | true             | [new ObjectData(_id: "subBom1", product_group_id: "otherGroup")] | true       | "1"       | true // SFA_BOM_GROUP_REQUIRED_PRODUCT
        "Required group present but disabled" | true             | [new ObjectData(_id: "subBom1", product_group_id: "otherGroup")] | false      | "1"       | true // SFA_BOM_EMPTY_GROUP_REQUIRED_PRODUCT
        "Required group present but not ON"   | true             | [new ObjectData(_id: "subBom1", product_group_id: "otherGroup")] | true       | "2"       | true // SFA_BOM_EMPTY_GROUP_REQUIRED_PRODUCT
        "Empty SubBomList"                    | true             | []                                                               | true       | "1"       | true // SFA_BOM_GROUP_REQUIRED_PRODUCT
    }

    def "test checkGroupRequired"() {
        given: "准备测试数据和模拟依赖"

        // 模拟 I18N.text 方法
        PowerMockito.when(I18N.text(anyString())).thenReturn("Error Message")
        PowerMockito.when(I18N.text(anyString(), anyString())).thenReturn("Error Message: %s")

        // 准备方法参数
        List<IObjectData> boms = Lists.newArrayList(
                new ObjectData(parent_bom_id: "rootBom1", _id: "bom1", root_id: "rootBom1", product_group_id: "reqGroup1", enabled_status: bomEnabled, product_status: bomStatus),
                new ObjectData(parent_bom_id: "rootBom1", _id: "bom1", root_id: "rootBom1", product_group_id: "reqGroup1", enabled_status: bomEnabled, product_status: bomStatus)
        )
        List<CheckProductBomModel.ProductBom> bomListParam = Lists.newArrayList(
                new CheckProductBomModel.ProductBom(rootBomId: "rootBom1", subBomList: ObjectDataDocument.ofList(subBomList))
        )
        Map<String, String> rootBomIdToProductNameMap = [rootBom1: "Root Product 1"]
        List<IObjectData> groupDataListParam = Lists.newArrayList(
                new ObjectData(_id: "reqGroup1", name: "Required Group 1", min_prod_count: 1, parent_bom_id: "bom1") // 假设分组需要至少1个产品
        )

        when: "调用 checkGroupRequired 方法"

        try {
            Whitebox.invokeMethod(tester, "checkGroupRequired", boms, bomListParam, rootBomIdToProductNameMap, groupDataListParam)
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then: "验证结果"
        1 == 1

        where:
        scenario                              | hasRequiredGroup | subBomList                                                       | bomEnabled | bomStatus | shouldThrowException
        "No required groups"                  | false            | [new ObjectData(_id: "subBom1", product_group_id: "otherGroup")] | true       | "1"       | false
        "Required group present"              | true             | [new ObjectData(_id: "subBom1", product_group_id: "reqGroup1")]  | true       | "1"       | false
        "Required group missing"              | true             | [new ObjectData(_id: "subBom1", product_group_id: "otherGroup")] | true       | "1"       | true // SFA_BOM_GROUP_REQUIRED_PRODUCT
        "Required group present but disabled" | true             | [new ObjectData(_id: "subBom1", product_group_id: "otherGroup")] | false      | "1"       | true // SFA_BOM_EMPTY_GROUP_REQUIRED_PRODUCT
        "Required group present but not ON"   | true             | [new ObjectData(_id: "subBom1", product_group_id: "otherGroup")] | true       | "2"       | true // SFA_BOM_EMPTY_GROUP_REQUIRED_PRODUCT
        "Empty SubBomList"                    | true             | []                                                               | true       | "1"       | true // SFA_BOM_GROUP_REQUIRED_PRODUCT
    }

    def "test getRootBomByCoreId"() {
        given: "准备测试数据和模拟依赖"
        def bomCoreId = "test-core-id"
        def serviceFacade = PowerMockito.mock(ServiceFacade.class)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)

        // 模拟查询结果
        def queryResult = new QueryResult<IObjectData>()
        if (hasResult) {
            queryResult.setData([new ObjectData(_id: "bom1", core_id: bomCoreId, name: "Test Bom")])
        } else {
            queryResult.setData([])
        }

        // 模拟 serviceFacade.findBySearchQuery 方法
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as IActionContext, anyString(), any() as SearchTemplateQuery)).thenReturn(queryResult)

        when: "调用 getRootBomByCoreId 方法"
        def result = tester.getRootBomByCoreId(bomCoreId, user, needCalculate)

        then: "验证结果"
        if (hasResult) {
            result != null
        } else {
            result == null
        }

        where:
        scenario   | hasResult | needCalculate
        "有结果且需要计算" | true      | true
        "有结果不需要计算" | true      | false
        "没有结果"     | false     | true
    }


    def "test getBomListByCoreId"() {
        given: "准备测试数据和模拟依赖"
        def bomCoreId = "test-core-id"
        def serviceFacade = PowerMockito.mock(ServiceFacade.class)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)

        // 模拟查询结果
        def queryResult = new QueryResult<IObjectData>()
        if (hasResult) {
            queryResult.setData([new ObjectData(_id: "bom1", core_id: bomCoreId, name: "Test Bom")])
        } else {
            queryResult.setData([])
        }

        // 模拟 serviceFacade.findBySearchQuery 方法
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as IActionContext, anyString(), any() as SearchTemplateQuery)).thenReturn(queryResult)

        when: "调用 getBomListByCoreId 方法"
        def result = tester.getBomListByCoreId(bomCoreId, user, true, needCalculate)

        then: "验证结果"
        if (hasResult) {
            result.size() > 0
        } else {
            result.size() == 0
        }

        where:
        scenario   | hasResult | needCalculate
        "有结果且需要计算" | true      | true
        "有结果不需要计算" | true      | false
        "没有结果"     | false     | true
    }

    def "test getBomsByProductId"() {
        given: "准备测试数据和模拟依赖"
        def bomCoreId = "test-core-id"
        def serviceFacade = PowerMockito.mock(ServiceFacade.class)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)

        // 模拟查询结果
        def queryResult = new QueryResult<IObjectData>()
        if (hasResult) {
            queryResult.setData([new ObjectData(_id: "bom1", core_id: bomCoreId, name: "Test Bom")])
        } else {
            queryResult.setData([])
        }

        // 模拟 serviceFacade.findBySearchQuery 方法
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as IActionContext, anyString(), any() as SearchTemplateQuery)).thenReturn(queryResult)

        when: "调用 getBomsByProductId 方法"
        def result = tester.getBomsByProductId("", user)

        then: "验证结果"
        if (hasResult) {
            result.size() > 0
        } else {
            result.size() == 0
        }

        where:
        scenario | hasResult
        "有结果"    | true
        "没有结果"   | false
    }

    def idMapArg(String id) {
        def map = Maps.newHashMap()
        map.put(id, new ObjectData("_id": id))
        map

    }

    def groupMapArg(String id) {
        def map = Maps.newHashMap()
        map.put(id, new ObjectData("_id": id, "group_options_control": true, "min_prod_count": 3, "max_prod_count": 1))
        map

    }

    def getArg(String str) {
        List<CheckProductBomModel.ProductBom> array = Lists.newArrayList()
        if (StringUtils.isNotBlank(str)) {
            array = JSON.parseArray(str, CheckProductBomModel.ProductBom.class)
        }
        array
    }

    def "test checkGroupCountNew"() {
        given: "准备测试数据和模拟依赖"
        // 模拟 I18N.text 方法
        PowerMockito.when(I18N.text(anyString(), anyString())).thenReturn("Group Count Error: %s")

        // 准备方法参数
        List<CheckProductBomModel.ProductBom> bomListParam = Lists.newArrayList(
                new CheckProductBomModel.ProductBom(
                        rootBomId: "rootBom1",
                        rootNodeAmount: rootAmount,
                        subBomList: ObjectDataDocument.ofList(subBomList)
                )
        )
        Map<String, IObjectData> groupMapParam = [
                (groupId): new ObjectData(_id: groupId, name: "Test Group", min_amount_count: minCount, max_amount_count: maxCount)
        ]
        Map<String, IObjectData> dbMapParam = subBomList.stream().collect(Collectors.toMap({ it.getId() }, { it }))

        when: "调用 checkGroupCountNew 方法"
        try {
            tester.checkGroupCountNew(bomListParam, groupMapParam, dbMapParam)
        } catch (Exception e) {
            assert e instanceof ValidateException
        }

        then: "验证结果"
        1 == 1

        where:
        rootAmount | groupId | subBomList                                                                                                                                                          | minCount | maxCount | shouldThrowException | exception
        1          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 3, bom_path: "rootBom1.bom1")]                                                                         | 2        | 4        | false                | null
        1          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 1, bom_path: "rootBom1.bom1")]                                                                         | 2        | 4        | true                 | null
        1          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 5, bom_path: "rootBom1.bom1")]                                                                         | 2        | 4        | true                 | null
        2          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 3, bom_path: "rootBom1.bom1")]                                                                         | 2        | 4        | true                 | null
        2          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 9, bom_path: "rootBom1.bom1")]                                                                         | 2        | 4        | true                 | null
        2          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 5, bom_path: "rootBom1.bom1")]                                                                         | 2        | 4        | false                | null
        1          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 1, bom_path: "rootBom1.bom1"), new ObjectData(_id: "bom2", amount: 2, bom_path: "rootBom1.bom1.bom2")] | 2        | 4        | false                | null // 1 (bom1 in group) + 2 (bom2 under bom1) = 3
        1          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 1, bom_path: "rootBom1.bom1"), new ObjectData(_id: "bom2", amount: 0, bom_path: "rootBom1.bom1.bom2")] | 2        | 4        | true                 | null // 1 + 0 = 1 < 2
        1          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 1, bom_path: "rootBom1.bom1"), new ObjectData(_id: "bom2", amount: 4, bom_path: "rootBom1.bom1.bom2")] | 2        | 4        | true                 | null // 1 + 4 = 5 > 4
        1          | "g2"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 1, bom_path: "rootBom1.bom1")]                                                                         | null     | null     | false                | null
        1          | "g1"    | []                                                                                                                                                                  | 2        | 4        | false                | null // Method returns early
    }

    def "test checkGroupCount"() {
        given: "准备测试数据和模拟依赖"
        // 模拟 I18N.text 方法
        PowerMockito.when(I18N.text(anyString(), anyString())).thenReturn("Group Count Error: %s")

        // 准备方法参数
        List<CheckProductBomModel.ProductBom> bomListParam = Lists.newArrayList(
                new CheckProductBomModel.ProductBom(
                        rootBomId: "rootBom1",
                        rootNodeAmount: rootAmount,
                        subBomList: ObjectDataDocument.ofList(subBomList)
                )
        )
        Map<String, IObjectData> groupMapParam = [
                (groupId): new ObjectData(_id: groupId, name: "Test Group", min_amount_count: minCount, max_amount_count: maxCount)
        ]
        Map<String, IObjectData> dbMapParam = subBomList.stream().collect(Collectors.toMap({ it.getId() }, { it }))

        when: "调用 checkGroupCount 方法"
        try {
            tester.checkGroupCount(bomListParam, groupMapParam, dbMapParam)
        } catch (Exception e) {
            assert e instanceof ValidateException
        }

        then: "验证结果"
        1 == 1

        where:
        rootAmount | groupId | subBomList                                                                                                                                                          | minCount | maxCount | shouldThrowException | exception
        1          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 3, bom_path: "rootBom1.bom1")]                                                                         | 2        | 4        | false                | null
        1          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 1, bom_path: "rootBom1.bom1")]                                                                         | 2        | 4        | true                 | null
        1          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 5, bom_path: "rootBom1.bom1")]                                                                         | 2        | 4        | true                 | null
        2          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 3, bom_path: "rootBom1.bom1")]                                                                         | 2        | 4        | true                 | null
        2          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 9, bom_path: "rootBom1.bom1")]                                                                         | 2        | 4        | true                 | null
        2          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 5, bom_path: "rootBom1.bom1")]                                                                         | 2        | 4        | false                | null
        1          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 1, bom_path: "rootBom1.bom1"), new ObjectData(_id: "bom2", amount: 2, bom_path: "rootBom1.bom1.bom2")] | 2        | 4        | false                | null // 1 (bom1 in group) + 2 (bom2 under bom1) = 3
        1          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 1, bom_path: "rootBom1.bom1"), new ObjectData(_id: "bom2", amount: 0, bom_path: "rootBom1.bom1.bom2")] | 2        | 4        | true                 | null // 1 + 0 = 1 < 2
        1          | "g1"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 1, bom_path: "rootBom1.bom1"), new ObjectData(_id: "bom2", amount: 4, bom_path: "rootBom1.bom1.bom2")] | 2        | 4        | true                 | null // 1 + 4 = 5 > 4
        1          | "g2"    | [new ObjectData(_id: "bom1", product_group_id: "g1", amount: 1, bom_path: "rootBom1.bom1")]                                                                         | null     | null     | false                | null
        1          | "g1"    | []                                                                                                                                                                  | 2        | 4        | false                | null // Method returns early
    }

    def "test checkSubProducts"() {
        given: "准备测试数据"
        def objectDataDocument = new ObjectDataDocument()
        objectDataDocument.put("max_amount", maxAmount)
        objectDataDocument.put("min_amount", minAmount)
        objectDataDocument.put("increment", increment)
        objectDataDocument.put("amount", amount)
        objectDataDocument.put("is_required", isRequired)
        objectDataDocument.put("selected_by_default", selectedByDefault)
        when: "调用 checkSubProducts 方法"
        try {
            tester.checkSubProducts(objectDataDocument)
        } catch (ValidateException e) {
            assert e instanceof ValidateException
        }

        then: "验证结果"
        print(scenario)
        1 == 1

        where: "测试数据"
        maxAmount | minAmount | increment | amount | isRequired | selectedByDefault | expectedResult | scenario
        10.0      | 1.0       | 1.0       | 5.0    | false      | false             | true           | "正常情况 - 所有参数都在合法范围内"
        10.0      | 1.0       | -1.0      | 5.0    | false      | false             | false          | "非法情况 - increment小于0"
        10.0      | 1.0       | 1.0       | -1.0   | false      | false             | false          | "非法情况 - amount小于0"
        -1.0      | 1.0       | 1.0       | 5.0    | false      | false             | false          | "非法情况 - maxAmount小于0"
        0.0       | 1.0       | 1.0       | 5.0    | true       | false             | false          | "非法情况 - isRequired为true但maxAmount为0"
        10.0      | 1.0       | 1.0       | 5.0    | true       | false             | false          | "非法情况 - isRequired为true但selectedByDefault为false"
        10.0      | 5.0       | 1.0       | 3.0    | false      | false             | false          | "非法情况 - amount小于minAmount"
        10.0      | 5.0       | 1.0       | 15.0   | false      | false             | false          | "非法情况 - amount大于maxAmount"
        10.0      | 1.0       | 2.0       | 3.0    | false      | false             | false          | "非法情况 - amount不能被increment整除"
        10.0      | 1.0       | 2.0       | 4.0    | false      | false             | true           | "正常情况 - amount能被increment整除"
        null      | null      | null      | null   | null       | null              | true           | "正常情况 - 所有参数都为null，使用默认值"
        10.0      | ""        | 1.0       | 5.0    | false      | false             | false          | "正常情况 - minAmount为空字符串"
        10.0      | null      | 1.0       | 5.0    | false      | false             | false          | "正常情况 - minAmount为null"
    }

    def "test buildCoreArg"() {
        given: "准备测试数据和mock"
        def serviceFacade = PowerMockito.mock(ServiceFacade.class)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.when(serviceFacade.generateId()).thenReturn("mockId")
        PowerMockito.when(I18N.text(anyString())).thenReturn("mock text")
        def queryResult = new QueryResult();
        queryResult.setData([
                new ObjectData(_id: "bom1", product_id: "prod1"),
                new ObjectData(_id: "bom2", product_id: "prod2")
        ])
        PowerMockito.when(serviceFacade.findBySearchQueryIgnoreAll(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        def arg = new CreateBomModel.Arg()
        arg.setAutoCreate(isAutoCreate)
        arg.setBomList(bomList)

        when: "调用 buildCoreArg 方法"
        try {
            def result = tester.buildCoreArg(user, arg)
            assert result != null
            if (isSuccess) {
                assert result.objectData != null
                assert result.details != null
                // 验证核心字段
                assert result.objectData.get("category") == "standard"
                assert result.objectData.get("purpose") == "sale"
            }
        } catch (ValidateException e) {
            assert e instanceof ValidateException
        }

        then: "验证结果"
        print(scenario)
        1 == 1

        where: "测试数据"
        scenario                     | isSuccess | isAutoCreate | bomList
        "正常情况 - 手动创建"                | true      | false        | [
                new CreateBomModel.CreateBomInfo(
                        root: true,
                        bomId: "bom1",
                        productId: "prod1",
                        nodeType: "standard",
                        parentProdPkgKey: "123456",
                        productGroupId: "11111"

                ),
                new CreateBomModel.CreateBomInfo(
                        root: false,
                        bomId: "bom2",
                        productId: "prod2",
                        nodeType: "standard"
                )
        ]
        "正常情况 - 自动创建"                | true      | true         | [
                new CreateBomModel.CreateBomInfo(
                        root: true,
                        bomId: "bom1",
                        productId: "prod1",
                        nodeType: "standard"
                ),
                new CreateBomModel.CreateBomInfo(
                        root: false,
                        bomId: "bom2",
                        productId: "prod2",
                        nodeType: "standard"
                )
        ]
        "异常情况 - bomList为空"           | false     | false        | []
        "异常情况 - bomList为null"        | false     | false        | null
        "异常情况 - 没有根节点"               | false     | false        | [
                new CreateBomModel.CreateBomInfo(
                        root: false,
                        bomId: "bom1",
                        productId: "prod1",
                        nodeType: "standard"
                )
        ]
        "异常情况 - 所有节点都没有bomId且不是临时节点" | false     | false        | [
                new CreateBomModel.CreateBomInfo(
                        root: true,
                        productId: "prod1",
                        nodeType: "standard"
                )
        ]
        "正常情况 - 包含临时节点"              | true      | false        | [
                new CreateBomModel.CreateBomInfo(
                        root: true,
                        bomId: "bom1",
                        productId: "prod1",
                        nodeType: "standard"
                ),
                new CreateBomModel.CreateBomInfo(
                        root: false,
                        productId: "prod2",
                        nodeType: "temp"
                )
        ]
    }

    def "test checkDuplicateBom"() {
        given: "准备测试数据和mock"
        PowerMockito.when(bizConfigThreadLocalCacheService.isOpenBomDuplicateCheck(anyString())).thenReturn(isOpenCheck)
        PowerMockito.when(bizConfigThreadLocalCacheService.isGenerateStandardBomNone(anyString())).thenReturn(isGenerateNone)
        PowerMockito.when(I18N.text(anyString())).thenReturn("mock text")
        def serviceFacade = PowerMockito.mock(ServiceFacade.class)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        if (hasDuplicates) {
            PowerMockito.when(objectDataService.findBySql(anyString(), anyString())).thenReturn([
                    [core_id: "core1", product_ids: ["prod1-1", "prod2-1"] as String[]]
            ])
            PowerMockito.when(serviceFacade.findObjectDataByIdsIgnoreAll(anyString(), anyList(), anyString())).thenReturn([
                    new ObjectData(_id: "bom1", product_id: "prod1"),
                    new ObjectData(_id: "bom2", product_id: "prod2")
            ])
        } else {
            PowerMockito.when(objectDataService.findBySql(anyString(), anyString())).thenReturn([])
        }

        def queryResult = new QueryResult();
        queryResult.setData([
                new ObjectData(_id: "bom1", product_id: "prod1"),
                new ObjectData(_id: "bom2", product_id: "prod2")
        ])

        PowerMockito.when(serviceFacade.findBySearchQuery(any() as IActionContext, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)

        def arg = new CreateBomModel.Arg()
        arg.setMasterApiName("testApi")
        arg.setMasterDataId("testId")
        arg.setBomList(bomList)

        when: "调用 checkDuplicateBom 方法"
        try {
            tester.checkDuplicateBom(user, arg)
            assert !shouldThrowException
        } catch (ValidateException e) {
            assert shouldThrowException
            assert e instanceof ValidateException
        } catch (MetadataServiceException e) {
            assert shouldThrowException
            assert e instanceof MetadataServiceException
        }

        then: "验证结果"
        print(scenario)
        1 == 1

        where: "测试数据"
        scenario         | isOpenCheck | isGenerateNone | bomList | hasDuplicates | shouldThrowException
        "不开启查重"          | false       | false          | [
                new CreateBomModel.CreateBomInfo(
                        root: true,
                        productId: "prod1",
                        quantity: BigDecimal.ONE
                )
        ]                                                         | false         | false
        "开启查重但生成空BOM"    | true        | true           | [
                new CreateBomModel.CreateBomInfo(
                        root: true,
                        productId: "prod1",
                        quantity: BigDecimal.ONE
                )
        ]                                                         | false         | false
        "开启查重但BomList为空" | true        | false          | []      | false         | false
        "开启查重但没有根节点"     | true        | false          | [
                new CreateBomModel.CreateBomInfo(
                        root: false,
                        productId: "prod1",
                        quantity: BigDecimal.ONE
                )
        ]                                                         | false         | true
        "开启查重且有重复BOM"    | true        | false          | [
                new CreateBomModel.CreateBomInfo(
                        root: true,
                        productId: "prod1",
                        quantity: BigDecimal.ONE
                ),
                new CreateBomModel.CreateBomInfo(
                        root: false,
                        productId: "prod2",
                        quantity: BigDecimal.ONE
                )
        ]                                                         | true          | true
        "开启查重但无重复BOM"    | true        | false          | [
                new CreateBomModel.CreateBomInfo(
                        root: true,
                        productId: "prod1",
                        quantity: BigDecimal.ONE
                ),
                new CreateBomModel.CreateBomInfo(
                        root: false,
                        productId: "prod2",
                        quantity: BigDecimal.ONE
                )
        ]                                                         | false         | false
    }

    def "test queryAllSubBomCore"() {
        given: "准备测试数据和mock"
        def serviceFacade = PowerMockito.mock(ServiceFacade.class)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)

        // Mock getBomListByCoreId的返回结果
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as IActionContext, anyString(), any() as SearchTemplateQuery))
                .thenReturn(new QueryResult(data: bomList))

        // Mock findObjectDataByIdsExcludeInvalid的返回结果
        PowerMockito.when(serviceFacade.findObjectDataByIdsExcludeInvalid(any() as IActionContext, any() as List<String>, anyString()))
                .thenReturn(validCoreList)

        // Mock I18N.text
        PowerMockito.when(I18N.text(anyString())).thenReturn("BOM层级不能超过10层")

        def arg = new QueryAllSubBomModel.Arg(
                bomCoreId: bomCoreId,
                newBomPath: newBomPath
        )

        when: "调用queryAllSubBomCore方法"
        def result = QueryAllSubBomModel.Result.builder().allSubBomList(Lists.newArrayList()).build()
        try {
            result = tester.queryAllSubBomCore(user, arg)
        } catch (ValidateException e) {
            assert e.getMessage() == "BOM层级不能超过10层"
        }
        then: "验证结果"
        if (expectedSize > 0) {
            // 验证返回列表中的数据
            result.getAllSubBomList().each { subBomInfo ->
                assert subBomInfo.getBomCoreId() != null
                assert subBomInfo.getNewBomId() != null
                assert subBomInfo.getNewBomPath() != null
            }
        }

        where: "测试数据"
        scenario                     | bomCoreId | newBomPath | bomList | validCoreList | shouldThrowException | expectedSize
        "正常场景 - 有效的BOM核心ID，包含多层子BOM" | "core1"   | "path1"    | [
                new ObjectData(_id: "bom1", related_core_id: "core2", bom_path: "path1.bom1"),
                new ObjectData(_id: "bom2", related_core_id: "core3", bom_path: "path1.bom2")
        ]                                                               | [
                new ObjectData(_id: "core2"),
                new ObjectData(_id: "core3")
        ]                                                                               | false                | 2

        "正常场景 - 有效的BOM核心ID，没有子BOM"   | "core1"   | "path1"    | []      | []            | false                | 0

        "异常场景 - BOM层级超过10层"          | "core1"   | "path1"    | [
                new ObjectData(_id: "bom1", related_core_id: "core2", bom_path: "path1.bom1"),
                new ObjectData(_id: "bom2", related_core_id: "core3", bom_path: "path1.bom2")
        ] * 11                                                          | [
                new ObjectData(_id: "core2"),
                new ObjectData(_id: "core3")
        ]                                                                               | true                 | 0

        "正常场景 - 子BOM已删除或作废"          | "core1"   | "path1"    | [
                new ObjectData(_id: "bom1", related_core_id: "core2", bom_path: "path1.bom1"),
                new ObjectData(_id: "bom2", related_core_id: "core3", bom_path: "path1.bom2")
        ]                                                               | []            | false                | 0

        "正常场景 - 复杂的BOM树结构"           | "core1"   | "path1"    | [
                new ObjectData(_id: "bom1", related_core_id: "core2", bom_path: "path1.bom1"),
                new ObjectData(_id: "bom2", related_core_id: "core3", bom_path: "path1.bom1.bom2"),
                new ObjectData(_id: "bom3", related_core_id: "core4", bom_path: "path1.bom1.bom2.bom3")
        ]                                                               | [
                new ObjectData(_id: "core2"),
                new ObjectData(_id: "core3"),
                new ObjectData(_id: "core4")
        ]                                                                               | false                | 3
    }

}
