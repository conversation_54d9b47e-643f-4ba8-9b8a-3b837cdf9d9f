package com.facishare.crm.sfa

import com.facishare.crm.describebuilder.ObjectDescribeBuilder
import com.facishare.crm.describebuilder.TextFieldDescribeBuilder
import com.facishare.crm.sfa.lto.utils.SearchUtil
import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.license.Result.ModuleInfoResult
import com.facishare.paas.license.pojo.ModuleInfoPojo
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.search.IFilter
import com.facishare.paas.metadata.api.search.Wheres
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.impl.search.Where
import com.facishare.paas.metadata.util.SpringContextUtil
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.github.autoconf.ConfigFactory
import com.github.autoconf.api.IChangeListener
import com.github.autoconf.api.IChangeable
import com.github.autoconf.api.IChangeableConfig
import com.github.autoconf.api.IConfigFactory
import com.github.autoconf.base.ProcessInfo
import com.github.autoconf.helper.ConfigHelper
import com.google.common.collect.Lists
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.MockRepository
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationContext
import spock.lang.Specification

import static org.powermock.reflect.Whitebox.setInternalState

@PrepareForTest([I18N.class, ConfigFactory.class, ConfigHelper.class])
@SuppressStaticInitializationFor([
        "com.facishare.paas.I18N"
])
abstract class EnhancedBaseGroovyTest extends Specification {
    def cleanupSpec() {
//        println "start releasing memory..."

        MockRepository.clear()
        MockRepository.suppressStaticInitializers.clear()
    }

    def setup() {
        removeConfigFactory()
        removeI18N()
    }

    def removeI18N() {
        long startTime = System.currentTimeMillis()
        setInternalState(I18N, "THREAD_LOCAL", new InheritableThreadLocal<>())
        setInternalState(I18N, "log", LoggerFactory.getLogger("test"))
        setInternalState(I18N, "I18N_RESOURCES", Collections.emptyList())
        // println("EnhancedBaseGroovyTest removeI18N costTime: " + (System.currentTimeMillis() - startTime))
    }

    def removeConfigFactory() {
        long startTime = System.currentTimeMillis()
        PowerMockito.stub(PowerMockito.method(ConfigHelper.class, "getProcessInfo"))
                .toReturn(new ProcessInfo())
        // println("#1 EnhancedBaseGroovyTest ConfigHelper costTime: " + (System.currentTimeMillis() - startTime))
        //
        startTime = System.currentTimeMillis()
        PowerMockito.stub(PowerMockito.method(ConfigFactory.class, "getInstance"))
                .toReturn(new MyConfigFactory())
        // println("#2 EnhancedBaseGroovyTest ConfigFactory costTime: " + (System.currentTimeMillis() - startTime))
        //
        startTime = System.currentTimeMillis()
        PowerMockito.stub(PowerMockito.method(IConfigFactory.class, "getConfig", String.class, IChangeListener.class))
                .toReturn(new MyChangeableConfig())
        // println("#3 EnhancedBaseGroovyTest ConfigFactory costTime: " + (System.currentTimeMillis() - startTime))
    }

    class MyChangeableConfig implements IChangeableConfig {

        @Override
        IChangeable getEventBus() {
            return null
        }

        @Override
        String getName() {
            return null
        }

        @Override
        byte[] getContent() {
            return null
        }
    }

    class MyConfigFactory implements IConfigFactory {

        @Override
        List<IChangeableConfig> getAllConfig() {
            return null
        }

        @Override
        IChangeableConfig getConfig(String name) {
            return null
        }

        @Override
        IChangeableConfig getConfig(String name, IChangeListener listener) {
            return null
        }

        @Override
        IChangeableConfig getConfig(String name, IChangeListener listener, boolean loadAfterRegister) {
            return null
        }

        @Override
        IChangeableConfig getConfig(String name, String sectionNames, IChangeListener listener, boolean loadAfterRegister) {
            return null
        }

        @Override
        boolean hasConfig(String name) {
            return false
        }

        @Override
        IChangeableConfig getConfigNotCreate(String name) {
            return null
        }

        @Override
        IChangeableConfig addConfig(IChangeableConfig config) {
            return null
        }
    }

//    class MyUser extends User {
//
//    }

    protected initI18nClient() {
        def field = I18nClient.class.getDeclaredField("impl")
        field.setAccessible(true)

        def stub = Stub(I18nServiceImpl)
        field.set(I18nClient.getInstance(), stub)
        stub.getOrDefault(_ as String, _ as Long, _ as String, _ as String) >> {
            String key, Long tenantId, String lang, String defaultVal ->
                return defaultVal
        }
    }

    protected ServiceContext getServiceContext(RequestContext requestContext, String serviceName, String serviceMethod) {
        return new ServiceContext(requestContext, serviceName, serviceMethod)
    }

    protected ServiceContext getServiceContext(String serviceName, String serviceMethod, boolean isOutUser) {
        ServiceContext serviceContext = new ServiceContext(RequestContext.builder().tenantId(this.user.getTenantId())
                .user(new User(this.user.getTenantId(), this.user.getUserId(), "333333", "23212112")).appId("crm").build(), serviceName, serviceMethod);
        return serviceContext
    }

    void initSpringContext() {
        def stubApplicationContext = Stub(ApplicationContext)
        def util = new SpringContextUtil()
        util.setApplicationContext(stubApplicationContext)
        Map<Class, Object> map = new HashMap<>()
        stubApplicationContext.getBean(_ as Class) >> { Class requiredType ->
            map.computeIfAbsent(requiredType, { key ->
                Stub(requiredType)
            })
        }

        Map<String, Object> map2 = new HashMap<>()
        stubApplicationContext.getBean(_ as String, _ as Class) >> { String name, Class requiredType ->
            map2.computeIfAbsent(name, { key ->
                Stub(requiredType)
            })
        }
    }

    void initSpringContextSpecialCrmNotificationService() {
        def stubApplicationContext = Stub(ApplicationContext)
        def util = new SpringContextUtil()
        util.setApplicationContext(stubApplicationContext)
        Map<Class, Object> map = new HashMap<>()
        stubApplicationContext.getBean(_ as Class) >> { Class requiredType ->
            map.computeIfAbsent(requiredType, { key ->
                Stub(requiredType)
            })
        }

        Map<String, Object> map2 = new HashMap<>()
        stubApplicationContext.getBean(_ as String, _ as Class) >> { String name, Class requiredType ->
            map2.computeIfAbsent(name, { key ->
                Stub(requiredType)
            })
        }
        stubApplicationContext.getBean(_ as String) >> { String name ->
            if ("crmNotificationService" == name) {
                Mock(CRMNotificationServiceImpl)
            } else {
                null
            }
        }
    }

    ActionContext createActionContext(Map params = [:]) {
        def actionContext = Mock(ActionContext)
        actionContext.requestContext >> (params.requestContext ?: createRequestContext(params))
        actionContext.getTenantId() >> (params.tenantId ?: "1")
        actionContext.getUser() >> (params.user ?: createUser(params))
        actionContext.getAppId() >> (params.appId ?: "1")
        actionContext.isFromOpenAPI() >> (params.isFromOpenAPI ?: false)
        actionContext.isFromSmartForm() >> (params.isFromSmartForm ?: false)
        actionContext.getObjectApiName() >> (params.objectApiName ?: "1")
        return actionContext
    }

    ServiceContext createServiceContext(Map params = [:]) {
        def serviceContext = Mock(ServiceContext)
        serviceContext.requestContext >> (params.requestContext ?: createRequestContext(params))
        serviceContext.getTenantId() >> (params.tenantId ?: "1")
        serviceContext.getUser() >> (params.user ?: createUser(params))
        serviceContext.getAppId() >> (params.appId ?: "1")
        return serviceContext
    }

    ControllerContext createControllerContext(Map params = [:]) {
        def controllerContext = Mock(ControllerContext)
        controllerContext.requestContext >> (params.requestContext ?: createRequestContext(params))
        controllerContext.getTenantId() >> (params.tenantId ?: "1")
        controllerContext.getUser() >> (params.user ?: createUser(params))
        controllerContext.getAppId() >> (params.appId ?: "1")
        return controllerContext
    }

    RequestContext createRequestContext(Map params = [:]) {
        def requestContext = Mock(RequestContext)
        requestContext.getTenantId() >> (params.tenantId ?: "1")
        requestContext.getUser() >> (params.user ?: createUser(params))
        requestContext.getAppId() >> (params.appId ?: "1")
        return requestContext
    }

    User createUser(Map params = [:]) {
        def user = new User("", "");
        user.getTenantId() >> (params.tenantId ?: "1")
        user.getUserId() >> (params.userId ?: "1000")
        user.getUpstreamOwnerIdOrUserId() >> (params.userId ?: "1000")
        return user
    }


    def getDescribe(String apiName) {
        ObjectDescribeBuilder.builder()
                .apiName(apiName)
                .displayName(apiName)
                .build()
    }

    def getDescribeWithDefaultFields(String apiName, List<String> defaultFields) {
        def describe = ObjectDescribeBuilder.builder()
                .apiName(apiName)
                .build()
        defaultFields.each {
            IFieldDescribe fieldDescribe = TextFieldDescribeBuilder.builder().apiName(it).label(it).build()
            describe.addFieldDescribe(fieldDescribe)
        }
        describe
    }

    def getDescribeWithDefaultFields(String apiName, String defaultFields) {
        def describe = ObjectDescribeBuilder.builder()
                .apiName(apiName)
                .build()
        MasterDetailFieldDescribe masterDetailFieldDescribe = new MasterDetailFieldDescribe();
        masterDetailFieldDescribe.setApiName(defaultFields)
        masterDetailFieldDescribe.setActive(true)
        masterDetailFieldDescribe.setIsExtend(false)
        masterDetailFieldDescribe.setFieldNum(null)
        masterDetailFieldDescribe.setStatus("released")
        masterDetailFieldDescribe.setDefineType("package")
        describe.addFieldDescribe(masterDetailFieldDescribe)
        describe
    }

    def getObjectData(List<String> fieldList) {
        def objectData = new ObjectData();
        fieldList.each {
            objectData.set(it, "xxxx")
        }
        objectData
    }

    def getObjectData(List<String> fieldList, List<Object> valueList) {
        IObjectData objectData = new ObjectData();
        fieldList.eachWithIndex { item, index ->
            objectData.set(item, valueList[index])
        }
        objectData
    }

    def getObjectDataList(List<String> fieldList, int size) {
        def objectDataList = new ArrayList<ObjectData>();
        for (i in 0..<size) {
            def objectData = new ObjectData();
            fieldList.each {
                objectData.set(it, "xxxx")
            }
            objectData.setDeleted(false)
            objectDataList.add(objectData)
        }
        objectDataList
    }

    def getObjectDataListDifferentValue(List<String> fieldList, int size) {
        def objectDataList = new ArrayList<ObjectData>();
        for (i in 0..<size) {
            def objectData = new ObjectData();
            fieldList.each {
                objectData.set(it, "xxxx" + i)
            }
            objectDataList.add(objectData)
        }
        objectDataList
    }

    def searchQueryAddFieldFilter(List<String> fields, List<Object> values) {
        def query = buildSearchQuery()
        fields.eachWithIndex { String f, int i ->
            List<IFilter> list = Lists.newArrayList();
            SearchUtil.fillFilterEq(list, f, values[i]);
            query.getFilters().addAll(list)
        }
        query
    }

    def buildSearchQueryNoFilter() {
        SearchTemplateQuery query = new SearchTemplateQuery()
        query.setNeedReturnCountNum(false)
        query.setPermissionType(0)
        query.setNeedReturnQuote(false)
        query.setOffset(0)
        query.setLimit(1000)
        query
    }

    def buildSearchQuery() {
        def query = buildSearchQueryNoFilter()
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "is_deleted", true);
        query.setFilters(filters)

        List<IFilter> filters2 = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters2, "biz_status", "normal");
        query.setFilters(filters2)
        Wheres wheres1 = new Wheres();
        wheres1.setConnector(Where.CONN.OR.toString());
        wheres1.setFilters(filters2);
        query.setWheres([wheres1]);
        query
    }

    def buildQueryResult() {
        QueryResult queryResult = new QueryResult();
        queryResult.setData(getObjectDataList(["account_id", "_id"], 3));
        queryResult
    }

    QueryResult<IObjectData> getQueryResult() {
        QueryResult<IObjectData> result = new QueryResult<>();
        result.setData(getObjectDataList(Lists.newArrayList("name"), 2))
        return result;
    }

    class OK extends RuntimeException {

    }


    ModuleInfoResult getModuleInfoResult(String str) {
        ModuleInfoResult moduleInfoResult = new ModuleInfoResult();
        List<ModuleInfoPojo> result = new ArrayList<>()
        ModuleInfoPojo moduleInfoPojo = new ModuleInfoPojo();
        moduleInfoPojo.setModuleCode(str)
        result.add(moduleInfoPojo)
        moduleInfoResult.setResult(result)
        return moduleInfoResult;
    }

    ServiceContext getServiceContext(User user, String serviceName, String serviceMethod) {
        RequestContext.RequestContextBuilder requestContextBuilder = RequestContext.builder()
        requestContextBuilder.user(user)
        requestContextBuilder.tenantId(user.getTenantId())
        return new ServiceContext(requestContextBuilder.build(), serviceName, serviceMethod)
    }

    ServiceContext getServiceContext(User user, String serviceName, String serviceMethod, boolean isOutUser) {
        RequestContext.RequestContextBuilder requestContextBuilder = RequestContext.builder()
        requestContextBuilder.user(new User(user.getTenantId(), user.getUserId(), "333333", "23212112"))
        requestContextBuilder.tenantId(user.getTenantId())
        return new ServiceContext(requestContextBuilder.build(), serviceName, serviceMethod)
    }

    QueryResult<IObjectData> getQueryResultA(List<IObjectData> list) {
        QueryResult<IObjectData> result = new QueryResult<>();
        result.setData(list)
        return result;
    }

}
