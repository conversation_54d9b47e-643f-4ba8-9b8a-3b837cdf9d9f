package com.facishare.crm.sfa.predefine.controller


import com.facishare.crm.sfa.RemoveUseless
import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils
import com.facishare.crm.sfa.utilities.util.ValidDateUtils
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.controller.BaseListController
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.util.SpringContextUtil
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.api.support.membermodification.MemberMatcher
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyBoolean

/**
 * <AUTHOR>
 * @since 2024/9/9
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([I18N, ValidDateUtils.class, PriceBookRelatedListController.class, PreDefineController.class, BaseListController.class])
@SuppressStaticInitializationFor(["com.facishare.paas.I18N", "com.facishare.paas.appframework.core.model.PreDefineController", "com.facishare.paas.appframework.core.predef.controller.StandardController", "com.facishare.paas.appframework.core.predef.controller.BaseListController"])
class PriceBookRelatedListControllerTest extends RemoveUseless {

    @Shared
    protected ControllerContext controllerContext

    def tenantId = '213'

    def setupSpec() {
        removeConfigFactory()
        removeI18N()
        initSpringContext()
        PowerMockito.mockStatic(ValidDateUtils.class)
    }

    def "doFunPrivilegeCheck"() {
        given:
        PriceBookRelatedListController priceBookRelatedListController = PowerMockito.spy(new PriceBookRelatedListController())
        controllerContext = Mock(ControllerContext)
        Whitebox.setInternalState(priceBookRelatedListController, "controllerContext", controllerContext)
        StandardRelatedListController.Arg arg = new StandardRelatedListController.Arg()
        IObjectData objectData = getObjectData(Lists.newArrayList("_id"))
        objectData.set("object_describe_api_name", "SalesOrderObj")
        objectData.set("account_id", "xxx")
        objectData.set("partner_id", "xxx")
        arg.setObjectData(ObjectDataDocument.of(objectData))
        arg.setMasterData(ObjectDataDocument.of(objectData))
        arg.setRelatedListName("xxx")
        Whitebox.setInternalState(priceBookRelatedListController, "arg", arg)
        InfraServiceFacade infraServiceFacade = PowerMockito.mock(InfraServiceFacade)
        Whitebox.setInternalState(priceBookRelatedListController, "infraServiceFacade", infraServiceFacade)
        DomainPluginParam domainPluginParam = new DomainPluginParam();
        Map<String, String> fieldMapping = Maps.newHashMap();
        fieldMapping.put("form_account_id", "account_id");
        fieldMapping.put("form_partner_id", "partner_id");
        domainPluginParam.setFieldMapping(fieldMapping);
        PowerMockito.doReturn(domainPluginParam).when(infraServiceFacade, "findPluginParam", any(), any(), any())
        PowerMockito.when(ValidDateUtils.getValidDate(any(), any(), any())).thenReturn(1452342424234L)
        PowerMockito.suppress(MemberMatcher.method(BaseListController, "doFunPrivilegeCheck"))
        when:
        priceBookRelatedListController.doFunPrivilegeCheck()
        then:
        1 == 1
    }

    def "customSearchTemplate"() {
        given:
        PriceBookRelatedListController priceBookRelatedListController = PowerMockito.spy(new PriceBookRelatedListController())
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery()
        DomainPluginParam domainPluginParam = new DomainPluginParam();
        if (hasFieldMapping) {
            Map<String, String> fieldMapping = Maps.newHashMap();
            fieldMapping.put("form_account_id", "account_id");
            fieldMapping.put("form_partner_id", "partner_id");
            fieldMapping.put("form_price_book_id", "price_book_id")
            domainPluginParam.setFieldMapping(fieldMapping);
        }
        Whitebox.setInternalState(priceBookRelatedListController, "pluginParam", domainPluginParam)
        Whitebox.setInternalState(priceBookRelatedListController, "controllerContext", controllerContext)
        StandardRelatedListController.Arg arg = new StandardRelatedListController.Arg()
        IObjectData objectData = getObjectData(Lists.newArrayList("_id"))
        objectData.set("object_describe_api_name", describeApiName)
        objectData.set("account_id", "--")
        objectData.set("partner_id", "xxx")
        if (hasPrieBookId) {
            objectData.set("price_book_id", "xxx")
        }
        arg.setObjectData(ObjectDataDocument.of(objectData))
        arg.setMasterData(ObjectDataDocument.of(objectData))
        arg.setRelatedListName("xxx")
        Whitebox.setInternalState(priceBookRelatedListController, "arg", arg)
        def availableRangeUtils = PowerMockito.mock(AvailableRangeUtils)
        PowerMockito.field(PriceBookRelatedListController.class, "availableRangeUtils").set(priceBookRelatedListController, availableRangeUtils)
        PowerMockito.doNothing().when(availableRangeUtils, "buildRangeSearchQueryForPriceBook", any(), any(), any(), any(), any(), any(), anyBoolean(), any())
        PowerMockito.doNothing().when(availableRangeUtils, "buildSearchQueryForPriceBook", any(), any(), any(), any())
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(priceBookRelatedListController, "serviceFacade", serviceFacade)
        IObjectDescribe objectDescribe = new ObjectDescribe()
        IFieldDescribe fieldDescribe = new ObjectReferenceFieldDescribe()
        fieldDescribe.setApiName("price_book_id")
        objectDescribe.addFieldDescribe(fieldDescribe)
        PowerMockito.doReturn(null).when(serviceFacade, "findObject", any(), any())
        when:
        priceBookRelatedListController.customSearchTemplate(searchTemplateQuery)
        then:
        1 == 1
        where:
        describeApiName | hasFieldMapping | hasPrieBookId
        "SalesOrderObj" | true            | true
        null            | null            | null
        "SalesOrderObj" | false           | null
        "SalesOrderObj" | true            | false
    }

    void initSpringContext() {
        def stubApplicationContext = Stub(ApplicationContext)
        def util = new SpringContextUtil()
        util.setApplicationContext(stubApplicationContext)
        Map<Class, Object> map = new HashMap<>()
        stubApplicationContext.getBean(_ as Class) >> { Class requiredType ->
            map.computeIfAbsent(requiredType, { key ->
                Stub(requiredType)
            })
        }

        Map<String, Object> map2 = new HashMap<>()
        stubApplicationContext.getBean(_ as String, _ as Class) >> { String name, Class requiredType ->
            map2.computeIfAbsent(name, { key ->
                Stub(requiredType)
            })
        }
    }

    def getObjectData(List<String> fieldList) {
        def objectData = new ObjectData();
        fieldList.each {
            objectData.set(it, "xxxx")
        }
        objectData.setTenantId(tenantId)
        objectData
    }

}
