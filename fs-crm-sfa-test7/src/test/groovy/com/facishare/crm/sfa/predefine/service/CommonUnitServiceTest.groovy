package com.facishare.crm.sfa.predefine.service
//package com.facishare.crm.sfa.test_package.test03
//
import com.facishare.crm.sfa.RemoveUseless
import com.facishare.crm.sfa.predefine.service.model.CrmMenuInitFindMenuDataList
import com.facishare.crm.sfa.predefine.service.model.MutipleUnitInfo
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.service.IObjectDescribeService
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.ui.layout.ILayout
import com.fxiaoke.functions.utils.Lists
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString

/**
 * <AUTHOR>
 * @time 2024-04-07 10:31
 * @Description
 */
@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@SuppressStaticInitializationFor([
        "com.facishare.crm.sfa.predefine.service.EnterpriseInitService"])
class CommonUnitServiceTest extends RemoveUseless {
    @Shared
    User user = new User("71568", "-10000")
    CommonUnitService tester

    @Shared
    IObjectDescribeService objectDescribeService

    def setup() {
        tester= new CommonUnitService()
        objectDescribeService = PowerMockito.mock(IObjectDescribeService)
    }

    def "newOpenService"() {
        given:
        def arg = tenantIds
        List<ILayout> layouts = new ArrayList<>()
        and:

        def objectDescribeService = PowerMockito.mock(IObjectDescribeService)
        def objectDescribeMock = PowerMockito.mock(IObjectDescribe)
        def enterpriseInitService = Mock(EnterpriseInitService)
        def crmMenuInitService = Mock(CrmMenuInitService)

        def serviceFacade = PowerMockito.mock(ServiceFacade)
        IObjectDescribe objectDescribe = new ObjectDescribe();
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(tester, "objectDescribeService", objectDescribeService)
        Whitebox.setInternalState(tester, "enterpriseInitService", enterpriseInitService)
        Whitebox.setInternalState(tester, "crmMenuInitService", crmMenuInitService)

        PowerMockito.doReturn(objectDescribe).when(objectDescribeService,"findByTenantIdAndDescribeApiName",anyString(),anyString());

        PowerMockito.doReturn(layouts).when(serviceFacade, "findLayoutByObjectApiName", any(), any())
        enterpriseInitService.initPrivilegeRelate(any(), any(User), any(), any(), any()) >> ""
        def crmMenuInitFindMenuDataListResult = new CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListResult()

        crmMenuInitService.innerFindMenuDataListByApiNames(any(), any()) >> crmMenuInitFindMenuDataListResult
        when:
        def result = tester.newOpenService(Lists.newArrayList(89150))
        then:
        true
        where:
        tenantIds                 | dataObjApiName
        Lists.newArrayList(89150) | "ProductObj"
    }

    def "openAmortizeInfo"() {
        given:
        def arg = tenantIds
        List<ILayout> layouts = new ArrayList<>()

        def requestContext = Mock(RequestContext)
        def serviceContext = new ServiceContext(requestContext, "", "")
        and:

        requestContext.getTenantId() >> "89150"
        requestContext.getUser() >> new User("89150", "-10000")

        and:

        def objectDescribeService = PowerMockito.mock(IObjectDescribeService)
        def objectDescribeMock = PowerMockito.mock(IObjectDescribe)
        def enterpriseInitService = Mock(EnterpriseInitService)
        def crmMenuInitService = Mock(CrmMenuInitService)

        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(tester, "objectDescribeService", objectDescribeService)
        Whitebox.setInternalState(tester, "enterpriseInitService", enterpriseInitService)
        Whitebox.setInternalState(tester, "crmMenuInitService", crmMenuInitService)

        IObjectDescribe objectDescribe = new ObjectDescribe();
        PowerMockito.doReturn(objectDescribe).when(objectDescribeService,"findByTenantIdAndDescribeApiName",anyString(),anyString());


        PowerMockito.doReturn(layouts).when(serviceFacade, "findLayoutByObjectApiName", any(), any())
        enterpriseInitService.initPrivilegeRelate(any(), any(User), any(), any(), any()) >> ""
        def crmMenuInitFindMenuDataListResult = new CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListResult()

        crmMenuInitService.innerFindMenuDataListByApiNames(any(), any()) >> crmMenuInitFindMenuDataListResult
        when:
        def result = tester.openAmortizeInfo(serviceContext, arg)
        then:
        notThrown(ValidateException)
        where:
        tenantIds                 | dataObjApiName
        Lists.newArrayList(89150) | "ProductObj"
    }

    def "openPayment"() {
        given:
        def arg = tenantIds
        List<ILayout> layouts = new ArrayList<>()
        def requestContext = Mock(RequestContext)
        def serviceContext = new ServiceContext(requestContext, "", "")
        and:

        def objectDescribeService = PowerMockito.mock(IObjectDescribeService)
        def enterpriseInitService = Mock(EnterpriseInitService)
        def crmMenuInitService = Mock(CrmMenuInitService)

        def serviceFacade = PowerMockito.mock(ServiceFacade)
        IObjectDescribe objectDescribe = new ObjectDescribe();
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(tester, "objectDescribeService", objectDescribeService)
        Whitebox.setInternalState(tester, "enterpriseInitService", enterpriseInitService)
        Whitebox.setInternalState(tester, "crmMenuInitService", crmMenuInitService)

        PowerMockito.doReturn(objectDescribe).when(objectDescribeService,"findByTenantIdAndDescribeApiName",anyString(),anyString());

        PowerMockito.doReturn(layouts).when(serviceFacade, "findLayoutByObjectApiName", any(), any())
        enterpriseInitService.initPrivilegeRelate(any(), any(User), any(), any(), any()) >> ""
        def crmMenuInitFindMenuDataListResult = new CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListResult()

        crmMenuInitService.innerFindMenuDataListByApiNames(any(), any()) >> crmMenuInitFindMenuDataListResult
        when:
        def result = tester.openPayment(serviceContext,Lists.newArrayList(89150))
        then:
        true
        where:
        tenantIds                 | dataObjApiName
        Lists.newArrayList(89150) | "ProductObj"
    }

    def "checkProductCommonUnit"() {
        given:

        def lazyLoadOptionsService = Mock(LazyLoadOptionsService)

        Whitebox.setInternalState(tester, "lazyLoadOptionsService", lazyLoadOptionsService)
        def options = new ArrayList<MutipleUnitInfo.Option>()
        MutipleUnitInfo.Option option = new MutipleUnitInfo.Option()
        option.setLabel("a1")
        option.setValue("1")
        options.add(option)
        lazyLoadOptionsService.getLazyLoadOptionList(_ as String, _ as User, _ as String) >> options
        when:
        def result = tester.checkProductCommonUnit(new User("89150", "-10000"), "89150"
                , productId, commonUnit)
        then:
        result == null
        where:
        productId | commonUnit
        "11111"   | "1"
    }

}
