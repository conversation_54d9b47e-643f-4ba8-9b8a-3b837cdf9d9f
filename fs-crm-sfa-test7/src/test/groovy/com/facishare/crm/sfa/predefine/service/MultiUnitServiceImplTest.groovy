package com.facishare.crm.sfa.predefine.service
//package com.facishare.crm.sfa.test_package.test05
//
import com.facishare.crm.describebuilder.CurrencyFieldDescribeBuilder
import com.facishare.crm.describebuilder.NumberFieldDescribeBuilder
import com.facishare.crm.describebuilder.SelectOneFieldDescribeBuilder
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.predefine.service.model.MultiUnitData
import com.facishare.crm.sfa.predefine.service.model.MultiUnitListData
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl
import com.facishare.crm.sfa.utilities.proxy.StockProxy
import com.facishare.crm.sfa.utilities.proxy.model.AllowModifyBaseUnitRestModel
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil
import com.facishare.crm.util.DomainPluginDescribeExt
import com.facishare.crm.util.UnitUtil
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.appframework.metadata.MetaDataActionService
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.ISelectOption
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe
import com.facishare.paas.metadata.impl.describe.SelectOption
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl
import com.facishare.paas.metadata.util.SpringUtil
import com.google.common.collect.Lists
import com.facishare.paas.I18N
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString

/**
 * @描述说明 ：
 *
 * @作 者：chench
 *
 * @创建日 期：2024-06-11
 */
@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([UnitUtil,I18N,SFAConfigUtil,SpringUtil])
@SuppressStaticInitializationFor([
        "com.facishare.paas.I18N",
        "com.facishare.crm.sfa.utilities.util.SFAConfigUtil"
])

class MultiUnitServiceImplTest extends Specification {
    @Shared
    def arTester = new MultiUnitServiceImpl()

    def setupSpec() {
        PowerMockito.mockStatic(I18N)
        PowerMockito.mockStatic(SFAConfigUtil)
    }

    def "checkEditSpuMultiUnit"() {
        given:
        List<ISelectOption> selectOptions = Lists.newArrayList();
        SelectOption one = new SelectOption("个", "1", "");
        SelectOption two = new SelectOption("箱", "2", "");
        selectOptions.add(one)
        selectOptions.add(two)

        IFieldDescribe unitFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName("unit")
                .label("单位")
                .required(false)
                .selectOptions(selectOptions)
                .build();
        def objectDataService = PowerMockito.mock(ObjectDataServiceImpl)
        Whitebox.setInternalState(arTester, "objectDataService", objectDataService)
        PowerMockito.doReturn([]).when(objectDataService, "findBySql", any(), any())
        def stockProxy = PowerMockito.mock(StockProxy)
        Whitebox.setInternalState(arTester, "stockProxy", stockProxy)
        AllowModifyBaseUnitRestModel.ResultData resultData = new AllowModifyBaseUnitRestModel.ResultData()
        resultData.result = false
        AllowModifyBaseUnitRestModel.Result result = new AllowModifyBaseUnitRestModel.Result()
        result.data = resultData
        PowerMockito.doReturn(result).when(stockProxy, "isAllowModifyBaseUnit", any(), any())

        when:
        arTester.checkEditSpuMultiUnit(spuData, multiUnitData, unitFieldDescribe)
        then:
        notThrown(ValidateException)
        where:
        spuData                                          | multiUnitData
        new ObjectData("_id": "111", "tenant_id": "222") | [new MultiUnitData.MultiUnitItem("isBaseUnit": true, "isPriceUnit": true, "conversionRatio": 1, "unitId": "1")]
    }

    def "checkEditSkuMultiUnit"() {
        given:
        List<ISelectOption> selectOptions = Lists.newArrayList();
        SelectOption one = new SelectOption("个", "1", "");
        SelectOption two = new SelectOption("箱", "2", "");
        selectOptions.add(one)
        selectOptions.add(two)

        IFieldDescribe unitFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName("unit")
                .label("单位")
                .required(false)
                .selectOptions(selectOptions)
                .build();
        def objectDataService = PowerMockito.mock(ObjectDataServiceImpl)
        Whitebox.setInternalState(arTester, "objectDataService", objectDataService)
        PowerMockito.doReturn([]).when(objectDataService, "findBySql", any(), any())
        def stockProxy = PowerMockito.mock(StockProxy)
        Whitebox.setInternalState(arTester, "stockProxy", stockProxy)
        AllowModifyBaseUnitRestModel.ResultData resultData = new AllowModifyBaseUnitRestModel.ResultData()
        resultData.result = false
        AllowModifyBaseUnitRestModel.Result result = new AllowModifyBaseUnitRestModel.Result()
        result.data = resultData
        PowerMockito.doReturn(result).when(stockProxy, "isAllowModifyBaseUnit", any(), any())

        when:
        arTester.checkEditSkuMultiUnit("89150", "11111", multiUnitData, unitFieldDescribe)
        then:
        notThrown(ValidateException)
        where:
        spuData                                          | multiUnitData
        new ObjectData("_id": "111", "tenant_id": "222") | [new MultiUnitData.MultiUnitItem("isBaseUnit": true, "isPriceUnit": true, "conversionRatio": 1, "unitId": "1")]
    }

    def "checkDuplicateData"() {
        def serviceFacade = PowerMockito.mock(ServiceFacade)

        Whitebox.setInternalState(arTester, "serviceFacade", serviceFacade)
        def dataList = [new ObjectData("name": "产品A", "is_package": true, "_id": "p1"), new ObjectData("name": "产品B")]
        def queryResult = new QueryResult()
        queryResult.setData(dataList)

        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQueryIgnoreAll", any(), any(), any())

        when:
        def result = arTester.checkDuplicateData(new User("89150", "-10000"), objectData, id)
        then:
        result != null
        where:
        objectData                                                             | id
        new ObjectData("spu_id": "111", "product_id": "222", "unit_id": "333") | "111"
    }

    def "batchSaveMultiUnit"() {
        def metaDataActionService = PowerMockito.mock(MetaDataActionService)

        Whitebox.setInternalState(arTester, "metaDataActionService", metaDataActionService)

        PowerMockito.doReturn(null).when(metaDataActionService, "bulkSaveObjectData", any(), any())

        List<MultiUnitData.MultiUnitItem> selectOptions = Lists.newArrayList();
        MultiUnitData.MultiUnitItem one = new MultiUnitData.MultiUnitItem();
        one.setIsBaseUnit(true)
        selectOptions.add(one)

        when:
        def result = arTester.batchSaveMultiUnit(new User("89150", "-10000"), objectData, selectOptions)
        then:
        result == null
        where:
        objectData                                                             | id
        new ObjectData("spu_id": "111", "product_id": "222", "unit_id": "333") | "111"
    }

    def "saveMultiUnit4SkuAdd"() {
        PowerMockito.mockStatic(SFAConfigUtil.class)
        def metaDataActionService = PowerMockito.mock(MetaDataActionService)

        Whitebox.setInternalState(arTester, "metaDataActionService", metaDataActionService)

        PowerMockito.doReturn(null).when(metaDataActionService, "bulkSaveObjectData", any(), any())

        List<MultiUnitData.MultiUnitItem> selectOptions = Lists.newArrayList();
        MultiUnitData.MultiUnitItem one = new MultiUnitData.MultiUnitItem();
        one.setIsBaseUnit(true)
        selectOptions.add(one)

        PowerMockito.when(SFAConfigUtil.isSpuOpen(anyString())).thenReturn(true)
        //mockedSFAConfigUtil.when(SFAConfigUtil.&isSpuOpen).thenReturn(true)

        when:
        def result = arTester.saveMultiUnit4SkuAdd(new User("89150", "-10000"), objectData, selectOptions)
        then:
        result == null
        where:
        objectData                                                             | id
        new ObjectData("spu_id": "111", "product_id": "222", "unit_id": "333") | "111"
    }

    def "preprocessMultiUnit"() {
        List<MultiUnitData.MultiUnitItem> selectOptions = Lists.newArrayList();
        MultiUnitData.MultiUnitItem one = new MultiUnitData.MultiUnitItem();
        one.setIsBaseUnit(true)
        selectOptions.add(one)
        ObjectDataDocument objectDataDocument = new ObjectDataDocument()
        objectDataDocument.put("is_multiple_unit", true)
        objectDataDocument.put("multi_unit_data", selectOptions)

        when:
        def result = arTester.preprocessMultiUnit(objectDataDocument)
        then:
        result != null
    }

    def "batchUpdateMultiUnit"() {
        PowerMockito.mockStatic(SFAConfigUtil.class)
        User user = new User("89150", "-10000")

        IObjectData spuData = new ObjectData();
        List<IObjectData> skuDataList = new ArrayList<>();
        IObjectData skuData1 = new ObjectData();
        skuData1.setId("skuId1");
        skuData1.set("status_flag", 2)
        skuDataList.add(skuData1);
        IObjectData skuData2 = Mock(IObjectData.class);
        skuData2.setId("skuId2");
        skuData2.set("status_flag", 3)
        skuDataList.add(skuData2);

        MultiUnitListData multiUnitListData = new MultiUnitListData();
        List<MultiUnitData.MultiUnitItem> addMultiUnitData = Lists.newArrayList();
        MultiUnitData.MultiUnitItem addData = new MultiUnitData.MultiUnitItem();
        addData.setUnitId("unitId1")
        addData.setIsBaseUnit(true)
        addMultiUnitData.add(addData);
        multiUnitListData.setAddMultiUnitData(addMultiUnitData);

        List<IObjectData> deleteMultiUnitData = new ArrayList<>();
        IObjectData deleteData = Mock(IObjectData.class);
        deleteMultiUnitData.add(deleteData);
        multiUnitListData.setDeleteMultiUnitData(deleteMultiUnitData);

        List<IObjectData> updateMultiUnitData = new ArrayList<>();
        IObjectData updateData = new ObjectData("unit_id": "unitId1","is_base":true);
        updateMultiUnitData.add(updateData);
        multiUnitListData.setUpdateMultiUnitData(updateMultiUnitData);

        def serviceFacade = PowerMockito.mock(ServiceFacade)

        Whitebox.setInternalState(arTester, "serviceFacade", serviceFacade)
        def metaDataActionService = PowerMockito.mock(MetaDataActionService)

        Whitebox.setInternalState(arTester, "metaDataActionService", metaDataActionService)

        PowerMockito.doReturn(null).when(metaDataActionService, "bulkSaveObjectData", any(), any())

        PowerMockito.doReturn(null).when(serviceFacade, "bulkDeleteDirect", any(), any());
        PowerMockito.doReturn(null).when(serviceFacade, "batchUpdate", any(), any());
        def dataList = [new ObjectData("unit_id": "unitId1")]
        def queryResult = new QueryResult()
        queryResult.setData(dataList)

        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQueryIgnoreAll", any(), any(), any())
        PowerMockito.when(SFAConfigUtil.isSpuOpen(anyString())).thenReturn(true)

        when:
        def result = arTester.batchUpdateMultiUnit(new User("89150", "-10000"), spuData, skuDataList, multiUnitListData)
        then:
        result == null
    }

    def "batchUpdateMultiUnit1"() {
        IObjectData spuData = new ObjectData();

        MultiUnitListData multiUnitListData = new MultiUnitListData();
        List<IObjectData> deleteMultiUnitData = new ArrayList<>();
        IObjectData deleteData = Mock(IObjectData.class);
        deleteMultiUnitData.add(deleteData);
        multiUnitListData.setDeleteMultiUnitData(deleteMultiUnitData);

        List<IObjectData> updateMultiUnitData = new ArrayList<>();
        IObjectData updateData = new ObjectData("unit_id": "unitId1","is_base":true);
        updateMultiUnitData.add(updateData);
        multiUnitListData.setUpdateMultiUnitData(updateMultiUnitData);

        def serviceFacade = PowerMockito.mock(ServiceFacade)

        Whitebox.setInternalState(arTester, "serviceFacade", serviceFacade)

        PowerMockito.doReturn(null).when(serviceFacade, "bulkDeleteDirect", any(), any());
        PowerMockito.doReturn(null).when(serviceFacade, "batchUpdate", any(), any());
        when:
        def result = arTester.batchUpdateMultiUnit(new User("89150", "-10000"), spuData, multiUnitListData)
        then:
        result == null
    }

    def "getMultiUnitDataByProductIds"() {
        QueryResult<IObjectData> queryResult = new QueryResult()

        def metaDataFindServiceExt = PowerMockito.mock(MetaDataFindServiceExt)

        Whitebox.setInternalState(arTester, "metaDataFindServiceExt", metaDataFindServiceExt)

        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQueryIgnoreAll", any(), any(), any())

        when:
        def result = arTester.getMultiUnitDataByProductIds(new User("89150", "-10000"), Lists.asList("1"))
        then:
        true
    }

    def "getDistinctMultiUnitDataBySpuId"() {
        QueryResult<IObjectData> queryResult = new QueryResult()
        def dataList = [new ObjectData("price": 1, "is_pricing": true), new ObjectData("price": null, "is_pricing": false)]
        queryResult.setData(dataList)


        def objectDataService = PowerMockito.mock(ObjectDataServiceImpl)
        Whitebox.setInternalState(arTester, "objectDataService", objectDataService)
        PowerMockito.doReturn(queryResult).when(objectDataService, "findBySql", any(), any(), any())

        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setFieldDescribes(Lists.asList(NumberFieldDescribeBuilder.builder()
                .apiName("standard_price")
                .decimalPalces(0).build()))

        def describeLogicService = PowerMockito.mock(DescribeLogicService)
        Whitebox.setInternalState(arTester, "describeLogicService", describeLogicService)
        PowerMockito.doReturn(objectDescribe).when(describeLogicService, "findObject", any(), any())

        when:
        def result = arTester.getDistinctMultiUnitDataBySpuId("1","1")
        then:
        true
    }

    def "getDistinctMultiUnitDataBySkuId"() {
        QueryResult<IObjectData> queryResult = new QueryResult()
        def dataList = [new ObjectData("price": 1, "is_pricing": true), new ObjectData("price": null, "is_pricing": false)]
        queryResult.setData(dataList)


        def objectDataService = PowerMockito.mock(ObjectDataServiceImpl)
        Whitebox.setInternalState(arTester, "objectDataService", objectDataService)
        PowerMockito.doReturn(queryResult).when(objectDataService, "findBySql", any(), any(), any())

        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setFieldDescribes(Lists.asList(NumberFieldDescribeBuilder.builder()
                .apiName("price")
                .decimalPalces(0).build()))

        def describeLogicService = PowerMockito.mock(DescribeLogicService)
        Whitebox.setInternalState(arTester, "describeLogicService", describeLogicService)
        PowerMockito.doReturn(objectDescribe).when(describeLogicService, "findObject", any(), any())

        when:
        def result = arTester.getDistinctMultiUnitDataBySkuId("1", "1", false)
        then:
        true
    }

    def "handleUnitAndPrice"() {
        def productDataList = [new ObjectData("_id": "11111")]
        List<Map> mapList = new ArrayList<>()
        Map multiUnitInfo = new HashMap()
        multiUnitInfo.put("product_id", "11111")
        multiUnitInfo.put("unit_id", "22222")
        multiUnitInfo.put("price", 1)
        mapList.add(multiUnitInfo)
        Map multiUnitInfo1 = new HashMap()
        multiUnitInfo1.put("product_id", "11111")
        multiUnitInfo1.put("unit_id", "22222")
        multiUnitInfo1.put("price", null)
        mapList.add(multiUnitInfo1)


        IObjectDescribe detailDescribe = new ObjectDescribe()
        detailDescribe.setFieldDescribes(Lists.asList(CurrencyFieldDescribeBuilder.builder()
                .apiName("price")
                .decimalPlaces(0).build()))

        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(arTester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(detailDescribe).when(serviceFacade, "findObject", any(), any())

        def objectDataService = PowerMockito.mock(ObjectDataServiceImpl)
        Whitebox.setInternalState(arTester, "objectDataService", objectDataService)
        PowerMockito.doReturn(mapList).when(objectDataService, "findBySql", any(), any())

        when:
        def result = arTester.handleUnitAndPrice(productDataList, "1")
        then:
        true
    }

    def "handleMultiUnitInfo"() {
        def productDataList = [new ObjectData("_id": "11111","is_multiple_unit":true,"price":1)]
        QueryResult<IObjectData> queryResult = new QueryResult()
        def dataList = [new ObjectData("product_id": "11111", "conversion_ratio": 1)]
        queryResult.setData(dataList)

        def metaDataFindServiceExt = PowerMockito.mock(MetaDataFindServiceExt)
        Whitebox.setInternalState(arTester, "metaDataFindServiceExt", metaDataFindServiceExt)
        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQueryIgnoreAll", any(), any(), any())

        when:
        def result = arTester.handleMultiUnitInfo("1", productDataList)
        then:
        true
    }

    def "updatePriceBookProductFields"() {
        List<Map> mapList = new ArrayList<>()
        Map multiUnitInfo = new HashMap()
        multiUnitInfo.put("discount", "11111")
        mapList.add(multiUnitInfo)
        def objectDataService = PowerMockito.mock(ObjectDataServiceImpl)
        Whitebox.setInternalState(arTester, "objectDataService", objectDataService)
        PowerMockito.doReturn(mapList).when(objectDataService, "findBySql", any(), any())

        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(arTester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(Lists.newArrayList()).when(serviceFacade, "batchUpdateByFields", any(), any(), any());
        when:
        def result = arTester.updatePriceBookProductFields(new User("89150", "-10000"), "2", "3")
        then:
        true
    }
    def "validateMobileClientVersion"() {
        def bizCommonConfigService = PowerMockito.mock(BizCommonConfigService)
        Whitebox.setInternalState(arTester, "bizCommonConfigService", bizCommonConfigService)
        PowerMockito.doReturn(true).when(bizCommonConfigService, "isOpen", any(), any());
        def requestContext = PowerMockito.mock(RequestContext)

        when:
        def result = arTester.validateMobileClientVersion(requestContext)
        then:
        true
    }

    def "test generateObjectData method"() {
        given: "准备测试数据"
        User user = new User("89150", "-10000")
        MultiUnitData.MultiUnitItem item = new MultiUnitData.MultiUnitItem(
                unitId: "unit123",
                conversionRatio: "1.0",
                barcode: "barcode123",
                isBaseUnit: true,
                isPriceUnit: false,
                isEnable: true,
                places_decimal: 2,
                unitType: "large",
                minimumOrderQuantity: 10,
                isScanCode: true
        )

        when: "调用generateObjectData方法"
        IObjectData result = arTester.generateObjectData(user, item)

        then: "验证生成的对象数据"
        result != null
    }


    def "test getQuantityDecimalPlaces method"() {
        given: "准备测试数据"
        IObjectDescribe salesOrderProductDescribe = new ObjectDescribe()
        List<IFieldDescribe> fields = new ArrayList<>()
        IFieldDescribe quantityField = new NumberFieldDescribeBuilder()
                .apiName("quantity")
                .decimalPalces(2)
                .build()
        fields.add(quantityField)
        salesOrderProductDescribe.setFieldDescribes(fields)

        when: "调用getQuantityDecimalPlaces方法"
        Integer result = arTester.getQuantityDecimalPlaces(salesOrderProductDescribe)

        then: "验证返回的小数位数"
        result == 2
    }

    def "test getOtherUnitQuantityDecimalPlaces method"() {
        given: "准备测试数据"
        IObjectDescribe salesOrderProductDescribe = new ObjectDescribe()
        List<IFieldDescribe> fields = new ArrayList<>()
        IFieldDescribe quantityField = new NumberFieldDescribeBuilder()
                .apiName("other_unit_quantity")
                .decimalPalces(2)
                .build()
        fields.add(quantityField)
        salesOrderProductDescribe.setFieldDescribes(fields)

        when: "调用getQuantityDecimalPlaces方法"
        Integer result = arTester.getOtherUnitQuantityDecimalPlaces(salesOrderProductDescribe)

        then: "验证返回的小数位数"
        result == 2
    }

    def "getMultiUnitInfoListByProduct"() {
        given:
        String tenantId = "tenant123"
        List<String> productIds = ["product1", "product2"]

        // Mock QueryResult 和返回数据
        QueryResult<IObjectData> queryResult = new QueryResult<>()
        List<IObjectData> mockDataList = [
                new ObjectData("product_id": "product1", "unit_id": "unit1"),
                new ObjectData("product_id": "product2", "unit_id": "unit2")
        ]
        queryResult.setData(mockDataList)

        // Mock serviceFacade 的行为
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(arTester, "serviceFacade", serviceFacade)

        // 当调用 findBySearchQueryIgnoreAll 时返回预定义的数据
        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQueryIgnoreAll", any(), anyString(), any())

        when:
        // 调用被测方法
        List<IObjectData> result = arTester.getMultiUnitInfoListByProduct(tenantId, productIds)

        then:
        // 验证结果是否符合预期
        result != null
        result.size() == 2
        result[0].get("product_id") == "product1"
        result[0].get("unit_id") == "unit1"
        result[1].get("product_id") == "product2"
        result[1].get("unit_id") == "unit2"
    }

    def "getMultiUnitInfoListBySPU"() {
        given:
        String tenantId = "tenant123"
        String spuId = "spuId"

        // Mock QueryResult 和返回数据
        QueryResult<IObjectData> queryResult = new QueryResult<>()
        List<IObjectData> mockDataList = [
                new ObjectData("product_id": "product1", "unit_id": "unit1"),
                new ObjectData("product_id": "product2", "unit_id": "unit2")
        ]
        queryResult.setData(mockDataList)

        // Mock serviceFacade 的行为
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(arTester, "serviceFacade", serviceFacade)

        // 当调用 findBySearchQueryIgnoreAll 时返回预定义的数据
        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQueryIgnoreAll", any(), anyString(), any())

        when:
        // 调用被测方法
        List<IObjectData> result = arTester.getMultiUnitInfoListBySPU(tenantId, spuId)

        then:
        // 验证结果是否符合预期
        result != null
    }

    def "compareMultiUnitData"() {
        given:
        MultiUnitListData result = new MultiUnitListData()

        // 模拟输入的 multiUnitItems（前端传入）
        List<MultiUnitData.MultiUnitItem> multiUnitItems = [
                new MultiUnitData.MultiUnitItem(unitId: "unit1", isBaseUnit: true),
                new MultiUnitData.MultiUnitItem(unitId: "unit2", isBaseUnit: false)
        ]

        // 模拟数据库中的 dbMultiUnitItems
        IObjectData dbItem1 = Mock(IObjectData)
        dbItem1.get("unit_id", String.class) >> "unit1"
        IObjectData dbItem2 = Mock(IObjectData)
        dbItem2.get("unit_id", String.class) >> "unit3"

        List<IObjectData> dbMultiUnitItems = [dbItem1, dbItem2]

        when:
        result = arTester.compareMultiUnitData(multiUnitItems, dbMultiUnitItems)

        then:
        // 验证 addMultiUnitData 是否包含 unit2（db 中没有）
        result.addMultiUnitData.size() == 1
        result.addMultiUnitData[0].unitId == "unit2"

        // 验证 deleteMultiUnitData 是否包含 unit3（multiUnitItems 中没有）
        result.deleteMultiUnitData.size() == 1
        result.deleteMultiUnitData.contains(dbItem2)

        // 验证 updateMultiUnitData 是否包含 unit1（存在于 db 和 multiUnitItems）
        result.updateMultiUnitData.size() == 1
        result.updateMultiUnitData.contains(dbItem1)

        when:
        // 当 dbMultiUnitItems 为空时
        result = arTester.compareMultiUnitData(multiUnitItems, [])

        then:
        result.addMultiUnitData.size() == 2
        result.deleteMultiUnitData.isEmpty()
        result.updateMultiUnitData.isEmpty()

        when:
        // 当 multiUnitItems 为空时
        result = arTester.compareMultiUnitData([], dbMultiUnitItems)

        then:
        result.addMultiUnitData.isEmpty()
        result.deleteMultiUnitData.size() == 2
        result.updateMultiUnitData.isEmpty()
    }
    def "mergeMultiUnitData should update matching unit items"() {
        given: "准备测试数据"
        List<MultiUnitData.MultiUnitItem> multiUnitItems = [
                new MultiUnitData.MultiUnitItem(
                        unitId: "unit1",
                        conversionRatio: "2.0",
                        barcode: "barcode2",
                        places_decimal: "3", // ✅ 改为字符串类型
                        unitType: "medium",
                        isBaseUnit: false,
                        isPriceUnit: true,
                        isEnable: false,
                        minimumOrderQuantity: 50,
                        isScanCode: false
                )
        ]

        List<IObjectData> updateMultiUnitItems = [Mock(IObjectData)]
        updateMultiUnitItems[0].get("unit_id", String.class) >> "unit1"

        when: "调用 mergeMultiUnitData 方法"
        arTester.mergeMultiUnitData(multiUnitItems, updateMultiUnitItems)

        then: "验证 IObjectData 的字段是否被正确设置"
        1 * updateMultiUnitItems[0].set("conversion_ratio", "2.0")
        1 * updateMultiUnitItems[0].set("barcode", "barcode2")
        1 * updateMultiUnitItems[0].set("places_decimal", "3") // ✅ 这里也改为字符串匹配
        1 * updateMultiUnitItems[0].set("unit_type", "medium")
        1 * updateMultiUnitItems[0].set("is_base", false)
        1 * updateMultiUnitItems[0].set("is_pricing", true)
        1 * updateMultiUnitItems[0].set("is_enable", false)
        1 * updateMultiUnitItems[0].set("minimum_order_quantity", 50)
        1 * updateMultiUnitItems[0].set("is_scan_code", false)
    }

    def "test handQuantityDecimalPlaces"() {
        given: "准备测试数据"
        String tenantId = "tenant123"
        List<String> productIds = ["product1", "product2"]
        List<IObjectData> objectDataList = []

        // 创建两个 mocked 的 IObjectData 数据，模拟查询结果
        def multiUnitRelateds = [
                Mock(IObjectData),
                Mock(IObjectData)
        ]

        multiUnitRelateds[0].get("product_id") >> "product1"
        multiUnitRelateds[0].get("unit_id") >> "unit1"
        multiUnitRelateds[0].get("places_decimal") >> "1"

        multiUnitRelateds[1].get("product_id") >> "product2"
        multiUnitRelateds[1].get("unit_id") >> "unit2"
        multiUnitRelateds[1].get("places_decimal") >> "2"

        // Mock QueryResult 和返回数据
        QueryResult<IObjectData> queryResult = new QueryResult<>()
        List<IObjectData> mockDataList = [
                new ObjectData("product_id": "product1", "unit_id": "unit1"),
                new ObjectData("product_id": "product2", "unit_id": "unit2")
        ]
        queryResult.setData(mockDataList)

        // Mock serviceFacade 的行为
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(arTester, "serviceFacade", serviceFacade)

        // 当调用 findBySearchQueryIgnoreAll 时返回预定义的数据
        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQueryIgnoreAll", any(), anyString(), any())


        when: "调用handQuantityDecimalPlaces方法"
        arTester.handQuantityDecimalPlaces(tenantId, productIds, objectDataList, Mock(IObjectDescribe))

        then: "验证结果是否符合预期"
        true
    }

    def "test validateQuantityDecimal"() {
        given: "准备测试数据"
        String tenantId = "tenant123"
        PowerMockito.mockStatic(com.facishare.paas.I18N.class)
        PowerMockito.when(com.facishare.paas.I18N.text(anyString())).thenReturn("异常")

        // 构造一个 IObjectDescribe，其中 quantity 字段 decimal_places=2
        def quantityField = new NumberFieldDescribeBuilder()
                .apiName("quantity")
                .decimalPalces(2)
                .build()

        def describe = Mock(IObjectDescribe)
        describe.getFieldDescribeMap() >> [(quantityField.apiName): quantityField]

        // 构造 DomainPluginDescribeExt（dmExt）
        def dmExt = Mockito.mock(DomainPluginDescribeExt)

        // 默认 detail field api name 是 product_id 和 actual_unit
        PowerMockito.doReturn("product_id").when(dmExt).getDefaultDetailFieldApiName("product_id")
        PowerMockito.doReturn("actual_unit").when(dmExt).getDefaultDetailFieldApiName("actual_unit")
        PowerMockito.doReturn("quantity").when(dmExt).getDefaultDetailFieldApiName("quantity")


        // 构造 objectDataList，每个对象都有 product_id、actual_unit、quantity
        def objectDataList = [
                Mock(IObjectData),
                Mock(IObjectData)
        ]

        objectDataList[0].get("product_id", String.class) >> "product1"
        objectDataList[0].get("actual_unit", String.class) >> "unit1"
        objectDataList[0].get("quantity", String.class) >> "10.54321"  // 小数位5 > places_decimal1

        objectDataList[1].get("product_id", String.class) >> "product2"
        objectDataList[1].get("actual_unit", String.class) >> "unit2"
        objectDataList[1].get("quantity", String.class) >> "5.6789"     // 小数位4 > places_decimal2

        // 模拟 serviceFacade.findNameByIds(...) 获取产品名
        def productNameMap = ["product1": "产品A", "product2": "产品B"]
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(arTester, "serviceFacade", serviceFacade)

        PowerMockito.doReturn(productNameMap).when(serviceFacade, "findNameByIds", any(), anyString(), any())

        // 模拟 multiUnitRelated 查询结果
        def multiUnitRelateds = [
                Mock(IObjectData),
                Mock(IObjectData)
        ]

        multiUnitRelateds[0].get("product_id") >> "product1"
        multiUnitRelateds[0].get("unit_id") >> "unit1"
        multiUnitRelateds[0].get("places_decimal") >> "1"

        multiUnitRelateds[1].get("product_id") >> "product2"
        multiUnitRelateds[1].get("unit_id") >> "unit2"
        multiUnitRelateds[1].get("places_decimal") >> "2"

        // mock serviceFacade.findBySearchQueryIgnoreAll(...) 返回 multiUnitRelateds
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> multiUnitRelateds

        // 当调用 findBySearchQueryIgnoreAll 时返回预定义的数据
        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQueryIgnoreAll", any(), anyString(), any())

        when: "调用 validateQuantityDecimal 方法"
        arTester.validateQuantityDecimal(tenantId, objectDataList, describe, dmExt)

        then: "应抛出 ValidateException"
        thrown(ValidateException)
    }

    def "test validateProductUnit passes when unit is valid and conversion ratio matches"() {
        given: "准备测试数据"
        String tenantId = "tenant123"

        def dmExt = Mockito.mock(DomainPluginDescribeExt)

        // 默认 detail field api name 是 product_id 和 actual_unit
        PowerMockito.doReturn("product_id").when(dmExt).getDefaultDetailFieldApiName("product_id")
        PowerMockito.doReturn("actual_unit").when(dmExt).getDefaultDetailFieldApiName("actual_unit")
        PowerMockito.doReturn("conversion_ratio").when(dmExt).getDefaultDetailFieldApiName("conversion_ratio")

        def objectData = Mock(IObjectData)
        objectData.get("product_id", String.class) >> "product1"
        objectData.get("actual_unit", String.class) >> "unit1"
        objectData.get("conversion_ratio", BigDecimal.class) >> new BigDecimal("1.5")

        def objectDataList = [objectData]

        // 构造 productList，表示 product1 是开启了多单位的产品
        // mock serviceFacade.findBySearchQueryIgnoreAll(...) 返回 multiUnitRelateds

        def productList = [Mock(IObjectData)]
        productList[0].getId() >> "product1"
        productList[0].get("is_multiple_unit", Boolean.class) >> false

        // 构造 multiUnitDataList，表示 unit1 的 conversion_ratio 是 1.5
        def multiUnitData = Mock(IObjectData)
        multiUnitData.get("product_id", String.class) >> "product1"
        multiUnitData.get("conversion_ratio", BigDecimal.class) >> new BigDecimal("1.5")

        def multiUnitDataList = [multiUnitData]
        def queryResult1 = Mock(QueryResult)
        queryResult1.getData() >> multiUnitDataList
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(arTester, "serviceFacade", serviceFacade)

        def queryResult = Mock(QueryResult)
        queryResult.getData() >> productList
        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQueryIgnoreAll", any(), anyString(), any())
        def metaDataFindServiceExt = PowerMockito.mock(MetaDataFindServiceExt)

        Whitebox.setInternalState(arTester, "metaDataFindServiceExt", metaDataFindServiceExt)

        PowerMockito.doReturn(queryResult1).when(metaDataFindServiceExt, "findBySearchQueryIgnoreAll", any(), any(), any())

        def productNameMap = ["product1": "产品A"]
        PowerMockito.doReturn(productNameMap).when(serviceFacade, "findNameByIds", any(), any(), any())


        when: "调用 validateProductUnit 方法"
        arTester.validateProductUnit(tenantId, objectDataList, dmExt)

        then: "应不抛出异常"
        notThrown(ValidateException)
    }

    def "test validateProductUnit throws ValidateException when actual_unit is not in multiUnitIds"() {
        given:
        String tenantId = "tenant123"

        def dmExt = Mock(DomainPluginDescribeExt)
        dmExt.getDefaultDetailFieldApiName("product_id") >> "product_id"
        dmExt.getDefaultDetailFieldApiName("actual_unit") >> "actual_unit"
        dmExt.getDefaultDetailFieldApiName("conversion_ratio") >> "conversion_ratio"

        def objectData = Mock(IObjectData)
        objectData.get("product_id", String.class) >> "product1"
        objectData.get("actual_unit", String.class) >> "unit3"  // 不在 multiUnitIds 中
        objectData.get("conversion_ratio", BigDecimal.class) >> new BigDecimal("3.0")

        def objectDataList = [objectData]

        def productList = []
        productList << new ObjectData("_id": "product1", "is_multiple_unit": true)
        productList << new ObjectData("_id": "product2", "is_multiple_unit": true)

        def multiUnitDataList = []
        // 仅支持 unit1 和 unit2
        multiUnitDataList << new ObjectData(product_id: "product1","unit_id": "unit3","conversion_ratio":"0")
        multiUnitDataList << new ObjectData(product_id: "product2","unit_id": "unit3","conversion_ratio":"0")
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> multiUnitDataList

        PowerMockito.mockStatic(com.facishare.paas.I18N.class)
        PowerMockito.when(com.facishare.paas.I18N.text(anyString())).thenReturn("错误：指定的单位不在多单位配置中！")

        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(arTester, "serviceFacade", serviceFacade)

        PowerMockito.doReturn(productList).when(serviceFacade, "findObjectDataByIdsIgnoreAll", any(), any(), any())
        def metaDataFindServiceExt = PowerMockito.mock(MetaDataFindServiceExt)

        Whitebox.setInternalState(arTester, "metaDataFindServiceExt", metaDataFindServiceExt)

        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQueryIgnoreAll", any(), any(), any())

        def productNameMap = ["product1": "产品A"]
        PowerMockito.doReturn(productNameMap).when(serviceFacade, "findNameByIds", any(), any(), any())

        when: "调用 validateProductUnit 方法"
        arTester.validateProductUnit(tenantId, objectDataList, dmExt)

        then: "应抛出 ValidateException"
        thrown(ValidateException)
    }

    def "test validateProductUnit throws ValidateException when conversion ratio does not match"() {
        given:
        String tenantId = "tenant123"

        def dmExt = Mock(DomainPluginDescribeExt)
        dmExt.getDefaultDetailFieldApiName("product_id") >> "product_id"
        dmExt.getDefaultDetailFieldApiName("actual_unit") >> "actual_unit"
        dmExt.getDefaultDetailFieldApiName("conversion_ratio") >> "conversion_ratio"

        def objectData = Mock(IObjectData)
        objectData.get("product_id", String.class) >> "product1"
        objectData.get("actual_unit", String.class) >> "unit1"
        objectData.get("conversion_ratio", BigDecimal.class) >> new BigDecimal("2.0")  // 应该是 1.5

        def objectDataList = [objectData]

        def productList = []
        productList << new ObjectData("_id": "product1", "is_multiple_unit": true)
        productList << new ObjectData("_id": "product2", "is_multiple_unit": true)

        def multiUnitData = Mock(IObjectData)
        multiUnitData.get("product_id", String.class) >> "product1"
        multiUnitData.get("conversion_ratio", BigDecimal.class) >> new BigDecimal("1.5")

        def multiUnitDataList = [multiUnitData]
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> multiUnitDataList

        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(arTester, "serviceFacade", serviceFacade)

        PowerMockito.doReturn(productList).when(serviceFacade, "findObjectDataByIdsIgnoreAll", any(), any(), any())

        def metaDataFindServiceExt = PowerMockito.mock(MetaDataFindServiceExt)

        Whitebox.setInternalState(arTester, "metaDataFindServiceExt", metaDataFindServiceExt)

        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQueryIgnoreAll", any(), any(), any())

        def productNameMap = ["product1": "产品A"]
        PowerMockito.doReturn(productNameMap).when(serviceFacade, "findNameByIds", any(), any(), any())

        PowerMockito.mockStatic(com.facishare.paas.I18N.class)
        PowerMockito.when(com.facishare.paas.I18N.text(any(String))).thenReturn("错误：转换比例不匹配！")

        when: "调用 validateProductUnit 方法"
        arTester.validateProductUnit(tenantId, objectDataList, dmExt)

        then: "应抛出 ValidateException"
        thrown(ValidateException)
    }
    def "test isOpenMultiUnit"() {
        def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        Whitebox.setInternalState(arTester, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)

        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isMultipleUnit", any())

        when:
        def result = arTester.isOpenMultiUnit("111")
        then:
        true
    }

    def "test getBaseUnitName returns correct mapping when multi unit is open and data exists"() {
        given:
        String tenantId = "tenant123"
        List<String> productIds = ["product1", "product2"]

        def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        Whitebox.setInternalState(arTester, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)

        // 模拟 BizConfigThreadLocalCacheService.isOpenMultipleUnit 返回 true
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isMultipleUnit", tenantId)

        // 构造 serviceFacade 的行为
        def multiUnitRelateResult = new QueryResult()
        def dataList = [new ObjectData("product_id": "product1", "unit_id": "unit1"), new ObjectData("product_id": "product2", "unit_id": "unit2")]
        multiUnitRelateResult.setData(dataList)

        def serviceFacade = PowerMockito.mock(ServiceFacade)
        org.powermock.reflect.Whitebox.setInternalState(arTester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(multiUnitRelateResult).when(serviceFacade, "findBySearchQuery", any(), any(), any())

        // 构造 UnitUtil 的行为
        Map<String, String> unitOptionMap = ["unit1": "个", "unit2": "箱"]

        ApplicationContext applicationContext=PowerMockito.mock(ApplicationContext)
        PowerMockito.mockStatic(SpringUtil)
        PowerMockito.stub(PowerMockito.method(SpringUtil.class, "getContext"))
                .toReturn(applicationContext)
        PowerMockito.mockStatic(UnitUtil.class)
        PowerMockito.when(UnitUtil.getUnitOption(any(), any(), any(), any())).thenReturn(unitOptionMap)

        when:
        Map<String, String> result = arTester.getBaseUnitName(new User(tenantId, "-10000"), productIds)

        then:
        true
    }

    def "test updateStandPriceBookProductUnit updates actual_unit for pricing units"() {
        given: "准备参数和 mock 数据"
        String tenantId = "tenant123"
        User user = new User(tenantId, "-10000")
        List<String> productIds = ["product1", "product2"]

        def dataList = [new ObjectData("is_pricing": true, "unit_id": "unit1", "product_id": "product1")]

        def queryResult = new QueryResult()
        queryResult.setData(dataList)
        // 模拟 updatePriceBookProductActualUnit 的行为
        def metaDataFindServiceExt = PowerMockito.mock(MetaDataFindServiceExt)
        Whitebox.setInternalState(arTester, "metaDataFindServiceExt", metaDataFindServiceExt)

        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQueryIgnoreAll", any(), any(), any())
        // 模拟 getMultiUnitDataByProductIds 返回的数据
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(arTester, "serviceFacade", serviceFacade)

        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQueryIgnoreAll", any(), any(), any())
        PowerMockito.doReturn(new ArrayList()).when(serviceFacade, "bulkUpdateObjectDataOneField", any(), any(), any(), any())

        when: "调用 updateStandPriceBookProductUnit 方法"
        arTester.updateStandPriceBookProductUnit(user, productIds)

        then: "验证是否调用 updatePriceBookProductActualUnit 并传入正确的 unitId 和 skuIds"
        true
    }

    def "test updateStandPriceBookProductUnit1"() {
        given: "准备参数和 mock 数据"
        String tenantId = "tenant123"
        User user = new User(tenantId, "-10000")
        List<MultiUnitData.MultiUnitItem> multiUnitItemList = [
                new MultiUnitData.MultiUnitItem(unitId: "unit1", isPriceUnit: false),
                new MultiUnitData.MultiUnitItem(unitId: "unit2", isPriceUnit: false)
        ]
        List<String> productIds = ["product1", "product2"]
        when: "调用 updateStandPriceBookProductUnit 方法"
        arTester.updateStandPriceBookProductUnit(user, productIds, multiUnitItemList)

        then: "验证是否调用 updatePriceBookProductActualUnit 并传入正确的 unitId 和 skuIds"
        true
    }

    def "test checkIsUpdatePricingUnit"() {
        given: "准备参数和 mock 数据"
        String tenantId = "tenant123"
        User user = new User(tenantId, "-10000")

        def dbMultiUnitData = [new ObjectData("is_pricing": true, "unit_id": "unit1", "product_id": "product1")]
        List<MultiUnitData.MultiUnitItem> multiUnitData = [
                new MultiUnitData.MultiUnitItem(unitId: "unit1", isPriceUnit: true),
                new MultiUnitData.MultiUnitItem(unitId: "unit2", isPriceUnit: false)
        ]

        when: "调用 updateStandPriceBookProductUnit 方法"
        arTester.checkIsUpdatePricingUnit(multiUnitData, dbMultiUnitData)

        then: "验证是否调用 updatePriceBookProductActualUnit 并传入正确的 unitId 和 skuIds"
        true
    }

    def "test generateObjectData method1"() {
        given: "准备测试数据"
        User user = new User("89150", "-10000")
        Map<String, Object> item = new HashMap<String, Object>(
                _id:"1111",
                spu_id:"spu_id",
                unit_id: "unit123",
                conversion_ratio: "1.0",
                barcode: "barcode123",
                is_base: true,
                is_pricing: false,
                is_enable: true,
                places_decimal: 2,
                unit_type: "large",
                minimum_order_quantity: 10,
                owner: true,
                record_type: true,
                is_scan_code: true
        )

        Map<String, Object> item1 = new HashMap<String, Object>()
        item1.put("containerDocument", item)

        when: "调用generateObjectData方法"
        IObjectData result = arTester.generateObjectData(user, item1)

        then: "验证生成的对象数据"
        result != null
    }

}
