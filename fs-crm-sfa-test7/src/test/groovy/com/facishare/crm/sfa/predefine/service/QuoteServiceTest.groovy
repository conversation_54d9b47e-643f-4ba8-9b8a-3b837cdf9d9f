package com.facishare.crm.sfa.predefine.service

import com.facishare.crm.sfa.RemoveUseless
import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule
import com.facishare.crm.sfa.predefine.service.model.ListLastProduct
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.appframework.metadata.ObjectMappingServiceImpl
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.IObjectMappingRuleDetailInfo
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.ui.layout.Layout
import com.facishare.paas.metadata.ui.layout.ILayout
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString

/**
 * <AUTHOR>
 * @time 2024-04-07 10:31
 * @Description
 */
@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([ObjectDescribeExt])
class QuoteServiceTest extends RemoveUseless {
    @Shared
    User user = new User("71568", "-10000")
    def QuoteService tester

    def setup() {
        tester = new QuoteService()
    }

    def "checkCpqStatus"() {
        given:
        def requestContext = Mock(RequestContext)
        def actionContext = new ActionContext(requestContext, "", "")

        def moduleCtrlConfigService = PowerMockito.mock(ModuleCtrlConfigService)
        and:
        ConfigCtrlModule.Result result1 = ConfigCtrlModule.Result.builder().build()
        ConfigCtrlModule.Value value1 = ConfigCtrlModule.Value.builder().build()
        result1.setValue(value1)

        ServiceContext serviceContext = new ServiceContext(requestContext, "", "");
        ConfigCtrlModule.Arg configCtrlModule = new ConfigCtrlModule.Arg();
        configCtrlModule.setModuleCode("cpq");
        configCtrlModule.setTenantId(user.getTenantId());

        PowerMockito.doReturn(result1).when(moduleCtrlConfigService,
                "checkModuleStatus",
                configCtrlModule,
                serviceContext)

        org.powermock.reflect.Whitebox.setInternalState(tester, "moduleCtrlConfigService", moduleCtrlConfigService)

        when:
        def result = tester.checkCpqStatus(user, actionContext)
        then:
        true
    }

    def "listLastProduct"() {
        given:
        def arg = new ListLastProduct.Arg();
        arg.setAccountId("1111")
        arg.setPriceBookId("2222")
        arg.setProductId("333")
        arg.setRecordType("default__c")
        def requestContext = Mock(RequestContext)
        def serviceContext = new ServiceContext(requestContext, "", "")
        and:

        requestContext.getTenantId() >> "89150"
        requestContext.getUser() >> new User("89150", "-10000")

        and:
        def data = new ObjectData("is_package": true, "product_id": "111")
        def queryResult = new QueryResult();
        def dataList = [new ObjectData("root_prod_pkg_key": "111111", "quote_id": "111")]
        queryResult.setData(dataList)
        List<IObjectMappingRuleInfo> mappingRuleInfos = new ArrayList<>()
        IObjectMappingRuleInfo mappingRuleInfo = PowerMockito.mock(IObjectMappingRuleInfo)
        mappingRuleInfos.add(mappingRuleInfo)
        PowerMockito.doReturn("rule_salesorderprodobj2quotelinesobj__c").when(mappingRuleInfo, "getRuleApiName")

        List<IObjectMappingRuleDetailInfo> fieldMappingList = new ArrayList<>()
        IObjectMappingRuleDetailInfo fieldMapping = PowerMockito.mock(IObjectMappingRuleDetailInfo)
        fieldMapping.setSourceFieldName("record_type")
        fieldMappingList.add(fieldMapping)
        PowerMockito.doReturn(fieldMappingList).when(mappingRuleInfo, "getFieldMapping")

        def objectDescribeMock = PowerMockito.mock(IObjectDescribe)
        def ret = PowerMockito.mock(ObjectDescribeExt)

        def serviceFacade = PowerMockito.mock(ServiceFacade)
        def infraServiceFacade = PowerMockito.mock(InfraServiceFacade)

        def objectMappingService = PowerMockito.mock(ObjectMappingServiceImpl)
        org.powermock.reflect.Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        org.powermock.reflect.Whitebox.setInternalState(tester, "infraServiceFacade", infraServiceFacade)
        org.powermock.reflect.Whitebox.setInternalState(tester, "objectMappingService", objectMappingService)
        PowerMockito.doReturn(data).when(serviceFacade, "findObjectData", any(), any(), any())
        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQuery", any(), any(), any(), any())
        PowerMockito.doReturn(objectDescribeMock).when(serviceFacade, "findObject", any(), any())
        PowerMockito.doReturn(mappingRuleInfos).when(objectMappingService, "findByApiName", any(), any())
        //PowerMockito.doNothing().when(infraServiceFacade, "fillQuoteFieldValue", any(), any(), any(), any(), false)
        PowerMockito.doNothing().when(serviceFacade, "fillObjectDataWithRefObject", any(), any(), any(), any())
        PowerMockito.doNothing().when(serviceFacade, "fillUserInfo", any(), any(), any())
        PowerMockito.doNothing().when(serviceFacade, "fillDepartmentInfo", any(), any(), any())
        PowerMockito.mockStatic(ObjectDescribeExt.class)
        PowerMockito.when(ObjectDescribeExt.copy(objectDescribeMock)).thenReturn(ret)
        when:
        def result = tester.listLastProduct(serviceContext, arg)
        then:
        true
    }

    def "fillSubProduct"() {
        given:
        PowerMockito.mockStatic(I18N.class)
        PowerMockito.when(I18N.text(anyString())).thenReturn("异常")
        and:
        def dataList = [new ObjectData("root_prod_pkg_key": "111111", "quote_id": "111")]
        Set<String> idList = new HashSet<>()
        idList.add("111")

        def queryResult = new QueryResult();
        queryResult.setData(dataList)

        and:

        def objectDescribeMock = PowerMockito.mock(IObjectDescribe)

        def serviceFacade = PowerMockito.mock(ServiceFacade)
        org.powermock.reflect.Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQuery", any(), any(), any());

        when:
        def result = tester.fillSubProduct(user, dataList, objectDescribeMock, idList)

        then:
        true
    }

    def "fillSubProduct1"() {
        given:
        PowerMockito.mockStatic(I18N.class)
        PowerMockito.when(I18N.text(anyString())).thenReturn("异常")
        and:
        def dataList = [new ObjectData("root_prod_pkg_key": "111111", "quote_id": "111","prod_pkg_key":"111")]
        Set<String> idList = new HashSet<>()
        idList.add("111")

        def queryResult = new QueryResult();
        queryResult.setData(dataList)

        and:

        IObjectDescribe objectDescribe = new ObjectDescribe();
        objectDescribe.setApiName(apiName)

        def serviceFacade = PowerMockito.mock(ServiceFacade)
        org.powermock.reflect.Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQuery", any(), any(), any());

        when:
        def result = tester.fillSubProduct(user, ObjectDescribeExt.of(objectDescribe), dataList)

        then:
        true

        where:
        apiName                                                             | id
        "QuoteLinesObj" | "111"
        "SalesOrderProductObj" | "111"
    }

    def "getDataRightsParameter"() {
        given:
        when:
        def result = tester.getDataRightsParameter(apiName)

        then:
        true

        where:
        apiName                                                             | id
        "QuoteLinesObj" | "111"
        "SalesOrderProductObj" | "111"
    }
    def "getComponentRender"() {
        given:
        ILayout layout = new Layout();
        IObjectDescribe objectDescribe = new ObjectDescribe();
        when:
        def result = tester.getComponentRender(layout,user,ObjectDescribeExt.of(objectDescribe))

        then:
        true
    }
}
