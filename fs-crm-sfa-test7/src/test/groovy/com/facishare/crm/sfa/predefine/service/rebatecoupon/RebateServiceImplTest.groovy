package com.facishare.crm.sfa.predefine.service.rebatecoupon

import com.facishare.crm.constants.CouponConstants
import com.facishare.crm.constants.RebateConstants
import com.facishare.crm.sfa.predefine.service.AvailableRangeCoreService
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.predefine.service.pricepolicy.RuleEngineLogicService
import com.facishare.crm.sfa.predefine.service.rebatecoupon.RebateServiceImpl
import com.facishare.crm.sfa.predefine.service.rebatecoupon.dao.RebateDao
import com.facishare.crm.sfa.predefine.service.rebatecoupon.dao.RebateRuleDao
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.Amortize
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.Rebate
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RebateCouponMatch
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RebateCouponQuery
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RebateCouponUse
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RebateCouponAutoUse
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RebateRule
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RebateUseAmount
import com.facishare.crm.sfa.utilities.util.Price.RealPriceModel
import com.facishare.crm.util.DomainPluginDescribeExt
import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.impl.ObjectData
import com.fxiaoke.common.StopWatch
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.LoggerFactory
import org.spockframework.runtime.Sputnik
import spock.lang.Specification

import java.math.RoundingMode

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyBoolean
import static org.mockito.ArgumentMatchers.anyList
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.ArgumentMatchers.eq

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([DomainPluginDescribeExt.class, StopWatch.class, UdobjGrayConfig.class])
@PowerMockIgnore(["javax.management.*","com.facishare.paas.appframework.core.util.UdobjGrayConfig"])
class RebateServiceImplTest extends Specification {
    def logger = LoggerFactory.getLogger(RebateServiceImpl.class)

    def setup() {
        Whitebox.setInternalState(RebateServiceImpl, "log", logger)
    }
    def "test removeCanNotUseData"() {
        given:
        def rebateServiceImpl = new RebateServiceImpl()

        // 创建测试数据
        String ruleId = "rule1"

        List<RebateRule.Rule> rules = Lists.newArrayList(
                RebateRule.Rule.builder().id("rule1").name("规则1").build(),
                RebateRule.Rule.builder().id("rule2").name("规则2").build()
        )

        List<RebateCouponUse.UseData> useDataList = Lists.newArrayList(
                RebateCouponUse.UseData.builder().id("rebate1").build(),
                RebateCouponUse.UseData.builder().id("rebate2").build(),
                RebateCouponUse.UseData.builder().id("rebate3").build()
        )

        List<Rebate> currList = Lists.newArrayList(
                Rebate.builder().id("rebate1").name("返利1").build(),
                Rebate.builder().id("rebate2").name("返利2").build(),
                Rebate.builder().id("rebate3").name("返利3").build(),
                Rebate.builder().id("rebate4").name("返利4").build()
        )

        when:
        Whitebox.invokeMethod(rebateServiceImpl, "removeCanNotUseData", ruleId, useDataList, rules, currList)

        then:
        // 验证结果
        rules.size() == 1
        rules[0].id == "rule2" // rule1应该被移除

        currList.size() == 1
        currList[0].id == "rebate4" // rebate1、rebate2、rebate3应该被移除，因为它们在useDataList中
    }

    def "test hasRebateCondition"() {
        given:
        def rebateDao = PowerMockito.spy(new RebateDao())
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())
        Whitebox.setInternalState(rebateServiceImpl, "rebateDao", rebateDao)

        // 规则1：没有返利条件
        def rule1 = RebateRule.Rule.builder()
                .id("rule1")
                .rebateCondition(null)
                .build()

        // 规则2：有条件但条件内容为空
        def rule2 = RebateRule.Rule.builder()
                .id("rule2")
                .rebateCondition("condition2")
                .build()

        // 规则3：有条件且条件内容不为空
        def rule3 = RebateRule.Rule.builder()
                .id("rule3")
                .rebateCondition("condition3")
                .build()

        PowerMockito.doReturn(Lists.newArrayList()).when(rebateDao).getWheres("condition2")
        PowerMockito.doReturn(Lists.newArrayList(new Object())).when(rebateDao).getWheres("condition3")

        when:
        def result1 = Whitebox.invokeMethod(rebateServiceImpl, "hasRebateCondition", rule1)
        def result2 = Whitebox.invokeMethod(rebateServiceImpl, "hasRebateCondition", rule2)
        def result3 = Whitebox.invokeMethod(rebateServiceImpl, "hasRebateCondition", rule3)

        then:
        !result1 // 规则1没有返利条件，应该返回false
        !result2 // 规则2有条件但内容为空，应该返回false
        result3  // 规则3有条件且内容不为空，应该返回true
    }

    def "test filterRangeRebate"() {
        given:
        def rebateServiceImpl = new RebateServiceImpl()

        // 准备测试数据
        Set<String> dataIndexes = Sets.newHashSet("1", "3")

        List<Rebate> dataList = Lists.newArrayList(
                Rebate.builder().id("rebate1").name("测试返利1").productConditionType(RebateConstants.ProductConditionType.ALL.getValue()).build(),
                Rebate.builder().id("rebate2").name("测试返利2").productConditionType("OTHER").build(),
                Rebate.builder().id("rebate3").name("测试返利3").productConditionType("OTHER").build(),
                Rebate.builder().id("rebate4").name("测试返利4").productConditionType("OTHER").build()
        )

        Map<String, List<Amortize.ProductData>> rebateProducts = Maps.newHashMap()
        rebateProducts.put("rebate1", Lists.newArrayList(Amortize.ProductData.builder().id("1").build()))
        rebateProducts.put("rebate2", Lists.newArrayList(Amortize.ProductData.builder().id("1").build(), Amortize.ProductData.builder().id("2").build()))
        rebateProducts.put("rebate3", Lists.newArrayList(Amortize.ProductData.builder().id("3").build()))
        rebateProducts.put("rebate4", Lists.newArrayList(Amortize.ProductData.builder().id("4").build()))

        when:
        def result = Whitebox.invokeMethod(rebateServiceImpl, "filterRangeRebate", rebateProducts, dataIndexes, dataList)

        then:
        result.size() == 3
        // rebate1因为是ALL类型所以应该包含
        // rebate2因为包含产品1所以应该包含
        // rebate3因为包含产品3所以应该包含
        // rebate4因为不包含产品1或3所以不应该包含
        result.collect { it.id }.sort() == ["rebate1", "rebate2", "rebate3"]
    }

    def "test getRangeRebateData"() {
        given:
        def rebateServiceImpl = new RebateServiceImpl()

        // 测试场景1：miscContent为null
        when:
        def result1 = Whitebox.invokeMethod(rebateServiceImpl, "getRangeRebateData", null)

        then:
        result1 != null
        result1.size() == 0

        // 测试场景2：有范围返利数据
        when:
        // 构造测试数据
        Map miscContent = Maps.newHashMap()
        List<Map<String, Object>> rangeRebateList = Lists.newArrayList()

        Map<String, Object> rangeRebate1 = Maps.newHashMap()
        rangeRebate1.put("rangeRuleId", "rangeRule1")
        List<Map<String, Object>> rebatesList1 = Lists.newArrayList()
        rebatesList1.add(["id": "rebate1", "amount": 100, "name": "返利1"])
        rebatesList1.add(["id": "rebate2", "amount": 200, "name": "返利2"])
        rangeRebate1.put("rangeRebates", rebatesList1)

        Map<String, Object> rangeRebate2 = Maps.newHashMap()
        rangeRebate2.put("rangeRuleId", "rangeRule2")
        List<Map<String, Object>> rebatesList2 = Lists.newArrayList()
        rebatesList2.add(["id": "rebate3", "amount": 300, "name": "返利3"])
        rangeRebate2.put("rangeRebates", rebatesList2)

        rangeRebateList.add(rangeRebate1)
        rangeRebateList.add(rangeRebate2)

        miscContent.put(RebateCouponUse.UseData.DATA_RANGE_REBATE, rangeRebateList)

        def result2 = Whitebox.invokeMethod(rebateServiceImpl, "getRangeRebateData", miscContent)

        then:
        result2 != null
    }

    def "test getUseRebateIds"() {
        given:
        def rebateServiceImpl = new RebateServiceImpl()

        // 创建测试数据
        List<RebateCouponUse.UseData> useDataList = Lists.newArrayList(
                RebateCouponUse.UseData.builder().id("rebate1").build(),
                RebateCouponUse.UseData.builder().id("rebate2").build()
        )

        List<RebateCouponUse.RangeRebateData> rangeRebateData = Lists.newArrayList(
                RebateCouponUse.RangeRebateData.builder()
                        .rangeRuleId("rangeRule1")
                        .rangeRebates(Lists.newArrayList(
                                RebateCouponUse.UseData.builder().id("rebate3").build(),
                                RebateCouponUse.UseData.builder().id("rebate4").build()
                        ))
                        .build(),
                RebateCouponUse.RangeRebateData.builder()
                        .rangeRuleId("rangeRule2")
                        .rangeRebates(Lists.newArrayList(
                                RebateCouponUse.UseData.builder().id("rebate5").build()
                        ))
                        .build()
        )

        when:
        def result = Whitebox.invokeMethod(rebateServiceImpl, "getUseRebateIds", useDataList, rangeRebateData)

        then:
        // 验证结果
        result != null
    }

    def "test getRuleIds"() {
        given:
        def rebateServiceImpl = new RebateServiceImpl()

        // 创建测试数据
        String ruleId = "rule1"

        List<RebateCouponUse.RangeRebateData> rangeRebateData = Lists.newArrayList(
                RebateCouponUse.RangeRebateData.builder().rangeRuleId("rangeRule1").build(),
                RebateCouponUse.RangeRebateData.builder().rangeRuleId("rangeRule2").build()
        )

        when:
        def result = Whitebox.invokeMethod(rebateServiceImpl, "getRuleIds", ruleId, rangeRebateData)

        then:
        // 验证结果
        result != null
    }

    def "test checkOver"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())

        // 创建基本测试数据
        RebateCouponMatch.Arg arg = RebateCouponMatch.Arg.builder()
                .user(new User("82681", "1000"))
                .build()

        Map<String, RebateCouponUse.UseData> useDataMap = Maps.newHashMap()

        // 场景1：rule为null的情况
        when:
        def result1 = Whitebox.invokeMethod(rebateServiceImpl, "checkOver", arg, null, useDataMap, Lists.newArrayList(Rebate.builder().build()))

        then:
        result1 == true

        // 场景2：使用金额小于限额的情况
        when:
        RebateRule.Rule rule = RebateRule.Rule.builder().id("rule1").build()

        List<Rebate> currList = Lists.newArrayList(
                Rebate.builder().id("rebate1").build(),
                Rebate.builder().id("rebate2").build()
        )

        useDataMap.put("rebate1", RebateCouponUse.UseData.builder().id("rebate1").amount(new BigDecimal("30")).build())
        useDataMap.put("rebate2", RebateCouponUse.UseData.builder().id("rebate2").amount(new BigDecimal("20")).build())

        PowerMockito.doReturn(new BigDecimal("100")).when(rebateServiceImpl, "calcLimitMoney", any(), any(), anyBoolean())

        def result2 = Whitebox.invokeMethod(rebateServiceImpl, "checkOver", arg, rule, useDataMap, currList)

        then:
        result2 == false
        // 验证未修改currList中的返利金额
        currList[0].unusedAmount == new BigDecimal("30")
        currList[1].unusedAmount == new BigDecimal("20")

        // 场景3：使用金额大于限额的情况 - 产品类型返利
        when:
        currList = Lists.newArrayList(
                Rebate.builder()
                        .id("rebate1")
                        .rebateType(RebateConstants.RebateType.PRODUCT.getValue())
                        .endDate(1000L)
                        .build(),
                Rebate.builder()
                        .id("rebate2")
                        .rebateType(RebateConstants.RebateType.PRODUCT.getValue())
                        .endDate(2000L)
                        .build()
        )

        useDataMap.clear()
        useDataMap.put("rebate1", RebateCouponUse.UseData.builder().id("rebate1").amount(new BigDecimal("60")).build())
        useDataMap.put("rebate2", RebateCouponUse.UseData.builder().id("rebate2").amount(new BigDecimal("40")).build())

        PowerMockito.doReturn(new BigDecimal("80")).when(rebateServiceImpl, "calcLimitMoney", any(), any(), anyBoolean())

        def result3 = Whitebox.invokeMethod(rebateServiceImpl, "checkOver", arg, rule, useDataMap, currList)

        then:
        result3 == true

        // 场景4：使用金额大于限额的情况 - 金额类型返利
        when:
        currList = Lists.newArrayList(
                Rebate.builder()
                        .id("rebate1")
                        .rebateType(RebateConstants.RebateType.MONEY.getValue())
                        .endDate(3000L)
                        .build(),
                Rebate.builder()
                        .id("rebate2")
                        .rebateType(RebateConstants.RebateType.MONEY.getValue())
                        .endDate(1000L)
                        .build()
        )

        useDataMap.clear()
        useDataMap.put("rebate1", RebateCouponUse.UseData.builder().id("rebate1").amount(new BigDecimal("60")).build())
        useDataMap.put("rebate2", RebateCouponUse.UseData.builder().id("rebate2").amount(new BigDecimal("40")).build())

        PowerMockito.doReturn(new BigDecimal("80")).when(rebateServiceImpl, "calcLimitMoney", any(), any(), anyBoolean())

        def result4 = Whitebox.invokeMethod(rebateServiceImpl, "checkOver", arg, rule, useDataMap, currList)

        then:
        result4 == true
    }

    def "test addAmortizeKey"() {
        given:
        def rebateServiceImpl = new RebateServiceImpl()

        // 创建测试数据
        User user = new User("82681", "1000")
        DomainPluginDescribeExt pluginParam = PowerMockito.mock(DomainPluginDescribeExt.class)

        RebateCouponMatch.Arg arg = RebateCouponMatch.Arg.builder()
                .user(user)
                .pluginParam(pluginParam)
                .build()

        Amortize.AmortizeKey amortizeKey = Amortize.AmortizeKey.builder()
                .amortizeKey("rebate_amortize_amount")
                .dynamicKey("rebate_dynamic_amount")
                .build()

        // 模拟方法调用
        PowerMockito.doReturn("rebate_amortize_amount").when(pluginParam).getDefaultDetailFieldApiName("rebate_amortize_amount")
        PowerMockito.doReturn("rebate_dynamic_amount").when(pluginParam).getDefaultDetailFieldApiName("rebate_dynamic_amount")

        when:
        // 场景1：amortizeObj中没有分摊键
        Map<String, Object> amortizeObj1 = Maps.newHashMap()
        Whitebox.invokeMethod(rebateServiceImpl, "addAmortizeKey", arg, amortizeKey, amortizeObj1)

        // 场景2：amortizeObj中已有分摊键
        Map<String, Object> amortizeObj2 = Maps.newHashMap()
        amortizeObj2.put("rebate_amortize_amount", new BigDecimal("100"))
        amortizeObj2.put("rebate_dynamic_amount", new BigDecimal("50"))
        Whitebox.invokeMethod(rebateServiceImpl, "addAmortizeKey", arg, amortizeKey, amortizeObj2)

        then:
        // 验证场景1结果
        amortizeObj1.containsKey("rebate_amortize_amount")
        amortizeObj1.get("rebate_amortize_amount") == BigDecimal.ZERO
        amortizeObj1.containsKey("rebate_dynamic_amount")
        amortizeObj1.get("rebate_dynamic_amount") == BigDecimal.ZERO

        // 验证场景2结果：已有键值不变
        amortizeObj2.get("rebate_amortize_amount") == new BigDecimal("100")
        amortizeObj2.get("rebate_dynamic_amount") == new BigDecimal("50")
    }


    def "test resetOrderAmount"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())
        def rebateProduct = PowerMockito.mock(RebateProduct.class)

        Whitebox.setInternalState(rebateServiceImpl, "rebateProduct", rebateProduct)

        User user = new User("82681", "1000")
        DomainPluginDescribeExt pluginParam = PowerMockito.mock(DomainPluginDescribeExt.class)

        // 创建测试数据
        RebateCouponQuery.Arg arg = RebateCouponQuery.Arg.builder()
                .user(user)
                .masterObjectApiName("SalesOrderObj")
                .masterData(new ObjectDataDocument())
                .pluginParam(pluginParam)
                .detailDataMap(["1": new ObjectDataDocument(["amount": new BigDecimal("50")]),
                                "2": new ObjectDataDocument(["amount": new BigDecimal("30")])])
                .build()

        // 模拟方法调用
        PowerMockito.doReturn("amount").when(rebateProduct).getAmountKey(anyString(), anyString())
        PowerMockito.doReturn("order_amount").when(pluginParam).getFieldApiName(CouponConstants.PluginField.ORDER_AMOUNT)

        when:
        Whitebox.invokeMethod(rebateServiceImpl, "resetOrderAmount", arg)

        then:
        // 验证结果
        arg.getMasterData().get("order_amount") == "80" // 50 + 30
    }

    def "test calcExpression"() {
        given:
        def rebateServiceImpl = new RebateServiceImpl()

        // 测试加减乘除四种运算
        RebateRule.ExpressionData addExpr = new RebateRule.ExpressionData()
        addExpr.setOperator("add")
        addExpr.setRight("50")

        RebateRule.ExpressionData subtractExpr = new RebateRule.ExpressionData()
        subtractExpr.setOperator("subtract")
        subtractExpr.setRight("30")

        RebateRule.ExpressionData multiplyExpr = new RebateRule.ExpressionData()
        multiplyExpr.setOperator("multiply")
        multiplyExpr.setRight("2")

        RebateRule.ExpressionData divideExpr = new RebateRule.ExpressionData()
        divideExpr.setOperator("divide")
        divideExpr.setRight("4")

        BigDecimal srcValue = new BigDecimal("100")

        when:
        def addResult = Whitebox.invokeMethod(rebateServiceImpl, "calcExpression", addExpr, srcValue)
        def subtractResult = Whitebox.invokeMethod(rebateServiceImpl, "calcExpression", subtractExpr, srcValue)
        def multiplyResult = Whitebox.invokeMethod(rebateServiceImpl, "calcExpression", multiplyExpr, srcValue)
        def divideResult = Whitebox.invokeMethod(rebateServiceImpl, "calcExpression", divideExpr, srcValue)

        then:
        addResult == new BigDecimal("150")      // 100 + 50 = 150
        subtractResult == new BigDecimal("70")  // 100 - 30 = 70
        multiplyResult == new BigDecimal("200") // 100 * 2 = 200
        divideResult == new BigDecimal("25")    // 100 / 4 = 25
    }

    def "test makeRangeData"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())

        // 准备测试数据
        List<Rebate> rangeData = Lists.newArrayList(
                Rebate.builder().id("rebate1").name("范围返利1").fundAccountId("fund1").build(),
                Rebate.builder().id("rebate2").name("范围返利2").fundAccountId("fund2").build(),
                Rebate.builder().id("rebate3").name("范围返利3").fundAccountId("fund3").build(),
                // 重复ID测试去重功能
                Rebate.builder().id("rebate3").name("范围返利3重复").fundAccountId("fund3").build()
        )

        List<RebateCouponQuery.RebateResult> existingDatas = Lists.newArrayList(
                RebateCouponQuery.RebateResult.builder().id("rebate2").name("已存在返利2").build(),
                RebateCouponQuery.RebateResult.builder().id("rebate4").name("已存在返利4").build()
        )

        RebateCouponQuery.Result result = RebateCouponQuery.Result.builder()
                .datas(existingDatas)
                .build()

        // 模拟changeResultData方法
        List<RebateCouponQuery.RebateResult> changedResults = Lists.newArrayList(
                RebateCouponQuery.RebateResult.builder().id("rebate1").name("范围返利1").fundAccountId("fund1").build(),
                RebateCouponQuery.RebateResult.builder().id("rebate3").name("范围返利3").fundAccountId("fund3").build()
        )
        PowerMockito.doReturn(changedResults).when(rebateServiceImpl, "changeResultData", any())

        when:
        def rangeDataList = Whitebox.invokeMethod(rebateServiceImpl, "makeRangeData", result, rangeData)

        then:
        // 验证结果
        rangeDataList.size() == 3

        // 验证已存在的rebate2被正确处理
        def fromExisting = rangeDataList.find { it.fromDatasId == "rebate2" }
        fromExisting != null
        fromExisting.fundAccountId == "fund2"

        // 验证新增的rebate1和rebate3被正确添加
        def rangeResult1 = rangeDataList.find { it.id == "rebate1" }
        rangeResult1 != null
        rangeResult1.name == "范围返利1"

        def rangeResult3 = rangeDataList.find { it.id == "rebate3" }
        rangeResult3 != null
        rangeResult3.name == "范围返利3"

        // 验证重复的rebate3被去重
        rangeDataList.count { it.id == "rebate3" || it.fromDatasId == "rebate3" } == 1
    }
    def "test queryDataList"() {
        given:
        def rebateDao = PowerMockito.spy(new RebateDao())
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())

        Whitebox.setInternalState(rebateServiceImpl, "rebateDao", rebateDao)

        User user = new User("82681", "1000")
        List<String> usedId = Lists.newArrayList("rebate1", "rebate2")

        Map<String, RebateCouponUse.UseData> useDataList = Maps.newHashMap()
        useDataList.put("rebate1", RebateCouponUse.UseData.builder()
                .id("rebate1")
                .amount(new BigDecimal("30"))
                .build())
        useDataList.put("rebate2", RebateCouponUse.UseData.builder()
                .id("rebate2")
                .quantity(new BigDecimal("5"))
                .build())

        // 模拟查询结果
        List<Rebate> dbRebates = Lists.newArrayList(
                Rebate.builder()
                        .id("rebate1")
                        .name("返利1")
                        .unusedAmount(new BigDecimal("70"))
                        .useType(RebateConstants.UseType.AMOUNT.getValue())
                        .build(),
                Rebate.builder()
                        .id("rebate2")
                        .name("返利2")
                        .unusedAmount(new BigDecimal("10"))
                        .useType(RebateConstants.UseType.QUANTITY.getValue())
                        .build()
        )

        PowerMockito.doReturn(dbRebates).when(rebateDao).getCurrUseData(user, usedId, false)

        when:
        def result = rebateServiceImpl.queryDataList(user, usedId, useDataList, false)

        then:
        result.size() == 2
        // 验证金额类型返利：可用金额 = 剩余金额 + 已使用金额
        result.find { it.id == "rebate1" }.unusedAmount == new BigDecimal("100") // 70 + 30
        // 验证数量类型返利：可用数量 = 剩余数量 + 已使用数量
        result.find { it.id == "rebate2" }.unusedAmount == new BigDecimal("15") // 10 + 5
    }

    def "test getOrDefault"() {
        given:
        def rebateServiceImpl = new RebateServiceImpl()

        // 测试场景
        Map map1 = ["field1": "value1", "field2": 100, "field3": ""]
        Map map2 = ["field1": null]
        Map map3 = [:]

        when:
        def result1 = Whitebox.invokeMethod(rebateServiceImpl, "getOrDefault", map1, "field1", "default1")
        def result2 = Whitebox.invokeMethod(rebateServiceImpl, "getOrDefault", map1, "field2", "default2")
        def result3 = Whitebox.invokeMethod(rebateServiceImpl, "getOrDefault", map1, "field3", "default3")
        def result4 = Whitebox.invokeMethod(rebateServiceImpl, "getOrDefault", map1, "field4", "default4")
        def result5 = Whitebox.invokeMethod(rebateServiceImpl, "getOrDefault", map2, "field1", "default5")
        def result6 = Whitebox.invokeMethod(rebateServiceImpl, "getOrDefault", map3, "field1", "default6")

        then:
        result1 == "value1"       // 正常值
        result2 == "100"          // 非字符串值转换
        result3 == "default3"     // 空字符串使用默认值
        result4 == "default4"     // 不存在的键使用默认值
        result5 == "default5"     // null值使用默认值
        result6 == "default6"     // 空Map使用默认值
    }

    def "test query"() {
        given:
        def rebateRuleDao = PowerMockito.spy(new RebateRuleDao())
        def rebateDao = PowerMockito.spy(new RebateDao())
        RuleEngineLogicService ruleEngineLogicService = Mock(RuleEngineLogicService.class)
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())

        Whitebox.setInternalState(rebateServiceImpl, "rebateRuleDao", rebateRuleDao)
        Whitebox.setInternalState(rebateServiceImpl, "rebateDao", rebateDao)
        Whitebox.setInternalState(rebateServiceImpl, "engineLogicService", ruleEngineLogicService)

        User user = new User("82681", "1000")
        List<RebateRule.Rule> rules = Lists.newArrayList()
        RebateRule.Rule rule = RebateRule.Rule.builder()
                .id(ruleId)
                .name("testName")
                .ruleContent("{\"cycle_info\": {\"cycle_data\": [], \"max_amount\": 0}, \"expressions\": [{\"right\": \"9999\", \"rowId\": \"****************\", \"execute_type\": \"CONSTANT\"}], \"calculate_type\": \"EXPRESSION\", \"object_api_name\": \"SalesOrderObj\", \"master_condition\": []}")
                .build()
        rules.add(rule)
        RebateCouponQuery.Arg arg = RebateCouponQuery.Arg.builder()
                .user(user)
                .accountId("64327ca3799beb0001f72b91")
                .detailDataMap(["1": new ObjectDataDocument(["product_id": "617b6433c677950001473516"])])
                .masterData(new ObjectDataDocument(["accountId": "64327ca3799beb0001f72b91"]))
                .masterObjectApiName("SalesOrderObj")
                .detailObjectApiName("SalesOrderProductObj")
                .requestId("dcb66840a9574fd28c75d285529d727f")
                .fundAccountId("65379165119a12000152babe")
                .ruleId(ruleId)
        // .rangeRuleIds(Lists.newArrayList("rangIdTest"))
                .build()

        List<Rebate> rebates = Lists.newArrayList(Rebate.builder().id("617b6433c677950001473516").name("test").endDate(*************).startDate(*************).build())
        PowerMockito.doReturn(rules).when(rebateRuleDao, "getDataByAccountId", user, arg.getMasterObjectApiName(), arg.getAccountId(), arg)
        PowerMockito.doReturn(rules).when(rebateRuleDao, "getCurrUseData", user, arg.getRangeRuleIds(), false)
        PowerMockito.doReturn(rebates).when(rebateDao, "getDataByAccountId", user, arg.getMasterObjectApiName(), arg.getAccountId(), arg)
        when:
        RebateCouponQuery.Result result = rebateServiceImpl.query(arg)
        then:
        for (item in datas) {
            def limitMoney = item.get("limitMoney")
            result.limitMoney == limitMoney

            def retRuleId = item.get("ruleId")
            if (null != retRuleId) {
                result.ruleId == retRuleId
            }
        }
        where:
        datas                                                         | ruleId    | rebateType
        [new ObjectData(["limitMoney": "9999", "ruleId": "617b643"])] | "617b643" | "Money"
        [new ObjectData(["limitMoney": "9999", "ruleId": "617b643"])] | null      | "Money"

    }

    def "test getType"() {
        given:
        def rebateServiceImpl = new RebateServiceImpl()

        when:
        def result = rebateServiceImpl.getType()

        then:
        result == "rebate"
    }

    def "test changeResultData"() {
        given:
        def rebateServiceImpl = new RebateServiceImpl()
        List<Rebate> rebateList = Lists.newArrayList(
                Rebate.builder().id("1").name("test1").build(),
                Rebate.builder().id("2").name("test2").build()
        )

        when:
        def result = rebateServiceImpl.changeResultData(rebateList)

        then:
        result != null
        result.size() == 2
    }

    def "test calcLimitMoney"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())
        def bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService.class)

        Whitebox.setInternalState(rebateServiceImpl, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)

        User user = new User("82681", "1000")
        RebateCouponQuery.Arg arg = RebateCouponQuery.Arg.builder()
                .user(user)
                .masterObjectApiName("SalesOrderObj")
                .build()

        RebateRule.Rule rule = RebateRule.Rule.builder()
                .id("rule1")
                .name("testName")
                .ruleContent("{\"cycle_info\": {\"cycle_data\": [], \"max_amount\": 0}, \"expressions\": [{\"right\": \"9999\", \"rowId\": \"****************\", \"execute_type\": \"CONSTANT\"}], \"calculate_type\": \"EXPRESSION\", \"object_api_name\": \"SalesOrderObj\", \"master_condition\": []}")
                .build()

        bizConfigThreadLocalCacheService.isOpenCoupon(_) >> false
        bizConfigThreadLocalCacheService.isOpenPricePolicy(_, _) >> false

        when:
        def result = rebateServiceImpl.calcLimitMoney(arg, rule, false)

        then:
        result == new BigDecimal("9999")
    }

    def "test filterRules"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())
        def bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService.class)
        def engineLogicService = Mock(RuleEngineLogicService.class)

        Whitebox.setInternalState(rebateServiceImpl, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        Whitebox.setInternalState(rebateServiceImpl, "engineLogicService", engineLogicService)

        User user = new User("82681", "1000")
        RebateCouponQuery.Arg arg = RebateCouponQuery.Arg.builder()
                .user(user)
                .masterObjectApiName("SalesOrderObj")
                .masterData(new ObjectDataDocument(["accountId": "64327ca3799beb0001f72b91"]))
                .build()

        RebateCouponQuery.Result result = RebateCouponQuery.Result.builder().build()

        StopWatch stopWatch = StopWatch.createStarted("test")

        List<RebateRule.Rule> rules = Lists.newArrayList()
        RebateRule.Rule rule = RebateRule.Rule.builder()
                .id("rule1")
                .name("testName")
                .ruleContent("{\"cycle_info\": {\"cycle_data\": [], \"max_amount\": 0}, \"expressions\": [{\"right\": \"9999\", \"rowId\": \"****************\", \"execute_type\": \"CONSTANT\"}], \"calculate_type\": \"EXPRESSION\", \"object_api_name\": \"SalesOrderObj\", \"master_condition\": []}")
                .build()
        rules.add(rule)

        bizConfigThreadLocalCacheService.isOpenCoupon(_) >> false
        bizConfigThreadLocalCacheService.isOpenPricePolicy(_, _) >> false
        //engineLogicService.checkRuleWhere(_, _, _, _) >> true

        when:
        def filteredRules = rebateServiceImpl.filterRules(arg, result, stopWatch, rules)

        then:
        filteredRules.size() == 1
    }

    def "test getAmortizeKey"() {
        given:
        def rebateServiceImpl = new RebateServiceImpl()

        when:
        def result = rebateServiceImpl.getAmortizeKey()

        then:
        result == "rebate_amortize"
    }

    def "test getMasterAmountKey"() {
        given:
        def rebateServiceImpl = new RebateServiceImpl()

        when:
        def result = rebateServiceImpl.getMasterAmountKey()

        then:
        result == "rebate_amount"
    }

    def "test matchAmortize"() {
        given:
        def rebateRuleDao = PowerMockito.spy(new RebateRuleDao())
        def rebateDao = PowerMockito.spy(new RebateDao())
        def rebateProduct = PowerMockito.mock(RebateProduct.class)
        def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService.class)
        RuleEngineLogicService ruleEngineLogicService = PowerMockito.mock(RuleEngineLogicService.class)
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())

        Whitebox.setInternalState(rebateServiceImpl, "rebateRuleDao", rebateRuleDao)
        Whitebox.setInternalState(rebateServiceImpl, "rebateDao", rebateDao)
        Whitebox.setInternalState(rebateServiceImpl, "engineLogicService", ruleEngineLogicService)
        Whitebox.setInternalState(rebateServiceImpl, "rebateProduct", rebateProduct)
        Whitebox.setInternalState(rebateServiceImpl, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)

        User user = new User("82681", "1000")
        RebateCouponMatch.Arg arg = RebateCouponMatch.Arg.builder()
                .user(user)
                .masterData(new ObjectDataDocument(["accountId": "64327ca3799beb0001f72b91"]))
                .detailDataMap(["1": new ObjectDataDocument(["product_id": "617b6433c677950001473516"])])
                .masterObjectApiName("SalesOrderObj")
                .detailObjectApiName("SalesOrderProductObj")
                .requestId("dcb66840a9574fd28c75d285529d727f")
                .ruleId("rule1")
                .build()

        List<RebateRule.Rule> rules = Lists.newArrayList()
        RebateRule.Rule rule = RebateRule.Rule.builder()
                .id("rule1")
                .name("testName")
                .ruleContent("{\"cycle_info\": {\"cycle_data\": [], \"max_amount\": 0}, \"expressions\": [{\"right\": \"9999\", \"rowId\": \"****************\", \"execute_type\": \"CONSTANT\"}], \"calculate_type\": \"EXPRESSION\", \"object_api_name\": \"SalesOrderObj\", \"master_condition\": []}")
                .build()
        rules.add(rule)

        List<Rebate> rebates = Lists.newArrayList(
                Rebate.builder().id("rebate1").name("rebate1").ruleId("rule1").unusedAmount(new BigDecimal("100")).build()
        )

        Map<String, List<Amortize.ProductData>> productDataMap = Maps.newHashMap()
        List<Amortize.ProductData> productDataList = Lists.newArrayList(
                Amortize.ProductData.builder().id("1").amount(BigDecimal.ONE).build()
        )
        productDataMap.put("rebate1", productDataList)

        PowerMockito.doReturn(rules).when(rebateRuleDao).getCurrUseData(any(), anyList(), anyBoolean())
        PowerMockito.doReturn(rebates).when(rebateDao).getCurrUseData(any(), anyList(), anyBoolean())
        PowerMockito.doReturn(false).when(bizConfigThreadLocalCacheService).isOpenCoupon(anyString())
        PowerMockito.doReturn(false).when(bizConfigThreadLocalCacheService).isOpenPricePolicy(any(), any())
        PowerMockito.doReturn(productDataMap).when(rebateProduct).getProduct(any(), any())

        when:
        RebateCouponMatch.Result result = rebateServiceImpl.matchAmortize(arg)

        then:
        result != null
    }

    def "test makeRangeRebateData"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())

        // 创建测试数据
        def calcParam = RebateCouponAutoUse.CalcParam.builder()
                .useRangeRebate(Lists.newArrayList(
                        RebateCouponAutoUse.RangeRebate.builder()
                                .rangeRuleId("rangeRule1")
                                .rangeRebates(Lists.newArrayList(
                                        Rebate.builder()
                                                .id("rebate1")
                                                .name("返利1")
                                                .sumAmount(new BigDecimal("50"))
                                                .unusedAmount(new BigDecimal("50"))
                                                .build()
                                ))
                                .build()
                ))
                .oldOtherRangeRebate(Lists.newArrayList(
                        RebateCouponUse.RangeRebateData.builder()
                                .rangeRuleId("rangeRule1")
                                .rangeRebates(Lists.newArrayList(
                                        RebateCouponUse.UseData.builder()
                                                .id("rebate2")
                                                .name("返利2")
                                                .amount(new BigDecimal("30"))
                                                .build()
                                ))
                                .build(),
                        RebateCouponUse.RangeRebateData.builder()
                                .rangeRuleId("rangeRule2")
                                .rangeRebates(Lists.newArrayList(
                                        RebateCouponUse.UseData.builder()
                                                .id("rebate3")
                                                .name("返利3")
                                                .amount(new BigDecimal("20"))
                                                .build()
                                ))
                                .build()
                ))
                .build()

        when:
        List<RebateCouponUse.RangeRebateData> result = rebateServiceImpl.makeRangeRebateData(calcParam)

        then:
        // 验证结果
        result != null
        result.size() == 2

        // 验证第一个范围返利数据
        def rangeRebateData1 = result.find { it.rangeRuleId == "rangeRule1" }
        rangeRebateData1 != null
        rangeRebateData1.rangeRebates.size() == 2
        rangeRebateData1.rangeRebates.any { it.id == "rebate1" && it.amount == new BigDecimal("50") }
        rangeRebateData1.rangeRebates.any { it.id == "rebate2" && it.amount == new BigDecimal("30") }

        // 验证第二个范围返利数据
        def rangeRebateData2 = result.find { it.rangeRuleId == "rangeRule2" }
        rangeRebateData2 != null
        rangeRebateData2.rangeRebates.size() == 1
        rangeRebateData2.rangeRebates[0].id == "rebate3"
        rangeRebateData2.rangeRebates[0].amount == new BigDecimal("20")
    }

    def "test makeRangeRebateData with empty useRangeRebate"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())

        // 创建没有新返利的测试数据
        def calcParam = RebateCouponAutoUse.CalcParam.builder()
                .useRangeRebate(Lists.newArrayList())
                .oldOtherRangeRebate(Lists.newArrayList(
                        RebateCouponUse.RangeRebateData.builder()
                                .rangeRuleId("rangeRule2")
                                .rangeRebates(Lists.newArrayList(
                                        RebateCouponUse. UseData.builder()
                                                .id("rebate3")
                                                .name("返利3")
                                                .amount(new BigDecimal("20"))
                                                .build()
                                ))
                                .build()
                ))
                .build()

        when:
        List<RebateCouponUse.RangeRebateData> result = rebateServiceImpl.makeRangeRebateData(calcParam)

        then:
        // 验证结果，应该保留原有的范围返利数据
        result != null
        result.size() == 1
        result[0].rangeRuleId == "rangeRule2"
        result[0].rangeRebates.size() == 1
        result[0].rangeRebates[0].id == "rebate3"
        result[0].rangeRebates[0].amount == new BigDecimal("20")
    }

    def "test makeRangeRebateData with empty oldOtherRangeRebate"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())

        // 创建没有旧返利的测试数据
        def calcParam = RebateCouponAutoUse.CalcParam.builder()
                .useRangeRebate(Lists.newArrayList(
                        RebateCouponAutoUse.RangeRebate.builder()
                                .rangeRuleId("rangeRule1")
                                .rangeRebates(Lists.newArrayList(
                                        Rebate.builder()
                                                .id("rebate1")
                                                .name("返利1")
                                                .sumAmount(new BigDecimal("50"))
                                                .unusedAmount(new BigDecimal("50"))
                                                .build()
                                ))
                                .build()
                ))
                .oldOtherRangeRebate(Lists.newArrayList())
                .build()

        when:
        List<RebateCouponUse.RangeRebateData> result = rebateServiceImpl.makeRangeRebateData(calcParam)

        then:
        // 验证结果，应该只有新的范围返利数据
        result != null
        result.size() == 1
        result[0].rangeRuleId == "rangeRule1"
        result[0].rangeRebates.size() == 1
        result[0].rangeRebates[0].id == "rebate1"
        result[0].rangeRebates[0].amount == new BigDecimal("50")
    }

    def "test makeRangeRebateData with both empty"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())

        // 创建新旧返利都为空的测试数据
        def calcParam = RebateCouponAutoUse.CalcParam.builder()
                .useRangeRebate(Lists.newArrayList())
                .oldOtherRangeRebate(Lists.newArrayList())
                .build()

        when:
        List<RebateCouponUse.RangeRebateData> result = rebateServiceImpl.makeRangeRebateData(calcParam)

        then:
        // 验证结果，应该返回空列表
        result != null
        result.size() == 0
    }

    def "test useMatch"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())
        def rebateProduct = PowerMockito.mock(RebateProduct.class)
        def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService.class)
        def rebateRuleDao = PowerMockito.spy(new RebateRuleDao())
        def rebateDao = PowerMockito.spy(new RebateDao())

        Whitebox.setInternalState(rebateServiceImpl, "rebateProduct", rebateProduct)
        Whitebox.setInternalState(rebateServiceImpl, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        Whitebox.setInternalState(rebateServiceImpl, "rebateRuleDao", rebateRuleDao)
        Whitebox.setInternalState(rebateServiceImpl, "rebateDao", rebateDao)

        // 创建基本参数
        User user = new User("82681", "1000")
        RebateCouponAutoUse.Arg arg = new RebateCouponAutoUse.Arg()
        arg.setUser(user)
        arg.setMasterData(ObjectDataDocument.of(["accountId": "64327ca3799beb0001f72b91"]))
        arg.setDetailDataMap(["1": ObjectDataDocument.of(["product_id": "617b6433c677950001473516"])])
        arg.setMasterObjectApiName("SalesOrderObj")
        arg.setDetailObjectApiName("SalesOrderProductObj")
        arg.setRuleId("rule1")
        arg.setFundAccountId("fund1")
        arg.setAmount(new BigDecimal("100"))

        // 创建计算参数
        RebateCouponAutoUse.CalcParam calcParam = RebateCouponAutoUse.CalcParam.builder()
                .totalUseAmount(new BigDecimal("100"))
                .ruleId("rule1")
                .oldUseRebate(Lists.newArrayList(
                        RebateCouponUse.UseData.builder()
                                .id("rebate1")
                                .name("返利1")
                                .amount(new BigDecimal("30"))
                                .build()
                ))
                .arg(arg)
                .useRangeRebate(Lists.newArrayList())
                .build()

        // 创建使用数据
        List<RebateCouponUse.UseData> useData = Lists.newArrayList(
                RebateCouponUse.UseData.builder()
                        .id("rebate2")
                        .name("返利2")
                        .amount(new BigDecimal("50"))
                        .build()
        )

        // 模拟规则
        List<RebateRule.Rule> rules = Lists.newArrayList(
                RebateRule.Rule.builder()
                        .id("rule1")
                        .name("规则1")
                        .ruleContent("{\"cycle_info\": {\"cycle_data\": [], \"max_amount\": 0}, \"expressions\": [{\"right\": \"9999\", \"rowId\": \"****************\", \"execute_type\": \"CONSTANT\"}], \"calculate_type\": \"EXPRESSION\", \"object_api_name\": \"SalesOrderObj\", \"master_condition\": []}")
                        .build()
        )

        // 模拟返利数据
        List<Rebate> rebates = Lists.newArrayList(
                Rebate.builder()
                        .id("rebate2")
                        .name("返利2")
                        .unusedAmount(new BigDecimal("50"))
                        .build()
        )

        // 模拟产品分摊数据
        Map<String, List<Amortize.ProductData>> productDataMap = Maps.newHashMap()
        List<Amortize.ProductData> productDataList = Lists.newArrayList(
                Amortize.ProductData.builder().id("1").amount(new BigDecimal("50")).build()
        )
        productDataMap.put("rebate2", productDataList)

        StopWatch stopWatch = StopWatch.createStarted("useMatch")

        // 模拟方法调用
        PowerMockito.doReturn(rules).when(rebateRuleDao).getCurrUseData(any(), anyList(), anyBoolean())
        PowerMockito.doReturn(rebates).when(rebateDao).getCurrUseData(any(), anyList(), anyBoolean())
        PowerMockito.doReturn(false).when(bizConfigThreadLocalCacheService).isOpenCoupon(anyString())
        PowerMockito.doReturn(false).when(bizConfigThreadLocalCacheService).isOpenPricePolicy(any(), any())
        PowerMockito.doReturn(productDataMap).when(rebateProduct).getProduct(any(), any())

        when:
        RebateCouponMatch.Result result = rebateServiceImpl.useMatch(arg, stopWatch, calcParam, useData)

        then:
        result != null
    }

    def "test canUseAmount"() {
        given:
        def rebateRuleDao = PowerMockito.spy(new RebateRuleDao())
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())

        Whitebox.setInternalState(rebateServiceImpl, "rebateRuleDao", rebateRuleDao)

        User user = new User("82681", "1000")
        RebateUseAmount.Arg arg = new RebateUseAmount.Arg()
        arg.setUser(user)
        arg.setMasterData(new ObjectDataDocument(["accountId": "64327ca3799beb0001f72b91"]))
        arg.setFundAccountId(fundAccountId)
        arg.setFundAccountIds(Lists.newArrayList ( "fund1", "fund2", "fund3"))
        arg.setPluginParam(DomainPluginDescribeExt.of("SalesOrderObj",domain()))
        // 准备测试数据
        List<Rebate> mockRebates = Lists.newArrayList(
                Rebate.builder().id("rebate1").name("测试返利1").fundAccountId("fund1").unusedAmount(new BigDecimal("50")).build(),
                Rebate.builder().id("rebate2").name("测试返利2").fundAccountId("fund2").unusedAmount(new BigDecimal("30")).build()
        )

        RebateCouponAutoUse.CalcParam calcParam = RebateCouponAutoUse.CalcParam
                .builder()
                .arg(arg)
                .oldAllUseRebate(Lists.newArrayList(RebateCouponUse.UseData.builder().id("rebate1").fundAccountId("fund1").amount(new BigDecimal("20")).build()))
                .oldOtherRangeRebate(Lists.newArrayList())
                .allRebate(mockRebates)
                .build()

        // Mock RebateServiceImpl 内部方法
        PowerMockito.doReturn (Lists.newArrayList()).when(rebateServiceImpl).filterRules(any(), any(), any(), any())
        PowerMockito.doReturn(Lists.newArrayList(
                RebateCouponUse.UseData.builder().id("rebate1").amount(new BigDecimal("50")).build(),
                RebateCouponUse.UseData.builder().id("rebate2").amount(new BigDecimal("30")).build()
        )).when(rebateServiceImpl).makeRebateUseData(anyList())
        PowerMockito.doReturn("rebate_amount").when(rebateServiceImpl).getMasterAmountKey()
        PowerMockito.doReturn(calcParam).when(rebateServiceImpl).getCalcParam(any())
        PowerMockito.doNothing().when(rebateServiceImpl).findRebate(any(),any(),any())
        PowerMockito.doNothing().when(rebateServiceImpl).useRangeRebate(any(),any())
        PowerMockito.doNothing().when(rebateServiceImpl).useNoRangeRebate(any(),any())

        when:
        RebateUseAmount.Result result = rebateServiceImpl.canUseAmount(arg)

        then:
        result != null
        where:
        fundAccountId | accountRemain              | expectedAmount
        "fund1"       | BigDecimal.valueOf(100)    | BigDecimal.valueOf(100)
    }

    def "test isAllUseRebate"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())
        def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService.class)

        Whitebox.setInternalState(rebateServiceImpl, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)

        // 创建测试数据
        User user = new User("82681", "1000")
        RebateCouponMatch.Arg arg = RebateCouponMatch.Arg.builder()
                .user(user)
                .masterObjectApiName("SalesOrderObj")
                .ruleId("rule1")
                .detailDataMap(["1": new ObjectDataDocument(["amount": new BigDecimal("50")]),
                                "2": new ObjectDataDocument(["amount": new BigDecimal("30")])])
                .build()

        RebateCouponMatch.Result result = RebateCouponMatch.Result.builder()
                .detailDataMap(["1": Maps.newHashMap(), "2": Maps.newHashMap()])
                .build()

        List<Rebate> currList = Lists.newArrayList(
                Rebate.builder()
                        .id("rebate1")
                        .ruleId("rule1")
                        .unusedAmount(new BigDecimal("100"))
                        .build(),
                Rebate.builder()
                        .id("rebate2")
                        .ruleId("rule1")
                        .unusedAmount(new BigDecimal("50"))
                        .build()
        )

        Map<String, List<Amortize.ProductData>> rebateProducts = Maps.newHashMap()
        rebateProducts.put("rebate1", Lists.newArrayList(
                Amortize.ProductData.builder().id("1").build(),
                Amortize.ProductData.builder().id("2").build()
        ))
        rebateProducts.put("rebate2", Lists.newArrayList(
                Amortize.ProductData.builder().id("1").build(),
                Amortize.ProductData.builder().id("2").build()
        ))

        // 模拟方法调用
        PowerMockito.doReturn(false).when(bizConfigThreadLocalCacheService).isOpenCoupon(anyString())
        PowerMockito.doReturn(false).when(bizConfigThreadLocalCacheService).isOpenPricePolicy(anyString(), anyString())

        // 场景1：subtotal合计 = 120，返利总额 = 150
        PowerMockito.doReturn(new BigDecimal("70"), new BigDecimal("50"))
                .when(rebateServiceImpl, "getSubtotal", any(), eq(false), eq(false), eq(false), any(), any())

        when:
        def result1 = Whitebox.invokeMethod(rebateServiceImpl, "isAllUseRebate", arg, currList, result, rebateProducts)

        then:
        // 返利ID都与规则ID一致，产品全部覆盖，返利金额大于subtotal，应该返回true
        result1 == true

        // 场景2：subtotal合计 = 200，大于返利总额 = 150
        when:
        PowerMockito.doReturn(new BigDecimal("100"), new BigDecimal("100"))
                .when(rebateServiceImpl, "getSubtotal", any(), eq(false), eq(false), eq(false), any(), any())

        def result2 = Whitebox.invokeMethod(rebateServiceImpl, "isAllUseRebate", arg, currList, result, rebateProducts)

        then:
        // 返利金额小于subtotal，应该返回false
        result2 == false

        // 场景3：返利ID不都与规则ID一致
        when:
        currList = Lists.newArrayList(
                Rebate.builder()
                        .id("rebate1")
                        .ruleId("rule1")
                        .unusedAmount(new BigDecimal("100"))
                        .build(),
                Rebate.builder()
                        .id("rebate2")
                        .ruleId("rule2") // 不同的规则ID
                        .unusedAmount(new BigDecimal("50"))
                        .build()
        )

        PowerMockito.doReturn(new BigDecimal("50"), new BigDecimal("50"))
                .when(rebateServiceImpl, "getSubtotal", any(), eq(false), eq(false), eq(false), any(), any())

        def result3 = Whitebox.invokeMethod(rebateServiceImpl, "isAllUseRebate", arg, currList, result, rebateProducts)

        then:
        // 不是所有返利都有相同规则ID，应该返回false
        result3 == false

        // 场景4：产品没有全覆盖
        when:
        currList = Lists.newArrayList(
                Rebate.builder()
                        .id("rebate1")
                        .ruleId("rule1")
                        .unusedAmount(new BigDecimal("100"))
                        .build()
        )

        // 只覆盖一个产品
        rebateProducts = Maps.newHashMap()
        rebateProducts.put("rebate1", Lists.newArrayList(Amortize.ProductData.builder().id("1").build()))

        PowerMockito.doReturn(new BigDecimal("50"), new BigDecimal("50"))
                .when(rebateServiceImpl, "getSubtotal", any(), eq(false), eq(false), eq(false), any(), any())

        def result4 = Whitebox.invokeMethod(rebateServiceImpl, "isAllUseRebate", arg, currList, result, rebateProducts)

        then:
        // 产品没有全部覆盖，应该返回false
        result4 == false
    }


    def "test addCurrData"() {
        given:
        def rebateServiceImpl = new RebateServiceImpl()

        List<Rebate> dataList = Lists.newArrayList(
                Rebate.builder().id("rebate1").name("返利1").build(),
                Rebate.builder().id("rebate2").name("返利2").build(),
                Rebate.builder().id("rebate3").name("返利3").build()
        )

        List<Rebate> currList = Lists.newArrayList(
                Rebate.builder().id("rebate2").name("已使用返利2").unusedAmount(new BigDecimal("50")).build(),
                Rebate.builder().id("rebate4").name("已使用返利4").unusedAmount(new BigDecimal("100")).build()
        )

        when:
        Whitebox.invokeMethod(rebateServiceImpl, "addCurrData", dataList, currList)

        then:
        dataList.size() == 4

    }

    def "test setProductPrice"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())
        def availableRangeCoreService = PowerMockito.mock(AvailableRangeCoreService.class)

        Whitebox.setInternalState(rebateServiceImpl, "availableRangeCoreService", availableRangeCoreService)

        User user = new User("82681", "1000")
        DomainPluginDescribeExt pluginParam = PowerMockito.mock(DomainPluginDescribeExt.class)

        // 非产品返利时直接返回
        RebateCouponQuery.Arg arg1 = RebateCouponQuery.Arg.builder()
                .user(user)
                .rebateType(RebateConstants.RebateType.MONEY.getValue())
                .build()

        // 产品返利
        RebateCouponQuery.Arg arg2 = RebateCouponQuery.Arg.builder()
                .user(user)
                .rebateType(RebateConstants.RebateType.PRODUCT.getValue())
                .accountId("account1")
                .pluginParam(pluginParam)
                .masterData(new ObjectDataDocument())
                .detailObjectApiName("SalesOrderProductObj")
                .build()

        List<Rebate> dataList = Lists.newArrayList(
                Rebate.builder()
                        .id("rebate1")
                        .name("产品返利1")
                        .productRangeType(RebateConstants.ProductConditionType.FIXED.getValue())
                        .productRange([
                                "data": [
                                        ["product_id": "prod1", "unit_id": "unit1"],
                                        ["product_id": "prod2", "unit_id": "unit2"]
                                ]
                        ])
                        .build()
        )

        PowerMockito.doReturn("price_book_id").when(pluginParam).getFieldApiName(CouponConstants.PluginField.PRICE_BOOK_ID)

        // 模拟价格查询结果
        RealPriceModel.Result realPrice = new RealPriceModel.Result()
        realPrice.setNewRst(Lists.newArrayList(
                ObjectDataDocument.of(["product_id": "prod1", "param_unit": "unit1", "price": 100, "discount": 0.9]),
                ObjectDataDocument.of(["product_id": "prod2", "param_unit": "unit2", "price": 200, "discount": 0.8])
        ))

        PowerMockito.doReturn(realPrice).when(availableRangeCoreService).getRealPrice(any(), any())

        when:
        // 测试非产品返利情况
        Whitebox.invokeMethod(rebateServiceImpl, "setProductPrice", arg1, dataList)

        // 测试产品返利情况
        Whitebox.invokeMethod(rebateServiceImpl, "setProductPrice", arg2, dataList)

        then:
        // 验证产品价格信息已设置
        def productRange = dataList[0].getProductRange() as Map
        def productsData = productRange.get("data") as List
        productsData.size() == 2
    }
    def "test getCycleMinNum"() {
        given:
        def rebateServiceImpl = new RebateServiceImpl()

        // 测试场景
        BigDecimal canUseAmount1 = new BigDecimal("1000")
        BigDecimal canUseAmount2 = new BigDecimal("500")
        BigDecimal canUseAmount3 = null

        RebateRule.CycleInfoData.CycleDataBean cycle = new RebateRule.CycleInfoData.CycleDataBean()
        cycle.setFieldValue(new BigDecimal("100"))
        cycle.setUsedAmount(new BigDecimal("50"))

        BigDecimal srcValue1 = new BigDecimal("250") // 将得到 250/100 = 2.5，向下取整为2，结果为 2*50 = 100
        BigDecimal srcValue2 = new BigDecimal("350") // 将得到 350/100 = 3.5，向下取整为3，结果为 3*50 = 150

        when:
        // 测试canUseAmount大于计算值的情况
        def result1 = Whitebox.invokeMethod(rebateServiceImpl, "getCycleMinNum", canUseAmount1, cycle, srcValue1, RoundingMode.HALF_DOWN)
        // 测试canUseAmount小于计算值的情况
        def result2 = Whitebox.invokeMethod(rebateServiceImpl, "getCycleMinNum", canUseAmount2, cycle, srcValue2, RoundingMode.HALF_DOWN)
        then:
        result1 == new BigDecimal("100") // 计算值为100，小于canUseAmount=1000，所以返回100
        result2 == new BigDecimal("150") // 500<150，所以仍然返回canUseAmount=500
    }
    def "test setProductDisplayName"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())
        def serviceFacade = PowerMockito.mock(ServiceFacade.class)
        def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService.class)

        Whitebox.setInternalState(rebateServiceImpl, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(rebateServiceImpl, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)

        User user = new User("82681", "1000")
        DomainPluginDescribeExt pluginParam =DomainPluginDescribeExt.of("SalesOrderObj",domain())

        RebateCouponQuery.Arg arg = RebateCouponQuery.Arg.builder()
                .user(user)
                .rebateType(RebateConstants.RebateType.PRODUCT.getValue())
                .pluginParam(pluginParam)
                .build()

        List<Rebate> dataList = Lists.newArrayList(
                Rebate.builder()
                        .id("rebate1")
                        .name("产品返利1")
                        .productRangeType(RebateConstants.ProductConditionType.FIXED.getValue())
                        .productRange([
                                "data": [
                                        ["product_id": "prod1"],
                                        ["product_id": "prod2"]
                                ]
                        ])
                        .build()
        )

        // 模拟查询产品信息
        List<IObjectData> products = Lists.newArrayList(
                new ObjectData(["id": "prod1", "display_name": "产品1"]),
                new ObjectData(["id": "prod2", "display_name": "产品2"])
        )

        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService).openPeriodicProduct(anyString())
        PowerMockito.doReturn(products).when(serviceFacade).findObjectDataByIds(anyString(), anyList(), anyString())

        when:
        Whitebox.invokeMethod(rebateServiceImpl, "setProductDisplayName", arg, dataList)

        then:
        // 验证产品展示名称已设置
        def productRange = dataList[0].getProductRange() as Map
        def productsData = productRange.get("data") as List
        productsData!=null
    }

    def "test setResultRebateData"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())

        // 创建测试数据
        RebateCouponMatch.Result result = RebateCouponMatch.Result.builder()
                .masterData(new ObjectDataDocument())
                .miscContent(Maps.newHashMap())
                .build()

        List<RebateCouponUse.UseData> useDataList = Lists.newArrayList(
                RebateCouponUse.UseData.builder().id("rebate1").build(),
                RebateCouponUse.UseData.builder().id("rebate2").build()
        )

        List<RebateCouponUse.RangeRebateData> rangeRebateData = Lists.newArrayList(
                RebateCouponUse.RangeRebateData.builder()
                        .rangeRuleId("rangeRule1")
                        .rangeRebates(Lists.newArrayList(
                                RebateCouponUse.UseData.builder().id("rebate3").build()
                        ))
                        .build()
        )

        List<Rebate> currList = Lists.newArrayList(
                Rebate.builder().id("rebate1").name("返利1").build(),
                Rebate.builder().id("rebate2").name("返利2").build(),
                Rebate.builder().id("rebate3").name("返利3").build()
        )

        String ruleId = "rule1"

        // 模拟方法调用
        List<RebateCouponUse.UseData> makeRebateResult1 = Lists.newArrayList(
                RebateCouponUse.UseData.builder().id("rebate1").name("返利1").build(),
                RebateCouponUse.UseData.builder().id("rebate2").name("返利2").build()
        )

        List<RebateCouponUse.UseData> makeRebateResult2 = Lists.newArrayList(
                RebateCouponUse.UseData.builder().id("rebate3").name("返利3").build()
        )

        PowerMockito.doReturn(makeRebateResult1).when(rebateServiceImpl).makeRebateUseData(anyList())

        when:
        // 第一次调用时模拟返回常规返利
        PowerMockito.doReturn(makeRebateResult1).when(rebateServiceImpl).makeRebateUseData(Lists.newArrayList(currList.get(0), currList.get(1)))
        // 第二次调用时模拟返回范围返利
        PowerMockito.doReturn(makeRebateResult2).when(rebateServiceImpl).makeRebateUseData(Lists.newArrayList(currList.get(2)))

        Whitebox.invokeMethod(rebateServiceImpl, "setResultRebateData", result, useDataList, rangeRebateData, currList, ruleId)

        then:
        // 验证结果
        result.getRebateDatas() == makeRebateResult1
        result.getMiscContent().get(RebateCouponUse.UseData.DATA_REBATE) == makeRebateResult1
        result.getMasterData().get(RebateConstants.PluginField.REBATE_RULE_ID) == ruleId

        // 验证范围返利
        def resultRangeRebateData = result.getMiscContent().get(RebateCouponUse.UseData.DATA_RANGE_REBATE) as List<RebateCouponUse.RangeRebateData>
        resultRangeRebateData.size() == 1
        resultRangeRebateData[0].getRangeRuleId() == "rangeRule1"
        resultRangeRebateData[0].getRangeRebates() == makeRebateResult2

        // 验证范围规则ID
        result.getMasterData().get(RebateConstants.PluginField.RANGE_REBATE_RULE_IDS) == ["rangeRule1"]
    }

    def "test subAmortizeDown"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())
        def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService.class)

        Whitebox.setInternalState(rebateServiceImpl, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)

        // 创建测试数据
        User user = new User("82681", "1000")
        DomainPluginDescribeExt pluginParam = DomainPluginDescribeExt.of("SalesOrderObj",domain())

        Map<String, ObjectDataDocument> detailDataMap = Maps.newHashMap()
        detailDataMap.put("1", ObjectDataDocument.of(["product_id": "11","price_book_subtotal": new BigDecimal("50"),"rebate_amortize_amount": new BigDecimal("-51")]))
        detailDataMap.put("2", ObjectDataDocument.of(["product_id": "22","price_book_subtotal": new BigDecimal("80"),"rebate_amortize_amount": new BigDecimal("-81")]))

        RebateCouponMatch.Arg arg = RebateCouponMatch.Arg.builder()
                .user(user)
                .masterObjectApiName("SalesOrderObj")
                .detailDataMap(detailDataMap)
                .pluginParam(pluginParam)
                .build()

        RebateCouponMatch.Result result = RebateCouponMatch.Result.builder()
                .masterData(new ObjectDataDocument(["rebate_amount": new BigDecimal("100")]))
                .detailDataMap(detailDataMap)
                .build()

        List<Rebate> currList = Lists.newArrayList(
                Rebate.builder()
                        .id("rebate1")
                        .ruleId("rule1")
                        .priority(1)
                        .ruleLastModifiedTime(1000L)
                        .endDate(3000L)
                        .useType(CouponConstants.UseType.CASH.getValue())
                        .unusedAmount(new BigDecimal("50"))
                        .build(),
                Rebate.builder()
                        .id("rebate2")
                        .ruleId("rule2")
                        .priority(2)
                        .ruleLastModifiedTime(2000L)
                        .endDate(2000L)
                        .useType(CouponConstants.UseType.DISCOUNT.getValue())
                        .unusedAmount(new BigDecimal("30"))
                        .build()
        )

        Amortize.AmortizeKey amortizeKey = Amortize.AmortizeKey.builder()
                .amortizeKey("rebate_amortize_amount")
                .dynamicKey("rebate_dynamic_amount")
                .masterAmountKey("rebate_amount")
                .contentKey("rebate_amortize")
                .build()

        // 模拟方法调用
        PowerMockito.doReturn(false).when(bizConfigThreadLocalCacheService).isOpenCoupon(anyString())
        PowerMockito.doReturn(false).when(bizConfigThreadLocalCacheService).isOpenPricePolicy(anyString(), anyString())


        // 模拟详情Map数据
        Map<String, Object> detailMap = result.getDetailDataMap().get("1")
        detailMap.put("rebate_amortize_amount", new BigDecimal("-80"))
        detailMap.put("rebate_dynamic_amount", new BigDecimal("-20"))

        Map<String, Object> miscContent = Maps.newHashMap()
        List<Amortize.AmortizeData> amortizeList = Lists.newArrayList(
                Amortize.AmortizeData.builder().id("rebate1").amount(new BigDecimal("50")).build(),
                Amortize.AmortizeData.builder().id("rebate2").amount(new BigDecimal("30")).build()
        )
        miscContent.put("rebate_amortize", amortizeList)
        detailMap.put(pluginParam.getDefaultDetailFieldApiName(CouponConstants.PluginField.MISC_CONTENT), miscContent)

        when:
        Whitebox.invokeMethod(rebateServiceImpl, "subAmortizeDown", arg, result, currList, amortizeKey)

        then:
        // 验证结果
        result!=null

    }

    def "test addCurrRangeData"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())

        User user = new User("82681", "1000")

        // 准备测试数据
        List<Rebate> dataList = Lists.newArrayList(
                Rebate.builder().id("rebate1").name("测试返利1").build(),
                Rebate.builder().id("rebate2").name("测试返利2").build()
        )

        List<Rebate> oldRangeRebates = Lists.newArrayList(
                Rebate.builder().id("oldRebate1").name("旧返利1").build(),
                Rebate.builder().id("oldRebate2").name("旧返利2").build(),
                Rebate.builder().id("oldRebate3").name("旧返利3").build()
        )

        Map<String, Set<String>> oldRangeRebateIds = Maps.newHashMap()
        oldRangeRebateIds.put("rangeRule1", Sets.newHashSet("oldRebate1", "oldRebate3"))

        RebateCouponQuery.Arg arg = RebateCouponQuery.Arg.builder()
                .user(user)
                .oldRangeRebates(oldRangeRebates)
                .oldRangeRebateIds(oldRangeRebateIds)
                .build()

        when:
        Whitebox.invokeMethod(rebateServiceImpl, "addCurrRangeData", arg, "rangeRule1", dataList)

        then:
        // 验证addCurrData被调用，并且参数正确
        dataList.size()>1
    }

    def "test filterRangRule"() {
        given:
        def rebateServiceImpl = PowerMockito.spy(new RebateServiceImpl())
        def rebateRuleDao = PowerMockito.mock(RebateRuleDao.class)
        def ruleEngineLogicService = PowerMockito.mock(RuleEngineLogicService.class)

        Whitebox.setInternalState(rebateServiceImpl, "rebateRuleDao", rebateRuleDao)
        Whitebox.setInternalState(rebateServiceImpl, "engineLogicService", ruleEngineLogicService)

        // 创建测试数据
        User user = new User("82681", "1000")
        RebateCouponQuery.Arg arg = RebateCouponQuery.Arg.builder()
                .user(user)
                .masterObjectApiName("SalesOrderObj")
                .masterData(new ObjectDataDocument(["accountId": "64327ca3799beb0001f72b91"]))
                .detailDataMap(["1": new ObjectDataDocument(["product_id": "prod1"]),
                                "2": new ObjectDataDocument(["product_id": "prod2"])])
                .build()

        RebateCouponQuery.Result result = RebateCouponQuery.Result.builder().build()

        // 模拟范围规则数据
        List<RebateRule.Rule> rangeRules = Lists.newArrayList(
                RebateRule.Rule.builder()
                        .id("rangeRule1")
                        .name("范围规则1")
                        .ruleContent("{\"master_condition\": [], \"detail_condition\": [{\"field_name\": \"product_id\", \"field_value\": \"prod1\"}]}")
                        .build(),
                RebateRule.Rule.builder()
                        .id("rangeRule2")
                        .name("范围规则2")
                        .ruleContent("{\"master_condition\": [], \"detail_condition\": [{\"field_name\": \"product_id\", \"field_value\": \"prod2\"}]}")
                        .build(),
                RebateRule.Rule.builder()
                        .id("rangeRule3")
                        .name("范围规则3")
                        .ruleContent("{\"master_condition\": [], \"detail_condition\": [{\"field_name\": \"product_id\", \"field_value\": \"prod3\"}]}")
                        .build()
        )

        // 模拟规则引擎逻辑服务的行为
        ruleEngineLogicService.checkRuleWhere(_, _, _, _) >> { args ->
            String detailId = args[2]
            RebateRule.Rule rule = args[3]

            // 根据规则ID和明细ID模拟不同的匹配结果
            if (rule.getId() == "rangeRule1" && detailId == "1") return true
            if (rule.getId() == "rangeRule2" && detailId == "2") return true
            return false
        }

        PowerMockito.doReturn(rangeRules).when(rebateRuleDao).getCurrUseData(any(), anyList(), anyBoolean())

        when:
        Map<String, Set<String>> result1 = Whitebox.invokeMethod(rebateServiceImpl, "filterRangRule", arg, result)

        then:
        // 验证结果
        result1 != null
    }

    def domain() {
        def domain = new DomainPluginParam()
        def detail = new DomainPluginParam.DetailObj()
        detail.setObjectApiName("SalesOrderProductObj")
        Map<String, String> fieldMapping = Maps.newHashMap()
        fieldMapping.put("misc_content", "misc_content")
        fieldMapping.put("misc_content_json", "misc_content_json")
        fieldMapping.put("account_id", "account_id")
        fieldMapping.put("product_rebate_rule_id", "product_rebate_rule_id")
        fieldMapping.put("rebate_amount", "rebate_amount")

        Map<String, String> fieldMappingDetail = Maps.newHashMap()
        fieldMappingDetail.put("misc_content", "misc_content")
        fieldMappingDetail.put("misc_content_json", "misc_content_json")
        fieldMappingDetail.put("account_id", "account_id")
        fieldMappingDetail.put("product_rebate_rule_id", "product_rebate_rule_id")
        fieldMappingDetail.put("rebate_amortize_amount", "rebate_amortize_amount")
        fieldMappingDetail.put("rebate_dynamic_amount", "rebate_dynamic_amount")
        fieldMappingDetail.put("rebate_amortize", "rebate_amortize")

        detail.setFieldMapping(fieldMappingDetail)
        domain.setFieldMapping(fieldMapping)
        domain.setDetails([detail])
        return domain
    }
}
