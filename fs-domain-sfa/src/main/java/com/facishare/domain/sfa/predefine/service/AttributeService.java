package com.facishare.domain.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.predefine.service.attribute.AttributeCoreService;
import com.facishare.crm.sfa.predefine.service.attribute.AttributeRangeService;
import com.facishare.crm.sfa.predefine.service.attribute.ProductAttributeService;
import com.facishare.crm.sfa.predefine.service.attribute.dao.AttributeDao;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeProductCategoryModel;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeRangeMode;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeRestModel;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants;
import com.facishare.crm.sfa.utilities.util.AttributeUtils;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by luxin on 2018/10/29.
 */
@ServiceModule("attribute")
@Component
@Slf4j
public class AttributeService {
    @Autowired
    private AttributeCoreService attributeCoreService;
    @Autowired
    private AttributeDao attributeDao;
    @Autowired
    private ProductAttributeService productAttributeService;
    @Autowired
    private AttributeRangeService attributeRangeService;

    @Autowired
    private ServiceFacade serviceFacade;

    @ServiceMethod("productCategoryRelateAttribute")
    public AttributeProductCategoryModel.RelatedResult productCategoryRelateAttribute(ServiceContext serviceContext, AttributeProductCategoryModel.RelatedArg arg) {
        if (CollectionUtils.isNotEmpty(arg.getAttributeIds()) && !arg.getCategoryId().isEmpty()) {
            boolean isRelated = arg.isRelated();
            if (isRelated) {
                attributeCoreService.batchSaveProductCategoryAttribute(serviceContext.getTenantId(), arg.getCategoryId(), arg.getAttributeIds(), arg.getAttributeList());
            } else {
                attributeCoreService.batchDeleteProductCategoryAttributeRelationByCategory(serviceContext.getTenantId(), arg.getCategoryId(), arg.getAttributeIds());
            }
        }
        return AttributeProductCategoryModel.RelatedResult.builder().isSuccess(true).build();
    }

    @ServiceMethod("attributeUserRelate")
    public AttributeProductCategoryModel.AttributeUserRelationResult attributeUserRelate(ServiceContext serviceContext, AttributeProductCategoryModel.AttributeUserRelationArg arg) {
        if (!arg.getUserId().isEmpty()) {
            attributeCoreService.batchSaveAttributeUserRelation(serviceContext.getUser(), arg.getCategoryId(), arg.getAttributeIds(), arg.getIsRecover(), arg.getAttributeList());
        }
        return AttributeProductCategoryModel.AttributeUserRelationResult.builder().isSuccess(true).build();
    }

    @ServiceMethod("getAttributeAndValueByCategoryId")
    public AttributeProductCategoryModel.GetAttributeAndValueResult getAttributeAndValueByCategoryId(ServiceContext serviceContext, AttributeProductCategoryModel.GetAttributeAndValueArg arg) {
        List<String> attributeIds = new ArrayList<>();
        List<IObjectData> attributeUserRelationList = attributeCoreService.getAttributeUserRelationByCategoryIdAndUser(serviceContext.getTenantId(), arg.getCategoryId(), serviceContext.getUser().getUpstreamOwnerIdOrUserId());
        Map<String, List<String>> attrMap = Maps.newHashMap();
        if (attributeUserRelationList != null && !attributeUserRelationList.isEmpty()) {
            IObjectData objectData = attributeUserRelationList.get(0);
            String attrJson = objectData.get(AttributeProductCategoryModel.ATTRIBUTE_JSON, String.class);
            if (StringUtils.isNotBlank(attrJson)) {
                List<AttributeProductCategoryModel.Attribute> attributes = JSON.parseArray(attrJson, AttributeProductCategoryModel.Attribute.class);
                if (CollectionUtils.isNotEmpty(attributes)) {
                    for (AttributeProductCategoryModel.Attribute x : attributes) {
                        attributeIds.add(x.getAttributeId());
                        attrMap.put(x.getAttributeId(), x.getAttributeValueIds());
                    }
                }
            } else {
                attributeIds = objectData.get(AttributeConstants.ProductField.ATTRIBUTE_IDS, ArrayList.class);
            }
        } else {
            //用户级没配置，走租户级
            handleArg(serviceContext, arg, attributeIds, attrMap);
        }

        return handAttributeAndValueInfo(serviceContext.getUser(), attributeIds, attrMap);
    }

    private void handleArg(ServiceContext serviceContext, AttributeProductCategoryModel.GetAttributeAndValueArg arg, List<String> attributeIds, Map<String, List<String>> attrMap) {
        List<IObjectData> categoryList = attributeCoreService.getCategoryInfoByCategoryId(serviceContext.getTenantId(), arg.getCategoryId());
        if (CollectionUtils.isNotEmpty(categoryList)) {
            for (IObjectData x : categoryList) {
                String attrId = String.valueOf(x.get(AttributeConstants.Field.ATTRIBUTE_ID));
                if (!attributeIds.contains(attrId)) {
                    attributeIds.add(attrId);
                    attrMap.put(attrId, x.get(AttributeProductCategoryModel.ATTRIBUTE_VALUE_IDS, ArrayList.class, Lists.newArrayList()));
                }
            }
        }
    }

    @ServiceMethod("getAttributeAndValueByCategoryIdForManager")
    public AttributeProductCategoryModel.GetAttributeAndValueResult getAttributeAndValueByCategoryIdForManager(ServiceContext serviceContext, AttributeProductCategoryModel.GetAttributeAndValueArg arg) {
        List<String> attributeIds = new ArrayList<>();
        Map<String, List<String>> attrMap = Maps.newHashMap();
        handleArg(serviceContext, arg, attributeIds, attrMap);

        return handAttributeAndValueInfo(serviceContext.getUser(), attributeIds, attrMap);
    }

    @ServiceMethod("checkValueProductRelation")
    public AttributeRestModel.CheckValueProductRelationResult checkValueProductRelation(ServiceContext serviceContext, AttributeRestModel.CheckValueProductRelationArg arg) {
        boolean result = productAttributeService.checkValuesHasBeUsed(serviceContext.getUser(), Lists.newArrayList(arg.getAttributeValueId()));
        return AttributeRestModel.CheckValueProductRelationResult.builder().hasRelation(result).build();
    }

    private AttributeProductCategoryModel.GetAttributeAndValueResult handAttributeAndValueInfo(User user, List<String> attributeIds, Map<String, List<String>> attrMap) {
        AttributeProductCategoryModel.GetAttributeAndValueResult result = AttributeProductCategoryModel.GetAttributeAndValueResult
                .builder()
                .build();
        if (CollectionUtils.isEmpty(attributeIds)) {
            return result;
        }
        List<IObjectData> attributeList = attributeDao.getAttributesByIds(user, attributeIds);
        Map<String, IObjectData> attributeMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(attributeList)) {
            attributeMap = attributeList.stream().collect(Collectors.toMap(DBRecord::getId, Function.identity(), (k1, k2) -> k1));
        }
        List<String> attributeValueIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attrMap.values())) {
            for (List<String> x : attrMap.values()) {
                attributeValueIds.addAll(x);
            }
        }
        List<IObjectData> attributeValueList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attributeValueIds)) {
            attributeValueList = attributeDao.getAttributeValuesByIds(user, attributeValueIds);
        } else {
            attributeValueList = attributeDao.getAttributeValuesByAttributeIds(user, attributeIds);
        }

        Map<String, List<IObjectData>> attributeValueMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(attributeValueList)) {
            attributeValueMap = attributeValueList.stream().collect(Collectors.groupingBy(x -> x.get(AttributeConstants.CategoryField.ATTRIBUTE_ID.getApiName(), String.class)));
        }
        Set<String> groupIds = attributeList.stream().map(x -> x.get(AttributeConstants.GroupField.ATTRIBUTE_GROUP_ID, String.class)).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Map<String, IObjectData> groupMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(groupIds)) {
            groupMap = attributeCoreService.queryAttrGroupMap(user, groupIds);
        }
        List<AttributeProductCategoryModel.AttributeRestEntity> entityList = new ArrayList<>();
        for (String attributeId : attributeIds) {
            IObjectData objectData = attributeMap.get(attributeId);
            if (Objects.isNull(objectData)) {
                continue;
            }
            AttributeProductCategoryModel.AttributeRestEntity entity = AttributeProductCategoryModel.AttributeRestEntity.builder().build();
            List<IObjectData> attributeValues = new ArrayList<>();
            List<IObjectData> tmpAttributeValueList = attributeValueMap.getOrDefault(attributeId, Lists.newArrayList());
            for (IObjectData attributeValue : tmpAttributeValueList) {
                List<String> attrValues = attrMap.get(attributeId);
                if (CollectionUtils.isNotEmpty(attrValues) && attrValues.contains(attributeValue.getId())) {
                    attributeValue.set(AttributeProductCategoryModel.SELECTED_FLAG, true);
                }
                attributeValues.add(attributeValue);
            }
            handleGroup(groupMap, objectData);
            entity.setAttribute(ObjectDataDocument.of(objectData));
            entity.setAttributeValues(ObjectDataDocument.ofList(attributeValues));
            entityList.add(entity);
        }
        result.setAttributes(entityList);
        return result;
    }

    private void handleGroup(Map<String, IObjectData> groupMap, IObjectData objectData) {
        IObjectData groupData = groupMap.get(objectData.get(AttributeConstants.GroupField.ATTRIBUTE_GROUP_ID, String.class));
        if (Objects.isNull(groupData)) {
            return;
        }
        objectData.set(AttributeConstants.GroupField.GROUP_ID, groupData.getId());
        objectData.set(AttributeConstants.GroupField.GROUP_NAME, AttributeUtils.getI18nName(groupData));
        objectData.set(AttributeConstants.GroupField.GROUP_NO, groupData.get(AttributeConstants.GroupField.SERIAL_NO));
    }

    @ServiceMethod("attributeRange")
    public AttributeRangeMode.Result saveAttributeRange(ServiceContext serviceContext, AttributeRangeMode.Arg arg) {
        attributeRangeService.batchSaveAttrRange(serviceContext.getUser(), arg);
        return AttributeRangeMode.Result.builder().isSuccess(true).build();
    }

    @ServiceMethod("queryAttributeRange")
    public AttributeRangeMode.QueryResult queryAttributeRange(ServiceContext serviceContext, AttributeRangeMode.QueryArg arg) {
        if (StringUtils.isBlank(arg.getApiName()) || CollectionUtils.isEmpty(arg.getDataIds())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MISS_REQUIRED_PARAMETERS));
        }
        List<IObjectData> dataList = serviceFacade.findObjectDataByIdsIgnoreAll(serviceContext.getTenantId(), arg.getDataIds(), arg.getApiName());
        if (CollectionUtils.isEmpty(dataList)) {
            return AttributeRangeMode.QueryResult.builder().isSuccess(true).dataList(Lists.newArrayList()).build();
        }
        attributeRangeService.queryAttrRange(serviceContext.getUser(), dataList, arg.getApiName());
        return AttributeRangeMode.QueryResult.builder().isSuccess(true).dataList(ObjectDataDocument.ofList(dataList)).build();
    }

    @ServiceMethod("getAttributeValuesByAttributeIds")
    public AttributeRestModel.GetAttributeValuesResult getAttributeValuesByAttributeIds(ServiceContext serviceContext, AttributeRestModel.GetAttributeValuesArg arg) {
        if (CollectionUtils.isEmpty(arg.getAttributeIds())) {
            return AttributeRestModel.GetAttributeValuesResult.builder()
                    .dataList(Lists.newArrayList())
                    .build();
        }
        
        List<IObjectData> attributeValueList = attributeDao.getAttributeValuesByAttributeIds(serviceContext.getUser(), arg.getAttributeIds());
        return AttributeRestModel.GetAttributeValuesResult.builder()
                .dataList(ObjectDataDocument.ofList(attributeValueList))
                .build();
    }

    @ServiceMethod("queryAttributeAndNonAttributeText")
    public AttributeRestModel.GetAttributeText fillAttributeAndNonAttributeText(ServiceContext serviceContext, AttributeRestModel.GetAttributeText arg) {
        List<IObjectData> dataList = ObjectDataDocument.ofDataList(arg.getDataList());
        attributeCoreService.fillAttributeAndNonAttributeText(dataList,serviceContext.getTenantId());
        return AttributeRestModel.GetAttributeText.builder().dataList(ObjectDataDocument.ofList(dataList)).build();
    }
}
