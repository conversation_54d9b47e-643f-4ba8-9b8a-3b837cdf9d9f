package com.facishare.domain.sfa.predefine.service.contract;

import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.si.InnerContractService;
import com.facishare.crm.si.OuterContractServiceFactory;
import com.facishare.crm.si.erpdss.enums.AppCodeEnums;
import com.facishare.crm.si.erpdss.model.ContractModel;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.CreateRule;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2024-12-02
 */
@ServiceModule("contract")
@Component
@Slf4j
public class ContractPluginService {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private InnerContractService innerContractService;

    @ServiceMethod("getDetailById")
    public ContractModel.DetailResult getDetailById(ServiceContext serviceContext, ContractModel.DetailArg arg) {
        if(StringUtils.isAnyBlank(arg.getId(), arg.getApiName())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONTRACT_PARAM_ERROR));
        }
        IObjectData objectData = serviceFacade.findObjectData(serviceContext.getUser(), arg.getId(), arg.getApiName());
        if(objectData == null) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_FIND_CONTRACT_PARAM_ERROR));
        }
        return OuterContractServiceFactory.getInstance(AppCodeEnums.ZHEN_LING.getType()).buildDetailResult(serviceContext.getUser(), arg);
    }

    @ServiceMethod("updateById")
    public ContractModel.UpdateResult updateById(ServiceContext serviceContext, ContractModel.UpdateArg arg) {
        if(StringUtils.isAnyBlank(arg.getId(), arg.getAppCode(), arg.getContractNumber())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONTRACT_PARAM_ERROR));
        }
        return OuterContractServiceFactory.getInstance(AppCodeEnums.ZHEN_LING.getType()).updateById(serviceContext.getUser(), arg);
    }

    @ServiceMethod("upsertRule")
    public ContractModel.UpdateResult upsertRule(ServiceContext serviceContext, CreateRule.Arg arg) {
        boolean flag = innerContractService.processContractRule(serviceContext.getUser(), arg, true);
        return ContractModel.UpdateResult.builder().success(flag).msg("").build();
    }

    @ServiceMethod("checkRecordTypeHasData")
    public ContractModel.CheckDataResult checkRecordTypeHasData(ServiceContext serviceContext, ContractModel.CheckDataArg arg) {
        Map<String, Boolean> result  = innerContractService.checkRecordTypeHasData(serviceContext.getUser(), arg.getRecordTypes());
        return ContractModel.CheckDataResult.builder().result(result).build();
    }
}
