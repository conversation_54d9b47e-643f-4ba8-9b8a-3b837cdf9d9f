package com.facishare.domain.sfa.predefine.service.quoter;

import com.facishare.crm.sfa.model.QuoterModel;
import com.facishare.crm.sfa.predefine.service.AdvancedFormulaService;
import com.facishare.crm.sfa.predefine.service.quoter.AttributeConstraintService;
import com.facishare.crm.sfa.predefine.service.quoter.model.AttributeConstraintModel;
import com.facishare.crm.sfa.utilities.constant.AttributeConstaintConstants;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@ServiceModule("quoter_sfa")
@Component
public class QuoterService {
    @Autowired
    private AdvancedFormulaService advancedFormulaService;

    @Autowired
    private AttributeConstraintService attributeConstraintService;

    @Autowired
    private ServiceFacade serviceFacade;

    @ServiceMethod("domainList")
    public List<ObjectDescribeDocument> domainList(ServiceContext serviceContext, QuoterModel.DomainArg arg) {
        return advancedFormulaService.domainList(serviceContext.getUser(), arg.getPluginApiName());
    }

    @ServiceMethod("queryAdvancedFormulaList")
    public Map<String, Map<String,Map<String, CalculateRelation>>> queryAdvancedFormulaList(ServiceContext serviceContext, QuoterModel.Arg arg) {
        return advancedFormulaService.queryAdvancedFormulaList(serviceContext.getUser(), arg);
    }

    @ServiceMethod("batchCalculate")
    public List<ObjectDataDocument> batchCalculate(ServiceContext serviceContext, QuoterModel.CalculateDataParam arg) {
        return advancedFormulaService.formulaCalculate(arg, serviceContext);
    }


    @ServiceMethod("query_by_id")
    public AttributeConstraintModel.Result queryById(ServiceContext serviceContext, AttributeConstraintModel.Arg arg) {
        AttributeConstraintModel.Result result = new AttributeConstraintModel.Result();
        List<IObjectData> dataList = serviceFacade.findObjectDataByIds(serviceContext.getTenantId(), Lists.newArrayList(arg.getId()), AttributeConstaintConstants.DESC_API_NAME);
        int version = 0;
        boolean changed = false;
        if(CollectionUtils.isNotEmpty(dataList)) {
            version = dataList.get(0).get(AttributeConstaintConstants.FIELD_VERSION, Integer.class,  0);
        }
        List<IObjectData> detailDatas = attributeConstraintService.getAttributeConstraintLinesByAttributeConstraintId(serviceContext.getUser(), arg.getId());
        result.setDataList(attributeConstraintService.list2MultiTree(detailDatas));
        result.setVersion(version);
        //前端传版本号过来，才需要进行版本变更判断，否则版本无变更
        if(arg.getVersion()  != 0 && arg.getVersion() != version) {
            changed = true;
        }
        result.setChanged(changed);
        return result;
    }
}
